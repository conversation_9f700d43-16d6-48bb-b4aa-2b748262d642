import { post } from '../../utils/request.js'

/**
 * 上传文件
 * @param {FormData} formData - 包含文件和其他信息的FormData对象
 * @returns {Promise} 上传结果
 */
export function uploadFile(formData) {
  return post(
    '/api/PageUpload/Upload?ftype=attmore&rkey=1454001635260895242&mid=1435766976160600065&fext=&table=PT_HANDOVER_ATTCH&field=str_path&billid=PT_HANDOVER_ATTCH',
    formData,

    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  )
}

/**
 * 保存交接
 */
export function saveHandover(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_handover',
    postData: postData,
  })
}
/**
 * 交接列表
 * @param {} param
 * @returns
 */
export function queryHandOverData(param) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_handover_page',
    Filter: param,
  })
}

/**
 * 获取交接单
 * @param {object} param - 查询参数
 * @returns {Promise} 交接单数据
 */
export function queryHandover(param) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_handover_byid',
    Filter: param,
  })
}

/**
 * 开工交接单   
 * @param {object} param - 查询参数
 * @returns {Promise} 交接单数据
 */
export function queryHandoverStart(param) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_start_work_handover',
    Filter: param,
  })
}

/**
 * 接收交接单
 * @param {object} postData - 交接单数据
 * @returns {Promise} 接收结果
 */
export function receiveHandover(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_confirm_handover',
    ...postData,
  })
}

/**
 * 删除交接单
 * @param {object} postData - 交接单数据
 * @returns {Promise} 接收结果
 */
export function deleteHandover(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_delete_handover',
    id:postData
  })
}

/**
 * 导出交接单
 */
export function exportHandover(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_export_handover',
    id:postData
  })
}
