import { post } from '../../config/axios/httpReuest.js';
import { EDD_UPDATE_TASK_PDA, PDA_VIEW_AD, PDA_VIEW_P } from '../../config/tabPaneKey.js';
import { useFilter } from './useFilter.js';

const { shallowRef, ref, unref, reactive } = Vue;

export function usePView() {
  let option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    legend: {
      show: true,
    },
    grid: {
      left: '3%',
      right: '5%',
      bottom: '3%',
      containLabel: true,
    },
    // 工具栏
    toolbox: {
      show: true,
      feature: {
        // 保存为图片
        saveAsImage: {
          show: true,
          name: 'PView',
          title: '保存为图片',
        },
      },
    },
    xAxis: {
      type: 'value',
      // 显示x轴的刻度
      axisTick: {
        show: true,
      },
      // 显示x轴的轴线
      axisLine: {
        show: true,
      },
    },
    yAxis: {
      type: 'category',
      axisLabel: {
        formatter: function (value) {
          const index = value.indexOf('(');
          const a_index = value.slice(1, index);
          let a_1 = '';
          let a_2 = '';
          if (a_index < 1) {
            a_1 = value.slice(0, index);
          } else {
            a_2 = value.slice(0, index);
          }
          const b = value.slice(index, value.length);
          const arr = [`{a|${a_1}}`, `{c|${a_2}}`, `{b|${b}}`];
          return arr.join('');
        },
        rich: {
          a: {
            color: 'green',
            fontFamily: 'Microsoft YaHei',
          },
          c: {
            color: '#E6A23C',
            fontFamily: 'Microsoft YaHei',
          },
          b: {
            color: '#666',
            fontFamily: 'Microsoft YaHei',
          },
        },
      },
    },
    dataZoom: [
      {
        start: 70,
        end: 100,
        orient: 'vertical',
        brushSelect: false,
      },
    ],
    series: [
      {
        name: '',
        type: 'bar',
        stack: 'p',
        encode: {
          x: 'int_set_pnum',
          y: 'int_new_day',
        },
        itemStyle: {
          color: '#006600',
        },
      },
      {
        name: '',
        type: 'bar',
        stack: 'p',
        encode: {
          x: 'int_no',
          y: 'int_new_day',
        },
        itemStyle: {
          color: '#E6A23C',
        },
        label: {
          show: true,
          position: 'right',
          formatter: function (params) {
            return params.data.int_pnum;
          },
        },
      },
    ],
    dataset: {
      source: [],
    },
  };

  const chartRef = shallowRef(null);

  const myChart = shallowRef(null);
  // pView data
  const pViewData = ref([]);
  // adView data
  const adViewData = ref([]);
  // 柱状图是P还是AD
  const flagView = ref('P');

  //Pview的总数
  const pViewTotal = ref(0);
  const exceedTotal = ref(0);
  const neverTotal = ref(0);

  const { getFilterData } = useFilter();
  /**
   * @description get pView data
   * @param typeView pView or adView
   * @param strType {string} 节点
   * @param dayNum {number}
   * @param currentDate {string}
   * @param searchForm {object} 查询条件
   */
  const getPViewData = async (typeView, strType, dayNum, dayLowerNum,currentDate, searchForm,is_gp) => {
    flagView.value = typeView;
    const params = {
      ac: typeView === PDA_VIEW_P || typeView === PDA_VIEW_AD ? 'pda_receuve_pview' : 'pda_get_plan_pview',
      day_num: dayNum,
      day_num_lower: dayLowerNum,
      dt_date: currentDate,
      str_type: strType,
      is_gp:is_gp,
      filter_fields: getFilterData(searchForm),
    };
    if (myChart.value) {
      myChart.value.dispose();
    }
    myChart.value = echarts.init(chartRef.value);
    myChart.value.showLoading({
      text: 'loading...',
      showSpinner: true,
      textColor: 'black',
    });
    try {
      const { data } = await post(params);
      pViewData.value = data.data.pData;
      adViewData.value = data.data.adData;
      pViewTotal.value = data.data.pTotal;
    } catch (e) {
      console.log(e);
    }
  };

  const getPViewEddTypeNum = async (strType) => {
    if (strType==="10") {
      const params = {
        ac: 'pda_edd_type_Statistics'
      };
      try {
        const { data } = await post(params);
        exceedTotal.value = data.data.overThreeNum;
        neverTotal.value = data.data.nevelNum;
      } catch (e) {
        console.log(e);
      }
    }
  };


  // 动态设置datazoom的start和end
  const setDataZoom = (data) => {
    const len = data.length;
    const start = len > 10 ? 100 - (10 / len) * 100 : 0;
    option.dataZoom[0].start = start;
    option.dataZoom[0].end = 100;
  };
  // 获取pView的dateset的source
  const getPViewDatasetSource = (realData) => {
    return realData.map((m, index) => {
      m.int_no = Math.abs(m.int_pnum - m.int_set_pnum);
      let sum = 0;
      if (index === 0) {
        sum = m.int_pnum;
      } else {
        sum = parseInt(m.int_pnum) + parseInt(realData[index - 1].sum);
      }
      m.int_new_day = 'P' + m.int_day + '(' + sum + ')';
      m.sum = sum;
      return m;
    });
  };
  // 获取ADView的dateset的source
  const getADViewDatasetSource = (realData) => {
    return realData.map((m, index) => {
      let sum = 0;
      if (index === 0) {
        sum = parseInt(m.int_anum) + parseInt(m.int_dnum);
      } else {
        sum = parseInt(m.int_anum) + parseInt(m.int_dnum) + parseInt(adViewData.value[index - 1].sum);
      }
      m.sum = sum;
      m.int_new_day = 'D' + m.int_day + '(' + sum + ')';
      return m;
    });
  };
  // 获取ADView的series的name
  const getADViewSeriesName = () => {
    option.series[0].name = 'A';
    option.series[0].encode = {
      x: 'int_anum',
      y: 'int_new_day',
    };
    option.series[1].name = 'D';
    option.series[1].encode = {
      x: 'int_dnum',
      y: 'int_new_day',
    };
    option.series[1].label.formatter = function (params) {
      return params.data.int_anum + params.data.int_dnum;
    };
  };
  // 获取PView的series的name
  const getPViewSeriesName = () => {
    option.series[0].name = '';
    option.series[0].encode = {
      x: 'int_set_pnum',
      y: 'int_new_day',
    };
    option.series[1].name = '';
    option.series[1].encode = {
      x: 'int_no',
      y: 'int_new_day',
    };
    option.series[1].label.formatter = function (params) {
      return params.data.int_pnum;
    };
  };
  // get dataset source
  const getDatasetSource = (typeView) => {
    if (typeView === PDA_VIEW_P || typeView === EDD_UPDATE_TASK_PDA) {
      const realData = unref(pViewData) ?? [];
      option.dataset.source = getPViewDatasetSource(realData);
      getPViewSeriesName();
    } else {
      const realData = unref(adViewData) ?? [];
      option.dataset.source = getADViewDatasetSource(realData);
      getADViewSeriesName();
    }
  };

  // init chart
  const initChart = () => {
    option.toolbox.feature.saveAsImage.name = flagView.value === PDA_VIEW_P ? 'PView' : 'ADView';
    setDataZoom(option.dataset.source);
    myChart.value.setOption(option);
    myChart.value.hideLoading();
    window.addEventListener('resize', () => {
      myChart.value.resize({
        width: 'auto',
      });
    });
    clickSeries();
  };

  // 点击柱子的抽屉
  const drawerState = reactive({
    isShowDrawer: false,
    date: '',
    title: '',
    flag: '',
  });

  const openDrawer = (date, flag) => {
    drawerState.isShowDrawer = true;
    drawerState.date = date;
    drawerState.title = date + '零件列表';
    drawerState.flag = flag;
  };

  // 点击series的事件
  const clickSeries = () => {
    myChart.value.on('click', 'series', (params) => {
      const date = params.value.dt_date;
      if (flagView.value === PDA_VIEW_P) {
        openDrawer(date, 'P');
      } else if (flagView.value === PDA_VIEW_AD) {
        openDrawer(date, 'AD');
      } else {
        openDrawer(date, 'dm_task_p');
      }
    });
  };

  return {
    getPViewData,
    getDatasetSource,
    chartRef,
    initChart,
    myChart,
    drawerState,
    pViewTotal,
    exceedTotal,
    neverTotal,
    getPViewEddTypeNum
  };
}
