import { submitFeedback,saveFeedBack } from '../../../api/teams/index.js'
const { useVModel } = VueUse
const { defineComponent, ref } = Vue
const { ElMessage } = ElementPlus
export const TeamFeedback = defineComponent({
  name: 'TeamFeedback',
  props: {
    task: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const openFeedback = useVModel(props, 'visible')
    const feedback = ref('')
    const handleClose = () => {
      openFeedback.value = false
    }
    const handleSubmit = async () => {
      // const params = {
      //   planid: props.task.planId,
      //   status: props.task.status,
      //   reason: feedback.value,
      // }


      const params = props.task.task.map(item => ({
        id_wo: props.task.id_wo,
        id_shift:props.task.id_shift,
        str_shift:props.task.str_shift,
        id_task:item.taskId,
        id_sm:item.id_sm,
        dt_feed:props.task.plan_date,
        int_status: props.task.status,
        str_content: feedback.value,
        str_flow:props.task.str_flow,
        str_group:props.task.str_group,
        str_feed_back_type:props.task.handover_type
        
      }))

      try {
        await saveFeedBack(params)
        openFeedback.value = false
        ElMessage.success('反馈成功')
        emit('refresh')
      } catch (error) {
        ElMessage.error('反馈失败')
      }
    }
    return {
      openFeedback,
      feedback,
      handleClose,
      handleSubmit,
    }
  },
  template: /*html*/ `
    <el-dialog class="common-dialog" v-model="openFeedback" title="班组反馈" width="50%">
      <label class="mb-2 el-form-item__label">Remark</label>
      <el-input v-model="feedback" type="textarea" :rows="4" placeholder="Please input" />
      <template #footer>
        <el-button type="primary" @click="handleClose">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit">Submit</el-button>
      </template>
    </el-dialog>
  `,
})

export default TeamFeedback
