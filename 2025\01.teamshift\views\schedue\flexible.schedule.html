<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>灵活排班</title>
    <!-- CSS -->
    <link rel="stylesheet" href="../../styles/tailwind.css" />
    <link rel="stylesheet" href="../../styles/common.dialog.css" />
    <link rel="stylesheet" href="../../assets/element-plus@2.9.4/dist/index.css" />
    <link rel="stylesheet" href="../../styles/schedue/flexible.schedule.css" />
    <!-- CDN js部分 -->
    <script src="../../assets/vue@3.5.13/vue.global.js"></script>
    <script src="../../assets/element-plus@2.9.4/dist/index.full.js"></script>
    <script src="../../assets/sortablejs@latest/Sortable.min.js"></script>
    <script src="../../assets/element-plus@2.9.4/icons-vue/index.full.js"></script>
    <script src="../../assets/moment/moment.min.js"></script>
    <script src="../../assets/lodash@4.17.21/lodash.min.js"></script>
    <script src="../../assets/element-plus@2.9.4/dist/locale/zh-cn.js"></script>
    <!-- api部分 -->
    <script src="../../assets/axios@1.6.7/axios.min.js"></script>
  </head>
  <body>
    <div id="app">
      <flexible-schedule></flexible-schedule>
    </div>
  </body>
  <script type="module">
    import FlexibleSchedule from './flexible.schedule.js'
    const { createApp } = Vue
    const app = createApp({
      components: {
        FlexibleSchedule,
      },
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
    app.mount('#app')
  </script>
</html>
