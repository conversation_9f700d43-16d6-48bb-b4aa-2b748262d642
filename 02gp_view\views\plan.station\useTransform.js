export function useTransform() {
  const transformSubPlansToGanttData = (item) => {
    return {
      id: item.id,
      text: item.str_node,
      start_date: item.dt_start ? moment(item.dt_start).format('YYYY-MM-DD') : null,
      dt_start: item.dt_start ? moment(item.dt_start).format('YYYY-MM-DD') : null,
      duration: item.duration,
      dt_end: item.dt_end ? moment(item.dt_end).format('YYYY-MM-DD') : null,
      status: item.int_status,
      person: item.person,
      progress: +item.dec_percent,
      mpStart: item.mpStart,
      mpEnd: item.mpEnd,
      ekdDate: item.ekdDate,
      taskSeq: item.taskSeq,
      preTask: item.preTask,
      parent: item.id_root,
      level: item.int_level,
      id_task: item.id_task,
      dt_mp: item.dt_mp,
      dt_ekd: item.dt_ekd,
      owner: item.id_dept,
      name: item.str_esn,
      str_engine_type: item.str_engine_type,
    }
  }
  const transformGanttDataToSubPlans = (item) => {
    return {
      id: item.id,
      str_node: item.text,
      dt_start: item.start_date,
      dt_end: item.dt_end,
      int_status: item.status,
      dec_percent: item.progress,
      mpStart: item.mpStart,
      mpEnd: item.mpEnd,
      ekdDate: item.ekdDate,
      taskSeq: item.taskSeq,
      preTask: item.preTask,
      parent: item.id_root,
      level: item.int_level,
      id_task: item.id_task,
      dt_mp: item.dt_mp,
      dt_ekd: item.dt_ekd,
      id_dept: item.owner,
    };
  };
  return {
    transformSubPlansToGanttData,
    transformGanttDataToSubPlans
  };
}
