@import './common/common.drawer.css';
.common-dialog {
  padding: 0 !important;
  /* 弹框整体样式 */
  .el-dialog {
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }

  .el-dialog__header {
    margin: 0;
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    position: relative;

    .el-dialog__title {
      font-size: 15px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 16px;
    max-height: calc(90vh - 250px);
    overflow-y: auto;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;

      &:hover {
        background-color: #c0c4cc;
      }
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    margin: 0;
    padding: 12px 16px;
    border-top: 1px solid #e4e7ed;
    background-color: #f5f7fa;
  }

  /* 弹框动画效果 */
  &.el-dialog-fade-enter-active {
    animation: dialogFadeIn 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: translateY(-15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 特殊弹窗样式 */
.special-dialog {
  padding: 0 !important;
  /* 弹框整体样式 */
  .el-dialog {
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }

  .el-dialog__header {
    margin: 0;
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    position: relative;

    .el-dialog__title {
      font-size: 15px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 16px;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;

      &:hover {
        background-color: #c0c4cc;
      }
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-dialog__footer {
    margin: 0;
    padding: 12px 16px;
    border-top: 1px solid #e4e7ed;
    background-color: #f5f7fa;
  }

}
