/**
 * @description FlowScoreSummary
 */
const { ref, reactive, onMounted } = Vue
import { post,apiEnvironment } from '../../../config/axios/httpReuest.js'
export default {
  name: 'FlowScoreSummary',
  setup(props, { emit }) {
    // 获取url参数
    const url = new URL(window.location.href)
    // 查询参数
    const params = new URLSearchParams(url.search)
    const searchParam = params.get('param_value')
    const searchParamObj = ref({})
    if (searchParam) {
      const jsonString = searchParam?.replace(/'/g, '"')?.replace(/(\w+):/g, '"$1":')
      searchParamObj.value = JSON.parse(jsonString)
    }
    const searchForm = reactive({
      dtBeginMonth: searchParamObj.value.str_calculate_month ?? moment().startOf('year').format('YYYY-MM'),
      dtEndMonth: searchParamObj.value.str_calculate_month ?? moment().format('YYYY-MM'),
    })

    const tableRef = ref(null)
    const tableData = ref([])
    // 表头数据
    const headerColGroup = ref([])
    // 获取表头数据
    const getHeaderColGroup = async () => {
      const params = {
        ac: 'pe_search_flow_score_head',
      }
      const { data: res } = await post(params)
      const { data } = res
      headerColGroup.value = data
    }

    // 初始化表格数据
    const initTableData = async () => {
      const params = {
        ac: 'pe_search_flow_score_summary',
        dt_begin_month: searchForm.dtBeginMonth,
        dt_end_month: searchForm.dtEndMonth,
      }
      const { data: res } = await post(params)
      const { data } = res
      tableData.value = data.map((item, index, array) => {
        // 将数据的第一行和第5行的数据相加
        if ((index + 1) % 6 === 0) {
          const firstRow = array[index - 5]
          const fifthRow = array[index - 1]
          headerColGroup.value.forEach((headerItem, headerIndex) => {
            let headerTotal = 0
            let footerTotal = 0
            headerItem.columnList.forEach((colItem) => {
              headerTotal += firstRow[colItem.field]
              footerTotal += fifthRow[colItem.field]
            })
            item[headerItem.columnList[0].field] = ((footerTotal / headerTotal) * 100).toFixed(2) + '%'
          })
        }
        return item
      })
      tableData.value = data.map((item, index, array) => {
          if(tableData.value.length - index=== 1){
            let bbbData = tableData.value.filter(item => item.function === 'Reached ratio');
            // 获取所有字段名（排除 function 字段）
            let fields = Object.keys(bbbData[0]).filter(field => field === 'cfm_dis' ||  field === 'cfm_ins' ||  field === 'cfm_rep' ||  field === 'cfm_kit' ||  field === 'b2_cfm_dis');
            // 计算每个字段的平均值
            let count = bbbData.length;
            if(count > 0){
            let sum = 0;
            let averages = {};
            fields.forEach(field =>{
              const values = bbbData.map(obj => parseFloat(obj[field].slice(0, -1)));
              const sum = values.reduce((acc, value) => acc + value, 0);
              item[field] =  (sum / values.length).toFixed(2)+'%';
              });
            }
        }
        return item
      })
    }

    const handleSearch = async () => {
      await initTableData()
      getMergeCells()
    }

    const handleExport = async() => {
      const params = {
        ac: 'pe_export_flow_summary',
        dt_begin_month: searchForm.dtBeginMonth,
        dt_end_month: searchForm.dtEndMonth,
      }
      const { data} = await post(params)
        const { code, text, data: res } = data
        if (code === 'success') {
          if (data && data.data) {
            // 重定向到Excel文件的URL以下载文件
            window.location.href = apiEnvironment + data.data
            searchClick();
          } else {
            alert('无法下载文件')
          }
        } else {
          ElementPlus.ElMessage.error(text)
        }

    }

    const mergeCells = ref([])
    const flagIndex = ref(0)

    const getMergeCells = () => {
      
      tableData.value.forEach((item, index) => {
        if ((index + 1) % 6 === 0 || (index + 1) % 6 === 1 || tableData.value.length - index<= 3) {
          flagIndex.value = index + 1
          mergeCells.value.push({ row: index, col: 0, rowspan: 1, colspan: 2 })
          if ((index + 1) % 6 === 0 || tableData.value.length - index=== 1) {
            // 根据头部的列数来合并
            let colIndex = 2
            headerColGroup.value.forEach((headerItem, headerIndex) => {
              if (headerIndex !== 0) {
                colIndex += headerColGroup.value[headerIndex - 1].columnList.length
              }
              mergeCells.value.push({
                row: index,
                col: colIndex,
                rowspan: 1,
                colspan: headerItem.columnList.length,
              })
            })
          }
        } 
        else {
          // 获取合并单元格的第一个行数
          mergeCells.value.push({
            row: flagIndex.value,
            col: 0,
            rowspan: 4,
            colspan: 1,
          })
        }
      })
    }

    const handleRowClass = ({ row, rowIndex }) => {
      if ((rowIndex + 1) % 6 === 1) {
        return 'bg-gray-200 text-black'
      }
      if ((rowIndex + 1) % 6 === 0) {
        return 'bg-gray-100 text-black'
      }

      if (tableData.value.length - rowIndex === 1) {
        return 'bg-gray-300 border-white text-black'
      }
    }

    const handleHeaderRowClass = ({ $rowIndex }) => {
      return 'bg-gray-300 border-white text-black'
    }

    const exportExcel = () => {
      const $table = tableRef.value
      const { tableData } = $table.getTableData()

      // 去除_X_ROW_KEY
      const realData = tableData.map((item) => {
        const obj = {}
        for (const key in item) {
          if (key !== '_X_ROW_KEY') {
            obj[key] = item[key]
          }
        }
        return obj
      })

      // 将a转换为exportHeader
      const convertAToB = (a) => {
        const headerRow = ['Selection/Flow', '']
        const subHeaderRow = ['Function', '']

        a.forEach((item) => {
          headerRow.push(item.title)
          for (let i = 1; i < item.columnList.length; i++) {
            headerRow.push('')
          }
          item.columnList.forEach((column) => {
            subHeaderRow.push(column.title)
          })
        })

        return [headerRow, subHeaderRow]
      }

      // 导出表头
      const exportHeader = convertAToB(headerColGroup.value)
      // 合并头部单元格
      // const mergeHeaderCells = [
      //   // 合并第一行第一个单元格
      //   { s: { r: 0, c: 0 }, e: { r: 0, c: 1 } },
      //   // 合并第一行第三个单元格
      //   { s: { r: 0, c: 2 }, e: { r: 0, c: 5 } },
      //   // 合并第一行第六个单元格
      //   { s: { r: 0, c: 6 }, e: { r: 0, c: 7 } },
      //   // 合并第一行第7个单元格
      //   { s: { r: 0, c: 8 }, e: { r: 0, c: 9 } },
      //   // 合并第一行第8个单元格
      //   { s: { r: 0, c: 10 }, e: { r: 0, c: 11 } },
      //   // 合并第一行第8个单元格
      //   { s: { r: 0, c: 12 }, e: { r: 0, c: 15 } },
      //   { s: { r: 1, c: 0 }, e: { r: 1, c: 1 } },
      // ]

      const worksheet = XLSX.utils.json_to_sheet(realData, {
        header: exportHeader[0],
      })
      // const worksheet = XLSX.utils.aoa_to_sheet(exportHeader)

      // XLSX.utils.sheet_add_json(worksheet, realData, { skipHeader: true, origin: exportHeader.length })

      // 合并单元格
      worksheet['!merges'] = mergeHeaderCells

      // mergeCells.value.forEach((item) => {
      //   const range = {
      //     s: { r: item.row + 2, c: item.col },
      //     e: { r: item.row + 2 + item.rowspan - 1, c: item.col + item.colspan - 1 },
      //   }
      //   worksheet['!merges'] = worksheet['!merges'] || []
      //   worksheet['!merges'].push(range)
      // })
      // //对worksheet去重
      // worksheet['!merges'] = worksheet['!merges'].filter((item, index, self) => {
      //   return self.findIndex((t) => t.s.r === item.s.r && t.s.c === item.s.c) === index
      // })

      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      XLSX.writeFile(workbook, 'flow_score_summary.xlsx')
    }

    onMounted(async () => {
      await getHeaderColGroup()
      await initTableData()
      getMergeCells()
    })
    return {
      searchForm,
      tableRef,
      tableData,
      headerColGroup,
      mergeCells,
      handleSearch,
      handleExport,
      handleRowClass,
      handleHeaderRowClass,
      exportExcel,
    }
  },
  template: /*html*/`
    <div class="m-4">
      <el-form :model="searchForm" inline>
        <el-form-item label="Begin Month">
          <el-date-picker
            v-model="searchForm.dtBeginMonth"
            type="month"
            placeholder="Select month"
            value-format="YYYY-MM"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="End Month">
          <el-date-picker
            v-model="searchForm.dtEndMonth"
            type="month"
            placeholder="Select month"
            value-format="YYYY-MM"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">Search</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="handleExport">Export Excel</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- <el-button type="primary" @click="exportExcel">Export Excel</el-button> -->
    <vxe-table
      class="m-4"
      border="full" 
      round
      ref="tableRef"
      :data="tableData"
      :merge-cells="mergeCells"
      :row-class-name="handleRowClass"
      :header-row-class-name="handleHeaderRowClass"
    >
      <vxe-colgroup title="Selection/Flow" align="center">
        <vxe-column title="Function" field="function"></vxe-column>
        <vxe-column title="" field="strType"></vxe-column>
      </vxe-colgroup>
      <vxe-colgroup v-for="(item,index) in headerColGroup" :key="index" :title="item.title" align="center">
        <vxe-column
          v-for="(col, index1) in item.columnList"
          :key="index1"
          :field="col.field"
          :title="col.title"
          :min-width="col.minWidth"
          align="center"
        ></vxe-column>
      </vxe-colgroup>
    </vxe-table>
  `,
}
