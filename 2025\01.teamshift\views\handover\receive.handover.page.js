import { queryHandover, receiveHandover } from '../../api/handover/index.js'
const { nextTick, onMounted } = Vue
export default {
  name: 'ReceiveHandover',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    row: {
      type: Object,
      default: () => {},
    },
    buinessType: {
      type: String,
      default: '',
    }
  },
  emits: ['update:visible', 'refresh'],
  setup(props, { emit }) {
    const { ref, useModel } = Vue
    const { ElMessage, ElLoading } = ElementPlus

    const baseInfo = ref({
      esn: props.row.esn,
      wo: props.row.wo,
      type: props.row.str_task_type,
      team: props.row.team,
      leader: props.row.leader,
      shift: props.row.shift,
    })

    // 表格数据
    const tableData = ref([])

    // 抽屉可见性控制
    const drawerVisible = ref(false)
    // 当前选中的文件列表
    const currentFiles = ref([])

    // 计算每个Legend的行数，用于合并单元格
    const getSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1) {
        // Code和Legend列
        const rows = tableData.value
        const currentCode = row.code

        // 向上找到第一个具有相同code的行的索引
        let startIndex = rowIndex
        while (startIndex > 0 && rows[startIndex - 1].code === currentCode) {
          startIndex--
        }

        // 如果是该分组的第一行
        if (rowIndex === startIndex) {
          // 计算具有相同code的行数
          let spanCount = 1
          let nextIndex = rowIndex + 1
          while (nextIndex < rows.length && rows[nextIndex].code === currentCode) {
            spanCount++
            nextIndex++
          }
          return {
            rowspan: spanCount,
            colspan: 1,
          }
        } else {
          // 不是第一行则隐藏
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    }

    // 查看文件
    const handleViewFiles = (row) => {
      currentFiles.value = row.attachment || []
      drawerVisible.value = true
    }

    // 弹窗可见性控制
    const dialogVisible = useModel(props, 'visible')

    const handleCloseDialog = () => {
      dialogVisible.value = false
    }

    // 接收交接单
    const handleReceiveHandover = async () => {
      let loading = null
      try {
        loading = ElLoading.service({
          lock: true,
          text: '正在接收...',
          background: 'rgba(0, 0, 0, 0.7)',
        })

        // 获取所有isTransferred为true的行
        const transferRows = tableData.value.filter((item) => item.isTransferred)
        const postData = {
          id_main: props.row.id,
          ids: transferRows.map((item) => item.id).join(','),
        }

        await receiveHandover(postData)
        ElMessage.success('接收成功')
        dialogVisible.value = false
        emit('refresh')
      } catch (error) {
        if (!error.isHandled) {
          ElMessage.error('接收失败：' + (error.message || '未知错误'))
          error.isHandled = true
        }
      } finally {
        loading?.close()
      }
    }

    const tableRef = ref(null)
    // 获取交接单
    const getHandover = async () => {
      const codeMap = {
        10: 'Tooling',
        20: 'Man Power',
        30: 'Parts',
        40: 'Doc (JC/Tag)',
        50: 'WS/Process',
        60: 'Special cp',
        70: 'Others',
      }

      const shiftMap = {
        0: '休息',
        1: '行政班',
        2: '晚班',
        3: '早班',
      }
      const param = {
        id: props.row.id,
        id_sms: props.row.id_sm,
      }
      let loading = null
      try {
        loading = ElLoading.service({
          lock: true,
          text: '加载中...',
          background: 'rgba(0, 0, 0, 0.7)',
          target: tableRef.value?.$el,
        })
        const res = await queryHandover(param)

        // 班次
        baseInfo.value.shift = res.pTHandoverMain.str_shift
        // 排序
        res.pTHandovers.sort((a, b) => a.pTHandover.int_type - b.pTHandover.int_type)

        // 先按code分组，找出每组的最后一个元素
        const codeGroups = {}
        res.pTHandovers.forEach((item) => {
          const code = item.pTHandover.int_type
          if (!codeGroups[code]) {
            codeGroups[code] = []
          }
          codeGroups[code].push(item)
        })

        tableData.value = res.pTHandovers.map((item) => {
          const code = item.pTHandover.int_type
          // 判断当前项是否是该code组的最后一个元素
          const isLastInGroup = codeGroups[code][codeGroups[code].length - 1] === item

          return {
            id: item.pTHandover.id,
            code: code,
            legend: codeMap[code],
            sm: item.pTHandover.str_sm,
            desc: item.pTHandover.str_content,
            isTransferred: item.pTHandover.int_receive_status === 1,
            isOriginal: isLastInGroup, // 只有最后一行标记为原始行
            attachment: item.files,
          }
        })
      } catch (error) {
        // if (!error.isHandled) {
        //   ElMessage.error('获取交接单失败：' + (error.message || '未知错误'))
        //   error.isHandled = true
        // }
      } finally {
        loading?.close()
      }
    }

    // 下载文件
    const handleDownload = (row) => {
      const loading = ElLoading.service({
        lock: true,
        text: '正在下载...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      try {
        // 不用window.open，而是用a标签下载
        const a = document.createElement('a')
        a.href = row.str_path
        a.download = row.str_file_name
        a.click()
        a.remove()
      } catch (error) {
        ElMessage.error('下载失败：' + (error.message || '未知错误'))
      } finally {
        loading.close()
      }
    }

    // 监听props.visible的变化
    const watchVisible = (newVal) => {
      if (newVal) {
        // 当弹窗显示时，更新基本信息并获取交接单
        baseInfo.value = {
          esn: props.row.str_esn,
          wo: props.row.str_wo,
          type: props.row.str_type,
          leader: props.row.team_leader,
        }
        nextTick(() => {
          getHandover()
        })
      }
    }

    // 监听visible属性变化
    Vue.watch(() => props.visible, watchVisible)

    onMounted(() => {
      if (props.visible) {
        getHandover()
      }
    })

    return {
      baseInfo,
      tableData,
      dialogVisible,
      drawerVisible,
      currentFiles,
      tableRef,
      handleViewFiles,
      getSpanMethod,
      handleCloseDialog,
      handleReceiveHandover,
      handleDownload,
    }
  },
  template: /*html*/ `
      <el-dialog v-model="dialogVisible" title="交接文件接收" width="80%" class="common-dialog" :show-close="false">
        <div class="mb-4">
          <el-descriptions :column="5" border>           
            <el-descriptions-item v-if="buinessType!=102" label="ESN">{{ baseInfo.esn }}</el-descriptions-item>
            <el-descriptions-item v-if="buinessType!=102" label="WO">{{ baseInfo.wo }}</el-descriptions-item>
            <el-descriptions-item v-if="buinessType==102 || buinessType==101" label="工序">{{ row.str_task_type }}</el-descriptions-item>
            <el-descriptions-item v-if="buinessType!=102 && buinessType !=101" label="组长">{{ baseInfo.leader }}</el-descriptions-item>
            <el-descriptions-item label="班次">{{ baseInfo.shift }}</el-descriptions-item>
          </el-descriptions>ss
        </div>
        <el-table ref="tableRef" :data="tableData" border :span-method="getSpanMethod">


          <el-table-column prop="code" label="Code" width="80" />
          <el-table-column prop="legend" label="Legend" width="120" />
          <el-table-column prop="sm" label="SM" width="100" />
          <el-table-column prop="desc" label="Desc">
            <template #default="{ row }">
              <div class="p-2">{{ row.desc || '无交接内容' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="Attachment" width="120" align="center">
            <template #default="{ row }">
              <div class="flex justify-center">
                <el-button v-if="row.attachment && row.attachment.length > 0" type="primary" link @click="handleViewFiles(row)">
                  <el-icon><View /></el-icon>
                </el-button>
                <span v-else>无附件</span>
              </div>
            </template>
          </el-table-column>
          <!--直接确认交接全部-->
          <!--<el-table-column label="交接状态" width="100" align="center">
            <template #default="{ row }">
              <el-checkbox v-model="row.isTransferred"  />
            </template>
          </el-table-column>-->
        </el-table>
        <template #footer>
          <el-button type="danger" @click="handleCloseDialog">取消</el-button>          
          <el-button v-if="row.int_status !== 1 && row.int_handover_status !== 0" type="primary" @click="handleReceiveHandover">确认接收</el-button>
        </template>
      </el-dialog>

      <!-- 文件查看抽屉 -->
      <el-drawer v-model="drawerVisible" title="文件列表" size="60%" direction="rtl">
        <el-table :data="currentFiles" border>
          <el-table-column prop="str_file_name" label="文件名">
            <!-- 处理文件名，只显示文件名，不显示后缀 eg: a.png -->
            <template #default="{ row }">{{ row.str_file_name.split('.')[0] }}</template>
          </el-table-column>
          <el-table-column prop="str_type" label="类型" width="100">
            <!-- 处理文件类型，只显示后缀 eg: a.png -->
            <template #default="{ row }">{{ row.str_file_name.split('.')[1] }}</template>
          </el-table-column>
          <el-table-column prop="dt_up" label="上传时间" width="180" />
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button text type="primary" @click="handleDownload(row)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-drawer>
  `,
}
