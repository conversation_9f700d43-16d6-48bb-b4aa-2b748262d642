export default {
  name: 'WhiteToYellowConverter',
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const { ref, reactive, onMounted, toRefs, computed } = Vue

    const state = reactive({
      pendingPnr: 0, // 待转PNR
      convertedPnr: 0, // 已转PNR
      pendingScan: 0, // 待点灯扫描
    })

    const loading = ref(false)

    // 假设这里会有一个获取数据的方法，暂时为空
    const getData = async () => {
      loading.value = true
      // 模拟数据加载
      await new Promise(resolve => setTimeout(resolve, 500));
      state.pendingPnr = 100
      state.convertedPnr = 250
      state.pendingScan = 50
      loading.value = false
    }

    const drawerState = reactive({
      visible: false,
      type: '',
    })

    const handleOpenDrawer = (type) => {
      drawerState.visible = true
      drawerState.type = type
      // 在实际应用中，这里可能会触发一个emit事件或者加载抽屉内容
      console.log(`打开抽屉类型: ${type}`)
    }

    onMounted(() => {
      getData()
    })

    const totalCount = computed(() => state.pendingPnr + state.convertedPnr + state.pendingScan)

    return {
      ...toRefs(state),
      loading,
      totalCount,
      handleOpenDrawer,
      drawerState
    }
  },
  template: /*html*/ `
    <div :id="'white-to-yellow-' + id" class="h-full">
      <el-skeleton :loading="loading" animated>
        <template #template>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
        </template>
        <template #default>
          <el-descriptions class="my-descriptions" :column="1" border>
            <el-descriptions-item label="待转PNR:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-500" @click="handleOpenDrawer('pendingPnr')">
                {{ pendingPnr }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="已转PNR:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-500" @click="handleOpenDrawer('convertedPnr')">
                {{ convertedPnr }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="待点灯扫描:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-500" @click="handleOpenDrawer('pendingScan')">
                {{ pendingScan }}
              </div>
            </el-descriptions-item>
             <el-descriptions-item label="总计:" align="center">
              <div class="font-bold">
                {{ totalCount }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </template>
      </el-skeleton>
    </div>
  `,
}
