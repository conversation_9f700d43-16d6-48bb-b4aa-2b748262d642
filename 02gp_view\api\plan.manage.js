/**
 * @description project计划演算API
 */
import { post } from '../../config/axios/httpReuest.js'

export const queryCalculationList = async (queryLists = []) => {
  const list = []
  const params = {
    ac: 'gp_planmain_list',
    queryLists,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return list
  }
  list.push(...data.data)
  return list
}

/**
 * @description 根据id获取gantt
 * @param {string} id
 * @param {string} str_node
 */
export const queryGanttById = async (id = null, str_node = '', dt_start = '', dt_end = '', str_engine_type = '') => {
  let res = null
  const params = {
    ac: 'gp_planmain_gantt',
    id,
    str_node,
    ...(dt_start && { dt_start }),
    ...(dt_end && { dt_end }),
    ...(str_engine_type && { str_engine_type }),
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return res
  }
  res = data.data
  return res
}

/**
 * @description 保存Project
 * @param {object} data
 * @return {Promise<boolean>}
 */
export const saveProject = async (data) => {
  const params = {
    ac: 'gp_planmain_ganttupdate',
    data,
  }
  const { data: res } = await post(params)
  if (res.code === 'error') {
    ElementPlus.ElMessage.error(res.text)
    return false
  }
  ElementPlus.ElMessage.success(res.text)
  return true
}

/**
 * @description 更新Project
 * @param {object}data
 * @return {Promise<boolean>}
 */
export const updateProject = async (data) => {
  const params = {
    ac: 'gp_plan_edit',
    data,
  }
  const { data: res } = await post(params)
  if (res.code === 'error') {
    ElementPlus.ElMessage.error(res.text)
    return false
  }
  ElementPlus.ElMessage.success(res.text)
  return true
}

/**
 * @description 查询Project List
 * @param queryLists
 * @return {Promise<*[]>}
 */
export const queryProjectByQuery = async (queryLists = []) => {
  const list = []
  const params = {
    ac: 'gp_plan_search',
    queryLists,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return list
  }
  list.push(...data.data)
  return list
}

/**
 * @description 根据id获取Project
 * @param {string} id
 */
export const queryProjectById = async (id) => {
  let res = null
  const params = {
    ac: 'gp_plan_search_by_id',
    id,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return res
  }
  res = data.data
  return res
}

/**
 * @description 删除Project
 * @param {array<string>} ids
 * @return {Promise<boolean>}
 */
export const delProject = async (ids) => {
  const params = {
    ac: 'gp_plan_del',
    lists: ids,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}

/**
 * @description 启用禁用Project
 * @param {array<string>} ids
 * @return {Promise<boolean>}
 */
export const activeProjectApi = async (row) => {
  const { id, is_state } = row
  const dealType = is_state === 1 ? 0 : 1
  const params = {
    ac: 'gp_planmain_updatestate',
    id,
    int_state: dealType,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}

/**
 * @description 获取部门
 * @param {array<string>} ids
 * @return {Promise<boolean>}
 */
export const getDept = async (ids) => {
  const params = {
    ac: 'rcpa_get_hr_dept',
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }

  return data.data
}
/**
 * @description 站位甘特图
 * @param {array<string>} ids
 * @return {Promise<boolean>}
 */
export const getStationGantt = async (id, str_node, dt_start, dt_end, str_engine_type) => {
  const params = {
    ac: 'gp_occupy_gantt',
    id,
    str_node,
    ...(dt_start && { dt_start }),
    ...(dt_end && { dt_end }),
    ...(str_engine_type && { str_engine_type }),
  }

  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }

  return data.data
}
