<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!--    引入element-plus的样式-->
    <link href="../../../assets/element-plus@2.5.5/index.css" rel="stylesheet">
    <!--    引入VUE-->
    <script src="../../../assets/vue@3.4.15/vue.global.prod.js"></script>
    <!-- 引入tailwindcss -->
    <link href='../../../assets/css/tailwind.css' rel='stylesheet'>
    <!--    全部样式-->
    <link href='../../../assets/css/index.css' rel='stylesheet'>
    <!--    引入element-plus-->
    <script src="../../../assets/element-plus@2.5.5/index.js"></script>
    <!--  引入element-plus-icon-->
    <script src="../../../assets/icons-vue@2.3.1/index.iife.min.js"></script>
    <!--    引入axios-->
    <script src="../../../assets/axios@1.6.7/axios.min.js"></script>
    <script src='../../../assets/moment/moment.min.js'></script>
    <script src='../../../assets/@vueuse/shared@10.7.2/index.iife.min.js'></script>
    <script src='../../../assets/@vueuse/core@10.7.2/index.iife.min.js'></script>

    
  <title>发动机视图</title>
</head>

<body>
  <div id='app'>
    <y-mp-wo></y-mp-wo>
  </div>
</body>

<script type='module'>
  import yMpGlobal from '../../../02gp_view/00view/01global/01wo.compent.js'
  const { createApp, ref } = Vue
  const app = createApp({
    components: {
      
    },
  })
  app.component('y-mp-wo', yMpGlobal); // 第三方组件
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component);
    }
    app.use(ElementPlus);
    app.mount('#app');

</script>
<style>
  .el-badge__content.is-fixed {
    top: -4px;
  }

  .my_red .el-badge__content.is-fixed {
    background-color: red;
  }

  .my_green .el-badge__content.is-fixed {
    background-color: green;
  }
  .my_gray .el-badge__content.is-fixed {
    background-color: #A8ABB2;
  }
</style>

</html>