import { post } from '../../config/axios/httpReuest.js';
import {
  ADMINISTRATIVE_RECEIVE,
  CSM_CONFIRM_PART,
  EXPORT_TRANSPORT_NODE,
  F1_2_NODE,
  F2_NODE,
  IMPORT_TRANSPORT_NODE,
  INCOMING_INSPECTION_NODE,
  LIGHT_UP_NODE,
  LOCK_BACKUP_BOARD,
  LOCK_HIGH_AVAILABLE,
  LOCK_LOW_AVAILABLE_NEW,
  PURCHASE_FACTORY_DELIVERY,
  PURCHASE_HIGH_AVAILABLE,
  PURCHASE_LOW_AVAILABLE_NEW,
  SEND_MATERIAL_NODE,
  START_SHIPMENT_NODE,
  SUBCONTRACT_NODE,
  SUBCONTRACT_PO,
  WAREHOUSING_NODE,
  CONFIGURATION_PART,
} from '../../config/nodeKey.js';
import { useFilter } from './useFilter.js';

const { shallowRef, ref, unref, reactive } = Vue;

/**
 * 总量视图
 * @param updateCurrentDate {function}
 */
export function useTotalView(updateCurrentDate) {
  let option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    legend: {},
    grid: {
      left: '3%',
      right: '5%',
      bottom: '3%',
      containLabel: true,
    },
    // 工具栏
    toolbox: {
      show: true,
      feature: {
        // 保存为图片
        saveAsImage: {
          show: true,
          title: '保存为图片',
          name: '总量视图',
        },
      },
    },
    xAxis: {
      type: 'category',
      triggerEvent: true,
      nameTextStyle: {
        fontWeight: 'bolder',
      },
    },
    yAxis: [
      {
        type: 'value',
        axisLabel: {},
        splitLine: {
          show: true,
        },
        alignTicks: true,
        axisLine: {
          show: true,
        },
        axisTick: {
          show: true,
        },
      },
      {
        type: 'value',
        name: 'Total\n任务总量',
        show: true,
        splitLine: {
          show: true,
        },
        alignTicks: true,
        axisLine: {
          show: true,
        },
        axisTick: {
          show: true,
        },
      },
    ],
    series: [],
    dataset: {},
  };

  // 获取参考产能series
  const getReferenceCapacitySeries = () => {
    return {
      name: 'Reference capacity \n 参考产能',
      type: 'line',
      step: 'middle',
      seriesLayoutBy: 'row',
      encode: {
        x: 'dt_date',
        y: 'num_capacity',
      },
      itemStyle: {
        color: '#d4d7de',
      },
      lineStyle: {
        type: 'dashed',
        color: '#d4d7de',
      },
      label: {
        show: true,
        position: 'left',
      },
    };
  };
  // 获取当日输入量series
  const getDayInputSeries = () => {
    return {
      name: 'Day import \n当日输入量',
      type: 'line',
      step: 'middle',
      seriesLayoutBy: 'row',
      encode: {
        x: 'dt_date',
        y: 'num_input',
      },
      itemStyle: {
        color: '#f3d19e',
      },
      lineStyle: {
        color: '#f3d19e',
      },
      label: {
        show: true,
      },
    };
  };
  // 获取当日排产量series
  const getDayProductionSchedulingSeries = () => {
    return {
      name: 'Day Production scheduling \n当日排产量',
      type: 'line',
      step: 'middle',
      seriesLayoutBy: 'row',
      encode: {
        x: 'dt_date',
        y: 'num_now_day',
      },
      itemStyle: {
        color: '#32CD32',
      },
      lineStyle: {
        color: '#32CD32',
        width: 3,
      },
      label: {
        show: true,
      },
    };
  };
  // 获取当日输出量series
  const getDayOutputSeries = () => {
    return {
      name: 'Day out \n当日输出量',
      type: 'line',
      step: 'middle',
      seriesLayoutBy: 'row',
      encode: {
        x: 'dt_date',
        y: 'num_out',
      },
      itemStyle: {
        color: '#00FF00',
      },
      lineStyle: {
        color: '#00FF00',
      },
      label: {
        show: true,
      },
    };
  };
  // 获取当日剩余任务量series
  const getDaySurplusTotalSeries = () => {
    return {
      name: 'Day surplus total \n当日剩余任务量',
      type: 'line',
      step: 'middle',
      seriesLayoutBy: 'row',
      encode: {
        x: 'dt_date',
        y: 'num_total',
      },
      itemStyle: {
        color: '#006600',
      },
      lineStyle: {
        color: '#006600',
        width: 3,
      },
      label: {
        show: true,
        position: 'left',
      },
    };
  };
  // 获取当日排产剩余量series
  const getDaySurplusProductionSchedulingSeries = () => {
    return {
      name: 'Day surplus production scheduling \n当日排产剩余量',
      type: 'line',
      step: 'middle',
      seriesLayoutBy: 'row',
      encode: {
        x: 'dt_date',
        y: 'num_now_day_surplus',
      },
      itemStyle: {
        color: '#FFD700',
      },
      lineStyle: {
        color: '#FFD700',
      },
      label: {
        show: true,
      },
    };
  };
  // 获取预计输出量series
  const getTotalPlannedOutputSeries = () => {
    return {
      name: 'Total planned output \n预计输出量',
      type: 'line',
      step: 'middle',
      seriesLayoutBy: 'row',
      encode: {
        x: 'dt_date',
        y: 'num_preout',
      },
      itemStyle: {
        color: '#8B4513',
      },
      lineStyle: {
        color: '#8B4513',
      },
      label: {
        show: true,
      },
    };
  };
  // 图表ref
  const chartRef = shallowRef(null);

  const totalViewData = ref([]);

  const myChart = shallowRef(null);
  // 获取请求的接口ac
  const getAc = (nodeKey) => {
    let isIncludeEx = false;
    switch (nodeKey) {
      case ADMINISTRATIVE_RECEIVE:
      case WAREHOUSING_NODE:
      case INCOMING_INSPECTION_NODE:
      case LIGHT_UP_NODE:
      case LOCK_BACKUP_BOARD:
      case SEND_MATERIAL_NODE:
        isIncludeEx = false;
        break;
      case PURCHASE_FACTORY_DELIVERY:
      case IMPORT_TRANSPORT_NODE:
      case SUBCONTRACT_NODE:
      case EXPORT_TRANSPORT_NODE:
      case LOCK_HIGH_AVAILABLE:
      case LOCK_LOW_AVAILABLE_NEW:
      case PURCHASE_HIGH_AVAILABLE:
      case PURCHASE_LOW_AVAILABLE_NEW:
      case START_SHIPMENT_NODE:
      case CSM_CONFIRM_PART:
      case SUBCONTRACT_PO:
      case F1_2_NODE:
      case F2_NODE:
      case CONFIGURATION_PART:
        isIncludeEx = true;
        break;
    }
    return isIncludeEx ? 'pda_GetPdaSummary' : 'pda_GetPdaSummary'; // 经验证pda_GetPdaSummaryEx 可以使用pda_GetPdaSummary 20240425
  };
  const { getFilterData } = useFilter();
  // 初始化series
  const initSeries = () => {
    return [
      {
        name: 'Total \n任务总量',
        type: 'line',
        step: 'middle',
        yAxisIndex: 1,
        seriesLayoutBy: 'row',
        encode: {
          x: 'dt_date',
          y: 'num_mtotal',
        },
        itemStyle: {
          color: '#FF0000',
        },
        lineStyle: {
          color: '#FF0000',
        },
        label: {
          show: true,
        },
      },
    ];
  };
  // 定义当前节点
  const currentNodeState = ref('');
  /**
   * 获取总量视图数据
   * @param strType {String} 类型
   * @param searchForm { object } 搜索条件
   *
   */
  const getTotalViewData = async (strType, searchForm) => {
    currentNodeState.value = strType;
    // 如果存在图形dom
    if (myChart.value) {
      myChart.value.dispose();
      myChart.value = null;
    }
    option.series = initSeries();
    myChart.value = echarts.init(chartRef.value);
    myChart.value.showLoading({
      text: 'loading...',
      showSpinner: true,
      textColor: 'black',
    });
    const dt_begin = searchForm.date[0];
    const dt_end = searchForm.date[1];
    const filter_fields = getFilterData(searchForm);

    const params = {
      ac: getAc(strType),
      dt_begin,
      dt_end,
      str_type: strType,
      filter_fields,
    };
    const { data } = await post(params);
    totalViewData.value = data.data;
  };
  /**
   * 获取x轴样式
   */
  const getXAxisLabel = (currentDate) => {
    option.xAxis.axisLabel = {
      color: (value) => {
        return moment(value).isSame(currentDate) ? 'red' : 'black';
      },
      formatter: (val) => {
        let data_t = totalViewData.value.find((f) => f.dt_date === val);
        const is_pc = data_t.is_pc;
        if (data_t.num_before_day) {
          // 如果is_pc,则显示(排x)
          return val + (is_pc ? `(排${data_t.num_now_day})` : '') + '\n' + `(负${data_t.num_before_day})`;
          // return val + `(排${data_t.num_now_day})` + '\n' + (is_pc ? '{hr|}' : '{none|}') + '\n' + '\n' + `(负${data_t.num_before_day})`;
        } else {
          return val + (is_pc ? `(排${data_t.num_now_day})` : '');
          // return val + `(排${data_t.num_now_day})` + '\n' + (is_pc ? '{hr|}' : '{none|}' + '\n');
        }
      },
      padding: [3, 10, 10, 5],
      rich: {
        hr: {
          width: '100%',
          borderColor: 'black',
          borderWidth: 0.5,
          height: 0,
          align: 'right',
        },
        none: {
          width: '100%',
          height: 1,
        },
      },
    };
  };

  // 获取三个节点的series
  const getThreeSeries = () => {
    // 获取参考产能series
    let referenceCapacitySeries = getReferenceCapacitySeries();
    // 获取当日输入量series
    let dayInputSeries = getDayInputSeries();
    option.series.push(referenceCapacitySeries, dayInputSeries);
  };
  // 获取四个节点的series
  const getFourSeries = () => {
    // 获取预计输出量series
    let totalPlannedOutputSeries = getTotalPlannedOutputSeries();
    // 获取当日剩余任务量series
    let daySurplusTotalSeries = getDaySurplusTotalSeries();
    // 获取当日输出量series
    let dayOutputSeries = getDayOutputSeries();
    option.series.push(totalPlannedOutputSeries, daySurplusTotalSeries, dayOutputSeries);
  };
  // 获取六条线的series
  const getSixSeries = () => {
    // 获取参考产能series
    let referenceCapacitySeries = getReferenceCapacitySeries();
    // 获取当日输入量series
    let dayInputSeries = getDayInputSeries();
    // 获取当日排产量series
    let dayProductionSchedulingSeries = getDayProductionSchedulingSeries();
    // 获取当日剩余任务量series
    let daySurplusTotalSeries = getDaySurplusTotalSeries();
    // 获取当日排产剩余量series
    let daySurplusProductionSchedulingSeries = getDaySurplusProductionSchedulingSeries();
    option.series.push(
      referenceCapacitySeries,
      dayInputSeries,
      dayProductionSchedulingSeries,
      daySurplusTotalSeries,
      daySurplusProductionSchedulingSeries
    );
  };

  // 获取是否显示三条线
  const getIsOnlyThreeLine = (currentNode) => {
    let isOnlyThreeLine = false;
    switch (currentNode) {
      case LOCK_HIGH_AVAILABLE:
      case LOCK_LOW_AVAILABLE_NEW:
      case LOCK_BACKUP_BOARD:
      case PURCHASE_HIGH_AVAILABLE:
      case PURCHASE_LOW_AVAILABLE_NEW:
      case CSM_CONFIRM_PART:
      case F1_2_NODE:
      case CONFIGURATION_PART:
        isOnlyThreeLine = true;
        break;
    }
    return isOnlyThreeLine;
  };
  // 获取是否显示四条线
  const getIsOnlyFourLine = (currentNode) => {
    let isOnlyFourLine = false;
    switch (currentNode) {
      case PURCHASE_FACTORY_DELIVERY:
      case IMPORT_TRANSPORT_NODE:
      case SUBCONTRACT_NODE:
      case EXPORT_TRANSPORT_NODE:
      case START_SHIPMENT_NODE:
      case SUBCONTRACT_PO:
      case F2_NODE:
        isOnlyFourLine = true;
        break;
    }
    return isOnlyFourLine;
  };
  // 获取是否显示六条线
  const getIsOnlySixLine = (currentNode) => {
    let isOnlySixLine = false;
    switch (currentNode) {
      case ADMINISTRATIVE_RECEIVE:
      case WAREHOUSING_NODE:
      case SEND_MATERIAL_NODE:
      case LIGHT_UP_NODE:
      case INCOMING_INSPECTION_NODE:
        isOnlySixLine = true;
        break;
    }
    return isOnlySixLine;
  };
  /**
   * 根据不同的节点获取不同的series
   */
  const getSeriesByCurrentNode = (currentNode) => {
    // 是否只有三条线
    const isOnlyThreeLine = getIsOnlyThreeLine(currentNode);
    // 是否只有四条线
    const isOnlyFourLine = getIsOnlyFourLine(currentNode);
    // 是否只有六条线
    const isOnlySixLine = getIsOnlySixLine(currentNode);
    if (isOnlyFourLine) {
      getFourSeries();
    } else if (isOnlySixLine) {
      getSixSeries();
    } else if (isOnlyThreeLine) {
      getThreeSeries();
    }
  };
  /**
   * 获取图表Dataset
   */
  const getDataset = () => {
    option.dataset.source = unref(totalViewData);
  };
  // 初始化图表
  const initChart = () => {
    myChart.value.setOption(option);
    myChart.value.hideLoading();
    clickXAxis();
    dbClickXAxis();
    clickTotalSeries();
    clickDaySurplusTotalSeries();
    clickDayOutputSeries();
    clickDaySurplusProductionSchedulingSeries();
    clickDayInputSeries();
    clickDayProductionSchedulingSeries();
    clickTotalPlannedOutputSeries();
  };
  // 重新渲染图表
  const reRenderChart = () => {
    myChart.value.setOption(option);
  };

  // 点击x轴事件
  const clickXAxis = () => {
    myChart.value.on('click', 'xAxis', (params) => {
      updateCurrentDate(params.value);
    });
  };

  const dialogState = reactive({
    isShowDialog: false,
    inputValue: '',
  });
  // 双击x轴事件
  const dbClickXAxis = () => {
    // 只有行政接收、进厂检验、入库、发料、点灯才能双击
    if (
      currentNodeState.value === ADMINISTRATIVE_RECEIVE ||
      currentNodeState.value === INCOMING_INSPECTION_NODE ||
      currentNodeState.value === WAREHOUSING_NODE ||
      currentNodeState.value === SEND_MATERIAL_NODE ||
      currentNodeState.value === LIGHT_UP_NODE
    ) {
      myChart.value.on('dblclick', 'xAxis', (params) => {
        dialogState.isShowDialog = true;
      });
    }
  };
  // 点击任务总量事件
  const clickTotalSeries = () => {
    myChart.value.on('click', { seriesName: 'Total \n任务总量' }, (params) => {
      openDrawer(params.name, 'Total-m', '任务总量零件列表');
    });
  };
  // 点击当日剩余任务量事件
  const clickDaySurplusTotalSeries = () => {
    myChart.value.on('click', { seriesName: 'Day surplus total \n当日剩余任务量' }, (params) => {
      openDrawer(params.name, 'Total-all', '当日剩余任务量零件列表');
    });
  };
  // 点击当日输出量事件
  const clickDayOutputSeries = () => {
    myChart.value.on('click', { seriesName: 'Day out \n当日输出量' }, (params) => {
      openDrawer(params.name, 'Total-out', '当日输出量零件列表');
    });
  };
  // 点击当日排产剩余量事件
  const clickDaySurplusProductionSchedulingSeries = () => {
    myChart.value.on('click', { seriesName: 'Day surplus production scheduling \n当日排产剩余量' }, (params) => {
      openDrawer(params.name, 'Total-pc-surplus', '当日排产剩余量零件列表');
    });
  };
  // 点击当日输入量事件
  const clickDayInputSeries = () => {
    myChart.value.on('click', { seriesName: 'Day import \n当日输入量' }, (params) => {
      openDrawer(params.name, 'Total-in', '当日输入量零件列表');
    });
  };
  // 点击当日排产量事件
  const clickDayProductionSchedulingSeries = () => {
    myChart.value.on('click', { seriesName: 'Day Production scheduling \n当日排产量' }, (params) => {
      openDrawer(params.name, 'Total-pc', '当日排产量零件列表');
    });
  };
  // 点击预计输出量事件
  const clickTotalPlannedOutputSeries = () => {
    myChart.value.on('click', { seriesName: 'Total planned output \n预计输出量' }, (params) => {
      openDrawer(params.name, 'Total-pc', '预计输入量零件列表');
    });
  };

  const drawerState = reactive({
    isShowDrawer: false,
    date: '',
    flag: '',
    title: '',
  });
  const openDrawer = (date, flag, title) => {
    drawerState.isShowDrawer = true;
    drawerState.date = date;
    drawerState.flag = flag;
    drawerState.title = title;
  };

  return {
    chartRef,
    getTotalViewData,
    getXAxisLabel,
    initChart,
    getDataset,
    reRenderChart,
    drawerState,
    getSeriesByCurrentNode,
    dialogState,
  };
}
