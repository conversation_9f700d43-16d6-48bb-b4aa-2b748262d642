const { ref } = Vue
/**
 * @description 点的表格列
 * @date 2024年8月7日10:41:24
 * @returns
 */
export function useDotColumn() {
  const dotTableColumns = ref([
    {
      title: 'Kitting完成/站点',
      field: 'str_nodename',
      minWidth: 85,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: '目标WO',
      field: 'str_code',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: '目标ESN',
      field: 'str_esn',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: 'PN',
      field: 'str_pn',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: '零件标签',
      field: 'str_label',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: '零件名称',
      field: 'str_part_name',
      minWidth: 150,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: '客户',
      field: 'str_client',
      minWidth: 150,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: 'PKP',
      field: 'id_pkp',
      minWidth: 150,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
  ])

  return {
    dotTableColumns,
  }
}
