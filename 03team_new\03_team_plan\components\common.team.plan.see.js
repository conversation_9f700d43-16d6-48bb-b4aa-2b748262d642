Vue.component('y-common-team-plan-form-see', {
  props: ['input_id_wo', 'input_plan_date', 'input_id_main', 'input_is_editsee'], //input_id_wo : 发动机  input_plan_date：计划日期 input_id_main
  data: function () {
    return {
      id_main: this.input_id_main,
      is_show: true,
      is_edit: this.input_is_editsee == 'true' ? true : false,
      task_list_old: [], // 任务分配 初始化加载的 老数据
      task_list: [], // 任务分配 展示使用
      task_data: [], // 所有任务
      model_list: [], // 单元体
      team_staff_list: [], // team 人员
      teamsection_staff_list: [], // team 分部人员
      shit_list: [], // 正常班次
      delayedshit_list: [], // 延迟 加班 班次
      str_num_plan: 0, // 已安排任务个数
      shiftendtime: 0, //正常班结束时间
      str_content: '',
    }
  },

  methods: {
    /**获取详情  若果有ID 是编辑*/
    getInfo() {
      let _this = this
      if (_this.id_main) {
        // 编辑

        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_get_team_plan_see',
            id_main: _this.id_main,
          })
          .then(function (response) {
            let task_list_t = []
            _this.str_content = response.data.data.pt_team_plan_dtosee.pt_main.str_content
            response.data.data.pt_team_plan_dtosee.pt_team_plans.forEach(x => {
              let team_staff_t = []
              let team_sec_staff_t = []

              x.pt_team_plan_staffs.forEach(element => {
                // 处理team 人员
                if (element.int_staff_type == 1) {
                  team_staff_t.push(element.str_staff_name)
                } else {
                  team_sec_staff_t.push(element.str_staff_name)
                }
              })
              let shift_times_t = []
              let delay_shift_times_t = []
              shift_times_t.push(x.pt_team_plan?.dt_shift_start, x.pt_team_plan?.dt_shift_end)
              delay_shift_times_t.push(x.pt_team_plan?.dt_delayed_start, x.pt_team_plan?.dt_delayed_end)
              task_list_t.push({
                id: x.pt_team_plan.id,
                str_task: x.pt_team_plan.str_task,
                str_task_name: x.pt_team_plan.str_task_name,
                str_model: x.pt_team_plan.str_model,
                str_team_staffs: team_staff_t,
                str_team_sec_staffs: team_sec_staff_t,
                str_shift: x.pt_team_plan.str_shift,
                shift_times: shift_times_t,
                str_delayed_shift: x.pt_team_plan.str_delayed_shift,
                delay_shift_times: delay_shift_times_t,
                str_remark: x.pt_team_plan.str_remark,
                assembly_task_name: x.pt_team_plan.assembly_task_name,
                site_name: x.pt_team_plan.site_name,
              })
            })
            _this.task_list = task_list_t || []
            _this.str_num_plan = _this.task_list.length
          })
          .catch(function (error) {
            console.log(error)
          })
      } else if (_this.id_main && !_this.is_edit) {
        // 查看
      }
    } /**关闭弹 */,
    closeDialog() {
      let _this = this
      _this.is_show = false
      _this.$emit('get_back_close_dialog') //关闭详情页
    },
    /**初始化加载 数据 */
    inint() {
      this.getInfo()
    },
    /**数组转 字符串 */
    exe_str(data) {
      return data && data.join(',')
    },
  },
  created: function () {
    Promise.all([this.getInfo()]).then(() => {
      //this.getInfo();
    })
  },
  mounted() {},

  template: /*html*/ `
    <div>
      <el-dialog title="Team Plan See" width="80%" :visible.sync="is_show" class="self_dialog" @close="closeDialog()">
        <el-row style="margin-bottom: 3px;">
          <el-badge :value="str_num_plan" class="item" type="info">
            <el-button size="small">Num</el-button>
          </el-badge>
        </el-row>
        <el-row style="margin-bottom: 10px;" v-for="(task,index) in task_list" v-bind:key="index">
          <el-card class="box-card">
            <el-form ref="form1" :model="task" label-position="left" label-width="auto">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="Task:" prop="id_task">
                    <span>{{task.str_task==null?'Others':task.str_task}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Task name:" prop="str_task_name">
                    <span>{{task.str_task_name}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Model:" prop="id_model">
                    <span>{{task.str_model}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8" v-if="task.assembly_task_name">
                  <el-form-item label="Assembly task:" prop="assembly_task_name" v-if="task.assembly_task_name">
                    <span>{{task.assembly_task_name}}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8" v-if="task.site_name">
                  <el-form-item label="Site:" prop="site_name" v-if="task.site_name">
                    <span>{{task.site_name}}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="Team:" prop="id_team_staffs">
                    <span>{{exe_str (task.str_team_staffs)}}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="Team Sec: " prop="str_sec_manager">
                    <span>{{ exe_str ( task.str_team_sec_staffs)}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="Shift:" prop="id_shift">
                    <span>{{task.str_shift}}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="Shift time:" prop="shift_times">
                    <span>{{exe_str (task.shift_times)}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :offset="8"> </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="Delay shift:" prop="str_sec_manager">
                    <span>{{task.str_delayed_shift}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Delay shift time:" prop="str_sec_manager">
                    <span>{{exe_str (task.delay_shift_times)}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="Remark:" prop="str_remark">
                    <span>{{task.str_remark}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
        </el-row>
        <el-row>
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>feedback</span>
            </div>
            <div class="text item">{{str_content}}</div>
          </el-card>
        </el-row>

        <div slot="footer">
          <el-button class="topButton_right" style="margin-left:20px;" size="small" type="danger" @click="closeDialog()"
            >Cancle
          </el-button>
        </div>
      </el-dialog>
    </div>
  `,
})
