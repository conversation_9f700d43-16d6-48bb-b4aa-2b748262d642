import { useVirtualScroll } from '../hooks/useVirtualScroll.js'

const { ref, onMounted, onUnmounted, watch, nextTick } = Vue

export default {
  name: 'VirtualScrollContainer',
  props: {
    items: {
      type: Array,
      default: () => [],
    },
    itemHeight: {
      type: Number,
      default: 400,
    },
    containerHeight: {
      type: Number,
      default: 600,
    },
    overscan: {
      type: Number,
      default: 2,
    },
  },
  emits: ['item-resize'],
  setup(props, { emit, slots }) {
    const containerRef = ref(null)
    const scrollRef = ref(null)
    const itemElementsRef = ref([])

    // 创建响应式的items引用
    const itemsRef = ref(props.items)

    // 使用虚拟滚动 hook
    const {
      visibleItems,
      totalHeight,
      offsetY,
      state,
      registerScrollElement,
      scrollToItem,
      scrollToOffset,
      updateItemHeight,
      resetPositions,
      visibleRange,
    } = useVirtualScroll({
      items: itemsRef,
      itemHeight: props.itemHeight,
      containerHeight: props.containerHeight,
      overscan: props.overscan,
    })

    // 监听项目变化
    watch(
      () => props.items,
      (newItems) => {
        itemsRef.value = newItems
        resetPositions()
      },
      { deep: true },
    )

    // 监听可见项目变化，更新项目真实高度
    watch(visibleItems, () => {
      nextTick(() => {
        updateItemHeights()
      })
    })

    // 获取项目真实高度并更新
    const updateItemHeights = () => {
      itemElementsRef.value.forEach((el, index) => {
        if (el) {
          const actualHeight = el.offsetHeight
          const itemIndex = visibleRange.value.start + index
          updateItemHeight(itemIndex, actualHeight)
        }
      })
    }

    // 设置项目引用
    const setItemRef = (el, index) => {
      if (el) {
        itemElementsRef.value[index] = el
      }
    }

    // 注册滚动元素
    onMounted(() => {
      if (scrollRef.value) {
        registerScrollElement(scrollRef.value)
      }
      
      // 监听窗口大小变化，重新计算容器高度
      const resizeObserver = new ResizeObserver(() => {
        if (scrollRef.value) {
          // 重新注册滚动元素以更新容器高度
          registerScrollElement(scrollRef.value)
        }
      })
      
      if (containerRef.value) {
        resizeObserver.observe(containerRef.value)
      }

      // 存储observer以便清理
      onUnmounted(() => {
        resizeObserver.disconnect()
      })
    })

    // 清理
    onUnmounted(() => {
      itemElementsRef.value = []
    })

    // 滚动到指定项目
    const scrollToIndex = (index) => {
      scrollToItem(index)
    }

    // 滚动到指定位置
    const scrollTo = (offset) => {
      scrollToOffset(offset)
    }

    // 暴露方法
    const expose = {
      scrollToIndex,
      scrollTo,
      getVisibleRange: () => visibleRange.value,
      getTotalHeight: () => totalHeight.value,
    }

    return {
      containerRef,
      scrollRef,
      visibleItems,
      totalHeight,
      offsetY,
      state,
      setItemRef,
      ...expose,
    }
  },
  template: /*html*/ `
    <div ref="containerRef" class="virtual-scroll-container" style="height: 100%">
      <div
        ref="scrollRef"
        class="virtual-scroll-wrapper"
        :style="{ 
          height: '100%',
          overflowY: 'auto',
          overflowX: 'hidden'
        }"
      >
        <!-- 模拟总高度的容器 -->
        <div class="virtual-scroll-spacer" :style="{ height: totalHeight + 'px', position: 'relative' }">
          <!-- 可见项目容器 -->
          <div
            class="virtual-scroll-items"
            :style="{ 
              transform: \`translateY(\${offsetY}px)\`,
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0
            }"
          >
            <div
              v-for="(item, index) in visibleItems"
              :key="item.key || item.id_wo"
              :ref="(el) => setItemRef(el, index)"
              class="virtual-scroll-item"
              :data-index="item.virtualIndex"
              :data-original-index="item.originalIndex"
            >
              <!-- 使用作用域插槽渲染项目内容 -->
              <slot :item="item" :index="item.virtualIndex" :originalIndex="item.originalIndex" />
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
}
