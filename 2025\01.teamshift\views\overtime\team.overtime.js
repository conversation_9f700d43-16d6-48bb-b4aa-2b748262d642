import HtVxeTable from '../../components/VxeTable/HtVxeTable.js'
import {
  getDelayApplyList,
  getShiftList,
  saveDelayApply,
  deleteDelayApply,
  getDelayApplyId,
  getTeams,
  getCurrentDeptStaff,
  withdrawDelayApply,
} from '../../api/timeover/index.js'
import teamOvertimeTemplate from './team.overtime.template.js'

export default {
  name: 'TeamOvertime',
  components: {
    HtVxeTable,
  },
  template: teamOvertimeTemplate,
  props: {
    is_hr: String,
  },
  setup(props) {
    const { ref, reactive, onMounted, nextTick, onUnmounted } = Vue
    const { ElMessage, ElMessageBox } = ElementPlus
    const loading = ref(true)
    const tableData = ref([])

    // 查看详情弹窗
    const detailDialogVisible = ref(false)
    const detailData = ref(null)

    const statusColor = (value) => {
      if (value === 0) return 'text-green-500'
      if (value === 301) return 'text-blue-500'
      if (value === 1) return 'text-green-500'
      if (value === -1) return 'text-red-500'
      return ''
    }
    const statusText = (value) => {
      if (value === 0) return '草稿'
      if (value === 301) return '待审批'
      if (value === 1) return '审批通过'
      if (value === -1) return '审批失败'
      if (value === -99) return '撤回'
      return ''
    }

    // 状态格式化 - 移到tableColumns定义之前
    const statusFormatter = ({ cellValue }) => {
      if (cellValue === 0) return '<span class="text-green-500">草稿</span>'
      if (cellValue === 301) return '<span class="text-blue-500">待审批</span>'
      if (cellValue === 1) return '<span class="text-green-500">审批通过</span>'
      if (cellValue === -1) return '<span class="text-red-500">审批失败</span>'
      if (cellValue === -99) return '<span class="text-red-500">撤回</span>'
      return cellValue
    }

    // 人员名称格式化函数，处理换行符
    const staffNameFormatter = ({ cellValue }) => {
      if (!cellValue) return ''
      return cellValue.replace(/\n/g, ', ')
    }

    const tableColumns = ref([
      {
        field: 'dt_delay',
        title: '日期',
        minWidth: 120,
        filterRender: { name: 'FilterCalendar' },
        filters: [{ data: '' }],
      },
      {
        field: 'shift_name',
        title: '班次',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'shift_time',
        title: '班次时间',
        minWidth: 150,
      },
      {
        field: 'int_delay',
        title: '延时时长',
        minWidth: 120,
      },
      {
        field: 'delay_time',
        title: '延时范围',
        minWidth: 150,
      },
      {
        field: 'team_name',
        title: 'Team',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_sec_name',
        title: '分部',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'staff_name',
        title: '人员',
        minWidth: 120,
        formatter: staffNameFormatter,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      { field: 'int_substatus', title: '状态', minWidth: 120, formatter: statusFormatter, type: 'html',  filters: [
          { label: '草稿', value: 0 },
          { label: '待审批', value: 301 },
          { label: '审批通过', value: 1 },
          { label: '审批不通过', value: -1 },
          { label: '撤回', value: -99 }
        ],
        filterMultiple: false, },
      {
        field: 'str_sec_manage',
        title: '审批人',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
    ])
    const tableHeight = ref('400px')
    const tableContainerRef = ref(null)

    // 分页配置
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
    })

    const filterParams = ref({})
    const handleFilterChange = (params) => {
      pagination.currentPage = 1
      let field = params.column.field;
      if(params.column.field=='int_substatus'){field='status'};
      filterParams.value[field] = params.datas?.[0] ||  params.filters.find(x=>x.field==params.column.field).values[0]
      loadTableData()
    }

    // 弹窗相关
    const dialogVisible = ref(false)
    const dialogTitle = ref('延时班申请')
    const formData = reactive({
      dt_delay: '',
      shift: '',
      int_delay: '',
      timeRanges: [{ dt_delay_start: '', dt_delay_end: '' ,int_delay: ''}],
      team: '',
      staffs: [],
    })

    // 加载表格数据
    const loadTableData = async () => {
      loading.value = true
      let param = {
        CurrentPage: pagination.currentPage,
        PageSize: pagination.pageSize,
        ...filterParams.value,
      }
      try {
        const res = await getDelayApplyList(param)
        tableData.value = res.items
        pagination.total = res.totalCount || 0
      } catch (error) {
        ElMessage.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }

    // 分页处理
    const handleCurrentChange = ({ currentPage, pageSize }) => {
      pagination.currentPage = currentPage
      pagination.pageSize = pageSize
      loadTableData()
    }

    const handleSizeChange = ({ currentPage, pageSize }) => {
      pagination.currentPage = currentPage
      pagination.pageSize = pageSize
      loadTableData()
    }

    // 处理表格高度
    const updateTableHeight = () => {
      nextTick(() => {
        if (tableContainerRef.value) {
          // 获取容器元素
          const container = tableContainerRef.value

          // 获取容器的padding
          const containerStyle = window.getComputedStyle(container)
          const paddingTop = parseInt(containerStyle.paddingTop) || 0
          const paddingBottom = parseInt(containerStyle.paddingBottom) || 0

          // 计算表格可用高度
          let availableHeight

          // 检查是否在iframe中
          if (window !== window.parent) {
            // iframe情况下使用clientHeight
            availableHeight = container.clientHeight
          } else {
            // 非iframe情况下使用视窗高度
            availableHeight = window.innerHeight
          }

          // 计算最终表格高度
          const finalHeight = availableHeight - paddingTop - paddingBottom - 120 // 120px作为缓冲

          // 设置最小高度
          const minHeight = 200
          tableHeight.value = `${Math.max(finalHeight, minHeight)}px`
        }
      })
    }

    // 创建ResizeObserver实例
    const resizeObserver = new ResizeObserver(() => {
      updateTableHeight()
    })

    const shiftOptionList = ref([])
    // 打开新增弹窗
    const handleAdd = async () => {
      const shiftRes = await getShiftList()
      shiftOptionList.value = shiftRes.data.filter((x) => x.str_name == '行政班' || x.str_name == '晚班'|| x.str_name == '早班')
      shiftOptionList.value.push({ id: '3001', str_name: '灵活班',str_start_time:'', str_end_time:'' })
      // 重置表单数据
      Object.assign(formData, {
        id: '',
        dt_delay: '',
        shift: '',
        int_delay: '',
        timeRanges: [{ dt_delay_start: '', dt_delay_end: '' ,int_delay: ''}],
        team: '',
        staffs: [],
      })
      dialogTitle.value = '延时班申请'
      dialogVisible.value = true
    }

    // 打开编辑弹窗
    const handleEdit = async (row) => {
      const delaydetail = await getDelayApplyId(row.id)
      const shiftRes = await getShiftList()
      shiftOptionList.value = shiftRes.data.filter((x) => x.str_name == '行政班' || x.str_name == '晚班' || x.str_name == '早班')
      shiftOptionList.value.push({ id: '3001', str_name: '灵活班',str_start_time:'', str_end_time:'' })
      // 处理延时范围数据
      // let timeRanges = []
      // if (delaydetail.dt_delay_start && delaydetail.dt_delay_end) {
      //   timeRanges = [{ 
      //     startTime: delaydetail.dt_delay_start, 
      //     endTime: delaydetail.dt_delay_end
      //   }]
      // } else {
        // timeRanges = [{ startTime: '', endTime: '' }]
      // }
      
      // 填充表单数据
      Object.assign(formData, {
        id: delaydetail.id,
        dt_delay: delaydetail.dt_delay,
        shift: delaydetail.id_shift,
        shift_time: delaydetail.str_start_time + '~' + delaydetail.str_end_time,
        int_delay: delaydetail.int_delay,
        timeRanges: delaydetail.delay_times || [],
        team: delaydetail.id_team,
        staffs: delaydetail.staffs,
      })
      dialogTitle.value = '修改延时班申请'
      dialogVisible.value = true
    }

    const handleCommit = (row) => {
      ElMessageBox.confirm('确定要提交该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const delaydetail = await getDelayApplyId(row.id)
        const postData = {
          id: delaydetail.id,
          dt_delay: delaydetail.dt_delay,
          id_shift: delaydetail.id_shift,
          int_delay: delaydetail.int_delay,
          // dt_delay_start: delaydetail.dt_delay_start,
          // dt_delay_end: delaydetail.dt_delay_end,
          delay_times: delaydetail.delay_times || [],
          id_team: delaydetail.id_team,
          int_substatus: 301, // 提交待审批
          staffs: delaydetail.staffs,
          is_hr: props.is_hr,
        }
        try {
          await saveDelayApply(postData)
          ElMessage.success('延时申请保存并提交成功')
          loadTableData()
        } catch (error) {
          ElMessage.error('保存失败：' + (error.message || '未知错误'))
        }
      })
    }

    // 查看详情
    const handleView = (row) => {
      detailData.value = { ...row }
      detailDialogVisible.value = true
    }

    // 删除记录
    const handleDelete = (row) => {
      ElMessageBox.confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          // 实际项目中应该调用API删除
          await deleteDelayApply(row.id)
          ElMessage.success('删除成功')
          loadTableData()
        })
        .catch(() => {})
    }

    // 保存表单
    const handleSave = async () => {
      // 表单验证
      if (!formData.dt_delay) {
        ElMessage.warning('请选择日期')
        return
      }
      if (!formData.shift) {
        ElMessage.warning('请选择班次')
        return
      }
      
      // 检查延时范围是否填写
      const hasEmptyTimeRange = formData.timeRanges.some(range => !range.dt_delay_start || !range.dt_delay_end)
      if (hasEmptyTimeRange) {
        ElMessage.warning('请完整填写延时范围')
        return
      }
      
      if (!formData.int_delay) {
        ElMessage.warning('请输入延时范围')
        return
      }
      if (!formData.staffs || formData.staffs.length === 0) {
        ElMessage.warning('请选择加班人员')
        return
      }

      // 获取第一个时间范围作为主要时间范围
      // const firstRange = formData.timeRanges[0] || { startTime: '', endTime: '' }
      
      // 实际项目中应该调用API保存
      const postData = {
        id: formData.id,
        dt_delay: formData.dt_delay,
        id_shift: formData.shift,
        int_delay: formData.int_delay,
        // dt_delay_start: firstRange.startTime,
        // dt_delay_end: firstRange.endTime,
        delay_times: formData.timeRanges,
        id_team: formData.team,
        staffs: formData.staffs,
        int_substatus: 0,// 保存待提交
        is_hr: props.is_hr
      }
      try {
        await saveDelayApply(postData)
        ElMessage.success('延时申请保存成功')
        dialogVisible.value = false // 保存成功后关闭弹窗
        loadTableData()
      } catch (error) {
        ElMessage.error('保存失败：' + (error.message || '未知错误'))
      }
    }
    
    const handleSaveAndCommit = async () => {
      // 表单验证
      if (!formData.dt_delay) {
        ElMessage.warning('请选择日期')
        return
      }
      
      // 检查延时范围是否填写
      const hasEmptyTimeRange = formData.timeRanges.some(range => !range.dt_delay_start || !range.dt_delay_end)
      if (hasEmptyTimeRange) {
        ElMessage.warning('请完整填写延时范围')
        return
      }
      
      if (!formData.int_delay) {
        ElMessage.warning('请输入延时范围')
        return
      }
      if (!formData.staffs || formData.staffs.length === 0) {
        ElMessage.warning('请选择加班人员')
        return
      }

      // 获取第一个时间范围作为主要时间范围
      const firstRange = formData.timeRanges[0] || { dt_delay_start: '', dt_delay_end: '',int_delay: '' }
      
      // 实际项目中应该调用API保存
      const postData = {
        id: formData.id,
        dt_delay: formData.dt_delay,
        id_shift: formData.shift,
        int_delay: formData.int_delay,
        // dt_delay_start: firstRange.startTime,
        // dt_delay_end: firstRange.endTime,
        delay_times: formData.timeRanges,
        id_team: formData.team,
        int_substatus: 301, // 提交待审批
        staffs: formData.staffs,
      }
      try {
        await saveDelayApply(postData)
        ElMessage.success('延时申请保存并提交成功')
        dialogVisible.value = false // 保存成功后关闭弹窗
        loadTableData()
      } catch (error) {
        ElMessage.error('保存失败：' + (error.message || '未知错误'))
      }
    }
    const xTableRef = ref(null)

    const shiftEndTime = ref('')

    const handleShiftChange = (value) => {
      const selectedShift = shiftOptionList.value.find((item) => item.id === value)
      if (!selectedShift) return

      const { str_start_time, str_end_time } = selectedShift
      formData.shift_time = `${str_start_time}~${str_end_time}`
      shiftEndTime.value = str_end_time

      // 如果已经选择了延时时长，重新计算延时范围
      if (formData.int_delay) {
        //handleDelayChange(formData.int_delay, formData.shift_time)
      }
    }

    const calculateDelay = () => {
      let totalDelay = 0
      
      formData.timeRanges.forEach(range => {
        if (range.dt_delay_start && range.dt_delay_end) {
          const startTime = moment(`1970-01-01T${range.dt_delay_start}:00`)
          const endTime = moment(`1970-01-01T${range.dt_delay_end}:00`)
          
          let delay = 0
          if (endTime.isBefore(startTime)) {
            delay = endTime.add(1, 'days').diff(startTime, 'hours', true)
          } else {
            delay = endTime.diff(startTime, 'hours', true)
          }
          range.int_delay = delay.toFixed(1)
          totalDelay += delay
        }
      })
      
      formData.int_delay = totalDelay.toFixed(1)
    }

    // 添加新的时间范围
    const addTimeRange = () => {
      formData.timeRanges.push({ dt_delay_start: '', dt_delay_end: '',int_delay: ''})
    }
    
    // 移除时间范围
    const removeTimeRange = (index) => {
      if (formData.timeRanges.length > 1) {
        formData.timeRanges.splice(index, 1)
        calculateDelay()
      }
    }

    onMounted(() => {
      loadTableData()
      updateTableHeight()

      // 监听容器大小变化
      if (tableContainerRef.value) {
        resizeObserver.observe(tableContainerRef.value)
      }

      // 监听窗口大小变化
      window.addEventListener('resize', updateTableHeight)
    })

    // 在组件卸载时清理
    onUnmounted(() => {
      // 移除ResizeObserver
      if (tableContainerRef.value) {
        resizeObserver.unobserve(tableContainerRef.value)
      }
      resizeObserver.disconnect()

      // 移除窗口事件监听
      window.removeEventListener('resize', updateTableHeight)
    })

    const teamOptionList = ref([])
    // 获取Team下拉列表
    const fetchTeamList = async () => {
      const res = await getTeams()
      teamOptionList.value = res.data.map((item) => ({
        label: item.str_name,
        value: item.id,
      }))
    }

    const staffOptionList = ref([])
    // 获取人员下拉列表
    const fetchStaffList = async () => {
      const res = await getCurrentDeptStaff(props.is_hr)
      staffOptionList.value = res.map((item) => ({
        label: item.str_name,
        value: item.id,
        id_team: item.id_team,
      }))
    }

    // 选择Team
    const handleTeamChange = (value) => {
      formData.staffs = []
      formData.staffs = staffOptionList.value.filter((item) => item.id_team === value).map((item) => item.value)
    }

    onMounted(() => {
      fetchTeamList()
      fetchStaffList()
    })

    /**
     * 撤回
     * @param {Object} row 当前行数据
     */
    const handleWithdraw = (row) => {
      // 填写备注
      ElMessageBox.prompt('请输入撤销信息', '撤销', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '撤销信息不能为空'
      }).then(async ({ value }) => {
        try {
          await withdrawDelayApply({
            id: row.id,
            status: -99,
            str_content: value
          })
          ElMessage.success('撤回成功')
          loadTableData() // 刷新表格数据
        } catch (error) {
          ElMessage.error('撤回失败：' + (error.message || '未知错误'))
        }
      }).catch(() => {
        // 用户取消操作，不做任何处理
      })
    }
    return {
      shiftEndTime,
      calculateDelay,
      handleShiftChange,
      xTableRef,
      tableData,
      tableColumns,
      tableHeight,
      tableContainerRef,
      pagination,
      dialogVisible,
      dialogTitle,
      formData,
      detailDialogVisible,
      detailData,
      handleCurrentChange,
      handleSizeChange,
      handleAdd,
      handleEdit,
      handleCommit,
      handleView,
      handleDelete,
      handleSave,
      handleSaveAndCommit,
      shiftOptionList,
      statusColor,
      statusText,
      teamOptionList,
      staffOptionList,
      handleTeamChange,
      handleFilterChange,
      handleWithdraw,
      addTimeRange,
      removeTimeRange,
    }
  },
}
