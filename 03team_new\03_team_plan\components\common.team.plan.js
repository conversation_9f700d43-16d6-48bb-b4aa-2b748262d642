Vue.component('y-common-team-plan-form', {
  props: [
    'input_id_wo',
    'input_plan_date',
    'input_id_main',
    'input_is_edit',
    'str_flow',
    'str_en_type',
    'input_str_esn',
    'input_esn_date_end',
    'input_id_team',
    'task_type',
    'input_group_type',
    'id_site',
  ], //input_id_wo : 发动机  input_plan_date：计划日期 input_id_main
  data: function () {
    return {
      str_esn: this.input_str_esn,
      id_main: this.input_id_main,
      //esn_end: this.input_esn_date_end,
      is_show: true,
      is_edit: this.input_is_edit == 'true' ? true : false,
      task_list_old: [], // 任务分配 初始化加载的 老数据
      task_list: [
        // 	{id_task:"", str_task_name
        // 	id_team_staffs:[],
        // 	id_team_sec_staffs:[],
        // 	id_shift:null,
        // 	dt_shift_start:null,
        // 	dt_shift_end:null,
        // 	id_delayed_shift:null,
        // 	dt_delayed_start:null,
        // 	dt_delayed_end:null,
        // }
      ], // 任务分配 展示使用
      task_data: [], // 所有任务
      model_list: [], // 单元体
      team_staff_list: [], // team 人员
      teamsection_staff_list: [], // team 分部人员
      assembly_task: [],
      sites: [],

      shit_list: [], // 正常班次
      delayedshit_list: [], // 延迟 加班 班次
      str_num_plan: 0, // 已安排任务个数
      shiftendtime: 0, //正常班结束时间
      rulesForm1: {
        // form1 验证
        id_task: [
          {
            required: true,
            message: 'Please select  task',
            trigger: 'blur',
          },
          //其他验证设置...
          // { validator: deptRule, trigger: 'blur' },
        ],

        // id_model: [
        // 	{
        // 		required: true,
        // 		message: "Please select  Model",
        // 		trigger: "blur",
        // 	},

        // ],
        // id_team_staffs: [
        // 	{
        // 		required: true,
        // 		message: "Please select  team",
        // 		trigger: "blur",
        // 	},
        // 	//其他验证设置...
        // 	// { validator: deptRule, trigger: 'blur' },
        // ],
        id_shift: [
          {
            required: true,
            message: 'Please select  shift',
            trigger: 'blur',
          },
          //其他验证设置...
          // { validator: deptRule, trigger: 'blur' },
        ],
        shift_times: [
          {
            required: true,
            message: 'Please select  shift time',
            trigger: 'blur',
          },
          //其他验证设置...
          // { validator: deptRule, trigger: 'blur' },
        ],
      },
      pickerOptions: {
        disabledDate: (val) => {
          return (
            new Date(val).getTime() < new Date().getTime() ||
            new Date(this.input_esn_date_end).getTime() < new Date(val).getTime()
          )
        },
      },
      adjust_main_date: '',
      plan_team: '',
      is_whole_move: false,
      saveLoading: false,
      str_sm: '',
    }
  },

  filters: {
    /**日期管道*/
    dateTime: function (date, type) {
      if (date != null && date != '') {
        let yer, month, day, HH, mm, ss
        let time = new Date(date),
          timeDate
        yer = time.getFullYear()
        month = time.getMonth() + 1
        day = time.getDate()
        HH = time.getHours() //获取系统时，
        mm = time.getMinutes() //分
        ss = time.getSeconds() //秒

        month = month < 10 ? '0' + month : month
        day = day < 10 ? '0' + day : day
        HH = HH < 10 ? '0' + HH : HH
        mm = mm < 10 ? '0' + mm : mm
        ss = ss < 10 ? '0' + ss : ss

        switch (type) {
          case 'yyyy':
            timeDate = String(yer)
            break
          case 'yyyy-MM':
            timeDate = yer + '-' + month
            break
          case 'yyyy-MM-dd':
            timeDate = yer + '-' + month + '-' + day
            break
          case 'yyyy/MM/dd':
            timeDate = yer + '/' + month + '/' + day
            break
          case 'yyyy-MM-dd HH:mm:ss':
            timeDate = yer + '-' + month + '-' + day + '' + HH + ':' + mm + ':' + ss
            break
          case 'HH:mm:ss':
            timeDate = HH + ':' + mm + ':' + ss
            break
          case 'MM':
            timeDate = String(month)
            break
          default:
            timeDate = yer < 1900 ? '' : yer + '-' + month + '-' + day
            break
        }
        return timeDate
      } else {
        return ''
      }
    },

    /**获取数据状态描述*/
    getStatusStr(data) {
      if (data == 1) {
        return '审批通过'
      } else if (data == -1) {
        return '审批失败'
      } else if (data == -999) {
        return '审批撤销'
      } else if (data == 0 || data > 300) {
        return '审批中'
      } else {
        return '待提交'
      }
    },
  },
  methods: {
    /**获取team leader 任务 */
    async get_task_list() {
      let _this = this
      param_t = {
        au: 'ssamc',
        ap: 'api2018',
        ak: '',
        ac: 'pt_get_task_list',
        task_type: _this.str_flow,
        input_group_type: _this.input_group_type,
        str_en_type: _this.str_en_type,
      }
      // let param_t = {
      //   au: 'ssamc',
      //   ap: 'api2018',
      //   ak: '',
      //   ac: 'e_obeya2_api_plan_QueryTaskGroupAndUnitList',
      //   str_esnname: 'ESN' + _this.str_esn,
      // }
      // if (_this.str_flow.toLowerCase() == 'inspect') {
      //   let tasktype = _this.task_type
      //   if (_this.task_type == 'BSI-IN' || _this.task_type == 'BSI-OUT') {
      //     tasktype = 'BSI'
      //   }
      //   param_t = {
      //     au: 'ssamc',
      //     ap: 'api2018',
      //     ak: '',
      //     ac: 'pt_get_task_list',
      //     task_type: tasktype,
      //     input_group_type: _this.input_group_type,
      //     str_en_type: _this.str_en_type,
      //   }
      // }
      // if (_this.str_flow.toLowerCase() == 'f1-1') {
      //   param_t = {
      //     au: 'ssamc',
      //     ap: 'api2018',
      //     ak: '',
      //     ac: 'pt_get_task_list',
      //     task_type: _this.str_flow,
      //   }
      // }
      await axios
        .post(globalApiUrl, param_t)
        .then(function (response) {
          _this.task_data = response.data?.data || []
          // _this.task_data.forEach((item) => {
          //   var index = item.str_task_name.indexOf(' ')
          //   item.str_task_name = item.str_task_name.substring(index, item.str_task_name.length)
          // })
          _this.task_data.push({
            id: '3333',
            id_task: '333',
            str_task_name: 'Others',
            id_sm: '',
            id_wo: '',
            str_end_date: '',
            str_esn: '',
            str_flow: '',
            str_group: '',
            str_sm: '',
            str_start_date: '',
          })
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    /**获取单元体 */
    async get_model_list() {
      let _this = this
      await axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_model_list',
          id_wo: _this.input_id_wo,
        })
        .then(function (response) {
          _this.model_list = response.data?.data || []
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    /**获取team 人员 */
    async get_team_list() {
      let _this = this
      await axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_team_staff_list',
          dt_date: _this.input_plan_date,
          id_main: _this.id_main,
          id_team: _this.input_id_team,
          str_flow: _this.str_flow,
        })
        .then(function (response) {
          _this.team_staff_list = response.data?.data || []
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    /**获取装配任务*/
    async get_assembly_task() {
      let _this = this
      await axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_assembly_task',
          str_sm: _this.str_sm,
        })
        .then(function (response) {
          _this.assembly_task = response.data?.data || []
        })
        .catch(function (error) {
          console.log(error)
        })
    },

    /**获取站点*/
    async get_site() {
      let _this = this
      await axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_site_list',
        })
        .then(function (response) {
          _this.sites = response.data?.data || []
        })
        .catch(function (error) {
          console.log(error)
        })
    },

    /**选中team人员 排除6/6人员*/
    change_team_staff(task) {},

    /**获取team  分部人员 */
    get_team_sec_list() {
      let _this = this
      axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_teamsection_staff_list',
          id_main: _this.id_main,
          id_team: _this.input_id_team,
        })
        .then(function (response) {
          _this.teamsection_staff_list = response.data?.data || []
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    /**获取正常班次 */
    async get_shit_list() {
      let _this = this
      await axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_shit_list',
          str_flow: _this.str_flow,
        })
        .then(function (response) {
          _this.shit_list = response.data?.data || []
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    /**获取延迟班次 */
    async get_delayedshit_list() {
      let _this = this
      await axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_delayedshit_list',
        })
        .then(function (response) {
          _this.delayedshit_list = response.data?.data || []
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    /**带出班次时间段 */
    changeSelectShift(task, index) {
      let _this = this
      let shift_t = _this.shit_list.find((x) => x.id == task.id_shift)
      if (shift_t && shift_t.int_type == 5) {
        //  task.shift_times = ["00:00","12:00"];
        // _this.task_list[index]=task;
        // task.shift_times=null;
        task.shift_times = [shift_t.str_start_time, shift_t.str_end_time]
        task.is_edit = true
      } else {
        task.is_edit = false
        task.shift_times = [shift_t.str_start_time, shift_t.str_end_time]
        // _this.$set(_this.task_list[index],)
        _this.shiftendtime = shift_t.str_end_time
      }
    },
    testClick(e, task, index) {
      let _this = this
      _this.$nextTick(() => {
        _this.$forceUpdate()
        // task.shift_times = task.shift_times;
      })
    } /**改变值 */,
    delayShiftClick(e, task, index) {
      let _this = this
      _this.$nextTick(() => {
        _this.$forceUpdate()
        // task.delay_shift_times = task.delay_shift_times;
      })
    },
    /**带出延时班时间段 */
    changeSelectDelayShift(val, task) {
      let _this = this
      _this.changeSelectShift(task)
      let delayshift_t = _this.delayedshit_list.find((x) => x.id == task.id_delayed_shift)
      if (delayshift_t) {
        var now = new Date()
        var date = now.toLocaleDateString()
        var sj = date + ' ' + _this.shiftendtime + ':00'
        var dates = new Date(sj)
        var hour = dates.setHours(dates.getHours() + delayshift_t.int_work_hours)
        if (
          delayshift_t.int_work_hours == 0.5 ||
          delayshift_t.int_work_hours == 1.5 ||
          delayshift_t.int_work_hours == 2.5
        ) {
          hour = dates.setMinutes(dates.getMinutes() + 30)
        }
        var newdate = new Date(hour)
        var hh = newdate.getHours()
        var mm = newdate.getMinutes()
        let endtime = hh + ':' + (mm == 0 ? '00' : mm)
        task.delay_shift_times = [_this.shiftendtime, endtime]
      }
    },
    /**添加任务排班 */
    add_task_plan() {
      let _this = this
      _this.task_list.push({ id_site: _this.id_site })
      _this.str_num_plan = _this.task_list.length
    },
    /** 删除任务 */
    delete_task(task_delete, index) {
      let _this = this
      _this.task_list.splice(index, 1)
      _this.str_num_plan = _this.task_list.length
    },
    /**保存计划 */
    async save_plan() {
      let _this = this
      _this.saveLoading = true
      let pt_team_plans = []
      let adjustdata = []
      let iswholemove = '1'
      if (_this.is_whole_move == true) {
        iswholemove = '0'
      }
      // let adjusttaskcount=0;
      let id_team = _this.plan_team
      if (id_team == '') {
        id_team = _this.input_id_team
      }
      let pt_main = {
        id: _this.input_id_main,
        id_wo: _this.input_id_wo,
        str_flow: _this.str_flow,
        pt_dt: _this.input_plan_date,
        id_team: id_team,
        id_pt_task_allot: '',
        str_task_type: _this.task_type,
      } // 主表数据
      // if (_this.str_flow == "INSPECT") {
      // 	pt_main.str_flow = _this.task_type
      // }
      let save_data_t = Object.assign(_this.task_list, {})
      let error_msg = []
      let teamstaffCodes =[];
      save_data_t.forEach((x) => {
        let team_staff_t = []
        x.id_team_staffs.forEach((element) => {
          // 处理team 人员
          team_staff_t.push({ id_staff: element, int_staff_type: 1 })
          _this.team_staff_list.forEach((item) => {
            if (item.id == element) {
              teamstaffCodes.push(item.str_code)
            }
          })
        
        })

        x.id_team_sec_staffs.forEach((element) => {
          // 处理分部 人员
          team_staff_t.push({ id_staff: element, int_staff_type: 2 })
          _this.teamsection_staff_list.forEach((item) => {
            if (item.id == element) {
              teamstaffCodes.push(item.str_code)
            }
          })
        })

        let selectedtask = _this.task_data.find((p) => p.id_task == x.id_task)
        if (typeof selectedtask == 'undefined') {
          error_msg.push(`没有选中的任务`)
        }
        if (typeof selectedtask != 'undefined') {
          pt_team_plans.push({
            pt_team_plan: {
              id: x.id,
              id_task: _this.task_data.find((p) => p.id_task == x.id_task).id,
              str_eobery_task_name: _this.task_data.find((p) => p.id_task == x.id_task)?.str_task_name || '',
              str_task_name: x.str_task_name, //_this.task_data.find(p => p.id == x.id_task)?.str_name,
              id_model: x.id_model,
              str_e_obery_task_id: x.id_task,
              // id_assembly_task: x.id_assembly_task.join(','),
              id_site: x.id_site,
              str_e_obery_id: _this.task_data.find((p) => p.id_task == x.id_task).id,
              str_sm: _this.model_list.find((p) => p.id == x.id_model)?.str_code || '',
              id_shift: x.id_shift,
              dt_shift_start: (x.shift_times && x.shift_times.length > 0 && x.shift_times[0]) || '',
              dt_shift_end: (x.shift_times && x.shift_times.length > 0 && x.shift_times[1]) || '',
              id_delayed_shift: x.id_delayed_shift,
              dt_delayed_start: (x.delay_shift_times && x.delay_shift_times.length > 0 && x.delay_shift_times[0]) || '',
              dt_delayed_end: (x.delay_shift_times && x.delay_shift_times.length > 0 && x.delay_shift_times[1]) || '',
              str_remark: x.str_remark,
            },
            pt_team_plan_staffs: team_staff_t,
            pt_assembly_task: x.id_assembly_task,
          })
        }
        adjustdata.push({ id: x.id, adjust_date: x.adjust_date })
        // if(x.adjust_date!="" && x.adjust_date!=undefined){adjusttaskcount+=1}
        // let id_team_t=[];
        // let name_team_t=[];
        // x.id_teams.forEach(t => {
        // 	id_team_t.push(t);
        // 	name_team_t.push(_this.teams.find(x=>x.id==t)?.str_name || "")
        // });
        // x.id_teams=id_team_t.join(',');
        // x.str_teams=name_team_t.join(',');
      })
      let authorflag = true;
      teamstaffCodes.push("468");
      let response= await axios.post("http://192.168.10.122/education/authorizeCenter/getAuthorizeCenterData?userNum="+teamstaffCodes.join(","))
      response.data.forEach((x) => {
        if (x.isAuthorized == 'TRUE') {
          authorflag=false;
        }
      })    
      if(authorflag &&  teamstaffCodes.length>0){
        error_msg.push(`该小组无授权人员，需重新选择人员`)
      }
      // 组织判断 action
      pt_team_plans &&
        pt_team_plans.length > 0 &&
        pt_team_plans.forEach((task, index) => {
          if (!task.pt_team_plan.id_task) {
            error_msg.push(`No.${index + 1} please select task`)
          }
          // if (!task.pt_team_plan.id_model) {
          // 	error_msg.push(`No.${index + 1} please select model`)
          // }
          // if (!task.pt_team_plan_staffs || task.pt_team_plan_staffs.length == 0) {
          // 	error_msg.push(`No.${index + 1} please select team`)
          // }
          if (!task.pt_team_plan.id_shift) {
            error_msg.push(`No.${index + 1} please select shift`)
          }
          if (!task.pt_team_plan.dt_shift_start || !task.pt_team_plan.dt_shift_end) {
            error_msg.push(`No.${index + 1} please select shift time`)
          }
          if (task.pt_team_plan.id_delayed_shift) {
            if (!task.pt_team_plan.dt_delayed_start || !task.pt_team_plan.dt_delayed_end) {
              error_msg.push(`No.${index + 1} please select extension shift time`)
            }
          }
        })
      // if(adjusttaskcount==pt_team_plans.length){
      // 	error_msg.push(`Please adjust the overall task at the schedule adjust`)
      //
      if (error_msg.length > 0) {
        _this.$message({
          dangerouslyUseHTMLString: true,
          message: error_msg.join('<br/>'),
          type: 'warning',
        })
        _this.saveLoading = false
      } else {
        let toolingMessage = ''
        //如果是机型是leap需要做前置工装校验
        if (_this.str_en_type == 'leap') {
          axios
            .post(globalApiUrl, {
              au: 'ssamc',
              ap: 'api2018',
              ak: '',
              ac: 'has_tooling',
              pt_team_plan_dto: { pt_main: pt_main, pt_team_plans: pt_team_plans },
              adjust_main_date: _this.adjust_main_date,
              adjust_task_data: adjustdata,
            })
            .then(function (response) {
              if (response.data.code == 'information') {
                toolingMessage = response.data.text
                _this
                  .$confirm(toolingMessage, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                  })
                  .then(() => {
                    _this.save_plan_action(
                      _this.str_en_type,
                      pt_main,
                      pt_team_plans,
                      _this.adjust_main_date,
                      adjustdata,
                      _this,
                      iswholemove
                    )
                  })
                  .catch(() => {
                    _this.$message({
                      type: 'info',
                      message: '取消保存',
                    })
                    _this.saveLoading = false
                    console.log(_this.saveLoading)
                  })
              } else if (response.data.code == 'error') {
                _this.$message({
                  type: 'error',
                  message: response.data.text,
                })
                _this.saveLoading = false
              } else {
                _this.save_plan_action(
                  _this.str_en_type,
                  pt_main,
                  pt_team_plans,
                  _this.adjust_main_date,
                  adjustdata,
                  _this,
                  iswholemove
                )
              }
            })
            .catch(function (error) {
              console.log(error)
              _this.saveLoading = false
            })
        } else {
          _this.save_plan_action(
            _this.str_en_type,
            pt_main,
            pt_team_plans,
            _this.adjust_main_date,
            adjustdata,
            _this,
            iswholemove
          )
        }
      }
    },
    save_plan_action(str_en_type, pt_main, pt_team_plans, adjust_main_date, adjustdata, _this, iswholemove) {
      // 可以保存
      axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_save_team_plan',
          pt_team_plan_dto: { pt_main: pt_main, pt_team_plans: pt_team_plans },
          adjust_main_date: adjust_main_date,
          adjust_task_data: adjustdata,
          str_en_type: str_en_type,
          iswholemove: iswholemove,
        })
        .then(function (response) {
          if (response.data.code == 'success') {
            _this.delayedshit_list = response.data?.data || []
            _this.$message({
              type: 'success',
              message: 'Save success',
            })
            _this.saveLoading = false
            _this.closeDialog()
          } else {
            _this.$message({
              type: 'warning',
              message: response.data.text,
            })
            _this.saveLoading = false
          }
        })
        .catch(function (error) {
          console.log(error)
          _this.saveLoading = false
        })
    },
    /**获取详情  若果有ID 是编辑*/
    getInfo() {
      let _this = this
      if (_this.id_main && _this.is_edit) {
        // 编辑

        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_get_team_plan',
            id_main: _this.id_main,
          })
          .then(function (response) {
            let task_list_t = []
            _this.input_id_main = response.data.data.pt_team_plan_dto.pt_main.id
            _this.plan_team = response.data.data.pt_team_plan_dto.pt_main.id_team
            response.data.data.pt_team_plan_dto.pt_team_plans.forEach((x) => {
              let team_staff_t = []

              let team_sec_staff_t = []

              x.pt_team_plan_staffs.forEach((element) => {
                // 处理team 人员
                if (element.int_staff_type == 1) {
                  team_staff_t.push(element.id_staff)
                } else {
                  team_sec_staff_t.push(element.id_staff)
                }
              })
              let pt_assembly_task = []
              x.pt_assembly_task.forEach((element) => {
                // 处理装配任务
                pt_assembly_task.push(element)
              })
              let shift_times_t = []
              let delay_shift_times_t = []
              shift_times_t.push(x.pt_team_plan?.dt_shift_start, x.pt_team_plan?.dt_shift_end)
              if (x.pt_team_plan?.dt_delayed_start && x.pt_team_plan?.dt_delayed_end) {
                delay_shift_times_t.push(x.pt_team_plan?.dt_delayed_start, x.pt_team_plan?.dt_delayed_end)
              }
              task_list_t.push({
                id: x.pt_team_plan.id,
                id_task: x.pt_team_plan.str_e_obery_task_id,
                str_task_name: x.pt_team_plan.str_task_name,
                id_model: x.pt_team_plan.id_model,
                id_assembly_task: pt_assembly_task,
                id_site: x.pt_team_plan.id_site,
                id_team_staffs: team_staff_t,
                id_team_sec_staffs: team_sec_staff_t,
                id_shift: x.pt_team_plan.id_shift,
                shift_times: shift_times_t,
                id_delayed_shift: x.pt_team_plan.id_delayed_shift,
                delay_shift_times: delay_shift_times_t,
                str_remark: x.pt_team_plan.str_remark,
              })
            })
            _this.task_list = task_list_t || []
            _this.str_num_plan = _this.task_list.length
          })
          .catch(function (error) {
            console.log(error)
          })
      } else if (_this.id_main && !_this.is_edit) {
        // 查看
      }
    } /**关闭弹 */,
    closeDialog() {
      let _this = this
      _this.$emit('close_dialog')
      _this.$emit('get_back_close_dialog') //关闭详情页
      _this.is_show = false
    },
    /**选择任务带出单元体 */
    changeSelectTask(task, index) {
      let _this = this
      let task_t = _this.task_data.find((x) => x.id_task == task.id_task)
      var index = task_t.str_task_name.indexOf(' ')
      var model = task_t.str_task_name.substring(0, index)
      task.id_model = _this.model_list.find((x) => x.str_name == model)?.id || ''
      task.str_e_obery_task_id = task_t.id_task
      task.str_e_obery_id = task_t.id
      _this.str_sm = task_t.str_sm
      _this.get_assembly_task()
    },
    changeSelectSm(task, index) {
      let _this = this
      _this.$forceUpdate() // 刷新视图
    },
    /**初始化加载 数据 */
    inint() {
      this.get_team_sec_list()
      this.get_team_list()
      this.get_assembly_task()
      this.get_model_list()
      this.get_site()
      this.get_shit_list()
      this.get_delayedshit_list()
    },
  },
  created: function () {
    Promise.all([
      this.get_team_list(),
      this.get_assembly_task(),
      this.get_task_list(),
      this.get_site(),
      this.get_team_sec_list(),
      this.get_shit_list(),
      this.get_delayedshit_list(),
      this.get_model_list(),
    ]).then(() => {
      this.getInfo()
    })
    // this.get_task_list();
    // this.get_model_list();
    // this.get_team_sec_list();
    // this.get_team_list();
    // this.get_shit_list();
    // this.get_delayedshit_list();
  },
  mounted() {
    this.add_task_plan()
  },

  template: /*html*/ `
    <div>
      <el-dialog
        v-loading="saveLoading"
        title="Team Plan"
        width="80%"
        :visible.sync="is_show"
        class="self_dialog"
        @close="closeDialog()"
      >
        <el-row align="middle">
          <el-col :span="4">
            <el-badge :value="str_num_plan" class="item" type="info">
              <el-button size="small">Num</el-button>
            </el-badge>
          </el-col>
          <el-col :span="6" v-if="!id_main==''">
            Schedule adjust to:
            <el-date-picker
              v-model="adjust_main_date"
              value-format="yyyy-MM-dd"
              type="date"
              size="small"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </el-col>

          <el-col :span="6" v-if="!id_main==''" style="margin-top:10px">
            Is whole move:
            <el-checkbox v-model="is_whole_move" size="small"></el-checkbox>
          </el-col>
        </el-row>

        <el-row style="margin-bottom: 10px;" v-for="(task,index) in task_list" v-bind:key="index">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>&nbsp;&nbsp;</span>
              <el-button style="float: right;" type="danger" size="small" @click="delete_task(task,index)">
                Delete
              </el-button>
            </div>
            <el-form ref="form1" :model="task" :rules="rulesForm1" label-position="left" label-width="auto">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="Task" prop="id_task">
                    <el-select
                      v-model="task.id_task"
                      filterable
                      placeholder="select task"
                      style="width: 100%;"
                      @change="changeSelectTask(task,index)"
                    >
                      <el-option
                        v-for="item in task_data"
                        :key="item.id_task"
                        :label="item.str_task_name"
                        :value="item.id_task"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Task description" prop="str_task_name">
                    <el-input v-model="task.str_task_name" filterable placeholder="input task description"> </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Model" prop="id_model">
                    <el-select
                      v-model="task.id_model"
                      filterable
                      placeholder="select model"
                      style="width: 100%;"
                      @change="changeSelectSm(task,index)"
                    >
                      <el-option
                        v-for="item in model_list"
                        :key="item.id"
                        :label="item.str_name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8" v-if="str_flow.toLowerCase()=='f4-1'">
                  <el-form-item label="Assembly task" prop="id_assembly_task">
                    <el-select
                      v-model="task.id_assembly_task"
                      clearable
                      filterable
                      multiple
                      placeholder="select Assembly Task"
                      :reserve-keyword="true"
                      style="width: 100%;"
                    >
                      <el-option
                        v-for="item in assembly_task"
                        :key="item.id"
                        :label="item.str_name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8" v-if="str_flow.toLowerCase()=='f4-1'">
                  <el-form-item label="Site" prop="id_site">
                    <el-select
                      v-model="task.id_site"
                      clearable
                      filterable
                      placeholder="select Site"
                      style="width: 100%;"
                    >
                      <el-option
                        v-for="item in sites"
                        :key="item.id"
                        :label="item.str_name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="Team" prop="id_team_staffs">
                    <el-select
                      v-model="task.id_team_staffs"
                      filterable
                      placeholder="select team staff"
                      multiple
                      style="width: 100%;"
                    >
                      <el-option
                        v-for="item in team_staff_list"
                        :key="item.id"
                        :label="item.str_name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="Team Sec " prop="str_sec_manager">
                    <el-select
                      v-model="task.id_team_sec_staffs"
                      filterable
                      placeholder="select team sec staff"
                      multiple
                      style="width: 100%;"
                    >
                      <el-option
                        v-for="item in teamsection_staff_list"
                        :key="item.id"
                        :label="item.str_name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="Shift" prop="id_shift">
                    <el-select
                      v-model="task.id_shift"
                      filterable
                      placeholder="select shift"
                      style="width: 100%;"
                      @change="changeSelectShift(task,index)"
                    >
                      <el-option v-for="item in shit_list" :key="item.id" :label="item.str_name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="Shift time" prop="shift_times">
                    <el-time-picker
                      is-range
                      v-model="task.shift_times"
                      format="HH:mm"
                      :disabled="!task.is_edit"
                      value-format="HH:mm"
                      range-separator="-"
                      start-placeholder="Start"
                      end-placeholder="End"
                      placeholder="time quantum"
                      style="width: 100%;"
                      @input="testClick($event,task,index)"
                    >
                    </el-time-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :offset="8"> </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="Extension shift" prop="str_sec_manager">
                    <el-select
                      v-model="task.id_delayed_shift"
                      clearable
                      filterable
                      placeholder="select shift"
                      style="width: 100%;"
                      @change="changeSelectDelayShift($event,task)"
                    >
                      <el-option
                        v-for="item in delayedshit_list"
                        :key="item.id"
                        :label="item.str_name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Extension shift time" prop="delay_shift_times">
                    <el-time-picker
                      is-range
                      v-model="task.delay_shift_times"
                      format="HH:mm"
                      value-format="HH:mm"
                      range-separator="-"
                      start-placeholder="Start"
                      end-placeholder="End"
                      placeholder="time quantum"
                      style="width: 100%;"
                    >
                    </el-time-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item label="Remark" prop="str_remark">
                  <el-input type="textarea" v-model="task.str_remark"></el-input>
                </el-form-item>
              </el-row>
              <el-row v-if="!id_main=='' && str_num_plan>1">
                <el-form-item label="task adjust to">
                  <el-date-picker
                    v-model="task.adjust_date"
                    value-format="yyyy-MM-dd"
                    type="date"
                    :picker-options="pickerOptions"
                  ></el-date-picker>
                </el-form-item>
              </el-row>
            </el-form>
          </el-card>
        </el-row>
        <el-row>
          <el-button style="margin-left:20px; float:right" size="small" type="success" @click="add_task_plan()">
            +New Scheduling
          </el-button>
        </el-row>
        <div slot="footer">
          <el-button class="topButton_right" style="margin-left:20px;" size="small" type="danger" @click="closeDialog()"
            >Cancle
          </el-button>
          <el-button class="topButton_right" size="small" type="primary" @click="save_plan()">Save </el-button>
        </div>
      </el-dialog>
    </div>
  `,
})
