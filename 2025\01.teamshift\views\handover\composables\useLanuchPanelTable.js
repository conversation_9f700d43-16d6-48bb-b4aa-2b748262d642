const { ref, onMounted } = Vue
import { useTableColumns } from './useTableColumns.js'
import { queryHandoverStart } from '../api/index.js'

export const useLanuchPanelTable = (props, businessType, page) => {
  const { tableColumns } = useTableColumns(businessType)
  const tableData = ref([])

  const tableProps = ref({
    tableData: tableData.value,
    tableColumns: tableColumns.value,
  })

  // 获取开工列表
  const getTableData = async () => {
    const param = {
      CurrentPage: page.currentPage,
      PageSize: page.pageSize,
      id_sms: props.queryParams.sm,
      id_wo: props.queryParams.esn,
      str_handover_type: businessType,
      int_status: '0',
      int_handover_status: '1',
    }
    const res = await queryHandoverStart(param)
    tableData.value = res.items
    page.total = res.totalCount
  }

  onMounted(() => {
    getTableData()
  })

  return {
    tableData,
    tableColumns,
    tableProps,
    getTableData,
  }
}
