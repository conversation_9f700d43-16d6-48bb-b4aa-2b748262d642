import { post } from '../../../utils/request.js'

/**
 * 获取枚举
 * @returns {Promise} 枚举数据
 */
export function getBusinessTypes() {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_enum',
    str_key: 'handover_type',
  })
}

/**
 * 枚举类型
 * @typedef {Object} Enum
 * @property {string} '101' - F1-2交接
 * @property {string} '102' - F2交接
 * @property {string} '103' - F1分解交接
 * @property {string} '104' - F4装配交接
 * @property {string} '105' - F1检验
 * @property {string} '106' - F4检验
 */

/**
 * 查询参数
 * @typedef {Object} QueryParams
 * @property {string} dt_range - 日期范围
 * @property {string} charType - 图表类型 '交接提交' 或 '交接接收'
 * @property {Enum} strType - 业务类型
 */
/**
 * 获取交接统计数据
 * @param {QueryParams} params - 请求参数
 * @returns {Promise} 交接统计数据
 */
export function getHandoverStatistics(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_handover_pie_chart',
    ...params,
  })
}

/**
 * 查询参数
 * @typedef {Object} QueryDetailParams
 * @property {string} dt_range - 日期范围
 * @property {string} charType - 图表类型 '交接提交' 或 '交接接收'
 * @property {Enum} strType - 业务类型
 * @property {string} itemType - 数据类型
 */
/**
 * 获取交接统计详情
 * @param {QueryDetailParams} params - 请求参数
 * @returns {Promise} 交接统计详情
 */
export function getHandoverDetail(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_handover_pie_chart_list',
    ...params,
  })
}
