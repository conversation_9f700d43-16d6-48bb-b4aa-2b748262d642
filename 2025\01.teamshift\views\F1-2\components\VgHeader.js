const { computed, defineComponent } = Vue

// 磨削（VG）头部内容组件
const VgHeader = defineComponent({
  name: 'VgHeader',
  props: {
    column: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const vgData = computed(() => {
      return props.column.grind.gingdata.map((item) => {
        return {
          ...item,
          sm: item.sm.filter((item) => item.str_type === 'VG'),
        }
      })
    })
    // 是否显示编辑按钮
    const isShowEdit = computed(() => {
      return props.column.grind.is_vg === '0'
    })

    // 获取VG下的str_esn和str_team
    const getVgStrEsnAndStrTeam = (vgItem) => {
      if (vgItem.sm.length > 0) {
        return `${vgItem.str_esn}${vgItem.str_team}:`
      }
      return ''
    }

    // 获取vgItem中sm的str_sm 拼接成字符串
    const getSmStrSm = (vgItem) => {
      return vgItem.sm.map((item) => item.str_sm).join(',')
    }

    // 判断是否闪烁
    const isBlink = computed(() => {
      return props.column.grind.vgfullflag === '1'
    })

    return {
      vgData,
      isShowEdit,
      getSmStrSm,
      isBlink,
      getVgStrEsnAndStrTeam,
    }
  },
  template: /*html*/ `
    <div class="h-full flex flex-col" :class="{ 'vg-blink-bg': isBlink }">
      <div v-if="isShowEdit">
        <el-icon :size="18" color="#FFF" class="cursor-pointer hover:text-blue-600">
          <Edit />
        </el-icon>
      </div>
      <div v-if="vgData && vgData.length > 0">
        <div v-for="vgItem in vgData" :key="vgItem.id_wo" class="flex items-center">
          <el-icon v-if="vgItem.sm.length > 0 && vgItem.is_hide_edit === '0'" :size="18" color="#FFF" class="cursor-pointer hover:text-blue-600">
            <Edit />
          </el-icon>
          <!-- 超出显示省略号 -->
          <div class="truncate"> 
            <span class="font-semibold">{{ getVgStrEsnAndStrTeam(vgItem) }}</span>
            <span class="font-semibold" :title="getSmStrSm(vgItem)">{{ getSmStrSm(vgItem) }}</span>
          </div>
        </div>
      </div>
    </div>
  `,
})

export default VgHeader 