import { post } from '../../../utils/request.js'
/**
 * 获取确认列表
 */
export function getMesConfirmList(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_mes_apply_sub_list',
    Filter: params,
  })
}

/**
 * 获取确认详情
 */
export function getMesConfirmDetail(id) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_mes_apply_sub_by_id',
    id,
  })
}

/**
 * 作废MES申请
 */
export function cancelMesConfirm(id) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_cancel_mes_apply_sub',
    id,
  })
}
