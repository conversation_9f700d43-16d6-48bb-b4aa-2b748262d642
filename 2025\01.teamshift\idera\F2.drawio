<mxfile host="65bd71144e">
    <diagram id="c9sdcKoYkkw9fc-AOJcX" name="第 1 页">
        <mxGraphModel dx="1084" dy="5468" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="3300" pageHeight="4681" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="" style="edgeStyle=none;html=1;strokeWidth=2;fontSize=16;" parent="1" source="2" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="F2 排班系统" style="ellipse;whiteSpace=wrap;html=1;strokeWidth=2;fontSize=16;" parent="1" vertex="1">
                    <mxGeometry x="490" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="edgeStyle=none;html=1;strokeWidth=2;fontSize=16;" parent="1" source="3" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="F2 交付目标" style="whiteSpace=wrap;html=1;strokeWidth=2;fontSize=16;" parent="1" vertex="1">
                    <mxGeometry x="490" y="170" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="组长或组员，自主选择发动机排班" style="whiteSpace=wrap;html=1;strokeWidth=2;fontSize=16;" parent="1" vertex="1">
                    <mxGeometry x="160" y="310" width="780" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>