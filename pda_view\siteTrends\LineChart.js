const { ref, shallowRef, onMounted, watch } = Vue;
const LineChart = {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    // create a reference to the chart
    const chartRef = ref(null);
    // create a reference to the chart instance
    const chartInstance = shallowRef(null);
    // get init options
    const getInitOptions = () => {
      return {
        legend: {
          show: false,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
        },
        grid: {
          top: '4%',
          left: '6%',
          right: '3%',
        },
        xAxis: {
          type: 'category',
          // 斜体
          axisLabel: {
            rotate: 25,
          },
        },
        yAxis: {},
        dataset: {
          source: props.data
          // source: [
          //   {
          //     currentDate: '2021-04-15',
          //     int_num: '120',
          //   },
          //   {
          //     currentDate: '2021-04-16',
          //     int_num: '130',
          //   },
          //   {
          //     currentDate: '2021-04-17',
          //     int_num: '140',
          //   },
          //   {
          //     currentDate: '2021-04-18',
          //     int_num: '150',
          //   },
          //   {
          //     currentDate: '2021-04-19',
          //     int_num: '160',
          //   },
          //   {
          //     currentDate: '2021-04-20',
          //     int_num: '170',
          //   },
          //   {
          //     currentDate: '2021-04-21',
          //     int_num: '180',
          //   },
          // ],
        },
        series: [
          {
            type: 'line',
            encode: {
              x: 'currentDate',
              y: 'int_num',
            },
          },
        ],
      };
    };
    // create the chart
    const createChart = () => {
      const chart = echarts.init(chartRef.value);
      chart.setOption(getInitOptions());
      chartInstance.value = chart;
    };
    // get series by props
    const getSeries = () => {
      return [];
    };
    // get dataset by props
    const getDataset = () => {
      return [];
    };
    // update the chart
    const updateChart = () => {
      chartInstance.value.setOption({
        // series: getSeries(),
        dataset: getDataset(),
      });
    };

    // watch for changes in the data prop
    watch(() => props.data, () => {
      updateChart();
    });

    onMounted(() => {
      createChart();
    });

    // resize the chart
    window.addEventListener('resize', () => {
      chartInstance.value.resize();
    });

    // return the chart reference
    return {
      chartRef,
      createChart,
      updateChart,
    };
  },
  /*html*/
  template: `
    <div ref="chartRef" class="h-[30vh] w-full"></div>
  `,
};

export default LineChart;
