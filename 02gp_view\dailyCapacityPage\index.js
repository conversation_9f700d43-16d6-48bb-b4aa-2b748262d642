import { useTableColumn } from '../hooks/useTableColumn.js';

const { toRefs, defineAsyncComponent, reactive } = Vue;
const DailyCapacityPage = {
  components: {
    HtVxeTable: defineAsyncComponent(() => import('../../components/VxeTable/HtVxeTable.js')),
    PagePager: defineAsyncComponent(() => import('../components/PagePager.js')),
    HtDrawer: defineAsyncComponent(() => import('../../components/ht.drawer.js')),
  },
  setup() {
    const { getDailyCapacityColumns } = useTableColumn();
    const tableState = reactive({
      tableData: [],
      tableColumns: getDailyCapacityColumns(),
    });

    return {
      ...toRefs(tableState),
    };
  },
  // language=HTML
  template: `
    <!--    头部按钮-->
    <div class="flex items-center flex-wrap">
      <article class="mx-4 my-2">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="info" @click="handleDetail">查看</el-button>
        <el-button type="warning" @click="handleEdit">修改</el-button>
        <el-button type="danger" @click="handleDelete">删除</el-button>
      </article>
      <article class="ml-auto mr-4">
        <el-button type="primary">导入</el-button>
        <el-button type="primary">导出</el-button>
      </article>
    </div>
    <div class="border-b-2 mb-2"></div>
    <!--    每日产能配置表格-->
    <div class="mx-4" style="height: calc(100vh - 140px);">
      <HtVxeTable
        ref="basePageTableRef"
        :tableData
        :tableColumns
      ></HtVxeTable>
      <PagePager
        :currentPage="currentPage"
        :pageSize="pageSize"
        :total="total"
        @pageChange="handlePageChange"
      ></PagePager>
    </div>
  `,
};
export default DailyCapacityPage;
