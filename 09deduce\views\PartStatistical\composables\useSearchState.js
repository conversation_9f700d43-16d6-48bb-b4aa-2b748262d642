import { post } from '../../../../config/axios/httpReuest.js'
const { reactive, ref, onMounted } = Vue
export function useSearchState() {
  const searchState = reactive({
    engine: [],
    receiver: '-1',
  })

  // 发动机列表
  const engineList = ref([])

  // 获取发动机列表
  const getEngineList = async () => {
    const { data } = await post({
      ac: 'de_drop_esn_list',
    })
    if (data.code === 'success') {
      engineList.value = data.data.map((item) => ({
        id: item.str_key,
        name: item.str_value,
      }))
    }
  }

  onMounted(() => {
    getEngineList()
  })

  return {
    searchState,
    engineList,
    getEngineList,
  }
}
