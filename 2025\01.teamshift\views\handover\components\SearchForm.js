const { h, defineComponent, inject } = Vue
const { ElForm, ElFormItem, ElSelect, ElOption } = ElementPlus
const { useVModel } = VueUse

export default defineComponent({
  name: 'SearchForm',
  props: {
    esnOptions: {
      type: Array,
      default: () => [],
    },
    smOptions: {
      type: Array,
      default: () => [],
    },
    shiftOptions: {
      type: Array,
      default: () => [],
    },
    taskOptions: {
      type: Array,
      default: () => [],
    },
    taskTypeOptions: {
      type: Array,
      default: () => [],
    },
    staffOptions: {
      type: Array,
      default: () => [],
    },
    modelValue: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['update:modelValue', 'esn-change', 'sm-change', 'shift-change', 'task-change'],
  setup(props, { emit }) {
    const queryParams = useVModel(props, 'modelValue', emit)
    const businessType = inject('businessType')
    // 事件处理函数
    const handleEsnChange = (value) => {
      queryParams.value.esn = value
      emit('esn-change', value)
    }

    const handleSmChange = (value) => {
      queryParams.value.sm = value
      emit('sm-change', value)
    }

    const handleShiftChange = (value) => {
      queryParams.value.shift = value
      emit('shift-change', value)
    }

    const handleTaskChange = (value) => {
      queryParams.value.task = value
      emit('task-change', value)
    }

    return () => {
      const formItems = []

      // ESN 选择器
      if (businessType !== '102') {
        formItems.push(
          h(
            ElFormItem,
            { label: 'ESN' },
            {
              default: () =>
                h(
                  ElSelect,
                  {
                    filterable: true,
                    modelValue: queryParams.value.esn,
                    placeholder: '请选择ESN',
                    clearable: true,
                    'onUpdate:modelValue': handleEsnChange,
                  },
                  {
                    default: () =>
                      props.esnOptions.map((item) =>
                        h(ElOption, {
                          key: item.id_wo,
                          label: item.str_esn,
                          value: item.id_wo,
                        }),
                      ),
                  },
                ),
            },
          ),
        )
      }

      // SM 选择器
      if (businessType != '101' && businessType != '102') {
        formItems.push(
          h(
            ElFormItem,
            { label: 'SM' },
            {
              default: () =>
                h(
                  ElSelect,
                  {
                    filterable: true,
                    modelValue: queryParams.value.sm,
                    placeholder: '请选择SM',
                    clearable: true,
                    'onUpdate:modelValue': handleSmChange,
                  },
                  {
                    default: () =>
                      props.smOptions.map((item) =>
                        h(ElOption, {
                          key: item.id,
                          label: item.str_name,
                          value: item.id,
                        }),
                      ),
                  },
                ),
            },
          ),
        )
      }

      // 班次选择器
      formItems.push(
        h(
          ElFormItem,
          { label: '班次' },
          {
            default: () =>
              h(
                ElSelect,
                {
                  modelValue: queryParams.value.shift,
                  placeholder: '请选择班次',
                  clearable: true,
                  'onUpdate:modelValue': handleShiftChange,
                },
                {
                  default: () =>
                    props.shiftOptions.map((item) =>
                      h(ElOption, {
                        key: item.value,
                        label: item.label,
                        value: item.value,
                      }),
                    ),
                },
              ),
          },
        ),
      )

      // 任务选择器
      if (businessType != '101' && businessType != '102') {
        formItems.push(
          h(
            ElFormItem,
            { label: '任务' },
            {
              default: () =>
                h(
                  ElSelect,
                  {
                    modelValue: queryParams.value.task,
                    placeholder: '请选择任务',
                    clearable: true,
                    'onUpdate:modelValue': handleTaskChange,
                  },
                  {
                    default: () =>
                      props.taskOptions.map((item) =>
                        h(ElOption, {
                          key: item.value,
                          label: item.label,
                          value: item.value,
                        }),
                      ),
                  },
                ),
            },
          ),
        )
      }

      // 工序选择器
      if (businessType == '101' || businessType == '102') {
        formItems.push(
          h(
            ElFormItem,
            { label: '工序' },
            {
              default: () =>
                h(
                  ElSelect,
                  {
                    modelValue: queryParams.value.str_task_type,
                    placeholder: '请选择工序',
                    clearable: true,
                    filterable: true,
                    'onUpdate:modelValue': (value) => {
                      queryParams.value.str_task_type = value
                    },
                  },
                  {
                    default: () =>
                      props.taskTypeOptions.map((item) =>
                        h(ElOption, {
                          key: item.value,
                          label: item.label,
                          value: item.value,
                        }),
                      ),
                  },
                ),
            },
          ),
        )
      }

      // 接收人选择器
      formItems.push(
        h(
          ElFormItem,
          { label: '接收人' },
          {
            default: () =>
              h(
                ElSelect,
                {
                  modelValue: queryParams.value.id_by_to_receive,
                  filterable: true,
                  clearable: true,
                  placeholder: '请选择接收人',
                  'onUpdate:modelValue': (value) => {
                    queryParams.value.id_by_to_receive = value
                  },
                },
                {
                  default: () =>
                    props.staffOptions.map((item) =>
                      h(ElOption, {
                        key: item.value,
                        label: item.label,
                        value: item.value,
                      }),
                    ),
                },
              ),
          },
        ),
      )

      // PENDING 选择器
      formItems.push(
        h(
          ElFormItem,
          { label: 'PENDING' },
          {
            default: () =>
              h(
                ElSelect,
                {
                  modelValue: queryParams.value.is_pending,
                  clearable: true,
                  'onUpdate:modelValue': (value) => {
                    queryParams.value.is_pending = value
                  },
                },
                {
                  default: () => [
                    h(ElOption, { key: 1, label: '是', value: '1' }),
                    h(ElOption, { key: 0, label: '否', value: '0' }),
                  ],
                },
              ),
          },
        ),
      )

      return h(
        ElForm,
        {
          model: queryParams.value,
          class: 'mb-4',
        },
        {
          default: () =>
            h(
              'div',
              {
                class: 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 items-end',
              },
              formItems,
            ),
        },
      )
    }
  },
})
