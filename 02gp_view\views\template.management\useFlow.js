import { getFlow } from '../../api/index.js'

export function useFlow(tableData, tableRef) {
  const flowState = Vue.reactive({
    visible: false,
    flow: '',
    option: [],
  })
  const getFlowOption = async () => {
    flowState.option = await getFlow()
  }
  const handleAddFlow = () => {
    flowState.visible = true
  }
  /* 获取当前排序 */
  const getCurrentSort = () => {
    if (tableData.value.length === 0) {
      const data = tableRef.value.getTableData().tableData
      return data.filter((item) => item.int_level === 1).length
    }
    return tableData.value.filter((item) => item.int_level === 1).length
  }
  const handleSaveFlow = async () => {
    const $table = tableRef.value
    const rid = Date.now()
    const count = getCurrentSort(tableData.value)
    const record = {
      id: rid,
      id_root: null,
      str_node: flowState.flow,
      int_tat: '',
      id_task: '',
      id_task_ago: '',
      str_task_ago: '',
      int_level: 1,
      int_sort: count + 1,
    }
    flowState.visible = false
    await $table.insertAt(record, -1)
    // 将父节点展开
    await $table.setTreeExpand(flowState.currentRow, true)
    handleClearFlow()
  }
  // handle clear flow
  const handleClearFlow = () => {
    flowState.flow = ''
  }
  return {
    flowState,
    getFlowOption,
    handleAddFlow,
    handleSaveFlow,
    handleClearFlow,
  }
}
