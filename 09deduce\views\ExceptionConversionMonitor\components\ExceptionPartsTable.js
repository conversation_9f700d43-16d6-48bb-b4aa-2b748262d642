const { ref, reactive, onMounted, onBeforeUnmount } = Vue
const { useIntervalFn } = VueUse

export default {
  name: 'ExceptionPartsTable',
  props: {
    autoScroll: {
      type: Boolean,
      default: true
    },
    scrollInterval: {
      type: Number,
      default: 3000
    }
  },
  setup(props) {
    const tableData = ref([
      {
        id: 1,
        sequence: '否',
        partNumber: '消件件',
        workOrder: 'E20241121',
        engineNumber: '569919',
        applicationNumber: '649-784-529-0',
        partName: 'NUT CAPTIVE WASHER',
        unitCode: 'QEC',
        exceptionCode: 'SM30'
      },
      {
        id: 2,
        sequence: '否',
        partNumber: 'Hardware',
        workOrder: 'E20241018',
        engineNumber: '598327',
        applicationNumber: '2463M30P01',
        partName: 'RING EXTERNAL RETAINING',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 3,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20241018',
        engineNumber: '598327',
        applicationNumber: 'J1493P05A',
        partName: 'BOLT',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 4,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20241018',
        engineNumber: '598327',
        applicationNumber: 'J1494P05A',
        partName: 'BOLT, MACHINE',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 5,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20250308',
        engineNumber: '802636',
        applicationNumber: '1523M73P01',
        partName: 'Forward heat shield pins and nuts',
        unitCode: 'SM53',
        exceptionCode: 'SM53'
      }
    ])

    const tableColumns = [
      { field: 'sequence', title: '序号', width: 80 },
      { field: 'partNumber', title: '集件已发', width: 120 },
      { field: 'workOrder', title: '工作指令', width: 120 },
      { field: 'engineNumber', title: '发动机号', width: 120 },
      { field: 'applicationNumber', title: '申请件号', width: 150 },
      { field: 'partName', title: '零件名称', width: 200 },
      { field: 'unitCode', title: '单元体', width: 100 },
      { field: 'exceptionCode', title: '异常编码', width: 120 }
    ]

    const tableRef = ref(null)
    const currentScrollTop = ref(0)
    const maxScrollTop = ref(0)

    // 自动滚动功能
    const { pause, resume } = useIntervalFn(() => {
      if (!props.autoScroll || !tableRef.value) return
      
      const tableBody = tableRef.value.$el.querySelector('.vxe-table--body-wrapper')
      if (!tableBody) return

      const scrollHeight = tableBody.scrollHeight
      const clientHeight = tableBody.clientHeight
      maxScrollTop.value = scrollHeight - clientHeight

      if (maxScrollTop.value <= 0) return

      currentScrollTop.value += 30
      if (currentScrollTop.value >= maxScrollTop.value) {
        currentScrollTop.value = 0
      }
      
      tableBody.scrollTop = currentScrollTop.value
    }, props.scrollInterval)

    onMounted(() => {
      if (props.autoScroll) {
        resume()
      }
    })

    onBeforeUnmount(() => {
      pause()
    })

    return {
      tableData,
      tableColumns,
      tableRef
    }
  },
  template: /*html*/ `
    <div class="exception-parts-table">
      <div class="table-header bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-t-lg">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">异常集件缺件零件</h3>
          <div class="flex items-center space-x-4">
            <span class="text-red-200">提取处理状态为：未处理的零件</span>
            <span class="text-yellow-200">5分钟刷新一次此模块数据</span>
          </div>
        </div>
      </div>
      <div class="table-container bg-white rounded-b-lg shadow-lg overflow-hidden">
        <vxe-table
          ref="tableRef"
          :data="tableData"
          :columns="tableColumns"
          border
          stripe
          height="300px"
          :scroll-y="{ enabled: true, mode: 'wheel' }"
          :row-config="{ isHover: true }"
          class="modern-table"
        >
          <vxe-column type="seq" width="60" fixed="left" title="#"></vxe-column>
          <vxe-column
            v-for="column in tableColumns"
            :key="column.field"
            :field="column.field"
            :title="column.title"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }" v-if="column.field === 'sequence'">
              <el-tag :type="row.sequence === '是' ? 'success' : 'info'" size="small">
                {{ row.sequence }}
              </el-tag>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>
  `
} 