import { getCheckAutoScheduleData, fetchPersonnelOptions } from '../api/index.js'
const { ref, onMounted } = Vue
const { ElMessage, ElLoading } = ElementPlus
export function useAssignTaskDrawer(props) {
  // 表格数据
  const allTasks = ref([])
  // 获取表格数据
  const getTableData = async () => {
    const loadingInstance = ElLoading.service({
      lock: true,
      text: 'Loading',
      target: document.querySelector('.common-drawer'),
    })
    try {
      const params = {
        dt_range: props.searchForm.date,
        str_engine_type: props.enginetype,
        str_type: props.type,
        str_flow: props.flow,
        teams: props.searchForm.str_team,
        str_sm: props.searchForm.str_sm,
        str_esn: props.searchForm.str_esn,
        str_wo: props.searchForm.str_wo,
        str_group: props.searchForm.str_type,
        staffs: props.searchForm.id_staff,
      }
      const res = await getCheckAutoScheduleData(params)
      // 取res数组中的task数据对象组合成一个新的数组
      allTasks.value = res
        .reduce((prev, next) => {
          return prev.concat(next.tasks)
        }, [])
        .map((item) => {
          return {
            id: item.id,
            taskId: item.id_task,
            date: item.dt_shift,
            wo: item.str_wo,
            task: item.str_task,
            shift: item.str_shift,
            type: item.str_task_type,
            assignedMember: item.staffs,
            skill: item.id_skill,
            shiftId: item.id_shift,
            decTat: item.dec_tat,
            sm: item.str_sm,
            checkType: item.int_check_type,
            woId: item.id_wo,
            str_group: item.str_group,
          }
        })
    } catch (error) {
      ElMessage.error('获取数据失败')
    } finally {
      loadingInstance.close()
    }
  }

  // 存储每个日期和技能组合的人员选项缓存
  const memberOptionsCache = ref({})

  // 获取人员选项的函数，接收日期和技能作为参数
  const getMemberOptions = async (date, skillId, shiftId) => {
    if (!date || !skillId || !shiftId) {
      return []
    }

    // 创建缓存键
    const cacheKey = `${date}_${skillId}_${shiftId}`

    // 如果缓存中已有该组合的数据，直接返回
    if (memberOptionsCache.value[cacheKey]) {
      return memberOptionsCache.value[cacheKey]
    }

    try {
      const params = {
        id_skill: skillId,
        dt_shift: date,
        id_shift: shiftId,
      }
      const res = await fetchPersonnelOptions(params)
      const options = res.map((item) => {
        return {
          value: item,
          label: item.str_name,
        }
      })

      // 将结果存入缓存
      memberOptionsCache.value[cacheKey] = options

      return options
    } catch (error) {
      ElMessage.error('获取人员选项失败')
      return []
    }
  }

  onMounted(() => {
    getTableData()
  })

  return {
    allTasks,
    getMemberOptions,
    memberOptionsCache,
  }
}
