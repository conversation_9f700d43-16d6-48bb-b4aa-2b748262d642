import { post } from '../../config/axios/httpReuest.js'
import { DM_VIEW, PDA_VIEW_AD, PDA_VIEW_P, WAYBILL_VIEW, WAYBILL_VIEW_ADMIN } from '../../config/tabPaneKey.js'
import { useFilter } from './useFilter.js'
import { useTableColumn } from './useTableColumn.js'

const { ref } = Vue

export function useTable(isShowColumn = false) {
  const { getTableColumn } = useTableColumn()
  const xTable = ref(null)
  // 列数据
  const columnData = ref([])
  // 获取列数据
  const getColumnData = (currentNode) => {
    columnData.value = getTableColumn(currentNode)
  }
  // 列配置
  const columnsConfig = {
    resizable: true,
  }
  // 表格数据
  const tableData = ref([])
  // 总数
  const totalNum = ref(0)

  const getDmFilter = (searchForm) => {
    const filter = []
    for (let key in searchForm) {
      if (
        searchForm[key] !== '' &&
        searchForm[key] !== null &&
        searchForm[key] !== undefined &&
        searchForm[key].length > 0
      ) {
        filter.push({
          str_key: key,
          str_value: searchForm[key],
        })
      }
    }
    return filter
  }
  const { getFilterData } = useFilter()
  // 获取ac接口
  const getParams = (str_type, dt_date, dt_p0, str_ywtype, currentTypeView, str_d, str_m, searchForm) => {
    let params = {}
    if (currentTypeView === WAYBILL_VIEW || currentTypeView === WAYBILL_VIEW_ADMIN) {
      params = {
        ac: 'pda_waybill',
        str_type,
      }
    } else if (currentTypeView === PDA_VIEW_P || currentTypeView === PDA_VIEW_AD) {
      params = {
        ac: 'pda_get_pda_list_pn',
        str_type,
        dt_date,
        str_ywtype,
        filter_fields: getFilterData(searchForm),
        sort_fields: [],
        dt_p0,
      }
    } else if (currentTypeView === DM_VIEW) {
      params = {
        ac: 'pda_DM_Pn_list',
        str_type,
        str_d,
        str_m,
        filter_fields: getDmFilter(searchForm),
        sort_fields: [],
        dt_p0,
      }
    } else {
      params = {
        ac: 'pda_get_plan_plist',
        str_type,
        dt_date,
        str_ywtype,
        filter_fields: getFilterData(searchForm),
        sort_fields: [],
        dt_p0,
      }
    }
    return params
  }
  /**
   * 获取表格数据
   * @param str_type 节点类型
   * @param dt_date 日期
   * @param dt_p0  当前时间
   * @param str_ywtype  Card-pc: 卡片视图-排产量 Card-p: 卡片视图-当日流入P
   * @param currentTypeView 当前视图
   * @param str_d DM的D坐标
   * @param str_m DM的M坐标
   * @param searchForm 搜索表单
   */
  const getTableData = async (str_type, dt_date, dt_p0, str_ywtype, currentTypeView, str_d, str_m, searchForm) => {
    const params = getParams(str_type, dt_date, dt_p0, str_ywtype, currentTypeView, str_d, str_m, searchForm)
    const { data } = await post(params)
    tableData.value = data.data
    totalNum.value = data.data.length
  }
  /**
   *  当属性为int_day的时候要带上符号
   *  @param property
   *  @param option
   *  @param row
   *  @returns {boolean}
   */
  const filterMethodByIntDay = (property, option, row) => {
    if (option.value.includes('>=')) {
      return parseInt(row[property]) >= parseInt(option.value.replace('>=', ''))
    } else if (option.value.includes('<=')) {
      return parseInt(row[property]) <= parseInt(option.value.replace('<=', ''))
    } else if (option.value.includes('>')) {
      return parseInt(row[property]) > parseInt(option.value.replace('>', ''))
    } else if (option.value.includes('<')) {
      return parseInt(row[property]) < parseInt(option.value.replace('<', ''))
    } else {
      return parseInt(row[property]) === parseInt(option.value)
    }
  }
  /**
   * 当属性为is_out或者is_sp/is_aog的时候
   * @param property
   * @param option
   * @param row
   */
  const filterMethodByIsOutOrIsSp = (property, option, row) => {
    if (option.value === 1 && row[property] != null && row[property] !== undefined) {
      return parseInt(row[property]) === 1
    } else {
      if (row[property] == null) {
        return row[property] !== 1
      }
      return parseInt(row[property]) !== 1
    }
  }
  /**
   * 筛选
   * @param value
   * @param option
   * @param cellValue
   * @param row
   * @param column
   */
  const filterMethod = ({ value, option, cellValue, row, column }) => {
    const property = column.property
    if (property === 'int_day' || property === 'int_d') {
      return filterMethodByIntDay(property, option, row)
    } else if (property === 'is_out' || property === 'is_sp' || property === 'is_aog') {
      return filterMethodByIsOutOrIsSp(property, option, row)
    }
    // 全部转为小写
    return String(row[property]).toLocaleLowerCase().indexOf(option.value.toLocaleLowerCase()) > -1
  }
  /**
   * 筛选变化
   * @param property
   * @param values
   * @param datas
   * @param filterList
   * @returns {void}
   */
  const filterChange = ({ property, values, datas, filterList }) => {
    const $table = xTable.value
    totalNum.value = $table.getTableData().visibleData.length
  }
  //重置
  const filterRecoverMethod = ({ options }) => {
    // 判断是否是下拉框筛选
    if (options.length > 1) {
      options.forEach((item, index) => {
        if (item.label === 'YES') {
          item.value = 1
        } else {
          item.value = 0
        }
      })
      return
    }
    // 如果是自定义筛选模板，当为点击确认时，该选项将被恢复为默认值
    options[0].value = ''
  }

  // 表格高度
  const tableHeight = ref(window.innerHeight - 210)
  // 监听窗口变化
  window.onresize = () => {
    tableHeight.value = window.innerHeight - 210
  }

  // 导出
  const exportDataEvent = () => {
    const $table = xTable.value
    const isSupportExportXLSX = VXETable.globalConfs.exportTypes.includes('xlsx')
    if (isSupportExportXLSX) {
      $table.exportData({
        type: 'xlsx',
        sheetName: 'Sheet1',
        useStyle: true,
        beforeExportMethod: onBeforeExport,
        download: false,
      })
    } else {
      $table.exportData({ type: 'csv', beforeExportMethod: onBeforeExport })
    }
  }

  // 导出前处理
  const onBeforeExport = ({ options }) => {
    // 1. 获取数据
    const data = xTable.value.getTableData().visibleData
    // 2. 处理数据 将数据为null的值替换为空字符串
    const newData = data.map((item) => {
      return Object.fromEntries(Object.entries(item).map(([key, value]) => [key, value === null ? ' ' : value]))
    })
    // 3. 将处理后的数据赋值给options.data
    options.data = newData
  }

  /**
   * 表格行样式
   */
  const handleRowClassName = ({ row, rowIndex }) => {
    if (row.is_forecast) {
      return 'row-green'
    }
    if (row.colortype === 1) {
      return 'row-red'
    }
    if (row.is_compute_type === -1) {
      return 'row-orange';
    }
    if (row.is_compute_type === 1) {
      return 'row-yellow';
    }
  };

  return {
    xTable,
    columnData,
    columnsConfig,
    tableData,
    getTableData,
    tableHeight,
    filterMethod,
    totalNum,
    filterChange,
    filterRecoverMethod,
    exportDataEvent,
    getColumnData,
    handleRowClassName,
  }
}
