/*
    模板管理接口
 */

import { post } from '../../config/axios/httpReuest.js';

/**
 * 获取模板列表
 * @param queryLists
 * @return {Promise<*[]>}
 */
export const queryTemplate = async (queryLists = []) => {
  const list = [];
  const params = {
    ac: 'gp_template_search',
    queryLists,
  };
  const { data } = await post(params);
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text);
    return [];
  }
  list.push(...data.data);
  return list;
};
