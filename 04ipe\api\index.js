import { post } from '../../config/axios/httpReuest.js'
export const queryEmployeeScore = (month, isDistribution = false) => {
  const params = {
    ac: 'pe_global_employee_score',
    month,
    isDistribution
  }
  return post(params)
}

/**
 * getExceptionStatus
 */
export const queryExceptionStatus = (month = moment().format('YYYY-MM'), type) => {
  const params = {
    ac: 'pe_global_exception_status',
    month,
    type
  }
  return post(params)
}

//Export
export const exportExcel = (str_month) => {
  const params = {
    ac: 'pe_export',
    str_month
  }
  return post(params)
}

//Compute
export const computeStaff = (str_month) => {
  const params = {
    ac: 'pe_compute_staff_score',
    str_month
  }
  return post(params)
}

export const computeFlow = (str_month) => {
  const params = {
    ac: 'pe_wip_tat_excute',
    str_month
  }
  return post(params)
}

export const computeTeam = (str_month) => {
  const params = {
    ac: 'pe_compute_team_score',
    str_month
  }
  return post(params)
}

export const queryUpdateState = (str_month) => {
  const params = {
    ac: 'pe_get_update_state',
    str_month
  }
  return post(params)
}

export const queryLockState = (str_month) => {
  const params = {
    ac: 'pe_search_lock',
    str_month,
  }
  return post(params)
}

export const updateLockState = (str_month, is_lock) => {
  const params = {
    ac: 'pe_month_lock',
    str_month,
    is_lock
  }
  return post(params)
}
