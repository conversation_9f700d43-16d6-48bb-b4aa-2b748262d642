const { ref, reactive, computed, watch, onMounted } = Vue
const { ElMessage, ElMessageBox } = ElementPlus

// 导入组件和配置
import { MesApplyFormDetails, MES_CONFIG, MesBusinessRules, MesApplyForm } from './components/index.js'
import HtVxeTable from '../../components/VxeTable/HtVxeTable.js'
import { useBrowserZoom } from '../../utils/browser-zoom.js'
import {
  getMesApplyList,
  getMesApplyById,
  submitMesApplyForApproval,
  confirmMesApply,
  withdrawMesApply,
} from './api/index.js'

export default {
  name: 'MesApply',
  components: {
    MesApplyForm,
    MesApplyFormDetails,
    HtVxeTable,
  },
  setup() {
    // 响应式数据
    const currentView = ref('list')
    const loading = ref(false)
    const tableRef = ref(null)
    const tableData = ref([])

    // 操作按钮loading状态
    const operationLoading = reactive({
      submit: {},
      confirm: {},
      withdraw: {},
      edit: {},
      view: {},
    })

    // 分页数据
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
      pageSizes: MES_CONFIG.table.pagination.pageSizes,
      layout: MES_CONFIG.table.pagination.layout,
    })

    const searchForm = ref({})

    // 申请表单
    const formData = reactive({
      id: null,
      person: '',
      startDate: '',
      endDate: '',
      reason: '',
      remarks: '',
      details: [],
    })

    // 配置数据
    const statusOptions = MES_CONFIG.status.options
    const tableColumns = ref([...MES_CONFIG.table.columns])
    const formRules = MES_CONFIG.form.rules

    // 动态获取审批状态选项
    const updateStatusFilterOptions = (data) => {
      // 找到审批状态列
      const statusColumn = tableColumns.value.find((col) => col.field === 'int_substatus')
      if (statusColumn && (!statusColumn.filters || statusColumn.filters.length === 0)) {
        // 从数据中提取唯一的状态值
        const statusSet = new Set()
        data.forEach((item) => {
          if (item.int_substatus !== undefined && item.int_substatus !== null) {
            statusSet.add(item.int_substatus)
          }
        })

        // 状态映射
        const statusMap = {
          0: '草稿',
          1: '审批通过',
          301: '待审批',
          '-1': '审批失败',
          '-99': '撤回',
        }

        // 生成选择选项 - 使用VxeTable原生筛选格式
        const filterOptions = Array.from(statusSet)
          .sort((a, b) => a - b) // 按状态值排序
          .map((status) => ({
            label: statusMap[status] || `状态${status}`,
            value: status,
          }))

        // 设置过滤器配置 - 使用VxeTable原生筛选格式
        statusColumn.filters = filterOptions
      }
    }

    // 初始化数据
    const initData = async () => {
      try {
        loading.value = true
        const params = {
          PageSize: pagination.pageSize,
          Currentpage: pagination.currentPage,
          ...searchForm.value,
        }
        const applications = await getMesApplyList(params)
        tableData.value = applications.items
        pagination.total = applications.totalCount

        // 动态更新审批状态过滤选项
        updateStatusFilterOptions(applications.items)
      } catch (error) {
        ElMessage.error('数据加载失败')
      } finally {
        loading.value = false
      }
    }

    const createDialogVisible = ref(false)
    const createLoading = ref(false)

    const handleCreate = () => {
      if (createLoading.value) return

      createLoading.value = true

      // 重置表单数据
      Object.assign(formData, {
        id: null,
        person: '',
        startDate: '',
        endDate: '',
        reason: '',
        remarks: '',
        details: [],
      })
      currentView.value = 'create'
      createDialogVisible.value = true

      // 延迟重置loading状态，避免闪烁
      setTimeout(() => {
        createLoading.value = false
      }, 300)
    }

    const handleEdit = async (row) => {
      try {
        // 设置按钮loading状态
        operationLoading.edit[row.id] = true
        loading.value = true

        // 使用API获取完整的申请详情
        const detailData = await getMesApplyById(row.id)

        const { apply, applysubs } = detailData
        Object.assign(formData, {
          id: apply.id,
          person: apply.id_staff,
          startDate: apply.dt_mes_start,
          endDate: apply.dt_mes_end,
          reason: apply.str_apply_reason,
          remarks: apply.str_remark || '',
          details: applysubs.map((sub) => ({
            date: sub.dt_date,
            startTime: sub.dt_start_time,
            endTime: sub.dt_end_time,
            appliedHours: sub.int_apply_hours,
            confirmStartTime: sub.dt_confirm_start || '',
            confirmEndTime: sub.dt_confirm_end || '',
            confirmHours: sub.int_confirm_hours || 0,
            confirmStatus: sub.int_confirm_status,
          })),
        })

        currentView.value = 'edit'
        createDialogVisible.value = true
      } catch (error) {
        console.error('获取申请详情失败:', error)
        ElMessage.error('获取申请详情失败，请重试')
      } finally {
        operationLoading.edit[row.id] = false
        loading.value = false
      }
    }

    const handleView = async (row) => {
      try {
        // 设置按钮loading状态
        operationLoading.view[row.id] = true
        loading.value = true

        // 使用API获取完整的申请详情
        const detailData = await getMesApplyById(row.id)

        const { apply, applysubs } = detailData
        // 格式化数据以匹配表单结构，包含确认字段
        Object.assign(formData, {
          id: apply.id,
          person: apply.id_staff,
          startDate: apply.dt_mes_start,
          endDate: apply.dt_mes_end,
          reason: apply.str_apply_reason,
          remarks: apply.str_remark || '',
          details: applysubs.map((sub) => ({
            id: sub.id,
            date: sub.dt_date,
            startTime: sub.dt_start_time,
            endTime: sub.dt_end_time,
            appliedHours: sub.int_apply_hours,
            // 包含确认字段用于查看
            confirmStartTime: sub.dt_confirm_start || '',
            confirmEndTime: sub.dt_confirm_end || '',
            confirmHours: sub.int_confirm_hours || 0,
            confirmStatus: sub.int_confirm_status,
          })),
        })

        currentView.value = 'view'
        createDialogVisible.value = true
      } catch (error) {
        console.error('获取申请详情失败:', error)
        ElMessage.error('获取申请详情失败，请重试')
      } finally {
        operationLoading.view[row.id] = false
        loading.value = false
      }
    }

    const handleBack = () => {
      createDialogVisible.value = false
      currentView.value = 'list'
    }

    // 保存成功处理
    const handleSaveSuccess = (event) => {
      createDialogVisible.value = false
      initData() // 刷新列表
    }

    // 状态操作
    const handleSubmitForApproval = async (row) => {
      try {
        await ElMessageBox.confirm('确认提交审批吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        // 设置按钮loading状态
        operationLoading.submit[row.id] = true

        await submitMesApplyForApproval(row.id)
        ElMessage.success('提交成功')
        await initData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('提交失败:', error)
          ElMessage.error('提交失败，请重试')
        }
      } finally {
        operationLoading.submit[row.id] = false
      }
    }

    const handleConfirm = async (row) => {
      try {
        await ElMessageBox.confirm('确认此申请吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        })

        // 设置按钮loading状态
        operationLoading.confirm[row.id] = true

        await confirmMesApply(row.id)
        ElMessage.success('确认成功')
        await initData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('确认失败:', error)
          ElMessage.error('确认失败，请重试')
        }
      } finally {
        operationLoading.confirm[row.id] = false
      }
    }

    const handleWithdraw = async (row) => {
      try {
        await ElMessageBox.confirm('确认撤回吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        // 设置按钮loading状态
        operationLoading.withdraw[row.id] = true

        await withdrawMesApply(row.id)
        ElMessage.success('撤回成功')
        await initData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤回失败:', error)
          ElMessage.error('撤回失败，请重试')
        }
      } finally {
        operationLoading.withdraw[row.id] = false
      }
    }

    // 过滤条件
    const handleFilterChange = (filter) => {
      searchForm.value = {}
      filter.filterList.forEach((element) => {
        // 获取过滤值
        let filterValue = null
        
        // 对于原生筛选，值在values数组中
        if (element.values && element.values.length > 0) {
          filterValue = element.values[0]
        }
        // 对于自定义筛选渲染器，值在datas数组中
        else if (element.datas && element.datas.length > 0) {
          filterValue = element.datas[0]
        }

        // 只有当过滤值不为空时才添加到搜索条件中
        if (filterValue !== undefined && filterValue !== null && filterValue !== '') {
          searchForm.value[element.field] = filterValue
        }
      })
      initData()
    }

    // 分页变化处理
    const handlePageChange = () => {
      initData()
    }

    // 使用浏览器缩放检测和视口适配
    const { zoomLevel, zoomPercentage, isZoomed, adaptedDimensions, fontScale, spacingScale } = useBrowserZoom()

    // 计算缩放级别对应的CSS类名
    const zoomScaleClass = computed(() => {
      const zoom = zoomLevel.value
      if (zoom <= 0.6) return 'zoom-scale-50'
      if (zoom <= 0.8) return 'zoom-scale-75'
      if (zoom <= 1.1) return 'zoom-scale-100'
      if (zoom <= 1.3) return 'zoom-scale-125'
      if (zoom <= 1.7) return 'zoom-scale-150'
      return 'zoom-scale-200'
    })

    // 计算容器样式
    const containerStyle = computed(() => ({
      width: `${adaptedDimensions.value.width}px`,
      height: `${adaptedDimensions.value.height}px`,
      fontSize: `${14 * fontScale.value}px`,
      '--zoom-level': zoomLevel.value,
      '--font-scale': fontScale.value,
      '--spacing-scale': spacingScale.value,
    }))

    // 计算表格高度（减去头部和分页器的高度）
    const tableHeight = computed(() => {
      const headerHeight = isZoomed.value ? 80 : 100
      const paginationHeight = isZoomed.value ? 50 : 60
      const padding = isZoomed.value ? 16 : 24
      return adaptedDimensions.value.height - headerHeight - paginationHeight - padding
    })

    // 生命周期
    onMounted(initData)

    return {
      currentView,
      loading,
      operationLoading,
      pagination,
      formData,
      statusOptions,
      tableRef,
      tableData,
      tableColumns,
      formRules,
      businessRules: MesBusinessRules,
      zoomLevel,
      zoomPercentage,
      isZoomed,
      zoomScaleClass,
      containerStyle,
      tableHeight,
      adaptedDimensions,
      createDialogVisible,
      createLoading,
      initData,
      handleCreate,
      handleEdit,
      handleView,
      handleBack,
      handleSaveSuccess,
      handleSubmitForApproval,
      handleConfirm,
      handleWithdraw,
      handleFilterChange,
      handlePageChange,
    }
  },

  template: /*html*/ `
    <div class="h-screen w-full p-4">
      <!-- 顶部按钮 -->
      <div class="flex justify-end pb-2">
        <el-button 
          type="primary" 
          :loading="createLoading"
          :disabled="createLoading"
          @click="handleCreate"
          icon="Plus"
        >
          {{ createLoading ? '准备中...' : '创建MES申请' }}
        </el-button>
      </div>
      <!-- 表格容器 -->
      <div class="h-[calc(100vh - 220px)]">
        <HtVxeTable
          v-loading="loading"
          ref="tableRef"
          :table-data="tableData"
          :table-columns="tableColumns"
          :height="tableHeight"
          :remote="true"
          @filter-change="handleFilterChange"
        >
          <template #operation>
            <vxe-column title="操作" min-width="220" fixed="right" align="center">
              <template #default="{ row }">
                <div class="flex justify-center gap-2 flex-wrap">
                  <el-button 
                    type="primary" 
                    size="small" 
                    link
                    :loading="operationLoading.view[row.id]"
                    @click="handleView(row)"
                  >
                    查看
                  </el-button>
                  <el-button 
                    v-if="row.int_substatus === 0" 
                    type="warning" 
                    size="small" 
                    link
                    :loading="operationLoading.edit[row.id]"
                    @click="handleEdit(row)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    v-if="row.int_substatus === 0" 
                    type="success" 
                    size="small" 
                    link
                    :loading="operationLoading.submit[row.id]"
                    @click="handleSubmitForApproval(row)"
                  >
                    提交审批
                  </el-button>
                  <el-button 
                    v-if="row.int_substatus !== 1" 
                    type="danger" 
                    size="small" 
                    link
                    :loading="operationLoading.withdraw[row.id]"
                    @click="handleWithdraw(row)"
                  >
                    撤回
                  </el-button>
                </div>
              </template>
            </vxe-column>
          </template>
        </HtVxeTable>
      </div>

      <!-- 分页器 -->
      <div class="pt-2">
        <vxe-pager
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="pagination.pageSizes"
          :layout="pagination.layout"
          :loading="loading"
          @page-change="handlePageChange"
        ></vxe-pager>
      </div>

      <!-- 创建/编辑MES申请组件 -->
      <MesApplyForm
        v-if="createDialogVisible"
        v-model:visible="createDialogVisible"
        :form-data="formData"
        :current-view="currentView"
        :business-rules="businessRules"
        @success="handleSaveSuccess"
        @back="handleBack"
      >
        <template #details="{ details, readonly, onUpdate }">
          <MesApplyFormDetails
            :details="details"
            :readonly="readonly"
            @update:details="onUpdate"
          />
        </template>
      </MesApplyForm>
    </div>
  `,
}
