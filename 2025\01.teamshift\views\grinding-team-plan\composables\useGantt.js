export function useGantt() {
  const configSettings = {
    columns: [
      { name: 'text', label: 'Task', width: '*', tree: true, resize: true },
      { name: 'start_date', label: 'Begin', width: 150 },
      { name: 'dt_end', label: 'End', width: 150 },
    ],
    date_format: '%Y-%m-%d',
    drag_progress: false,
    drag_links: false,
  }
  const templateSettings = {
    task_class: (start, end, task) => {
      if (task.int_level === 0) {
        return '!bg-[#1de9b6]'
      }
      if (task.int_level === 1) {
        return '!bg-[#ff723b]'
      }
    },
  }

  // 移动任务
  const moveTask = (id, mode) => {
    const task = gantt.getTask(id)
    console.log(task)
    return true
  }

  // 事件配置
  const eventSettings = {
    onBeforeTaskDrag: (id, mode) => {
      return moveTask(id, mode)
    },
  }

  return {
    configSettings,
    templateSettings,
    eventSettings,
  }
}
