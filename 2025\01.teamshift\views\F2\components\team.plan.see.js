const { useVModel } = VueUse
const { ref, onMounted } = Vue
import { 
  queryF2TeamPlanView,
  queryEnum,
  queryF2TaskEnum,
  queryF2TargetEnum,
  queryShifts,
  queryTeamStaff,
  queryDeptStaff
 } from '../../../api/teams/index.js'
export default {
  name: 'TeamPlanSee',
  props: {
    planItem: Object,
    visible: Boolean,
    deptId:String,
    teamId:String
  },
  setup(props) {
    const teamPlanSeeVisible = useVModel(props, 'visible')
    const teamPlanSeeList = ref([])
    const num = ref(0)

    const feedbackItem = ref(null)
    const getTeamPlanSeeList = async () => {
      const res = await queryF2TeamPlanView(props.planItem.id_staff,props.planItem.dt_shift)
      //feedbackItem.value = res.pt_team_plan_dtosee.pt_main.str_content
      teamPlanSeeList.value = res ?? []
      num.value = teamPlanSeeList.value.length
    }
 
    const repairTypeEnum = ref({})
    //获取Repair Type下拉选择
    const getRepairTypeOptions = async () => { 
      const res = await queryEnum("pb_f2_repair_type")
      const repairTypeOptions = res ?? [];
      repairTypeEnum.value = repairTypeOptions.reduce((map, item) => {
        map[item.str_value] = item.str_key
        return map
      }, {})
    }


    const f2TargetEnum = ref([])
    //获取F2交付目标
    const getf2TargetOptions = async () => {
      const res = await queryF2TargetEnum()
      const f2TargetOptions = res ?? []
      f2TargetEnum.value = f2TargetOptions.reduce((map, item) => {
        map[item.id] = item.str_esn
        return map
      }, {})
    }

    
    const shiftEnum = ref([])
    // 获取Site 下拉选择
    const getShiftOptions = async () => {
      const res = await queryShifts()
      const shiftOptions = res.data ?? []
      shiftEnum.value = shiftOptions.reduce((map, item) => {
        map[item.id] = item.str_name
        return map
      }, {})
    }
    
 

     const teamStaffs = ref([])
      //获取F2交付目标
      const getTeamStaff = async () => {
        const res = await queryTeamStaff(props.teamId,props.deptId)
        const teamItems = res ?? []
        teamStaffs.value = teamItems.reduce((map, item) => {
          map[item.id] = item.str_name
          return map
        }, {})
      }

      const deptStaffs = ref([])
      //获取F2交付目标
      const getDeptStaff = async () => {
        const res = await queryDeptStaff(props.deptId)
        const deptitems = res ?? []
        deptStaffs.value = deptitems.reduce((map, item) => {
          map[item.id] = item.str_name
          return map
        }, {})
      }
     
  
    // 转化数组为字符串
    const convertArrayToString = (array) => {
      return array?.join('~')
    }

    const convertArrayToNameName = (array) => {
      let strs = [];
      array.forEach((taskId) => {
        strs.push(deptStaffs.value[taskId]);
      }) 
      return strs?.join(',')
    }
    // 关闭弹窗
    const handleClose = () => {
      teamPlanSeeVisible.value = false
    }


    const f2TaskOptions = ref([])
    // 获取Site 下拉选择
    const getF2TaskOptions = async () => {
      const res = await queryF2TaskEnum()
      const f2TaskItems = res ?? []
      f2TaskOptions.value = f2TaskItems.reduce((map, item) => {
        map[item.str_value] = item.str_key
        return map
      }, {})
    }

    onMounted(() => {
      getTeamPlanSeeList();
      getRepairTypeOptions();
      getf2TargetOptions();
      getShiftOptions();
      getTeamStaff();
      getDeptStaff();
      getF2TaskOptions();
    })
    return {
      teamPlanSeeVisible,
      teamPlanSeeList,
      convertArrayToString,
      convertArrayToNameName,
      num,
      f2TaskOptions,
      repairTypeEnum, 
      f2TargetEnum,
      shiftEnum,
      handleClose,
      deptStaffs,
      teamStaffs
    }
  },
  template: /*html*/ `
    <el-dialog v-model="teamPlanSeeVisible" title="Team Plan See" width="50%" class="common-dialog">
      <div class="mb-2">
        <el-badge :value="num" class="item" type="info">
          <el-button>Num</el-button>
        </el-badge>
      </div>
      <div class="mb-2" v-for="item in teamPlanSeeList" :key="item.id">
        <el-card>
          <el-descriptions :column="3" border>
          <el-descriptions-item label="Name">{{ teamStaffs[item.staffs[0]] }}</el-descriptions-item>
            <el-descriptions-item label="ESN">{{ f2TargetEnum[item.id_f2_target] }}</el-descriptions-item>
            <el-descriptions-item label="Type">{{ item.str_type }}</el-descriptions-item>
            <el-descriptions-item label="Task Type">{{ repairTypeEnum[Number(item.int_task_type)] }}</el-descriptions-item>
            <el-descriptions-item label="Task">{{f2TaskOptions[item.tasks[0]] }}</el-descriptions-item>
            <el-descriptions-item label="目标范围">{{ convertArrayToString(item.dt_target_range) }}</el-descriptions-item>
            <el-descriptions-item label="排班时间">{{ item.dt_shift }}</el-descriptions-item>
            <el-descriptions-item label="Shift">{{ shiftEnum[item.id_shift] }}</el-descriptions-item>
            <el-descriptions-item label="Shift Time">{{ convertArrayToString(item.dt_shift_time) }}</el-descriptions-item>
            <el-descriptions-item :span="3" label="Team Secs">{{ convertArrayToNameName(item.teamSecs) }}</el-descriptions-item>
            <el-descriptions-item :span="3" label="Remark">{{ item.str_remark }}</el-descriptions-item>
          </el-descriptions> 
        </el-card>
      </div>
      <div>
        <el-card header="feedback">
          <div>{{ feedbackItem }}</div>
        </el-card>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleClose">Cancel</el-button>
      </template>
    </el-dialog>
  `,
}
