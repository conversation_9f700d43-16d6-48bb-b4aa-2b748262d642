.my-clip {
    position: relative;
    width: 145px;
    height: 34px;
    /*margin: auto;*/
    cursor: pointer;
    line-height: 34px;
    text-align: center;
    transition: 0.3s;
    clip-path: polygon(var(--clipPath));
    --clipPath: 85% 0%, 100% 50%, 85% 100%, 0% 100%, 15% 50%, 0% 0%;
    --borderWidrh: 1;
    background-color: #535bf2;
    color: #FFFFFF;
}

.my-clip::before {
    content: "";
    position: absolute;
    z-index: -1;
    inset: 0;
    mask: paint(borderDraw);
    background: var(--color);
}

.active {
    background-color: chartreuse;
    color: #000;
    font-weight: 700;
}

.table_data {
    font-size: 10px;
    line-height: 12px;
    background-color: #dfd8e3 !important;
    font-weight: 700;
}

.table_header {
    background-color: #a82cc8 !important;
    color: #FFFFFF;
}

.ht-table .el-table .el-table__cell {
    padding: 1px;
}

.rb-table-one {
    background-color: #c1bbbb !important;
}
.rb-table-two {
    background-color: #3a8ee6 !important;
}
