import { post } from '../../../utils/request.js'

/**
 * 查询参数
 * @typedef {Object} QueryParams
 * @property {string} start_date - 开始日期 eg: 2021-01-01
 * @property {string} end_date - 结束日期 eg: 2021-01-01
 */
/**
 * 查询检验班组计划
 * @param {QueryParams} params
 * @returns
 */
export const getF12Data = async (params) => {
  const res = await post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_query_f12_team_plan',
    ...params,
  })
  return res
}

/**
 * 查询参数
 * @typedef {Object} FilterParams
 * @property {string} id - ID eg: ''
 * @property {string} id_wo - 工作指令 eg: ''
 * @property {string} id_sms - 单元体，逗号拼接字符串 eg: ''
 * @property {string} id_tasks - 任务ID，逗号拼接字符串 eg: ''
 * @property {string} id_shift - 班次ID eg: ''
 * @property {string} str_handover_type - 交接类型 eg: ''
 * @property {string} dt_date - 交接日期 eg: ''
 */
/**
 * 查询检验交接单
 * @param {FilterParams} params
 * @returns
 */
export const getHandover = async (params) => {
  const res = await post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_handover_byid',
    Filter: params,
  })
  return res
}

/**
 * 上传文件
 * @param {FormData} formData - 包含文件和其他信息的FormData
 * @returns {Promise<Object>} 返回上传结果
 */
export const uploadFile = async (formData) => {
  const res = await post('/api/Upload/UploadFile', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  return res
}

/**
 * 保存交接单
 * @param {Object} data - 交接单数据
 * @returns {Promise<Object>} 返回保存结果
 */
export const saveHandover = async (data) => {
  const res = await post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_handover',
    postData: data,
  })
  return res
}

/**
 * 班组计划根据i的获取数据
 * @param {Object} data - ids
 * @returns {Promise<Object>} 返回保存结果
 */
export const GetCheckShiftByIds = async (data) => {
  const res = await post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_check_shift_by_ids',
    ids: data,
  })
  return res
}

export const queryShifts = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_shift',
  })
}

/**
 * 获取Team Sec 选项
 * @param {object} params
 */
export const queryTeamSecList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_teamsection_staff_list',
    ...params,
  })
}

/**
 * 更新检验任务
 * @param {object} params
 */
export const updateCheckShift = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_teamplan_update_check_schedule',
    param_data: params,
  })
}

/**
 * 获取F1-2Team成员
 * @param {object} params
 * @property {string} id_team - 团队ID eg: ''
 * @property {string} dt_shift - 班次日期 eg: ''
 */
export const queryF12TeamMember = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_f12_team_member',
    ...params,
  })
}

/**
 * 人员
 * @typedef {Object} staff
 * @property {string} id_staff - 员工ID eg: ''
 * @property {string} id_shift - 班次ID eg: ''
 */
/**
 *
 * @typedef {Object} teamSec
 * @property {string} id_staff - 团队成员ID eg: ''
 */
/**
 * 参数对象
 * @typedef {Object} PostData
 * @property {string} id - 主键ID eg: ''
 * @property {string} id_f12_allocate - 团队计划ID row.idmain eg: ''
 * @property {string} dt_shift - 班次日期 eg: ''
 * @property {string} id_team - teamID eg: ''
 * @property {string} str_notes - 备注 eg: ''
 * @property {staff[]} staffs - 人员 eg: []
 * @property {teamSec[]} team_secs - 团队成员 eg: []
 */
/**
 * 新增F12Team Plan
 * @param {PostData[]} params
 */
export const saveF12TeamPlan = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_f12_team_plan',
    postData: params,
  })
}

/**
 * 添加初始化数据
 * @param {object} params
 * @property {string} id_main - 主键ID eg: ''
 * @property {string} id_team - 团队ID eg: ''
 * @property {string} dt_shift - 班次日期 eg: ''
 */
export function initAdd(params) {
  return post('/api/Do/DoAPI', {
    ac: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_plan_f12_by_add',
    ...params,
  })
}

/**
 * 编辑初始化数据
 * @param {Array} ids
 */
export function initEdit(ids) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_plan_f12_by_edit',
    ids,
  })
}


/**
 * 获取Team
 */
export const queryTeamList = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_query_team_by_dept',
    str_dept: 'RUPI'
  })
}

/** 
 * 获取Type
 */
export const queryTypeList = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_enum',
    str_key: 'pt_f12_task_type'
  })
}