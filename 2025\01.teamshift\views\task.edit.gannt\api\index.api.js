import { post } from '../../../utils/request.js'



/**
 * 获取排班任务，甘特图
 * @param {*} params
 * @returns
 */
export function getTaskGantt(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_team_task_gantt',
    filter: params,
  })
}

/**
 * 保存调整的任务
 * @param {} params
 * @returns
 */
export function updateTaskGantt(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_update_task_gantt',
    postdate: params,
  })
}

/**
 * 点击查看人员详情
 * @param {} params
 * @returns
 */
export function ptResourseStaff(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_resourse_staff',
    ...params,
  })
}

