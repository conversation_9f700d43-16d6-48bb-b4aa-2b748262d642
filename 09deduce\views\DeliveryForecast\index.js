import { useDownloadAndUpload } from '../../hooks/useDownloadAndUpload.js'

/**
 * @description 
 * <AUTHOR>
 */
const { ref } = Vue
const { genFileId } = ElementPlus
const PerformanceView = {
  setup() {
    const upload = ref(null)
    const fileList = ref([])
    const handleExceed = (files) => {
      upload.value?.clearFiles()
      const file = files[0]
      file.uid = genFileId()
      upload.value?.handleStart(file)
    }
    const { downloadTemplate, uploadXlsx } = useDownloadAndUpload()
    const map = {
      'Delivery Month': 'str_delivery_month',
      'SV Type': 'str_sv_type',
      'WS Type': 'str_ws_type',
      'TAT Start': 'dt_tat_start',
      'Induction Month':'str_induction_month',
      'Release Plan':'dt_release_plan',
      'FLOW': 'str_flow',
      'ENGINE MODEL':'str_engine_model'
    }
    const submitUpload = () => {
      uploadXlsx(upload, fileList, map, 'de_importforecast')
    }

    // * 下载模板
    const handleDownloadTemplate = () => {
      // 使用XLXS.js生成模板
      const data = [
        ['Delivery Month', 'SV Type', 'WS Type', 'TAT Start','Induction Month','Release Plan', 'FLOW','ENGINE MODEL'],
        ['2024-01','4. SV Light','SV','2023-9-22','2023-09','2024-01-03','Finish','CFM56']
        
      ]
      downloadTemplate(data, '交付预测模拟表导入模板.xlsx')
    }

    return {
      upload,
      fileList,
      handleExceed,
      submitUpload,
      handleDownloadTemplate,
    }
  },
  template: /*html*/ `
    <el-upload
      ref="upload"
      class="m-3"
      v-model:file-list="fileList"
      :limit="1"
      :on-exceed="handleExceed"
      :auto-upload="false"
      accpet=".xls,.xlsx"
    >
      <template #trigger>
        <el-button type="primary">select file</el-button>
      </template>  
      <el-button class="ml-3" type="success" @click="submitUpload">upload to server</el-button>
      <el-button class="ml-3" type="warning" @click="handleDownloadTemplate">download template</el-button>
      <template #tip>
        <div class="text-sm italic">only support xls/xlsx file</div>
      </template>
    </el-upload>
  `,
}

export default PerformanceView
