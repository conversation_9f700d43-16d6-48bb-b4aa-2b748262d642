const { ref, shallowRef, reactive } = Vue
export function useLine() {
  const chartRef = ref(null)
  const myChart = shallowRef(null)

  // 初始化图表配置
  const chartOption = reactive({
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      show: false,
      textStyle: {
        color: '#fff',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: [],
      axisLabel: {
        color: '#fff',
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#fff',
      },
    },
    series: [
      {
        name: 'P80交付周期',
        type: 'line',
        data: [],
        symbol: 'circle',
        symbolSize: 20,
        // 颜色为黄色
        itemStyle: {
          color: '#FFD700',
        },
      },
    ],
  })

  const initChart = () => {
    myChart.value = echarts.init(chartRef.value)
  }
  const handleResize = () => {
    myChart.value.resize()
  }

  return {
    chartRef,
    chartOption,
    initChart,
    myChart,
  }
}
