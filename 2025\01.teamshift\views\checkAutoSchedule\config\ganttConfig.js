const weekScaleTemplate = (date) => {
  const dateToStr = gantt.date.date_to_str('%l, %F %d')
  const endDate = gantt.date.add(date, 7 - date.getDay(), 'day')
  return dateToStr(date) + ' - ' + dateToStr(endDate)
}

export const ganttConfig = {
  columns: [
    { name: 'text', label: 'Task name', tree: true, width: '*', resize: true },
    { name: 'start_date', label: 'Start time', align: 'center', width: 120, resize: true },
    {
      name: 'end_date',
      label: 'End time',
      align: 'center',
      width: 120,
      resize: true,
    },
    { name: 'duration', label: 'Duration(h)', align: 'center', width: 80, resize: true },
  ],
  // 基本时间刻度配置，您可以根据需求调整以匹配图片中的 "早/晚" 等复杂表头
  scales: [
    { unit: 'month', step: 1, format: '%F, %Y' },
    { unit: 'week', step: 1, format: weekScaleTemplate },
    { unit: 'day', step: 1, format: '%D' },
    { unit: 'hour', step: 1, format: '%H:%i' },
  ],
  // 启用行高亮
  highlight_critical_path: true,
  // 显示任务间的连线
  show_links: true,
  // 默认行高
  row_height: 40,
  // 刻度高度
  scale_height: 80,
  grid_width: 600,
  date_format: '%Y-%m-%d',
  duration_unit: 'hour',
}

// 添加甘特图模板配置
export const ganttTemplates = {
  // 修改结束日期显示，使得当开始和结束日期相同时，仍然正确显示为同一天
  task_end_date: function (date) {
    return gantt.templates.task_date(new Date(date.valueOf() - 1))
  },

  // 修改网格日期格式
  grid_date_format: function (date, column) {
    const gridDateToStr = gantt.date.date_to_str('%Y-%m-%d')
    if (column === 'end_date') {
      return gridDateToStr(new Date(date.valueOf() - 1))
    } else {
      return gridDateToStr(date)
    }
  },
  // 任务样式
  task_style: function (start, end, task) {
    const taskStyle = ['gantt-task']
    // 过程检验
    if (task.int_check_type === 0) {
      taskStyle.push('gantt-process-check')
    }
    // 放行检验
    if (task.int_check_type === 1) {
      taskStyle.push('gantt-release-check')
    }
    return taskStyle.join(' ')
  },
}
