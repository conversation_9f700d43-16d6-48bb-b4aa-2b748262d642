Vue.component('y-f4-1-shift-page-summary-edit', {
  props: [
    'str_flow',
    'input_id_wo',
    'input_plan_date',
    'input_id_main',
    'input_str_flow',
    'input_str_esn_type',
    'is_edit_page',
    'input_group_type',
  ],
  data: function () {
    return {
      str_flow_now: this.input_str_flow, // 当前进入的Flow
      str_esn: '', // 发动机编号
      loading: false, // 列表加载动画
      isteamplanshow: false, // 添加页面显示
      id_main: '',
      task_type: '',
      searchParams: { dt_date: '' }, // 筛选条件
      dialogVisible: false,
      form: {},
      height: 0,
      scrollTop: 0,
      radio: 1,
      datas: [],
      savedatas: [],
      checked: false,
      open: false,
      dialogVisibledata: '',
      radio: '',
      checkboxGroup: [],
      ischeckcolumn: '',
      tabledate: [],
      tableviewData: [],
      esnlist: [],
      typelist: [
        { type_code: 'Core', str_name: 'Core' },
        { type_code: 'LPT', str_name: 'LPT' },
        { type_code: 'FAN', str_name: 'FAN' },
        { type_code: 'B1', str_name: 'B1' },
      ],
      teamlist: [],
      search: {
        date: [],
        beginDate: '',
        endDate: '',
      },
      pickerOptions: {
        shortcuts: [
          {
            text: 'In the latest week',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: 'Last Month',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: 'Last 3 months',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
      title: '添加',

      columns_config: [],
      tableData: [],
      isteamplanshow: false,
      addDate: '',
      addIdwo: '',
      showtable: true,
      id_main: '', // 计划主数据ID
      input_is_edit: false,
      input_is_editsee: false,
      isteamplanshowsee: false,

      isteamplanshowvg: false,
      isteamplanshowvg1: false,
      input_is_editvg: false,
      vgday: '',
      input_vg_plan_date_end: '',
      input_vg_plan_date_start: '',
      input_vg_plan_date: '',
      team_id_info: null,
      reachtable: true,
      esnfilter: [],
      rulesformPartA: {
        // form1 验证
        reason: [
          {
            required: true,
            message: 'Please select  reason',
            trigger: 'blur',
          },
          //其他验证设置...
          // { validator: deptRule, trigger: 'blur' },
        ],
      },
      is_show_inspator_suport: false, // 发动机下 检验人员信息显示
      show_inspator_suport_info: {}, // 发动机下 检验人员信息
      tableheight: 475,
      id_site: '',
    }
  },

  mounted: function () {
    this.loadList()
    let m1 = this.str_flow_now
    this.$nextTick(() => {
      this.tableheight = window.innerHeight - this.$refs.xTable.$el.offsetTop - 8
      let self = this
      window.onresize = function () {
        self.tableheight = window.innerHeight - self.$refs.xTable.$el.offsetTop - 8
      }
    })
  },
  created: function () {
    this.searchParams.dt_date = [AddDays(0), AddDays(14)]
    this.init_esn()
    this.init_teams()
  },
  methods: {
    /**
     * 判断是否在试车区间内
     */
    is_between_test_start_and_test_end(row, title) {
      return moment(title).isBetween(row.test_start, row.test_end, undefined, '[]')
    },
    /** 查看*/
    boxview(row, plan) {
      let _this = this
      // _this.addDate = date
      _this.isteamplanshowsee = true
      _this.id_main = plan.planId
    },
    /**查询*/
    query() {
      let _this = this
      //如果查询条件中有发动机号，则根据发动机号先查询出发动机的分配区间，取发动机分配的最小值为start ,最大值为end
      if (_this.searchParams.id_wo != null && _this.searchParams.id_wo != '') {
        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_get_engine_allot_date',
            str_flow: _this.input_str_flow,
            str_engine_type: _this.input_str_esn_type,
            id_wo: _this.searchParams.id_wo,
          })
          .then(function (response) {
            _this.searchParams.dt_date = [response.data.data.startdate, response.data.data.enddate]
            _this.loadList()
          })
          .catch(function (error) {
            console.log(error)
          })
      } else {
        _this.loadList()
      }
    },
    /** 初始化表头*/
    inint_columns() {
      let _this = this
      _this.columns_config = []
      return new Promise((resolve) => {
        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_get_summary_head',
            str_flow: _this.input_str_flow,
            start_date: (_this.searchParams.dt_date && _this.searchParams.dt_date[0]) || '', //getNowDay(),
            end_date: (_this.searchParams.dt_date && _this.searchParams.dt_date[1]) || '',
          })
          .then(function (response) {
            _this.columns_config =
              response.data.data &&
              response.data.data.map((item) => {
                return {
                  title_0: item.onduty,
                  title_g_0: item.mesleave,
                  title_vg_0: item.grind,
                  title_vg_1: item.dailydata,
                  title_g: item.str_week,
                  title: item.day,
                  field: 'attr2',
                }
              })

            resolve(_this.columns_config)
          })
          .catch(function (error) {
            console.log(error)
          })
      })
    },
    /** 重新加载*/
    loadList() {
      let _this = this

      this.loading = true

      _this.inint_columns().then((colums_t) => {
        _this.mockList().then((data) => {
          // 使用函数式加载，阻断 vue 对大数据的监听
          const xTable = this.$refs.xTable
          const startTime = Date.now()
          if (xTable) {
            _this.esnfilter = []
            data.forEach(function (value, index) {
              _this.esnfilter.push({ label: value.esn, value: value.esn })
            })

            _this.esnfilter = JSON.stringify(_this.esnfilter)

            this.$refs.xTable.reloadData(data).then(() => {
              // console.log(_this.esnfilter )
              // VXETable.modal.message({ content: `渲染 ${size} 行，用时 ${Date.now() - startTime}毫秒`, status: 'info' })
              _this.reachtable = false
              this.loading = false
              _this.reachtable = true
            })
          }
        })
      })
    },
    /**加载数据*/
    mockList() {
      let _this = this
      return new Promise((resolve) => {
        let list = []
        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_get_summary_engine_list',
            str_flow: _this.input_str_flow,
            start_date: (_this.searchParams.dt_date && _this.searchParams.dt_date[0]) || '', //getNowDay(),
            end_date: (_this.searchParams.dt_date && _this.searchParams.dt_date[1]) || '',
            id_wo: _this.searchParams.id_wo,
            str_type: _this.searchParams.type_code,
            id_team: _this.searchParams.id_team,
            str_esn_type: _this.searchParams.engint_type, // 机型
            str_group_type: _this.input_group_type,
          })
          .then(function (response) {
            list = (response.data.data && response.data.data) || []
            resolve(list)
          })
          .catch(function (error) {
            console.log(error)
          })
      })
    },
    /**查询所有的已排班发动机 */
    init_teams() {
      let _this = this
      axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_teams',
        })
        .then(function (response) {
          _this.teamlist = response.data.data.data
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    init_esn() {
      let _this = this
      axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_summary_esn',
          str_flow: _this.input_str_flow,
          str_esn_type: _this.input_str_esn_type,
        })
        .then(function (response) {
          _this.esnlist = response.data.data
        })
        .catch(function (error) {
          console.log(error)
        })
    },

    /** 关闭弹窗*/
    close_dialog() {
      let _this = this
      _this.isteamplanshow = false
      _this.id_main = ''
      _this.query()
    },

    /** 处理数组成字符串 */
    exe_str(data) {
      return data.join(',')
    },

    getteamstaff(data) {
      let name = []
      for (let item of data) {
        name.push(item.staff_name)
      }
      return name.toString()
    },

    //判断 开始/结束时间变化，要标记
    changestatus({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex }) {
      if (row.changestatus == '1') {
        return 'bgPurple'
      }
    },
    Isweek({ $rowIndex, column, columnIndex, $columnIndex }) {
      if (column.title == '星期六' || column.title == '星期日') {
        return 'bgDust'
      } else if (
        $rowIndex == 1 &&
        this.columns_config[$columnIndex - 1] != null &&
        this.columns_config[$columnIndex - 1].title_vg_0.vgfullflag == '1'
      ) {
        return 'header-cell'
      } else if (
        $rowIndex == 2 &&
        this.columns_config[$columnIndex - 1] != null &&
        this.columns_config[$columnIndex - 1].title_vg_0.hsgfullflag == '1'
      ) {
        return 'header-cell'
      } else {
        return 'bghead'
      }
    },
    //判断状态
    activeclor({ row, column, rowIndex, columnIndex }) {
      let _this = this
      const startTime = new Date(_this.searchParams.dt_date[0]).getTime()
      const endTime = new Date(_this.searchParams.dt_date[1]).getTime()
      let border_css = '' // 标记红色边框
      let cell_css = '' // 标记单元格颜色
      // 是否需要变色内容
      if (column.type == 'Isdata') {
        // 结束日期
        let endday = new Date(row.end_date).getTime()
        // 列日期
        let colday = new Date(column.title).getTime()
        // 开始日期
        let startday = new Date(row.start_date).getTime()
        // pp开始时间
        let pStartDay = new Date(row.project_start).getTime()
        // pp结束时间
        let pEndDay = new Date(row.project_end).getTime()
        // 结束后一天
        let Nextendday = new Date(row.end_date).getTime() + 3600 * 1000 * 24 * 1

        if (colday === endday && endday === startday) {
          border_css = '  border-red-all' // 只有一格的是时候
        } else if (colday === startday || (colday > startday && colday == startTime)) {
          border_css = '  border-red-first'
        } else if (colday === endday || (colday === endTime && colday <= endday)) {
          border_css = '  border-red-end'
        } else if (startday < colday && colday < endday) {
          border_css = '  border-red-other'
        }

        // 日期再PP 计划之外
        if (colday >= pStartDay && pEndDay >= colday) {
          let plan_t = row.plan.find((x) => x.plan_date == column.title)
          if (plan_t && plan_t.task.length > 0) {
            if (plan_t.fedbackstatus == '0') {
              cell_css = 'bgblue'
            } else if (plan_t.fedbackstatus == '1') {
              cell_css = 'bgSuccess'
            } else if (plan_t.fedbackstatus == '-1') {
              cell_css = 'bgWarning'
            } else if (plan_t.fedbackstatus == '-2') {
              cell_css = 'bgDanger'
            } else {
              cell_css = 'bginfoblue'
            }
          } else {
            cell_css = 'bginfoblue'
          }
        } else {
          let plan_t = row.plan.find((x) => x.plan_date == column.title)
          if (plan_t && plan_t.task.length > 0) {
            if (plan_t.fedbackstatus == '0') {
              cell_css = 'bgblue'
            } else if (plan_t.fedbackstatus == '1') {
              cell_css = 'bgSuccess'
            } else if (plan_t.fedbackstatus == '-1') {
              cell_css = 'bgWarning'
            } else if (plan_t.fedbackstatus == '-2') {
              cell_css = 'bgDanger'
            } else {
              cell_css = 'bgInfo'
            }
          } else {
            cell_css = 'bgInfo'
          }
        }
      } else {
        cell_css = 'bgInfo'
      }
      return cell_css + border_css
    },
    /**数组转 字符串 */
    exe_str(data) {
      return data.join(',')
    },

    /**打开发动机下 支持人员 */
    open_is_show_inspator_suport(data) {
      let _this = this
      _this.is_show_inspator_suport = true
      if (data.inspector == null) {
        data.inspector = ''
      }
      _this.show_inspator_suport_info = data
    },
    close_is_show_inspator_suport() {
      let _this = this
      _this.is_show_inspator_suport = false
    },
    changeMoXue(coldata, nowday) {
      let _this = this
      _this.isteamplanshowvg = true
      _this.id_main = coldata

      _this.input_vg_plan_date_end = _this.searchParams.dt_date[1]
      _this.input_vg_plan_date_start = _this.searchParams.dt_date[0]
      _this.input_vg_plan_date = nowday
    },
    changeVg(coldata, nowday) {
      let _this = this
      _this.is_show_vg = true
      _this.changeMoXue(coldata, nowday)
    },
    changeHsg(coldata, nowday) {
      let _this = this
      _this.is_show_vg = false
      _this.changeMoXue(coldata, nowday)
    },
    get_back_close_vg_dialog() {
      let _this = this
      _this.isteamplanshowvg = false
      _this.isteamplanshowvg1 = false
      _this.id_main = ''
      _this.query()
    },
    /**关闭编辑页 */
    close_edit_dialog() {
      let _this = this
      _this.isteamplanshow = false
      _this.id_main = ''
      _this.team_id_info = ''
      _this.query()
    },
    close_self_dialog() {
      let _this = this
      _this.isteamplanshow = false
      _this.id_main = ''
      _this.addDate = ''
      _this.addIdWo = ''
      _this.team_id_info = ''
      _this.str_esn = ''
      _this.esn_data_end = ''
    },

    /** 编辑*/
    boxedit(row, plan, date) {
      let _this = this
      _this.addDate = date
      _this.addIdwo = row.id_wo
      _this.isteamplanshow = true
      _this.id_main = plan.planId
      _this.str_esn = row.esn
      _this.esn_data_end = row.end_date
      _this.input_is_edit = 'true'
      _this.task_type = row.str_type
    },
    /**判断是不是当天日期 当天之前不能编辑 */
    is_now_date(row, dataitem, title) {
      let new_date = getNowDay()
      return title >= new_date
    },
    //添加
    addPlan(date, idwo, row) {
      let _this = this
      _this.addDate = date
      _this.addIdwo = idwo
      _this.team_id_info = row.id_team
      _this.task_type = row.str_type
      _this.str_esn = row.esn
      _this.id_site = row.id_site
      _this.esn_data_end = row.end_date
      _this.isteamplanshow = true
    },
    Endstaff(colday, endday) {
      // 结束日期
      let newEndday = new Date(endday).getTime()
      // 列日期
      let newColday = new Date(colday).getTime()
      // 结束后一天
      let Nextendday = new Date(endday).getTime() + 3600 * 1000 * 24 * 1

      if (newEndday >= newColday) {
        return '0'
      } else if (Nextendday == newColday) {
        return '1'
      }
    },
  },
  template: /*html*/ `
    <div>
      <!--	<el-row>-->
      <el-form
        ref="searchForm"
        :model="searchParams"
        label-width="auto"
        class="demo-form-inline"
        label-position="right"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="Date:">
              <el-date-picker
                style="width: 100%"
                clearable
                type="daterange"
                value-format="yyyy-MM-dd"
                v-model="searchParams.dt_date"
                start-placeholder="起始日期"
                end-placeholder="截止日期"
                size="small"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="ESN:">
              <el-select clearable v-model="searchParams.id_wo" filterable size="small">
                 
                <el-option v-for="item in esnlist" :key="item.id_wo" :label="item.esn" :value="item.id_wo"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="Group:">
              <el-select clearable v-model="searchParams.type_code" size="small">
                 
                <el-option
                  v-for="item in typelist"
                  :key="item.type_code"
                  :label="item.str_name"
                  :value="item.type_code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="Engine Type:">
              <el-select clearable v-model="searchParams.engint_type" size="small">                 
                <el-option label="CFM56" value="CFM56"></el-option>
                <el-option label="LEAP" value="LEAP"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="Team:">
              <el-select clearable v-model="searchParams.id_team" size="small">
                 
                <el-option v-for="item in teamlist" :key="item.id" :label="item.str_name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="1">
            <el-form-item>
              <el-button type="primary" size="small" @click="query" class="el-icon-search"> </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!--	</el-row>-->

      <vxe-table
        border
        ref="xTable"
        tooltip-effect="dark"
        v-if="reachtable"
        :header-cell-class-name="Isweek"
        :header-cell-style="{color:'#fff',border:'0.01rem solid #fff'}"
        :max-height="tableheight"
        :cell-class-name="activeclor"
        :row-config="{height: 150}"
        :column-config="{resizable: true,width:160}"
        :loading="loading"
      >
        <vxe-colgroup fixed="left">
          <template #header="{}">
            <el-row> <span>RUE/RUMC/RULA/RUQA值班人员 </span></el-row>
          </template>
          <vxe-colgroup title="磨削(VG)">
            <vxe-colgroup fixed="left">
              <template #header="{}">
                <el-row> <span>磨削(HSG) </span></el-row>
              </template>
              <vxe-column type="seq" title="序号" width="40" align="center"></vxe-column>
              <vxe-column title="Team" field="str_team" align="center" width="60">
                <template v-slot:default="{ row,column,rowKey,columnKey }">
                  <el-row class="headtitle newheadtitle">
                    <span v-if="row.str_team" style="font-size: 12px;">{{row.str_team.split('(')[0]}}</span>
                  </el-row>
                  <el-row class="headtitle newheadtitle">
                    <span v-if="row.str_team" style="font-size: 12px;">({{row.str_team.split('(')[1]}}</span>
                  </el-row>
                </template>
              </vxe-column>

              <vxe-column title="Type" field="str_type" align="center" width="52"> </vxe-column>
              <vxe-column field="esn" title="ESN" width="100" :class-name="changestatus" align="center">
                <template v-slot:default="{ row,column,rowKey,columnKey }">
                  <el-row class="headtitle newheadtitle">
                    <span
                      style="font-size: 14px;
							font-weight: 600"
                      >{{row.esn}}</span
                    >
                  </el-row>
                  <el-row class="boxBottom">
                    <!-- 发动机下 操作区域 -->
                    <el-col :span="12" class="span_btn">
                      <span title="查看支持检验" @click="open_is_show_inspator_suport(row)"
                        ><i class="el-icon-view self_icon_color_default"></i
                      ></span>
                    </el-col>
                  </el-row>
                </template>
              </vxe-column>
              <vxe-column title="Start Date" field="start_date" align="center" width="85">
                <template v-slot:default="{ row,column,rowKey,columnKey }">
                  <span>{{row.start_date}}</span>
                  <el-divider></el-divider>
                  <span>{{row.project_start}}</span>
                </template>
              </vxe-column>
              <vxe-column title="End Date" field="end_date" align="center" width="80">
                <template v-slot:default="{ row,column,rowKey,columnKey }">
                  <span>{{row.end_date}}</span>
                  <el-divider></el-divider>
                  <span>{{row.project_end}}</span>
                </template>
              </vxe-column>
              <vxe-column title="TAT" field="tat" align="center" width="45"> </vxe-column>
            </vxe-colgroup>
          </vxe-colgroup>
        </vxe-colgroup>
        <vxe-colgroup v-for="(column_config ,index) in columns_config" :key="index" show-header-overflow>
          <template #header="{}">
            <el-row>
              <div class="headtitle" v-for="(item_title_0,index) in column_config.title_0">
                <span>{{item_title_0 }}</span>
              </div>
            </el-row>
          </template>
          <vxe-colgroup>
            <template #header="{}">
              <!--磨削(VG)-->
              <i
                v-if='column_config.title_vg_0.is_vg === "0"'
                class="el-icon-edit"
                @click="changeVg('',column_config.title)"
              ></i>
              <div
                class="headtitle"
                style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                v-for="(item,index) in column_config.title_vg_0.gingdata"
              >
                <i
                  style="cursor: pointer"
                  v-if='item.sm.filter(f => f.str_type === "VG").length > 0 && item.is_hide_edit=="0"'
                  class="el-icon-edit"
                  @click="changeVg(item.id_main,column_config.title)"
                ></i>
                <!-- 如果有数据并且可编辑就显示编辑按钮，有数据不可编辑 显示添加按钮 自己添加完成之后 添加按钮变成可编辑按钮-->
                <!-- <i v-else-if='!column_config.title_vg_0.is_new_flag' class="el-icon-edit" @click="changeVg('',column_config.title)"></i>-->
                <span
                  :title="item.sm.filter(f => f.str_type === 'VG').map(m => m.str_sm).join(',')"
                  v-if="item.sm.filter(f => f.str_type === 'VG').length > 0"
                  >{{item.str_esn }}{{item.str_team}}:
                </span>
                <span v-if='item.sm.filter(f => f.str_type === "VG").length > 0'>
                  {{item.sm.filter(f => f.str_type === "VG").map(m => m.str_sm).join(',')}}
                </span>
              </div>
            </template>
            <vxe-colgroup>
              <template #header="{}">
                <!--磨削(HSG)-->
                <i
                  v-if='column_config.title_vg_0.is_hsg === "0"'
                  class="el-icon-edit"
                  @click="changeHsg('',column_config.title)"
                ></i>
                <div
                  class="headtitle"
                  style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                  v-for="(item,index) in column_config.title_vg_0.gingdata"
                >
                  <i
                    style="cursor: pointer"
                    v-if='item.sm.filter(f => f.str_type === "HSG").length > 0 && item.is_hide_edit=="0"'
                    class="el-icon-edit"
                    @click="changeHsg(item.id_main,column_config.title)"
                  ></i>
                  <!--   <i v-else-if='!column_config.title_vg_0.is_new_flag' class="el-icon-edit" @click="changeHsg('',column_config.title)"></i>-->
                  <span
                    :title="item.sm.filter(f => f.str_type === 'HSG').map(m => m.str_sm).join(',')"
                    v-if="item.sm.filter(f => f.str_type === 'HSG').length > 0"
                    >{{item.str_esn }}{{item.str_team}}:</span
                  >
                  <span v-if='item.sm.filter(f => f.str_type === "HSG").length > 0'>
                    {{item.sm.filter(f => f.str_type === "HSG").map(m => m.str_sm).join(',')}}
                  </span>
                </div>
              </template>
              <vxe-colgroup :title="column_config.title_g">
                <vxe-column type="Isdata" :title="column_config.title">
                  <template #default="{  row  }">
                    <div class="wrapbox">
                      <!-- 组件内容 -->
                      <div v-for="(dataitem,index) in row.plan" :key="dataitem.plan_date + index">
                        <!-- 绑定每日数据 有数据 -->
                        <div
                          class="thbox"
                          v-if="dataitem.plan_date == column_config.title"
                          key="dataitem.plan_date + index +"
                        >
                          <!-- 有数据 -->
                          <div v-if="dataitem.task.length>0" :key="'A'+index">
                            <!-- task 包区域 -->
                            <div class="taskgroup" v-for="(task,index) in dataitem.task">
                              <div class="taskitem">
                                <!-- <span class='taskname'>{{task.taskname}}</span> -->

                                <el-popover placement="top-start" width="70" trigger="hover" :content="task.taskname">
                                  <span slot="reference" style="width:70px" class="taskname omit_line"
                                    >{{ task.taskname}}</span
                                  >
                                </el-popover>

                                <el-popover
                                  placement="top-start"
                                  width="70"
                                  trigger="hover"
                                  :content="getteamstaff(task.teamstaff)"
                                >
                                  <span slot="reference" style="width:70px" class="omit_line"
                                    >{{ getteamstaff(task.teamstaff)}}{{ getteamstaff(task.sectionstaff)}}</span
                                  >
                                </el-popover>
                              </div>
                            </div>

                            <!-- 操作区域 -->
                            <div class="boxBottom">
                              <el-col :span="8" class="span_btn">
                                <span @click="boxview(row,dataitem)">
                                  <i class="el-icon-view"></i>
                                </span>
                              </el-col>
                              <el-col :span="8" class="span_btn" v-if="is_edit_page">
                                <span
                                  @click="boxedit(row,dataitem,column_config.title)"
                                  v-if="is_now_date(row,dataitem,column_config.title)"
                                >
                                  <i class="el-icon-edit"></i>
                                </span>
                                <span v-else> &nbsp;</span>
                              </el-col>
                            </div>
                          </div>
                          <!-- 无数据 -->
                          <div class="addbox" v-if="dataitem.task.length==0 " :key="'B'+index">
                            <!-- 如果在test_start 和test_end 之间 展示试车	 -->
                            <div v-if="is_between_test_start_and_test_end(row, column_config.title)">试车</div>
                            <!-- 不超过最终日期 -->
                            <div v-else>
                              <div v-if="Endstaff(column_config.title, row.end_date) == '0' && is_edit_page" :key="'C'+index">
                                <el-button
                                  type="primary"
                                  size="small"
                                  @click="addPlan(column_config.title,row.id_wo,row)"
                                  icon="el-icon-circle-plus-outline"
                                  >add</el-button
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </vxe-column>
              </vxe-colgroup>
            </vxe-colgroup>
          </vxe-colgroup>
        </vxe-colgroup>
      </vxe-table>

      <div v-if="isteamplanshow">
        <y-common-team-plan-form
          @get_back_close_dialog="close_edit_dialog()"
          @close_dialog="close_self_dialog"
          :input_plan_date="addDate"
          :input_id_wo="addIdwo"
          :input_id_main="id_main"
          :input_is_edit="input_is_edit"
          :str_flow="input_str_flow"
          :input_str_esn="str_esn"
          :input_esn_date_end="esn_data_end"
          :str_en_type="input_str_esn_type"
          :input_id_team="team_id_info"
          :task_type="task_type"
          :id_site="id_site"
          :input_group_type="input_group_type"
        ></y-common-team-plan-form>
      </div>
      <div v-if="isteamplanshowsee">
        <y-common-team-plan-form-see
          @get_back_close_dialog="isteamplanshowsee=false"
          :input_id_main="id_main"
          :input_is_editsee="input_is_editsee"
        ></y-common-team-plan-form-see>
      </div>
      <div v-if="isteamplanshowvg">
        <y-vg-hsg-sm-edit
          :is_show_vg="is_show_vg"
          @get_back_close_vg_dialog="get_back_close_vg_dialog()"
          :input_vg_plan_date="input_vg_plan_date"
          :input_vg_plan_date_end="input_vg_plan_date_end"
          :input_vg_plan_date_start="input_vg_plan_date_start"
          :input_id_main="id_main"
          :input_str_flow="str_flow_now"
        ></y-vg-hsg-sm-edit>
      </div>
      <!--检验支持人员信息-->
      <el-dialog :visible.sync="is_show_inspator_suport" class="self_dialog" width="30%">
        <el-card class="box-card">
          <div slot="header" class="clearfix" style="font-size: 16px;">
            <span>检验、支持人员 Inspector Supporter</span>
          </div>

          <div class="text item" style="padding: 5px 0;font-size: 14px;">
            <span style="color: #5b8ff9;">{{'检验:' +"&nbsp;&nbsp;"}}</span> {{"&nbsp;&nbsp;"+
            show_inspator_suport_info.inspector}}
          </div>

          <template v-for="o in  show_inspator_suport_info.supportor">
            <div class="text item" style="padding: 5px 0;font-size: 14px;">
              <span style="color: #5b8ff9;">{{o.str_type +"&nbsp;&nbsp;"}}</span> {{o.staffnames}}
            </div>
          </template>
        </el-card>
        <div slot="footer">
          <el-button
            class="topButton_right"
            style="margin-left:20px;"
            size="small"
            type="danger"
            @click="close_is_show_inspator_suport()"
            >Close
          </el-button>
        </div>
      </el-dialog>
    </div>
  `,
})
