const { h, onMounted, ref } = Vue
const { VxeColumn, VxeColgroup } = VXETable
import { getHandoverTableColumns } from '../api/index.js'

// 表格列配置
export function useTableColumns() {
  const allFilters = [{ data: '' }]
  // 基础列配置 - 动态获取
  const baseColumns = ref([])

  // 分组配置 - 动态获取
  const colgroups = ref([])

  // 获取表格列配置
  const getTableColumns = async () => {
    try {
      const res = await getHandoverTableColumns()

      // 处理API返回的包装格式
      const data = res

      // 分离基础列和分组列
      baseColumns.value = data
        .filter((item) => !item.items || item.items.length === 0)
        .map((col) => ({
          title: col.title,
          field: col.fields, // 注意API返回的是 fields 而不是 field
          sortable: true,
        }))

      // 处理分组列 - 这些是有 items 的列
      const groupItems = data.filter((item) => item.items && item.items.length > 0)

      colgroups.value = groupItems.map((group) => ({
        title: group.title,
        field: group.fields,
        items: group.items.map((subGroup) => ({
          title: subGroup.title,
          field: subGroup.fields,
          sortable: false,
        })),
      }))
    } catch (error) {
      console.error('获取表格列配置失败:', error)
    }
  }

  onMounted(() => {
    getTableColumns()
  })

  return {
    allFilters,
    baseColumns,
    colgroups,
    getTableColumns,
  }
}

// 列渲染工厂
export function useColumnRenderer(openDrawer) {
  const allFilters = [{ data: '' }]

  const handleBaseCellClick = (row, column) => {
    openDrawer({ row, column })
  }

  const handleGroupCellClick = (row, processField, shiftField, idx) => {
    const category = row[processField]?.[shiftField]?.[idx]?.str_title || ''
    openDrawer({ row, column: { str_engine_type: processField, str_shift: shiftField, title: category, idx } }, true)
  }

  // 公共列配置
  const createColumn = (config) =>
    h(
      VxeColumn,
      {
        filters: allFilters,
        'filter-render': { name: 'FilterInput' },
        sortable: true,
        width: 100,
        ...config,
        fixed: config.field === 'str_flow' || config.field === 'dt_date' ? 'left' : '',
      },
      {
        default: ({ row, column }) => {
          if (column.field === 'str_category') {
            return renderCategoryCell(row, column.field)
          }
          // 不需要点击事件
          if (column.field === 'str_flow' || column.field === 'dispatch_target' || column.field === 'dt_date') {
            return h('div', {}, [row[column.field]])
          }
          return h(
            'div',
            { class: 'hover:cursor-pointer hover:underline', onClick: () => handleBaseCellClick(row, column) },
            [row[column.field]],
          )
        },
      },
    )

  // 创建分组列 - 支持多层嵌套
  const createColgroup = (title, field, items) => {
    return h(
      VxeColgroup,
      { title, field },
      {
        default: () =>
          items.map((item) =>
            h(
              VxeColumn,
              {
                title: item.title,
                field: `${field}_${item.field}`, // 组合字段名
                width: 80,
                sortable: false,
              },
              {
                default: ({ row }) => {
                  return renderShiftCell(row, field, item.field)
                },
              },
            ),
          ),
      },
    )
  }

  // 渲染班次单元格 - 显示多个数据项，每个一行
  const renderShiftCell = (row, processField, shiftField) => {
    // 获取班次数据
    const shiftData = row[processField]?.[shiftField] || []

    // 创建每个数据项的显示行
    const dataRows = shiftData.map((item, idx) => {
      const value = item?.key_num
      const isLastItem = idx === shiftData.length - 1
      // 是否可点击
      const isClickable = item?.str_title !== '调度目标'

      return h(
        'div',
        {
          key: `${processField}-${shiftField}-${idx}`,
          class: `flex items-center justify-center py-1 px-2 ${!isLastItem ? 'border-b border-gray-200' : ''}`,
        },
        [
          h(
            'span',
            {
              class: `font-semibold text-center ${isClickable ? 'hover:cursor-pointer hover:underline' : ''}`,
              onClick: () => isClickable && handleGroupCellClick(row, processField, shiftField, idx),
            },
            value !== null && value !== undefined ? value : '-',
          ),
        ],
      )
    })

    // 创建容器
    const container = h(
      'div',
      {
        class: 'flex flex-col justify-center border border-gray-200',
      },
      [...dataRows],
    )

    return container
  }

  const renderCategoryCell = (row, field) => {
    return h('div', { class: 'flex flex-col justify-center border border-gray-200' }, [
      row[field].map((item, idx) =>
        h(
          'span',
          {
            class: `text-gray-700 py-1 px-2 flex-shrink-0 ${idx === row[field].length - 1 ? '' : 'border-b border-gray-200'}`,
          },
          item,
        ),
      ),
    ])
  }

  // 创建序号列
  const createSeqColumn = () =>
    h(VxeColumn, {
      type: 'seq',
      title: '序号',
      width: '50',
      fixed: 'left',
    })

  return {
    createColumn,
    createColgroup,
    createSeqColumn,
    renderShiftCell,
  }
}
