const { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } = Vue
const { useIntervalFn } = VueUse

export default {
  name: 'CarouselTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    tableColumns: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      required: true
    },
    bgColor: {
      type: String,
      default: 'from-blue-500 to-blue-600'
    },
    autoScroll: {
      type: Boolean,
      default: true
    },
    scrollInterval: {
      type: Number,
      default: 2000
    }
  },
  setup(props) {
    const tableRef = ref(null)
    const currentScrollTop = ref(0)
    const maxScrollTop = ref(0)
    const isScrolling = ref(false)
    
    // 自动滚动功能 - 表格内容垂直滚动
    const { pause, resume } = useIntervalFn(() => {
      if (!props.autoScroll || !tableRef.value || isScrolling.value) return
      
      const tableBody = tableRef.value.$el.querySelector('.vxe-table--body-wrapper')
      if (!tableBody) return

      const scrollHeight = tableBody.scrollHeight
      const clientHeight = tableBody.clientHeight
      maxScrollTop.value = scrollHeight - clientHeight

      if (maxScrollTop.value <= 0) return

      isScrolling.value = true
      
      // 平滑滚动
      currentScrollTop.value += 30
      if (currentScrollTop.value >= maxScrollTop.value) {
        currentScrollTop.value = 0
      }
      
      // 使用平滑滚动
      tableBody.scrollTo({
        top: currentScrollTop.value,
        behavior: 'smooth'
      })
      
      // 滚动完成后重置状态
      setTimeout(() => {
        isScrolling.value = false
      }, 300)
    }, props.scrollInterval)

    onMounted(() => {
      if (props.autoScroll) {
        resume()
      }
    })

    onBeforeUnmount(() => {
      pause()
    })

    return {
      tableRef,
      tableData: computed(() => props.tableData)
    }
  },
  template: /*html*/ `
    <div class="carousel-table-container h-full w-full flex flex-col">
      <!-- 表格标题 -->
      <div :class="['table-header bg-gradient-to-r text-white p-4 rounded-t-lg flex justify-between items-center w-full', bgColor]">
        <h3 class="text-lg font-semibold">{{ title }}</h3>
        <div class="flex items-center space-x-2">
          <span class="text-sm opacity-80">共 {{ tableData.length }} 条数据</span>
        </div>
      </div>
      
      <!-- 表格内容区域 -->
      <div class="table-content bg-white flex-1 overflow-hidden relative w-full">
        <div class="table-wrapper h-full w-full">
          <vxe-table
            ref="tableRef"
            :data="tableData"
            border
            stripe
            height="100%"
            width="100%"
            :row-config="{ isHover: true, useKey: true }"
            :scroll-y="{ enabled: true, mode: 'wheel' }"
            :scroll-x="{ enabled: true }"
            :column-config="{ useKey: true, resizable: true }"
            class="modern-table"
            show-overflow="tooltip"
            :optimize="{ scrollX: { gt: 0 }, scrollY: { gt: 0 } }"
          >
            <vxe-column type="seq" width="60" fixed="left" title="#"></vxe-column>
            <vxe-column
              v-for="column in tableColumns"
              :key="column.field"
              :field="column.field"
              :title="column.title"
              :width="column.width"
              :min-width="column.minWidth || 120"
            >
              <template #default="{ row }" v-if="column.field === 'sequence'">
                <el-tag 
                  :type="row.sequence === '是' || row.sequence === '已验证' ? 'success' : 'warning'" 
                  size="small"
                >
                  {{ row.sequence }}
                </el-tag>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>
    </div>
  `
} 