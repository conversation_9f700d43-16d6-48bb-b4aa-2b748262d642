import { getWhiteToYellowData } from '../EngineSimulationPlan/api/index.js'
import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'
const { useVModel } = VueUse
const { ref, onMounted } = Vue

const DefaultSerialReturn = {
  components: {
    HtVxeTable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    idWo: {
      type: String,
      required: true,
    },
    type: {
      type: Number,
      default: 0,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)
    const tableData = ref([])
    const tableColumns = ref([
      {
        title: 'ONLOG清单',
        field: 'is_onlog',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '集件项',
        field: 'is_kit',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '集件状态',
        field: 'is_kit_dispname',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '工作指令',
        field: 'str_code',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '发动机号',
        field: 'str_esn',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '显示客户',
        field: 'str_client',
        minWidth: 120,
      },
      {
        title: '原台工作指令',
        field: 'str_code_ori',
        minWidth: 130,
      },
      {
        title: '原台ESN',
        field: 'str_esn_ori',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '件号',
        field: 'str_pn_do',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件名称',
        field: 'str_part_name',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '集件总数',
        field: 'int_qty',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '已集件数量',
        field: 'int_kit',
        minWidth: 120,
      },
      {
        title: '串件零件',
        field: 'id_parent_revoke',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '拆机零件',
        field: 'id_offlog',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
    ])
    const total = ref(0)
    const tableRef = ref(null)

    const getTableData = async () => {
      const { data } = await getWhiteToYellowData(props.idWo, props.type)
      if (data.code === 'success') {
        tableData.value = data.data
        total.value = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    const handleJump = () => {
      const selectedData = tableRef.value.getSelectedData()
      if (selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }
      const idList = selectedData.map((item) => item.id_wo).join(',')
      if (idList === '') {
        ElementPlus.ElMessage.warning('无串件零件信息')
        return
      }
      com.refreshTab('发动机传入件清单', `/Page/?moduleid=1700038720247447552&qrc_id=${idList}`)
    }

    onMounted(() => {
      getTableData()
    })

    return {
      visible,
      tableData,
      tableColumns,
      total,
      handleJump,
      tableRef,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="80%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex items-center justify-between">
          <div class="text-white">默认串回零件清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="flex items-center">
              <el-button type="primary" @click="handleJump">跳转发动机传入件清单</el-button>
            </div>
          </div>
          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable
          ref="tableRef"
          :tableData="tableData"
          :tableColumns="tableColumns"
          :isShowHeaderCheckbox="true"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
  `,
}

export default DefaultSerialReturn
