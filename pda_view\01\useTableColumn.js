export function useTableColumn() {
  const getTableColumn = () => {
    return [
      {
        showColumn: true,
        prop: 'int_day',
        label: 'P/#',
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_billcode',
        label: '定单号',
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      }, // 订单
      {
        showColumn: true,
        prop: 'str_ycode',
        label: '运单号',
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },// 运单号
      {
        showColumn: true,
        prop: 'dt_ssamc',
        label: '到达ssamc时间',
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },//到达ssamc时间
      {
        showColumn: true,
        prop: 'str_esn',
        label: 'ESN',
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      { showColumn: true, prop: 'int_pack', label: '箱数', field: [{ value: '' }], is_select: 'input' },
      {
        showColumn: true,
        prop: 'is_send_area',
        label: '送往区域',
        field: [{ value: '' }],
        is_select: 'input',
      },//送往区域
      {
        showColumn: true,
        prop: 'int_num',
        label: 'Qty',
        field: [{ value: '' }],
        is_select: 'input',
      },//送往区域
    ];
  };

  return {
    getTableColumn,
  };
}
