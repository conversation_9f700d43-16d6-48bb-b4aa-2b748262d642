import { WorkOrderSubmissionComponent } from './submission.js'
const { ref } = Vue
export const WorkOrderComponent = {
  components: {
    WorkOrderSubmissionComponent,
  },
  setup() {
    const createWorkOrder = () => {
      console.log(111)
    }

    const tableData = ref([])

    const addVisible = ref(false)
    /** 添加 */
    const handleAdd = () => {
      addVisible.value = true
    }

    return {
      createWorkOrder,
      tableData,
      handleAdd,
      addVisible,
    }
  },
  template: /*html*/ `
    <div class="flex h-screen flex-col bg-gray-300 p-4">
      <div class="space-x-4 rounded-lg bg-white p-4 shadow-sm">
        <button @click="createWorkOrder" class="rounded-md bg-amber-500 px-4 py-2 text-sm text-white hover:bg-blue-700">
          我的待办
        </button>
        <button @click="createWorkOrder" class="rounded-md bg-blue-500 px-4 py-2 text-sm text-white hover:bg-blue-700">
          我创建的
        </button>
      </div>
      <div class="mt-4 flex-1 justify-end rounded-lg bg-white p-4 shadow-sm">
        <button
          @click="handleAdd"
          class="float-right rounded-md bg-green-500 px-4 py-2 text-sm text-white hover:bg-blue-700"
        >
          Add
        </button>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="date" label="日期"></el-table-column>
          <el-table-column prop="name" label="姓名"></el-table-column>
          <el-table-column prop="address" label="地址"></el-table-column>
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button type="text" size="small">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 添加 -->
    <el-drawer
      class="my_drawer"
      title="添加"
      v-model="addVisible"
      @close="addVisible = false"
      size="80%"
      :append-to-body="true"
    >
      <work-order-submission-component></work-order-submission-component>
      <template #footer>
        <el-button @click="addVisible = false">取 消</el-button>
        <el-button type="primary">确 定</el-button>
      </template>
    </el-drawer>
  `,
}
