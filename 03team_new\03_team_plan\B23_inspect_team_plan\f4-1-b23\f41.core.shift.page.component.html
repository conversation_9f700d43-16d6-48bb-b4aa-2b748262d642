<!-- StringBuilder strhtml = new StringBuilder(); strhtml.Append(@' -->
<!DOCTYPE html>
<html lang='en'>
<head>
  <meta charset='UTF-8'>
  <meta http-equiv='X-UA-Compatible' content='IE=edge'>
  <meta name='viewport' content='width=device-width, initial-scale=1.0'>
  <title>F4-1B23班组计划列表组件</title>
  <script src='../../../../assets/jquery/jquery.js'></script>
  <script src='../../../../assets/echarts/echarts.min.js'></script>
  <script src='../../../../assets/vue/vue.js'></script>
  <script src='../../../../assets/element/index.js'></script>
  <script src='../../../../assets/axios/axios.min.js'></script>
  <script src='../../../../03team_new/comm/api_environment.js'></script>
  <script src='../../../../assets/tools/helper.js'></script>
  <script src='../../../../assets/echarts/echarts.min.js'></script>
  <link rel='stylesheet' href='../../../../assets/element/index.css'>
  <link rel='stylesheet' href='../../../../assets/css/el.dialog.css'>
  <link rel='stylesheet' href='../../../../assets/css/comm.self.css'>

  <link rel='stylesheet' href='../../../../assets/css/shift.page.component.css'>
  <!--组件-->
  <script src='../../components/f1leap.f4cfm.shift.page.component.js'></script>
  <!-- 引入样式 -->
  <link rel='stylesheet' href='../../../../assets/vxe-table-v3/style.css'>
  <!-- 引入脚本 -->
  <script src='../../../../assets/vxe-table-v3/xe-utils.js'></script>
  <script src='../../../../assets/vxe-table-v3/<EMAIL>'></script>
  <script src='../../components/common.team.plan.js'></script>
  <script src='../../components/common.team.plan.see.js'></script>
  <!--组件-->
  <script src='../../B1_team_plan/f4-1-b1/f4_1.vg.sm.js'></script>
  <script src='../../B1_team_plan/f4-1-b1/f4_1.vg.status.js'></script>



</head>
<body>
  <div id='app'>
    <!-- input_str_esn_type:机型-->
    <y-f41cfm-f11leap-shift-page input_str_flow='INSPECT' input_group_type='F4-1-Core'></y-f41cfm-f11leap-shift-page>
  </div>
  <script>
    var vue1 = new Vue({
      el: '#app',
      data: function () {
        return {
          id_main: ''
        }
      },
      methods: {
        backCall() {

        }
      }
    })
  </script>
</body>
</html>
<!-- '); arg.redata = strhtml.ToString(); return arg.redata; -->