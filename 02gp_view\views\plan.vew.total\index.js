const { defineAsyncComponent, onMounted, ref, reactive } = Vue
const PlanView = {
  components: {
    PlanGanttView: defineAsyncComponent(() => import('./plangantt.view.js')),
  },
  setup() {
    const planGanttRef = ref(null)
    const searchForm = reactive({
      projectName: '',
    })
    const queryList = () => {
      planGanttRef.value.queryList(searchForm)
      isExpand.value = true
    }

    const isExpand = ref(false)
    /**
     * 展开甘特图
     */
    const handleExpandGantt = () => {
      isExpand.value = true
      planGanttRef.value.toggleGantt(isExpand.value)
    }
    /**
     * 收起甘特图
     */
    const handleCollapseGantt = () => {
      isExpand.value = false
      planGanttRef.value.toggleGantt(isExpand.value)
    }
    return {
      searchForm,
      queryList,
      planGanttRef,
      handleExpandGantt,
      handleCollapseGantt,
      isExpand,
    }
  },
  template: /*html*/ `
    <el-form class="ml-4 mt-2" :model="searchForm" :inline="true">
      <el-form-item label="任务名称">
        <el-input style="width:220px" v-model="searchForm.projectName" clearable placeholder="请输入任务名称"></el-input>
      </el-form-item>
      <el-form-item class="w-[220px]" label="机型">
        <el-select v-model="searchForm.str_engine_type" placeholder="请选择机型" clearable>
          <el-option label="CFM56" value="CFM56"></el-option>
          <el-option label="LEAP" value="LEAP"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="release开始时间">
        <el-date-picker v-model="searchForm.dt_start" type="date" placeholder="请选择开始时间" value-format="YYYY-MM-DD"></el-date-picker>
      </el-form-item>
      <el-form-item label="release结束时间">
        <el-date-picker v-model="searchForm.dt_end" type="date" placeholder="请选择结束时间" value-format="YYYY-MM-DD"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryList">查询</el-button>
        <el-button v-if="!isExpand" type="primary" @click="handleExpandGantt">展开甘特图</el-button>
        <el-button v-else type="primary" @click="handleCollapseGantt">收起甘特图</el-button>
      </el-form-item>
    </el-form>
    <div class="h-[90vh]">
      <PlanGanttView ref="planGanttRef"></PlanGanttView>
    </div>
  `,
}

export default PlanView
