import { post } from '../../config/axios/httpReuest.js';

const { reactive, ref, onMounted } = Vue
export default {
  name: 'OutboundMissingPartsComponent',
  setup() {
    const searchForm = reactive({

    });

    const tableData = ref([]);

    const getTableData = async () => {
      const params = {
        ac: 'pda_node_missingOut_data',
      };
      const { data } = await post(params);
      tableData.value = data.data;
    };

    const getRefreshData = async () => {
      const params = {
        ac: 'pda_getpsmbegindate',
      };
      await post(params);
      await getTableData();
    };

    onMounted(() => {
      getTableData();
    })

    const tableHeaderClass = ({ row, column, rowIndex, columnIndex }) => {
      return 'table-header';
    };
    return {
      searchForm,
      tableData,
      getTableData,
      getRefreshData,
      tableHeaderClass
    };
  },
  // language=html
  template: `
    <!--    头部搜索-->
    <el-form :model="searchForm" inline label-width="100px" size="small">
      <el-form-item label="日期:">
        <el-date-picker v-model="searchForm.date" type="daterange" value-format="YYYY-MM-DD" clearable></el-date-picker>
      </el-form-item>
      <el-form-item label="发动机号:">
        <el-input v-model="searchForm.str_esn" clearable></el-input>
      </el-form-item>
      <el-form-item label="单元体:">
        <el-input v-model="searchForm.str_sm" clearable></el-input>
      </el-form-item>
      <el-form-item label="集件箱号:">
        <el-input v-model="searchForm.box_num" clearable></el-input>
      </el-form-item>
      <el-form-item label="零件名称:">
        <el-input v-model="searchForm.str_name" clearable></el-input>
      </el-form-item>
      <el-form-item label="集件件号:">
        <el-input v-model="searchForm.str_pn_do" clearable></el-input>
      </el-form-item>
      <el-form-item label="缺件数量:">
        <el-input v-model="searchForm.int_missing_num" clearable></el-input>
      </el-form-item>
      <el-form-item label=" ">
        <el-button type="primary" circle @click="getTableData">
          <template #icon>
            <el-icon>
              <Search></Search>
            </el-icon>
          </template>
        </el-button>
        <el-button type="primary" circle @click="getRefreshData">
          <template #icon>
            <el-icon>
              <Refresh></Refresh>
            </el-icon>
          </template>
        </el-button>
      </el-form-item>
    </el-form>

    <!--    表格数据-->
    <el-table :data="tableData" border stipe :header-cell-class-name="tableHeaderClass">
      <el-table-column type="index" label="#" width="80"></el-table-column>
      <el-table-column prop="str_esn" label="发动机号"></el-table-column>
      <el-table-column prop="str_sm" label="单元体"></el-table-column>
      <el-table-column prop="box_num" label="集件箱号"></el-table-column>
      <el-table-column prop="str_name" label="零件名称"></el-table-column>
      <el-table-column prop="str_pn_do" label="集件件号"></el-table-column>
      <el-table-column prop="int_missing_num" label="缺件数量"></el-table-column>
    </el-table>
  `,
};
