import { freezeAndUnfreezeApi } from '../../../api/calculation.js'
// 初始化甘特图
export function useInitialization() {
  // 甘特图基础配置
  const GANTT_CONFIG = {
    date_format: '%Y-%m-%d %H:%i:%s',
    date_grid: '%Y-%m-%d',
    drag_links: false,
    grid_width: 600,
    open_tree_initially: false,
    drag_project: false,
    drag_progress: false,
    drag_move: false,
    // 开启撤销
    undo: true,
    // 撤销类型
    undo_types: {
      // 任务撤销
      task: true,
      // 链接撤销
      link: false,
    },
    // 自动调度
    auto_scheduling: true,
    // 自动调度兼容
    auto_scheduling_compatibility: true,
    // 设置开始日期
    start_date: new Date(),
    // 设置滚动位置
    scroll_position: new Date(),
    // 最小列宽
    min_column_width: 100,
    scale_height: 100,
    // 时间分割
    scales: [
      // 年
      { unit: 'year', step: 1, format: '%Y年' },
      // 月
      { unit: 'month', step: 1, format: '%M' },
      // 周
      { unit: 'week', step: 1, format: 'W%w' },
      // 星期
      { unit: 'day', step: 1, format: '%D' },
      // 天
      { unit: 'day', step: 1, format: '%m-%d' },
    ],
  }

  // 甘特图列配置
  const GANTT_COLUMNS = [
    { name: 'text', label: '任务名称', tree: true, width: '*', resize: true },
    { name: 'str_engine_type', label: '机型', align: 'center', width: 100 },
    { name: 'start_date', label: '开始时间', align: 'center', width: 100 },
    {
      name: 'dt_end',
      label: '结束时间',
      align: 'center',
      width: 100,
    },
    { name: 'duration', label: '持续时间', align: 'center', width: 100 },
    { name: 'progress', label: '进度', align: 'center', width: 100 },
    { name: 'dt_f2_3_end', label: 'F2/3结束时间', width: 100 },
  ]

  // 甘特图插件
  const GANTT_PLUGIN = {
    auto_scheduling: true,
    critical_path: true,
    marker: true,
    undo: true,
  }

  // 设置甘特图模板
  const setGanttTemplate = (gantt) => {
    gantt.templates.task_class = (start, end, task) => `main-level-${task.$level}`
  }

  // 禁用任务双击事件
  const disableTaskDblClick = () => {
    gantt.attachEvent('onTaskDblClick', function (id, e) {
      return false // 返回 false 阻止默认行为
    })
  }

  // 根据id获取任务以及其父任务
  const getTaskById = (id) => {
    const task = gantt.getTask(id)
    const parent = gantt.getTask(task.parent)
    return { task, parent }
  }

  /**
   * 拖动前的事件处理
   * @param {string | number} id 任务id
   * @param {string} mode 拖动模式
   * @returns {boolean} 是否允许拖动
   */
  const handleBeforeTaskDrag = (id, mode) => {
    // 获取任务
    const task = gantt.getTask(id)
    // 查看当前任务是否有子任务
    const children = gantt.getChildren(id)
    // 查看当前任务是否有父任务
    const parent = gantt.getTask(task.parent)
    if (children.length > 0) {
      return false
    }
    // 查看当前任务是否是父任务
    if (parent.id === 0) {
      return false
    }

    // 当拖动任务时，如果任务的开始时间小于父任务的开始时间，则不允许拖动
    if (moment(task.start_date).isBefore(moment(parent.start_date))) {
      return false
    }
    task._changed = true
    return true
  }

  // 拖动结束后的事件处理
  const handleAfterTaskDrag = (id) => {
    const { task } = getTaskById(id)
    task.dt_end = moment(task.start_date).add(task.duration, 'days').subtract(1, 'days').format('YYYY-MM-DD')
    gantt.updateTask(id, task)
    gantt.showDate(task.start_date)
  }

  /**
   * 甘特图事件
   * @param {function} func
   */
  const ganttEvent = (func) => {
    // gantt.attachEvent('onGanttRender', function () {
    //   gantt.showDate(new Date()) // 滚动到今天
    // })
    // 拖动前的事件处理
    gantt.attachEvent('onBeforeTaskDrag', handleBeforeTaskDrag)
    // 拖动结束后的事件处理
    gantt.attachEvent('onAfterTaskDrag', handleAfterTaskDrag)
  }

  // 初始化甘特图
  const initGantt = (isEdit, func) => {
    gantt.dateFromPos(-10)
    gantt.plugins(GANTT_PLUGIN)
    Object.entries(GANTT_CONFIG).forEach(([key, value]) => {
      gantt.config[key] = value
    })
    const dateEditor = gantt.config.editor_types.date
    gantt.config.editor_types.end_date = gantt.mixin({
      set_value: function (value, id, column, node) {
        const correctedValue = gantt.date.add(value, -1, 'days')
        return dateEditor.set_value.apply(this, [correctedValue, id, column, node])
      },
      get_value: function (id, column, node) {
        const selectedDate = dateEditor.get_value.apply(this, [id, column, node])
        return gantt.date.add(selectedDate, 1, 'days')
      },
    })
    gantt.config.columns = GANTT_COLUMNS
    // 是否拖动
    gantt.config.drag_move = isEdit

    // 设置甘特图模板
    setGanttTemplate(gantt)
    // 禁用任务双击事件
    disableTaskDblClick(gantt)
    // 甘特图事件
    ganttEvent(func)
  }

  // 添加当前日期标记
  const addCurrentDateMarker = () => {
    const today = new Date()
    gantt.addMarker({
      start_date: today,
      css: 'current-date-marker',
      text: moment(today).format('YYYY-MM-DD'),
      title: '今天',
    })
  }

  // 渲染甘特图
  const renderGantt = (id, ganttParse) => {
    gantt.init(id)
    addCurrentDateMarker()
    gantt.parse(ganttParse)
    gantt.render()
  }

  return {
    initGantt,
    GANTT_COLUMNS,
    renderGantt,
  }
}
