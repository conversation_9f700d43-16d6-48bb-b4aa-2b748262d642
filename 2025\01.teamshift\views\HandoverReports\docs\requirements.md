# 交接统计报表需求文档

## 需求描述

本文档描述了交接统计报表的功能需求。该报表旨在提供清晰的交接数据概览，帮助用户跟踪交接进度和状态。

## 功能模块

### 1. 交接提交统计

#### 1.1 概述
此模块用于展示用户提交交接事项的情况。

#### 1.2 图表展示
*   **图表类型**: 建议使用饼图或环形图，清晰展示提交状态的比例。
*   **数据维度**:
    *   **已提交**: 显示已成功提交的交接事项数量。
    *   **未提交**: 显示尚未提交的交接事项数量。
*   **交互功能**:
    *   **钻取**: 点击"已提交"或"未提交"部分，可以钻取到下一级，展示这些事项的详细列表或更细致的分类统计（例如按部门、按类型等）。
    *   **悬浮提示**: 鼠标悬浮在图表上时，应显示对应部分的具体数值和百分比。

### 2. 交接接收统计

#### 2.1 概述
此模块用于展示用户接收交接事项的情况。

#### 2.2 图表展示
*   **图表类型**: 建议使用饼图或环形图，清晰展示接收状态的比例。
*   **数据维度**:
    *   **已接收**: 显示已成功接收的交接事项数量。
    *   **未接收**: 显示尚未接收或已拒绝接收的交接事项数量。
*   **交互功能**:
    *   **钻取**: 点击"已接收"或"未接收"部分，可以钻取到下一级，展示这些事项的详细列表或更细致的分类统计（例如按部门、按类型等）。
    *   **悬浮提示**: 鼠标悬浮在图表上时，应显示对应部分的具体数值和百分比。

## 通用功能

### 数据筛选
用户应能够根据以下条件对交接提交和交接接收的统计数据进行筛选：

*   **时间范围**: 用户可以选择预设的时间范围（如本周、本月、本季度）或自定义起始和结束日期来筛选特定时间段内的数据。
*   **业务类型**: 用户可以通过多选或单选的方式，根据以下业务类型筛选数据：
    *   F1-2
    *   F2
    *   F1分解
    *   F4装配
    *   分配检验
    *   装配检验

筛选条件应用后，图表和相关的钻取数据应相应更新，仅显示符合筛选条件的数据。

## 技术栈
*   **前端框架**: Vue.js
*   **UI 组件库**: Element Plus
*   **样式**: Tailwind CSS
*   **图表库**: ECharts
  