import { post } from '../../utils/request.js'

/**
 * 上传文件
 * @param {FormData} formData - 包含文件和其他信息的FormData对象
 * @returns {Promise} 上传结果
 */
/**
 * 延时申请列表
 */
export function getDelayApplyList(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_delay_list',
    Filter: postData,
  })
}
/**
 * 获取班次
 * @param {} params
 * @returns
 */
export function getShiftList() {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_shift',
  })
}
/**
 * 保存延时申请
 * @param {*} params
 * @returns
 */
export function saveDelayApply(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_delay_apply',
    postdata: params,
  })
}

/**
 * 获取延时申请明细
 * @param {} params
 * @returns
 */
export function getDelayApplyId(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_delay_by_id',
    id: params,
  })
}

/**
 * 删除延时申请
 * @param {} params
 * @returns
 */
export function deleteDelayApply(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_delete_delay',
    ids: params,
  })
}

/**
 * 获取team
 * @param {} params
 * @returns
 */
export function getTeams() {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_teams',
  })
}

/**
 * 获取staff
 * @param {} params
 * @returns
 */
export function getCurrentDeptStaff(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_current_dept_staff',
    is_hr:params
  })
}

/**
 * 提交审批
 * @param {} params
 * @returns
 */
export function submitApprove(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_autid_delay_apply',
    id: params.id,
    status: params.status,
    str_content: params.str_content,
  })
}

/**
 * 撤回
 * @param {} params
 * @returns
 */
export function withdrawDelayApply(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_autid_delay_apply',
    id: params.id,
    status: params.status,
  })
}