import { queryGanttById } from '../../api/plan.manage.js'
import { useTransform } from '../plan.manage/useTransform.js'
import DynamicColumnConfigurator from '../../components/DynamicColumnConfigurator.js'
import { useDynamicColumn } from '../../composables/useDynamicColumn.js'

const { onMounted, ref } = Vue
const PlanGanttView = {
  components: {
    DynamicColumnConfigurator,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const { transformSubPlansToGanttData } = useTransform()
    const ganttData = ref({})
    // 获取甘特图数据
    const getGanttData = async (node, dt_start, dt_end, str_engine_type) => {
      const data = await queryGanttById(props.id, node, dt_start, dt_end, str_engine_type)
      if (data) {
        ganttData.value.data = _.map(data.subPlans, transformSubPlansToGanttData)
        ganttData.value.links = data.links
      }
    }

    const GANTT_COLUMNS = [
      { name: 'text', label: 'Task name', width: '*', tree: true, align: 'left' },
      { name: 'str_engine_type', label: '机型', width: 100 },
      { name: 'start_date', label: 'Start time', width: 100 },
      { name: 'duration', label: 'Duration', width: 60 },
      { name: 'dt_f2_3_end', label: 'F2/3结束时间', width: 100 },
    ]
    

    const ganttInit = () => {
      gantt.config.grid_width = 450
      gantt.config.open_tree_initially = false
      // 设置时间格式
      gantt.config.date_format = '%Y-%m-%d %H:%i:%s'
      gantt.locale.labels.section_status = 'Status'
      gantt.locale.labels['section_progress'] = 'Progress'
      gantt.locale.labels['section_owner'] = 'Owner' // 弹框字段
      // 禁止拖动
      gantt.config.drag_links = false
      gantt.config.drag_progress = false
      gantt.config.drag_move = false
      gantt.config.drag_resize = false
      // 设置gantt列
      gantt.config.columns = GANTT_COLUMNS

      gantt.plugins({})
      // 禁用任务双击事件
      gantt.attachEvent('onTaskDblClick', function (id, e) {
        return false // 返回 false 阻止默认行为
      })
      // 设置任务的样式
      gantt.templates.task_class = function (start, end, task) {
        return `sub-level-${task.$level}`
      }
      gantt.templates.scale_cell_class = function (date) {
        if (!gantt.isWorkTime(date)) return 'weekend'
      }
    }

    const dynamicColumn = useDynamicColumn(gantt, GANTT_COLUMNS)

    // 展开或折叠甘特图
    const toggleGantt = (expand) => {
      gantt.batchUpdate(() => {
        gantt.eachTask((task) => {
          if (expand) {
            gantt.open(task.id)
          } else {
            gantt.close(task.id)
          }
        })
      })
    }

    // 查询
    const queryList = async (searchForm) => {
      console.log(searchForm)
      gantt.clearAll()
      gantt.init('gantt_all')
      await getGanttData(searchForm.projectName, searchForm.dt_start, searchForm.dt_end, searchForm.str_engine_type)
      gantt.parse(ganttData.value)
      toggleGantt(true)
      gantt.render()
    }

    onMounted(async () => {
      await getGanttData()
      ganttInit()
      gantt.init('gantt_all')
      gantt.parse(ganttData.value)
    })
    return {
      ganttData,
      getGanttData,
      queryList,
      toggleGantt,
      ...dynamicColumn,
    }
  },
  template: /*html*/ `
    <div class="flex justify-end gap-2 mb-2">
      <DynamicColumnConfigurator
        v-model="visibleColumns"
        :all-columns="columnConfig"
        button-text="列配置"
        button-icon="Grid"
      />
    </div>

    <div id="gantt_all" class="h-full w-full"></div>
  `,
}
export default PlanGanttView
