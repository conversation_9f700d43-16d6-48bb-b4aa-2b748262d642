// 人力资源状态编辑
Vue.component('y-f1-1-vg-status_edit', {
	props: ['input_vg_plan_date_start', 'input_vg_plan_date_end', 'input_id_main', 'input_vg_plan_date'], //input_id_wo : 发动机  input_plan_date：计划日期 input_id_main 
	data: function () {
		return {
			id_main: this.input_id_main,
			is_show: true,
			str_num_plan: 0,
			task_list: [
			], // 任务分配 展示使用
			status_data: [], // 所有人员状态
			task: {},
			staff_list: [], // 单元体
			rulesForm1: {
				// form1 验证
				id_wo: [
					{
						required: true,
						message: "Please select  task",
						trigger: "blur",
					},
					//其他验证设置...
					// { validator: deptRule, trigger: 'blur' },
				],

				id_model: [
					{
						required: true,
						message: "Please select  Model",
						trigger: "blur",
					},
					//其他验证设置...
					// { validator: deptRule, trigger: 'blur' },
				],


			}

		}
	},

	filters: {
		/**日期管道*/
		dateTime: function (date, type) {
			if (date != null && date != '') {
				let yer, month, day, HH, mm, ss
				let time = new Date(date),
					timeDate
				yer = time.getFullYear()
				month = time.getMonth() + 1
				day = time.getDate()
				HH = time.getHours() //获取系统时，
				mm = time.getMinutes() //分
				ss = time.getSeconds() //秒

				month = month < 10 ? '0' + month : month
				day = day < 10 ? '0' + day : day
				HH = HH < 10 ? '0' + HH : HH
				mm = mm < 10 ? '0' + mm : mm
				ss = ss < 10 ? '0' + ss : ss

				switch (type) {
					case 'yyyy':
						timeDate = String(yer)
						break
					case 'yyyy-MM':
						timeDate = yer + '-' + month
						break
					case 'yyyy-MM-dd':
						timeDate = yer + '-' + month + '-' + day
						break
					case 'yyyy/MM/dd':
						timeDate = yer + '/' + month + '/' + day
						break
					case 'yyyy-MM-dd HH:mm:ss':
						timeDate =
							yer +
							'-' +
							month +
							'-' +
							day +
							'' +
							HH +
							':' +
							mm +
							':' +
							ss
						break
					case 'HH:mm:ss':
						timeDate = HH + ':' + mm + ':' + ss
						break
					case 'MM':
						timeDate = String(month)
						break
					default:
						timeDate =
							yer < 1900 ? '' : yer + '-' + month + '-' + day
						break
				}
				return timeDate
			} else {
				return ''
			}
		},

		/**获取数据状态描述*/
		getStatusStr(data) {
			if (data == 1) {
				return '审批通过'
			} else if (data == -1) {
				return '审批失败'
			} else if (data == -999) {
				return '审批撤销'
			} else if (data == 0 || data > 300) {
				return '审批中'
			} else {
				return '待提交'
			}
		},

	},
	methods: {
		/**获取 工作指令 */
		async get_status_list() {

			let _this = this;
			await axios
				.post(globalApiUrl, {
					au: "ssamc",
					ap: "api2018",
					ak: "",
					ac: "pt_get_staff_status",
				})
				.then(function (response) {
					_this.status_data = response.data?.data || [];
				})
				.catch(function (error) {

					console.log(error);
				});


		},
		/**获取单元体 */
		async get_staff_list(id) {

			let _this = this;
			await axios
				.post(globalApiUrl, {
					au: "ssamc",
					ap: "api2018",
					ak: "",
					ac: "pt_get_team_staff_list",
					dt_date:_this.input_vg_plan_date
				})
				.then(function (response) {
					_this.staff_list = response.data?.data || [];
				})
				.catch(function (error) {

					console.log(error);
				});

		},


		/**添加任务排班 */
		add_task_plan() {
			let _this = this;
			_this.task_list.push({});
			_this.str_num_plan = _this.task_list.length;
		},
		/** 删除任务 */
		delete_task(task_delete, index) {
			let _this = this;
			_this.task_list.splice(index, 1);
			_this.str_num_plan = _this.task_list.length;

		},
		/**保存磨削 */
		save_plan() {
			let _this = this;

			let PtDailyStaffStatus_t = [];

			_this.task_list.forEach(element => {
				let id_staff_t=[];
				element.id_staff.forEach(id_staff => {
					id_staff_t.push(id_staff);
				});
				PtDailyStaffStatus_t.push({ status: element.int_status, staffIds: id_staff_t })
			});


			// 可以保存
			axios
				.post(globalApiUrl, {
					au: "ssamc",
					ap: "api2018",
					ak: "",
					ac: "pt_save_daily_staff_status",
					plan_date: _this.input_vg_plan_date,
					savedata: { id_main: _this.id_main, dailyData: PtDailyStaffStatus_t }

				})
				.then(function (response) {

					_this.$message({
						type: "success",
						message: "Save success",
					});
					_this.closeDialog();
				})
				.catch(function (error) {
					console.log(error);
				});
			// }

		},
		/**获取详情  若果有ID 是编辑*/
		getInfo() {
			let _this = this;
			if (_this.id_main) {
				// 编辑
				axios
					.post(globalApiUrl, {
						au: "ssamc",
						ap: "api2018",
						ak: "",
						ac: "pt_get_dailystaff_plan",
						id_main: _this.id_main
					})
					.then(function (response) {
						let task_list_t = [];
						 _this.input_id_main = response.data.data.id_main;
						 response.data.data.statusdata.forEach(x=>{
							 id_staff_t = [];
							 x.staff.forEach(staff=>{
								id_staff_t.push(staff.id_staff)
							 });
							 task_list_t.push({
								 int_status:x.int_status+"",
								 id:x.id,
								 id_staff:id_staff_t
							 });
							 _this.task_list = task_list_t || [];
							 _this.str_num_plan = _this.task_list.length;
						 })
					})
					.catch(function (error) {
						console.log(error);

					});
			}
			else if (_this.id_main && !_this.is_edit) {
				// 查看
			}


		},	/**关闭弹 */
		closeDialog() {
			let _this = this;
			_this.is_show = false;
			_this.$emit("get_back_close_vg_dialog"); //关闭详情页

		},

	},
	created: function () {
		Promise.all([this.get_status_list(), this.get_staff_list()]).then(() => {
			this.getInfo();
		});
	},
	mounted() {
		this.add_task_plan();
	},

	template: `
	<div>
	<el-dialog title="Staff Status Plan" width="50%" :visible.sync="is_show" class="self_dialog" @close="closeDialog()">
	<el-row style="margin-bottom: 3px;">
		<el-badge :value="str_num_plan" class="item" type="info">
			<el-button size="small">Num</el-button>
		</el-badge>
	</el-row>


	<el-row style="margin-bottom: 10px;" v-for="(task,index) in task_list" v-bind:key="index">
		<el-card class="box-card">
			<div slot="header" class="clearfix">
				<span>&nbsp;&nbsp;</span>
				<el-button style="float: right;" type="danger" size="small" @click="delete_task(task,index)">
					Delete</el-button>
			</div>
			<el-form ref="form1" :model="task" :rules="rulesForm1" label-position="left" label-width="auto">
				<el-row>
					<el-col :span="12">
						<el-form-item label="Status" prop='int_status'>
							<el-select v-model="task.int_status" filterable placeholder="select status""
								style="width: 100%;">
								<el-option v-for="(item,index) in status_data" :key="item.str_code" :label="item.str_name"
									:value="item.str_code"></el-option>
							</el-select>

						</el-form-item>
					</el-col>
					
					<el-col :span="12">
						<el-form-item label="Staff" prop='id_staff'>
							<el-select v-model="task.id_staff"  multiple filterable placeholder="select stafff"
								style="width: 100%;">
								<el-option v-for="(item,index)  in staff_list" :key="item.id" :label="item.str_name"
									:value="item.id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				
		
			</el-form>
		</el-card>
	</el-row>
	<el-row>
		<el-button style="margin-left:20px; float:right" size="small" type="success" @click="add_task_plan()">
			+New 
		</el-button>
	</el-row>
	<div slot="footer">
		<el-button class="topButton_right" style="margin-left:20px;" size="small" type='danger'
			@click="closeDialog()">Cancle
		</el-button>
		<el-button class="topButton_right" size="small" type='primary' @click="save_plan()">Save
		</el-button>
	</div>
</el-dialog>
</div>
    `,
})
