
  /**
   * 处理查询参数
   * @param {Object} formSearch 查询表单数据
   */
  export const resolveSearchParam = (formSearch) => {
    return Object.entries(formSearch).reduce((acc, [key, value]) => {
      if (value && value.length > 0) {
        const fieldMap = {
          f4_1BeginTime: 'dt_f41_date',
          realeaseTime: 'dt_release_date',
          f2_3ClosedTime: 'dt_f23_date',
          engineType: 'str_engine_type',
          repairType: 'str_maintenance_type',
        }
        acc.push({
          str_key: fieldMap[key],
          str_value: value,
        })
      }
      return acc
    }, [])
  }