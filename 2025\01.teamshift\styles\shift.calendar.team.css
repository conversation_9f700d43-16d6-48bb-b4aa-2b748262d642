/* 容器样式 */
.shift-calendar-container {
  padding: 10px;
  position: relative;
}

/* 加载遮罩 */
.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-text {
  margin-top: 10px;
  color: #409eff;
}

/* 单元格样式 */
.shift-cell {
  width: 100%;
  height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  margin: 2px;
  cursor: pointer;
  transition: opacity 0.2s;
}

.shift-cell:hover {
  opacity: 0.8;
}

.shift-cell-content {
  padding: 8px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
}

.shift-type {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.staff-count {
  font-size: 12px;
  line-height: 16px;
}

/* 状态样式 */
.status-day {
  background-color: #e1f3d8;
  color: #67c23a;
}

.status-night {
  background-color: #ecf5ff;
  color: #409eff;
}

.status-rest {
  background-color: #fef0f0;
  color: #f56c6c;
}

/* 表头样式 */
.table-header {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  text-align: center;
}

.weekend-header {
  background-color: #fef0f0;
  color: #f56c6c !important;
  font-weight: bold;
  text-align: center;
}

.holiday-header {
  background-color: #fdf6ec;
  color: #e6a23c;
  font-weight: bold;
  text-align: center;
}

.workday-header {
  background-color: #f4f4f5;
  color: #909399;
  font-weight: bold;
  text-align: center;
}

.table-cell {
  padding: 0;
  height: 52px;
}

/* 周表头 */
.week-header {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  text-align: center;
  height: 40px;
  line-height: 40px;
}

/* 日期表头 */
.date-header {
  height: 40px;
  line-height: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.holiday-text {
  font-size: 12px;
  margin-top: 2px;
}

/* 今日单元格样式 */
.today {
  border: 2px solid #409eff !important;
  background-color: #f5f7fa;
}

/* 弹框表单样式 */
.form-content {
  line-height: 32px;
  padding: 0 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #606266;
}

/* 弹框表格样式 */
.el-table {
  --el-table-border-color: #dcdfe6;
  --el-table-header-bg-color: #f5f7fa;
  --el-table-header-text-color: #606266;
  --el-table-text-color: #606266;
  --el-table-row-hover-bg-color: #f5f7fa;
}

.el-table th {
  font-weight: 600;
  background-color: var(--el-table-header-bg-color);
  color: var(--el-table-header-text-color);
}

/* 弹框标题样式 */
.dialog-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

/* 弹框底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单项间距 */
.el-form-item {
  margin-bottom: 18px;
}

/* 选择器样式 */
.el-select {
  width: 100%;
}

/* 弹框整体样式 */
.shift-dialog {
  .section-title {
    position: relative;
    font-size: 15px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
    padding-left: 12px;
    line-height: 1.5;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #409eff;
      border-radius: 2px;
    }
  }
}
