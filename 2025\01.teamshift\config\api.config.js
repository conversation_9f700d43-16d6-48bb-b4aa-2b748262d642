// 接口基础配置
const envConfig = {
  development: {
    baseURL: 'http://**************',
  },
  test: {
    baseURL: 'http://**************',
  },
  production: {
    baseURL: 'http://*************',
  },
  local: {
    baseURL: 'http://localhost:5003',
  },
}

// 当前环境（默认根据NODE_ENV判断）
const currentEnv = 'test'

// 合并配置
const apiConfig = {
  ...envConfig[currentEnv],
  // 具体接口地址
  endpoints: {
    login: '/auth/login',
    userInfo: '/user/profile',
    upload: '/file/upload',
  },
}

export default apiConfig
