// 班组长列表组件，使用于CFM F4 ，leap F1-1

Vue.component('y-f41cfm-f11leap-shift-page', {
  props: [
    'str_flow',
    'input_id_wo',
    'input_plan_date',
    'input_id_main',
    'input_str_flow',
    'input_str_esn_type',
    'inspect_type',
    'input_group_type',
  ],
  data: function () {
    return {
      str_flow_now: this.input_str_flow, // 当前进入的Flow
      str_esn: '', // 发动机编号
      task_type: '',
      loading: false, // 列表加载动画
      isteamplanshow: false, // 添加页面显示
      id_main: '',
      searchParams: {
        dt_date: '',
        str_esn: '',
      }, // 筛选条件

      formInline: {
        WorkOrder: '',
        Engine: '',
        date: [],
      },
      formPartA: {
        reason: '',
        planId: '',
        status: '',
      },
      dialogVisible: false,
      form: {},
      height: 0,
      scrollTop: 0,
      radio: 1,
      datas: [],
      savedatas: [],
      checked: false,
      open: false,
      dialogVisibledata: '',
      radio: '',
      checkboxGroup: [],
      ischeckcolumn: '',
      tabledate: [],
      tableviewData: [],
      search: {
        date: [],
        beginDate: '',
        endDate: '',
      },
      pickerOptions: {
        shortcuts: [
          {
            text: 'In the latest week',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: 'Last Month',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: 'Last 3 months',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
      title: '添加',

      columns_config: [],
      tableData: [],
      isteamplanshow: false,
      addDate: '',
      addIdwo: '',
      id_site: '',
      showtable: true,
      id_main: '', // 计划主数据ID
      input_is_edit: false,
      input_is_editsee: false,
      isteamplanshowsee: false,

      isteamplanshowvg: false,
      isteamplanshowvg1: false,
      input_is_editvg: false,
      vgday: '',
      input_vg_plan_date_end: '',
      input_vg_plan_date_start: '',
      input_vg_plan_date: '',
      team_info: null,
      reachtable: true,
      esnfilter: [],
      rulesformPartA: {
        // form1 验证
        reason: [
          {
            required: true,
            message: 'Please select  reason',
            trigger: 'blur',
          },
          //其他验证设置...
          // { validator: deptRule, trigger: 'blur' },
        ],
      },
      is_show_inspator_suport: false, // 发动机下 检验人员信息显示
      show_inspator_suport_info: {}, // 发动机下 检验人员信息
      is_show_vg: true, //是否展示vg
      esn_data_end: '',
      tableData: [],
      columnList: [],
      showHeader: false,
      loadingData: false,
      tableMaxheight: 850,
      is_show_batch_plan: false,
      batch_plan_data: {},
      templateList: [],
      quick_plan_data: [],
      tasksList: [],
      batch_plan_type: '',
    }
  },
  mounted: function () {
    this.loadList()
  },
  created: function () {
    this.searchParams.dt_date = [AddDays(0), AddDays(14)]
    //获取Team人员
    this.getteamleaderandstaff()
    this.getTasksList()
  },
  methods: {
    /** 查看*/
    boxview(row, plan) {
      let _this = this
      // _this.addDate = date
      _this.isteamplanshowsee = true
      _this.id_main = plan.planId
    },
    /** 编辑*/
    boxedit(row, plan, date) {
      let _this = this
      _this.addDate = date
      _this.addIdwo = row.id_wo
      _this.isteamplanshow = true
      _this.id_main = plan.planId
      _this.str_esn = row.esn
      _this.esn_data_end = row.end_date
      _this.input_is_edit = 'true'
      _this.task_type = row.str_task_type
    },
    //添加
    addPlan(date, idwo, row) {
      let _this = this
      _this.addDate = date
      _this.addIdwo = idwo
      _this.isteamplanshow = true
      // if(_this.input_str_esn_type=='leap'){
      _this.task_type = row.str_task_type
      // }
      _this.id_site = row.id_site
      _this.str_esn = row.esn
      _this.esn_data_end = row.end_date
    },
    opendilog(dataitem, type) {
      let _this = this
      _this.formPartA.planId = dataitem.id
      if (type == '1') {
        let parame = {
          ac: 'pt_plan_fedback',
          ak: '',
          ap: '',
          au: 'ssamc',
          planid: dataitem.planId,
          status: type,
        }
        $.ajax({
          url: globalApiUrl, //表示发送请求的路径
          type: 'post', //http请求方式
          accordion: 'true',
          data: parame,
          async: false, //同步请求，将锁住浏览器，用户其他操作必须等待请求完成才可以执行
          error: function () {
            alert('系统错误，请联系管理员!')
            return false
          },
          success: function (res) {
            if (res.code == 'success') {
              // if (res.data.length > 0) {
              //     that.tableColumnview.tableColumn = []
              //     that.tableColumnview.tableColumn = JSON.parse(JSON.stringify(res.data))

              //     that.$nextTick(() => {
              //         that.$refs.plxTable.reloadColumn(that.tableColumnview)
              //     })
              //     //内容
              //     that.gettableData()
              // } else {
              //     that.Isempty()
              // }
              _this.query()
            } else {
              that.$message.error(res.text)
            }
          },
        })
      } else {
        _this.formPartA.planId = dataitem.planId
        _this.formPartA.status = type
        _this.dialogVisible = true
      }
    },
    toggleEditDialog() {
      if (this.dialogVisible) {
        let _this = this
        let _is_can_save = true
        if (_this.formPartA.status == -2 || _this.formPartA.status == -1) {
          if (!_this.formPartA.reason) {
            _is_can_save = false
            _this.$message({
              message: '请备注说明',
              type: 'warning',
            })
          }
        }

        if (_is_can_save) {
          // 可以保存
          let parame = {
            ac: 'pt_plan_fedback',
            ak: '',
            ap: '',
            au: 'ssamc',
            planid: _this.formPartA.planId,
            status: _this.formPartA.status,
            reason: _this.formPartA.reason,
          }
          $.ajax({
            url: globalApiUrl, //表示发送请求的路径
            type: 'post', //http请求方式
            accordion: 'true',
            data: parame,
            async: false, //同步请求，将锁住浏览器，用户其他操作必须等待请求完成才可以执行
            error: function () {
              alert('系统错误，请联系管理员!')
              return false
            },
            success: function (res) {
              if (res.code == 'success') {
                _this.query()
              } else {
                that.$message.error(res.text)
              }
            },
          })
          _this.dialogVisible = false
        }
      } else {
        _this.dialogVisible = true
      }
    },
    /**查询*/
    query() {
      let _this = this
      _this.loadList()
    },
    /** 初始化表头*/
    inint_columns() {
      let _this = this
      _this.columns_config = []
      return new Promise((resolve) => {
        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_query_staff_list_head',
            str_flow: _this.input_str_flow,
            start_date: (_this.searchParams.dt_date && _this.searchParams.dt_date[0]) || '', //getNowDay(),
            end_date: (_this.searchParams.dt_date && _this.searchParams.dt_date[1]) || '',
          })
          .then(function (response) {
            _this.columnList = response.data.data

            _this.columns_config =
              response.data.data &&
              response.data.data.map((item) => {
                return {
                  title_0: item.onduty,
                  title_g_0: item.mesleave,
                  title_vg_0: item.grind,
                  title_vg_1: item.dailydata,
                  title_g: item.str_week,
                  title: item.day,
                  field: 'attr2',
                }
              })

            resolve(_this.columns_config)
          })
          .catch(function (error) {
            console.log(error)
          })
      })
    },
    /** 重新加载*/
    loadList() {
      let _this = this

      this.loading = true

      _this.inint_columns().then((colums_t) => {
        _this.mockList().then((data) => {
          // 使用函数式加载，阻断 vue 对大数据的监听
          const xTable = this.$refs.xTable
          const startTime = Date.now()
          if (xTable) {
            _this.esnfilter = []
            data.forEach(function (value, index) {
              _this.esnfilter.push({
                label: value.esn,
                value: value.esn,
              })
            })

            _this.esnfilter = JSON.stringify(_this.esnfilter)

            this.$refs.xTable.reloadData(data).then(() => {
              // console.log(_this.esnfilter )
              // VXETable.modal.message({ content: `渲染 ${size} 行，用时 ${Date.now() - startTime}毫秒`, status: 'info' })
              _this.reachtable = false
              this.loading = false
              _this.reachtable = true
            })
          }
        })
      })
    },
    /**加载数据*/
    mockList() {
      let _this = this
      _this.loadingData = true
      return new Promise((resolve) => {
        let list = []
        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_query_team_engine_plan',
            str_flow: _this.str_flow_now,
            str_type: _this.inspect_type, // 任务类型。检验班组计划初始查数据需要
            start_date: (_this.searchParams.dt_date && _this.searchParams.dt_date[0]) || '', //getNowDay(),
            end_date: (_this.searchParams.dt_date && _this.searchParams.dt_date[1]) || '',
            str_esn: _this.searchParams.str_esn,
            str_esn_type: _this.input_str_esn_type, //发动机类型
            str_group_type: _this.input_group_type,
          })
          .then(function (response) {
            list = (response.data.data && response.data.data) || []
            resolve(list)
            _this.tableData = list
            _this.loadingData = false
          })
          .catch(function (error) {
            console.log(error)
          })
      })
    },
    /**编辑*/
    edit_row(row, clun, outy_day) {
      let _this = this
      _this.isteamplanshow = true
      let edit_cell = row.plan.find((x) => x.outy_day && x.outy_day == outy_day) || {}
      _this.id_main = edit_cell.id_main
    },
    /** 批量添加*/
    add_batch() {
      let _this = this
      _this.isteamplanshow = true
    },
    /** 获取当天人员*/
    oudy_day_pip_peoples(row, outy_day) {
      return row.plan?.find((x) => x.outy_day == outy_day)?.peolpes || []
    },

    /** 关闭弹窗*/
    close_dialog() {
      let _this = this
      _this.isteamplanshow = false
      _this.id_main = ''
      _this.query()
    },
    get_back_close_vg_dialog() {
      let _this = this
      _this.isteamplanshowvg = false
      _this.isteamplanshowvg1 = false
      _this.id_main = ''
      _this.query()
    },

    /** 处理数组成字符串 */
    exe_str(data) {
      return data.join(',')
    },

    getteamstaff(data) {
      let name = []
      for (let item of data) {
        name.push(item.staff_name)
      }
      return name.toString()
    },
    is_between_test_start_and_test_end(row, column_title){
      return moment(column_title).isBetween(row.test_start, row.test_end, undefined, '[]')
    },
    Endstaff(colday, endday) {
      // 结束日期
      let newEndday = new Date(endday).getTime()
      // 列日期
      let newColday = new Date(colday).getTime()
      // 结束后一天
      let Nextendday = new Date(endday).getTime() + 3600 * 1000 * 24 * 1

      if (newEndday >= newColday) {
        return '0'
      } else if (Nextendday == newColday) {
        return '1'
      }
    },
    changeMoXue(coldata, nowday) {
      let _this = this
      _this.isteamplanshowvg = true
      _this.id_main = coldata

      _this.input_vg_plan_date_end = _this.searchParams.dt_date[1]
      _this.input_vg_plan_date_start = _this.searchParams.dt_date[0]
      _this.input_vg_plan_date = nowday
    },
    changeVg(coldata, nowday) {
      let _this = this
      _this.is_show_vg = true
      _this.changeMoXue(coldata, nowday)
    },
    changeHsg(coldata, nowday) {
      let _this = this
      _this.is_show_vg = false
      _this.changeMoXue(coldata, nowday)
    },
    changeesn(coldata, nowday) {
      let _this = this
      _this.isteamplanshowvg1 = true
      _this.id_main = coldata

      _this.input_vg_plan_date_end = _this.searchParams.dt_date[1]
      _this.input_vg_plan_date_start = _this.searchParams.dt_date[0]
      _this.input_vg_plan_date = nowday
    },
    //判断 开始/结束时间变化，要标记
    changestatus({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex }) {
      if (row.changestatus == '1') {
        return 'bgPurple'
      }
    },
    Isweek({ $rowIndex, column, columnIndex, $columnIndex }) {
      if (column.title == '星期六' || column.title == '星期日') {
        return 'bgDust'
      } else if (
        $rowIndex == 1 &&
        this.columns_config[$columnIndex - 1] != null &&
        this.columns_config[$columnIndex - 1].title_vg_0.vgfullflag == '1'
      ) {
        return 'header-cell'
      } else if (
        $rowIndex == 2 &&
        this.columns_config[$columnIndex - 1] != null &&
        this.columns_config[$columnIndex - 1].title_vg_0.hsgfullflag == '1'
      ) {
        return 'header-cell'
      } else {
        return 'bghead'
      }
    },
    isWeek({ $rowIndex, column, columnIndex, $columnIndex }) {
      if (column.title == '星期六' || column.title == '星期日') {
        return 'bgDust'
      } else {
        return 'bghead'
      }
    },

    //判断状态
    activeclor({ row, column, rowIndex, columnIndex }) {
      let _this = this
      const startTime = new Date(_this.searchParams.dt_date[0]).getTime()
      const endTime = new Date(_this.searchParams.dt_date[1]).getTime()
      let border_css = '' // 标记红色边框
      let cell_css = '' // 标记单元格颜色
      // 是否需要变色内容
      if (column.type == 'Isdata') {
        // 结束日期
        let endday = new Date(row.end_date).getTime()
        // 列日期
        let colday = new Date(column.title).getTime()
        // 开始日期
        let startday = new Date(row.start_date).getTime()
        // pp开始时间
        let pStartDay = new Date(row.project_start).getTime()
        // pp结束时间
        let pEndDay = new Date(row.project_end).getTime()
        // 结束后一天
        let Nextendday = new Date(row.end_date).getTime() + 3600 * 1000 * 24 * 1

        if (colday === endday && endday === startday) {
          border_css = '  border-red-all' // 只有一格的是时候
        } else if (colday === startday || (colday > startday && colday == startTime)) {
          border_css = '  border-red-first'
        } else if (colday === endday || (colday === endTime && colday <= endday)) {
          border_css = '  border-red-end'
        } else if (startday < colday && colday < endday) {
          border_css = '  border-red-other'
        }

        // 日期再PP 计划之外
        if (colday >= pStartDay && pEndDay >= colday) {
          let plan_t = row.plan.find((x) => x.plan_date == column.title)
          if (plan_t && plan_t.task.length > 0) {
            if (plan_t.fedbackstatus == '0') {
              cell_css = 'bgblue'
            } else if (plan_t.fedbackstatus == '1') {
              cell_css = 'bgSuccess'
            } else if (plan_t.fedbackstatus == '-1') {
              cell_css = 'bgWarning'
            } else if (plan_t.fedbackstatus == '-2') {
              cell_css = 'bgDanger'
            } else {
              cell_css = 'bginfoblue'
            }
          } else {
            cell_css = 'bginfoblue'
          }
        } else {
          let plan_t = row.plan.find((x) => x.plan_date == column.title)
          if (plan_t && plan_t.task.length > 0) {
            if (plan_t.fedbackstatus == '0') {
              cell_css = 'bgblue'
            } else if (plan_t.fedbackstatus == '1') {
              cell_css = 'bgSuccess'
            } else if (plan_t.fedbackstatus == '-1') {
              cell_css = 'bgWarning'
            } else if (plan_t.fedbackstatus == '-2') {
              cell_css = 'bgDanger'
            } else {
              cell_css = 'bgInfo'
            }
          } else {
            cell_css = 'bgInfo'
          }
        }
      } else {
        cell_css = 'bgInfo'
      }
      return cell_css + border_css
    },

    //获取组长和组员
    getteamleaderandstaff() {
      let _this = this
      let parame = {
        ac: 'pt_get_teamleaderandstaff',
        ak: '',
        ap: '',
        au: 'ssamc',
      }
      $.ajax({
        url: globalApiUrl, //表示发送请求的路径
        type: 'post', //http请求方式
        accordion: 'true',
        data: parame,
        async: false, //同步请求，将锁住浏览器，用户其他操作必须等待请求完成才可以执行
        error: function () {
          alert('系统错误，请联系管理员!')
          return false
        },
        success: function (res) {
          if (res.code == 'success') {
            _this.team_info = res.data
          } else {
            _this.$message.error(res.text)
          }
        },
      })
    },
    /**数组转 字符串 */
    exe_str(data) {
      return data.join(',')
    },
    /**关闭编辑页 */
    close_edit_dialog() {
      let _this = this
      _this.isteamplanshow = false
      _this.id_main = ''
      _this.addDate = ''
      _this.addIdWo = ''
      //_this.input_is_edit= ""
      //_this.input_str_flow= ""
      _this.str_esn = ''
      _this.esn_data_end = ''
      _this.query()
    },
    close_self_dialog() {
      let _this = this
      _this.isteamplanshow = false
      _this.id_main = ''
      _this.addDate = ''
      _this.addIdWo = ''
      // _this.input_is_edit= ""
      //  _this.input_str_flow= ""
      _this.str_esn = ''
      _this.esn_data_end = ''
    },
    /**确认已修改排班 */
    ISchangevg(id) {
      let _this = this
      _this
        .$confirm('排班、磨削等已调整完毕?', '确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          axios
            .post(globalApiUrl, {
              au: 'ssamc',
              ap: 'api2018',
              ak: '',
              ac: 'pt_update_change_allot',
              id: id,
            })
            .then(function (response) {
              list = (response.data.data && response.data.data) || []
              _this.query()
              _this.$message({
                type: 'success',
                message: '操作成功!',
              })
            })
            .catch(function (error) {
              console.log(error)
            })
        })
        .catch(() => {
          // _this.$message({
          // 	type: 'info',
          // 	message: '已取消删除'
          // });
        })
    },
    /**打开发动机下 支持人员 */
    open_is_show_inspator_suport(data) {
      let _this = this
      _this.is_show_inspator_suport = true
      if (data.inspector == null) {
        data.inspector = ''
      }
      _this.show_inspator_suport_info = data
    },
    close_is_show_inspator_suport() {
      let _this = this
      _this.is_show_inspator_suport = false
    },
    /**判断是不是当天日期 当天之前不能编辑 */
    is_now_date(row, dataitem, title) {
      let new_date = getNowDay()
      return title >= new_date
    },
    clickShowHeader() {
      this.columnList = []
      this.showHeader = false
      this.tableMaxheight = 850
      this.inint_columns()
    },
    open_plan_batch(data) {
      let _this = this
      _this.quick_plan_data = []
      _this.batch_plan_type = data.str_task_type
      _this.is_show_batch_plan = true
      let parame = {
        ac: 'pt_get_template',
        ak: '',
        ap: '',
        au: 'ssamc',
      }
      $.ajax({
        url: globalApiUrl, //表示发送请求的路径
        type: 'post', //http请求方式
        accordion: 'true',
        data: parame,
        async: false, //同步请求，将锁住浏览器，用户其他操作必须等待请求完成才可以执行
        error: function () {
          alert('系统错误，请联系管理员!')
          return false
        },
        success: function (res) {
          if (res.code == 'success') {
            _this.templateList = res.data
          } else {
            _this.$message.error(res.text)
          }
        },
      })

      _this.batch_plan_data = Object.assign({}, data)
    },
    close_is_show_inspator_suport() {
      let _this = this
      _this.is_show_inspator_suport = false
    },
    /**判断是不是当天日期 当天之前不能编辑 */
    is_now_date(row, dataitem, title) {
      let new_date = getNowDay()
      return title >= new_date
    },
    getSelectName(id_task) {
      return this.tasksList
        .filter((item) => id_task.includes(item.value))
        .map((item) => item.label)
        .join()
    },
    //选择模板事件
    handleChangeTemplate(template) {
      let _this = this
      let temp = _this.templateList.filter((item) => item.id == template)[0]

      if (temp.str_flow != _this.input_str_flow || temp.str_type != _this.batch_plan_type) {
        this.$message.error('该模板和当前排班Flow、类型不匹配')
        return
      }
      _this.tasksList = _this.tasksList.filter((item) => item.flow == temp.str_flow)
      //根据模板获取模板对应得任务
      axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_template_task_by_template_id',
          id: template,
        })
        .then(function (response) {
          _this.quick_plan_data = response.data.data
            .map((item) => {
              const day = Number(item.str_sort) - 1
              item.id_task = item.id_task.split(',')
              return {
                ...item,
                dt_start_date: moment(_this.batch_plan_data.start_date).add(day, 'day').format('YYYY-MM-DD'),
              }
            })
            .sort((a, b) => a.str_sort - b.str_sort)
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    // 选择排班日期
    handleChangeStartDate(date) {
      this.quick_plan_data = this.quick_plan_data.map((item, index) => {
        const day = Number(item.str_sort) - 1
        return {
          ...item,
          dt_start_date: moment(date).add(day, 'day').format('YYYY-MM-DD'),
        }
      })
    },
    getTasksList() {
      let _this = this
      axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_quick_schedul_task_list',
        })
        .then((res) => {
          _this.tasksList = res.data.data.map((item) => {
            return {
              value: item.id_task,
              label: item.str_task_name,
              flow: item.str_flow,
            }
          })
        })
    },

    hasDuplicateString(arr, key) {
      const uniqueStrings = new Set()
      return arr.some((item) => {
        const stringToCheck = item[key]
        if (uniqueStrings.has(stringToCheck)) {
          return true
        }
        uniqueStrings.add(stringToCheck)
        return false
      })
    },
    //保存批量排班
    save_batch_plan() {
      let quick_schedul_data = []
      let message = ''
      if (this.quick_plan_data.length == 0) {
        message = '请选择排班模板'
      }
      this.quick_plan_data.forEach((item) => {
        if (item.dt_start_date > this.batch_plan_data.end_date) {
          message = '排班日期超出分配日期'
          return
        }
        if (!item.id_task) {
          message = '排班任务不能为空'
          return
        }
        if (!item.dt_start_date) {
          message = '排班日期不能为空'
          return
        }
        quick_schedul_data.push({
          id_wo: this.batch_plan_data.id_wo,
          tasks: item.id_task,
          pt_dt: item.dt_start_date,
        })
      })
      const hasDuplicate = this.hasDuplicateString(quick_schedul_data, 'pt_dt')
      if (hasDuplicate) {
        message = '排班日期重复'
      }
      if (message != '') {
        this.$message.error(message)
      } else {
        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_save_quick_schedul',
            quick_schedul_data: quick_schedul_data,
            id_template: this.batch_plan_data.id_template,
          })
          .then((res) => {
            if (res.data.code == 'success') {
              this.$message.success('保存成功')
              this.is_show_batch_plan = false
              this.loadList()
            } else {
              this.$message.error(res.data.text)
            }
          })
      }
    },
  },

  template: /*html*/ `
    <div>
      <el-row>
        <el-form
          ref="searchForm"
          :model="searchParams"
          label-width="auto"
          class="demo-form-inline"
          label-position="right"
        >
          <el-row>
            <el-col :span="6">
              <el-form-item label="Date:">
                <el-date-picker
                  style="width: 100%"
                  clearable
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  v-model="searchParams.dt_date"
                  start-placeholder="起始日期"
                  end-placeholder="截止日期"
                  size="small"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="ESN:">
                <el-input v-model="searchParams.str_esn" size="small" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="1">
              <el-form-item>
                <el-button type="primary" size="small" @click="query" class="el-icon-search"> </el-button>
              </el-form-item>
            </el-col>
            <el-col :span="3" v-if="!showHeader">
              <el-form-item>
                <el-button size="small" type="primary" @click="showHeader = true;loadList()">展开</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="3" v-else>
              <el-form-item>
                <el-button type="primary" size="small" @click="clickShowHeader">收起</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>

      <el-row
        style="color: #fff;
    background-color: #5d7092; font-size: 14px;"
      >
        <el-row>
          <span>{{team_info.groupLeader}} {{team_info.teamType}}</span>&nbsp;&nbsp;&nbsp;&nbsp<span
            >标准工时：{{team_info.bashours}}H</span
          >
        </el-row>
        <el-row>
          <span>{{ exe_str(team_info.member)}}</span>
        </el-row>
      </el-row>

      <vxe-table
        v-if="reachtable && !showHeader"
        :data="tableData"
        ref="xTable"
        border
        :header-cell-class-name="isWeek"
        :header-cell-style="{color:'#fff',border:'0.01rem solid #fff'}"
        :max-height="tableMaxheight"
        :cell-class-name="activeclor"
        :row-config="{height: 150}"
        :column-config="{resizable: true,width:170}"
        :loading="loadingData"
      >
        <vxe-colgroup fixed="left">
          <vxe-column type="seq" title="序号" width="60" align="center"></vxe-column>
          <vxe-column field="esn" title="ESN" width="100" :class-name="changestatus" align="center">
            <template #default="{ row }">
              <el-row class="headtitle newheadtitle">
                <span style="font-size: 14px;font-weight: 600">{{row.esn}}</span>
              </el-row>
              <el-row class="boxBottom">
                <!-- 发动机下 操作区域 -->

                <el-col :span="12" class="span_btn">
                  <span title="查看支持检验" @click="open_is_show_inspator_suport(row)"
                    ><i class="el-icon-view self_icon_color_default"></i
                  ></span>
                </el-col>

                <el-col :span="12" class="span_btn">
                  <span v-if="row.changestatus=='1'" title="确认排班等调整完毕" @click="ISchangevg(row.id)"
                    ><i class="el-icon-check self_icon_color_default"></i
                  ></span>
                </el-col>

                <el-col :span="12" class="span_btn">
                  <span v-if="input_str_flow!='INSPECT'" title="批量排班" @click="open_plan_batch(row)"
                    ><i class="el-icon-document-add self_icon_color_default"></i
                  ></span>
                </el-col>
              </el-row>
            </template>
          </vxe-column>
          <vxe-column title="Type" field="str_task_type" width="60" align="center"></vxe-column>
          <vxe-column title="Start Date" field="start_date" align="center" width="86">
            <template v-slot:default="{ row,column,rowKey,columnKey }">
              <span>{{row.start_date}}</span>
              <el-divider></el-divider>
              <span>{{row.project_start}}</span>
            </template>
          </vxe-column>
          <vxe-column title="End Date" field="end_date" align="center" width="80">
            <template v-slot:default="{ row,column,rowKey,columnKey }">
              <span>{{row.end_date}}</span>
              <el-divider></el-divider>
              <span>{{row.project_end}}</span>
            </template>
          </vxe-column>
          <vxe-column title="TAT" field="tat" align="center" width="45"></vxe-column>
        </vxe-colgroup>

        <vxe-colgroup v-for="(item,index) in columnList" :key="index" :title="item.str_week">
          <vxe-column type="Isdata" :title="item.day">
            <template #default="{ row,column }">
              <div class="wrapbox">
                <!-- 组件内容 -->
                <div v-for="(dataitem,index) in row.plan" :key="dataitem.plan_date + index">
                  <!-- 绑定每日数据 有数据 -->
                  <div class="thbox" v-if="dataitem.plan_date == column.title" key="dataitem.plan_date + index +">
                    <!-- 有数据 -->
                    <div v-if="dataitem.task.length>0" :key="'A'+index">
                      <!-- task 包区域 -->
                      <div class="taskgroup" v-for="(task,index) in dataitem.task">
                        <div class="taskitem">
                          <!-- <span class='taskname'>{{task.taskname}}</span> -->

                          <el-popover placement="top-start" width="70" trigger="hover" :content="task.taskname">
                            <span slot="reference" style="width:70px" class="taskname omit_line"
                              >{{ task.taskname}}</span
                            >
                          </el-popover>

                          <el-popover
                            placement="top-start"
                            width="70"
                            trigger="hover"
                            :content="getteamstaff(task.teamstaff)"
                          >
                            <span slot="reference" style="width:70px" class="omit_line"
                              >{{ getteamstaff(task.teamstaff)}}{{ getteamstaff(task.sectionstaff)}}</span
                            >
                          </el-popover>
                        </div>
                      </div>

                      <!-- 操作区域 -->
                      <div class="boxBottom">
                        <el-col :span="8" class="span_btn">
                          <span @click="boxview(row,dataitem)">
                            <i class="el-icon-view"></i>
                          </span>
                        </el-col>
                        <el-col :span="8" class="span_btn">
                          <span
                            @click="boxedit(row,dataitem,column.title)"
                            v-if="is_now_date(row,dataitem,column.title)"
                          >
                            <i class="el-icon-edit"></i>
                          </span>
                          <span v-else> &nbsp;</span>
                        </el-col>
                        <el-col :span="8" class="span_btn">
                          <span>
                            <el-popover placement="bottom" width="50" trigger="hover">
                              <ul class="popover">
                                <li @click="opendilog(dataitem,'-2')">
                                  <span style="color:red">红色</span>
                                </li>
                                <li @click="opendilog(dataitem,'-1')">
                                  <span style="color:#FFD700">黄色</span>
                                </li>
                                <li @click="opendilog(dataitem,'1')">
                                  <span style="color:#67C23A">绿色</span>
                                </li>
                              </ul>
                              <template #reference>
                                <i class="el-icon-more"></i>
                              </template>
                            </el-popover>
                          </span>
                        </el-col>
                      </div>
                    </div>
                    <!-- 无数据 -->
                    <div class="addbox" v-if="dataitem.task.length==0" :key="'B'+index">
                      <!-- 根据test_start 和test_end 来进行判断是展示试车还是下面这些 -->
                       <!-- 如果在test_start 和test_end 之间 展示试车 -->
                       <div v-if="is_between_test_start_and_test_end(row, column.title)">
                       F4-2 试车
                       </div>
                       <!-- 如果不在test_start 和test_end 之间 展示下面这些 -->
                       <div v-else>
                      <!-- 不超过最终日期 -->
                      <div v-if="Endstaff(column.title, row.end_date) == '0'" :key="'C'+index">
                        <el-button
                          type="primary"
                          size="small"
                          @click="addPlan(column.title,row.id_wo,row)"
                          icon="el-icon-circle-plus-outline"
                          >add</el-button
                        >
                      </div>
                      <div
                        style=" line-height: 0;"
                        v-else-if="Endstaff(column.title,row.end_date) == '1' && team_info.teamType=='B1'"
                        :key="'D'+index"
                      >
                          Release
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </vxe-column>
        </vxe-colgroup>
      </vxe-table>

      <vxe-table
        border
        ref="xTable"
        tooltip-effect="dark"
        v-if="reachtable && showHeader"
        :header-cell-class-name="Isweek"
        :header-cell-style="{color:'#fff',border:'0.01rem solid #fff'}"
        max-height="850"
        :cell-class-name="activeclor"
        :auto-resize="false"
        :row-config="{height: 150}"
        :column-config="{resizable: true,width:170}"
        :loading="loading"
      >
        <vxe-colgroup fixed="left">
          <template #header="{}">
            <el-row> <span>RUE/RUMC/RULA/RUQA值班人员 </span></el-row>
          </template>
          <vxe-colgroup title="磨削(VG)">
            <vxe-colgroup fixed="left">
              <template #header="{}">
                <el-row> <span>磨削(HSG) </span></el-row>
              </template>
              <vxe-colgroup title="每日人力状态 （只填休假、MES等人员状态）" style="text-align: center;">
                <vxe-column type="seq" title="序号" width="60" align="center"></vxe-column>

                <vxe-column field="esn" title="ESN" width="100" :class-name="changestatus" align="center">
                  <template v-slot:default="{ row,column,rowKey,columnKey }">
                    <!--<el-button v-if="row.changestatus=='1'" class='circle-edit' type='text' circle  size='mini' icon='el-icon-check' @click='ISchangevg(row.id)'></el-button>  -->

                    <el-row class="headtitle newheadtitle">
                      <span
                        style="font-size: 14px;
                                font-weight: 600"
                        >{{row.esn}}</span
                      >
                    </el-row>
                    <el-row class="boxBottom">
                      <!-- 发动机下 操作区域 -->

                      <el-col :span="12" class="span_btn">
                        <span title="查看支持检验" @click="open_is_show_inspator_suport(row)"
                          ><i class="el-icon-view self_icon_color_default"></i
                        ></span>
                      </el-col>

                      <el-col :span="12" class="span_btn">
                        <span v-if="row.changestatus=='1'" title="确认排班等调整完毕" @click="ISchangevg(row.id)"
                          ><i class="el-icon-check self_icon_color_default"></i
                        ></span>
                      </el-col>
                    </el-row>
                  </template>
                </vxe-column>
                <vxe-column title="Start Date" field="start_date" align="center" width="86"> </vxe-column>
                <vxe-column title="End Date" field="end_date" align="center" width="80"> </vxe-column>
                <vxe-column title="TAT" field="tat" align="center" width="45"> </vxe-column>
              </vxe-colgroup>
            </vxe-colgroup>
          </vxe-colgroup>
        </vxe-colgroup>

        <vxe-colgroup v-for="(column_config ,index) in columns_config" :key="index">
          <template #header="{}">
            <el-row>
              <div class="headtitle" v-for="(item_title_0,index) in column_config.title_0">
                <span>{{item_title_0 }}</span>
              </div>
              <!--<span>{{ column_config.title_0}} </span>-->
            </el-row>
          </template>
          <vxe-colgroup>
            <template #header="{ column, columnIndex, $columnIndex, _columnIndex, $rowIndex}">
              <!--磨削(VG)-->
              <i
                v-if='column_config.title_vg_0.is_vg === "0"'
                class="el-icon-edit"
                @click="changeVg('',column_config.title)"
              ></i>
              <div
                class="headtitle"
                style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                v-for="(item,index) in column_config.title_vg_0.gingdata"
              >
                <i
                  style="cursor: pointer"
                  v-if='item.sm.filter(f => f.str_type === "VG").length > 0 && item.is_hide_edit=="0"'
                  class="el-icon-edit"
                  @click="changeVg(item.id_main,column_config.title)"
                ></i>
                <!-- 如果有数据并且可编辑就显示编辑按钮，有数据不可编辑 显示添加按钮 自己添加完成之后 添加按钮变成可编辑按钮-->
                <!-- <i v-else-if='!column_config.title_vg_0.is_new_flag' class="el-icon-edit" @click="changeVg('',column_config.title)"></i>-->
                <span
                  :title="item.sm.filter(f => f.str_type === 'VG').map(m => m.str_sm).join(',')"
                  v-if="item.sm.filter(f => f.str_type === 'VG').length > 0"
                  >{{item.str_esn }}{{item.str_team}}:
                </span>
                <span v-if='item.sm.filter(f => f.str_type === "VG").length > 0'>
                  {{item.sm.filter(f => f.str_type === "VG").map(m => m.str_sm).join(',')}}
                </span>
              </div>
            </template>
            <vxe-colgroup>
              <template #header="{ column, columnIndex, $columnIndex, _columnIndex, $rowIndex}">
                <!--磨削(HSG)-->
                <i
                  v-if='column_config.title_vg_0.is_hsg === "0"'
                  class="el-icon-edit"
                  @click="changeHsg('',column_config.title)"
                ></i>
                <div
                  class="headtitle"
                  style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"
                  v-for="(item,index) in column_config.title_vg_0.gingdata"
                >
                  <i
                    style="cursor: pointer"
                    v-if='item.sm.filter(f => f.str_type === "HSG").length > 0 && item.is_hide_edit=="0"'
                    class="el-icon-edit"
                    @click="changeHsg(item.id_main,column_config.title)"
                  ></i>
                  <!--   <i v-else-if='!column_config.title_vg_0.is_new_flag' class="el-icon-edit" @click="changeHsg('',column_config.title)"></i>-->
                  <span
                    :title="item.sm.filter(f => f.str_type === 'HSG').map(m => m.str_sm).join(',')"
                    v-if="item.sm.filter(f => f.str_type === 'HSG').length > 0"
                    >{{item.str_esn }}{{item.str_team}}:</span
                  >
                  <span v-if='item.sm.filter(f => f.str_type === "HSG").length > 0'>
                    {{item.sm.filter(f => f.str_type === "HSG").map(m => m.str_sm).join(',')}}
                  </span>
                </div>
                <!--				<el-button type='text' v-if="column_config.title_vg_0.is_hide_edit=='0'" circle  size='mini' icon='el-icon-edit' @click='changeHsg(column_config.title_vg_0.id_main,column_config.title)'></el-button>  -->
              </template>
              <vxe-colgroup>
                <template #header="{}">
                  <!--人力状态-->
                  <div class="headtitle" v-for="(item,index) in column_config.title_vg_1.statusdata">
                    <span>{{item.staff_type }}:</span>
                    <span v-for="(items,indexs) in item.staff"> {{items.staff_name}}, </span>
                  </div>
                  <i class="el-icon-edit" @click="changeesn(column_config.title_vg_1.id_main,column_config.title)"></i>
                  <!--					<el-button type='text' circle  size='mini' icon='el-icon-edit' @click='changeesn(column_config.title_vg_1.id_main,column_config.title)'></el-button> -->
                </template>
                <vxe-colgroup :title="column_config.title_g">
                  <!--<template #header="{}">
						<el-row> 222<span>{{column_config.title_g.toString() ? column_config.title_g.toString():'N/A'}} </span></el-row>
					</template>-->

                  <vxe-column type="Isdata" :title="column_config.title">
                    <template #default="{  row  }">
                      <div class="wrapbox">
                        <!-- 组件内容 -->
                        <div v-for="(dataitem,index) in row.plan" :key="dataitem.plan_date + index">
                          <!-- 绑定每日数据 有数据 -->
                          <div
                            class="thbox"
                            v-if="dataitem.plan_date == column_config.title"
                            key="dataitem.plan_date + index +"
                          >
                            <!-- 有数据 -->
                            <div v-if="dataitem.task.length>0" :key="'A'+index">
                              <!-- task 包区域 -->
                              <div class="taskgroup" v-for="(task,index) in dataitem.task">
                                <div class="taskitem">
                                  <!-- <span class='taskname'>{{task.taskname}}</span> -->

                                  <el-popover placement="top-start" width="70" trigger="hover" :content="task.taskname">
                                    <span slot="reference" style="width:70px" class="taskname omit_line"
                                      >{{ task.taskname}}</span
                                    >
                                  </el-popover>

                                  <el-popover
                                    placement="top-start"
                                    width="70"
                                    trigger="hover"
                                    :content="getteamstaff(task.teamstaff)"
                                  >
                                    <span slot="reference" style="width:70px" class="omit_line"
                                      >{{ getteamstaff(task.teamstaff)}}{{ getteamstaff(task.sectionstaff)}}</span
                                    >
                                  </el-popover>
                                </div>
                              </div>

                              <!-- 操作区域 -->
                              <div class="boxBottom">
                                <el-col :span="8" class="span_btn">
                                  <span @click="boxview(row,dataitem)">
                                    <i class="el-icon-view"></i>
                                  </span>
                                </el-col>
                                <el-col :span="8" class="span_btn">
                                  <span
                                    @click="boxedit(row,dataitem,column_config.title)"
                                    v-if="is_now_date(row,dataitem,column_config.title)"
                                  >
                                    <i class="el-icon-edit"></i>
                                  </span>
                                  <span v-else> &nbsp;</span>
                                </el-col>
                                <el-col :span="8" class="span_btn">
                                  <span>
                                    <el-popover placement="bottom" width="50" trigger="hover">
                                      <ul class="popover">
                                        <li @click="opendilog(dataitem,'-2')">
                                          <span style="color:red">红色</span>
                                        </li>
                                        <li @click="opendilog(dataitem,'-1')">
                                          <span style="color:#FFD700">黄色</span>
                                        </li>
                                        <li @click="opendilog(dataitem,'1')">
                                          <span style="color:#67C23A">绿色</span>
                                        </li>
                                      </ul>
                                      <template #reference>
                                        <i class="el-icon-more"></i>
                                      </template>
                                    </el-popover>
                                  </span>
                                </el-col>
                              </div>
                            </div>
                            <!-- 无数据 -->
                            <div class="addbox" v-if="dataitem.task.length==0" :key="'B'+index">
                              <!-- 如果在test_start 和test_end 之间 展示试车 -->
                              <div v-if="is_between_test_start_and_test_end(row, column_config.title)">
                                试车
                              </div>
                              <!-- 不在test_start 和test_end 之间 展示下面这些 -->    
                              <div v-else>
                                <!-- 不超过最终日期 -->
                                <div v-if="Endstaff(column_config.title, row.end_date) == '0'" :key="'C'+index">
                                <el-button
                                  type="primary"
                                  size="small"
                                  @click="addPlan(column_config.title,row.id_wo,row)"
                                  icon="el-icon-circle-plus-outline"
                                  >add</el-button
                                >
                              </div>
                              <div
                                style=" line-height: 0;"
                                v-else-if="Endstaff(column_config.title,row.end_date) == '1' && team_info.teamType=='B1'"
                                :key="'D'+index"
                              >
                                Release
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </vxe-column>
                </vxe-colgroup>
              </vxe-colgroup>
            </vxe-colgroup>
          </vxe-colgroup>
        </vxe-colgroup>
      </vxe-table>

      <el-dialog title="Reason" :visible.sync="dialogVisible" width="35%">
        <el-form
          ref="formPartA"
          :model="formPartA"
          :rules="rulesformPartA"
          label-position="right"
          size="small"
          label-width="auto"
        >
          <el-form-item label="Remark" prop="reason">
            <el-input type="textarea" controls-position="right" v-model="formPartA.reason" style="width:100%" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="toggleEditDialog" size="default">Save</el-button>
            <el-button size="default" @click="dialogVisible=false">Close</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <div v-if="isteamplanshow">
        <y-common-team-plan-form
          @get_back_close_dialog="close_edit_dialog()"
          @close_dialog="close_self_dialog"
          :input_plan_date="addDate"
          :input_id_wo="addIdwo"
          :input_id_main="id_main"
          :input_is_edit="input_is_edit"
          :str_flow="input_str_flow"
          :str_en_type="input_str_esn_type"
          :input_group_type="input_group_type"
          :input_str_esn="str_esn"
          :input_esn_date_end="esn_data_end"
          :task_type="task_type"
          :id_site="id_site"
        ></y-common-team-plan-form>
      </div>
      <div v-if="isteamplanshowsee">
        <y-common-team-plan-form-see
          @get_back_close_dialog="isteamplanshowsee=false"
          @close_dialog="close_self_dialog"
          :input_id_main="id_main"
          :input_is_editsee="input_is_editsee"
        ></y-common-team-plan-form-see>
      </div>

      <div v-if="isteamplanshowvg">
        <y-f4-1-vg-sm_edit
          :is_show_vg="is_show_vg"
          @get_back_close_vg_dialog="get_back_close_vg_dialog()"
          :input_vg_plan_date="input_vg_plan_date"
          :input_vg_plan_date_end="input_vg_plan_date_end"
          :input_vg_plan_date_start="input_vg_plan_date_start"
          :input_id_main="id_main"
          :input_str_flow="str_flow_now"
        ></y-f4-1-vg-sm_edit>
      </div>
      <div v-if="isteamplanshowvg1">
        <y-f4-1-vg-status_edit
          @get_back_close_vg_dialog="get_back_close_vg_dialog()"
          :input_vg_plan_date="input_vg_plan_date"
          :input_vg_plan_date_end="input_vg_plan_date_end"
          :input_vg_plan_date_start="input_vg_plan_date_start"
          :input_id_main="id_main"
        ></y-f4-1-vg-status_edit>
      </div>

      <!--检验支持人员信息-->
      <el-dialog :visible.sync="is_show_inspator_suport" class="self_dialog" width="30%">
        <el-card class="box-card">
          <div slot="header" class="clearfix" style="font-size: 16px;">
            <span>检验、支持人员 Inspector Supporter</span>
          </div>

          <div class="text item" style="padding: 5px 0;font-size: 14px;">
            <span style="color: #5b8ff9;">{{'检验:' +"&nbsp;&nbsp;"}}</span> {{"&nbsp;&nbsp;"+
            show_inspator_suport_info.inspector}}
          </div>

          <template v-for="o in  show_inspator_suport_info.supportor">
            <div class="text item" style="padding: 5px 0;font-size: 14px;">
              <span style="color: #5b8ff9;">{{o.str_type +"&nbsp;&nbsp;"}}</span> {{o.staffnames}}
            </div>
          </template>
        </el-card>
        <div slot="footer">
          <el-button
            class="topButton_right"
            style="margin-left:20px;"
            size="small"
            type="danger"
            @click="close_is_show_inspator_suport()"
            >Close
          </el-button>
        </div>
      </el-dialog>
      <!--批量排班-->
      <el-dialog :visible.sync="is_show_batch_plan" class="self_dialog" width="60%" title="批量排班" append-to-body>
        <div class="flex flex-col justify-center border">
          <!-- <div class="mx-4 my-2 text-xl">批量排班</div> -->
          <!-- 分割线 -->
          <div class="mb-4 border-b"></div>
          <el-form label-width="100px" :model="batch_plan_data">
            <el-form-item label="模板" prop="id_template">
              <el-select
                v-model="batch_plan_data.id_template"
                clearable
                filterable
                placeholder="Please select Template"
                @change="handleChangeTemplate"
              >
                <el-option
                  v-for="item in templateList"
                  :key="item.id"
                  :label="item.str_name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="排班开始日期" prop="start_date">
              <el-date-picker
                v-model="batch_plan_data.start_date"
                type="date"
                placeholder="选择日期"
                clearable
                @change="handleChangeStartDate"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="发动机" prop="id_wo">
              <el-input v-model="batch_plan_data.esn" disabled></el-input>
            </el-form-item>
          </el-form>
        </div>
        <vxe-table
          height="400"
          border
          show-overflow
          keep-source
          ref="tableRef"
          :data="quick_plan_data"
          :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
        >
          <vxe-column field="id_task" title="Task Name" :edit-render="{ autofocus: '.myinput'}">
            <template #default="{ row }">
              <span>{{ getSelectName(row.id_task) }}</span>
            </template>
            <template #edit="{ row }">
              <vxe-select
                class="myinput"
                v-model="row.id_task"
                multiple
                clearable
                placeholder="Please select"
                filterable
                style="width: 80%"
              >
                <vxe-option
                  v-for="item in tasksList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></vxe-option>
              </vxe-select>
            </template>
          </vxe-column>
          <vxe-column field="dt_start_date" title="Dt Plan" :edit-render="{ autofocus: '.myinput'}">
            <template #edit="scope">
              <!-- <vxe-input type="date" v-model="scope.row.dt_start_date" class="myinput"></vxe-input> -->
              <input type="date" class="myinput" v-model="scope.row.dt_start_date" 
                style="padding: 5px;border-radius: 5px;border: 1px solid #dcdfe6;"
              />
            </template>
          </vxe-column>
        </vxe-table>
        <div slot="footer">
          <el-button
            class="topButton_right"
            style="margin-left:20px;"
            size="small"
            type="danger"
            @click="save_batch_plan()"
            >Save
          </el-button>
        </div>
      </el-dialog>
    </div>
  `,
})
