import { useDotColumn } from './useDotColumn.js'

const { reactive } = Vue
/**
 * @description 红 蓝 绿 紫 四个点的表格 以及散点图的点的点击事件内的表格数据
 * @date 2024年8月7日10:38:47
 */
export function useDotTable() {
  const { dotTableColumns } = useDotColumn()
  const tableState = reactive({
    data: null,
    columns: dotTableColumns,
  })

  // * 获取表格数据
  const getDotTableData = (id) => {
    const params = {

    }
  }

  return {
    tableState,
    getDotTableData,
  }
}
