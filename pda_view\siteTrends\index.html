<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset='UTF-8'>
    <meta content='width=640, user-scalable=no' name='viewport'>
    <meta content='ie=edge' http-equiv='X-UA-Compatible'>
    <meta content='telephone=no' name='format-detection'>
    <meta content='yes' name='apple-mobile-web-app-capable'>
    <meta content='black' name='apple-mobile-web-app-status-bar-style'>
    <!-- 引入tailwindcss -->
    <link href='../../assets/css/tailwind.css' rel='stylesheet'>
    <!--    全部样式-->
    <link href='../../assets/css/index.css' rel='stylesheet'>
    <!--    引入element-plus的样式-->
    <link href='../../assets/element-plus@2.5.5/index.css' rel='stylesheet'>
    <!--    引入drawer的样式-->
    <link href='../../assets/css/drawer.css' rel='stylesheet'>
    <!--    引入vxe-table的样式-->
    <link href='../../assets/vxe-table/style.css' rel='stylesheet'>
    <link href='../../assets/css/home.css' rel='stylesheet'>
    <!--    引入VUE-->
    <script src='../../assets/vue@3.4.15/vue.global.prod.js'></script>
    <!--    引入vxe-table组件-->
    <script src='../../assets/vxe-table/xe-utils.js'></script>
    <script src='../../assets/vxe-table/vxe-table.js'></script>
    <!--    引入element-plus-->
    <script src='../../assets/element-plus@2.5.5/index.js'></script>
    <!--    引入element-plus的语言包-->
    <script src='../../assets/element-plus@2.5.5/lang/zh-cn.js'></script>
    <!--  引入element-plus-icon-->
    <script src='../../assets/icons-vue@2.3.1/index.iife.min.js'></script>
    <!--    引入axios-->
    <script src='../../assets/axios@1.6.7/axios.min.js'></script>
    <!--    引入@vueuse/core-->
    <script src='../../assets/@vueuse/shared@10.7.2/index.iife.min.js'></script>
    <script src='../../assets/@vueuse/core@10.7.2/index.iife.min.js'></script>
    <!--    引入moment-->
    <script src='../../assets/moment/moment.min.js'></script>
    <!--    引入echarts-->
    <script src='../../assets/echarts@5.5.0/echarts.min.js'></script>
    <title>站点趋势图</title>
  </head>
  <body>
    <div id="app">
        <site-trends></site-trends>
    </div>
  </body>
  <script type='module'>
    import SiteTrends from './index.js'

    const { createApp } = Vue;

    const app = createApp({
      components: {
        // 组件
        SiteTrends
      },
    });
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component);
    }
    app.use(ElementPlus, {
      locale: ElementPlusLocaleZhCn
    });
    app.use(VXETable);
    app.use(XEUtils);
    app.mount('#app');
  </script>
</html>
