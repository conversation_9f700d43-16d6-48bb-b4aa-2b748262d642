import { TYPE_CONFIG } from '../config/typeConfig.js'
export function usePartStatistics(drawerState, tableState) {
  /**
   *  转包无EDD
   */
  const onInt109Click = (row, field) => {
    const config = TYPE_CONFIG[field]
    if (!config) return
    Object.assign(drawerState, {
      visible: true,
      type: config.type,
      title: config.title,
      idWo: row.id_wo,
      int_type: field,
    })
  }
  const onFooterInt109Click = (column, field) => {
    const config = TYPE_CONFIG[field]
    if (!config) return
    let dataTableIdWo = tableState.data
      .filter((item) => item[field] > 0)
      .map((item) => item.id_wo)
      .join(',') // 多个ID_wo用逗号分隔
    Object.assign(drawerState, {
      visible: true,
      type: config.type,
      title: config.title,
      idWo: dataTableIdWo,
      int_type: field,
    })
  }

  return {
    onInt109Click,
    onFooterInt109Click,
  }
}
