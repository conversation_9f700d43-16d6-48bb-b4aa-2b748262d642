Vue.component("y-offer-import-excel", {
	props: ["input_id_main", "input_type"], // input_type 模板类型
 	data: function () {
		return {
			flag: true,
			is_show_load_click: true,
			str_batch: "", // 批次
			// 附件列表
			fileList: [],
			// 允许的文件类型
			fileType: ["XLSX"],
			// 运行上传文件大小，单位 M
			fileSize: 10,
			// 附件数量限制
			fileLimit: 3,
			is_show_succes: false,
			check_colnums: [
				{
					type: 1,
					api: "offer_import_clp_excel", // 导入CLP
					templatename:"Sister team和人员关系导入模板.xlsx",
					colnum: [
						{ colun_name: "SISTER TEAM", db_field: "str_sysname" },
						{ colun_name: "Team Leader 编号", db_field: "str_teamleader_code" },
						{ colun_name: "Dept 编号", db_field: "str_dept_code" },
						{ colun_name: "团队分类", db_field: "str_team_category" },
						{ colun_name: "FLOW", db_field: "str_flow" },
						{ colun_name: "STAFF'S NO.", db_field: "str_staff_code" },
						{ colun_name: "员工姓名", db_field: "str_staff_name" },
					],
				},
			], // 数据列检查
		};
	},

	mounted() { },
	created: function () { },

	methods: {
		/**
		 * 导出模板
		 */
		exportExcelTemplate() {
			let _this=this;
			let  filename =_this.check_colnums.find(x=>x.type==_this.input_type)?.templatename; //文件名称
			let  data = [_this.check_colnums.find(x=>x.type==_this.input_type)?.colnum?.map((column)=>{
				return column.colun_name
			})];//数据，一定注意需要时二维数组
	
			const merge = [
				// 纵向合并，范围是第1列的行1到行2
				// { s: { r: 0, c: 0 }, e: { r: 2, c: 0 } },
				// // 纵向合并，范围是第2列的行1到行2
				// { s: { r: 0, c: 1 }, e: { r: 1, c: 1 } },
				// // 横向合并，范围是第1行的列3到列5
				// { s: { r: 0, c: 2 }, e: { r: 0, c: 4 } },
				// // 横向合并，范围是第1行的列6到列11
				// { s: { r: 0, c: 5 }, e: { r: 0, c: 10 } },
				// // 横向合并，范围是第1行的列12到列17
				// { s: { r: 0, c: 11 }, e: { r: 0, c: 16 } },
				// // 横向合并，范围是第1行的列18到列23
				// { s: { r: 0, c: 17 }, e: { r: 0, c: 22 } },
				// // 横向合并，范围是第1行的列24到列29
				// { s: { r: 0, c: 23 }, e: { r: 0, c: 28 } },
				// // 横向合并，范围是第1行的列30到列35
				// { s: { r: 0, c: 29 }, e: { r: 0, c: 34 } }
			];

			var ws_name = "Sheet1"; //Excel第一个sheet的名称
			var wb = XLSX.utils.book_new(),
				ws = XLSX.utils.aoa_to_sheet(data);
			ws["!merges"] = merge;
			XLSX.utils.book_append_sheet(wb, ws, ws_name); //将数据添加到工作薄
			XLSX.writeFile(wb, filename); //导出Excel
		},
		on_change(file, fileList) {
			if (
				this.fileType !== null &&
				this.fileType !== "" &&
				!this.fileType.includes(
					this.lastSubstring(file.name.toUpperCase(), ".")
				)
			) {
				this.$message({
					showClose: true,
					message: "错了哦，文件类型不可上传",
					type: "error",
				});
				let idx = fileList.findIndex((item) => item.uid === file.uid); // 关键作用代码，去除文件列表失败文件（uploadFiles为el-upload中的ref值）

				fileList.splice(idx, 1); // 关键作用代码，去除文件列表失败文件
			}
		},
		// 文件上传之时（文件不会上传成功，在触发下一步操作前截取文件信息）
		beforeUpload(file) {
			if (file) {
			}
		},
		// 清空表单
		clear() {
			// 清空附件
			this.$refs.upload.clearFiles();
		},

		// 附件检查
		// 检查附件是否属于可上传类型
		// 检查附件是否超过限制大小
		checkFile() {
			this.flag = true;
			let tip = "";
			let files = this.$refs.upload.uploadFiles;
			files.forEach((item) => {
				// 文件过大
				if (
					this.size !== null &&
					this.size !== "" &&
					item.size > this.fileSize * 1024 * 1024
				) {
					this.flag = false;
					tip = " File more than" + this.fileSize + "M";
				}
				// 文件类型不属于可上传的类型
				if (
					this.fileType !== null &&
					this.fileType !== "" &&
					!this.fileType.includes(
						this.lastSubstring(item.name.toUpperCase(), ".")
					)
				) {
					this.flag = false;
					tip = " The file type cannot be uploaded";
				}
			});
			if (!this.flag) {
				this.$message({
					showClose: true,
					message: "Error，" + tip,
					type: "error",
				});
			}
		},

		// 提交附件
		submitUpload() {
			if (this.flag) {
				this.$refs.upload.submit();
			} else {
				console.log("取消上传");
			}
		},
		// 自定义文件上传方法
		uploadFile(file) {
			let _this = this;
			let is_check_template_succse=true; // 模板是否检查通过
			if (!_this.flag) return;
		
			// 准备解析Excel 数据
			let reader = new FileReader();
			reader.onload = function (e) {
				let data_type = [];
				let data = e.target.result;
				let wb = XLSX.read(data, {
					type: "binary",
					cellDates:true,
					cellText:false
				});

				wb &&
					wb.SheetNames &&
					wb.SheetNames.forEach((sheetanme) => {
						const sheetArry = XLSX.utils.sheet_to_json(wb.Sheets[sheetanme],{defval:'',raw:false,dateNF:'yyyyy-mm-dd'});
						// 检查列顺序
						let first_colnum_t = sheetArry && sheetArry[0]; // 取第一行判断模板
						if (first_colnum_t && is_check_template_succse) {
							Object.keys(first_colnum_t).forEach((key, index) => {
								let colnums_template = _this.check_colnums.find(x => x.type == _this.input_type)?.colnum; // 模板列

								if (key !== colnums_template[index]?.colun_name) {
									_this.$message({
										message: '模板不是最新的，请找管理员确认模板',
										type: 'warning'
									});
									is_check_template_succse=false;
									return;
								}
							});
						}
					
						sheetArry && sheetArry.forEach(json_data => {
							let itemMap = new Map();

							Object.keys(json_data).forEach((key, index) => {
								let colnums_template = _this.check_colnums.find(x => x.type == _this.input_type)?.colnum; // 模板列

								if (key == colnums_template[index]?.colun_name) {
									itemMap.set(colnums_template[index].db_field, json_data[key]);
								}
							});

							data_type.push(itemMap);

						});


					});

				// 接口保存
				const loading = _this.$loading({
					Lock: true,
					text: "Loading, please wait",
					spinner: 'el-icon-loading',
					background: "rgba(0,0,0,0.35)",
				});
				let savedata=_this.map2json(data_type);
				axios
                .post(globalApiUrl, {
                    au: "ssamc",
                    ap: "api2018",
                    ak: "",
                    ac: _this.check_colnums.find(x => x.type == _this.input_type)?.api,
                    post_data:savedata ,
                })
                .then(function (response) {
					if (response.data.code == "success") {
						_this.$message({
							title: "Success",
							duration: 2000,
							type: "success",
							message: "Import success",
							showClose: false
						});
						loading.close();
					}else{
						_this.$message({
							title: "Error",
							duration: 2000,
							type: "error",
							message:  response.data.data,
							showClose: false
						});
					}
                  console.log(response);
                })
                .catch(function (error) {
                    console.log(error);
					loading.close();
                });
			
				
			};
			reader.readAsBinaryString(file.file);
		},

		pad2(n) {
			return n < 10 ? "0" + n : n;
		},

		generateTimeReqestNumber() {
			var date = new Date();
			return (
				date.getFullYear().toString() +
				this.pad2(date.getMonth() + 1) +
				this.pad2(date.getDate()) +
				this.pad2(date.getHours()) +
				this.pad2(date.getMinutes()) +
				this.pad2(date.getSeconds())
			);
		},
		// 移除附件
		handleRemove(file, fileList) {
			console.log("移除附件...");
		},

		// 附件上传失败，打印下失败原因
		onError(err) {
			this.$message({
				showClose: true,
				message: "错了哦，附件上传失败",
				type: "error",
			});
		},

		// 字符串重组
		strRebuild(str) {
			return strRebuild(str);
		},
		// 字符串相关工具类
		// 数组根据分隔符重组为字符串
		strRebuild(arr, split) {
			if (
				arr === undefined ||
				arr === null ||
				!(arr instanceof Array) ||
				arr.length === 0
			) {
				return "";
			}
			if (split === undefined || split === null) {
				split = "，";
			}
			var str = "";
			arr.forEach((v, i) => {
				if (i === arr.length - 1) {
					str = str + v;
				} else {
					str = str + v + split;
				}
			});
			return str;
		},
		// 截取最后一个特定字符后面的字符串
		lastSubstring(str, split) {
			if (
				str === undefined ||
				str === null ||
				split === undefined ||
				split === null
			) {
				return "";
			}
			return str.substring(str.lastIndexOf(split) + 1);
		},
		handlePreview(file) { },
		child_f() { },
		map2json(list) {
			let datalist = [];
			list.forEach((item) => { 
				let json={};

				for (let [k,v] of item){
					json[k]=v;
				}
				datalist.push(json)
				
			});
			return datalist;
		}
	},
	template: `
    <div>
    <el-upload ref='upload'  :on-remove='handleRemove' :on-error='onError' :file-list='fileList' :auto-upload='false' :http-request='uploadFile' action='' :on-change="on_change" class="upload-demo">
            <el-button slot='trigger' size='small' type='primary'>Select file</el-button>
            <el-button  v-if="is_show_load_click" style='margin-left: 10px;' size='small' type='success' @click='submitUpload'>Uploading to the server</el-button>
			<el-button  v-if="is_show_load_click" style='margin-left: 10px;' size='small' type='warning' @click='exportExcelTemplate'>Template</el-button>
			<div slot='tip' class='el-upload__tip'>Support  upload {{ strRebuild(fileType) }} format，and no more than {{ fileSize }}M</div>
        </el-upload>

    </div>
    `,
});
