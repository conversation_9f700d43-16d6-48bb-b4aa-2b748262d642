import { getSmConfigData, saveEngineTaskBySm } from '../api/index.js'

const { defineComponent, ref, onMounted } = Vue
const { useVModel } = VueUse
const { ElMessage } = ElementPlus

export const SelectSmDialog = defineComponent({
  name: 'SelectSmDialog',
  props: {
    modelValue: { type: Boolean, default: false },
    currentAddRow: { type: Object, default: null },
    idWo: { type: String, default: '' },
    strFlow: { type: String, default: '' },
  },
  emits: ['update:modelValue', 'sm-selected'],
  setup(props, { emit }) {
    // 状态管理
    const dialogVisible = useVModel(props, 'modelValue', emit)
    const smConfigList = ref([])
    const selectedSm = ref('')
    const loading = ref(false)

    // 获取SM配置数据
    const getSmConfig = async () => {
      try {
        loading.value = true
        const res = await getSmConfigData({str_group:props.currentAddRow?.str_name || '',id_wo: props.idWo})
        smConfigList.value = res || []
      } catch (error) {
        console.error('获取SM配置数据失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 保存SM选择
    const handleSave = async () => {
      if (!selectedSm.value) {
        ElMessage.warning('请选择SM配置')
        return
      }

      try {
        loading.value = true
        const params = {
          id_wo: props.idWo,
          str_flow: props.strFlow,
          str_sm: selectedSm.value,
        }

        await saveEngineTaskBySm(params)
        ElMessage.success('保存成功')

        // 通知父组件刷新数据
        emit('sm-selected')

        // 关闭弹框
        dialogVisible.value = false

        // 重置选择
        selectedSm.value = ''
      } catch (error) {
        console.error('保存失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 关闭弹框
    const handleClose = () => {
      dialogVisible.value = false
      selectedSm.value = ''
    }

    // 监听弹框显示状态
    const handleDialogOpen = () => {
      if (dialogVisible.value) {
        getSmConfig()
      }
    }

    onMounted(() => {
      if (dialogVisible.value) {
        getSmConfig()
      }
    })

    return {
      dialogVisible,
      smConfigList,
      selectedSm,
      loading,
      handleSave,
      handleClose,
      handleDialogOpen,
    }
  },
  template: /*html*/ `
    <el-dialog 
      class="common-dialog" 
      v-model="dialogVisible" 
      title="选择SM配置" 
      width="600px"
      @open="handleDialogOpen"
    >
      <div class="p-4" v-loading="loading">
        <el-form label-width="100px">
          <el-form-item label="SM配置" required >
            <el-select 
            filterable
              v-model="selectedSm" 
              placeholder="请选择SM配置" 
              style="width: 100%"
              :disabled="loading"
            >
              <el-option 
                v-for="item in smConfigList" 
                :key="item.str_code" 
                :label="item.str_code" 
                :value="item.str_code"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button 
            type="primary" 
            @click="handleSave"
            :loading="loading"
            :disabled="!selectedSm"
          >
            保存
          </el-button>
          <el-button 
            @click="handleClose"
            :disabled="loading"
          >
            取消
          </el-button>
        </div>
      </template>
    </el-dialog>
  `,
})
