<!-- StringBuilder strhtml = new StringBuilder(); strhtml.Append(@" -->
<!DOCTYPE html>
<html lang='en'>

<head>
  <meta charset='UTF-8'>
  <meta http-equiv='X-UA-Compatible' content='IE=edge'>
  <meta name='viewport' content='width=device-width, initial-scale=1.0'>
  <title>BSI检验汇总列表组件</title>
  <script src='../../../assets/jquery/jquery.js'></script>
  <script src='../../../assets/vue/vue.js'></script>
  <script src='../../../assets/element/index.js'></script>
  <script src='../../../assets/axios/axios.min.js'></script>
  <script src='../../../03team_new/comm/api_environment.js'></script>
  <script src='../../../assets/tools/helper.js'></script>
  <script src='../../../assets/moment/moment.min.js'></script>
  <script src='../../../assets/echarts/echarts.min.js'></script>
  <!-- 引入脚本 -->
  <script src='../../../assets/vxe-table-v3/xe-utils.js'></script>
  <script src='../../../assets/vxe-table-v3/<EMAIL>'></script>
  <link rel='stylesheet' href='../../../assets/element/index.css'>
  <link rel='stylesheet' href='../../../assets/css/el.dialog.css'>
  <link rel='stylesheet' href='../../../assets/css/comm.self.css'>
  <link rel='stylesheet' href='../../../assets/css/shift.page.component.css'>
  <link rel='stylesheet' href='../../../assets/vxe-table-v3/style.css'>
  <!-- 内部组件 -->
  <script src='../../04summary/components/f11cfm.common.page.summary.edit.js'></script>
  <script src='../../03_team_plan/components/common.team.plan.js'></script>
  <script src='../../03_team_plan/components/common.team.plan.see.js'></script>
</head>

<body>
  <div id='app'>
    <!-- is_edit：是否编辑;input_str_esn_type:机型 -->
    <y-shift-summary-f11cfm-page-edit input_str_flow='INSPECT' :is_edit_page='false' input_group_type='F1-1-INSPECT' ></y-shift-summary-f11cfm-page-edit>
  </div>

  <script>
    var vue1 = new Vue({
      el: '#app',
      data: function () {
        return {
          id_main: ''
        }
      },
      methods: {
        backCall() {

        }
      }
    })
  </script>
</body>


</html>
<!-- "); arg.redata = strhtml.ToString(); return arg.redata; -->