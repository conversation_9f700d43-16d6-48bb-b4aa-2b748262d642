import { getStaffList, getF2TaskTypeList } from '../api/index.js'

const { ref, onMounted } = Vue
export const useDropdownOptions = () => {
  const staffOptions = ref([])
  const getStaffOptions = async () => {
    const res = await getStaffList()
    staffOptions.value = res.map((item) => ({
      value: item.id,
      label: item.str_name,
    }))
  }
  const taskTypeOptions = ref([])
  const getTaskTypeOptions = async () => {
    const res = await getF2TaskTypeList()
    taskTypeOptions.value = res.map((item) => ({
      value: item.str_value,
      label: item.str_key,
    }))
  }

  onMounted(() => {
    getStaffOptions()
    getTaskTypeOptions()
  })

  return {
    staffOptions,
    taskTypeOptions,
  }
}
