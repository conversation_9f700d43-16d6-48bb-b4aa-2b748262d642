export default /*html*/ `
  <div class="h-screen p-2" ref="tableContainerRef">
    <!-- 操作按钮 -->
    <div class="mb-4 flex gap-2">
      <el-button type="primary" class="bg-green-500" @click="handleAdd">新增</el-button>
    </div>

    <!-- 表格 -->
    <HtVxeTable
      ref="xTableRef"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :height="tableHeight"
      @filterChange="handleFilterChange"
    >
      <template #checkbox>
        <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
      </template>
      <template #operation>
        <vxe-column title="操作" min-width="280" fixed="right">
          <template #default="{ row }">
            <div class="flex gap-2">
              <span class="cursor-pointer text-blue-500 hover:text-blue-600" @click="handleView(row)">查看</span>
              <span
                class="cursor-pointer text-blue-500 hover:text-blue-600"
                v-if="row.int_substatus==0 || row.int_substatus==-1 || row.int_substatus==-99"
                @click="handleCommit(row)"
              >
                提交审批
              </span>
              <span
                class="cursor-pointer text-blue-500 hover:text-blue-600"
                v-if="row.int_substatus==0 || row.int_substatus==-1 || row.int_substatus==-99"
                @click="handleEdit(row)"
              >
                修改
              </span>
              <span
                class="cursor-pointer text-blue-500 hover:text-blue-600"
                @click="handleWithdraw(row)"
              >
                撤回
              </span>
              <span
                class="cursor-pointer text-red-500 hover:text-red-600"
                v-if="row.int_substatus==0 || row.int_substatus==-1 || row.int_substatus==-99"
                @click="handleDelete(row)"
              >
                删除
              </span>
            </div>
          </template>
        </vxe-column>
      </template>
    </HtVxeTable>

    <!-- 分页组件 -->
    <div class="mt-4 flex justify-end">
      <vxe-pager
        background
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :layouts="['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
      ></vxe-pager>
    </div>

    <!-- 新增/编辑弹窗表单 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px" destroy-on-close class="common-dialog">
      <el-form label-width="100px">
        <el-form-item label="开始日期" required>
          <el-date-picker
            v-model="formData.dt_borrow_start"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
             @change="handleStartDateChange"
          />
        </el-form-item>
        <el-form-item label="借调天数" required>
          <el-input-number  v-model="formData.int_borrow_day" :min="1" :max="10" style="width: 100%" @change="handleBorrowDayChange"/>
        </el-form-item>
        <el-form-item label="结束日期">
          <el-input disabled v-model="formData.dt_borrow_end"  />
        </el-form-item>
        <el-form-item label="实际班次">
          <el-select
            v-model="formData.id_shift"
            placeholder="请选择班次"
            style="width: 100%"
            clearable
          >
            <el-option v-for="option in shiftOptionList" :key="option.id" :label="option.str_name" :value="option.id" />
          </el-select>
        </el-form-item>
         <el-form-item label="人员" required>
          <el-select v-model="formData.staffs" placeholder="请选择人员" multiple style="width: 100%" filterable @change="handleStaffChange">
            <el-option v-for="item in staffOptionList" :key="item.value" :label="item.str_team +'-'+ item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="From Team" required>
          <el-select
            v-model="formData.id_from_team"
            placeholder="请选择Team"
            style="width: 100%"
            filterable
            @change="handleTeamChange"
          >
            <el-option v-for="item in teamOptionList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="To Team" required>
          <el-select
            v-model="formData.id_to_team"
            placeholder="请选择Team"
            style="width: 100%"
            filterable
          >
            <el-option v-for="item in teamOptionList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="能力类型" required>
          <el-select v-model="formData.skillTypes" placeholder="请选择人员能力" multiple style="width: 100%" filterable >
            <el-option v-for="item in skillTypeOptionList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="能力" required>
          <el-select v-model="formData.skills" placeholder="请选择人员能力" multiple style="width: 100%" filterable @change="handleSkillChange">
            <el-option v-for="item in skillOptionList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button type="primary" @click="handleSaveAndCommit">保存并提交</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="详情" width="500px" destroy-on-close>
      <el-descriptions v-if="detailData" :column="1" border>
        <el-descriptions-item label="开始日期">{{ detailData.dt_borrow_start }}</el-descriptions-item>
        <el-descriptions-item label="借调天数">{{ detailData.int_borrow_day }}</el-descriptions-item>
        <el-descriptions-item label="结束日期">{{ detailData.dt_borrow_end }}</el-descriptions-item>
        <!-- <el-descriptions-item label="From Team">{{ detailData.str_from_team }}</el-descriptions-item> -->
        <el-descriptions-item label="To Team">{{ detailData.str_to_team }}</el-descriptions-item>
        <el-descriptions-item label="人员">{{ detailData.staff_name }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <span :class="statusColor(detailData.int_substatus)">{{ statusText(detailData.int_substatus) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="审批人">{{ detailData.str_sec_manage }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
`
