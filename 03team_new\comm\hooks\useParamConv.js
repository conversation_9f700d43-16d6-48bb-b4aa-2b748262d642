export function useParamConv() {
  // 查询参数转换
  const searchParamConv = (searchParam) => {
    const param = {}
    param.toolingName = searchParam?.name ?? ''
    param.cardName = searchParam?.cardName ?? ''
    param.PageSize = searchParam?.pageSize ?? 10
    param.currentPage = searchParam?.pageNum ?? 1
    return param
  }
  const importMapEnum = {
    '工装名称': 'toolingName',
    '启用工卡': 'enableCardName',
    '释放工卡': 'releaseCardName'
  }
  // 导入参数转换
  const importParamConv = (importParam) => {
    const param = {}
    Object.keys(importParam).forEach(key => {
      param[importMapEnum[key]] = importParam[key]
    })
    return param
  }


  // 操作类工装查询参数转换
  const operationSearchParamConv = (searchParam) => {
    const param = {}
    param.taskName = searchParam?.name ?? ''
    param.toolingName = searchParam?.toolingName ?? ''
    param.PageSize = searchParam?.pageSize ?? 10
    param.currentPage = searchParam?.pageNum ?? 1
    return param
  }

  return {
    searchParamConv,
    importParamConv,
    operationSearchParamConv
  }
}
