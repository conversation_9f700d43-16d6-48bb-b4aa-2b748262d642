const { reactive, ref, shallowRef, onMounted, onUnmounted } = Vue
import { post } from '../../../config/axios/httpReuest.js'
import DetailDrawer from './components/DetailDrawer.js'
/**
 * 发动机交付预测趋势图
 */
const EngineDeliveryChart = {
  components: {
    DetailDrawer
  },
  setup() {
    const form = reactive({
      f4_1BeginTime: [],
      realeaseTime: [],
      f2_3ClosedTime: [],
    })
    const timeRangeType = ref('1')
    const changeTimeRangeType = (val) => {
      // option.xAxis.data = generateDate(Number(val))
      // option.series[0].data = getSeriesData(Number(val))
      // myChart.value.setOption(option)
    }
    /* 查询 */
    const getEngineDeliveryChart = async () => {
      await generateData()
      getXaxisData()
      getSeriesData()
      myChart.value.setOption(option)
    }
    const chartData = ref([])
    // 生成一年的数据
    const generateData = async () => {
      const filterFields = Object.entries(form).reduce((acc, [key, value]) => {
        if (value && value.length > 0) {
          acc.push({
            str_key: key,
            str_value: value,
          })
        }
        return acc
      }, [])
      const params = {
        ac: 'de_sm_delivery_forecast_report',
        filter_fields: filterFields,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        chartData.value = data.data
      }
    }

    const chartRef = ref(null)
    const myChart = shallowRef(null)
    // 折线图
    let option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        backdropFilter: 'blur(8px)',
        textStyle: {
          color: '#fff'
        },
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.2)',
            width: 1
          }
        }
      },
      legend: {
        show: true,
        data: ['CORE', 'FAN', 'LPT', 'B1'],
        textStyle: {
          color: '#fff',
          fontSize: 12
        },
        icon: 'roundRect',
        selectedMode: 'single',
        top: 10,
        right: 20,
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        borderRadius: 4,
        padding: 10,
      },
      grid: {
        left: '3%',
        right: '3%',
        bottom: '8%',
        top: '8%',
        containLabel: true
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          bottom: '3%',
          height: 20,
          borderColor: 'rgba(255,255,255,0.2)',
          backgroundColor: 'rgba(255,255,255,0.05)',
          fillerColor: 'rgba(255,255,255,0.1)',
          textStyle: {
            color: '#fff'
          },
          handleStyle: {
            color: 'rgba(255,255,255,0.3)',
            borderColor: 'rgba(255,255,255,0.5)'
          },
          moveHandleStyle: {
            color: 'rgba(255,255,255,0.5)'
          },
          selectedDataBackground: {
            lineStyle: {
              color: 'rgba(255,255,255,0.3)'
            },
            areaStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        {
          type: 'inside',
          xAxisIndex: [0]
        }
      ],
      xAxis: {
        type: 'category',
        boundaryGap: true,
        data: [],
        axisLabel: {
          color: 'rgba(255,255,255,0.8)',
          fontSize: 12,
          rotate: 45
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.2)'
          }
        },
        splitLine: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
            type: 'dashed',
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: 'rgba(255,255,255,0.8)',
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: 'CORE',
          type: 'line',
          data: [],
          smooth: true,
          symbolSize: 8,
          symbol: 'circle',
          itemStyle: {
            color: '#FFD700',
          },
          lineStyle: {
            width: 3,
            shadowColor: 'rgba(255,215,0,0.3)',
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255,215,0,0.2)' },
              { offset: 1, color: 'rgba(255,215,0,0)' }
            ])
          }
        },
        {
          name: 'FAN',
          type: 'line',
          data: [],
          smooth: true,
          symbolSize: 8,
          symbol: 'circle',
          itemStyle: {
            color: '#FF6347',
          },
          lineStyle: {
            width: 3,
            shadowColor: 'rgba(255,99,71,0.3)',
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255,99,71,0.2)' },
              { offset: 1, color: 'rgba(255,99,71,0)' }
            ])
          }
        },
        {
          name: 'LPT',
          type: 'line',
          data: [],
          smooth: true,
          symbolSize: 8,
          symbol: 'circle',
          itemStyle: {
            color: '#1E90FF',
          },
          lineStyle: {
            width: 3,
            shadowColor: 'rgba(30,144,255,0.3)',
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(30,144,255,0.2)' },
              { offset: 1, color: 'rgba(30,144,255,0)' }
            ])
          }
        },
        {
          name: 'B1',
          type: 'line',
          data: [],
          smooth: true,
          symbolSize: 8,
          symbol: 'circle',
          itemStyle: {
            color: '#8A2BE2',
          },
          lineStyle: {
            width: 3,
            shadowColor: 'rgba(138,43,226,0.3)',
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(138,43,226,0.2)' },
              { offset: 1, color: 'rgba(138,43,226,0)' }
            ])
          }
        },
        {
          type: 'line',
          markLine: {
            silent: true,
            data: [
              {
                name: '工装量',
                yAxis: 14,
                label: {
                  formatter: '工装量',
                  color: '#fff',
                  backgroundColor: 'rgba(255,215,0,0.2)',
                  padding: [4, 8],
                  borderRadius: 4
                },
                lineStyle: {
                  color: '#FFD700',
                  width: 2,
                  type: 'dashed',
                  opacity: 0.6
                },
              },
            ],
          },
        },
      ],
    }
    const handleResize = () => {
      myChart.value.resize()
    }
    const getXaxisData = () => {
      option.xAxis.data = chartData.value.map(item => item.dt_date)
    }
    const getSeriesData = () => {
      option.series[0].data = chartData.value.map(item => item.int_core)
      option.series[1].data = chartData.value.map(item => item.int_fan)
      option.series[2].data = chartData.value.map(item => item.int_lpt)
      option.series[3].data = chartData.value.map(item => item.int_b1)

      option.series.forEach(series => {
        if (series.type === 'line' && !series.markLine) {
          series.connectNulls = true
        }
      })
    }

    const drawerVisible = ref(false)
    const drawerData = ref({})

    const handleChartClick = (params) => {
      if (params.componentType === 'series' && params.seriesType === 'line') {
        const clickedDate = params.name
        const clickedValue = params.value
        const seriesName = params.seriesName
        
        drawerData.value = {
          seriesName,
          date: clickedDate,
        }
        
        drawerVisible.value = true
      }
    }

    onMounted(async () => {
      await generateData()
      myChart.value = echarts.init(chartRef.value)
      getXaxisData()
      getSeriesData()
      myChart.value.setOption(option)
      
      myChart.value.on('click', handleChartClick)
      
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (myChart.value) {
        myChart.value.off('click')
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      form,
      getEngineDeliveryChart,
      chartRef,
      timeRangeType,
      changeTimeRangeType,
      drawerVisible,
      drawerData
    }
  },
  template: /*html*/ `
    <div class="flex h-screen flex-col bg-slate-800/95 p-4">
      <div class="rounded-lg backdrop-blur-md bg-white/5 p-4 shadow-lg border border-white/10">
        <el-form :model="form" :inline="true" class="engine-form">
          <el-form-item label="F4-1 Begin Time:" label-class="text-white">
            <el-date-picker
              v-model="form.f4_1BeginTime"
              type="daterange"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              class="w-64"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="Realease Time:">
            <el-date-picker
              v-model="form.realeaseTime"
              type="daterange"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="F2/3 Closed Time:">
            <el-date-picker
              v-model="form.f2_3ClosedTime"
              type="daterange"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label=" ">
            <el-button type="primary" @click="getEngineDeliveryChart" class="bg-blue-500 hover:bg-blue-600">
              查询
            </el-button>
            <el-button type="info" class="bg-gray-500 hover:bg-gray-600">
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <div class="mt-4 flex items-center justify-between">
          <div class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
            发动机Project计划单元体交付图
          </div>
        </div>
      </div>
      <div class="corner-border mt-4 flex-auto rounded-lg backdrop-blur-md bg-white/5 p-4 border border-white/10">
        <div ref="chartRef" class="h-full w-full"></div>
      </div>

      <detail-drawer
        v-if="drawerVisible"
        v-model:visible="drawerVisible"
        :detail-data="drawerData"
      />
    </div>
  `,
}

export default EngineDeliveryChart
