<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!--    全部样式-->
    <link href="../../../assets/css/index.css" rel="stylesheet" />
    <!--    引入element-plus的样式-->
    <link href="../../../assets/element-plus@2.5.5/index.css" rel="stylesheet" />
    <!--    引入vxe-table的样式-->
    <link href="../../../assets/vxe-table/style.css" rel="stylesheet" />
    <!--    引入VUE-->
    <script src="../../../assets/vue@3.4.15/vue.global.prod.js"></script>
    <!--    引入vxe-table组件-->
    <script src="../../../assets/vxe-table/xe-utils.js"></script>
    <script src="../../../assets/vxe-table/vxe-table.js"></script>
    <!--    引入element-plus-->
    <script src="../../../assets/element-plus@2.5.5/index.js"></script>
    <!--  引入element-plus-icon-->
    <script src="../../../assets/icons-vue@2.3.1/index.iife.min.js"></script>
    <!--    引入axios-->
    <script src="../../../assets/axios@1.6.7/axios.min.js"></script>
    <!--    引入@vueuse/core-->
    <script src="../../../assets/@vueuse/shared@10.7.2/index.iife.min.js"></script>
    <script src="../../../assets/@vueuse/core@10.7.2/index.iife.min.js"></script>
    <!--    引入moment-->
    <script src="../../../assets/moment/moment.min.js"></script>
    <!--    引入echarts-->
    <script src="../../../assets/echarts@5.5.0/echarts.min.js"></script>
    <!--    引入xlsx-->
    <script src="../../../assets/xlsx@0.16.8/xlsx.full.min.js"></script>
    <!--    引入dhtml-gantt-->
    <link href="../../../assets/dhtmlx-gantt/index.css" rel="stylesheet" />
    <script src="../../../assets/dhtmlx-gantt/index.js"></script>
    <link href="../../assets/index.css" rel="stylesheet" />
    <!--    引入lodash-->
    <script src="../../../assets/plugins/lodash/lodash.js"></script>
    <title>站位计划管理</title>
  </head>

  <body>
    <div id="app">
      <station-plan-gantt></station-plan-gantt>
    </div>
  </body>
  <script type="module">
    import StationPlanGantt from './plan.gantt.station.js'
    // import '../../../components/VxeTable/renderer/index.js';

    const { createApp } = Vue
    const app = createApp({
      components: {
        StationPlanGantt,
      },
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    app.use(ElementPlus)
    app.use(VXETable)
    app.mount('#app')
  </script>
</html>
