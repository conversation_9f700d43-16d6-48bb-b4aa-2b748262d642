const { useVModel } = VueUse;
export default {
  name: 'HtDialogComponent',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: 'Dialog',
    },
    width: {
      type: String,
      default: '50%',
    },
    isShowSave: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['save', 'clear'],
  setup(props, { emit }) {
    const isShowDialog = useVModel(props, 'visible', emit);

    // save dialog
    const saveDialog = () => {
      emit('save');
    };
    // quit dialog
    const quitDialog = () => {
      emit('clear');
      isShowDialog.value = false;
    };
    return {
      isShowDialog,
      saveDialog,
      quitDialog,
    };
  },
  // language=HTML
  template: `
    <el-dialog
      v-model="isShowDialog"
      :title
      :width
      class="my-dialog"
      :show-close="false"
      destroy-on-close
      draggable
    >
      <template #title>
        <div class="flex justify-between items-center">
          <span class="text-white">{{ title }}</span>
          <el-button class="w-16" type="danger" @click="quitDialog" size="small">Close</el-button>
        </div>
      </template>
      <slot></slot>
      <template #footer>
        <el-button class="w-16" type="danger" @click="quitDialog" size="small">Quit</el-button>
        <el-button v-if="isShowSave" class="w-16" type="primary" @click="saveDialog" size="small">Save</el-button>
      </template>
    </el-dialog>
  `,
};
