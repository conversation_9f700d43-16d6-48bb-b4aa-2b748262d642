/**
 * @description Hook to filter data
 * <AUTHOR>
 * @date 2024-07-20
 */
export function useFilter() {
  // * 重组过滤条件
  const recombineFilter = (formData) => {
    const list = []
    for (const key in formData) {
      if (formData[key]?.toString()?.length > 0) {
        list.push({
          str_key: key,
          str_value: formData[key],
        })
      }
    }
    return list
  }

  return {
    recombineFilter,
  }
}
