const { ref, defineComponent, onMounted } = Vue
import { queryShifts, queryTeamList, queryTypeList } from '../api/index.js'

// 搜索表单组件
const SearchForm = defineComponent({
  name: 'SearchForm',
  emits: ['search'],
  setup(props, { emit }) {
    const searchFormRef = ref(null)
    const searchParams = ref({
      dt_date: [moment().format('YYYY-MM-DD'), moment().add(14, 'days').format('YYYY-MM-DD')],
      str_esn: '',
      id_teams: [],
      id_shifts: [],
      str_types: [],
    })

    // 班次类型选项
    const shiftTypeOptions = ref([])
    
    // 获取班次类型选项
    const getShiftTypeOptions = async () => {
      try {
        const res = await queryShifts()
        shiftTypeOptions.value = res.data || []
      } catch (error) {
        console.error('获取班次类型失败:', error)
      }
    }

    const teamList = ref([])
    // 获取Team
    const getTeamList = async () => {
      try {
        const res = await queryTeamList()
        teamList.value = res || []
      } catch (error) {
        console.error('获取Team失败:', error)
      }
    }

    const typeList = ref([])
    // 获取Type
    const getTypeList = async () => {
      try {
        const res = await queryTypeList()
        typeList.value = res || []
      } catch (error) {
        console.error('获取Type失败:', error)
      }
    }

    onMounted(() => {
      getShiftTypeOptions()
      getTeamList()
      getTypeList()
    })

    return {
      searchFormRef,
      searchParams,
      shiftTypeOptions,
      teamList,
      typeList
    }
  },
  template: /*html*/ `
    <el-form ref="searchFormRef" :model="searchParams" label-width="auto" inline label-position="right">
      <el-form-item label="Date:">
        <el-date-picker
          style="width: 100%"
          clearable
          type="daterange"
          value-format="YYYY-MM-DD"
          v-model="searchParams.dt_date"
          start-placeholder="起始日期"
          end-placeholder="截止日期"
        />
      </el-form-item>
      <el-form-item label="ESN:">
        <el-input v-model="searchParams.str_esn" clearable />
      </el-form-item>
      <!-- 新增Team -->
      <el-form-item label="Team:">
        <el-select
          v-model="searchParams.id_teams"
          multiple
          filterable
          clearable
          placeholder="选择Team"
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="team in teamList"
            :key="team.id"
            :label="team.str_name"
            :value="team.str_key"
          />
        </el-select>
      </el-form-item>
      <!-- 新增班次类型筛选 -->
      <el-form-item label="Shift:">
        <el-select
          v-model="searchParams.id_shifts"
          multiple
          filterable
          clearable
          placeholder="选择班次类型"
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="shift in shiftTypeOptions"
            :key="shift.id"
            :label="shift.str_name"
            :value="shift.id"
          />
        </el-select>
      </el-form-item>
      <!-- 新增Type -->
      <el-form-item label="Type:">
        <el-select
          v-model="searchParams.str_types"
          multiple
          filterable
          clearable
          placeholder="选择Type"
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="type in typeList"
            :key="type.str_key"
            :label="type.str_value"
            :value="type.str_key"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="$emit('search', searchParams)" icon="Search" />
      </el-form-item>
   </el-form>
  `,
})

export default SearchForm 