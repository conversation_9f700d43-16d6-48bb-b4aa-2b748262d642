<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发动机零件VSM - 精确还原版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
        }

        .vsm-wrapper {
            background: white;
            border: 3px solid #000;
            max-width: 1600px;
            margin: 0 auto;
            overflow-x: auto;
        }

        /* 顶部时间线 */
        .top-timeline {
            border-bottom: 2px solid #000;
        }

        .timeline-header {
            background: #e0e0e0;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid #000;
        }

        .timeline-grid {
            display: grid;
            grid-template-columns: 200px repeat(9, 1fr);
            min-width: 1400px;
        }

        .timeline-label {
            padding: 6px 10px;
            border-right: 1px solid #000;
            border-bottom: 1px solid #000;
            font-size: 12px;
            display: flex;
            align-items: center;
        }

        .timeline-cell {
            padding: 6px;
            border-right: 1px solid #000;
            border-bottom: 1px solid #000;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
        }

        .touch-time { background: #ffebee; }
        .leading-time { background: #e8f5e8; }
        .plan-date { background: #fff9c4; }

        /* 流程图区域 */
        .process-area {
            height: 300px;
            position: relative;
            border-bottom: 2px solid #000;
            background: #fafafa;
        }

        .process-flow {
            display: flex;
            align-items: center;
            justify-content: space-around;
            height: 100%;
            padding: 20px;
        }

        .process-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .process-box {
            width: 100px;
            height: 60px;
            border: 2px solid #000;
            background: #e3f2fd;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            text-align: center;
            margin-bottom: 10px;
        }

        .inventory-box {
            width: 80px;
            height: 40px;
            background: #fff3e0;
            border: 2px solid #ff9800;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            margin-bottom: 10px;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        }

        .process-data {
            background: white;
            border: 1px solid #000;
            padding: 4px;
            font-size: 9px;
            text-align: center;
            min-width: 60px;
        }

        .arrow {
            width: 50px;
            height: 2px;
            background: #000;
            position: relative;
            margin: 0 10px;
        }

        .arrow::after {
            content: '';
            position: absolute;
            right: -6px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid #000;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }

        /* 底部时间线 */
        .bottom-timeline {
            border-bottom: 2px solid #000;
        }

        /* 信息流区域 */
        .info-flow {
            height: 120px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 50px;
        }

        .supplier, .customer {
            width: 120px;
            height: 60px;
            border: 2px solid #4caf50;
            background: #e8f5e8;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .production-control {
            width: 150px;
            height: 60px;
            border: 2px solid #9c27b0;
            background: #f3e5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            position: absolute;
            left: 50%;
            top: 20px;
            transform: translateX(-50%);
        }

        /* 数据表格 */
        .data-table {
            border-top: 2px solid #000;
        }

        .table-grid {
            display: grid;
            grid-template-columns: 100px repeat(9, 1fr);
            min-width: 1400px;
        }

        .table-header-cell {
            background: #e0e0e0;
            padding: 8px;
            border-right: 1px solid #000;
            border-bottom: 1px solid #000;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }

        .table-cell {
            padding: 8px;
            border-right: 1px solid #000;
            border-bottom: 1px solid #000;
            text-align: center;
            font-size: 11px;
        }

        /* 控制面板 */
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid #333;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .control-btn {
            display: block;
            width: 100%;
            margin-bottom: 10px;
            padding: 8px 16px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .control-btn:hover {
            background: #1976d2;
        }

        .control-btn.export { background: #4caf50; }
        .control-btn.edit { background: #ff9800; }
        .control-btn.calculate { background: #9c27b0; }

        /* 编辑模式 */
        .editable {
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .editable:hover {
            background-color: rgba(33, 150, 243, 0.1);
        }

        /* 响应式 */
        @media (max-width: 1600px) {
            .vsm-wrapper {
                margin: 0 10px;
            }
        }

        /* 高亮显示 */
        .highlight {
            background-color: #ffeb3b !important;
            animation: pulse 1s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="controls">
        <h4 style="margin-bottom: 10px; text-align: center;">VSM控制面板</h4>
        <button class="control-btn edit" onclick="toggleEditMode()">编辑模式</button>
        <button class="control-btn export" onclick="exportData()">导出数据</button>
        <button class="control-btn calculate" onclick="calculateMetrics()">计算指标</button>
        <button class="control-btn" onclick="resetData()">重置数据</button>
        <button class="control-btn" onclick="addProcess()">添加工序</button>
    </div>

    <div class="vsm-wrapper">
        <!-- 顶部时间线 -->
        <div class="top-timeline">
            <div class="timeline-header">发动机零件VSM价值流图 - Engine Parts Value Stream Mapping</div>
            <div class="timeline-grid">
                <div class="timeline-label">指标项目</div>
                <div class="timeline-cell">拆解</div>
                <div class="timeline-cell">等待清洗</div>
                <div class="timeline-cell">苛性清洗</div>
                <div class="timeline-cell">目视检查</div>
                <div class="timeline-cell">等待去除TBC</div>
                <div class="timeline-cell">水射流去除TBC</div>
                <div class="timeline-cell">目视检查</div>
                <div class="timeline-cell">苛性清洗</div>
                <div class="timeline-cell">目视检查</div>
                
                <div class="timeline-label touch-time">Touch time from engineer (H)</div>
                <div class="timeline-cell touch-time editable" data-process="0" data-field="touchTime">205</div>
                <div class="timeline-cell touch-time editable" data-process="1" data-field="touchTime">4</div>
                <div class="timeline-cell touch-time editable" data-process="2" data-field="touchTime">4</div>
                <div class="timeline-cell touch-time editable" data-process="3" data-field="touchTime">2</div>
                <div class="timeline-cell touch-time editable" data-process="4" data-field="touchTime">8</div>
                <div class="timeline-cell touch-time editable" data-process="5" data-field="touchTime">0.5</div>
                <div class="timeline-cell touch-time editable" data-process="6" data-field="touchTime">0.5</div>
                <div class="timeline-cell touch-time editable" data-process="7" data-field="touchTime">8</div>
                <div class="timeline-cell touch-time editable" data-process="8" data-field="touchTime">2</div>
                
                <div class="timeline-label leading-time">Leading time (H)</div>
                <div class="timeline-cell leading-time editable" data-process="0" data-field="leadingTime">253</div>
                <div class="timeline-cell leading-time editable" data-process="1" data-field="leadingTime">4</div>
                <div class="timeline-cell leading-time editable" data-process="2" data-field="leadingTime">4</div>
                <div class="timeline-cell leading-time editable" data-process="3" data-field="leadingTime">2</div>
                <div class="timeline-cell leading-time editable" data-process="4" data-field="leadingTime">8</div>
                <div class="timeline-cell leading-time editable" data-process="5" data-field="leadingTime">2</div>
                <div class="timeline-cell leading-time editable" data-process="6" data-field="leadingTime">4</div>
                <div class="timeline-cell leading-time editable" data-process="7" data-field="leadingTime">2</div>
                <div class="timeline-cell leading-time editable" data-process="8" data-field="leadingTime">11</div>
                
                <div class="timeline-label">567657 DONE repair</div>
                <div class="timeline-cell">301</div>
                <div class="timeline-cell">4</div>
                <div class="timeline-cell">4</div>
                <div class="timeline-cell">2</div>
                <div class="timeline-cell" style="background: #ffcdd2;">40</div>
                <div class="timeline-cell">2</div>
                <div class="timeline-cell">4</div>
                <div class="timeline-cell">2</div>
                <div class="timeline-cell">11</div>
                
                <div class="timeline-label plan-date">Plan start date</div>
                <div class="timeline-cell plan-date">2022/8/5</div>
                <div class="timeline-cell plan-date">2022/8/6</div>
                <div class="timeline-cell plan-date">2022/8/5</div>
                <div class="timeline-cell plan-date">2022/8/7</div>
                <div class="timeline-cell plan-date">2022/8/9</div>
                <div class="timeline-cell plan-date">2022/8/16</div>
                <div class="timeline-cell plan-date">2022/8/15</div>
                <div class="timeline-cell plan-date">2022/8/16</div>
                <div class="timeline-cell plan-date">2022/8/16</div>
                
                <div class="timeline-label plan-date">End date</div>
                <div class="timeline-cell plan-date">2022/8/5</div>
                <div class="timeline-cell plan-date">2022/8/6</div>
                <div class="timeline-cell plan-date">2022/8/6</div>
                <div class="timeline-cell plan-date">2022/8/9</div>
                <div class="timeline-cell plan-date">2022/8/14</div>
                <div class="timeline-cell plan-date">2022/8/16</div>
                <div class="timeline-cell plan-date">2022/8/15</div>
                <div class="timeline-cell plan-date">2022/8/16</div>
                <div class="timeline-cell plan-date">2022/8/24</div>
                
                <div class="timeline-label">work time</div>
                <div class="timeline-cell">301</div>
                <div class="timeline-cell">4</div>
                <div class="timeline-cell">4</div>
                <div class="timeline-cell">2</div>
                <div class="timeline-cell" style="background: #ffcdd2;">40</div>
                <div class="timeline-cell">2</div>
                <div class="timeline-cell">4</div>
                <div class="timeline-cell">2</div>
                <div class="timeline-cell">11</div>
            </div>
        </div>

        <!-- 流程图区域 -->
        <div class="process-area">
            <div class="process-flow" id="processFlow">
                <!-- 流程节点将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 底部时间线（重复） -->
        <div class="bottom-timeline">
            <div class="timeline-grid">
                <div class="timeline-label touch-time">Touch time from engineer (H)</div>
                <div class="timeline-cell touch-time">205</div>
                <div class="timeline-cell touch-time">4</div>
                <div class="timeline-cell touch-time">4</div>
                <div class="timeline-cell touch-time">2</div>
                <div class="timeline-cell touch-time">8</div>
                <div class="timeline-cell touch-time">0.5</div>
                <div class="timeline-cell touch-time">0.5</div>
                <div class="timeline-cell touch-time">8</div>
                <div class="timeline-cell touch-time">2</div>
                
                <div class="timeline-label leading-time">Leading time (H)</div>
                <div class="timeline-cell leading-time">253</div>
                <div class="timeline-cell leading-time">4</div>
                <div class="timeline-cell leading-time">4</div>
                <div class="timeline-cell leading-time">2</div>
                <div class="timeline-cell leading-time">8</div>
                <div class="timeline-cell leading-time">2</div>
                <div class="timeline-cell leading-time">4</div>
                <div class="timeline-cell leading-time">2</div>
                <div class="timeline-cell leading-time">11</div>
                
                <div class="timeline-label">697657 DONE repair</div>
                <div class="timeline-cell">Plan start date</div>
                <div class="timeline-cell plan-date">2022/8/11</div>
                <div class="timeline-cell plan-date">2022/8/11</div>
                <div class="timeline-cell plan-date">2022/8/11</div>
                <div class="timeline-cell plan-date">2022/8/12</div>
                <div class="timeline-cell plan-date">2022/8/13</div>
                <div class="timeline-cell plan-date">2022/8/14</div>
                <div class="timeline-cell plan-date">2022/8/15</div>
                <div class="timeline-cell plan-date">2022/8/15</div>
            </div>
        </div>

        <!-- 信息流区域 -->
        <div class="info-flow">
            <div class="supplier">供应商<br>Supplier</div>
            <div class="production-control">生产计划控制<br>Production Control</div>
            <div class="customer">客户<br>Customer</div>
        </div>

        <!-- 数据表格 -->
        <div class="data-table">
            <div class="table-grid">
                <div class="table-header-cell">Process</div>
                <div class="table-header-cell">Disassembly</div>
                <div class="table-header-cell">Waiting Cleaning</div>
                <div class="table-header-cell">Cleaning Caustic Wash</div>
                <div class="table-header-cell">Visual Inspection</div>
                <div class="table-header-cell">Waiting Remove TBC</div>
                <div class="table-header-cell">Remove TBC by Water Jet</div>
                <div class="table-header-cell">Visual Inspection</div>
                <div class="table-header-cell">Cleaning Caustic Wash</div>
                <div class="table-header-cell">Visual Inspection</div>
                
                <div class="table-cell">POST</div>
                <div class="table-cell editable">CC A/D</div>
                <div class="table-cell editable">F1-2</div>
                <div class="table-cell editable">F1-2</div>
                <div class="table-cell editable">CC A/D</div>
                <div class="table-cell editable">WJ</div>
                <div class="table-cell editable">WJ</div>
                <div class="table-cell editable">CC A/D</div>
                <div class="table-cell editable">F1-2</div>
                <div class="table-cell editable">CC A/D</div>
                
                <div class="table-cell">remark</div>
                <div class="table-cell">-44778.00</div>
                <div class="table-cell"></div>
                <div class="table-cell"></div>
                <div class="table-cell"></div>
                <div class="table-cell"></div>
                <div class="table-cell"></div>
                <div class="table-cell"></div>
                <div class="table-cell"></div>
                <div class="table-cell"></div>
            </div>
        </div>
    </div>

    <script>
        // 数据模型
        let vsmData = {
            processes: [
                { name: 'Disassembly', type: 'process', touchTime: 205, leadingTime: 253, workTime: 301, post: 'CC A/D' },
                { name: 'Waiting Cleaning', type: 'inventory', touchTime: 4, leadingTime: 4, workTime: 4, post: 'F1-2' },
                { name: 'Cleaning Caustic Wash', type: 'process', touchTime: 4, leadingTime: 4, workTime: 4, post: 'F1-2' },
                { name: 'Visual Inspection', type: 'process', touchTime: 2, leadingTime: 2, workTime: 2, post: 'CC A/D' },
                { name: 'Waiting Remove TBC', type: 'inventory', touchTime: 8, leadingTime: 8, workTime: 40, post: 'WJ' },
                { name: 'Remove TBC by Water Jet', type: 'process', touchTime: 0.5, leadingTime: 2, workTime: 2, post: 'WJ' },
                { name: 'Visual Inspection', type: 'process', touchTime: 0.5, leadingTime: 4, workTime: 4, post: 'CC A/D' },
                { name: 'Cleaning Caustic Wash', type: 'process', touchTime: 8, leadingTime: 2, workTime: 2, post: 'F1-2' },
                { name: 'Visual Inspection', type: 'process', touchTime: 2, leadingTime: 11, workTime: 11, post: 'CC A/D' }
            ]
        };

        let editMode = false;

        // 初始化页面
        function initializePage() {
            renderProcessFlow();
            setupEditableElements();
        }

        // 渲染流程图
        function renderProcessFlow() {
            const container = document.getElementById('processFlow');
            container.innerHTML = '';

            vsmData.processes.forEach((process, index) => {
                const stepDiv = document.createElement('div');
                stepDiv.className = 'process-step';

                // 创建流程框
                const processBox = document.createElement('div');
                if (process.type === 'process') {
                    processBox.className = 'process-box';
                    processBox.innerHTML = `
                        <div style="font-weight: bold;">${process.name}</div>
                        <div>CT: ${process.touchTime}H</div>
                    `;
                } else {
                    processBox.className = 'inventory-box';
                    processBox.innerHTML = `${process.touchTime}`;
                }

                // 创建数据框
                const dataBox = document.createElement('div');
                dataBox.className = 'process-data';
                dataBox.innerHTML = `
                    <div>${process.post}</div>
                    <div>LT: ${process.leadingTime}H</div>
                `;

                stepDiv.appendChild(processBox);
                stepDiv.appendChild(dataBox);
                container.appendChild(stepDiv);

                // 添加箭头（除了最后一个）
                if (index < vsmData.processes.length - 1) {
                    const arrow = document.createElement('div');
                    arrow.className = 'arrow';
                    container.appendChild(arrow);
                }
            });
        }

        // 设置可编辑元素
        function setupEditableElements() {
            const editables = document.querySelectorAll('.editable');
            editables.forEach(element => {
                element.addEventListener('click', function() {
                    if (editMode) {
                        editCell(this);
                    }
                });
            });
        }

        // 编辑单元格
        function editCell(element) {
            const currentValue = element.textContent;
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentValue;
            input.style.width = '100%';
            input.style.border = 'none';
            input.style.background = 'transparent';
            input.style.textAlign = 'center';
            input.style.fontSize = 'inherit';

            element.innerHTML = '';
            element.appendChild(input);
            input.focus();
            input.select();

            const saveValue = () => {
                const newValue = input.value;
                element.textContent = newValue;
                
                // 更新数据模型
                const processIndex = element.dataset.process;
                const field = element.dataset.field;
                if (processIndex !== undefined && field) {
                    vsmData.processes[processIndex][field] = parseFloat(newValue) || newValue;
                    renderProcessFlow();
                }
            };

            input.addEventListener('blur', saveValue);
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveValue();
                }
            });
        }

        // 切换编辑模式
        function toggleEditMode() {
            editMode = !editMode;
            const btn = document.querySelector('.control-btn.edit');
            btn.textContent = editMode ? '退出编辑' : '编辑模式';
            btn.style.background = editMode ? '#f44336' : '#ff9800';

            const editables = document.querySelectorAll('.editable');
            editables.forEach(el => {
                if (editMode) {
                    el.style.cursor = 'pointer';
                    el.title = '点击编辑';
                    el.style.border = '1px dashed #2196f3';
                } else {
                    el.style.cursor = 'default';
                    el.title = '';
                    el.style.border = 'none';
                }
            });
        }

        // 导出数据
        function exportData() {
            const dataStr = JSON.stringify(vsmData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'engine-vsm-exact-data.json';
            link.click();
            URL.revokeObjectURL(url);
            
            alert('VSM数据已导出为JSON文件！');
        }

        // 计算指标
        function calculateMetrics() {
            const totalTouchTime = vsmData.processes.reduce((sum, p) => sum + parseFloat(p.touchTime || 0), 0);
            const totalLeadingTime = vsmData.processes.reduce((sum, p) => sum + parseFloat(p.leadingTime || 0), 0);
            const totalWorkTime = vsmData.processes.reduce((sum, p) => sum + parseFloat(p.workTime || 0), 0);
            const valueAddedRatio = ((totalTouchTime / totalLeadingTime) * 100).toFixed(2);

            // 高亮显示计算结果
            const touchCells = document.querySelectorAll('.touch-time');
            const leadingCells = document.querySelectorAll('.leading-time');
            
            touchCells.forEach(cell => cell.classList.add('highlight'));
            leadingCells.forEach(cell => cell.classList.add('highlight'));
            
            setTimeout(() => {
                touchCells.forEach(cell => cell.classList.remove('highlight'));
                leadingCells.forEach(cell => cell.classList.remove('highlight'));
            }, 2000);

            alert(`VSM指标计算结果：
总接触时间: ${totalTouchTime} 小时
总前置时间: ${totalLeadingTime} 小时  
总工作时间: ${totalWorkTime} 小时
增值比率: ${valueAddedRatio}%

建议：
- 减少等待时间以提高增值比率
- 优化瓶颈工序（工作时间40小时的环节）
- 考虑并行处理某些检查步骤`);
        }

        // 重置数据
        function resetData() {
            if (confirm('确定要重置所有数据吗？此操作不可撤销。')) {
                location.reload();
            }
        }

        // 添加工序
        function addProcess() {
            const name = prompt('请输入新工序名称:');
            if (name) {
                const newProcess = {
                    name: name,
                    type: 'process',
                    touchTime: 1,
                    leadingTime: 1,
                    workTime: 1,
                    post: 'NEW'
                };
                vsmData.processes.push(newProcess);
                renderProcessFlow();
                alert('新工序已添加！请在编辑模式下修改具体参数。');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
