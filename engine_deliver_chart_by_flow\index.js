const { ref, reactive, onMounted } = Vue;
import { post } from '../config/axios/httpReuest.js';

export default {
  name: 'EngineDeliverChart',
  props: ['str_eng_type'],
  setup(props) {
    const tableData = ref([]);
    const title_chart = ref("");
    const count = ref(0);
    const timer = ref(null);
    const localtime = ref(null);
    const loading = ref(false)
    const query = async () => {
      loading.value=true;
      const params = {
        ac: 'pda_delieve_flow_chart',
        str_eng_type: props.str_eng_type,
        indexWeek:indexWeek.value
      };
      const { data } = await post(params);
      tableData.value = data.data;
      loading.value=false;
    };
    // 计算每个flow值在表格数据中出现的次数

    // 表格合并
    const handleSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 3 || columnIndex === 8 || columnIndex === 9) {
        if (rowIndex === 0 || tableData.value[rowIndex - 1].str_flow !== row.str_flow) {
          // const length = flowCount[row.str_flow];
          const length = tableData.value.reduce((acc, cur) => {
            if (acc[cur.str_flow]) {
              acc[cur.str_flow] += 1;
            } else {
              acc[cur.str_flow] = 1;
            }
            return acc;
          }, {})[row.str_flow];
          return {
            rowspan: length,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    };
    const styleBack = ({ row, column, rowIndex, columnIndex }) => {
      return {
        background: '#2d8cf0',
        color: 'white',
        fontSize: '2rem',
        lineHeight: '3rem',
      };
    };
    const rowStyle = ({ row, rowIndex }) => {
      return {
        fontSize: '1.8rem'
      }
    };
    const getCodeWait = () => {
      let TIME_COUNT = 60;
      if (!timer.value) {
        count.value = TIME_COUNT;
        timer.value = setInterval(() => {
          if (count.value > 0 && count.value <= TIME_COUNT) {
            count.value--;
          } else {
            clearInterval(timer.value);
          }
        }, 1000);
      }
    };
    const showLocale = (objD) => {
      let str, colorhead, colorfoot;
      let yy = objD.getYear();
      if (yy < 1900) yy = yy + 1900;
      let MM = objD.getMonth() + 1;
      if (MM < 10) MM = '0' + MM;
      let dd = objD.getDate();
      if (dd < 10) dd = '0' + dd;
      let hh = objD.getHours();
      if (hh < 10) hh = '0' + hh;
      let mm = objD.getMinutes();
      if (mm < 10) mm = '0' + mm;
      let ss = objD.getSeconds();
      if (ss < 10) ss = '0' + ss;
      let ww = objD.getDay();
      // if (ww == 0) colorhead = "<font color=\"#ffffff\">";
      // if (ww > 0 && ww < 6) colorhead = "<font color=\"#ffffff\">";
      // if (ww == 6) colorhead = "<font color=\"#ffffff\">";
      // if (ww == 0) ww = "星期日";
      // if (ww == 1) ww = "星期一";
      // if (ww == 2) ww = "星期二";
      // if (ww == 3) ww = "星期三";
      // if (ww == 4) ww = "星期四";
      // if (ww == 5) ww = "星期五";
      // if (ww == 6) ww = "星期六";
      // colorfoot = "</font>"
      str = yy + '-' + MM + '-' + dd + ' ' + hh + ':' + mm + ':' + ss + '  '; //colorfoot
      return (str);
    };
    const tick = () => {
      let today;
      today = new Date();
      localtime.value = showLocale(today);
      window.setTimeout(tick, 1000);
    };
    const searchForm = reactive({
      enginType: '',
    });

    // 当前周
    const currentWeek = ref(moment().weeks());
    // 当前周的前一周
    const lastWeek = ref(moment().subtract(1, 'weeks').weeks());
    // 当前周的前两周
    const lastTwoWeek = ref(moment().subtract(2, 'weeks').weeks());
    // 当前周的开始时间
    const weekStart = ref(moment().startOf('weeks').add(1, 'days').format('YYYY-MM-DD'));
    // 当前周的结束时间
    const weekEnd = ref(moment().endOf('weeks').add(1, 'days').format('YYYY-MM-DD'));
    const indexWeek = ref(0);// 标记当前周
    /**下一周 */
    const nextWeek = (is_next,index_go) => {
     
      if(!index_go){
        if (is_next) {
          indexWeek.value += 1;
        } else {
          indexWeek.value -= 1;
        }
      }

      currentWeek.value = moment().subtract(-indexWeek.value, 'weeks').weeks();
      lastWeek.value = currentWeek.value - 1;
      lastTwoWeek.value = currentWeek.value - 2;
      // 当前周的开始时间
      weekStart.value = moment().subtract(-indexWeek.value, 'weeks').startOf('weeks').add(1, 'days').format('YYYY-MM-DD');
      // 当前周的结束时间
      weekEnd.value = moment().subtract(-indexWeek.value, 'weeks').endOf('weeks').add(1, 'days').format('YYYY-MM-DD');
      query();

    }/**上一周 */
    const agoWeek = () => {

    }
    onMounted(async () => {
      await query(props.str_eng_type);
      tick();
      if (props.str_eng_type == "CFM") {
        title_chart.value = "CFM56 Engine Deliver Chart";

      } else {
        title_chart.value = "LEAP Engine Deliver Chart";
      }
      // setInterval(() => {
      //   window.location.reload();
      // }, 60 * 1000);

    });
    return {
      tableData,
      handleSpanMethod,
      searchForm,
      query,
      getCodeWait,
      count,
      timer,
      showLocale,
      tick,
      localtime,
      currentWeek,
      weekStart,
      weekEnd,
      lastWeek,
      lastTwoWeek,
      styleBack,
      rowStyle,
      title_chart,
      nextWeek,
      agoWeek,
      indexWeek,
      loading
    };

  },
  // language=HTML
  template: `
    <el-row>
    <el-col :span="1" >
    <el-button type="primary" @click="nextWeek(false)">Ago {{indexWeek<0?indexWeek:''}}</el-button>
    </el-col>
      <el-col :span="1" :offset="1">
        <span style="color:white;font-size:15px">Total:{{tableData.length}}</span>
      </el-col>
      <el-col :span="5" :offset="1">
      <el-text class="mx-10" size="large" style="color:white;font-size:25px">{{title_chart}}
      </el-text>
     
    </el-col>
      <el-col :span="7" :offset="2">
        <el-text class="mx-10" size="large" style="color:white;font-size:25px">Week {{ currentWeek }}({{ weekStart }}~{{ weekEnd }})
        </el-text>
      </el-col>
      <el-col :span="2" :offset="1">
        <span style="color:white;font-size:12px">{{localtime}}</span>
      </el-col>
      <el-col :span="1" :offset="1">
      <el-button type="primary" @click="nextWeek(true)">Next </el-button>
      </el-col>
      <el-col :span="1" >
      <el-input-number  :min="0" :max="100"  v-model="indexWeek"  controls-position="right" style="width:75px"
      size="mx-1" @change="nextWeek(true,true)" @blur="nextWeek(true,true)"> </<el-input-number>
     
      </el-col>
    
    </el-row>
    <el-table
      :data="tableData"
      border
      stripe
      :span-method="handleSpanMethod"
      :header-cell-style="styleBack"
      :row-style="rowStyle"
      v-loading="loading"
    >
      <el-table-column prop="str_flow" label="Flow" width="120"></el-table-column>
      <el-table-column prop="week_ago_2" label="Week 06" width="165" align="center">
        <template #header>
          <span>Week {{ lastTwoWeek }}</span>
        </template>
        <template #default="{row}">
        <div   style="font-size: 30px;
        font-weight: 600;"
          :class="(row.week_ago_2_actual >= row.week_ago_2 && row.week_ago_2_actual!=0) ? 'text-green-500' : row.week_ago_2_actual < row.week_ago_2 ? 'text-red-500' : ''">
          {{ row.week_ago_2_actual }} / {{ row.week_ago_2}}
        </div>
      </template>
      </el-table-column>
      <el-table-column prop="week_ago_1" width="165" align="center">
        <template #header>
          <span>Week {{ lastWeek }}</span>
        </template>
        <template #default="{row}">
        <div
        style="font-size: 30px;
        font-weight: 600;"
          :class="(row.week_ago_1_actual >= row.week_ago_1 && row.week_ago_1_actual!=0) ? 'text-green-500' : row.week_ago_1_actual < row.week_ago_1 ? 'text-red-500' :'' ">
          {{ row.week_ago_1_actual }} / {{ row.week_ago_1}}
        </div>
      </template>
        
      </el-table-column>
      <el-table-column prop="int_target" label="Target" width="130" align="center"></el-table-column>
      <el-table-column prop="str_esn" label="ESN" width="220" align="center"></el-table-column>
      <el-table-column prop="slot" label="Slot" width="120" align="center"></el-table-column>
      <el-table-column prop="str_esn_type" label="Type" align="center" width="140">
      
      </el-table-column>
      <el-table-column label="Commitment" prop="dt_plan_close" align="center"></el-table-column>
      <!--      <el-table-column label="Process tracking" align="center">-->
      <!--        <el-table-column label="Finding" align="center">-->
      <!--          <template #default="{row}">-->
      <!--            <span style="color:white">{{ row.openFinding }}</span> / <span style="">{{ row.totalFinding}}</span>-->
      <!--          </template>-->
      <!--        </el-table-column>-->
      <!--        <el-table-column label="JC" prop="jc" align="center">-->
      <!--          <template #default="{row}">-->
      <!--            {{ row.openJc }} / {{ row.totalJc}}-->
      <!--          </template>-->
      <!--        </el-table-column>-->
      <!--      </el-table-column>-->
      <el-table-column label="Weekly TargeNext 4 weeks" prop="int_target_4"
                       align="center"></el-table-column>
      <el-table-column label="WIP" prop="int_wip" align="center" width="100"></el-table-column>

    </el-table>
  `,
};
