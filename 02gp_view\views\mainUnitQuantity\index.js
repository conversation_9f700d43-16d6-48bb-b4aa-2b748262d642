import { getMainUnitGantt, saveMainUnitGantt } from '../../api/index.js'
const { ref, reactive, onMounted, nextTick, computed, onUnmounted, watch } = Vue
import { useInitialization } from './composables/useInitialization.js'
import { useButton } from './composables/useButton.js'
import DynamicColumnConfigurator from '../../components/DynamicColumnConfigurator.js'

export const MainUnitQuantityGantt = {
  components: {
    DynamicColumnConfigurator,
  },
  props: {
    edit: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const formSearch = reactive({
      str_node: '',
      dt_start: '',
      dt_end: '',
      str_engine_type: '',
    })

    const ganttParse = ref({
      data: [],
      links: [],
    })

    // 存储原始数据，用于客户端筛选
    const originalData = ref({
      data: [],
      links: [],
    })

    // 存储筛选事件ID
    let filterEventId = null

    const { initGantt, GANTT_COLUMNS, renderGantt } = useInitialization()

    const { toggleGrid, gridButtonText, toggleGantt } = useButton(GANTT_COLUMNS)
    // 数据转换优化
    const transformData = (data) => {
      return data.map((item) => {
        const { id, str_node, dt_start, dt_end, dec_precent, id_root, duration, str_engine_type, is_lock } = item
        const hasChild = data.some((it) => it.id_root === id)
        const unscheduled = !(dt_start && dt_end) && !hasChild
        return {
          id,
          text: str_node,
          start_date: dt_start,
          dt_end,
          duration,
          progress: dec_precent,
          parent: id_root,
          type: hasChild ? 'project' : 'task',
          unscheduled,
          str_engine_type,
          is_lock,
        }
      })
    }

    const loading = ref(false)

    // 检查任务是否符合筛选条件
    const taskMatchesFilter = (task) => {
      // 按任务名称筛选
      if (formSearch.str_node && formSearch.str_node.trim()) {
        if (!task.text || !task.text.includes(formSearch.str_node.trim())) {
          return false
        }
      }

      // 按机型筛选（模糊匹配）
      if (formSearch.str_engine_type && formSearch.str_engine_type.trim()) {
        if (!task.str_engine_type || !task.str_engine_type.includes(formSearch.str_engine_type.trim())) {
          return false
        }
      }

      // 按开始时间筛选
      if (formSearch.dt_start) {
        const taskStartDate = task.start_date ? new Date(task.start_date) : null
        const filterStartDate = new Date(formSearch.dt_start)
        if (!taskStartDate || taskStartDate < filterStartDate) {
          return false
        }
      }

      // 按结束时间筛选
      if (formSearch.dt_end) {
        const taskEndDate = task.dt_end ? new Date(task.dt_end) : task.start_date ? new Date(task.start_date) : null
        const filterEndDate = new Date(formSearch.dt_end)
        if (!taskEndDate || taskEndDate > filterEndDate) {
          return false
        }
      }

      return true
    }

    // 检查任务或其子任务是否符合筛选条件
    const taskOrChildrenMatch = (taskId) => {
      const task = gantt.getTask(taskId)

      // 如果当前任务符合条件，返回true
      if (taskMatchesFilter(task)) {
        return true
      }

      // 检查子任务是否符合条件
      const children = gantt.getChildren(taskId)
      for (let i = 0; i < children.length; i++) {
        if (taskOrChildrenMatch(children[i])) {
          return true
        }
      }

      return false
    }

    // 客户端筛选逻辑
    const applyClientFilter = () => {
      // 先清除之前的筛选事件
      clearClientFilter()

      // 设置筛选事件
      filterEventId = gantt.attachEvent('onBeforeTaskDisplay', function (id, task) {
        // 如果没有设置任何筛选条件，显示所有任务
        const hasFilters = formSearch.str_node || formSearch.str_engine_type || formSearch.dt_start || formSearch.dt_end
        if (!hasFilters) {
          return true
        }

        // 检查当前任务或其子任务是否符合筛选条件
        return taskOrChildrenMatch(id)
      })

      // 刷新甘特图以应用筛选
      gantt.refreshData()
    }

    // 清除筛选
    const clearClientFilter = () => {
      // 移除筛选事件
      if (filterEventId) {
        gantt.detachEvent(filterEventId)
        filterEventId = null
      }
      // 刷新甘特图显示所有数据
      gantt.refreshData()
    }

    // 获取甘特图数据（只在初始化时调用）
    const getGanttData = async () => {
      // 清除甘特图数据
      gantt.clearAll()
      try {
        loading.value = true
        // 获取全量数据，不传筛选参数
        const data = await getMainUnitGantt(null, '', '', '', '')
        getTaskNameList(data.subPlans)

        // 转换数据并保存原始数据
        const transformedData = transformData(data.subPlans)
        originalData.value.data = transformedData
        originalData.value.links = data.links

        ganttParse.value.data = transformedData
        ganttParse.value.links = data.links

        await nextTick()
        renderGantt('gantt-container', ganttParse.value)
      } catch (error) {
        console.error('获取甘特图数据失败:', error)
        ElementPlus.ElMessage.error('获取数据失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    const taskNameList = ref([])
    /**
     * 获取任务名称下拉列表
     */
    const getTaskNameList = async (data) => {
      taskNameList.value = Array.from(
        new Set(data.filter(({ int_level }) => int_level !== 0).map(({ str_node }) => str_node)),
      )
    }

    // 重置筛选
    const reset = () => {
      formSearch.str_node = ''
      formSearch.dt_start = ''
      formSearch.dt_end = ''
      formSearch.str_engine_type = ''

      // 清除客户端筛选
      clearClientFilter()
      handleCollapseGantt()
    }

    // 添加容器高度响应式变量
    const containerHeight = ref('100%')

    // 检查是否在 iframe 中
    const isInIframe = () => {
      try {
        return window !== window.top
      } catch (e) {
        return true
      }
    }

    // 计算并设置容器高度
    const updateContainerHeight = () => {
      try {
        if (isInIframe()) {
          // iframe 环境下的高度计算
          // 获取iframe的可视区域高度
          const iframeHeight = window.innerHeight || document.documentElement.clientHeight
          // 减去顶部搜索区域的高度(48px)和内边距(32px) 80px = header高度 + padding等
          containerHeight.value = `${iframeHeight - 130}px`
        } else {
          // 非 iframe 环境下的高度计算
          const viewportHeight = window.innerHeight
          containerHeight.value = `${viewportHeight - 130}px` // 80px = header高度 + padding等
        }
      } catch (error) {
        console.error('更新容器高度失败:', error)
        containerHeight.value = '100%'
      }
    }

    const scrollToToday = () => {
      const today = new Date()
      gantt.showDate(today)
    }

    onMounted(() => {
      initGantt(props.edit, getGanttData)
      getGanttData()
      updateContainerHeight()
      window.addEventListener('resize', updateContainerHeight)
    })

    // 组件销毁时移除事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', updateContainerHeight)
      // 清理甘特图筛选事件
      clearClientFilter()
    })

    const isExpand = ref(false)
    /**
     * 展开甘特图
     */
    const handleExpandGantt = () => {
      isExpand.value = true
      toggleGantt(isExpand.value)
    }
    /**
     * 收起甘特图
     */
    const handleCollapseGantt = () => {
      isExpand.value = false
      toggleGantt(isExpand.value)
    }

    // 修改搜索方法，使用客户端筛选
    const search = () => {
      // 应用客户端筛选
      applyClientFilter()
      handleExpandGantt()

      // 显示筛选结果提示
      const hasFilters = formSearch.str_node || formSearch.str_engine_type || formSearch.dt_start || formSearch.dt_end
      if (hasFilters) {
        ElementPlus.ElMessage.success('筛选已应用')
      }
    }

    const handleSave = () => {
      const parentTaskList = []
      let childTaskList = []
      const { data } = gantt.serialize()
      // 判断某条任务被修改了
      const changeTaskList = data.filter((_it) => _it._changed)
      const parentId = Array.from(new Set(changeTaskList.map((_it) => _it.parent)))
      for (let index = 0; index < parentId.length; index++) {
        const element = parentId[index]
        const parentTask = gantt.getTask(element)
        parentTaskList.push(parentTask)
      }
      childTaskList = changeTaskList.map((_it) => {
        return {
          id: _it.id,
          text: _it.text,
          start_date: _it.start_date,
          end_date: _it.end_date,
          duration: _it.duration,
          progress: _it.progress,
          parent: _it.parent,
          level: 1,
        }
      })
      const taskList = parentTaskList.concat(...childTaskList).map((_it) => {
        return {
          id: _it.id,
          text: _it.text,
          start_date: _it.start_date,
          end_date: _it.end_date,
          duration: _it.duration,
          progress: _it.progress,
          parent: _it.parent,
          level: _it.$level,
        }
      })
      saveMainUnitGantt(taskList).then((res) => {
        // if (res) {
        //   getGanttData()
        // }
      })
    }

    // 甘特图列配置
    const visibleColumns = ref(GANTT_COLUMNS.map((col) => col.name))

    // 为列配置组件准备的列定义
    const columnConfig = computed(() => {
      // 确保列的key与GANTT_COLUMNS中的name保持一致
      return GANTT_COLUMNS.map((col) => {
        return {
          key: col.name, // 保持key与甘特图列的name一致
          label: col.label,
          fixed: col.name === 'text', // 任务名称列设为固定列
        }
      })
    })
    // 更新甘特图列显示
    const updateGanttColumns = (columns) => {
      if (gantt.config && gantt.config.columns) {
        // 1. 保留原始GANTT_COLUMNS的完整属性
        const updatedColumns = GANTT_COLUMNS.filter(
          (col) =>
            // 过滤出选中的列和固定列
            columns.includes(col.name) || col.name === 'text',
        )

        // 2. 更新甘特图配置
        gantt.config.columns = updatedColumns

        // 3. 刷新甘特图布局和数据
        gantt.render()
      }
    }

    // 监听列显示变化，更新甘特图显示
    watch(
      visibleColumns,
      (newColumns) => {
        if (gantt && typeof gantt.config !== 'undefined') {
          updateGanttColumns(newColumns)
        }
      },
      { deep: true, immediate: true },
    )

    return {
      formSearch,
      search,
      toggleGrid,
      gridButtonText,
      reset,
      loading,
      containerHeight,
      scrollToToday,
      handleExpandGantt,
      handleCollapseGantt,
      isExpand,
      handleSave,
      taskNameList,
      visibleColumns,
      columnConfig,
    }
  },
  template: /*html*/ `
    <div class="flex flex-col gap-4 p-4">
      <div class="flex flex-wrap items-center justify-between">
        <!-- 左侧搜索区域 -->
        <div class="flex flex-wrap items-center gap-2">
          <label class="text-sm text-gray-500">任务名称</label>
          <el-select style="width:220px" v-model="formSearch.str_node" placeholder="请选择任务名称" clearable>
            <el-option v-for="(item, index) in taskNameList" :key="index" :label="item" :value="item"></el-option>
          </el-select>
          <label class="text-sm text-gray-500">机型</label>
          <el-select style="width:220px" v-model="formSearch.str_engine_type" placeholder="请选择机型" clearable>
            <el-option label="CFM56" value="CFM56"></el-option>
            <el-option label="LEAP" value="LEAP"></el-option>
          </el-select>
          <label class="text-sm text-gray-500">release开始时间</label>
          <el-date-picker
            v-model="formSearch.dt_start"
            type="date"
            placeholder="请选择release开始时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
          <label class="text-sm text-gray-500">release结束时间</label>
          <el-date-picker
            v-model="formSearch.dt_end"
            type="date"
            placeholder="请选择release结束时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
          <el-button type="primary" :loading="loading" @click="search">搜索</el-button>
          <el-button type="info" :loading="loading" @click="reset">重置</el-button>
          <el-button v-if="!isExpand" type="primary" @click="handleExpandGantt">展开甘特图</el-button>
          <el-button v-else type="primary" @click="handleCollapseGantt">收起甘特图</el-button>
          <el-button type="primary" @click="handleSave">保存并应用</el-button>
        </div>

        <!-- 右侧功能按钮区域 -->
        <div class="flex items-center gap-2">
          <el-button plain type="default" :loading="loading" @click="toggleGrid">{{ gridButtonText }}</el-button>
          <el-button plain type="primary" :loading="loading" @click="scrollToToday">回到今天</el-button>
        </div>
      </div>

      <!-- 甘特图列配置 -->
      <div class="flex justify-end gap-2">
        <DynamicColumnConfigurator
          v-model="visibleColumns"
          :all-columns="columnConfig"
          button-text="列配置"
          button-icon="Grid"
        />
      </div>
      <!-- 甘特图区域 -->
      <div v-loading="loading" class="relative" :style="{ height: containerHeight }">
        <div id="gantt-container" class="h-full w-full"></div>
      </div>
    </div>
  `,
}
