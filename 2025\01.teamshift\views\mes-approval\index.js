const { ref, reactive, computed, onMounted } = Vue
const { ElMessage } = ElementPlus

// 导入组件和配置
import HtVxeTable from '../../components/VxeTable/HtVxeTable.js'
import { useBrowserZoom } from '../../utils/browser-zoom.js'
import {
  getMesApplyList,
  getMesApplyById,
  approveMesApply,
  rejectMesApply,
  confirmSaveMesApply,
} from '../mes-apply/api/index.js'
import { MES_CONFIG } from '../mes-apply/components/config.js'
import { MesApplyForm, MesApplyFormDetails } from '../mes-apply/components/index.js'

// 常量定义
const APPROVAL_STATUS = {
  PENDING: 301, // 待审批
  APPROVED: 1,  // 审批通过
}

const VIEW_MODES = {
  VIEW: 'view',
  APPROVAL: 'approval', 
  CONFIRM: 'confirm',
}

export default {
  name: 'MesApproval',
  components: {
    HtVxeTable,
    MesApplyForm,
    MesApplyFormDetails,
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const tableRef = ref(null)
    const tableData = ref([])
    const createDialogVisible = ref(false)
    const rejectDialogVisible = ref(false)
    const rejectReason = ref('')
    const currentView = ref(VIEW_MODES.VIEW)
    const currentApprovalRow = ref(null)
    const isSubmitting = ref(false)

    // 表单数据
    const formData = reactive({
      id: null,
      person: '',
      startDate: '',
      endDate: '',
      reason: '',
      remarks: '',
      details: [],
    })

    // 分页数据
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
      pageSizes: MES_CONFIG.table.pagination.pageSizes,
      layout: MES_CONFIG.table.pagination.layout,
    })

    const searchForm = ref({})

    // 公共函数：格式化申请详情数据
    const formatApplyData = (apply, applysubs, mode = VIEW_MODES.VIEW) => {
      return {
        id: apply.id,
        person: apply.id_staff,
        startDate: apply.dt_mes_start,
        endDate: apply.dt_mes_end,
        reason: apply.str_apply_reason,
        remarks: apply.str_remark || '',
        details: applysubs.map((sub) => ({
          id: sub.id,
          date: sub.dt_date,
          startTime: sub.dt_start_time,
          endTime: sub.dt_end_time,
          appliedHours: sub.int_apply_hours,
          // 根据模式设置确认字段
          confirmStartTime: mode === VIEW_MODES.CONFIRM 
            ? (sub.dt_confirm_start || sub.dt_start_time)
            : (sub.dt_confirm_start || ''),
          confirmEndTime: mode === VIEW_MODES.CONFIRM
            ? (sub.dt_confirm_end || sub.dt_end_time)
            : (sub.dt_confirm_end || ''),
          confirmHours: mode === VIEW_MODES.CONFIRM
            ? (sub.int_confirm_hours || sub.int_apply_hours)
            : (sub.int_confirm_hours || 0),
          confirmStatus: sub.int_confirm_status,
        })),
      }
    }

    // 公共函数：打开申请详情对话框
    const openApplyDialog = async (row, mode) => {
      try {
        const detailData = await getMesApplyById(row.id)
        const { apply, applysubs } = detailData
        
        Object.assign(formData, formatApplyData(apply, applysubs, mode))
        currentApprovalRow.value = row
        currentView.value = mode
        createDialogVisible.value = true
      } catch (error) {
        console.error('获取申请详情失败:', error)
        ElMessage.error('获取申请详情失败')
      }
    }

    // 重置对话框状态
    const resetDialogState = () => {
      createDialogVisible.value = false
      rejectDialogVisible.value = false
      rejectReason.value = ''
    }

    // 成功处理后的回调
    const handleSaveSuccess = () => {
      resetDialogState()
      initData()
    }

    // 返回/取消操作
    const handleBack = () => {
      resetDialogState()
    }

    // 从驳回对话框返回到审批页面
    const handleBackToView = () => {
      rejectDialogVisible.value = false
      createDialogVisible.value = true
      rejectReason.value = ''
    }

    // 初始化数据
    const initData = async () => {
      try {
        loading.value = true
        const params = {
          PageSize: pagination.pageSize,
          Currentpage: pagination.currentPage,
          int_substatus: APPROVAL_STATUS.PENDING,
          ...searchForm.value,
        }
        const applications = await getMesApplyList(params)
        tableData.value = applications.items
        pagination.total = applications.totalCount
        
        // 动态更新审批状态过滤选项
        updateStatusFilterOptions(applications.items)
      } catch (error) {
        ElMessage.error('数据加载失败')
      } finally {
        loading.value = false
      }
    }

    // 查看申请详情
    const handleView = (row) => openApplyDialog(row, VIEW_MODES.VIEW)

    // 审批申请
    const handleApproval = (row) => openApplyDialog(row, VIEW_MODES.APPROVAL)

    // 审批通过
    const handleApprove = async () => {
      try {
        isSubmitting.value = true
        await approveMesApply(currentApprovalRow.value.id)
        ElMessage.success('审批通过成功')
        handleSaveSuccess()
      } catch (error) {
        console.error('审批通过失败:', error)
        ElMessage.error('审批通过失败')
      } finally {
        isSubmitting.value = false
      }
    }

    // 显示驳回对话框
    const showRejectDialog = () => {
      rejectDialogVisible.value = true
      createDialogVisible.value = false
    }

    // 审批驳回
    const handleReject = async () => {
      if (!rejectReason.value.trim()) {
        ElMessage.error('请填写驳回原因')
        return
      }

      try {
        isSubmitting.value = true
        await rejectMesApply(currentApprovalRow.value.id, rejectReason.value)
        ElMessage.success('审批驳回成功')
        handleSaveSuccess()
      } catch (error) {
        console.error('审批驳回失败:', error)
        ElMessage.error('审批驳回失败')
      } finally {
        isSubmitting.value = false
      }
    }

    // 过滤条件处理
    const handleFilterChange = (filter) => {
      searchForm.value = {}
      filter.filterList.forEach((element) => {
        // 获取过滤值
        let filterValue = null
        
        // 对于原生筛选，值在values数组中
        if (element.values && element.values.length > 0) {
          filterValue = element.values[0]
        }
        // 对于自定义筛选渲染器，值在datas数组中
        else if (element.datas && element.datas.length > 0) {
          filterValue = element.datas[0]
        }
        
        // 只有当过滤值不为空时才添加到搜索条件中
        if (filterValue !== undefined && filterValue !== null && filterValue !== '') {
          searchForm.value[element.field] = filterValue
        }
      })
      initData()
    }

    // 分页变化处理
    const handlePageChange = () => {
      initData()
    }

    // 浏览器缩放适配
    const { isZoomed, adaptedDimensions } = useBrowserZoom()

    // 计算表格高度
    const tableHeight = computed(() => {
      const headerHeight = isZoomed.value ? 80 : 100
      const paginationHeight = isZoomed.value ? 50 : 60
      const padding = isZoomed.value ? 16 : 24
      return adaptedDimensions.value.height - headerHeight - paginationHeight - padding
    })

    // 计算是否为只读模式
    const isReadonly = computed(() => 
      currentView.value === VIEW_MODES.VIEW || currentView.value === VIEW_MODES.APPROVAL
    )

    // 生命周期
    onMounted(initData)

    // 表格列配置（响应式）
    const tableColumns = ref([...MES_CONFIG.table.columns])

    // 动态获取审批状态选项
    const updateStatusFilterOptions = (data) => {
      // 找到审批状态列
      const statusColumn = tableColumns.value.find(col => col.field === 'int_substatus')
      if (statusColumn && (!statusColumn.filters || statusColumn.filters.length === 0)) {
        // 从数据中提取唯一的状态值
        const statusSet = new Set()
        data.forEach(item => {
          if (item.int_substatus !== undefined && item.int_substatus !== null) {
            statusSet.add(item.int_substatus)
          }
        })
        
        // 状态映射
        const statusMap = {
          0: '草稿',
          1: '审批通过', 
          301: '待审批',
          '-1': '审批失败',
          '-99': '撤回'
        }
        
        // 生成选择选项 - 使用VxeTable原生筛选格式
        const filterOptions = Array.from(statusSet)
          .sort((a, b) => a - b) // 按状态值排序
          .map(status => ({
            label: statusMap[status] || `状态${status}`,
            value: status
          }))
        
        // 设置过滤器配置 - 使用VxeTable原生筛选格式
        statusColumn.filters = filterOptions
      }
    }

    return {
      // 数据
      loading,
      tableColumns,
      tableData,
      pagination,
      tableRef,
      tableHeight,
      createDialogVisible,
      formData,
      currentView,
      rejectDialogVisible,
      rejectReason,
      currentApprovalRow,
      isSubmitting,
      isReadonly,
      // 常量
      APPROVAL_STATUS,
      VIEW_MODES,
      // 方法
      handleView,
      handleApproval,
      handleSaveSuccess,
      handleBack,
      handleBackToView,
      handleApprove,
      showRejectDialog,
      handleReject,
      handleFilterChange,
      handlePageChange,
    }
  },

  template: /*html*/ `
    <div class="h-screen w-full p-4">
      <!-- 表格容器 -->
      <div class="h-[calc(100vh-220px)]">
        <HtVxeTable
          v-loading="loading"
          ref="tableRef"
          :table-data="tableData"
          :table-columns="tableColumns"
          :height="tableHeight"
          :remote="true"
          :is-show-header-checkbox="false"
          @filter-change="handleFilterChange"
        >
          <template #operation>
            <vxe-column title="操作" min-width="180" fixed="right" align="center">
              <template #default="{ row }">
                <div class="flex flex-wrap justify-center gap-2">
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="handleView(row)" 
                    icon="View" 
                    link
                  >
                    查看
                  </el-button>
                  <el-button 
                    v-if="row.int_substatus === APPROVAL_STATUS.PENDING" 
                    type="success" 
                    size="small" 
                    @click="handleApproval(row)" 
                    icon="Check" 
                    link
                  >
                    审批
                  </el-button>
                </div>
              </template>
            </vxe-column>
          </template>
        </HtVxeTable>
      </div>

      <!-- 分页器 -->
      <div class="pt-2">
        <vxe-pager
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="pagination.pageSizes"
          :layout="pagination.layout"
          :loading="loading"
          @page-change="handlePageChange"
        />
      </div>

      <!-- 申请详情对话框 -->
      <MesApplyForm
        v-if="createDialogVisible"
        v-model:visible="createDialogVisible"
        :form-data="formData"
        :current-view="currentView"
        @success="handleSaveSuccess"
        @back="handleBack"
      >
        <template #details="{ details, onUpdate }">
          <MesApplyFormDetails
            mode="confirm"
            :details="details"
            :readonly="isReadonly"
            @update:details="onUpdate"
          />
        </template>


        <!-- 查看模式底部按钮 -->
        <template v-if="currentView === VIEW_MODES.VIEW" #footer>
          <el-button @click="handleBack" icon="Back">关闭</el-button>
        </template>

        <!-- 审批模式底部按钮 -->
        <template v-if="currentView === VIEW_MODES.APPROVAL" #footer>
          <div class="flex justify-end space-x-3">
            <el-button @click="handleBack" icon="Back">取消</el-button>
            <el-button
              type="success"
              @click="handleApprove"
              :loading="isSubmitting"
              :disabled="isSubmitting"
              icon="Check"
            >
              {{ isSubmitting ? '处理中...' : '审批通过' }}
            </el-button>
            <el-button 
              type="danger" 
              @click="showRejectDialog" 
              :disabled="isSubmitting" 
              icon="Close"
            >
              审批驳回
            </el-button>
          </div>
        </template>
      </MesApplyForm>

      <!-- 驳回原因对话框 -->
      <el-dialog
        v-model="rejectDialogVisible"
        title="审批驳回"
        width="500px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="common-dialog"
      >
        <div class="space-y-6">
          <!-- 警告提示 -->
          <div class="rounded-lg border border-red-200 bg-gradient-to-r from-red-50 to-pink-50 p-4">
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <svg class="mt-0.5 h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="flex-1">
                <h3 class="text-sm font-medium text-red-800">驳回确认</h3>
                <div class="mt-1 text-sm text-red-700">
                  即将驳回申请人
                  <span class="font-semibold">{{ currentApprovalRow?.apply_user }}</span>
                  的申请
                </div>
              </div>
            </div>
          </div>

          <!-- 驳回原因输入 -->
          <div class="space-y-3">
            <label class="block text-sm font-medium text-gray-800">
              驳回原因
              <span class="text-red-500">*</span>
            </label>
            <el-input
              v-model="rejectReason"
              type="textarea"
              :rows="4"
              placeholder="请详细说明驳回原因，以便申请人了解并改进..."
              maxlength="500"
              show-word-limit
              class="w-full"
            />
            <p class="text-xs text-gray-500">
              请提供具体、建设性的驳回理由，帮助申请人改进申请内容
            </p>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <el-button @click="handleBackToView" :disabled="isSubmitting" size="large">
              取消
            </el-button>
            <el-button
              type="danger"
              @click="handleReject"
              :loading="isSubmitting"
              :disabled="isSubmitting || !rejectReason.trim()"
              icon="Close"
              size="large"
            >
              {{ isSubmitting ? '处理中...' : '确认驳回' }}
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  `,
}
