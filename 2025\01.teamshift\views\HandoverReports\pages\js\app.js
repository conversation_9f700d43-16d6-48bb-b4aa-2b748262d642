/**
 * 交接统计报表主应用 - 使用Composition API整合所有模块
 */
document.addEventListener('DOMContentLoaded', () => {
  // 获取并注册Element Plus图标
  const icons = window['@element-plus/icons-vue']
  const { defineAsyncComponent } = Vue

  /**
   * 详情表格组件（抽屉形式）
   */
  const DetailDrawerComponent = {
    name: 'DetailDrawerComponent',
    components: {
      HtVxeTable: defineAsyncComponent(() => import('../../../../components/VxeTable/HtVxeTable.js')),
    },
    props: {
      title: {
        type: String,
        default: '详细数据',
      },
      data: {
        type: Array,
        default: () => [],
      },
      visible: {
        type: Boolean,
        default: false,
      },
      onClose: {
        type: Function,
        default: () => {},
      },
    },
    setup(props) {
      // 关闭抽屉
      const handleClose = () => {
        props.onClose()
      }
      const columns = [
        {
          field: 'str_wo',
          title: 'WO',
          minWidth: 100,
          filterRender: { name: 'FilterInput' },
          filters: [{ data: '' }],
        },
        {
          field: 'str_esn',
          title: 'ESN',
          minWidth: 100,
          filterRender: { name: 'FilterInput' },
          filters: [{ data: '' }],
        },
        {
          field: 'str_task_type',
          title: 'Type',
          minWidth: 100,
          filterRender: { name: 'FilterInput' },
          filters: [{ data: '' }],
        },
        {
          field: 'str_sm',
          title: 'SM',
          minWidth: 100,
          filterRender: { name: 'FilterInput' },
          filters: [{ data: '' }],
        },
        {
          field: 'str_task',
          title: '任务名称',
          minWidth: 100,
          filterRender: { name: 'FilterInput' },
          filters: [{ data: '' }],
        },
        {
          field: 'str_shift',
          title: '班次',
          minWidth: 100,
          filterRender: { name: 'FilterInput' },
          filters: [{ data: '' }],
        },
        {
          field: 'str_commit',
          title: '交班人',
          minWidth: 100,
          filterRender: { name: 'FilterInput' },
          filters: [{ data: '' }],
        },
        {
          field: 'str_receive',
          title: '接班人',
          minWidth: 100,
          filterRender: { name: 'FilterInput' },
          filters: [{ data: '' }],
        },
        {
          field: 'dt_commit',
          title: '交班时间',
          minWidth: 100,
          filterRender: { name: 'FilterCalendar' },
        },
        {
          field: 'dt_receive',
          title: '接班时间',
          minWidth: 100,
          filterRender: { name: 'FilterCalendar' },
        },
        {
          field: 'int_status',
          title: '是否交班',
          minWidth: 100,
          filterRender: { name: 'FilterInput' },
          formatter: ({ row }) => {
            return row.int_status === -1 ? '未提交' : row.int_status === 0 ? '已提交' : '已接收'
          },
        },
        {
          field: 'percentage',
          title: '交接率',
          minWidth: 100,
          filterRender: { name: 'FilterInput' }
        },
      ]

      return {
        handleClose,
        columns,
      }
    },
    template: /*html*/ `
      <el-drawer
        class="common-drawer"
        v-model="visible"
        :title="title"
        direction="rtl"
        size="80%"
        :destroy-on-close="false"
        :close-on-click-modal="false"
        :show-close="true"
        :with-header="true"
        @close="handleClose"
      >
        <div class="p-6">
          <HtVxeTable :table-data="data" :table-columns="columns" />
        </div>

        <template #footer>
          <el-button type="danger" @click="handleClose">关闭</el-button>
        </template>
      </el-drawer>
    `,
  }

  /**
   * 统计卡片组件
   */
  const StatCardComponent = {
    name: 'StatCardComponent',
    props: {
      title: {
        type: String,
        required: true,
      },
      value: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
      type: {
        type: String,
        default: 'info',
        validator: (value) => ['info', 'success', 'warning', 'danger'].includes(value),
      },
      icon: {
        type: String,
        default: '',
      },
    },
    setup(props) {
      const percentage = Vue.computed(() => {
        if (props.total === 0) return 0
        return Math.round((props.value / props.total) * 100)
      })

      return {
        percentage,
      }
    },
    template: `
            <div class="stat-card p-4 rounded-lg shadow-sm bg-white hover:shadow-md">
                <div class="flex justify-between items-start mb-3">
                    <h3 class="text-lg font-semibold text-gray-700">{{ title }}</h3>
                    <span class="text-sm px-2 py-1 rounded-full" :class="'bg-' + type + '-100 text-' + type + '-700'">
                        {{ percentage }}%
                    </span>
                </div>
                <div class="flex justify-between items-end">
                    <span class="text-3xl font-bold text-gray-800">{{ value }}</span>
                    <span class="text-sm text-gray-500">总计: {{ total }}</span>
                </div>
                <el-progress 
                    :percentage="percentage" 
                    :color="type === 'success' ? '#67C23A' : type === 'warning' ? '#E6A23C' : type === 'danger' ? '#F56C6C' : '#409EFF'"
                    class="mt-3"
                    :stroke-width="8">
                </el-progress>
            </div>
        `,
  }

  /**
   * 主应用组件
   */
  const App = {
    name: 'HandoverReportApp',
    components: {
      'filter-component': FilterModule.createFilterComponent(),
      'submission-chart': SubmissionChartModule.createChartComponent(),
      'receiving-chart': ReceivingChartModule.createChartComponent(),
      'detail-drawer': DetailDrawerComponent,
      'stat-card': StatCardComponent,
    },
    setup() {
      // 筛选条件
      const filters = Vue.reactive({
        dateRange: [moment().format('YYYY-MM-DD'), moment().add(14, 'day').format('YYYY-MM-DD')],
        businessTypes: '105',
      })

      // 详情数据相关状态
      const detailTitle = Vue.ref('详细数据')
      const detailData = Vue.ref([])
      const drawerVisible = Vue.ref(false)

      // 统计数据
      const statsData = Vue.reactive({
        submission: {
          submitted: 0,
          unsubmitted: 0,
          total: 0,
        },
        receiving: {
          received: 0,
          unreceived: 0,
          total: 0,
        },
      })

      // 图表组件引用
      const submissionChartRef = Vue.ref(null)
      const receivingChartRef = Vue.ref(null)

      // 存储图表刷新方法
      const chartsRefresh = Vue.reactive({
        submission: null,
        receiving: null,
      })

      // 处理筛选器变化
      const handleFilterChange = (newFilters) => {
        Object.assign(filters, newFilters)
        // 刷新图表
        refreshAllCharts()
      }

      // 刷新所有图表
      const refreshAllCharts = () => {
        submissionChartRef.value.loadData()
        receivingChartRef.value.loadData()
      }

      // 处理图表点击
      const handleChartClick = (event) => {
        console.log('Chart clicked:', event) // 调试信息

        const { type, dataType, data } = event

        // 设置详情表格标题
        let title = ''
        if (type === 'submission') {
          title = dataType === '已提交' ? '已提交交接事项' : '未提交交接事项'
        } else if (type === 'receiving') {
          title = dataType === '已接收' ? '已接收交接事项' : '未接收交接事项'
        }
        detailTitle.value = title

        // 更新详情数据
        detailData.value = data || []

        // 显示抽屉
        drawerVisible.value = true
      }

      // 关闭抽屉
      const closeDrawer = () => {
        drawerVisible.value = false
      }

      // 更新统计数据
      const updateStatsData = (type, data) => {
        if (type === 'submission') {
          statsData.submission.submitted = data.submitted || 0
          statsData.submission.unsubmitted = data.unsubmitted || 0
          statsData.submission.total = data.total || 0
        } else if (type === 'receiving') {
          statsData.receiving.received = data.received || 0
          statsData.receiving.unreceived = data.unreceived || 0
          statsData.receiving.total = data.total || 0
        }
      }

      // 接收图表数据更新通知
      const handleChartDataUpdate = (event) => {
        const { type, data } = event
        updateStatsData(type, data)
      }

      return {
        filters,
        detailTitle,
        detailData,
        drawerVisible,
        statsData,
        submissionChartRef,
        receivingChartRef,
        handleFilterChange,
        handleChartClick,
        closeDrawer,
        handleChartDataUpdate,
        refreshAllCharts,
      }
    },
    template: /*html*/ `
      <div class="w-full">
        <div class="module-spacing">
          <h1 class="page-title text-center">交接统计报表</h1>
        </div>

        <!-- 筛选器组件 -->
        <filter-component class="module-spacing" 
          :on-filter-change="handleFilterChange" 
          :refresh-charts="refreshAllCharts" />

        <!-- 统计卡片区域 -->
        <div class="grid-container stats module-spacing">
          <stat-card 
            title="已提交交接" 
            :value="statsData.submission.submitted" 
            :total="statsData.submission.total" 
            type="success" 
          />
          <stat-card 
            title="未提交交接" 
            :value="statsData.submission.unsubmitted" 
            :total="statsData.submission.total" 
            type="danger" 
          />
          <stat-card 
            title="已接收交接" 
            :value="statsData.receiving.received" 
            :total="statsData.receiving.total" 
            type="success" 
          />
          <stat-card 
            title="未接收交接" 
            :value="statsData.receiving.unreceived" 
            :total="statsData.receiving.total" 
            type="danger" 
          />
        </div>

        <!-- 统计图表区域 -->
        <div class="grid-container module-spacing">
          <!-- 交接提交统计 -->
          <submission-chart 
            ref="submissionChartRef"
            :filters="filters" 
            :on-chart-click="handleChartClick" 
            :on-data-update="handleChartDataUpdate"
            class="chart-container"
          />
          
          <!-- 交接接收统计 -->
          <receiving-chart 
            ref="receivingChartRef"
            :filters="filters" 
            :on-chart-click="handleChartClick" 
            :on-data-update="handleChartDataUpdate"
            class="chart-container"
          />
        </div>

        <!-- 提示信息 -->
        <div class="module-spacing mt-4 text-center text-sm text-gray-500">
          <p>点击图表区域查看详细数据</p>
        </div>

        <!-- 详情数据抽屉 -->
        <detail-drawer :title="detailTitle" :data="detailData" :visible="drawerVisible" :on-close="closeDrawer" />
      </div>
    `,
  }

  // 创建并挂载应用
  const app = Vue.createApp(App)

  // 使用Element Plus
  app.use(ElementPlus)

  // 注册所有图标组件
  if (icons) {
    for (const [key, component] of Object.entries(icons)) {
      app.component(key, component)
    }
  }

  app.use(VxeUI)
  app.use(VXETable)

  // 挂载应用
  app.mount('#app')
})
