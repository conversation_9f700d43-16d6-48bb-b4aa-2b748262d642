const { ref, defineComponent } = Vue
import PendingHandover from '../../handover/pending.handover.js'

// ESN下的单元格内容组件
export default defineComponent({
  name: 'EsnCell',
  components: {
    PendingHandover,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
  },
  setup() {
    // 查看支持检验
    const handleOpenIsShowInspectSupport = (row) => {
      console.log(row)
    }

    // 确认排班等调整完毕
    const handleChangeVg = (row) => {
      console.log(row)
    }

    const openStartWorkDialog = ref(false)
    const startWorkIdWo = ref('')
    const isStart = ref(false)
    // 开工
    const handleStartWork = (row) => {
      startWorkIdWo.value = row.id_wo
      isStart.value = true
      openStartWorkDialog.value = true
    }

    const handleCloseStartWorkDialog = () => {
      openStartWorkDialog.value = false
      isStart.value = false
    }

    return {
      handleOpenIsShowInspectSupport,
      handleChangeVg,
      handleStartWork,
      handleCloseStartWorkDialog,
      openStartWorkDialog,
      startWorkIdWo,
      isStart,
    }
  },
  template: /*html*/ `
    <div class="flex h-full w-full flex-col relative min-h-[60px]">
      <!-- ESN 标题 -->
      <div class="m-2 text-center">
        <div class="rounded px-2">
          <span class="text-[14px] font-semibold">{{ row.str_team }}</span>
        </div>
      </div>
      <!-- 操作按钮区域 - 固定在底部 -->
      <div class="absolute bottom-0 left-0 flex w-full items-center justify-center pb-1">
        <el-button text type="primary" @click="handleStartWork(row)">开工</el-button>
      </div>
    </div>
    <el-drawer
      class="common-drawer"
      v-model="openStartWorkDialog"
      title="开工"
      size="80%"
      :append-to-body="true"
      @close="handleCloseStartWorkDialog"
    >
      <pending-handover v-if="openStartWorkDialog" :id-wo="startWorkIdWo" strFlow="F2" :is-start="isStart" :start-work-row="row" />
    </el-drawer>
  `,
}) 