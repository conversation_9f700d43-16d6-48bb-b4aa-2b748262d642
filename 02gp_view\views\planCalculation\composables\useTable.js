import { queryCalculationList } from '../../../api/calculation.js'
import { useTableColumns } from './useTableColumns.js'
import { useTableFilter } from './useTableFilter.js'

const { reactive, onMounted, ref, computed } = Vue

export function useTable() {
  // 表格数据状态
  const state = reactive({
    tableData: [],
    tableColumns: [],
  })

  // 分页状态
  const pagePagerState = reactive({
    currentPage: 1,
    pageSize: 100,
    total: 0,
  })

  // 使用列配置 composable
  const { generateColumns, updateFlowOptions } = useTableColumns()
  
  // 使用过滤器 composable
  const { 
    parentFilterList, 
    handleFilterChange: handleFilterChangeBase,
    addCustomFilter,
    removeFilter,
    clearFilters,
    clearTableFilters,
    clearHeaderFilters,
    getHeaderFilters,
    getTableFilters,
    mergeFilters
  } = useTableFilter()

  // 原始数据缓存
  const originalData = ref([])

  // 计算属性：当前页数据
  const currentPageData = computed(() => {
    const start = (pagePagerState.currentPage - 1) * pagePagerState.pageSize
    const end = pagePagerState.currentPage * pagePagerState.pageSize
    return originalData.value.slice(start, end)
  })

  // 初始化表格列
  const initializeColumns = () => {
    state.tableColumns = generateColumns()
  }

  // 提取 Flow 选项并更新列配置
  const extractAndUpdateFlowOptions = (data) => {
    const flowSet = new Set(data.map(item => item.str_flow))
    const flows = Array.from(flowSet)
    updateFlowOptions(flows)
    
    // 重新生成列配置
    state.tableColumns = generateColumns()
  }

  // 过滤器变更处理（包装原始处理函数）
  const handleFilterChange = async (panel) => {
    // 重置当前页
    pagePagerState.currentPage = 1
    
    // 处理过滤器变更
    handleFilterChangeBase(panel)
    
    // 重新获取数据
    await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize)
  }

  /**
   * 获取表格数据（前端分页）
   * @param {number} currentPage - 当前页
   * @param {number} pageSize - 每页条数
   * @param {Array} additionalFilters - 额外过滤条件
   * @return {Promise<void>}
   */
  const getTableDataByFrontPage = async (currentPage = 1, pageSize = 100, additionalFilters = []) => {
    try {
      // 合并过滤器
      const filterList = mergeFilters(additionalFilters)
      
      // 获取数据
      const tableData = await queryCalculationList(filterList)
      
      // 缓存原始数据
      originalData.value = tableData
      
      // 如果是首次加载，提取 Flow 选项
      if (state.tableColumns.length === 0) {
        extractAndUpdateFlowOptions(tableData)
      }
      
      // 计算分页
      const start = (currentPage - 1) * pageSize
      const end = currentPage * pageSize
      state.tableData = tableData.slice(start, end)
      pagePagerState.total = tableData.length
      
    } catch (error) {
      console.error('获取表格数据失败:', error)
      ElementPlus.ElMessage.error('获取数据失败')
    }
  }

  // 分页变更处理
  const handlePageChange = async (pageInfo) => {
    pagePagerState.currentPage = pageInfo.currentPage
    pagePagerState.pageSize = pageInfo.pageSize
    
    // 从缓存数据中获取当前页数据
    const start = (pagePagerState.currentPage - 1) * pagePagerState.pageSize
    const end = pagePagerState.currentPage * pagePagerState.pageSize
    state.tableData = originalData.value.slice(start, end)
  }

  // 搜索功能
  const handleSearch = async (searchFilters = []) => {
    pagePagerState.currentPage = 1
    await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize, searchFilters)
  }

  // 重置功能
  const handleReset = async () => {
    clearFilters()
    pagePagerState.currentPage = 1
    await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize)
  }

  // 刷新数据
  const refreshData = async () => {
    await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize)
  }

  // 导出的方法和状态
  return {
    // 状态
    state,
    pagePagerState,
    parentFilterList,
    
    // 数据操作
    getTableDataByFrontPage,
    refreshData,
    handleSearch,
    handleReset,
    
    // 过滤器操作
    handleFilterChange,
    addCustomFilter,
    removeFilter,
    clearFilters,
    clearTableFilters,
    clearHeaderFilters,
    getHeaderFilters,
    getTableFilters,
    
    // 分页操作
    handlePageChange,
    
    // 列配置
    initializeColumns
  }
}
