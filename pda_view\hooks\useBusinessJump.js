const { unref } = Vue

export function useBusinessJump() {
  const nodeNameMap = {
    1: '行政接收明细',
    2: '转包可用件检验',
    3: '我的入库任务',
    5: '我的发料任务',
    6: '集件零件管理',
    8: '进口运单明细管理',
    9: 'F1-2零件登记',
    10: '转包零件跟踪',
    11: '待装箱零件',
    12: '出口运单明细查询',
    13: '采购零件跟踪',
    15: '零件报表',
    41: '锁库处理',
    42: '锁库处理',
    43: '锁库处理',
    111: '转包工卡接收',
    142: '待采购管理',
    141: '待采购管理',
    411: 'CSM确认',
    412: '进场缺件工程确认',
  }
  const nodeIdMap = {
    1: '1435766975762141185',
    2: '1435766975787307009',
    3: '1435766979071447041',
    5: '1448490182793764864',
    6: '1435766996880461825',
    8: '1587605073650782209',
    9: '1435767004610564097',
    10: '1435766992040235009',
    11: '1552459213409619969',
    12: '1579630248701464577',
    13: '1435766977628606465',
    15: '1435766981386702849',
    41: '1453282700483895296',
    42: '1453282700483895296',
    43: '1453282700483895296',
    111: '1435766991432060929',
    142: '1457909599063515136',
    141: '1457909599063515136',
    411: '1453280769795432448',
    412: '1435766995211128833',
  }

  /* ---------------------- 起运准备跳转 ---------------------- */
  const jumpByEleven = ($table) => {
    const idListByOne = $table
      .getCheckboxRecords()
      .filter((item) => item.is_state_dispname === '待装箱')
      .map((item) => item.id_main)
      .filter((item) => item !== null)
      .join(',')

    if (idListByOne) {
      com.refreshTab(nodeNameMap[11], `/Page/?moduleid=${nodeIdMap[11]}&qrc_id=${idListByOne}`)
    }
    const idListByTwo = $table
      .getCheckboxRecords()
      .filter((item) => item.is_state_dispname === '待运出')
      .map((item) => item.id_main)
      .filter((item) => item !== null)
      .join(',')
    if (idListByTwo) {
      com.refreshTab('待运出零件', `/Page/?moduleid=1552497359757250561&qrc_id=${idListByTwo}`)
    }
  }

  const skipSelectEvent = (tableDom, currentNode) => {
    const $table = unref(tableDom)
    if (!$table) return
    // 行政接收
    if (currentNode === '1') {
      const idList = $table
        .getCheckboxRecords()
        .map((item) => item.id_main_top)
        .join(',')
      com.refreshTab(nodeNameMap[currentNode], `/Page/?moduleid=${nodeIdMap[currentNode]}&qrc_id=${idList}`)
      return
    }
    if (currentNode === '2') {
      // 采购可用件
      const idList2 = $table
        .getCheckboxRecords()
        .filter((item) => item.str_from === '采购可用件')
        .map((item) => item.id_main)
        .join(',')
      const id = '1435766975833444353'
      com.refreshTab('采购可用件检验', `/Page/?moduleid=${id}&qrc_id=${idList2}`)
      // 转包可用件
      const idList = $table
        .getCheckboxRecords()
        .filter((item) => item.str_from === '转包可用件')
        .map((item) => item.id_main)
        .join(',')
      com.refreshTab(nodeNameMap[currentNode], `/Page/?moduleid=${nodeIdMap[currentNode]}&qrc_id=${idList}`)
      return
    }

    if (currentNode === '412') {
      // 构型确认
      const idList2 = $table
        .getCheckboxRecords()
        .filter((item) => item.str_type === '构型确认')
        .map((item) => item.id_main)
        .join(',')
      const id = '1453229113389096960'
      if (idList2 !== '') {
        com.refreshTab('构型管理', `/Page/?moduleid=${id}&qrc_id=${idList2}`)
      }
      // 进场缺件
      const idList = $table
        .getCheckboxRecords()
        .filter((item) => item.str_type === '进场缺件')
        .map((item) => item.id_main_top)
        .join(',')
      if (idList !== '') {
        com.refreshTab(nodeNameMap[currentNode], `/Page/?moduleid=${nodeIdMap[currentNode]}&qrc_id=${idList}`)
      }
      return
    }

    // 转包PO
    if (currentNode === '111') {
      const idList = $table
        .getCheckboxRecords()
        .map((item) => item.id_main_top)
        .join(',')
      com.refreshTab(nodeNameMap[currentNode], `/Page/?moduleid=${nodeIdMap[currentNode]}&qrc_id=${idList}`)
      return
    }
    // 起运准备
    if (currentNode === '11') {
      jumpByEleven($table)
      return
    }

    const idList = $table
      .getCheckboxRecords()
      .map((item) => item.id_main)
      .join(',')
    // const idList = '1528934211842478080,1552815223118761985'
    com.refreshTab(nodeNameMap[currentNode], `/Page/?moduleid=${nodeIdMap[currentNode]}&qrc_id=${idList}`)
  }

  return {
    skipSelectEvent,
  }
}
