const { ref, onMounted, nextTick, onUnmounted } = Vue
const { ElMessage } = ElementPlus
import {
  queryTeam,
  get_pt_get_shift,
  get_pt_get_shift_people,
  pt_save_team_shift_peolpe,
} from '../../../api/shift.calendar.team/index.js'
import { getCommonEnumList } from '../../../api/comm/index.js'

// 定义节假日数据
const HOLIDAY_DATA = {}

// 获取节假日数据
const getHolidays = (year) => {
  const holidays = {}
  Object.entries(HOLIDAY_DATA).forEach(([date, info]) => {
    if (date.startsWith(year)) {
      holidays[date] = info
    }
  })
  return holidays
}

export default {
  name: 'ShiftCalendarTeam',
  props: {
    str_sec: String,
  },
  setup(props) {
    // 加载状态
    const loading = ref(true)

    // 节假日数据
    const holidays = ref({})

    // 当前选择的月份 - 修改初始化方式
    const currentMonth = ref(moment().format('YYYY-MM'))

    // 搜索表单
    const searchForm = ref({
      month: currentMonth.value,
      team: '',
      type: '',
    })

    // 分页相关状态
    const pagination = ref({
      currentPage: 1,
      pageSize: 10,
      total: 0,
    })

    // 添加弹框相关的状态
    const dialogVisible = ref(false)
    const selectedShift = ref(null)
    const selectedDate = ref(null)
    const selectedTeam = ref(null)
    const selectedShiftType = ref('')
    const shiftTypeOptions = ref([])

    // 模拟Team数据
    const teamMembers = ref([])

    const teamSecMembers = ref([{ id: 'SM31', name: '张三', ability: 'SM31' }])

    // 获取月份的起始日期和结束日期
    const getMonthRange = (dateStr) => {
      const momentObj = moment(dateStr, 'YYYY-MM')
      return {
        start: momentObj.clone().startOf('month'),
        end: momentObj.clone().endOf('month'),
        year: momentObj.year(),
        month: momentObj.month() + 1,
      }
    }

    // 更新节假日数据
    const updateHolidays = (year) => {
      holidays.value = getHolidays(year)
    }

    // 获取当前月的日期数组
    const getDateRange = () => {
      const monthRange = getMonthRange(currentMonth.value)
      const dates = []
      const current = monthRange.start.clone()
      const end = monthRange.end.clone()

      while (current.isSameOrBefore(end, 'day')) {
        const dateStr = current.format('YYYY-MM-DD')
        const holiday = holidays.value[dateStr]
        dates.push({
          date: dateStr,
          day: current.date(),
          weekday: '日一二三四五六'.charAt(current.day()),
          isWeekend: current.day() === 0 || current.day() === 6,
          isHoliday: holiday?.type === 'holiday',
          isWorkday: holiday?.type === 'workday',
          holidayName: holiday?.name,
          isToday: current.isSame(moment(), 'day'),
          weekNumber: current.week(),
        })
        current.add(1, 'day')
      }
      return dates
    }

    // 获取表头数据
    const getHeaderData = () => {
      const weeks = []
      const dates = getDateRange()

      let currentWeek = null
      let weekSpan = 0
      let weekDates = []

      dates.forEach((date, index) => {
        const momentDate = moment(date.date)
        const weekNumber = momentDate.week()

        if (currentWeek === null) {
          currentWeek = weekNumber
        }

        if (currentWeek !== weekNumber) {
          weeks.push({
            weekNumber: currentWeek,
            span: weekSpan,
            dates: [...weekDates],
          })
          weekSpan = 0
          weekDates = []
          currentWeek = weekNumber
        }

        weekSpan++
        weekDates.push(date)

        // 处理最后一周
        if (index === dates.length - 1) {
          weeks.push({
            weekNumber: currentWeek,
            span: weekSpan,
            dates: [...weekDates],
          })
        }
      })

      return {
        weeks,
        dates,
      }
    }

    // 表格数据
    const tableData = ref([])
    const headerData = ref({})

    // 获取表格数据
    const getTableData = async () => {
      loading.value = true
      headerData.value = getHeaderData()
      try {
        const params = {
          str_year: currentMonth.value.split('-')[0],
          str_month: currentMonth.value.split('-')[1],
          str_team: searchForm.value.team,
          type: searchForm.value.type,
          currentPage: pagination.value.currentPage,
          pageSize: pagination.value.pageSize,
          str_sec: props.str_sec,
        }

        // 调用API获取数据
        const response = await queryTeam(params)

        // 适配新的数据结构
        if (response && response.items) {
          tableData.value = response.items
          pagination.value.total = response.totalCount || 0
        } else {
          tableData.value = []
          pagination.value.total = 0
          ElMessage.warning('未获取到数据')
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        ElMessage.error(error.message || '数据加载失败')
        tableData.value = []
        pagination.value.total = 0
      } finally {
        loading.value = false
      }
    }

    // 处理页码变化
    const handleCurrentChange = (page) => {
      pagination.value.currentPage = page
      getTableData()
    }

    // 处理每页条数变化
    const handleSizeChange = (size) => {
      pagination.value.pageSize = size
      pagination.value.currentPage = 1
      getTableData()
    }

    // 处理月份变化
    const handleMonthChange = async (date) => {
      if (!date) {
        currentMonth.value = moment().format('YYYY-MM')
      } else {
        currentMonth.value = date
      }

      searchForm.value.month = currentMonth.value
      const year = moment(currentMonth.value).year()
      updateHolidays(year)
      pagination.value.currentPage = 1 // 切换月份时重置页码
      // await getTableData()
    }

    // 获取单元格样式
    const getCellClass = (shift, date) => {
      const classes = ['shift-cell']

      if (!shift) {
        if (moment(date).isSame(moment(), 'day')) {
          classes.push('today')
        }
        return classes
      }

      // 适配新的数据结构，同时兼容原有结构
      const shiftType = shift.str_shift_team || shift.str_shift || ''

      if (shiftType === '早班') {
        classes.push('status-day')
      } else if (shiftType === '晚班') {
        classes.push('status-night')
      } else if (shiftType === '行政班') {
        classes.push('status-rest')
      }

      if (moment(date).isSame(moment(), 'day')) {
        classes.push('today')
      }

      return classes
    }

    // 获取表头样式
    const getHeaderClass = (date) => {
      if (date.isHoliday) {
        return 'holiday-header'
      }
      if (date.isWorkday) {
        return 'workday-header'
      }
      if (date.isWeekend && !date.isWorkday) {
        return 'weekend-header'
      }
      return 'table-header'
    }
    // 处理单元格点击事件
    const handleCellClick = async (shift, date, team) => {
      let shiftType = shift?.id_shift || ''
      shiftCopy.value = shiftType
      // 判断date是否为周末
      if (date.isWeekend) {
        shiftType = shiftTypeOptions.value.find((item) => item.str_name === '行政班')?.id
      }
      // 如果team有id_team字段，则调用handleShiftPeople
      if (team.id_team) {
        handleShiftPeople(team.id_team, date.date, date.isWeekend)
      } else {
        // 清空teamMembers，因为新数据结构可能不需要
        teamMembers.value = []
      }
      // 获取下拉状态枚举
      await fetchOperationStatus()

      // 设置选中的班次类型，兼容新旧数据结构
      selectedShiftType.value = shiftType
      selectedShift.value = shift
      selectedDate.value = date
      selectedTeam.value = team
      dialogVisible.value = true
    }
    const handleShiftTypeOptions = async () => {
      const response = await get_pt_get_shift()
      shiftTypeOptions.value = response.data
    }

    // 班次副本
    const shiftCopy = ref('')
    const handleShiftPeople = async (id_team, dt_leave, isWeekend) => {
      const response = await get_pt_get_shift_people(id_team, dt_leave)
      teamMembers.value = response.data.map((item) => ({
        ...item,
        // 如果是周末并且str_type为-1，则设置为1
        str_type: isWeekend && !shiftCopy.value ? '1' : item.str_type,
      }))
    }
    const handShiftsInfo = (shifts, date) => {
      if (shifts && shifts.length > 0) {
        // 适配新的数据结构，使用dt_plan字段查找班次信息
        const shift = shifts.find((shift) => shift.dt_plan === date)

        // 如果找到班次信息，确保它有str_shift_team字段
        if (shift) {
          // 如果没有str_shift_team字段，但有str_shift字段，则使用str_shift
          if (!shift.str_shift_team && shift.str_shift) {
            shift.str_shift_team = shift.str_shift
          }
          return shift
        }
      }
      return null
    }
    const handSave = async (shifts, date) => {
      dialogVisible.value = false

      // 构建参数，同时兼容新旧数据结构
      const params = {
        team_shift_calendar: {
          id_team: selectedTeam.value.id_team,
          id_staff: selectedTeam.value.id || selectedTeam.value.id_staff,
          dt_pt: selectedDate.value.date,
          dt_plan: selectedDate.value.date,
          id_shift: selectedShiftType.value,
          str_year: selectedDate.value.date.split('-')[0],
          str_month: selectedDate.value.date.split('-')[1],
          str_shift: shiftTypeOptions.value.find((item) => item.id === selectedShiftType.value)?.str_name || '',
        },
        pt_leaves: teamMembers.value,
      }

      const response = await pt_save_team_shift_peolpe(params)
      if (response.code === 200) {
        ElMessage.success('保存成功')
        await getTableData()
      }
    }

    // 表格高度
    const tableHeight = ref('400px')

    // 表格容器引用
    const tableContainerRef = ref(null)

    // 窗口大小变化监听器
    let resizeObserver = null

    // 计算表格高度
    const calculateTableHeight = () => {
      nextTick(() => {
        // 获取窗口高度
        let windowHeight = window.innerHeight

        // 检查是否在iframe中
        const isInIframe = window !== window.parent

        if (isInIframe) {
          try {
            // 尝试获取iframe的高度
            const iframe = window.frameElement
            if (iframe) {
              windowHeight = iframe.clientHeight
            }
          } catch (e) {
            console.error('获取iframe高度失败:', e)
          }
        }

        // 获取表格容器元素
        const tableContainer = tableContainerRef.value
        if (!tableContainer) return

        // 获取表格容器到视口顶部的距离
        const containerTop = tableContainer.getBoundingClientRect().top

        // 获取分页器高度
        const paginationHeight = 60

        // 计算表格可用高度 (窗口高度 - 表格顶部位置 - 分页器高度 - 底部边距)
        const availableHeight = windowHeight - containerTop - paginationHeight - 20

        // 设置最小高度
        const minHeight = 300

        // 更新表格高度
        tableHeight.value = `${Math.max(availableHeight, minHeight)}px`
      })
    }

    // 下拉状态枚举
    const operationStatus = ref([])
    // 获取下拉状态枚举
    const fetchOperationStatus = async () => {
      const response = await getCommonEnumList('pt_staff_status')
      operationStatus.value = response.map((item) => ({
        value: item.str_code,
        label: item.str_name,
      }))
    }

    // 组件挂载时初始化数据
    onMounted(async () => {
      const year = moment().year()
      updateHolidays(year)
      searchForm.value.month = currentMonth.value
      await getTableData()
      await handleShiftTypeOptions()

      // 初始计算表格高度
      calculateTableHeight()

      // 监听窗口大小变化
      resizeObserver = new ResizeObserver(calculateTableHeight)

      // 监听窗口大小变化
      window.addEventListener('resize', calculateTableHeight)

      // 如果在iframe中，尝试监听iframe大小变化
      if (window !== window.parent) {
        try {
          const iframe = window.frameElement
          if (iframe && resizeObserver) {
            resizeObserver.observe(iframe)
          }
        } catch (e) {
          console.error('监听iframe大小变化失败:', e)
        }
      }

      // 监听表格容器大小变化
      if (tableContainerRef.value && resizeObserver) {
        resizeObserver.observe(tableContainerRef.value)
      }
    })

    // 组件卸载时清理监听器
    onUnmounted(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', calculateTableHeight)

      // 断开ResizeObserver连接
      if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
      }
    })

    // 搜索
    const handleSearch = () => {
      pagination.value.currentPage = 1
      getTableData()
    }

    return {
      loading,
      searchForm,
      tableData,
      headerData,
      currentMonth,
      pagination,
      operationStatus,
      getCellClass,
      getHeaderClass,
      handleMonthChange,
      handleCurrentChange,
      handleSizeChange,
      dialogVisible,
      selectedShift,
      selectedDate,
      selectedTeam,
      handleCellClick,
      selectedShiftType,
      teamMembers,
      teamSecMembers,
      shiftTypeOptions,
      handleShiftTypeOptions,
      handShiftsInfo,
      handSave,
      tableHeight,
      tableContainerRef,
      handleSearch,
    }
  },
  template: /*html*/ `
    <div class="shift-calendar-container">
      <!-- 加载遮罩 -->
      <div v-if="loading" class="loading-mask">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span class="loading-text">加载中...</span>
      </div>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="月份">
          <el-date-picker
            v-model="currentMonth"
            type="month"
            format="YYYY-MM"
            value-format="YYYY-MM"
            :clearable="false"
            :editable="false"
            placeholder="选择月份"
          />
        </el-form-item>
        <el-form-item label="Team">
          <el-input v-model="searchForm.team" placeholder="请输入Team" />
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </el-form-item>
      </el-form>

      <!-- 日历表格 -->
      <div ref="tableContainerRef">
        <el-table :data="tableData" border class="w-full" :height="tableHeight">
          <!-- 基本信息列 -->
          <el-table-column prop="str_team" label="Team" min-width="80" class-name="table-header" fixed="left" />
          <el-table-column prop="str_leader" label="组长" min-width="80" class-name="table-header" fixed="left" />

          <!-- 使用多级表头实现周信息 -->
          <template v-for="week in headerData.weeks" :key="'week-' + week.weekNumber">
            <!-- 周信息作为父级表头 -->
            <el-table-column :label="'第' + week.weekNumber + '周'" align="center">
              <!-- 日期列作为子级表头 -->
              <el-table-column
                v-for="date in week.dates"
                :key="date.date"
                align="center"
                min-width="80"
                :class-name="getHeaderClass(date)"
              >
                <!-- 自定义日期表头 -->
                <template #header>
                  <div class="p-1 text-xs md:text-sm">
                    <div>{{ date.day }} {{ date.weekday }}</div>
                    <div v-if="date.isHoliday" class="holiday-text text-xs">{{ date.holidayName }}</div>
                  </div>
                </template>

                <!-- 单元格内容 -->
                <template #default="scope">
                  <div
                    class="flex h-full w-full flex-col items-center justify-center p-1"
                    :class="getCellClass(handShiftsInfo(scope.row.shifts, date.date), date.date)"
                    @click="handleCellClick(handShiftsInfo(scope.row.shifts, date.date), date, scope.row)"
                  >
                    <div class="shift-type whitespace-nowrap text-xs md:text-sm">
                      {{ handShiftsInfo(scope.row.shifts, date.date)?.str_shift_team }}
                    </div>
                    <div
                      class="staff-count text-xs md:text-sm"
                      v-if="handShiftsInfo(scope.row.shifts, date.date)?.str_shift_team !== '休息'"
                    >
                      {{ handShiftsInfo(scope.row.shifts, date.date)?.int_attendance || 0 }}/{{ scope.row.int_total || 0
                      }}
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
          </template>
        </el-table>
      </div>

      <!-- 分页器 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 弹框组件 -->
      <el-dialog
        v-model="dialogVisible"
        title="资源编辑"
        width="50%"
        :close-on-click-modal="false"
        class="common-dialog shift-dialog"
        :destroy-on-close="true"
      >
        <div class="p-4">
          <!-- 小组信息 -->
          <div class="section-title">小组信息</div>
          <div class="grid grid-cols-3 gap-4">
            <div class="flex items-center gap-2">
              <label class="w-16">日期</label>
              <div>{{ selectedDate?.date || '' }}</div>
            </div>
            <div class="flex items-center gap-2">
              <label class="w-16">Team</label>
              <div>{{ selectedTeam?.str_team || '' }}</div>
            </div>
            <div class="flex items-center gap-2">
              <label class="w-16">总人数</label>
              <div>{{ selectedTeam?.int_total || 0 }}</div>
            </div>
            <div class="flex items-center gap-2">
              <label class="w-16">出勤人数</label>
              <div>{{ selectedShift?.int_attendance || 0 }}</div>
            </div>
            <div class="flex items-center gap-2">
              <label class="w-16">班次</label>
              <el-select v-model="selectedShiftType" clearable>
                <el-option v-for="item in shiftTypeOptions" :key="item.id" :label="item.str_name" :value="item.id" />
              </el-select>
            </div>
          </div>

          <!-- Team表格 -->
          <div class="section-title">Team成员</div>
          <div>
            <el-table :data="teamMembers" border>
              <el-table-column prop="str_code" label="工号" />
              <el-table-column prop="str_name" label="姓名" />
              <el-table-column prop="operation" label="状态">
                <template #default="scope">
                  <el-select v-model="scope.row.str_type">
                    <el-option v-for="item in operationStatus" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <template #footer>
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handSave">保存</el-button>
        </template>
      </el-dialog>
    </div>
  `,
}
