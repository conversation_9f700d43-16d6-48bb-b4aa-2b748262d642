const { h, defineComponent } = Vue
const { ElIcon } = ElementPlus
const { Promotion, User } = ElementPlusIconsVue

const CardInfo = defineComponent({
  name: 'CardInfo',
  props: {
    flow: {
      type: String,
      required: true,
    },
    teamName: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const createCard = (bgClass, iconBg, icon, iconColor, label, value) =>
      h('div', { class: `rounded-lg ${bgClass} p-2 shadow-sm` }, [
        h('div', { class: 'flex items-center space-x-3' }, [
          h('div', { class: `flex items-center justify-center rounded-full ${iconBg}` }, [
            h(ElIcon, { class: iconColor, size: 20 }, () => h(icon)),
          ]),
          h('div', null, [
            h('div', { class: 'text-sm text-gray-500' }, label),
            h('div', { class: 'text-lg font-semibold' }, value),
          ]),
        ]),
      ])

    return () =>
      h('div', { class: 'mb-6 grid grid-cols-1 gap-4 md:grid-cols-2' }, [
        createCard(
          'bg-gradient-to-r from-blue-50 to-indigo-50',
          'bg-blue-100',
          Promotion,
          'text-blue-600',
          '当前Flow',
          props.flow,
        ),
        createCard(
          'bg-gradient-to-r from-purple-50 to-pink-50',
          'bg-purple-100',
          User,
          'text-purple-600',
          '当前Team',
          props.teamName,
        ),
      ])
  },
})

export default CardInfo
