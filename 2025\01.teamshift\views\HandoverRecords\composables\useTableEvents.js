// 表格事件处理
export function useTableEvents() {
  const { ref } = Vue

  const selectedRows = ref([])
  const currentRow = ref(null)

  // 事件处理方法
  const handleCellClick = (params) => {
    const { row, column } = params
  }

  const handleRowClick = (params) => {
    console.log('行点击:', params)
    currentRow.value = params.row
  }

  const handleSortChange = (params) => {
    console.log('排序变化:', params)
  }

  const handleFilterChange = (params) => {
    console.log('过滤变化:', params)
  }

  const handleSelectionChange = (params) => {
    selectedRows.value = params.records
    console.log('选择变化:', selectedRows.value)
  }

  const handleRowDoubleClick = (params) => {
    console.log('行双击:', params)
    // 可以触发编辑模式
  }

  return {
    selectedRows,
    currentRow,
    handleCellClick,
    handleRowClick,
    handleSortChange,
    handleFilterChange,
    handleSelectionChange,
    handleRowDoubleClick,
  }
}
