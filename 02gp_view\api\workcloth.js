/**
 * @description 工装统计API
 */
import {post} from '../../config/axios/httpReuest.js';

export const queryWorkClothesList = async (queryLists = []) => {
    const list = [];
    const params = {
        ac: 'gp_workclothes_summary',
        queryLists,
    };
    const {data} = await post(params);
    if (data.code === 'error') {
        ElementPlus.ElMessage.error(data.text);
        return list;
    }
    list.push(...data.data);
    return list;
};

/**
 * @description 工装占用明细
 * @param {string} id
 */
export const queryworkclothesById = async (id=null) => {
    let res = null;
    const params = {
        ac: 'gp_workclothes_inuse',
        id,
    };
    const {data} = await post(params);
    if (data.code === 'error') {
        ElementPlus.ElMessage.error(data.text);
        return res;
    }
    res = data.data;
    return res;
};

