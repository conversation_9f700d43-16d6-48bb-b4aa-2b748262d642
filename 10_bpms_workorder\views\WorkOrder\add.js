const { ref } = Vue
export const WorkOrderAddComponent = {
  setup() {
    const active = ref(0)
    const form = ref({
      name: '',
      type: '',
      status: '',
      description: '',
    })
    /** 基本信息form */
    const basicForm = ref({
      name: '',
      type: '',
      status: '',
      description: '',
    })

    /**文件上传 */
    const fileList = ref([])
    const handleRemove = (file, fileList) => {
      console.log(file, fileList)
    }
    const handleSuccess = (response, file, fileList) => {
      console.log(response, file, fileList)
    }
    const handleError = (err, file, fileList) => {
      console.log(err, file, fileList)
    }

    const scrollbarRef = ref(null)

    /**锚点的变换 */
    const handleAnchorChange = val => {
      console.log(val)
    }
    return {
      active,
      form,
      handleAnchorChange,
      fileList,
      handleRemove,
      handleSuccess,
      handleError,
      scrollbarRef,
      basicForm,
    }
  },
  template: /*html*/ `
    <article class="flex flex-col bg-gray-200 p-4">
      <div class="sticky top-0 z-50 rounded-lg bg-white p-2 text-right shadow-sm">
        <button class="el-button el-button--primary rounded-sm" @click="active--">提交</button>
      </div>
      <div class="mt-4 flex flex-1 rounded-lg">
        <div class="flex-1">
          <div id="basic" class="bg-white p-4">
            <h2 class="text-lg font-bold border-l-4 pl-2 border-blue-500">基本信息</h2>
            <div class="my-4 border-b border-gray-200"></div>
            <el-form label-position="top" label-width="100px" :model="basicForm">
              <div class="grid gap-4 sm:grid-cols-1 lg:grid-cols-3">
                <el-form-item>
                  <template #label>
                    <div class="flex flex-col">
                      <div class="font-bold">姓名</div>
                      <em>Name:</em>
                    </div>
                  </template>
                  <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item>
                  <template #label>
                    <div class="flex flex-col">
                      <div class="font-bold">分部</div>
                      <em>Section:</em>
                    </div>
                  </template>
                  <el-select v-model="form.type" placeholder="请选择分部">
                    <el-option label="类型1" value="1"></el-option>
                    <el-option label="类型2" value="2"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <template #label>
                    <div class="flex flex-col">
                      <div class="font-bold">工号</div>
                      <em>ID:</em>
                    </div>
                  </template>
                  <el-input v-model="form.status" placeholder="请输入工号"></el-input>
                </el-form-item>
                <el-form-item>
                  <template #label>
                    <div class="flex flex-col">
                      <div class="font-bold">请选择您提议的日期</div>
                      <em>Date:</em>
                    </div>
                  </template>
                  <el-date-picker v-mode="basicForm.dt_submit" class="!w-full" type="date" placeholder="请选择您提议的日期"></el-date-picker>
                </el-form-item>
                <div class="sm:col-span-1 lg:col-span-2">
                  <el-form-item>
                    <template #label>
                      <div class="flex flex-col">
                        <div class="font-bold">提报时是否实施</div>
                        <em>Implemented or not when raising?</em>
                      </div>
                    </template>
                    <el-radio-group v-model="basicForm.is_implement">
                      <el-radio label="1">YES(A类)</el-radio>
                      <el-radio label="2">NO(B类)</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div class="sm:col-span-1 lg:col-span-3">
                  <el-form-item>
                    <template #label>
                      <div class="flex flex-col">
                        <div class="font-bold">建议描述</div>
                        <em>Proposal Description:</em>
                      </div>
                    </template>
                    <el-input v-model="basicForm.str_proposal" type="textarea" placeholder="请输入建议描述"></el-input>
                  </el-form-item>
                </div>
                <div class="sm:col-span-1 lg:col-span-3">
                  <el-form-item>
                    <template #label>
                      <div class="flex flex-col">
                        <div class="font-bold">您的建议解决方案</div>
                        <em>Your Suggested Solution:</em>
                      </div>
                    </template>
                    <el-input
                      v-model="form.description"
                      type="textarea"
                      placeholder="请输入您的建议解决方案"
                    ></el-input>
                  </el-form-item>
                </div>
                <el-form-item>
                  <template #label>
                    <div class="flex flex-col">
                      <div class="font-bold">实施的负责人</div>
                      <em>Who in charge:</em>
                    </div>
                  </template>
                  <el-input v-model="form.description" placeholder="请输入实施的负责人"></el-input>
                </el-form-item>
                <el-form-item>
                  <template #label>
                    <div class="flex flex-col">
                      <div class="font-bold">实施完成的时间</div>
                      <em>Complete Date:</em>
                    </div>
                  </template>
                  <el-date-picker class="!w-full" type="date" placeholder="选择实施完成的时间"></el-date-picker>
                </el-form-item>
                <el-form-item>
                  <template #label>
                    <div class="flex flex-col">
                      <div class="font-bold">建议实施后所产生的效益涉及哪些方面</div>
                      <em>The benefit involves:</em>
                    </div>
                  </template>
                  <el-checkbox-group v-model="form.status">
                    <el-checkbox label="1">Safety</el-checkbox>
                    <el-checkbox label="2">Quality</el-checkbox>
                    <el-checkbox label="3">Cost</el-checkbox>
                    <el-checkbox label="4">Delivery</el-checkbox>
                    <el-checkbox label="4">Personnel</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item>
                  <template #label>
                    <div class="flex flex-col">
                      <div class="font-bold">是否申请重大无形效益附加分</div>
                      <em>Apply for intangable value score?</em>
                    </div>
                  </template>
                  <el-radio-group v-model="form.status">
                    <el-radio label="1">YES</el-radio>
                    <el-radio label="2">NO</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div class="sm:col-span-1 lg:col-span-3">
                  <el-form-item>
                    <template #label>
                      <div class="flex flex-col">
                        <div class="font-bold">效益描述</div>
                        <em>Benefit Description:</em>
                      </div>
                    </template>
                    <el-input v-model="form.description" type="textarea" placeholder="请输入工单描述"></el-input>
                  </el-form-item>
                </div>
                <div class="sm:col-span-1 lg:col-span-3">
                <el-form-item>
                  <template #label>
                    <div class="flex flex-col">
                      <div class="font-bold">上传相关附件</div>
                      <em>Upload your relevant attachment:</em>
                    </div>
                  </template>
                  <el-upload
                    class="!w-full"
                    list-type="picture-card"
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :on-remove="handleRemove"
                    :onSuccess="handleSuccess"
                    :onError="handleError"
                    :file-list="fileList"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <!-- 第二部分 -->
          <div id="collect" class="mt-2 bg-white p-4">
            <h2 class="text-lg font-bold border-l-4 pl-2 border-blue-500">收集/整理/分类</h2>
            <div class="my-4 border-b border-gray-200"></div>
            <el-form label-position="top" label-width="100px">
              <div class="grid gap-4 sm:grid-cols-1 lg:grid-cols-3">
                <div class="sm:col-span-1 lg:col-span-3">
                  <el-form-item>
                    <template #label>
                      <div class="flex flex-col">
                        <div class="font-bold">建议</div>
                        <em>Proposal</em>
                      </div>
                    </template>
                    <el-input v-model="form.name" placeholder="请输入建议" type="textarea"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <!-- 第三部分 -->
          <div id="implement" class="mt-2 bg-white p-4">
            <h2 class="text-lg font-bold border-l-4 pl-2 border-blue-500">实施</h2>
            <div class="my-4 border-b border-gray-200"></div>
            <el-form label-position="top" label-width="100px">
              <div class="grid gap-4 sm:grid-cols-1 lg:grid-cols-3">
                <div class="sm:col-span-1 lg:col-span-3">
                  <el-form-item>
                    <template #label>
                      <div class="flex flex-col">
                        <div class="font-bold">建议</div>
                        <em>Proposal</em>
                      </div>
                    </template>
                    <el-input v-model="form.name" placeholder="请输入建议" type="textarea"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <!-- 第四部分 -->
          <div id="review" class="mt-2 bg-white p-4">
            <h2 class="text-lg font-bold border-l-4 pl-2 border-blue-500">评审</h2>
            <div class="my-4 border-b border-gray-200"></div>
            <el-form label-position="top" label-width="100px">
              <div class="grid gap-4 sm:grid-cols-1 lg:grid-cols-3">
                <div class="sm:col-span-1 lg:col-span-3">
                  <el-form-item>
                    <template #label>
                      <div class="flex flex-col">
                        <div class="font-bold">建议</div>
                        <em>Proposal</em>
                      </div>
                    </template>
                    <el-input v-model="form.name" placeholder="请输入建议" type="textarea"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <!-- 第五部分 -->
          <div id="feedback" class="mt-2 bg-white p-4">
            <h2 class="text-lg font-bold border-l-4 pl-2 border-blue-500">归档/反馈</h2>
            <div class="my-4 border-b border-gray-200"></div>
            <el-form label-position="top" label-width="100px">
              <div class="grid gap-4 sm:grid-cols-1 lg:grid-cols-3">
                <div class="sm:col-span-1 lg:col-span-3">
                  <el-form-item>
                    <template #label>
                      <div class="flex flex-col">
                        <div class="font-bold">建议</div>
                        <em>Proposal</em>
                      </div>
                    </template>
                    <el-input v-model="form.name" placeholder="请输入建议" type="textarea"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
        </div>
        <aside class="rounded-lg bg-white p-4 shadow-sm">
          <el-affix :offset="90">
            <!-- 锚点 -->
            <el-anchor type="underline" :offset="95" @change="handleAnchorChange">
              <el-anchor-link title="基本信息" href="#basic"></el-anchor-link>
              <el-anchor-link title="收集/整理/分类" href="#collect"></el-anchor-link>
              <el-anchor-link title="实施" href="#implement"></el-anchor-link>
              <el-anchor-link title="评审" href="#review"></el-anchor-link>
              <el-anchor-link title="归档/反馈" href="#feedback"></el-anchor-link>
            </el-anchor>
          </el-affix>
        </aside>
      </div>
    </article>
  `,
}
