import { post } from '../../config/axios/httpReuest.js'
import { currentDateKey, currentNodeKey, searchDateKey, searchFormKey } from '../../config/keys.js'
import ErrorComponent from '../../components/error.component.js'
import LoadingComponent from '../../components/loading.component.js'
import { useTotalView } from '../hooks/useTotalView.js'

const { inject, onMounted, ref, watch, defineAsyncComponent, toRefs, reactive } = Vue
export default {
  name: 'TotalViewComponent',
  components: {
    HtDrawer: defineAsyncComponent({
      loader: () => import('../../components/ht.drawer.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    TotalTableComponent: defineAsyncComponent({
      loader: () => import('./total.table.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HtDialog: defineAsyncComponent({
      loader: () => import('../../components/ht.dialog.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup(props, { emit }) {
    const currentNode = inject(currentNodeKey)

    const { currentDate, updateCurrentDate } = inject(currentDateKey)
    watch(currentDate, (newVal) => {
      getXAxisLabel(newVal)
      reRenderChart()
    })

    const { searchForm } = inject(searchFormKey, { searchForm: reactive({}) })
    watch(searchForm, async (newVal) => {
      await getTotalViewData(currentNode, searchForm)
      isShow.value = true
      getSeriesByCurrentNode(currentNode)
      getDataset()
      getXAxisLabel(currentDate.value)
      initChart()
    })

    const {
      getTotalViewData,
      getXAxisLabel,
      initChart,
      chartRef,
      getDataset,
      reRenderChart,
      drawerState,
      getSeriesByCurrentNode,
      dialogState,
    } = useTotalView(updateCurrentDate)

    const isShow = ref(false)
    onMounted(async () => {
      await getTotalViewData(currentNode, searchForm)
      isShow.value = true
      getSeriesByCurrentNode(currentNode)
      getDataset()
      getXAxisLabel(currentDate.value)
      initChart()
    })
    // save set pc
    const handleSaveSetPcClick = async () => {
      const params = {
        ac: 'pda_SetPdaCapacity',
        str_date: currentDate.value,
        str_type: currentNode,
        str_val: dialogState.inputValue,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        ElementPlus.ElMessage.success('SUCCESS')
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    return {
      chartRef,
      isShow,
      ...toRefs(drawerState),
      dialogState,
      currentDate,
      handleSaveSetPcClick,
    }
  },
  template: /*html*/ `
    <el-card :body-style="{ padding: '0px' }">
      <template #header>
        <span>Total View 总量视图</span>
      </template>
      <div ref="chartRef" class="h-[50vh] w-full"></div>
    </el-card>
    <!--    点击series的抽屉-->
    <HtDrawer v-model:visible="isShowDrawer" :title="title" :is-show-save="false">
      <TotalTableComponent :type="flag" :date="date"></TotalTableComponent>
    </HtDrawer>
    <!--    双击x轴的弹框-->
    <HtDialog
      v-model:visible="dialogState.isShowDialog"
      :title="currentDate + '设置排产量'"
      width="30%"
      @save="handleSaveSetPcClick"
    >
      <article class="py-5">
        <div class="flex items-center justify-center">
          <div class="w-[100px]">排产数量:</div>
          <div class="w-[200px">
            <el-input size="small" v-model="dialogState.inputValue"></el-input>
          </div>
        </div>
      </article>
    </HtDialog>
  `,
}
