/* 排班表样式 */
.shift-calendar-container {
  padding: 20px;
  background-color: #fff;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-text {
  margin-top: 10px;
  color: #409EFF;
}

.search-area {
  margin-bottom: 20px;
}

.week-header {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  text-align: center;
  height: 40px;
  line-height: 40px;
}

.date-header {
  height: 40px;
  line-height: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.holiday-text {
  font-size: 12px;
  margin-top: 2px;
}

.holiday-header {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.weekend-header {
  background-color: #fef0f0;
  color: #f56c6c;
}

.workday-header {
  background-color: #f4f4f5;
  color: #909399;
}

.table-header {
  background-color: #f5f7fa;
  color: #606266;
}

.bg-green-500 {
  background-color: #67c23a;
}

.bg-blue-500 {
  background-color: #409EFF;
}

.bg-red-500 {
  background-color: #F56C6C;
}

.bg-white {
  background-color: #ffffff;
}

.text-white {
  color: #ffffff;
}

.text-black {
  color: #000000;
}

.text-red-500 {
  color: #F56C6C;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.form-content {
  line-height: 32px;
  padding: 0 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 班次样式 */
.el-table .cell {
  padding: 0 !important;
}

.border {
  border-width: 1px;
  border-style: solid;
}

.border-gray-300 {
  border-color: #dcdfe6;
}

/* 表格样式 */
.el-table th.el-table__cell {
  background-color: #f5f7fa;
}

.el-table--border, .el-table--group {
  border: 1px solid #ebeef5;
}

.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border-bottom: 1px solid #ebeef5;
}

.el-table--border .el-table__cell {
  border-right: 1px solid #ebeef5;
}

/* 周末和节假日样式 */
.bg-gray-100 {
  background-color: #f9f9f9;
}

/* 图例样式 */
.gap-4 {
  gap: 1rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.w-full {
  width: 100%;
}

.h-12 {
  height: 3rem;
}

.text-lg {
  font-size: 1.125rem;
}

.font-bold {
  font-weight: 700;
}

.cursor-pointer {
  cursor: pointer;
}

.p-0\.5 {
  padding: 0.125rem;
}

/* 班次颜色 */
.shift-day {
  background-color: #67c23a;
  color: #ffffff;
}

.shift-night {
  background-color: #409EFF;
  color: #ffffff;
}

.shift-white {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid #dcdfe6;
}

.shift-rest {
  background-color: #F56C6C;
  color: #ffffff;
} 