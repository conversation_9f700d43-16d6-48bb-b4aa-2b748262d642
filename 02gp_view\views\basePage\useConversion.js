/**
 * @description: 字符串转换
 * @author: Rik_tang
 * @date: 2024-04-16
 */
export const useConversion = () => {
  /**
   * @description: 将form表单数据转换为接口需要的数据格式
   * @param {object} form - form表单数据
   */
  const formToApi = (form) => {
    debugger;
    return {
      id: form.id,
      id_engine_type: '',
      str_engine_type: form.model?.toUpperCase(),
      str_flow: form.flow,
      str_group: form.type,
      str_sm: form.smObject?.str_key,
      id_sm: form.smObject?.str_value,
      id_occupy_site: form.occupySite,
      int_tat: form.duration,
      id_task_ago: form.preTask,
      str_task_name: form.taskName,
      str_remark: form.remark,
      int_state: form.int_state,
    };
  };

  /**
   * @description: 将接口数据转换为form表单数据
   * @param {object} apiData
   */
  const apiToForm = (apiData) => {
    return {
      id: apiData.id,
      model: apiData.str_engine_type?.toUpperCase(),
      flow: apiData.str_flow,
      type: apiData.str_group,
      smObject: {
        str_key: apiData.str_sm,
        str_value: apiData.id_sm,
      },
      occupySite: apiData.id_occupy_site === '0' ? '' : apiData.id_occupy_site,
      duration: apiData.int_tat,
      preTask: apiData.id_task_ago,
      taskName: apiData.str_task_name,
      remark: apiData.str_remark,
      int_state: apiData.int_state,
    };
  };

  /**
   * @description: 将api数据转换为tableData数据
   * @param {array} apiData
   */
  const apiToTableData = (apiData) => {
    return apiData.map((item) => {
      return {
        id: item.id,
        model: item.str_engine_type?.toUpperCase(),
        flow: item.str_flow,
        type: item.str_group,
        sm: item.str_sm,
        duration: item.int_tat,
        taskName: item.str_task_name,
        createTime: item.dt_up,
        createBy: item.str_by,
        int_state: item.int_state,
        str_occupy_site: item.str_occupy_site,
      };
    });
  };

  /**
   * @description: 字段转换
   *
   */
  const fieldConversion = () => {
    return {
      id: 'id',
      model: 'str_engine_type',
      flow: 'str_flow',
      type: 'str_group',
      sm: 'str_sm',
      occupySite: 'id_occupy_site',
      duration: 'int_tat',
      taskName: 'str_task_name',
      createTime: 'dt_up',
      createBy: 'str_by',
    };
  };
  return {
    formToApi,
    apiToForm,
    apiToTableData,
    fieldConversion,
  };
};
