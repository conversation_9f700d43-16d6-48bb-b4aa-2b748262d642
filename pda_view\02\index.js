import ErrorComponent from '../../components/error.component.js'
import LoadingComponent from '../../components/loading.component.js'
import { currentDateKey, currentNodeKey, searchDateKey, searchForm<PERSON>ey } from '../../config/keys.js'
import { INCOMING_INSPECTION_NODE } from '../../config/nodeKey.js'
import { useCommApi } from '../hooks/useCommApi.js'

const { reactive, ref, defineAsyncComponent, provide, onMounted } = Vue
export default {
  name: 'IncomingInspectionComponent',
  components: {
    CardComponent: defineAsyncComponent({
      loader: () => import('../components/card.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    TotalViewComponent: defineAsyncComponent({
      loader: () => import('../components/total.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    PViewComponent: defineAsyncComponent({
      loader: () => import('../components/p.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HeaderSearchComponent: defineAsyncComponent({
      loader: () => import('../components/HeaderSearch/index.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup() {
    const { getUsers, burialPoint } = useCommApi()
    const userS = ref([])
    const userST = ref([])
    const startDate = ref(moment().format('YYYY-MM-DD'))
    const endDate = ref(moment().add(6, 'days').format('YYYY-MM-DD'))
    provide(searchDateKey, {
      startDate,
      endDate,
      updateSearchDate: (start, end) => {
        startDate.value = start
        endDate.value = end
      },
    })

    const currentDate = ref(moment().format('YYYY-MM-DD'))
    provide(currentDateKey, {
      currentDate,
      updateCurrentDate: date => {
        currentDate.value = date
      },
    })

    provide(currentNodeKey, INCOMING_INSPECTION_NODE)
    const searchForm = reactive({
      date: [moment().format('YYYY-MM-DD'), moment().add(6, 'days').format('YYYY-MM-DD')],
      //id_engine_type: 'cfm56',
    })
    const refreshKey = ref(0)
    const updateSearchForm = form => {
      Object.assign(searchForm, form)
      refreshKey.value += 1
    }
    provide(searchFormKey, {
      searchForm,
      updateSearchForm,
    })
    // 查询
    const handleSearchClick = form => {
      updateSearchForm(form)
    }
    const userFilter = val => {
      if (val) {
        userST.value = userS.value.filter(x => x.str_name?.indexOf(val) > -1 || x.str_code?.indexOf(val) > -1)
      } else {
        userST.value = userS.value
      }
    }
    onMounted(async () => {
      burialPoint(INCOMING_INSPECTION_NODE)
      userS.value = await getUsers('RULA')
      userST.value = await getUsers('RULA')
    })
    return {
      searchForm,
      handleSearchClick,
      refreshKey,
    }
  },
  template: /*html*/ `
    <!--    头部查询-->
    <div class="mx-4 mt-4">
      <HeaderSearchComponent @search="handleSearchClick">
        <vxe-form-item title="Owner:" folding>
          <template #default="{data}">
            <el-select
              v-model.trim="data.str_staff"
              clearable
              filterable
              style="width: 210px;"
              :filter-method="userFilter"
            >
              <el-option v-for="item in userST" :key="item.id" :label="item.str_name" :value="item.str_name"
                >{{ item.str_name }}-{{ item.str_code }}
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
      </HeaderSearchComponent>
    </div>

    <div class="mb-4 w-full border-b border-gray-200"></div>
    <!--    卡片-->
    <div class="mx-4">
      <CardComponent :key="refreshKey"></CardComponent>
    </div>
    <div class="my-4 w-full border-b border-gray-200"></div>
    <div class="mx-4">
      <TotalViewComponent :key="refreshKey"></TotalViewComponent>
    </div>

    <div class="my-4 w-full border-b border-gray-200"></div>
    <div class="mx-4">
      <PViewComponent :key="refreshKey"></PViewComponent>
    </div>
    <div class="mt-4"></div>
  `,
}
