const { ref, computed, defineComponent } = Vue
import { saveFeedBack } from '../../../api/teams/index.js'
import Handover from './HandoverAdd.js'
import TeamPlanSee from './team.plan.see.js'
import TeamPlanEdit from './team.plan.edit.js'
import TeamFeedback from './team.feedback.js'

// 任务项组件
export default defineComponent({
  name: 'TaskItem',
  components: {
    Handover,
    TeamPlanSee,
    TeamPlanEdit,
    TeamFeedback,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    deptId: String,
    teamId: String,
    flow: String,
    // 交接类型
    businessType: {
      type: Number,
      required: true,
    },
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const { ElMessage } = ElementPlus
    const planItem = computed(() => {
      const plans = props.row.plans || []
      return plans.find((item) => item.dt_shift === props.column.day) || { task: [] }
    })

    // 获取team staff 中的staff_name 并拼接成字符串
    const getTeamStaffName = (teamstaff) => {
      return teamstaff.map((item) => item.staff_name).join(',')
    }

    const handoverVisible = ref(false)

    // 打开交接弹窗
    const handleOpenHandover = (task) => {
      handoverVisible.value = true
    }

    const teamPlanSeeRef = ref(null)
    const teamPlanSeeVisible = ref(false)

    // 打开班组计划查看弹窗
    const handleOpenTeamPlanSee = (task) => {
      teamPlanSeeVisible.value = true
    }

    const teamPlanEditRef = ref(null)
    const teamPlanEditVisible = ref(false)

    // 打开班组计划编辑弹窗
    const handleOpenTeamPlanEdit = (task) => {
      teamPlanEditVisible.value = true
    }

    const deptId = props.deptId
    const teamId = props.teamId

    // 处理任务详情操作
    const handleOpenTaskDetail = (task) => {
      console.log('打开任务详情', task)
    }

    const teamFeedbackVisible = ref(false)
    const feedbackStatus = ref(null)
    // 处理颜色选择
    const handleColorCommand = (command) => {
      feedbackStatus.value = command
      if (command === '-2' || command === '-1') {
        teamFeedbackVisible.value = true
      }
      if (command === '1') {
        handleSaveFeedback()
      }
    }

    const statusClass = computed(() => {
      const statusMap = {
        1: 'bg-green-500 text-white',
        '-1': 'bg-yellow-500 text-white',
        '-2': 'bg-red-500 text-white',
      }
      return statusMap[planItem.value.fedbackstatus] || ''
    })

    const handleRefresh = () => {
      emit('refresh')
    }

    // 保存反馈
    const handleSaveFeedback = async (feedback = '') => {
      const feedbackList = []
      const feedbackdata = {
        id: '',
        dt_feed: props.column.day,
        id_shift: props.row.plans.find(item=> item.dt_shift === props.column.day).id_shift,
        str_shift: props.row.shift_name,
        str_feed_back_type: props.businessType,
        str_task_type: props.row.str_step,
        str_flow: props.flow,
        int_status: feedbackStatus.value,
        str_content: feedback,
      }
      feedbackList.push(feedbackdata)
      try {
        await saveFeedBack(feedbackList)
        ElMessage.success('反馈成功')
        teamFeedbackVisible.value = false
        handleRefresh()
      } catch (error) {
        ElMessage.error('反馈失败')
      }
    }

    return {
      planItem,
      getTeamStaffName,
      handleOpenHandover,
      handoverVisible,
      teamPlanSeeRef,
      teamPlanSeeVisible,
      handleOpenTeamPlanSee,
      teamPlanEditRef,
      teamPlanEditVisible,
      deptId,
      teamId,
      handleOpenTeamPlanEdit,
      handleOpenTaskDetail,
      handleColorCommand,
      handleRefresh,
      teamFeedbackVisible,
      statusClass,
      feedbackStatus,
      handleSaveFeedback,
    }
  },
  template: /*html*/ `
    <div v-if="planItem.task.length > 0" class="h-full min-h-[60px]" :class="statusClass">
      <!-- 任务内容区域 -->
      <div class="p-2 pb-10">
        <div class="my-2 flex flex-col rounded-md border border-gray-300" v-for="task in planItem.task" :key="task.id">
          <div class="gap-2">
            <el-icon color="red" :size="20" v-if="task.is_abnormal"><WarningFilled /></el-icon>
            <!-- 任务名称 太长 显示省略号 -->
            <div class="truncate pl-2 font-semibold" :title="task.str_task_new">{{ task.str_task_new }}</div>
            <!-- 任务人员 -->
            <div class="truncate pl-2" :title="task.str_esn+'/'+task.str_type">{{ task.str_esn }}/{{task.str_type}}</div>
          </div>

          <!-- 底部按钮 - 使用boxBottom类 -->
          <div class="boxBottom">
            <div class="span" @click="handleOpenTeamPlanSee(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <View />
              </el-icon>
            </div>
            <div class="span" @click="handleOpenTeamPlanEdit(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <Edit />
              </el-icon>
            </div>
            <div class="span" @click="handleOpenHandover(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <Folder />
              </el-icon>
            </div>

            <div class="span">
              <el-dropdown trigger="click" @command="(command) => handleColorCommand(command, task)">
                <el-icon class="cursor-pointer hover:text-blue-600" :size="18" color="#409EFF">
                  <More />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="-2" class="!text-red-500">红色</el-dropdown-item>
                    <el-dropdown-item command="-1" class="!text-yellow-500">黄色</el-dropdown-item>
                    <el-dropdown-item command="1" class="!text-green-500">绿色</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Handover
      v-if="handoverVisible"
      ref="handoverRef"
      v-model:visible="handoverVisible"
      :row="row"
      :column="column"
      :flow="flow"
      :businessType="businessType"
    />
    <TeamPlanSee
      v-if="teamPlanSeeVisible"
      ref="teamPlanSeeRef"
      v-model:visible="teamPlanSeeVisible"
      :planItem="planItem"
      :deptId="deptId"
      :teamId="teamId"
    />

    <TeamPlanEdit
      v-if="teamPlanEditVisible"
      ref="teamPlanEditRef"
      v-model:visible="teamPlanEditVisible"
      :planItem="planItem"
      :deptId="deptId"
      :teamId="teamId"
      @refresh="handleRefresh"
    />

    <TeamFeedback
      v-if="teamFeedbackVisible"
      v-model:visible="teamFeedbackVisible"
      :row="row"
      :column="column"
      :businessType="businessType"
      :flow="flow"
      :status="feedbackStatus"
      @refresh="handleRefresh"
      @saveFeedback="handleSaveFeedback"
    />
  `,
})
