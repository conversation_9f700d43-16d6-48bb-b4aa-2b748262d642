import { queryBaseTask } from '../../api/baseTask.js';

const { reactive, ref } = Vue;

export function useRelation() {
  const relationForm = reactive({
    list: [
      { task: '', relation: '' },
    ],
  });
  // 初始化relationForm
  const initRelationForm = () => {
    relationForm.list = [
      { task: '', relation: '' },
    ];
  };

  // 关联任务下拉列表
  const relationTaskList = ref([]);
  // 获取关联任务下拉列表
  const getRelationTaskList = async (flow, type,id) => {
    const queryList = [
      {
        str_key: 'str_engine_type',
        str_value: type,
      },
      {
        str_key: 'id',
        str_value: id,
      },{
        str_key: 'int_state',
        str_value: '1',
      }
    ];
    relationTaskList.value = await queryBaseTask(queryList);
  };

  return {
    relationForm,
    initRelationForm,
    relationTaskList,
    getRelationTaskList,
  };
}
