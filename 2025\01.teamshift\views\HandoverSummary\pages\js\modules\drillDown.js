/**
 * 钻取详情模块
 * 处理统计卡片点击后的详情展示
 */

const { defineAsyncComponent } = Vue

/**
 * 钻取详情组件
 */
export const DrillDownComponent = {
  name: 'DrillDownComponent',
  components: {
    HtVxeTable: defineAsyncComponent(() => import('../../../../../components/VxeTable/HtVxeTable.js')),
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    drillData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const loading = Vue.ref(false)
    const detailData = Vue.ref([])

    // 创建本地的visible状态，避免直接修改props
    const localVisible = Vue.computed({
      get: () => props.visible,
      set: (value) => {
        if (!value) {
          emit('close')
        }
      },
    })

    // 获取详情数据
    const fetchDetailData = async () => {
      if (!props.drillData.businessTypeCode || !props.drillData.type) return

      loading.value = true
      try {
        // 动态导入API
        const { getHandoverDetail } = await import('../../../api/index.js')

        const response = await getHandoverDetail({
          businessType: props.drillData.businessTypeCode,
          status: props.drillData.type,
        })

        detailData.value = response || []
      } catch (error) {
        console.error('获取详情数据失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 关闭弹窗
    const handleClose = () => {
      emit('close')
    }

    // 获取状态显示文本
    const getStatusText = (type) => {
      const statusMap = {
        pending: 'Pending',
        unsubmitted: '待提交',
        unreceived: '待接收',
      }
      return statusMap[type] || type
    }

    // 定义表格列配置
    const columns = [
      {
        field: 'str_wo',
        title: 'WO',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_esn',
        title: 'ESN',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_group',
        title: 'Type',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_sm',
        title: 'SM',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_task',
        title: '任务名称',
        minWidth: 150,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_shift',
        title: '班次',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_commit',
        title: '交班人',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_receive',
        title: '接班人',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'dt_commit',
        title: '交班时间',
        minWidth: 100,
        filterRender: { name: 'FilterCalendar' },
      },
      {
        field: 'dt_receive',
        title: '接班时间',
        minWidth: 100,
        filterRender: { name: 'FilterCalendar' },
      },
      {
        field: 'int_status',
        title: '是否交班',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        formatter: ({ row }) => {
          return row.int_status === -1 ? '未提交' : row.int_status === 0 ? '待接收' : '已接收'
        },
      },
   
    ]

    // 监听弹窗显示状态
    Vue.watch(
      () => props.visible,
      (newVal) => {
        if (newVal) {
          fetchDetailData()
        }
      },
    )

    return {
      loading,
      detailData,
      localVisible,
      handleClose,
      getStatusText,
      columns,
    }
  },
  template: `
    <el-drawer
      class="common-drawer"
      v-model="localVisible"
      :title="drillData.businessTypeName + ' - ' + getStatusText(drillData.type)"
      direction="rtl"
      size="80%"
      :destroy-on-close="false"
      :close-on-click-modal="false"
      :show-close="true"
      :with-header="true"
      @close="handleClose"
    >
      <div style="height: calc(100vh - 140px); display: flex; flex-direction: column;">
        <!-- 内容区域 - 设置固定高度 -->
        <div style="flex: 1; overflow: hidden; padding: 8px;">
          <!-- 加载状态 -->
          <div v-if="loading" class="flex justify-center items-center h-48">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mr-3"></div>
            <span class="text-gray-600">正在加载数据...</span>
          </div>

          <!-- 数据表格容器 - 设置固定高度 -->
          <div v-else style="height: 100%; overflow: hidden;">
            <HtVxeTable 
              :table-data="detailData" 
              :table-columns="columns"
              :show-overflow="true"
            />
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && detailData.length === 0" class="text-center py-12">
            <div class="text-gray-400 text-lg mb-2">📋</div>
            <p class="text-gray-500">暂无数据</p>
          </div>
        </div>

      </div>
        <template #footer>
          <el-button type="danger" @click="handleClose">关闭</el-button>
        </template>
    </el-drawer>
  `,
}
