import { post } from '../../utils/request.js'

/**
 * 获取资源日历数据
 * @param {Object} params - 请求参数
 * @param {string} params.dt_start - 开始日期
 * @param {string} params.dt_end - 结束日期
 * @param {string} params.str_sm - 单元体
 * @param {string} params.str_flow - Flow
 * @param {string} params.str_engine_type - 发动机类型
 */
export const getResourceCalendar = (params) => {
  const queryParams = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_skill_calendar',
    ...params,
  }
  return post('/api/Do/DoAPI', queryParams)
}
