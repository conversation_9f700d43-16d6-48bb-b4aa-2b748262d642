// ==================== MES申请系统配置文件 ====================
export const MES_CONFIG = {
  // 表单配置
  form: {
    rules: {
      person: [{ required: true, message: '请选择人员', trigger: 'change' }],
      startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
      endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
      reason: [{ required: true, message: '请填写申请事由', trigger: 'blur' }],
    },
    defaultWorkTime: {
      startTime: '09:00',
      endTime: '18:00',
      appliedHours: 8,
    },
  },

  // 状态配置
  status: {
    options: [
      { label: '全部', value: '' },
      { label: '草稿', value: 'Draft' },
      { label: '待审批', value: 'Pending' },
      { label: '已批准', value: 'Approved' },
      { label: '已驳回', value: 'Rejected' },
      { label: '已确认', value: 'Confirmed' },
    ],
    styles: {
      Draft: { type: 'info', text: '草稿' },
      Pending: { type: 'warning', text: '待审批' },
      Approved: { type: 'success', text: '已批准' },
      Rejected: { type: 'danger', text: '已驳回' },
      Confirmed: { type: 'primary', text: '已确认' },
    },
  },

  // 表格配置
  table: {
    columns: [
     {
        field: 'staff_name',
        title: 'MES人员',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'dt_up',
        title: '申请时间',
        minWidth: 140,
        filterRender: { name: 'FilterCalendar' },
        filters: [{ data: '' }],
      },
      {
        field: 'dt_mes_start',
        title: '开始日期',
        minWidth: 120,
        filterRender: { name: 'FilterCalendar' },
        filters: [{ data: '' }],
      },
      {
        field: 'dt_mes_end',
        title: '结束日期',
        minWidth: 120,
        filterRender: { name: 'FilterCalendar' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_apply_reason',
        title: '申请事由',
        minWidth: 180,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_remark',
        title: '备注',
        minWidth: 160,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        type: 'html',
        field: 'int_substatus',
        title: '审批状态',
        minWidth: 120,
        filters: [],
        filterMultiple: false,
        formatter: ({ cellValue }) => {
          const subStatusMap = {
            0: '<span class="text-green-500">草稿</span>',
            1: '<span class="text-blue-500">审批通过</span>',
            301: '<span class="text-blue-500">待审批</span>',
            '-1': '<span class="text-red-500">审批失败</span>',
            '-99': '<span class="text-red-500">撤回</span>',
          }
          return subStatusMap[cellValue] || '--'
        },
      },
      {
        field: 'str_audit_manage',
        title: '审批人',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_mes_manage',
        title: 'MES审批人',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      
       {
        field: 'apply_user',
        title: '申请人',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
    ],
    pagination: {
      pageSizes: [10, 20, 50, 100],
      layout: 'total, sizes, prev, pager, next, jumper',
    },
  },

  // 操作权限配置
  permissions: {
    canEdit: ['Draft', 'Rejected'],
    canSubmit: ['Draft'],
    canConfirm: ['Approved'],
  },
}
