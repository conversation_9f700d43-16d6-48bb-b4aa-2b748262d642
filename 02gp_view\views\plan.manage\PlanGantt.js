import { getDept, queryGanttById, saveProject } from '../../api/plan.manage.js'
import { useTransform } from './useTransform.js'
import { useMainUnitGantt } from './composables/useMainUnitGantt.js'
import DynamicColumnConfigurator from '../../components/DynamicColumnConfigurator.js'


const PlanGantt = {
  components: {
    DynamicColumnConfigurator,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  emits: ['hasSave'],
  setup(props, { emit }) {
    const { transformSubPlansToGanttData, transformGanttDataToSubPlans } = useTransform()
    /* ----------头部搜索---------- */
    const formSearch = Vue.reactive({
      str_node: '',
    })
    const searchNode = Vue.ref('')
    // 搜索
    const search = async () => {
      searchNode.value = formSearch.str_node
      if (isViewMainUnitGantt.value) {
        handleMainUnitGantt(formSearch.str_node)
      } else {
        gantt.clearAll()
        await getGanttData()
        gantt.parse(ganttData.value)
        expandGantt()
        gantt.render()
      }
    }
    const isCollapse = Vue.ref(true)
    // 折叠甘特图
    const collapseGantt = () => {
      isCollapse.value = !isCollapse.value
      // 使用批量操作优化性能
      gantt.batchUpdate(() => {
        // 只折叠第一层，保持数据结构
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.close(taskId)
          }
        })
      })
    }
    // 展开甘特图
    const expandGantt = () => {
      isCollapse.value = false
      // 使用批量操作来优化性能
      gantt.batchUpdate(() => {
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.open(taskId)
            const children = gantt.getChildren(taskId)
            children.forEach((child) => {
              gantt.open(child)
            })
          }
        })
      })
    }
    const isCollapseGrid = Vue.ref(false)
    // 收起grid
    const collapseGrid = () => {
      isCollapseGrid.value = !isCollapseGrid.value
      gantt.config.show_grid = false
      gantt.render()
    }
    // 展开grid
    const expandGrid = () => {
      isCollapseGrid.value = false
      gantt.config.show_grid = true
      gantt.render()
    }
    /* ----------头部搜索END---------- */
    const deptS = [] // 部门
    const ganttData = Vue.ref({})
    // 获取甘特图数据
    const getGanttData = async () => {
      const data = await queryGanttById(props.id, formSearch.str_node)
      if (data) {
        ganttData.value.data = _.map(data.subPlans, (task) => ({
          ...transformSubPlansToGanttData(task),
          $has_child: task.children?.length > 0,
          $level: task.level || 1,
        }))
        ganttData.value.links = data.links
      }
    }
    /**其他服务接口数据*/
    const getSeverList = async () => {
      const deptsReq = await getDept()
      if (deptsReq) {
        deptS.value = deptsReq.data.map((x) => {
          return {
            key: x.id,
            label: x.str_name,
          }
        })
        gantt.serverList('user', deptS.value)
      }
    }
    // 进度template
    const progressTemplate = (task) => {
      if (task.id_task === '0') {
        return ''
      }
      return Math.round(task.progress * 100) + '%'
    }
    // 状态template
    const statusTemplate = (task) => {
      if (task.id_task === '0') {
        return ''
      }
      if (task.status == 1) return 'Open'
      if (task.status == 2) return 'Pause'
      if (task.status == 3) return 'Complete'
      return ''
    }
    // owner template
    const ownerTemplate = (task) => {
      return byId(gantt.serverList('user'), task.owner)
    }
    // 默认的gantt列
    const defaultColumns = [
      { name: 'text', label: 'Task name', tree: true, width: '*' },
      { name: 'start_date', label: 'Start time', width: 100 },
      { name: 'duration', label: 'Duration', width: 60 },
      { name: 'progress', label: 'Progress', width: 60, template: progressTemplate },
      { name: 'status', label: 'Status', width: 80, align: 'center', template: statusTemplate },
      { name: 'owner', label: 'owner', width: 80, align: 'center', template: ownerTemplate },
      { name: 'dt_mp', label: 'MP', width: 80, align: 'center' },
      { name: 'dt_ekd', label: 'EKD', width: 80, align: 'center' },
    ]
    /**
     * 改变列
     * @param {Array} column
     */
    const changeColumn = (column) => {
      // 从默认列中找到对应的列
      const list = []
      column.forEach((item) => {
        const find = defaultColumns.find((column) => column.name === item.name)
        if (find) {
          list.push(find)
        }
      })
      // 设置列
      gantt.config.columns = list
      gantt.config.grid_width = list.length * 100 + 100
      // 重新渲染
      gantt.render()
    }
    // 获取甘特图数据中最小的开始时间和最大的结束时间
    const getGanttDataMinMaxTime = () => {
      const minTime = _.minBy(ganttData.value.data, 'start_date').start_date
      // 获取当前月份的前一天
      const preDayOfMonth = moment(minTime).subtract(1, 'days').format('YYYY-MM-DD')
      const maxTime = _.maxBy(ganttData.value.data, 'dt_end').dt_end
      // 获取最大日期的年
      const maxYear = moment(maxTime).year()
      // 获取当前年的下一年的第一天
      const lastDayOfYear = moment(`${maxYear + 1}-01-01`).format('YYYY-MM-DD')
      return { preDayOfMonth, lastDayOfYear }
    }
    // 初始化gantt
    const ganttInit = () => {
      // gantt.config.autofit = true
      // 动态调整甘特图的宽度
      gantt.config.grid_width = defaultColumns.length * 100

      // 设置起始时间
      gantt.config.start_date = getGanttDataMinMaxTime().preDayOfMonth
      // 设置结束时间
      gantt.config.end_date = getGanttDataMinMaxTime().lastDayOfYear
      // 设置不可拖动
      gantt.config.drag_links = false
      gantt.config.drag_lightbox = false
      gantt.config.drag_move = false
      gantt.config.drag_resize = false
      // 设置时间格式
      gantt.config.date_format = '%Y-%m-%d'
      // 设置gantt列
      gantt.config.columns = defaultColumns
      gantt.locale.labels.section_status = 'Status'
      gantt.locale.labels['section_progress'] = 'Progress'
      gantt.locale.labels['section_owner'] = 'Owner' // 弹框字段
      gantt.config.lightbox.sections = [
        {
          name: 'description',
          height: 38,
          map_to: 'text',
          type: 'textarea',
          focus: true,
        },
        {
          name: 'status',
          height: 30,
          map_to: 'status',
          type: 'select',
          options: [
            { key: 1, label: 'Open' },
            { key: 2, label: 'Pause' },
            { key: 3, label: 'Complete' },
          ],
        },
        {
          name: 'progress',
          height: 30,
          map_to: 'progress',
          type: 'select',
          options: [
            { key: 0.0, label: 'Not started' },
            { key: 0.1, label: '10%' },
            { key: 0.2, label: '20%' },
            { key: 0.3, label: '30%' },
            { key: 0.4, label: '40%' },
            { key: 0.5, label: '50%' },
            { key: 0.6, label: '60%' },
            { key: 0.7, label: '70%' },
            { key: 0.8, label: '80%' },
            { key: 0.9, label: '90%' },
            { key: 1.0, label: 'Complete' },
          ],
        },
        {
          name: 'owner',
          height: 30,
          map_to: 'owner',
          type: 'select',
          options: gantt.serverList('user'),
        },
        // {name: "time", type: "duration", map_to: "auto"}
      ]
      // 设置弹出框的删除按钮不显示
      gantt.config.buttons_left = ['gantt_save_btn']
      gantt.config.buttons_right = ['gantt_cancel_btn']

      gantt.plugins({
        auto_scheduling: true, // 自动调度
        undo: true,
        tooltip: true,
      })

      // 配置动态加载
      gantt.config.branch_loading = true
      gantt.config.show_loading = true
      gantt.config.load_level = 2 // 初始只加载2层

      // 配置延迟渲染
      gantt.config.smart_rendering = true
      gantt.config.smart_scales = true
      // 设置任务的样式
      gantt.templates.task_class = function (start, end, task) {
        // 如果任务id不为0，则不显示可拖动的进度条
        if (task.id_task === '0') {
          return `hide-progress-drag sub-level-${task.level}`
        }
        return `sub-level-${task.level}`
      }
      gantt.templates.scale_cell_class = function (date) {
        if (!gantt.isWorkTime(date)) return 'weekend'
      }

      // 设置tooltip文本
      gantt.templates.tooltip_text = function (start, end, task) {
        return '<b>Task:</b> ' + task.text + '<br><b>Start:</b> ' + task.dt_start + '<br><b>End:</b> ' + task.dt_end
      }

      // 多选
      gantt.form_blocks['multiselect'] = {
        render: function (sns) {
          var height = (sns.height || '23') + 'px'
          var html =
            "<div class='gantt_cal_ltext gantt_cal_chosen gantt_cal_multiselect' style='height:" +
            height +
            ";'><select data-placeholder='...' class='chosen-select' multiple>"
          if (sns.options) {
            for (var i = 0; i < sns.options.length; i++) {
              if (sns.unassigned_value !== undefined && sns.options[i].key == sns.unassigned_value) {
                continue
              }
              html += "<option value='" + sns.options[i].key + "'>" + sns.options[i].label + '</option>'
            }
          }
          html += '</select></div>'
          return html
        },

        set_value: function (node, value, ev, sns) {
          node.style.overflow = 'visible'
          node.parentNode.style.overflow = 'visible'
          node.style.display = 'inline-block'
          var select = $(node.firstChild)

          if (value) {
            value = (value + '').split(',')
            select.val(value)
          } else {
            select.val([])
          }

          select.chosen()
          if (sns.onchange) {
            select.change(function () {
              sns.onchange.call(this)
            })
          }
          select.trigger('chosen:updated')
          select.trigger('change')
        },

        get_value: function (node, ev) {
          var value = $(node.firstChild).val()
          return value
        },

        focus: function (node) {
          $(node.firstChild).focus()
        },
      }
    }
    // 清除gantt图toolTip
    const clearGanttTooltip = () => {
      gantt.ext.tooltips.tooltip.hide()
    }
    // 拖动进度条
    gantt.attachEvent('onTaskDrag', function (id, mode, task, original) {
      if (mode == gantt.config.drag_mode.progress) {
        task.progress = (Math.round(task.progress * 10) / 10).toFixed(1) // 将进度四舍五入到最近的十分之一
        if (task.progress < 0) task.progress = 0.0 // 进度不能小于0
        if (task.progress > 1) task.progress = 1.0 // 进度不能大于1
      }
    })

    // 打开甘特图弹框之前
    gantt.attachEvent('onBeforeLightbox', function (id) {
      const task = gantt.getTask(id)
      // 进度值保留一位小数
      task.progress = Number(task.progress).toFixed(1)

      return true
    })

    // 保存
    const saveGantt = async () => {
      // 获取甘特图数据
      const data = gantt.serialize()?.data
      const req = await saveProject(_.map(data, transformGanttDataToSubPlans))
      // if (req) {
      //   //ganttData.value.data = _.map(data.subPlans, transformSubPlansToGanttData);
      //   // ganttData.value.links = data.links;
      // }
      // saveProject()
    }
    const byId = (list, id) => {
      return (list.find((x) => x.key == id) && list.find((x) => x.key == id)?.label) || ''
    }
    // 设置弹框
    const settingDialog = Vue.reactive({
      visible: false,
      tableData: [],
    })
    const settingTableRef = Vue.ref(null)
    // 打开设置弹框
    const openSettingDialog = () => {
      settingDialog.visible = true
      settingDialog.tableData = getSettingTableData()
      Vue.nextTick(() => {
        // 根据当前列设置选中
        const columns = gantt.config.columns
        columns.forEach((column) => {
          const find = settingDialog.tableData.find((item) => item.name === column.name)
          if (find) {
            settingTableRef.value.toggleRowSelection(find, true)
          }
        })
      })
    }
    // 获取设置表格数据
    const getSettingTableData = () => {
      const columns = {
        text: 'Task name',
        start_date: 'Start time',
        duration: 'Duration',
        progress: 'Progress',
        status: 'Status',
        owner: 'owner',
        dt_mp: 'MP',
        dt_ekd: 'EKD',
      }
      return Object.keys(columns).map((key, index) => {
        return {
          name: key,
          label: columns[key],
          sort: index,
        }
      })
    }
    // 确认设置弹框
    const confirmSettingDialog = () => {
      const selection = settingTableRef.value.getSelectionRows()
      const column = selection.map((item) => {
        return {
          name: item.name,
          sort: item.sort,
        }
      })
      changeColumn(column)
      settingDialog.visible = false
    }

    const {
      handleMainUnitGantt,
      isViewMainUnitGantt,
      isMainUnitCollapse,
      isMainUnitCollapseGrid,
      handleMainUnitCollapse,
      handleMainUnitExpand,
      handleMainUnitExpandGrid,
      handleMainUnitCollapseGrid,
      dynamicColumn,
    } = useMainUnitGantt(props.id, emit)

    // 返回
    const handleBack = async () => {
      isViewMainUnitGantt.value = false
      isCollapse.value = true
      gantt.clearAll()
      await getGanttData()
      await getSeverList()
      ganttInit()
      gantt.init('gantt')
      gantt.parse(ganttData.value)
      emit('hasSave', true)
    }

    Vue.onMounted(async () => {
      handleMainUnitGantt()
    })
    return {
      ganttData,
      saveGantt,
      byId,
      getSeverList,
      settingDialog,
      settingTableRef,
      openSettingDialog,
      confirmSettingDialog,
      clearGanttTooltip,
      search,
      formSearch,
      isCollapse,
      collapseGantt,
      expandGantt,
      isCollapseGrid,
      collapseGrid,
      expandGrid,
      handleMainUnitGantt,
      isViewMainUnitGantt,
      isMainUnitCollapse,
      isMainUnitCollapseGrid,
      handleMainUnitCollapse,
      handleMainUnitExpand,
      handleMainUnitExpandGrid,
      handleMainUnitCollapseGrid,
      handleBack,
      ...dynamicColumn,
    }
  },
  template: /*html*/ `
    <div class="flex h-full w-full flex-col pr-2">
      <div class="mb-2 flex justify-between">
        <el-form :inline="true" :model="formSearch">
          <el-form-item label="">
            <el-input
              v-model="formSearch.str_node"
              clearable
              placeholder="请输入任务名称"
              style="width:220px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search">搜索</el-button>
          </el-form-item>
          <el-form-item>
            <div v-if="!isViewMainUnitGantt" class="flex items-center gap-2">
              <el-button v-if="!isCollapse" type="primary" @click="collapseGantt">折叠甘特图</el-button>
              <el-button v-else type="primary" @click="expandGantt">展开甘特图</el-button>
            </div>
            <div v-else class="flex items-center gap-2">
              <el-button v-if="!isMainUnitCollapse" type="primary" @click="handleMainUnitCollapse">折叠甘特图</el-button>
              <el-button v-else type="primary" @click="handleMainUnitExpand">展开甘特图</el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <div v-if="!isViewMainUnitGantt" class="flex items-center gap-2">
              <el-button v-if="isCollapseGrid" type="primary" @click="expandGrid">展开grid</el-button>
              <el-button v-else type="primary" @click="collapseGrid">收起grid</el-button>
            </div>
            <div v-else class="flex items-center gap-2">
              <el-button v-if="isMainUnitCollapseGrid" type="primary" @click="handleMainUnitExpandGrid">展开grid</el-button>
              <el-button v-else type="primary" @click="handleMainUnitCollapseGrid">收起grid</el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button v-if="!isViewMainUnitGantt" type="primary" @click="handleMainUnitGantt(formSearch.str_node)">主单元体甘特图</el-button>
            <el-button v-else type="primary" @click="handleBack">子单元体甘特图</el-button>
          </el-form-item>
        </el-form>
        <el-button v-if="!isViewMainUnitGantt" type="primary" @click="openSettingDialog">设置</el-button>
      </div>
      <div v-if="isViewMainUnitGantt" class="flex justify-end gap-2 mb-2">
        <DynamicColumnConfigurator
          v-model="visibleColumns"
          :all-columns="columnConfig"
          button-text="列配置"
          button-icon="Grid"
        />
      </div>
      
      <div v-if="isViewMainUnitGantt" id="mainUnitGantt" class="h-full w-full"></div>
      <div v-else id="gantt" class="h-full w-full"></div>
    </div>
    <!--    修改列表弹框-->
    <el-dialog class="my-dialog" title="设置" v-model="settingDialog.visible" width="50%" :show-close="false">
      <el-table ref="settingTableRef" :data="settingDialog.tableData" stripe border>
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column prop="label" label="列名"></el-table-column>
        <el-table-column prop="sort" label="排序"></el-table-column>
      </el-table>
      <template #footer>
        <el-button type="primary" @click="confirmSettingDialog">确定</el-button>
        <el-button @click="settingDialog.visible = false">取消</el-button>
      </template>
    </el-dialog>
  `,
}
export default PlanGantt
