import { post } from '../../../config/axios/httpReuest.js';
export function useCipcApplyApi() {

  // 获取模板管理表格列
  const getColumns = () => {
    return [
      {
        field: 'str_engine_type',
        title: '建议描述',
        minWidth: 100,
        filters: [{ label: 'CFM56', value: 'CFM56' }, { label: 'LEAP', value: 'LEAP' }],
        filterMultiple: false,
        // filterRender: { name: 'FilterInput' },
        formatter: ({ cellValue }) => {
          return cellValue.toUpperCase();
        },
      },
      {
        title: '提出人',
        field: 'str_template_name',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '部门',
        field: 'str_type',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '工号',
        field: 'str_repair_type',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        formatter: ({ cellValue }) => {
          return cellValue.toUpperCase();
        },
      },
      {
        title: '操作时间',
        field: 'dt_up',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '操作人',
        field: 'str_by',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      // {
      //   type: 'html',
      //   title: '状态',
      //   field: 'is_state',
      //   minWidth: 100,
      //   filters: [{ label: '启用', value: 1 }, { label: '停用', value: 0 }],
      //   filterMultiple: false,
      //   // filterRender: { name: 'FilterInput' },
      //   formatter: ({ cellValue }) => {
      //     return `<span style="color: ${cellValue === 1 ? 'green' : 'red'}">${cellValue === 1 ? '启用' : '停用'}</span>`;
      //   },
      // },
    ];
  };

  const getTablePage =async () => {
    let res = null;
    const params = {
      ac: 'gp_workclothes_inuse',
      id,
    };
    const { data } = await post(params);
    if (data.code === 'error') {
      ElementPlus.ElMessage.error(data.text);
      return res;
    }
    res = data.data;
    return res;
  }
  return {
    getColumns,
    getTablePage
  };
}