/**
 * @description 已串件清单
 * <AUTHOR>
 */
import { post } from '../../../../config/axios/httpReuest.js'
// 引入公共表格组件
import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
const { reactive, toRefs, ref, onMounted } = Vue
const { useVModel } = VueUse
const ChangeListDrawer = {
  components: {
    HtVxeTable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    idWo: {
      type: String,
      required: true,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const tableRef = ref(null)
    const visible = useVModel(props, 'visible', emit)
    // 表格数据
    const tableState = reactive({
      data: null,
      columns: [
        {
          title: '目标WO',
          field: 'str_code',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '目标ESN',
          field: 'str_esn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原ESN',
          field: 'str_ori_esn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PN',
          field: 'str_pn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'SM',
          field: 'str_sm',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件标签',
          field: 'str_label',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件名称',
          field: 'str_part_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '数量',
          field: 'int_qty',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
      ],
      total: 0,
    })
    // * 获取表格数据
    const getTableData = async () => {
      const { idWo } = props
      const params = {
        ac: 'de_get_change_list',
        id_wo: idWo,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        tableState.data = data.data
        tableState.total = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    // * 表格筛选
    const handleFilterChange = (data) => {
      tableState.total = tableRef.value.getCurrentLength()
    }
    // * 导出表格数据
    const exportTableData = () => {
      tableRef.value.exportData()
    }

    onMounted(() => {
      getTableData()
    })
    return {
      visible,
      tableRef,
      ...toRefs(tableState),
      handleFilterChange,
      exportTableData,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="80%" :show-close="false" destroy-on-close :modal="false" :z-index="1">
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-white">已串件清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
          
            <el-button type="primary" @click="exportTableData">导出</el-button>
          </div>
          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable
          ref="tableRef"
          :tableData="data"
          :tableColumns="columns"
          :isShowHeaderCheckbox="true"
          @filterChange="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
  `,
}

export default ChangeListDrawer
