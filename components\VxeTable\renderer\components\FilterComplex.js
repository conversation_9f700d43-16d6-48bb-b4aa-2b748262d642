const FilterComplex = {
  name: 'FilterComplex',
  props: {
    params: Object,
  },
  setup(props) {
    const selectState = Vue.reactive({
      option: null
    });

    const load = () => {
      const { params } = props;
      if (params) {
        const { column } = params;
        selectState.option = column.filters[0];
      }
    };

    const changeOptionEvent = () => {
      const { params } = props;
      const { option } = selectState;
      if (params && option) {
        const { $panel } = params;
        const checked = !!option.data.name;
        $panel.changeOption(null, checked, option);
      }
    };

    const confirmEvent = () => {
      const { params } = props;
      if (params) {
        const { $panel } = params;
        $panel.confirmFilter();
      }
    };

    const restEvent = () => {
      const { params } = props;
      if (params) {
        const { $panel } = params;
        $panel.resetFilter();
      }
    };

    return {
      selectState,
      load,
      changeOptionEvent,
      confirmEvent,
      restEvent
    };
  },
  template: `
    <div class="">
      <vxe-radio v-model="selectState.option.data.type" name="fType" label="less">小于</vxe-radio>
      <vxe-radio v-model="selectState.option.data.type" name="fType" label="eq">等于</vxe-radio>
      <vxe-radio v-model="selectState.option.data.type" name="fType" label="greater">大于</vxe-radio>
    </div>
    <div class="">
      <vxe-input v-model="selectState.option.data.name" type="text" placeholder="请输入内容"
                 @input="changeOptionEvent"></vxe-input>
    </div>
    <div>
      <vxe-button status="primary" @click="confirmEvent">确定</vxe-button>
      <vxe-button @click="restEvent">重置</vxe-button>
    </div>
  `
};
export default FilterComplex;
