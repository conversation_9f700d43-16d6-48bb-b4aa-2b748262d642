import { queryBaseTask } from '../../api/baseTask.js'
import { getRepairType, saveTemplate } from '../../api/index.js'
import { convertData } from './conversionData.js'
import { useFlow } from './useFlow.js'
import { useSm } from './useSm.js'
import { useTask } from './useTask.js'
import { useTemplateForm } from './useTemplateForm.js'
import { useType } from './useType.js'

const { onMounted, defineAsyncComponent, ref, reactive } = Vue
const TemplateAdd = {
  components: {
    HtDialog: defineAsyncComponent(() => import('../../../components/ht.dialog.js')),
  },
  props: {
    blAddFlow: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const { formRules, requestFormDataById } = useTemplateForm()
    const formStateRef = ref(null)
    // define form state
    const formState = ref({
      is_state: 1,
      nodes: [],
    })
    const repairTypeOption = ref([])
    // 获取维修类型option
    const getRepairTypeOption = async () => {
      repairTypeOption.value = await getRepairType()
    }
    /**
     *  @description 根据ID获取form数据
     *  @param {string} id - ID
     */
    const getFormDataById = async (id) => {
      formState.value = await requestFormDataById(id)
      tableData.value = formState.value.nodes
      const queryLists = [
        {
          str_key: 'str_engine_type',
          str_value: formState.value.str_engine_type,
        },
        {
          str_key: 'int_state',
          str_value: '1',
        },
      ]
      taskState.option = await queryBaseTask(queryLists)
      const $table = tableRef.value
      $table.setAllTreeExpand(true) // 展开所有
    }

    const tableData = Vue.ref([])
    const tableRef = ref(null)
    /**
     * @description 插入子节点 、 删除节点
     * @param {object} row - 行数据
     * @param {string} buttonName - 按钮名称
     */
    const insertChildren = (row, buttonName) => {
      const nameMap = {
        'Add Flow': () => {
          flowState.visible = true
        },
        'Add Type': () => {
          typeState.visible = true
          typeState.currentRow = row
        },
        'Add SM': () => {
          smState.visible = true
          smState.currentRow = row
        },
        'Add Task': () => {
          taskState.visible = true
          taskState.currentRow = row
        },
        Remove: async () => {
          await removeRow(row)
        },
      }
      nameMap[buttonName]()
    }
    // get button name
    const getButtonName = (row) => {
      const buttonMap = {
        1: ['Add Type', 'Add SM', 'Add Task', 'Remove'],
        2: ['Add SM', 'Add Task', 'Remove'],
        3: ['Add SM', 'Add Task', 'Remove'],
        4: ['Remove'],
      }
      return buttonMap[row.int_level]
    }
    /**
     * @description 获取label名称
     * @param {object} row
     * @return {*}
     */
    const getLabelName = (row) => {
      const labelMap = {
        1: 'Flow',
        2: 'Type',
        3: 'SM',
        4: 'Task',
      }
      return labelMap[row.int_level]
    }

    // use flow
    const { flowState, getFlowOption, handleAddFlow, handleSaveFlow, handleClearFlow } = useFlow(tableData, tableRef)
    // 改变机型, 获取任务列表
    const changeEngineType = async (value) => {
      const queryList = [
        {
          str_key: 'str_engine_type',
          str_value: value,
        },
        {
          str_key: 'int_state',
          str_value: '1',
        },
      ]
      taskState.option = await queryBaseTask(queryList)
    }

    // use type
    const { typeState, handleClearType, handleSaveType } = useType(tableData, tableRef)

    // use sm
    const { smState, getSmOption, handleSaveSm, handleClearSm } = useSm(tableData, tableRef)

    // use task
    const { taskState, handleSaveTask, handleClearTask } = useTask(tableData, tableRef)

    // remove row
    const removeRow = async (row) => {
      const $table = tableRef.value
      $table.remove({ row })
      // 将数据扁平化
      const data  = convertData($table.getTableData().tableData)
      tableData.value = data.filter((item) => item.id !== row.id)
      
    }

    /**
     * @description 保存或编辑模板
     * @param {string} type - 类型 add or edit
     * @return {Promise<boolean>}
     */
    const addAndEditTemplate = async (type) => {
      const $table = tableRef.value
      if ($table) {
        $table.setAllTreeExpand(true)
      }

      let saveD = null
      // let nodes = []
      // 校验表单
      const valid = await formStateRef.value.validate()
      if (!valid) {
        return false
      }
      let dataT = $table.getTableData().tableData
      formState.value.nodes = convertData(dataT)
      return await saveTemplate(formState.value)
    }

    onMounted(async () => {
      await getFlowOption()
      await getSmOption()
      await getRepairTypeOption()
    })

    return {
      formStateRef,
      formState,
      formRules,
      changeEngineType,
      getFormDataById,
      tableRef,
      tableData,
      insertChildren,
      getButtonName,
      handleAddFlow,
      flowState,
      handleSaveFlow,
      handleClearFlow,
      typeState,
      smState,
      taskState,
      handleSaveType,
      handleClearType,
      handleSaveSm,
      handleClearSm,
      handleSaveTask,
      handleClearTask,
      removeRow,
      getLabelName,
      addAndEditTemplate,
      repairTypeOption,
    }
  },
  template: /*html*/ `
    <div class="border-l-4 border-l-emerald-700 text-emerald-700">
      <span class="pl-4 text-xl">基础信息</span>
    </div>
    <div class="my-2 border-b-2"></div>
    <el-form ref="formStateRef" :model="formState" :rules="formRules" label-width="120px">
      <div class="grid grid-cols-5 grid-rows-2">
        <el-form-item label="模板名称" prop="str_template_name" class="items-center">
          <el-input v-model="formState.str_template_name"></el-input>
        </el-form-item>
        <el-form-item label="机型" prop="str_engine_type" class="items-center">
          <el-select v-model="formState.str_engine_type" placeholder="请选择" @change="changeEngineType">
            <el-option label="CFM56" value="CFM56"></el-option>
            <el-option label="LEAP" value="LEAP"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="is_state" class="items-center">
          <el-switch
            v-model="formState.is_state"
            inline-prompt
            :active-value="1"
            active-text="启用"
            inactive-text="禁用"
            :inactive-value="0"
            inactive-color="red"
            active-color="green"
          ></el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="str_remark" class="col-span-5 items-center">
          <el-input v-model="formState.str_remark" type="textarea"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <div class="flex items-center justify-between border-l-4 border-l-emerald-700 text-emerald-700">
      <span class="pl-4 text-xl">模板数据</span>
      <vxe-button type="text" status="primary" @click="handleAddFlow" v-if="blAddFlow">Add Flow</vxe-button>
    </div>
    <div class="my-2 border-b-2"></div>
    <vxe-table
      ref="tableRef"
      :data="tableData"
      :tree-config="{ transform: true, parentField: 'id_root', accordion: false, reserve: true }"
      :row-config="{ useKey: true }"
      show-overflow="title"
      :height="500"
      border="inner"
    >
      <vxe-column type="seq" width="80"></vxe-column>
      <vxe-column title="Label" tree-node>
        <template #default="{ row }">
          <span>{{ getLabelName(row) }}</span>
        </template>
      </vxe-column>
      <vxe-column field="str_node" title="Name"></vxe-column>
      <vxe-column title="工期" field="int_tat"></vxe-column>
      <vxe-column title="关联任务" field="str_task_ago"></vxe-column>
      <vxe-column title="操作" min-width="150" v-if="blAddFlow">
        <template #default="{ row }">
          <el-button-group>
            <el-button
              text
              type="primary"
              v-for="item in getButtonName(row)"
              :key="item"
              @click="insertChildren(row, item)"
            >
              {{ item }}
            </el-button>
          </el-button-group>
        </template>
      </vxe-column>
    </vxe-table>

    <!--    添加Flow-->
    <ht-dialog v-model:visible="flowState.visible" title="添加Flow" @save="handleSaveFlow" @clear="handleClearFlow">
      <div class="m-4">
        <el-form>
          <el-form-item label="Flow:">
            <el-select v-model="flowState.flow" filterable placeholder="请选择">
              <el-option
                v-for="item in flowState.option"
                :key="item.str_value"
                :label="item.str_key"
                :value="item.str_key"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </ht-dialog>

    <!--    添加类型-->
    <ht-dialog v-model:visible="typeState.visible" title="添加类型" @save="handleSaveType" @clear="handleClearType">
      <div class="m-4">
        <el-form>
          <el-form-item label="Type:">
            <el-select v-model="typeState.type" placeholder="请选择">
              <el-option label="Core" value="Core"></el-option>
              <el-option label="Fan" value="Fan"></el-option>
              <el-option label="Lpt" value="Lpt"></el-option>
              <el-option label="B1" value="B1"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </ht-dialog>

    <!--添加单元体-->
    <ht-dialog v-model:visible="smState.visible" title="添加单元体" @save="handleSaveSm" @clear="handleClearSm">
      <div class="m-4">
        <el-form>
          <el-form-item label="SM:">
            <el-select v-model="smState.sm" filterable placeholder="请选择">
              <el-option
                v-for="item in smState.option"
                :key="item.str_value"
                :label="item.str_key"
                :value="item.str_key"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </ht-dialog>

    <!--添加子任务-->
    <ht-dialog v-model:visible="taskState.visible" title="添加子任务" @save="handleSaveTask" @clear="handleClearTask">
      <div class="m-4">
        <el-form :model="taskState" label-width="120px">
          <el-form-item label="任务名称:">
            <el-select v-model="taskState.taskObj" filterable placeholder="请选择" value-key="id">
              <el-option
                v-for="item in taskState.option"
                :key="item.id"
                :label="item.str_task_name"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </ht-dialog>
  `,
}

export default TemplateAdd
