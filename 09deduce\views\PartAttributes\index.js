import { useDownloadAndUpload } from '../../hooks/useDownloadAndUpload.js'

/**
 * @description 绩效导入
 * <AUTHOR>
 */
const { ref } = Vue
const { genFileId } = ElementPlus
const PerformanceView = {
  setup() {
    const upload = ref(null)
    const fileList = ref([])
    const handleExceed = (files) => {
      upload.value?.clearFiles()
      const file = files[0]
      file.uid = genFileId()
      upload.value?.handleStart(file)
    }
    const { downloadTemplate, uploadXlsx } = useDownloadAndUpload()
    const map = {
      机型编码: 'id_engine_type',
      物料编码: 'str_pn',
      供应商件号: 'str_pn_vendor',
      '位置编号/SIN': 'str_location',
      手册单元体:'str_sm',
      物料名称:'str_part_name',
      串件可行性: 'str_point_type',
    }
    const submitUpload = () => {
      uploadXlsx(upload, fileList, map, 'de_importpartconfig')
    }

    // * 下载模板
    const handleDownloadTemplate = () => {
      // 使用XLXS.js生成模板
      const data = [
        ['机型编码', '物料编码', '供应商件号', '位置编号/SIN','手册单元体','物料名称', '串行可行性']
        
      ]
      downloadTemplate(data, '零件属性导入模板.xlsx')
    }

    return {
      upload,
      fileList,
      handleExceed,
      submitUpload,
      handleDownloadTemplate,
    }
  },
  template: /*html*/ `
    <el-upload
      ref="upload"
      class="m-3"
      v-model:file-list="fileList"
      :limit="1"
      :on-exceed="handleExceed"
      :auto-upload="false"
      accpet=".xls,.xlsx"
    >
      <template #trigger>
        <el-button type="primary">select file</el-button>
      </template>  
      <el-button class="ml-3" type="success" @click="submitUpload">upload to server</el-button>
      <el-button class="ml-3" type="warning" @click="handleDownloadTemplate">download template</el-button>
      <template #tip>
        <div class="text-sm italic">only support xls/xlsx file</div>
      </template>
    </el-upload>
  `,
}

export default PerformanceView
