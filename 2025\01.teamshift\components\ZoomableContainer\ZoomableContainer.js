const { ref, reactive, onMounted, onUnmounted, nextTick } = Vue

export default {
  name: 'ZoomableContainer',
  props: {
    // 最小缩放比例
    minScale: {
      type: Number,
      default: 0.1
    },
    // 最大缩放比例
    maxScale: {
      type: Number,
      default: 5
    },
    // 缩放步长
    scaleStep: {
      type: Number,
      default: 0.1
    },
    // 是否启用缩放
    enableZoom: {
      type: Boolean,
      default: true
    },
    // 是否启用平移
    enablePan: {
      type: Boolean,
      default: true
    }
  },
  emits: ['scale-change', 'position-change'],
  setup(props, { emit }) {
    const containerRef = ref(null)
    const contentRef = ref(null)
    
    // 缩放和位置状态
    const state = reactive({
      scale: 1,
      translateX: 0,
      translateY: 0,
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0
    })

    // 应用变换
    const applyTransform = () => {
      if (contentRef.value) {
        contentRef.value.style.transform = 
          `translate(${state.translateX}px, ${state.translateY}px) scale(${state.scale})`
      }
    }

    // 处理滚轮缩放
    const handleWheel = (event) => {
      if (!props.enableZoom) return
      
      event.preventDefault()
      
      // 获取鼠标在容器中的位置
      const rect = containerRef.value.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top
      
      // 计算缩放前鼠标在内容中的位置
      const beforeX = (mouseX - state.translateX) / state.scale
      const beforeY = (mouseY - state.translateY) / state.scale
      
      // 计算新的缩放比例
      const delta = event.deltaY > 0 ? -props.scaleStep : props.scaleStep
      const newScale = Math.max(props.minScale, Math.min(props.maxScale, state.scale + delta))
      
      // 如果缩放比例没有变化，直接返回
      if (newScale === state.scale) return
      
      // 计算缩放后鼠标在内容中的位置
      const afterX = (mouseX - state.translateX) / newScale
      const afterY = (mouseY - state.translateY) / newScale
      
      // 调整平移量，使鼠标位置保持不变
      state.translateX += (afterX - beforeX) * newScale
      state.translateY += (afterY - beforeY) * newScale
      state.scale = newScale
      
      applyTransform()
      emit('scale-change', state.scale)
    }

    // 处理鼠标按下（开始拖拽）
    const handleMouseDown = (event) => {
      if (!props.enablePan) return
      
      event.preventDefault()
      state.isDragging = true
      state.lastMouseX = event.clientX
      state.lastMouseY = event.clientY
      
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    // 处理鼠标移动（拖拽）
    const handleMouseMove = (event) => {
      if (!state.isDragging) return
      
      const deltaX = event.clientX - state.lastMouseX
      const deltaY = event.clientY - state.lastMouseY
      
      state.translateX += deltaX
      state.translateY += deltaY
      state.lastMouseX = event.clientX
      state.lastMouseY = event.clientY
      
      applyTransform()
      emit('position-change', { x: state.translateX, y: state.translateY })
    }

    // 处理鼠标抬起（结束拖拽）
    const handleMouseUp = () => {
      state.isDragging = false
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    // 重置缩放和位置
    const reset = () => {
      state.scale = 1
      state.translateX = 0
      state.translateY = 0
      applyTransform()
      emit('scale-change', state.scale)
      emit('position-change', { x: state.translateX, y: state.translateY })
    }

    // 设置缩放比例
    const setScale = (scale, centerX = null, centerY = null) => {
      const newScale = Math.max(props.minScale, Math.min(props.maxScale, scale))
      
      if (centerX !== null && centerY !== null) {
        // 以指定点为中心缩放
        const beforeX = (centerX - state.translateX) / state.scale
        const beforeY = (centerY - state.translateY) / state.scale
        
        const afterX = (centerX - state.translateX) / newScale
        const afterY = (centerY - state.translateY) / newScale
        
        state.translateX += (afterX - beforeX) * newScale
        state.translateY += (afterY - beforeY) * newScale
      }
      
      state.scale = newScale
      applyTransform()
      emit('scale-change', state.scale)
    }

    // 放大
    const zoomIn = () => {
      const rect = containerRef.value.getBoundingClientRect()
      const centerX = rect.width / 2
      const centerY = rect.height / 2
      setScale(state.scale + props.scaleStep, centerX, centerY)
    }

    // 缩小
    const zoomOut = () => {
      const rect = containerRef.value.getBoundingClientRect()
      const centerX = rect.width / 2
      const centerY = rect.height / 2
      setScale(state.scale - props.scaleStep, centerX, centerY)
    }

    // 适应容器大小
    const fitToContainer = () => {
      if (!containerRef.value || !contentRef.value) return
      
      const containerRect = containerRef.value.getBoundingClientRect()
      const contentRect = contentRef.value.getBoundingClientRect()
      
      const scaleX = containerRect.width / contentRect.width
      const scaleY = containerRect.height / contentRect.height
      const newScale = Math.min(scaleX, scaleY) * 0.9 // 留一些边距
      
      state.scale = Math.max(props.minScale, Math.min(props.maxScale, newScale))
      state.translateX = (containerRect.width - contentRect.width * state.scale) / 2
      state.translateY = (containerRect.height - contentRect.height * state.scale) / 2
      
      applyTransform()
      emit('scale-change', state.scale)
      emit('position-change', { x: state.translateX, y: state.translateY })
    }

    // 组件挂载时绑定事件
    onMounted(() => {
      if (containerRef.value) {
        containerRef.value.addEventListener('wheel', handleWheel, { passive: false })
        containerRef.value.addEventListener('mousedown', handleMouseDown)
      }
    })

    // 组件卸载时清理事件
    onUnmounted(() => {
      if (containerRef.value) {
        containerRef.value.removeEventListener('wheel', handleWheel)
        containerRef.value.removeEventListener('mousedown', handleMouseDown)
      }
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    })

    return {
      containerRef,
      contentRef,
      state,
      reset,
      setScale,
      zoomIn,
      zoomOut,
      fitToContainer
    }
  },

  template: /*html*/ `
    <div 
      ref="containerRef"
      class="zoomable-container"
      :class="{ 'is-dragging': state.isDragging }"
    >
      <div 
        ref="contentRef"
        class="zoomable-content"
        :style="{ transformOrigin: '0 0' }"
      >
        <slot></slot>
      </div>
      
      <!-- 缩放控制按钮 -->
      <div class="zoom-controls" v-if="$slots.controls">
        <slot name="controls" :zoom-in="zoomIn" :zoom-out="zoomOut" :reset="reset" :fit="fitToContainer" :scale="state.scale"></slot>
      </div>
    </div>
  `
} 