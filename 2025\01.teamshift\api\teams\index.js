import { post } from '../../utils/request.js'
/**
 * 获取组长和组员
 */
export const queryTeamLeaderAndStaff = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_teamleaderandstaff',
  })
}

/**
 * 获取班组计划
 */
export const queryTeamPlan = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_query_team_engine_plan',
    ...params,
  })
}

/**
 * 获取班组计划任务列表
 */
export const queryTeamPlanTaskList = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_quick_schedul_task_list',
  })
}

/**
 * 获取头部数据
 */
export const queryHeadData = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_query_staff_list_head',
    ...params,
  })
}

/**
 * 获取班组计划任务列表
 */
export const queryTeamPlanSeeList = (id_main) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_plan_see',
    id_main,
  })
}

/**
 * 提交反馈
 */
export const submitFeedback = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_plan_fedback',
    ...params,
  })
}

/**
 * 获取班组计划编辑列表
 */
export const queryTeamPlanEditList = (id_main) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_plan',
    id_main,
  })
}

/**
 * 获取Assemblt Task 下拉选择
 * @param {string} str_sm
 */
export const queryAssembltTaskList = (str_sm) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_assembly_task',
    str_sm,
  })
}

/**
 * 获取Site 下拉选择
 */
export const querySiteList = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_site_list',
  })
}

/**
 * 获取团队选项
 * @param {object} params
 */
export const queryTeamList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_staff_list',
    ...params,
  })
}

/**
 * 获取Team Sec 选项
 * @param {object} params
 */
export const queryTeamSecList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_teamsection_staff_list',
    ...params,
  })
}

/**
 * 保存班组计划
 * @param {array} planItems
 */
export const saveTeamPlan = (planItems) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_team_plan',
    planItems,
  })
}

/**
 * 获取班组计划
 */
export const queryF2TeamPlan = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_query_f2_team_plan',
    param_data:params
  })
}

/**
 * 获取班组计划
 */
export const queryF2TeamPlanEditList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_query_f2_team_plan',
    ...params,
  })
}

export const queryF2TargetEnum = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_f2_target_enum'
  })
}

export const queryEnum = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_enum',
    str_key:params
  })
}

export const queryF2TaskEnum = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_f2_task_enum'
  })
}

export const queryF2TeamPlanView = (staff,shiftDate) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_f2_team_plan_view',
    id_staff:staff,
    dt_shift:shiftDate

  })
}

export const queryShifts = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_shift'

  })
}

export const queryTeamStaff = (params1,params2,params3) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_team_member',
    id_team:params1,
    id_dept:params2,
    str_dept:params3
  })
}

export const queryDeptStaff = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_team_member',
    id_dept:params
  })
}

/**
 * 保存班组计划
 * @param {array} planItems
 */
export const saveF2TeamPlan = (planItems) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_F2_team_plan',
    data:planItems,
  })
}


/**
 * 批量保存班组计划
 * @param {array} planItems
 */
export const saveBatchF2TeamPlan = (planItems) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_batch_F2_team_plan',
    data:planItems,
  })
}
//修改后的保存反馈
export const saveFeedBack = (param) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_feed_back_record',
    postData:param,
  })
}
