/* 主容器样式 */
.schedule-container {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

/* 加载遮罩层 */
.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.loading-text {
  font-size: 14px;
  color: #409eff;
}

/* 搜索表单 */
.search-form {
  margin-bottom: 20px;
}

/* 表格样式 */
.schedule-table {
  flex: 1;
}

/* 表格单元格样式 */
.schedule-table .el-table__cell {
  padding: 6px 0; /* 减少单元格内边距 */
  vertical-align: top;
}

/* 表格行样式 */
.schedule-table .el-table__row {
  height: auto;
}

/* 任务列表容器 */
.task-list {
  display: flex;
  flex-direction: column;
  min-height: 80px;
  padding: 6px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

/* 任务容器 */
.tasks-container {
  flex: 1;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* 任务项 */
.task-item {
  padding: 3px 4px;
  border-radius: 4px;
  background-color: #ffffff;
  border: 1px solid #e0e6ed;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  min-height: 24px;
}

.task-item:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
  border-color: #409eff;
}

.task-item.is-locked {
  opacity: 0.7;
  cursor: not-allowed;
}

.task-item.status-process-check {
  border-left: 4px solid #90ee90;
  font-weight: bold;
}

.task-item.status-release-check {
  border-left: 4px solid #e6a23c;
  font-weight: bold;
}

/* 新增：确保 task-content 填满 task-item */
.task-item .task-content {
  width: 100%;
  height: 100%;
}

.task-item.is-dragging {
  opacity: 0.5;
  transform: scale(1.02);
}

/* 任务内容 */
.task-content {
  display: flex;
  align-items: center;
  font-size: 12px;
}

/* 单元格操作按钮 */
.cell-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  flex-wrap: wrap;
}

/* 查看更多按钮样式 */
.cell-actions .el-button--text {
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #e0f2fe;
  border-radius: 6px;
  padding: 2px 8px;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
  min-height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 按钮悬停效果 */
.cell-actions .el-button--text:hover {
  background: #e0f2fe;
  color: #0284c7;
  border-color: #bae6fd;
  transform: none;
}

/* 按钮点击效果 */
.cell-actions .el-button--text:active {
  background: #bae6fd;
  transform: scale(0.98);
}

/* 按钮焦点效果 */
.cell-actions .el-button--text:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* 移除波纹效果 */
.cell-actions .el-button--text::after {
  display: none;
}

/* 查看更多按钮文字样式 */
.cell-actions .el-button--text span {
  position: relative;
}

/* 添加小图标效果 */
.cell-actions .el-button--text::before {
  content: '';
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 3px solid currentColor;
  margin-right: 4px;
  transition: transform 0.2s ease;
}

/* 展开状态的图标 */
.cell-actions .el-button--text[data-expanded="true"]::before {
  transform: rotate(180deg);
}

/* 周末样式 */
.weekend-cell {
  background-color: #fff5f5;
}

/* 拖拽状态样式 */
.sortable-ghost {
  opacity: 0.2;
  background-color: #e6f7ff;
  border: 1px dashed #409eff;
}

.sortable-drag {
  opacity: 0.8;
  transform: scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* 媒体查询 - 小屏幕适配 */
@media screen and (max-width: 768px) {
  .schedule-container {
    padding: 8px;
  }

  .task-list {
    min-height: 60px;
  }

  /* 小屏幕下的查看更多按钮 */
  .cell-actions .el-button--text {
    padding: 2px 6px;
    font-size: 10px;
    min-height: 20px;
  }

  .cell-actions .el-button--text::before {
    border-left: 2px solid transparent;
    border-right: 2px solid transparent;
    border-top: 2px solid currentColor;
    margin-right: 3px;
  }
}

/* 锁定状态按钮 */
.is-locked {
  color: #f56c6c;
} 