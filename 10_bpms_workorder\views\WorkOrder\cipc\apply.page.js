const { ref ,defineAsyncComponent,reactive,onMounted} = Vue
import { useCipcApplyApi } from '../../../api/cipc/useapply.js';
export const CipcApplyPageComponent = {
    components: {
        HtVxeTable: defineAsyncComponent(() => import('../../../../components/VxeTable/HtVxeTable.js')),
        PagePager: defineAsyncComponent(() => import('../../../../components/VxePager/PagePager.js')),
        HtDrawer: defineAsyncComponent(() => import('../../../../components/ht.drawer.js')),
    },
    setup() {

        const { getColumns ,getTablePage} = useCipcApplyApi();
        const tableState = reactive({
            tableData: [],
            tableColumns: getColumns(),
        });
        // 分页数据
        const pagerState = reactive({
            CurrentPage: 1,
            PageSize: 10,
            Total: 0,
        })
        // 获取表格数据通过前端分页
        const getPage = async (currentPage, pageSize, queryLists = []) => {
            const tableData = await getTablePage(queryLists)
            const start = (currentPage - 1) * pageSize
            const end = currentPage * pageSize
            tableState.tableData = tableData.slice(start, end)
            pagerState.total = tableData.length
        }
        onMounted(() => {
            getPage(pagerState.CurrentPage, pagerState.PageSize)
        })
        return {
            tableState,
            pagerState,
            getPage,
            
        }
    },
    template: /*html*/ `
    <!--    头部按钮-->
    <div class="flex items-center flex-wrap">
      <article class="mx-4 my-2">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="info" @click="handleDetail">查看</el-button>
      </article>

    </div>
    <div class="border-b-2 mb-2"></div>
    <!--    每日产能配置表格-->
    <div class="mx-4" style="height: calc(100vh - 140px);">
      <HtVxeTable
        ref="basePageTableRef"
        :tableState.tableData
        :tableState.tableColumns
      ></HtVxeTable>
      <PagePager
        :currentPage="pagerState.currentPage"
        :pageSize="pagerState.PageSize"
        :total="pagerState.total"
        @pageChange="handlePageChange"
      ></PagePager>
    </div>
  `,
}
