import { ganttConfig, ganttTemplates } from '../config/ganttConfig.js'

const { ref, watch, nextTick, onMounted, onUnmounted } = Vue
const { useVModel } = VueUse

export default {
  name: 'GanttDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    tasks: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: '甘特图详情',
    },
  },
  emits: ['update:visible', 'close'],
  setup(props, { emit }) {
    const ganttContainer = ref(null)
    const vModelVisible = useVModel(props, 'visible', emit)
    const gantt = Gantt.getGanttInstance()
    // gantt.i18n.setLocale('cn')

    const initGantt = () => {
      if (ganttContainer.value) {
        // 应用配置
        for (const key in ganttConfig) {
          if (ganttConfig.hasOwnProperty(key)) {
            gantt.config[key] = ganttConfig[key]
          }
        }

        // 应用模板
        for (const key in ganttTemplates) {
          if (ganttTemplates.hasOwnProperty(key)) {
            gantt.templates[key] = ganttTemplates[key]
          }
        }

        gantt.init(ganttContainer.value)

        gantt.parse(JSON.parse(JSON.stringify(props.tasks)))
      } else {
        console.warn('Gantt instance (gantt) or container (ganttContainer) not found for initGantt.')
      }
    }

    onMounted(() => {
      nextTick(() => {
        initGantt()
      })
    })

    const handleDrawerCloseEvent = () => {
      // 关闭甘特图
      gantt.clearAll()
      emit('close')
    }

    return {
      ganttContainer,
      vModelVisible,
      handleDrawerCloseEvent,
    }
  },
  template: /*html*/ `
    <el-drawer
      class="common-drawer"
      v-model="vModelVisible"
      :title="title"
      direction="rtl"
      size="80%"
      destroy-on-close
      @close="handleDrawerCloseEvent"
    >
      <div class="p-4 h-full flex flex-col">
        <div ref="ganttContainer" class="w-full flex-grow border border-gray-300 min-h-[300px]"></div>
      </div>
    </el-drawer>
  `,
}
