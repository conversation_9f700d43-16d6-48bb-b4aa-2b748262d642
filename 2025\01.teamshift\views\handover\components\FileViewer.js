export default {
  name: 'FileViewerComponent',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    files: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update:visible', 'download'],
  setup(props, { emit }) {
    const { useVModel } = VueUse

    // 抽屉可见性控制
    const drawerVisible = useVModel(props, 'visible', emit)
    
    // 处理下载事件
    const handleDownload = (file) => {
      emit('download', file)
    }
    
    // 获取文件扩展名
    const getFileExtension = (filename) => {
      if (!filename) return ''
      return filename.split('.').pop()
    }

    // 处理删除事件
    const handleDelete = (file) => {
      emit('delete', file)
    }

    return {
      drawerVisible,
      handleDownload,
      getFileExtension,
    }
  },
  template: /*html*/ `
    <el-drawer v-model="drawerVisible" title="文件列表" size="50%" direction="rtl">
      <el-table :data="files" border size="small">
        <el-table-column prop="str_file_name" label="文件名">
          <template #default="{ row }">{{ row.str_file_name || row.name }}</template>
        </el-table-column>
        <el-table-column label="类型" width="100">
          <template #default="{ row }">{{ getFileExtension(row.str_file_name || row.name) }}</template>
        </el-table-column>
        <el-table-column prop="uploadTime" label="上传时间" width="180" />
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleDownload(row)">下载</el-button>
            <el-button text type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
  `,
} 