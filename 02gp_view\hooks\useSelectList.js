import { post } from '../../config/axios/httpReuest.js';

export function useSelectList() {
  // get engine type list
  const getEngineTypeList = async () => {
    const param = {
      ac: 'gp_search_engine_type',
    };
    const { data } = await post(param);
    if (data.code === 'error') {
      ElementPlus.ElMessage.error(data.text);
      return [];
    }
    return data.data;
  };

  // get flow list
  const getFlowList = async () => {
    const param = {
      ac: 'gp_search_flow',
    };
    const { data } = await post(param);
    if (data.code === 'error') {
      ElementPlus.ElMessage.error(data.text);
      return [];
    }
    return data.data.map((item) => {
      return {
        label: item.str_key,
        value: item.str_value,
      };
    });
  };

  /**
   * @description get SM list
   * @param engineType {string}  机型Id
   * @return {Promise<*|*[]>}
   */
  const getSmList = async (engineType = '') => {
    const param = {
      ac: 'gp_search_engine_sm',
      id_engine_type: engineType,
    };
    const { data } = await post(param);
    if (data.code === 'error') {
      ElementPlus.ElMessage.error(data.text);
      return [];
    }
    return data.data;
  };

  /**
   * @description get occupy list
   * @return {Promise<*|*[]>}
   */
  const getOccupyList = async () => {
    const param = {
      ac: 'gp_search_occupy',
    };
    const { data } = await post(param);
    if (data.code === 'error') {
      ElementPlus.ElMessage.error(data.text);
      return [];
    }
    return data.data;
  };

  /**
   * @description get workclothes list
   * @return {Promise<*|*[]>}
   */
  const getWorkclothesList = async () => {
    const param = {
      ac: 'gp_search_workclothes',
    };
    const { data } = await post(param);
    if (data.code === 'error') {
      ElementPlus.ElMessage.error(data.text);
      return [];
    }
    return data.data;
  };

  return {
    getEngineTypeList,
    getFlowList,
    getSmList,
    getOccupyList,
    getWorkclothesList,
  };
}
