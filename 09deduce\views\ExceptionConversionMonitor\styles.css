/* 异常转换监控页面自定义样式 */

/* 全局样式增强 */
.exception-monitor-container {
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 轮播容器样式 */
.table-carousel-wrapper {
  height: 100%;
  width: 100%;
}

.carousel-controls {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid #e2e8f0;
}

.control-btn {
  transition: all 0.2s ease;
}

.control-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.table-indicator {
  transition: all 0.2s ease;
  cursor: pointer;
}

.table-indicator:hover {
  transform: translateY(-1px);
}

.progress-container {
  overflow: hidden;
}

.progress-bar {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

/* 轮播表格样式 */
.carousel-table-container {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin: 0;
  padding: 15px;
  box-sizing: border-box;
}

.carousel-table-container:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.table-content {
  position: relative;
}

.navigation-buttons {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.table-content:hover .navigation-buttons {
  opacity: 1;
}

.nav-btn {
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
}

.nav-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

.page-indicators {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.indicator-dot {
  cursor: pointer;
  transition: all 0.2s ease;
}

.indicator-dot:hover {
  transform: scale(1.2);
}

/* 表格现代化样式 */
.modern-table {
  border-radius: 8px;
  overflow: hidden;
}

.modern-table .vxe-table--header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.modern-table .vxe-table--header th {
  border-color: rgba(255, 255, 255, 0.2);
}

.modern-table .vxe-table--body tr:hover {
  background-color: rgba(102, 126, 234, 0.05);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.modern-table .vxe-table--body td {
  border-color: #f0f2f5;
}

/* 表格卡片样式 */
.table-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.table-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 滚动条美化 */
.vxe-table--body-wrapper::-webkit-scrollbar {
  width: 6px;
}

.vxe-table--body-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.vxe-table--body-wrapper::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 3px;
}

.vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a42a0);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.exception-parts-section,
.white-to-yellow-section {
  animation: fadeInUp 0.6s ease;
}

/* 状态指示器 */
.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -10px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  transform: translateY(-50%);
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .exception-monitor-container {
    padding: 1rem;
  }
  
  .white-to-yellow-section .grid {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .table-card {
    margin-bottom: 1rem;
  }
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 数据更新提示 */
.update-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 表格标题样式增强 */
.table-header h3 {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 按钮悬停效果 */
.el-button {
  transition: all 0.2s ease;
}

.el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 标签样式增强 */
.el-tag {
  font-weight: 500;
  border-radius: 12px;
}

/* 页面标题装饰 */
.header-section h1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 状态栏样式 */
.status-bar {
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

/* 性能优化样式 */
.modern-table {
  will-change: transform;
  transform: translateZ(0);
  width: 100% !important;
}

.table-wrapper {
  will-change: opacity, transform;
  transform: translateZ(0);
  width: 100% !important;
}

.carousel-content {
  contain: layout style paint;
}

.table-container {
  contain: layout style paint;
  will-change: opacity, transform;
  width: 100%;
  height: 100%;
}

/* 表格滚动条样式 */
.modern-table .vxe-table--body-wrapper {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  scroll-behavior: smooth;
}

.modern-table .vxe-table--body-wrapper::-webkit-scrollbar {
  width: 6px;
}

.modern-table .vxe-table--body-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modern-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 3px;
  opacity: 0.7;
}

.modern-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a42a0);
  opacity: 1;
}

/* 优化表格行高度 */
.modern-table .vxe-table--body tr {
  height: 48px;
}

.modern-table .vxe-table--header tr {
  height: 56px;
}

/* 轮播动画优化 */
@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.table-container {
  animation: fadeInSlide 0.3s ease-out;
}

/* 防止文字选择 */
.carousel-controls,
.table-header,
.page-indicators {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 硬件加速 */
.control-btn,
.table-indicator,
.nav-btn,
.indicator-dot {
  transform: translateZ(0);
  will-change: transform;
}

/* 表格全屏宽度样式 */
.vxe-table {
  width: 100% !important;
}

.vxe-table--main-wrapper {
  width: 100% !important;
}

.vxe-table--header-wrapper,
.vxe-table--body-wrapper,
.vxe-table--footer-wrapper {
  width: 100% !important;
}

.modern-table .vxe-table--header,
.modern-table .vxe-table--body {
  width: 100% !important;
}

/* 确保表格容器占满宽度 */
.carousel-table-container,
.table-content,
.table-wrapper {
  width: 100% !important;
  box-sizing: border-box;
}

/* 响应式列宽调整 */
.modern-table .vxe-table--body td,
.modern-table .vxe-table--header th {
  min-width: 120px;
}

/* 表格自动适应宽度 */
.modern-table {
  table-layout: auto !important;
}

.modern-table .vxe-table--header table,
.modern-table .vxe-table--body table {
  width: 100% !important;
  table-layout: auto !important;
} 