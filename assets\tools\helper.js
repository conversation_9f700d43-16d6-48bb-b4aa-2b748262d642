//获取Query参数
function getQueryString(key) {
    var reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
};

//获取Query参数
function getParamValue(key) {
    var paramStr = getQueryString("param_value");
    var obj = jQuery.parseJSON(paramStr);
    if (obj == null) {
        return null;
    }
    var result = obj[key];
    return result;
};

//是否为空
function isNullOrEmpty(value) {
    if (typeof (value) == undefined || value == null || value == "") {
        return true;
    }
    return false;
}

//休眠
function sleep(time) {
    var timeStamp = new Date().getTime();
    var endTime = timeStamp + time;
    while (true) {
        if (new Date().getTime() > endTime) {
            return;
        }
    }
}

//获取今天是今年的第几周
function getWeek(dt) {
    let d1 = new Date(dt);
    let d2 = new Date(dt);
    d2.setMonth(0);
    d2.setDate(1);
    let rq = d1 - d2;
    let days = Math.ceil(rq / (24 * 60 * 60 * 1000));
    let num = Math.ceil(days / 7);
    return num + 1;
}
/**获取当前日期  yyyy-MM-dd*/
function getNowDay(date_input, separator = "-") {
    let date=new Date();
    if (date_input) {
        date = new Date(date_input);
    }
    let month = date.getMonth() + 1;
    let day = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (day >= 1 && day <= 9) {
        day = "0" + day;
    }

    return date.getFullYear() + separator + month + separator + day;
}
/**获取几天前时间 */
function AddDays(num) {
    let newdate = new Date();
    let newtimes = newdate.getTime() + (num * 24 * 60 * 60 * 1000);
    newdate.setTime(newtimes);
    return getNowDay(newdate.toString(),"-")
}