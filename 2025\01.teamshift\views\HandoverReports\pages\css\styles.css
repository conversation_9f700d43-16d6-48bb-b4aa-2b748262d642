/* 交接统计报表自定义样式 */

/* 页面整体布局 */
.handover-report-container {
  width: 100%;
  padding: 1.25rem;
  box-sizing: border-box;
}

/* 确保图表容器鼠标悬停时显示手型光标，提示可点击 */
.echarts-container {
  cursor: pointer;
  width: 100%;
  height: 350px !important; /* 确保足够的高度显示图表和图例 */
}

/* 统计卡片样式 */
.stat-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.05);
}

/* 模块间隔 */
.module-spacing {
  margin-bottom: 1.25rem;
}

/* 图表容器样式 */
.chart-container {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.25rem;
  width: 100%;
  height: 420px; /* 增加高度容纳标示线和图例 */
}


/* 表格自定义样式 */
.handover-table {
  border-radius: 0.25rem;
  overflow: hidden;
  width: 100%;
}

/* 表格头部样式 */
.handover-table .el-table__header-wrapper th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  height: 50px;
}

/* 表格行样式 */
.handover-table .el-table__row {
  cursor: default;
  transition: background-color 0.2s;
}

/* 表格行悬停样式 */
.handover-table .el-table__row:hover > td {
  background-color: #f0f9ff !important;
}

/* 滚动条样式 */
.handover-table .el-scrollbar__bar.is-horizontal {
  height: 8px;
}

.handover-table .el-scrollbar__bar.is-vertical {
  width: 8px;
}

/* 过滤器容器样式 */
.filter-container {
  background-color: white;
  border-radius: 0.5rem;
  margin-bottom: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* 美化筛选器内部元素 */
.filter-container .el-date-editor,
.filter-container .el-select {
  width: 100%;
}

.filter-container .el-input__inner,
.filter-container .el-input__suffix {
  height: 36px;
  line-height: 36px;
}

.filter-container .el-tag {
  margin-right: 4px;
}

.filter-container .el-button + .el-button {
  margin-left: 0.5rem;
}

/* 页面标题样式 */
.page-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.page-subtitle {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

/* 响应式网格布局 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.25rem;
  width: 100%;
}

@media (min-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-container.stats {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 自定义主题颜色 */
:root {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
} 