import {
  GetCheckShiftByIds,
  queryShifts,
  queryTeamSecList,
  updateCheckShift
} from '../api/index.js'




export default {
  name: 'TeamPlanEdit',
  props: {
    planItem: Object,
    visible: Boolean,
    deptId:String,
    teamId:String
    
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const { reactive, onMounted, ref } = Vue
    const { useVModel } = VueUse
    const teamPlanEditVisible = useVModel(props, 'visible')

    // 团队计划数据
    const planData = reactive({
     // name: props.planItem?.name || '',
      scheduleAdjust: '',
      isWholeMove: false,
      planItems: [],
    })

    // 计算计划项数量
    const planItemsCount = ref(0)

    // 保存计划
    const handleSave = async () => {

      try {
        // 触发表单验证
        //await formRef.value.validate()
        
        // 添加自定义验证逻辑
        // const isValid = planData.planItems.every(item => {
        //   // 检查排班日期
        //   if (!item.dt_shift_time || item.dt_shift_time.length !== 2) {
        //     ElMessage.error('排班日期必须填写')
        //     return false
        //   }
        //   return true
        // })
  
        //if (!isValid) return
   
        await updateCheckShift(planData.planItems)
        teamPlanEditVisible.value = false
        emit('refresh')
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    }
   

   const shiftOptions = ref([])
    // 获取Site 下拉选择
    const getShiftOptions = async () => {
      const res = await queryShifts()
      shiftOptions.value = res.data ?? []
    }
 
  
    
  
 

    // 添加表单引用
    const formRef = ref(null) 


    // 获取编辑页的数据
    const getTeamPlanEditList = async () => {
      try { 
        const ids = props.planItem.task.map(item => item.id)
        const res = await GetCheckShiftByIds(ids)
        if (!res) return 
        planData.planItems = res
        planItemsCount.value = res.length
      } catch (error) {
        console.error('获取团队计划编辑数据失败:', error)
      }
    }

    //增加任务
    const addTaskPlan = () => {
      planData.planItems.push(
        { staffs:[props.planItem.id_staff], 
          dt_shift:props.planItem.dt_shift,
          id_shift:props.planItem.task[0].id_shift,
          dt_shift_time:props.planItem.task[0].dt_shift_time
        })
        planItemsCount.value = planData.planItems.length
    };
    //删除任务
    const delTaskPlan = (task,index) => {
        planData.planItems.splice(index, 1)
        planItemsCount.value = planData.planItems.length
 
    };

    const teamSecOptions = ref([])
    const getTeamSecOptions = async () => {
      const params = {
        id_main: props.planItem.planId,
        id_team: props.planItem.id_team,
        pt_dt:props.planItem.plan_date
      }
      const res = await queryTeamSecList(params)
      teamSecOptions.value = res ?? []
    }
 

    onMounted(async () => {
       await getTeamPlanEditList()
      // await getF2TaskOptions();
      // await getf2TargetOptions();
      // await getTypeOptions();
       await getShiftOptions();
       await getTeamSecOptions();
      // await getRepairTypeOptions();
      // await getDeptStaff();
      // await getTeamStaff();

    })

    return { 
      formRef,
      shiftOptions,
      teamSecOptions,
      teamPlanEditVisible,
      planData,
      planItemsCount,
      handleSave,   
      addTaskPlan,
      delTaskPlan

    }
  },
  template: /*html*/ `
    <el-dialog
      v-model="teamPlanEditVisible"
      title="Team Plan"
      width="80%"
      class="common-dialog"
      :fullscreen="false"
      :append-to-body="true"
    >
      <div class="pr-2">
        <el-form :model="planData" label-width="150px"  ref="formRef"
        :rules="formRules">
          <div class="my-2">
            <el-badge :value="planItemsCount" class="item" type="info">
              <el-button>Num</el-button>
            </el-badge>
          </div>

          <div
            v-for="(item, index) in planData.planItems"
            :key="item.id"
            class="my-2 flex flex-col rounded-md border p-4">
            <!-- <div>
              <el-button style="float: right;" type="danger" size="small" @click="delTaskPlan(item,index)">
                  Delete
              </el-button>
            </div> -->

            <div class="grid grid-cols-1 gap-4 pt-2 md:grid-cols-3">
              <el-form-item label="ESN">
                <el-input  v-model="item.str_esn"   disabled > 
              </el-form-item> 
              <el-form-item label="Model">
                <el-input  v-model="item.str_sm"   disabled > 
              </el-form-item> 
            <el-form-item label="Task">  
                <el-input  v-model="item.str_task"   disabled >
              </el-form-item> 
            <el-form-item label="Name">  
                <el-input  v-model="item.str_staff"   disabled >
              </el-form-item> 
              <el-form-item label="Type" >
                 <el-input  v-model="item.str_check_type"   disabled >  
              </el-form-item> 
               <el-form-item label="Task Description">  
                <el-input  v-model="item.str_task_description"  >
              </el-form-item> 

              <el-form-item label="Team Secs" >
                <el-select
                  v-model="item.teamSecs"
                  multiple
                  filterable
                  placeholder="请选择借调人员"
                >
                  <el-option v-for="item in teamSecOptions" :key="item.id" :label="item.str_name" :value="item.id" />
                </el-select>
              </el-form-item> 
 

              <el-form-item label="排班日期"> 
                  <el-input  v-model="item.dt_shift"   disabled >  
              </el-form-item> 

               <el-form-item label="Shift" required>
              <el-select v-model="item.id_shift"  disabled placeholder="请选择班次" class="w-full">
                  <el-option v-for="sub in shiftOptions"  :key="sub.id" :label="sub.str_name" :value="sub.id" />
                </el-select>
              </el-form-item>
            </div>
           
 

            <div class="grid grid-cols-1 gap-4 pt-2 md:grid-cols-1">
              <el-form-item label="Shift time">
                <el-time-picker
                  v-model="item.dt_shift_time"
                  is-range
                  start-placeholder="End time"
                  range-separator="To"
                  end-placeholder="End time"
                  value-format="HH:mm"
                  format="HH:mm"
                  disabled
                />
              </el-form-item>
            </div>

            <el-form-item label="Remark" class="pt-2">
              <el-input v-model="item.str_notes" type="textarea" :rows="3" placeholder="请输入备注" />
            </el-form-item>
          </div>
        </el-form>
      </div> 
      <!-- <el-button style="margin-left:20px; float:right" size="default" type="success" @click="addTaskPlan()">
            +New Scheduling
      </el-button>  -->

      <template #footer>
        <div class="flex justify-end">
          <el-button @click="teamPlanEditVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>
  `,
}
