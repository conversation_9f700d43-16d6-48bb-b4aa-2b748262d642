<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!--    全部样式-->
    <link href="../../../../assets/css/index.css" rel="stylesheet" />
    <!--    引入element-plus的样式-->
    <link href="../../../../assets/ElementPlus@2.8.6/index.css" rel="stylesheet" />
    <!--    引入drawer的样式-->
    <link href="../../../../assets/css/drawer.css" rel="stylesheet" />
    <!--    引入vxe-table的样式-->
    <link href="../../../../assets/vxe-table/style.css" rel="stylesheet" />
    <!--    引入VUE-->
    <script src="../../../../assets/vue@3.5.12/vue.global.js"></script>
    <!--    引入vxe-table组件-->
    <script src="../../../../assets/vxe-table/xe-utils.js"></script>
    <script src="../../../../assets/vxe-table/vxe-table.js"></script>
    <!--    引入element-plus-->
    <script src="../../../../assets/ElementPlus@2.8.6/index.full.js"></script>
    <!--  引入element-plus-icon-->
    <script src="../../../../assets/icons-vue@2.3.1/index.iife.min.js"></script>
    <!-- 引入element-plus-ch -->
    <script src="../../../../assets/element-plus@2.5.5/lang/zh-cn.js"></script>
    <!--    引入axios-->
    <script src="../../../../assets/axios@1.6.7/axios.min.js"></script>
    <!--    引入moment-->
    <script src="../../../../assets/moment/moment.min.js"></script>
    <script src="../../../../03team_new/comm/api_environment.js"></script>
    <title>CIPC 提交列表页</title>
  </head>
  <body>
    <div id="app">
      <cipc-apply-page-component></cipc-apply-page-component>
    </div>
  </body>
  <script type="module">
    import { CipcApplyPageComponent } from './apply.page.js'
    const { createApp } = Vue
    moment.updateLocale('en', {
      week: {
        dow: 1,
      },
    })
    const app = createApp({
      components: {
        CipcApplyPageComponent,
      },
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    app.use(ElementPlus, {
      locale: ElementPlusLocaleZhCn,
    })
    // app.use(VXETable)
    app.mount('#app')
  </script>
</html>
