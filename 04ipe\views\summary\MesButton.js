const MesButton = {
  props: {
    isAbnormal: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },
  template: /*html*/ `
    <button class="flex items-center gap-3 bg-white px-5 py-4 text-center text-sm text-black shadow-sm" type="button">
      <svg
        t="1724382143891"
        class="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="6542"
        width="62"
        height="62"
      >
        <path
          d="M884.363636 721.454545V186.181818H139.636364v535.272727h325.818181v69.818182h-139.636363v46.545455h372.363636v-46.545455h-162.909091v-69.818182h349.090909zM209.454545 256h605.09091v256H209.454545V256z m0 395.636364v-93.090909h605.09091v93.090909H209.454545z"
          fill="#409EFF"
          p-id="6543"
        ></path>
      </svg>
      <span class="text-2xl">MES<span v-if="isAbnormal" style="color: #00FF00;">(已完成)</span></span>
    </button>
  `,
}
export default MesButton
