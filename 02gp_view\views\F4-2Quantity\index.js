import { getF42QuantityGantt, updateGanttTask } from '../../api/index.js'
import DynamicColumnConfigurator from '../../components/DynamicColumnConfigurator.js'
import { useDynamicColumn } from '../../composables/useDynamicColumn.js'
import { freezeAndUnfreezeApi } from '../../api/calculation.js'

const { ref, reactive, onMounted, nextTick, computed, onUnmounted } = Vue

// 甘特图基础配置
const GANTT_CONFIG = {
  grid_width: 800,
  date_format: '%Y-%m-%d',
  date_grid: '%Y-%m-%d',
  drag_links: false,
  drag_project: true,
  drag_progress: false,
  drag_move: true,
  drag_resize: false,
  auto_type: true,
  auto_scheduling: true,
  // 启动撤销功能
  undo: true,
  // 撤销类型
  undo_types: {
    // 任务撤销
    task: true,
    // 链接撤销
    link: false,
  },
  // 设置滚动到今天
  scroll_position: new Date(),
  start_date: new Date(),
  // 结束时间为当前年份的最后一天
  end_date: moment().endOf('year').format('YYYY-MM-DD'),
  columns: [
    { name: 'text', label: 'ESN', tree: true, width: '*', resize: true },
    { name: 'wo', label: 'WO', align: 'center', width: 100 },
    { name: 'engine_type', label: '发动机类型', align: 'center', width: 100 },
    { name: 'client', label: '客户', align: 'center', width: 100 },
    { name: 'level', label: '修理级别', align: 'center', width: 100 },
    { name: 'start_date', label: '开始时间', align: 'center', width: 100 },
    { name: 'dt_end', label: '结束时间', align: 'center', width: 100 },
    { name: 'duration', label: '持续时间', align: 'center', width: 100 },
  ],
  min_column_width: 40,
  scale_height: 100,
  // 时间分割
  scales: [
    // 年
    { unit: 'year', step: 1, format: '%Y年' },
    // 月
    { unit: 'month', step: 1, format: '%M' },
    // 周
    { unit: 'week', step: 1, format: 'W%w' },
    // 星期
    { unit: 'day', step: 1, format: '%D' },
    // 天
    { unit: 'day', step: 1, format: '%m-%d' },
  ],
}

export const VehicleTestingTotalQuantityGantt = {
  components: {
    DynamicColumnConfigurator,
  },
  setup(props, { emit }) {
    const formSearch = reactive({
      str_node: '',
    })

    const dateValue = ref([])

    const ganttParse = ref({
      data: [],
      links: [],
    })

    const isCollapseGrid = ref(false)

    // 计算属性优化按钮文本显示
    const gridButtonText = computed(() => (isCollapseGrid.value ? '展开左侧' : '收起左侧'))

    // 初始化甘特图配置
    const initGanttConfig = () => {
      gantt.plugins({
        auto_scheduling: true,
        critical_path: true,
        marker: true,
        undo: true,
      })

      Object.entries(GANTT_CONFIG).forEach(([key, value]) => {
        gantt.config[key] = value
      })

      gantt.templates.task_class = (start, end, task) => `main-level-${task.$level}`

      // 禁用任务双击事件
      gantt.attachEvent('onTaskDblClick', function (id, e) {
        return false // 返回 false 阻止默认行为
      })

      // 拖动结束后的事件处理
      gantt.attachEvent('onAfterTaskDrag', function (id, mode) {
        const task = gantt.getTask(id)
        task._changed = true
        gantt.showDate(task.start_date)
      })

      gantt.attachEvent('onGanttRender', function () {
        scrollToToday()
      })
    }

    const dynamicColumn = useDynamicColumn(gantt, GANTT_CONFIG.columns)

    // 数据转换优化
    const transformData = (data) => {
      return data.map((item) => {
        const { id, str_esn, dt_start, dt_end, str_wo, str_engine_type, str_client, str_level, duration, is_lock,id_plan } =
          item
        return {
          id,
          text: str_esn,
          start_date: dt_start ? moment(dt_start).format('YYYY-MM-DD') : null,
          dt_end,
          duration,
          wo: str_wo,
          engine_type: str_engine_type,
          client: str_client,
          level: str_level,
          is_lock,
          id_plan
        }
      })
    }

    // 添加当前日期标记
    const addCurrentDateMarker = () => {
      const today = new Date()
      gantt.addMarker({
        start_date: today,
        css: 'current-date-marker',
        text: moment(today).format('YYYY-MM-DD'),
        title: '今天',
      })
    }

    const renderGantt = () => {
      gantt.init('gantt-container')
      addCurrentDateMarker()
      gantt.parse(ganttParse.value)
      gantt.render()
    }

    const loading = ref(false)

    const getGanttData = async () => {
      // 清除甘特图数据
      gantt.clearAll()
      try {
        loading.value = true
        let startDate, endDate
        if (Array.isArray(dateValue.value)) {
          startDate = dateValue.value[0]
          endDate = dateValue.value[1]
        } else {
          startDate = endDate = dateValue.value
        }
        const data = await getF42QuantityGantt(formSearch.str_node, startDate, endDate)
        // 获取当前数据中最小的日期
        const minDate = Math.min(...data.subPlans.map((item) => moment(item.dt_start).valueOf()))
        gantt.config.start_date = new Date(minDate)
        ganttParse.value.data = transformData(data.subPlans)
        ganttParse.value.links = data.links
        await nextTick()
        renderGantt()
      } catch (error) {
        console.error('获取甘特图数据失败:', error)
        ElementPlus.ElMessage.error('获取数据失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    // 切换甘特图的列
    const toggleGrid = () => {
      isCollapseGrid.value = !isCollapseGrid.value
      // 重新配置列,并且改变列的宽度
      gantt.config.columns = isCollapseGrid.value
        ? [{ name: 'text', label: 'ESN', tree: true, width: '*', resize: true }]
        : GANTT_CONFIG.columns
      gantt.config.grid_width = isCollapseGrid.value ? 200 : 800
      // 重新渲染
      gantt.render()
    }
    // 重置
    const reset = () => {
      formSearch.str_node = ''
      dateValue.value = []
      getGanttData()
    }

    // 添加容器高度响应式变量
    const containerHeight = ref('100%')

    // 检查是否在 iframe 中
    const isInIframe = () => {
      try {
        return window !== window.top
      } catch (e) {
        return true
      }
    }

    // 计算并设置容器高度
    const updateContainerHeight = () => {
      try {
        if (isInIframe()) {
          // iframe 环下的高度计算
          // 获取iframe的可视区域高度
          const iframeHeight = window.innerHeight || document.documentElement.clientHeight
          // 减去顶部搜索区域的高度(48px)和内边距(32px) 80px = header高度 + padding等
          containerHeight.value = `${iframeHeight - 130}px`
        } else {
          // 非 iframe 环境下的高度计算
          const viewportHeight = window.innerHeight
          containerHeight.value = `${viewportHeight - 130}px` // 80px = header高度 + padding等
        }
      } catch (error) {
        console.error('更新容器高度失败:', error)
        containerHeight.value = '100%'
      }
    }

    const scrollToToday = () => {
      const today = new Date()
      gantt.showDate(today)
    }

    const handleSave = () => {
      // 获取甘特图数据
      const { data } = gantt.serialize()
      const params = data
        .filter((item) => item._changed)
        .map((item) => ({
          id: item.id,
          dt_start: item.start_date,
        }))

      updateGanttTask(params).then((res) => {
        if (res) {
          ElementPlus.ElMessage.success('保存成功')
          getGanttData()
        }
      })
    }

    onMounted(() => {
      initGanttConfig()
      getGanttData()
      updateContainerHeight()
      window.addEventListener('resize', updateContainerHeight)
    })

    // 组件销毁时移除事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', updateContainerHeight)
    })

    return {
      formSearch,
      dateValue,
      search: getGanttData,
      toggleGrid,
      gridButtonText,
      reset,
      loading,
      containerHeight,
      scrollToToday,
      handleSave,
      ...dynamicColumn,
    }
  },
  template: /*html*/ `
    <div class="flex flex-col gap-4 p-4">
      <div class="flex items-center justify-between">
        <!-- 左侧搜索区域 -->
        <div class="flex items-center gap-2">
          <label class="el-form-item__label text-right">关键字:</label>
          <el-input
            style="width:300px"
            v-model="formSearch.str_node"
            placeholder="请输入ESN\\WO\\发动机类型\\客户\\修理级别"
            clearable
          ></el-input>
          <label class="el-form-item__label text-right">release开始时间:</label>
          <el-date-picker
            v-model="dateValue[0]"
            type="date"
            placeholder="请选择release开始时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
          <label class="el-form-item__label text-right">release结束时间:</label>
          <el-date-picker
            v-model="dateValue[1]"
            type="date"
            placeholder="请选择release结束时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
          <el-button type="primary" :loading="loading" @click="search">搜索</el-button>
          <el-button type="info" :loading="loading" @click="reset">重置</el-button>
          <el-button type="primary" @click="handleSave">保存并应用</el-button>
        </div>

        <!-- 右侧功能按钮区域 -->
        <div class="flex items-center gap-2">
          <el-button plain type="default" :loading="loading" @click="toggleGrid"> {{ gridButtonText }} </el-button>
          <el-button plain type="primary" :loading="loading" @click="scrollToToday">回到今天</el-button>
        </div>
      </div>
      <div class="flex justify-end gap-2">
        <DynamicColumnConfigurator
          v-model="visibleColumns"
          :all-columns="columnConfig"
          button-text="列配置"
          button-icon="Grid"
        />
      </div>
      <!-- 甘特图区域 -->
      <div v-loading="loading" class="relative" :style="{ height: containerHeight }">
        <div id="gantt-container" class="h-full w-full"></div>
      </div>
    </div>
  `,
}
