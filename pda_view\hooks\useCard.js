import { post } from '../../config/axios/httpReuest.js';

const { reactive, onMounted, ref } = Vue;

export function useCard() {
  /**
   * 卡片UI是否可见
   */
  const cardVisibility = ref(false);
  // 卡片数据
  const cardState = reactive({
    int_pcnum: 0,
    str_rate: '',
    int_pnum: 0,
    int_anum: 0,
    int_dnum: 0,
    int_xjnum: 0,
  });
  /**
   * 获取工作卡片数据
   * @param str_type 站点类型
   * @param dt_date 当前日期
   */
  const getDailyWorkCardData = async (str_type, dt_date) => {
    cardVisibility.value = false;
    const params = {
      ac: 'pda_pcard',
      str_type,
      dt_date,
    };
    const { data } = await post(params);
    cardState.int_pcnum = data.data.int_pcnum;
    cardState.str_rate = data.data.str_rate;
    cardState.int_pnum = data.data.int_pnum;
    cardState.int_anum = data.data.int_anum;
    cardState.int_dnum = data.data.int_dnum;
    cardState.int_xjnum = data.data.int_xjnum;
    cardVisibility.value = true;
  };

  return {
    cardState,
    getDailyWorkCardData,
    cardVisibility,
  };
}
