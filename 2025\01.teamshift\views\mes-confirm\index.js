const { ref, reactive, computed, onMounted } = Vue
const { ElMessage, ElMessageBox } = ElementPlus

// 导入组件和配置
import HtVxeTable from '../../components/VxeTable/HtVxeTable.js'
import { useBrowserZoom } from '../../utils/browser-zoom.js'
import { MES_CONFIG } from './config/index.js'
import { getMesConfirmList, getMesConfirmDetail, cancelMesConfirm } from './api/index.js'
import MesConfirmDialog from './components/MesConfirmDialog.js'

export default {
  name: 'MesConfirm',
  components: {
    HtVxeTable,
    MesConfirmDialog,
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const tableRef = ref(null)
    const tableData = ref([])
    const confirmDialogVisible = ref(false)
    const formData = reactive({
      id: null,
      person: '',
      startDate: '',
      endDate: '',
      reason: '',
      remarks: '',
      details: [],
    })

    const handleSaveSuccess = () => {
      confirmDialogVisible.value = false
      initData() // 刷新数据
    }

    // 分页数据
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
      pageSizes: MES_CONFIG.table.pagination.pageSizes,
      layout: MES_CONFIG.table.pagination.layout,
    })

    const searchForm = ref({})

    // 表格列配置（响应式）
    const tableColumns = ref([...MES_CONFIG.table.columns])

    // 动态获取确认状态选项
    const updateConfirmStatusFilterOptions = (data) => {
      // 找到确认状态列
      const statusColumn = tableColumns.value.find(col => col.field === 'int_confirm_status')
      if (statusColumn && (!statusColumn.filters || statusColumn.filters.length === 0)) {
        // 从数据中提取唯一的状态值
        const statusSet = new Set()
        data.forEach(item => {
          if (item.int_confirm_status !== undefined && item.int_confirm_status !== null) {
            statusSet.add(item.int_confirm_status)
          }
        })
        
        // 状态映射
        const statusMap = {
          0: '待确认',
          1: '已确认',
          '-1': '已作废'
        }
        
        // 生成选择选项 - 使用VxeTable原生筛选格式
        const filterOptions = Array.from(statusSet)
          .sort((a, b) => a - b) // 按状态值排序
          .map(status => ({
            label: statusMap[status] || `状态${status}`,
            value: status
          }))
        
        // 设置过滤器配置 - 使用VxeTable原生筛选格式
        statusColumn.filters = filterOptions
      }
    }

    // 初始化数据
    const initData = async () => {
      try {
        loading.value = true
        const params = {
          PageSize: pagination.pageSize,
          Currentpage: pagination.currentPage,
          ...searchForm.value,
        }
        const applications = await getMesConfirmList(params)
        tableData.value = applications.items
        pagination.total = applications.totalCount
        
        // 动态更新确认状态过滤选项
        updateConfirmStatusFilterOptions(applications.items)
      } catch (error) {
        ElMessage.error('数据加载失败')
      } finally {
        loading.value = false
      }
    }

    // 处理确认操作
    const handleConfirm = async (row) => {
      try {
        // 使用API获取完整的申请详情
        const detailData = await getMesConfirmDetail(row.id)

        // 根据实际接口返回的数据结构处理
        // detailData 是单个详情对象，包含所有字段
        Object.assign(formData, {
          id: detailData.id,
          person: row.staff_name || '', // 从列表行数据获取
          startDate: detailData.dt_date,
          endDate: detailData.dt_date, // 单日申请，开始和结束日期相同
          reason: row.str_apply_reason || '', // 从列表行数据获取
          remarks: row.str_remark || '', // 从列表行数据获取
          details: [
            {
              id: detailData.id,
              date: detailData.dt_date,
              startTime: detailData.dt_start_time,
              endTime: detailData.dt_end_time,
              appliedHours: detailData.int_apply_hours,
              // 初始化确认字段
              confirmStartTime: detailData.dt_confirm_start || detailData.dt_start_time,
              confirmEndTime: detailData.dt_confirm_end || detailData.dt_end_time,
              confirmHours: detailData.int_confirm_hours || detailData.int_apply_hours,
              confirmStatus: 1,
            },
          ],
        })

        confirmDialogVisible.value = true
      } catch (error) {
        console.error('获取申请详情失败:', error)
      }
    }

    // 处理作废操作
    const handleCancel = async (row) => {
      console.log('作废操作:', row)
      ElMessageBox.confirm('确定作废该申请吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          await cancelMesConfirm(row.id)
          ElMessage.success('作废成功')
          initData()
        })
        .catch(() => {
          console.log('已取消作废')
          // ElMessage.info('已取消作废')
        })
    }

    // 过滤条件
    const handleFilterChange = (filter) => {
      searchForm.value = {}
      filter.filterList.forEach((element) => {
        // 获取过滤值
        let filterValue = null
        
        // 对于原生筛选，值在values数组中
        if (element.values && element.values.length > 0) {
          filterValue = element.values[0]
        }
        
        // 只有当过滤值不为空时才添加到搜索条件中
        if (filterValue !== undefined && filterValue !== null && filterValue !== '') {
          searchForm.value[element.field] = filterValue
        }
      })
      initData()
    }

    // 分页变化处理
    const handlePageChange = () => {
      initData()
    }

    // 使用浏览器缩放检测和视口适配
    const { isZoomed, adaptedDimensions } = useBrowserZoom()

    // 计算表格高度（减去头部和分页器的高度）
    const tableHeight = computed(() => {
      const headerHeight = isZoomed.value ? 80 : 100
      const paginationHeight = isZoomed.value ? 50 : 60
      const padding = isZoomed.value ? 16 : 24
      return adaptedDimensions.value.height - headerHeight - paginationHeight - padding
    })

    // 生命周期
    onMounted(initData)

    return {
      loading,
      tableColumns,
      tableData,
      pagination,
      tableRef,
      tableHeight,
      confirmDialogVisible,
      formData,
      handleConfirm,
      handleCancel,
      handleSaveSuccess,
      handleFilterChange,
      handlePageChange,
    }
  },

  template: /*html*/ `
    <div class="h-screen w-full p-4">
      <!-- 表格容器 -->
      <div class="h-[calc(100vh - 220px)]">
        <HtVxeTable
          v-loading="loading"
          ref="tableRef"
          :table-data="tableData"
          :table-columns="tableColumns"
          :height="tableHeight"
          :remote="true"
          :is-show-header-checkbox="false"
          @filter-change="handleFilterChange"
        >
          <template #operation>
            <vxe-column title="操作" min-width="180" fixed="right" align="center">
              <template #default="{ row }">
                <div class="flex flex-wrap justify-center gap-2">
                  <el-button
                    v-if="row.int_confirm_status === 0"
                    type="primary"
                    size="small"
                    @click="handleConfirm(row)"
                    icon="Check"
                    link
                  >
                    确认
                  </el-button>
                  <el-button type="danger" size="small" @click="handleCancel(row)" icon="Close" link>作废</el-button>
                </div>
              </template>
            </vxe-column>
          </template>
        </HtVxeTable>
      </div>

      <!-- 分页器 -->
      <div class="pt-2">
        <vxe-pager
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="pagination.pageSizes"
          :layout="pagination.layout"
          :loading="loading"
          @page-change="handlePageChange"
        ></vxe-pager>
      </div>

      <!-- 确认对话框 -->
      <MesConfirmDialog v-model:visible="confirmDialogVisible" :form-data="formData" @success="handleSaveSuccess" />
    </div>
  `,
}
