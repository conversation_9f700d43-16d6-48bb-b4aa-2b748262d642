const { h, ref, onMounted } = Vue
const { ElForm, ElFormItem, ElDatePicker, ElSelect, ElOption, ElInput, ElButton } = ElementPlus

// 渲染表单
export function useFromRenderer(getTableData) {
  const searchForm = ref({
    dt_range: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
    str_engine_type: '',
    str_type: '',
  })
  const handleReset = () => {
    searchForm.value = {
      dt_range: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      str_engine_type: '',
      str_type: '',
    }
    handleSearch()
  }
  const handleSearch = () => {
    getTableData(searchForm.value)
  }

  onMounted(() => {
    handleSearch()
  })

  const renderFrom = () => {
    return h(
      ElForm,
      { model: searchForm, inline: true, class: 'mt-2' },
      {
        default: () => [
          h(
            ElFormItem,
            { label: '日期' },
            {
              default: () => [
                h(ElDatePicker, {
                  type: 'daterange',
                  placeholder: '选择日期',
                  modelValue: searchForm.value.dt_range,
                  clearable: true,
                  valueFormat: 'YYYY-MM-DD',
                  format: 'YYYY-MM-DD',
                  'onUpdate:modelValue': (val) => {
                    searchForm.value.dt_range = val
                  },
                }),
              ],
            },
          ),
          h(
            ElFormItem,
            { label: '机型' },
            {
              default: () => [
                h(
                  ElSelect,
                  {
                    placeholder: '选择机型',
                    class: '!w-48',
                    modelValue: searchForm.value.str_engine_type,
                    clearable: true,
                    onChange: (val) => {
                      searchForm.value.str_engine_type = val
                    },
                  },
                  {
                    default: () => [
                      h(ElOption, { value: 'LEAP' }, { default: () => 'LEAP' }),
                      h(ElOption, { value: 'CFM56' }, { default: () => 'CFM56' }),
                    ],
                  },
                ),
              ],
            },
          ),
          h(
            ElFormItem,
            { label: 'TYPE' },
            {
              default: () => [
                h(
                  ElSelect,
                  {
                    class: '!w-48',
                    placeholder: '选择TYPE',
                    modelValue: searchForm.value.str_type,
                    clearable: true,
                    onChange: (val) => {
                      searchForm.value.str_type = val
                    },
                  },
                  {
                    default: () => [
                      h(ElOption, { value: 'B1' }, { default: () => 'B1' }),
                      h(ElOption, { value: 'B23' }, { default: () => 'B23' }),
                    ],
                  },
                ),
              ],
            },
          ),
          // 按钮
          h(ElFormItem, null, {
            default: () => [
              h(
                ElButton,
                {
                  type: 'primary',
                  onClick: () => handleSearch(),
                },
                { default: () => '查询' },
              ),
              h(
                ElButton,
                {
                  type: 'primary',
                  onClick: () => handleReset(),
                },
                { default: () => '重置' },
              ),
            ],
          }),
        ],
      },
    )
  }

  return {
    searchForm,
    renderFrom,
  }
}
