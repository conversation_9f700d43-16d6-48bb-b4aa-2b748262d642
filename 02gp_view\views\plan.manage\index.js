import { useSelection } from '../../hooks/useSelection.js'
import { useTable } from './useTable.js'
import { delProject, activeProjectApi } from '../../api/plan.manage.js'

const { toRefs, defineAsyncComponent, onMounted, ref } = Vue
const PlanCalculation = {
  components: {
    HtVxeTable: defineAsyncComponent(() => import('../../../components/VxeTable/HtVxeTable.js')),
    PagePager: defineAsyncComponent(() => import('../../components/PagePager.js')),
    HtDrawer: defineAsyncComponent(() => import('../../../components/ht.drawer.js')),
    PlanGantt: defineAsyncComponent(() => import('./PlanGantt.js')),
    PlanGanttSattion: defineAsyncComponent(() => import('../../../02gp_view/views/plan.station/plan.gantt.station.js')),
  },
  setup() {
    const { state, handleFilterChange, getTableDataByFrontPage, pagePagerState, handlePageChange, parentFilterList } =
      useTable()
    const { hasSelectedData, hasSelectedOneData } = useSelection()
    const addProjectRef = Vue.ref(null)
    const tableRef = Vue.ref(null)
    const currentState = Vue.reactive({
      id: '',
    })
    const dialogStationVisible = ref(false)
    // 新增Project
    const dialogVisible = ref(false)
    const openAddProjectDialog = () => {
      dialogVisible.value = true
    }
    // 编辑Project
    const openEditProjectDialog = () => {
      const selectedData = hasSelectedData(tableRef, 'getSelectedData')
      const oneSelectedData = hasSelectedOneData(selectedData)
      if (!oneSelectedData) {
        return
      }
      dialogVisible.value = true
      currentState.id = oneSelectedData.id
    }
    // 删除Project
    const removeProject = async () => {
      const selectedData = hasSelectedData(tableRef, 'getSelectedData')
      const ids = selectedData.map((item) => item.id)
      const res = await ElementPlus.ElMessageBox.confirm('是否删除该项目', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      if (res === 'confirm') {
        // 删除操作
        const boolean = await delProject(ids)
        if (boolean) {
          await getTableDataByFrontPage()
        }
      }
    }

    // 启用禁用Project
    const activeProject = async (row) => {
      const res = await ElementPlus.ElMessageBox.confirm('是否确认操作', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      if (res === 'confirm') {
        const boolean = await activeProjectApi(row)
        if (boolean) {
          await getTableDataByFrontPage()
        }
      }
    }

    const planGanttVisible = Vue.ref(false)
    // 打开计划甘特图
    const openPlanGantt = () => {
      const selectedData = hasSelectedData(tableRef, 'getSelectedData')
      const oneSelectedData = hasSelectedOneData(selectedData)
      if (!oneSelectedData) {
        return
      }
      currentState.id = oneSelectedData.id
      planGanttVisible.value = true
    }
    const planGanttRef = Vue.ref(null)
    // 保存计划甘特图
    const savePlanGantt = () => {
      planGanttRef.value.saveGantt()
    }

    const saveProjectDialog = async () => {
      const res = await addProjectRef.value.addAndEditProject()
      if (res) {
        dialogVisible.value = false
        await getTableDataByFrontPage()
      }
    }
    const planGanttStationRef = ref(null)
    // 关闭计划甘特图
    const closePlanGantt = () => {
      if (planGanttRef.value) planGanttRef.value.clearGanttTooltip()
      if (planGanttStationRef.value) planGanttStationRef.value.clearGanttTooltip()
    }

    // 打开站位甘特图
    const openStationDialog = () => {
      dialogStationVisible.value = true
    }
    onMounted(() => {
      getTableDataByFrontPage()
    })
    const isShowSave = ref(true)
    const handleHasSave = (isShow) => {
      isShowSave.value = isShow
    }

    const search = Vue.reactive({
      dt_release_start: '',
      dt_release_end: '',
    })

    const handleSearch = async () => {
      await getTableDataByFrontPage(1, 10)
    }

    const handleReleaseStartChange = (value) => {
      if (value) {
        parentFilterList.value.push({
          str_key: 'dt_release_start',
          str_value: value,
        })
      } else {
        parentFilterList.value = parentFilterList.value.filter((item) => item.str_key !== 'dt_release_start')
      }
    }

    const handleReleaseEndChange = (value) => {
      if (value) {
        parentFilterList.value.push({
          str_key: 'dt_release_end',
          str_value: value,
        })
      } else {
        parentFilterList.value = parentFilterList.value.filter((item) => item.str_key !== 'dt_release_end')
      }
    }

    const handleReset = () => {
      search.dt_release_start = ''
      search.dt_release_end = ''
      // 过滤掉dt_release_start和dt_release_end
      parentFilterList.value = parentFilterList.value.filter(
        (item) => item.str_key !== 'dt_release_start' && item.str_key !== 'dt_release_end',
      )
      handleSearch()
    }

    return {
      ...toRefs(state),
      ...toRefs(pagePagerState),
      tableRef,
      handleFilterChange,
      handlePageChange,
      openAddProjectDialog,
      openEditProjectDialog,
      removeProject,
      dialogVisible,
      openPlanGantt,
      planGanttVisible,
      currentState,
      planGanttRef,
      savePlanGantt,
      addProjectRef,
      saveProjectDialog,
      dialogStationVisible,
      openStationDialog,
      activeProject,
      closePlanGantt,
      planGanttStationRef,
      isShowSave,
      handleHasSave,
      search,
      handleSearch,
      handleReset,
      handleReleaseStartChange,
      handleReleaseEndChange,
    }
  },
  template: /*html*/ `
    <div class="m-2 flex items-center gap-2">
      <label class="el-form-item__label text-right">release开始时间:</label>
      <el-date-picker
        v-model="search.dt_release_start"
        type="date"
        placeholder="请选择release开始时间"
        value-format="YYYY-MM-DD"
        @change="handleReleaseStartChange"
      />
      <label class="el-form-item__label text-right">release结束时间:</label>
      <el-date-picker
        v-model="search.dt_release_end"
        type="date"
        placeholder="请选择release结束时间"
        value-format="YYYY-MM-DD"
        @change="handleReleaseEndChange"
      />
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button type="primary" @click="handleReset">重置</el-button>
      <el-button type="primary" @click="openPlanGantt">计划甘特图</el-button>
      <el-button type="primary" @click="openStationDialog">站位甘特图</el-button>
    </div>
    <div class="mb-2 border-b-2"></div>
    <div class="mx-2" style="height: calc(100vh - 140px);">
      <HtVxeTable ref="tableRef" :tableData :tableColumns :remote="true" @filter-change="handleFilterChange">
        <template #checkbox>
          <vxe-column type="checkbox" width="80" fixed="left"></vxe-column>
        </template>
      </HtVxeTable>
      <div class="my-2 border-b-2"></div>
      <PagePager :currentPage :pageSize :total @pageChange="handlePageChange"></PagePager>
    </div>

    <!--    计划甘特图-->
    <HtDrawer
      v-model:visible="planGanttVisible"
      :isShowSave="isShowSave"
      title="计划甘特图"
      @save="savePlanGantt"
      @clear="closePlanGantt"
    >
      <PlanGantt ref="planGanttRef" :id="currentState.id" @hasSave="handleHasSave"></PlanGantt>
    </HtDrawer>
    <!--    站位甘特图-->
    <HtDrawer v-model:visible="dialogStationVisible" :isShowSave="false" title="计划站位甘特图" @clear="closePlanGantt">
      <PlanGanttSattion ref="planGanttStationRef" :isShowBottomBtn="false"></PlanGanttSattion>
    </HtDrawer>
  `,
}

export default PlanCalculation
