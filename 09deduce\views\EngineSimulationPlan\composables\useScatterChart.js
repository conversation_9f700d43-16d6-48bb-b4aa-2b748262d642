const { ref } = Vue
export function useScatterChart() {
  const flashing = ref(false)
  /**
   * 点位闪烁
   * @param myChart
   * @param {Array} chartData
   */
  const toggleFlashing = (myChart, chartData) => {
    // 当is_ltdate === 1的时候闪烁
    const flashData = chartData.filter((_it) => _it.is_ltdate === 1)
    if (flashData.length > 0) {
      flashing.value = !flashing.value
      const actionType = flashing.value ? 'highlight' : 'downplay'
      flashData.forEach((data) => {
        myChart.dispatchAction({
          type: actionType,
          seriesIndex: [0, 1],
          dataIndex: chartData.indexOf(data),
        })
      })
    }
  }
  return {
    toggleFlashing,
  }
}
