import { post } from '../../config/axios/httpReuest.js';
import { currentDateKey, currentNodeKey } from '../../config/keys.js';
import { useCard } from '../hooks/useCard.js';
import { useTableColumn } from '../hooks/useTableColumn.js';
import ErrorComponent from '../../components/error.component.js';
import LoadingComponent from '../../components/loading.component.js';

const { toRefs, onMounted, inject, watch, defineAsyncComponent, nextTick, reactive } = Vue;
export default {
  name: 'CardComponent',
  components: {
    HtDrawer: defineAsyncComponent({
      loader: () => import('../../components/ht.drawer.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HtVxeTable: defineAsyncComponent({
      loader: () => import('./ht.vxe.table.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  props: {
    pdaNode: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    // 注入当前日期
    const { currentDate } = inject(currentDateKey);
    // 注入当前节点
    const currentNode = inject(currentNodeKey);

    const { cardState, getDailyWorkCardData, cardVisibility } = useCard();
    watch(currentDate, async (newVal) => {
      await getDailyWorkCardData(currentNode, newVal);
    });

    onMounted(async () => {
      await getDailyWorkCardData(currentNode, currentDate.value);
    });

    const { getTableColumn } = useTableColumn();

    const drawerState = reactive({
      isShowDrawer: false,
      tableData: null,
      tableColumn: getTableColumn(currentNode),
      totalNum: 0,
      title: '',
    });
    const openTaskQtyDrawer = (yw_type) => {
      drawerState.tableData = null;
      drawerState.totalNum = 0;
      drawerState.isShowDrawer = true;
      nextTick(() => {
        getTableDataAndTotalNum(yw_type);
      });
    };

    // 获取表格数据
    const getTableDataAndTotalNum = async (yw_type) => {
      const params = {
        ac: 'pda_get_pda_list_pn',
        str_type: currentNode,
        str_ywtype: yw_type,
        filter_fields: [],
        sort_fields: [],
        dt_p0: '',
        dt_date: currentDate.value,
      };
      if (yw_type === 'Card-pc') {
        drawerState.title = 'Task Qty 清单列表';
      } else  if (yw_type === 'Tout') {
        drawerState.title = '当日输出清单列表';
      } else {
        drawerState.title = 'Day inflow 当日流入P(<1) 清单列表';
      }
      const { data } = await post(params);
      if (data.code === 'success') {
        drawerState.tableData = data.data;
        drawerState.totalNum = data.data.length;
      } else {
        ElementPlus.ElMessage.error(data.text);
      }
    };


    return {
      ...toRefs(cardState),
      ...toRefs(drawerState),
      openTaskQtyDrawer,
      getDailyWorkCardData,
      cardVisibility,
    };
  },
  // language=HTML
  template: `
    <div v-if="cardVisibility" class="flex flex-wrap shadow-md p-2 items-center border justify-center">
      <div class="flex-1 grid divide-x divide-solid grid-cols-2 text-amber-500">
        <div class="ml-auto mr-4">Task Qty</div>
        <div class="pl-4 hover:cursor-pointer hover:text-blue-500 underline"
             @click="openTaskQtyDrawer('Card-pc')">{{ int_pcnum}}
        </div>
      </div>
      <div class="flex-2 grid grid-cols-2 divide-x divide-solid text-amber-500">
        <div class="ml-auto mr-4">Work progress</div>
        <div class="pl-4 hover:cursor-pointer hover:text-blue-500 underline"
        @click="openTaskQtyDrawer('Tout')">{{ str_rate }}</div>
      </div>
      <div class="flex-1 grid grid-cols-2 divide-x divide-solid text-amber-500">
        <div class="ml-auto mr-4">P&D(&lt;1)</div>
        <div class="pl-4">{{ int_pnum }}</div>
      </div>
      <div class="flex-1 grid grid-cols-2 divide-x divide-solid text-red-500">
        <div class="ml-auto mr-4">A(All)</div>
        <div class="pl-4">{{ int_anum }}</div>
      </div>
      <div class="flex-1 grid grid-cols-2 divide-x divide-solid text-green-500">
        <div class="ml-auto mr-4">D(All)</div>
        <div class="pl-4">{{ int_dnum }}</div>
      </div>
      <div class="flex-2 grid grid-cols-2 divide-x divide-solid text-green-500">
        <div class="ml-auto mr-4">Day inflow 当日流入P(&lt;1)</div>
        <div class="pl-4 hover:cursor-pointer hover:text-blue-500 underline"
             @click="openTaskQtyDrawer('Card-p')">{{ int_xjnum }}
        </div>
      </div>
    </div>

    <el-skeleton v-else :rows="1" :loading="true" animated></el-skeleton>
    <!--    Task Qty 抽屉-->
    <HtDrawer v-model:visible="isShowDrawer" :title>
      <HtVxeTable :tableData :tableColumn></HtVxeTable>
    </HtDrawer>
  `,
};
