(function(e,t){"object"===typeof exports&&"object"===typeof module?module.exports=t(require("vue"),require("xe-utils")):"function"===typeof define&&define.amd?define([,"xe-utils"],t):"object"===typeof exports?exports["VXETable"]=t(require("vue"),require("xe-utils")):e["VXETable"]=t(e["Vue"],e["XEUtils"])})("undefined"!==typeof self?self:this,(function(e,t){return function(){var o={9662:function(e,t,o){var n=o(614),l=o(6330),r=TypeError;e.exports=function(e){if(n(e))return e;throw r(l(e)+" is not a function")}},9670:function(e,t,o){var n=o(111),l=String,r=TypeError;e.exports=function(e){if(n(e))return e;throw r(l(e)+" is not an object")}},1318:function(e,t,o){var n=o(5656),l=o(1400),r=o(6244),a=function(e){return function(t,o,a){var i,s=n(t),c=r(s),u=l(a,c);if(e&&o!=o){while(c>u)if(i=s[u++],i!=i)return!0}else for(;c>u;u++)if((e||u in s)&&s[u]===o)return e||u||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},3658:function(e,t,o){"use strict";var n=o(9781),l=o(3157),r=TypeError,a=Object.getOwnPropertyDescriptor,i=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=i?function(e,t){if(l(e)&&!a(e,"length").writable)throw r("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},4326:function(e,t,o){var n=o(84),l=n({}.toString),r=n("".slice);e.exports=function(e){return r(l(e),8,-1)}},9920:function(e,t,o){var n=o(2597),l=o(3887),r=o(1236),a=o(3070);e.exports=function(e,t,o){for(var i=l(t),s=a.f,c=r.f,u=0;u<i.length;u++){var d=i[u];n(e,d)||o&&n(o,d)||s(e,d,c(t,d))}}},8880:function(e,t,o){var n=o(9781),l=o(3070),r=o(9114);e.exports=n?function(e,t,o){return l.f(e,t,r(1,o))}:function(e,t,o){return e[t]=o,e}},9114:function(e){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},8052:function(e,t,o){var n=o(614),l=o(3070),r=o(6339),a=o(3072);e.exports=function(e,t,o,i){i||(i={});var s=i.enumerable,c=void 0!==i.name?i.name:t;if(n(o)&&r(o,c,i),i.global)s?e[t]=o:a(t,o);else{try{i.unsafe?e[t]&&(s=!0):delete e[t]}catch(u){}s?e[t]=o:l.f(e,t,{value:o,enumerable:!1,configurable:!i.nonConfigurable,writable:!i.nonWritable})}return e}},3072:function(e,t,o){var n=o(7854),l=Object.defineProperty;e.exports=function(e,t){try{l(n,e,{value:t,configurable:!0,writable:!0})}catch(o){n[e]=t}return t}},5117:function(e,t,o){"use strict";var n=o(6330),l=TypeError;e.exports=function(e,t){if(!delete e[t])throw l("Cannot delete property "+n(t)+" of "+n(e))}},9781:function(e,t,o){var n=o(7293);e.exports=!n((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4154:function(e){var t="object"==typeof document&&document.all,o="undefined"==typeof t&&void 0!==t;e.exports={all:t,IS_HTMLDDA:o}},317:function(e,t,o){var n=o(7854),l=o(111),r=n.document,a=l(r)&&l(r.createElement);e.exports=function(e){return a?r.createElement(e):{}}},7207:function(e){var t=TypeError,o=9007199254740991;e.exports=function(e){if(e>o)throw t("Maximum allowed index exceeded");return e}},8113:function(e,t,o){var n=o(5005);e.exports=n("navigator","userAgent")||""},7392:function(e,t,o){var n,l,r=o(7854),a=o(8113),i=r.process,s=r.Deno,c=i&&i.versions||s&&s.version,u=c&&c.v8;u&&(n=u.split("."),l=n[0]>0&&n[0]<4?1:+(n[0]+n[1])),!l&&a&&(n=a.match(/Edge\/(\d+)/),(!n||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/),n&&(l=+n[1]))),e.exports=l},748:function(e){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},2109:function(e,t,o){var n=o(7854),l=o(1236).f,r=o(8880),a=o(8052),i=o(3072),s=o(9920),c=o(4705);e.exports=function(e,t){var o,u,d,p,f,m,h=e.target,g=e.global,v=e.stat;if(u=g?n:v?n[h]||i(h,{}):(n[h]||{}).prototype,u)for(d in t){if(f=t[d],e.dontCallGetSet?(m=l(u,d),p=m&&m.value):p=u[d],o=c(g?d:h+(v?".":"#")+d,e.forced),!o&&void 0!==p){if(typeof f==typeof p)continue;s(f,p)}(e.sham||p&&p.sham)&&r(f,"sham",!0),a(u,d,f,e)}}},7293:function(e){e.exports=function(e){try{return!!e()}catch(t){return!0}}},4374:function(e,t,o){var n=o(7293);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},6916:function(e,t,o){var n=o(4374),l=Function.prototype.call;e.exports=n?l.bind(l):function(){return l.apply(l,arguments)}},6530:function(e,t,o){var n=o(9781),l=o(2597),r=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,i=l(r,"name"),s=i&&"something"===function(){}.name,c=i&&(!n||n&&a(r,"name").configurable);e.exports={EXISTS:i,PROPER:s,CONFIGURABLE:c}},84:function(e,t,o){var n=o(4374),l=Function.prototype,r=l.call,a=n&&l.bind.bind(r,r);e.exports=n?a:function(e){return function(){return r.apply(e,arguments)}}},1702:function(e,t,o){var n=o(4326),l=o(84);e.exports=function(e){if("Function"===n(e))return l(e)}},5005:function(e,t,o){var n=o(7854),l=o(614),r=function(e){return l(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?r(n[e]):n[e]&&n[e][t]}},8173:function(e,t,o){var n=o(9662),l=o(8554);e.exports=function(e,t){var o=e[t];return l(o)?void 0:n(o)}},7854:function(e,t,o){var n=function(e){return e&&e.Math==Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof o.g&&o.g)||function(){return this}()||Function("return this")()},2597:function(e,t,o){var n=o(1702),l=o(7908),r=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return r(l(e),t)}},3501:function(e){e.exports={}},4664:function(e,t,o){var n=o(9781),l=o(7293),r=o(317);e.exports=!n&&!l((function(){return 7!=Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))},8361:function(e,t,o){var n=o(1702),l=o(7293),r=o(4326),a=Object,i=n("".split);e.exports=l((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"==r(e)?i(e,""):a(e)}:a},2788:function(e,t,o){var n=o(1702),l=o(614),r=o(5465),a=n(Function.toString);l(r.inspectSource)||(r.inspectSource=function(e){return a(e)}),e.exports=r.inspectSource},9909:function(e,t,o){var n,l,r,a=o(4811),i=o(7854),s=o(111),c=o(8880),u=o(2597),d=o(5465),p=o(6200),f=o(3501),m="Object already initialized",h=i.TypeError,g=i.WeakMap,v=function(e){return r(e)?l(e):n(e,{})},x=function(e){return function(t){var o;if(!s(t)||(o=l(t)).type!==e)throw h("Incompatible receiver, "+e+" required");return o}};if(a||d.state){var b=d.state||(d.state=new g);b.get=b.get,b.has=b.has,b.set=b.set,n=function(e,t){if(b.has(e))throw h(m);return t.facade=e,b.set(e,t),t},l=function(e){return b.get(e)||{}},r=function(e){return b.has(e)}}else{var w=p("state");f[w]=!0,n=function(e,t){if(u(e,w))throw h(m);return t.facade=e,c(e,w,t),t},l=function(e){return u(e,w)?e[w]:{}},r=function(e){return u(e,w)}}e.exports={set:n,get:l,has:r,enforce:v,getterFor:x}},3157:function(e,t,o){var n=o(4326);e.exports=Array.isArray||function(e){return"Array"==n(e)}},614:function(e,t,o){var n=o(4154),l=n.all;e.exports=n.IS_HTMLDDA?function(e){return"function"==typeof e||e===l}:function(e){return"function"==typeof e}},4705:function(e,t,o){var n=o(7293),l=o(614),r=/#|\.prototype\./,a=function(e,t){var o=s[i(e)];return o==u||o!=c&&(l(t)?n(t):!!t)},i=a.normalize=function(e){return String(e).replace(r,".").toLowerCase()},s=a.data={},c=a.NATIVE="N",u=a.POLYFILL="P";e.exports=a},8554:function(e){e.exports=function(e){return null===e||void 0===e}},111:function(e,t,o){var n=o(614),l=o(4154),r=l.all;e.exports=l.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:n(e)||e===r}:function(e){return"object"==typeof e?null!==e:n(e)}},1913:function(e){e.exports=!1},2190:function(e,t,o){var n=o(5005),l=o(614),r=o(7976),a=o(3307),i=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return l(t)&&r(t.prototype,i(e))}},6244:function(e,t,o){var n=o(7466);e.exports=function(e){return n(e.length)}},6339:function(e,t,o){var n=o(7293),l=o(614),r=o(2597),a=o(9781),i=o(6530).CONFIGURABLE,s=o(2788),c=o(9909),u=c.enforce,d=c.get,p=Object.defineProperty,f=a&&!n((function(){return 8!==p((function(){}),"length",{value:8}).length})),m=String(String).split("String"),h=e.exports=function(e,t,o){"Symbol("===String(t).slice(0,7)&&(t="["+String(t).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),o&&o.getter&&(t="get "+t),o&&o.setter&&(t="set "+t),(!r(e,"name")||i&&e.name!==t)&&(a?p(e,"name",{value:t,configurable:!0}):e.name=t),f&&o&&r(o,"arity")&&e.length!==o.arity&&p(e,"length",{value:o.arity});try{o&&r(o,"constructor")&&o.constructor?a&&p(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(l){}var n=u(e);return r(n,"source")||(n.source=m.join("string"==typeof t?t:"")),e};Function.prototype.toString=h((function(){return l(this)&&d(this).source||s(this)}),"toString")},4758:function(e){var t=Math.ceil,o=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?o:t)(n)}},3070:function(e,t,o){var n=o(9781),l=o(4664),r=o(3353),a=o(9670),i=o(4948),s=TypeError,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,d="enumerable",p="configurable",f="writable";t.f=n?r?function(e,t,o){if(a(e),t=i(t),a(o),"function"===typeof e&&"prototype"===t&&"value"in o&&f in o&&!o[f]){var n=u(e,t);n&&n[f]&&(e[t]=o.value,o={configurable:p in o?o[p]:n[p],enumerable:d in o?o[d]:n[d],writable:!1})}return c(e,t,o)}:c:function(e,t,o){if(a(e),t=i(t),a(o),l)try{return c(e,t,o)}catch(n){}if("get"in o||"set"in o)throw s("Accessors not supported");return"value"in o&&(e[t]=o.value),e}},1236:function(e,t,o){var n=o(9781),l=o(6916),r=o(5296),a=o(9114),i=o(5656),s=o(4948),c=o(2597),u=o(4664),d=Object.getOwnPropertyDescriptor;t.f=n?d:function(e,t){if(e=i(e),t=s(t),u)try{return d(e,t)}catch(o){}if(c(e,t))return a(!l(r.f,e,t),e[t])}},8006:function(e,t,o){var n=o(6324),l=o(748),r=l.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,r)}},5181:function(e,t){t.f=Object.getOwnPropertySymbols},7976:function(e,t,o){var n=o(1702);e.exports=n({}.isPrototypeOf)},6324:function(e,t,o){var n=o(1702),l=o(2597),r=o(5656),a=o(1318).indexOf,i=o(3501),s=n([].push);e.exports=function(e,t){var o,n=r(e),c=0,u=[];for(o in n)!l(i,o)&&l(n,o)&&s(u,o);while(t.length>c)l(n,o=t[c++])&&(~a(u,o)||s(u,o));return u}},5296:function(e,t){"use strict";var o={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,l=n&&!o.call({1:2},1);t.f=l?function(e){var t=n(this,e);return!!t&&t.enumerable}:o},2140:function(e,t,o){var n=o(6916),l=o(614),r=o(111),a=TypeError;e.exports=function(e,t){var o,i;if("string"===t&&l(o=e.toString)&&!r(i=n(o,e)))return i;if(l(o=e.valueOf)&&!r(i=n(o,e)))return i;if("string"!==t&&l(o=e.toString)&&!r(i=n(o,e)))return i;throw a("Can't convert object to primitive value")}},3887:function(e,t,o){var n=o(5005),l=o(1702),r=o(8006),a=o(5181),i=o(9670),s=l([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=r.f(i(e)),o=a.f;return o?s(t,o(e)):t}},4488:function(e,t,o){var n=o(8554),l=TypeError;e.exports=function(e){if(n(e))throw l("Can't call method on "+e);return e}},6200:function(e,t,o){var n=o(2309),l=o(9711),r=n("keys");e.exports=function(e){return r[e]||(r[e]=l(e))}},5465:function(e,t,o){var n=o(7854),l=o(3072),r="__core-js_shared__",a=n[r]||l(r,{});e.exports=a},2309:function(e,t,o){var n=o(1913),l=o(5465);(e.exports=function(e,t){return l[e]||(l[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.26.0",mode:n?"pure":"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.26.0/LICENSE",source:"https://github.com/zloirock/core-js"})},6293:function(e,t,o){var n=o(7392),l=o(7293);e.exports=!!Object.getOwnPropertySymbols&&!l((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},1400:function(e,t,o){var n=o(9303),l=Math.max,r=Math.min;e.exports=function(e,t){var o=n(e);return o<0?l(o+t,0):r(o,t)}},5656:function(e,t,o){var n=o(8361),l=o(4488);e.exports=function(e){return n(l(e))}},9303:function(e,t,o){var n=o(4758);e.exports=function(e){var t=+e;return t!==t||0===t?0:n(t)}},7466:function(e,t,o){var n=o(9303),l=Math.min;e.exports=function(e){return e>0?l(n(e),9007199254740991):0}},7908:function(e,t,o){var n=o(4488),l=Object;e.exports=function(e){return l(n(e))}},7593:function(e,t,o){var n=o(6916),l=o(111),r=o(2190),a=o(8173),i=o(2140),s=o(5112),c=TypeError,u=s("toPrimitive");e.exports=function(e,t){if(!l(e)||r(e))return e;var o,s=a(e,u);if(s){if(void 0===t&&(t="default"),o=n(s,e,t),!l(o)||r(o))return o;throw c("Can't convert object to primitive value")}return void 0===t&&(t="number"),i(e,t)}},4948:function(e,t,o){var n=o(7593),l=o(2190);e.exports=function(e){var t=n(e,"string");return l(t)?t:t+""}},6330:function(e){var t=String;e.exports=function(e){try{return t(e)}catch(o){return"Object"}}},9711:function(e,t,o){var n=o(1702),l=0,r=Math.random(),a=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++l+r,36)}},3307:function(e,t,o){var n=o(6293);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},3353:function(e,t,o){var n=o(9781),l=o(7293);e.exports=n&&l((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},4811:function(e,t,o){var n=o(7854),l=o(614),r=n.WeakMap;e.exports=l(r)&&/native code/.test(String(r))},5112:function(e,t,o){var n=o(7854),l=o(2309),r=o(2597),a=o(9711),i=o(6293),s=o(3307),c=l("wks"),u=n.Symbol,d=u&&u["for"],p=s?u:u&&u.withoutSetter||a;e.exports=function(e){if(!r(c,e)||!i&&"string"!=typeof c[e]){var t="Symbol."+e;i&&r(u,e)?c[e]=u[e]:c[e]=s&&d?d(t):p(t)}return c[e]}},7658:function(e,t,o){"use strict";var n=o(2109),l=o(7908),r=o(6244),a=o(3658),i=o(7207),s=o(7293),c=s((function(){return 4294967297!==[].push.call({length:4294967296},1)})),u=!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}();n({target:"Array",proto:!0,arity:1,forced:c||u},{push:function(e){var t=l(this),o=r(t),n=arguments.length;i(o+n);for(var s=0;s<n;s++)t[o]=arguments[s],o++;return a(t,o),o}})},541:function(e,t,o){"use strict";var n=o(2109),l=o(7908),r=o(6244),a=o(3658),i=o(5117),s=o(7207),c=1!==[].unshift(0),u=!function(){try{Object.defineProperty([],"length",{writable:!1}).unshift()}catch(e){return e instanceof TypeError}}();n({target:"Array",proto:!0,arity:1,forced:c||u},{unshift:function(e){var t=l(this),o=r(t),n=arguments.length;if(n){s(o+n);var c=o;while(c--){var u=c+n;c in t?t[u]=t[c]:i(t,u)}for(var d=0;d<n;d++)t[d]=arguments[d]}return a(t,o+n)}})},7203:function(t){"use strict";t.exports=e},4980:function(e){"use strict";e.exports=t}},n={};function l(e){var t=n[e];if(void 0!==t)return t.exports;var r=n[e]={exports:{}};return o[e](r,r.exports,l),r.exports}!function(){l.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return l.d(t,{a:t}),t}}(),function(){l.d=function(e,t){for(var o in t)l.o(t,o)&&!l.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}}(),function(){l.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){l.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){l.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){l.p=""}();var r={};return function(){"use strict";l.r(r),l.d(r,{Button:function(){return Zl},Checkbox:function(){return Fl},CheckboxGroup:function(){return Al},Colgroup:function(){return vl},Column:function(){return ml},Edit:function(){return Do},Export:function(){return Wn},Filter:function(){return mo},Form:function(){return Ir},FormGather:function(){return Vr},FormItem:function(){return Lr},Grid:function(){return Sl},Header:function(){return ya},Icon:function(){return ll},Input:function(){return ql},Keyboard:function(){return Gn},List:function(){return da},Menu:function(){return Ro},Modal:function(){return sr},Optgroup:function(){return Gr},Option:function(){return Jr},Pager:function(){return Il},Pulldown:function(){return ma},Radio:function(){return Vl},RadioButton:function(){return zl},RadioGroup:function(){return jl},Select:function(){return jr},Switch:function(){return ta},Table:function(){return Aa},Textarea:function(){return Gl},Toolbar:function(){return Ol},Tooltip:function(){return dr},VXETable:function(){return oo},Validator:function(){return tl},VxeButton:function(){return Kl},VxeCheckbox:function(){return Dl},VxeCheckboxGroup:function(){return Ll},VxeColgroup:function(){return gl},VxeColumn:function(){return fl},VxeForm:function(){return $r},VxeFormGather:function(){return Pr},VxeFormItem:function(){return Nr},VxeGrid:function(){return El},VxeIcon:function(){return nl},VxeInput:function(){return Wl},VxeList:function(){return ua},VxeModal:function(){return ir},VxeModuleEdit:function(){return Io},VxeModuleExport:function(){return zn},VxeModuleFilter:function(){return fo},VxeModuleKeyboard:function(){return Yn},VxeModuleMenu:function(){return ko},VxeModuleValidator:function(){return el},VxeOptgroup:function(){return Yr},VxeOption:function(){return Zr},VxePager:function(){return $l},VxePulldown:function(){return fa},VxeRadio:function(){return Pl},VxeRadioButton:function(){return Bl},VxeRadioGroup:function(){return _l},VxeSelect:function(){return _r},VxeSwitch:function(){return ea},VxeTable:function(){return La},VxeTextarea:function(){return Yl},VxeToolbar:function(){return Rl},VxeTooltip:function(){return ur},_t:function(){return Kt},commands:function(){return Vt},config:function(){return qt},default:function(){return Ha},formats:function(){return b},globalConfs:function(){return Jt},globalStore:function(){return to},hooks:function(){return Wt},install:function(){return _a},interceptor:function(){return h},menus:function(){return jt},modal:function(){return ar},print:function(){return Bn},readFile:function(){return dn},renderer:function(){return At},saveFile:function(){return gn},setup:function(){return eo},t:function(){return Gt},use:function(){return Yt},v:function(){return Qt},validators:function(){return zt}});var e={};if(l.r(e),l.d(e,{Button:function(){return Zl},Checkbox:function(){return Fl},CheckboxGroup:function(){return Al},Colgroup:function(){return vl},Column:function(){return ml},Edit:function(){return Do},Export:function(){return Wn},Filter:function(){return mo},Form:function(){return Ir},FormGather:function(){return Vr},FormItem:function(){return Lr},Grid:function(){return Sl},Header:function(){return ya},Icon:function(){return ll},Input:function(){return ql},Keyboard:function(){return Gn},List:function(){return da},Menu:function(){return Ro},Modal:function(){return sr},Optgroup:function(){return Gr},Option:function(){return Jr},Pager:function(){return Il},Pulldown:function(){return ma},Radio:function(){return Vl},RadioButton:function(){return zl},RadioGroup:function(){return jl},Select:function(){return jr},Switch:function(){return ta},Table:function(){return Aa},Textarea:function(){return Gl},Toolbar:function(){return Ol},Tooltip:function(){return dr},VXETable:function(){return oo},Validator:function(){return tl},VxeButton:function(){return Kl},VxeCheckbox:function(){return Dl},VxeCheckboxGroup:function(){return Ll},VxeColgroup:function(){return gl},VxeColumn:function(){return fl},VxeForm:function(){return $r},VxeFormGather:function(){return Pr},VxeFormItem:function(){return Nr},VxeGrid:function(){return El},VxeIcon:function(){return nl},VxeInput:function(){return Wl},VxeList:function(){return ua},VxeModal:function(){return ir},VxeModuleEdit:function(){return Io},VxeModuleExport:function(){return zn},VxeModuleFilter:function(){return fo},VxeModuleKeyboard:function(){return Yn},VxeModuleMenu:function(){return ko},VxeModuleValidator:function(){return el},VxeOptgroup:function(){return Yr},VxeOption:function(){return Zr},VxePager:function(){return $l},VxePulldown:function(){return fa},VxeRadio:function(){return Pl},VxeRadioButton:function(){return Bl},VxeRadioGroup:function(){return _l},VxeSelect:function(){return _r},VxeSwitch:function(){return ea},VxeTable:function(){return La},VxeTextarea:function(){return Yl},VxeToolbar:function(){return Rl},VxeTooltip:function(){return ur},_t:function(){return Kt},commands:function(){return Vt},config:function(){return qt},formats:function(){return b},globalConfs:function(){return Jt},globalStore:function(){return to},hooks:function(){return Wt},install:function(){return _a},interceptor:function(){return h},menus:function(){return jt},modal:function(){return ar},print:function(){return Bn},readFile:function(){return dn},renderer:function(){return At},saveFile:function(){return gn},setup:function(){return eo},t:function(){return Gt},use:function(){return Yt},v:function(){return Qt},validators:function(){return zt}}),"undefined"!==typeof window){var t=window.document.currentScript,o=t&&t.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);o&&(l.p=o[1])}var n=l(4980),a=l.n(n);l(7658);const i="vxe-icon-",s={size:null,zIndex:999,version:0,emptyCell:"　",table:{fit:!0,showHeader:!0,animat:!0,delayHover:250,autoResize:!0,minHeight:144,resizeConfig:{refreshDelay:250},radioConfig:{strict:!0},checkboxConfig:{strict:!0},tooltipConfig:{enterable:!0},validConfig:{showMessage:!0,autoClear:!0,message:"inline",msgMode:"single"},columnConfig:{maxFixedSize:4},sortConfig:{showIcon:!0},filterConfig:{showIcon:!0},treeConfig:{rowField:"id",parentField:"parentId",childrenField:"children",hasChildField:"hasChild",mapChildrenField:"_X_ROW_CHILD",indent:20,showIcon:!0},expandConfig:{showIcon:!0},editConfig:{showIcon:!0,showAsterisk:!0},importConfig:{modes:["insert","covering"]},exportConfig:{modes:["current","selected"]},printConfig:{modes:["current","selected"]},mouseConfig:{extension:!0},keyboardConfig:{isEsc:!0},areaConfig:{selectCellByHeader:!0},clipConfig:{isCopy:!0,isCut:!0,isPaste:!0},fnrConfig:{isFind:!0,isReplace:!0},scrollX:{gt:60},scrollY:{gt:100}},export:{types:{}},icon:{LOADING:i+"spinner roll vxe-loading--default-icon",TABLE_SORT_ASC:i+"caret-up",TABLE_SORT_DESC:i+"caret-down",TABLE_FILTER_NONE:i+"funnel",TABLE_FILTER_MATCH:i+"funnel",TABLE_EDIT:i+"edit",TABLE_HELP:i+"question-circle-fill",TABLE_TREE_LOADED:i+"spinner roll",TABLE_TREE_OPEN:i+"caret-right rotate90",TABLE_TREE_CLOSE:i+"caret-right",TABLE_EXPAND_LOADED:i+"spinner roll",TABLE_EXPAND_OPEN:i+"arrow-right rotate90",TABLE_EXPAND_CLOSE:i+"arrow-right",TABLE_CHECKBOX_CHECKED:i+"checkbox-checked",TABLE_CHECKBOX_UNCHECKED:i+"checkbox-unchecked",TABLE_CHECKBOX_INDETERMINATE:i+"checkbox-indeterminate",TABLE_RADIO_CHECKED:i+"radio-checked",TABLE_RADIO_UNCHECKED:i+"radio-unchecked",BUTTON_DROPDOWN:i+"arrow-down",BUTTON_LOADING:i+"spinner roll",SELECT_LOADED:i+"spinner roll",SELECT_OPEN:i+"caret-down rotate180",SELECT_CLOSE:i+"caret-down",PAGER_JUMP_PREV:i+"arrow-double-left",PAGER_JUMP_NEXT:i+"arrow-double-right",PAGER_PREV_PAGE:i+"arrow-left",PAGER_NEXT_PAGE:i+"arrow-right",PAGER_JUMP_MORE:i+"ellipsis-h",INPUT_CLEAR:i+"error-circle-fill",INPUT_PWD:i+"eye-fill",INPUT_SHOW_PWD:i+"eye-fill-close",INPUT_PREV_NUM:i+"caret-up",INPUT_NEXT_NUM:i+"caret-down",INPUT_DATE:i+"calendar",INPUT_SEARCH:i+"search",MODAL_ZOOM_IN:i+"square",MODAL_ZOOM_OUT:i+"maximize",MODAL_CLOSE:i+"close",MODAL_INFO:i+"info-circle-fill",MODAL_SUCCESS:i+"success-circle-fill",MODAL_WARNING:i+"warnion-circle-fill",MODAL_ERROR:i+"error-circle-fill",MODAL_QUESTION:i+"question-circle-fill",MODAL_LOADING:i+"spinner roll",TOOLBAR_TOOLS_REFRESH:i+"repeat",TOOLBAR_TOOLS_REFRESH_LOADING:i+"repeat roll",TOOLBAR_TOOLS_IMPORT:i+"upload",TOOLBAR_TOOLS_EXPORT:i+"download",TOOLBAR_TOOLS_PRINT:i+"print",TOOLBAR_TOOLS_FULLSCREEN:i+"fullscreen",TOOLBAR_TOOLS_MINIMIZE:i+"minimize",TOOLBAR_TOOLS_CUSTOM:i+"custom-column",TOOLBAR_TOOLS_FIXED_LEFT:i+"fixed-left",TOOLBAR_TOOLS_FIXED_LEFT_ACTIVED:i+"fixed-left-fill",TOOLBAR_TOOLS_FIXED_RIGHT:i+"fixed-right",TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVED:i+"fixed-right-fill",FORM_PREFIX:i+"question-circle-fill",FORM_SUFFIX:i+"question-circle-fill",FORM_FOLDING:i+"arrow-up rotate180",FORM_UNFOLDING:i+"arrow-up"},grid:{formConfig:{enabled:!0},pagerConfig:{enabled:!0},toolbarConfig:{enabled:!0},proxyConfig:{enabled:!0,autoLoad:!0,message:!0,props:{list:null,result:"result",total:"page.total",message:"message"}}},tooltip:{trigger:"hover",theme:"dark",enterDelay:500,leaveDelay:300},pager:{},form:{validConfig:{showMessage:!0,autoPos:!0},tooltipConfig:{enterable:!0},titleAsterisk:!0},input:{startDate:new Date(1900,0,1),endDate:new Date(2100,0,1),startDay:1,selectDay:1,digits:2,controls:!0},textarea:{},select:{multiCharOverflow:8},toolbar:{custom:{allowFixed:!0,showFooter:!0}},button:{},radio:{strict:!0},radioButton:{strict:!0},radioGroup:{strict:!0},checkbox:{},switch:{},modal:{top:15,showHeader:!0,minWidth:340,minHeight:140,lockView:!0,mask:!0,duration:3e3,marginSize:0,dblclickZoom:!0,showTitleOverflow:!0,animat:!0,showClose:!0,draggable:!0,storageKey:"VXE_MODAL_POSITION"},list:{scrollY:{enabled:!0,gt:100}},i18n:e=>e};var c=s;function u(e,t){return`[vxe-table v4.5.18] ${c.i18n(e,t)}`}function d(e){return function(t,o){const n=u(t,o);return console[e](n),n}}const p=d("warn"),f=d("error"),m={},h={mixin(e){return a().each(e,((e,t)=>h.add(t,e))),h},get(e){return m[e]||[]},add(e,t){{const t=["created","mounted","activated","beforeUnmount","unmounted","event.clearActived","event.clearFilter","event.clearAreas","event.showMenu","event.keydown","event.export","event.import"];-1===t.indexOf(e)&&p("vxe.error.errProp",[`Interceptor.${e}`,t.join("|")])}if(t){let o=m[e];o||(o=m[e]=[]),o.indexOf(t)>-1&&p("vxe.error.coverProp",["Interceptor",e]),o.push(t)}return h},delete(e,t){const o=m[e];o&&(t?a().remove(o,(e=>e===t)):delete m[e])}};var g=l(7203);function v(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class x{constructor(){v(this,"store",{})}mixin(e){return a().each(e,((e,t)=>{this.add(t,e)})),this}has(e){return!!this.get(e)}get(e){return this.store[e]}add(e,t){const o=this.store[e];a().isFunction(t)&&(p("vxe.error.delProp",["formats -> callback","cellFormatMethod"]),t={cellFormatMethod:t});{const n=a().keys(o);a().each(t,((t,o)=>{n.includes(o)&&p("vxe.error.coverProp",[e,o])}))}return this.store[e]=o?a().merge(o,t):t,this}delete(e){delete this.store[e]}forEach(e){a().objectEach(this.store,e)}}const b=new x;Object.assign(b,{_name:"Formats"});var w=null,C="z-index-manage",y="m",T="s",E={m:1e3,s:1e3};function S(){return w||"undefined"!==typeof document&&(w=document.getElementById(C),w||(w=document.createElement("div"),w.id=C,w.style.display="none",document.body.appendChild(w),R(E.m),I(E.s))),w}function k(e){return function(t){if(t){t=Number(t),E[e]=t;var o=S();o&&(o.dataset?o.dataset[e]=t+"":o.setAttribute("data-"+e,t+""))}return E[e]}}var R=k(y);function O(e,t){return function(o){var n,l=S();if(l){var r=l.dataset?l.dataset[e]:l.getAttribute("data-"+e);r&&(n=Number(r))}return n||(n=E[e]),o?Number(o)<n?t():o:n}}var M=O(y,$);function $(){return R(M()+1)}var I=k(T),D=O(T,N);function F(){return M()+D()}function N(){return I(D()+1),F()}var L={setCurrent:R,getCurrent:M,getNext:$,setSubCurrent:I,getSubCurrent:F,getSubNext:N},A=L;function P(e){return e&&!1!==e.enabled}function V(e){return null===e||void 0===e||""===e}function _(e){const t=e.name,o=a().lastIndexOf(t,"."),n=t.substring(o+1,t.length).toLowerCase(),l=t.substring(0,o);return{filename:l,type:n}}function j(){return A.getNext()}function H(){return A.getCurrent()}function B(e){return e&&e.children&&e.children.length>0}function z(e){return e?a().toValueString(c.translate?c.translate(""+e):e):""}function W(e,t){return""+(V(e)?t?c.emptyCell:"":e)}function q(e){return""===e||a().eqNull(e)}class U{constructor(e,t,{renderHeader:o,renderCell:n,renderFooter:l,renderData:r}={}){v(this,"title",void 0),v(this,"type",void 0),v(this,"field",void 0);const i=e.xegrid,s=t.formatter,c=!a().isBoolean(t.visible)||t.visible;{const o=["seq","checkbox","radio","expand","html"];if(t.type&&-1===o.indexOf(t.type)&&p("vxe.error.errProp",[`type=${t.type}`,o.join(", ")]),(a().isBoolean(t.cellRender)||t.cellRender&&!a().isObject(t.cellRender))&&p("vxe.error.errProp",[`column.cell-render=${t.cellRender}`,"column.cell-render={}"]),(a().isBoolean(t.editRender)||t.editRender&&!a().isObject(t.editRender))&&p("vxe.error.errProp",[`column.edit-render=${t.editRender}`,"column.edit-render={}"]),t.cellRender&&t.editRender&&p("vxe.error.errConflicts",["column.cell-render","column.edit-render"]),"expand"===t.type){const{props:t}=e,{treeConfig:o}=t,{computeTreeOpts:n}=e.getComputeMaps(),l=n.value;o&&(l.showLine||l.line)&&f("vxe.error.errConflicts",["tree-config.showLine","column.type=expand"])}if(s)if(a().isString(s)){const e=b.get(s)||a()[s];e&&a().isFunction(e.cellFormatMethod)||f("vxe.error.notFormats",[s])}else if(a().isArray(s)){const e=b.get(s[0])||a()[s[0]];e&&a().isFunction(e.cellFormatMethod)||f("vxe.error.notFormats",[s[0]])}}if(Object.assign(this,{type:t.type,property:t.field,field:t.field,title:t.title,width:t.width,minWidth:t.minWidth,maxWidth:t.maxWidth,resizable:t.resizable,fixed:t.fixed,align:t.align,headerAlign:t.headerAlign,footerAlign:t.footerAlign,showOverflow:t.showOverflow,showHeaderOverflow:t.showHeaderOverflow,showFooterOverflow:t.showFooterOverflow,className:t.className,headerClassName:t.headerClassName,footerClassName:t.footerClassName,formatter:s,sortable:t.sortable,sortBy:t.sortBy,sortType:t.sortType,filters:Re(t.filters),filterMultiple:!a().isBoolean(t.filterMultiple)||t.filterMultiple,filterMethod:t.filterMethod,filterResetMethod:t.filterResetMethod,filterRecoverMethod:t.filterRecoverMethod,filterRender:t.filterRender,treeNode:t.treeNode,cellType:t.cellType,cellRender:t.cellRender,editRender:t.editRender,contentRender:t.contentRender,headerExportMethod:t.headerExportMethod,exportMethod:t.exportMethod,footerExportMethod:t.footerExportMethod,titleHelp:t.titleHelp,titlePrefix:t.titlePrefix,params:t.params,id:t.colId||a().uniqueId("col_"),parentId:null,visible:c,halfVisible:!1,defaultVisible:c,defaultFixed:t.fixed,checked:!1,halfChecked:!1,disabled:!1,level:1,rowSpan:1,colSpan:1,order:null,sortTime:0,customOrder:0,renderWidth:0,renderHeight:0,resizeWidth:0,renderLeft:0,renderArgs:[],model:{},renderHeader:o||t.renderHeader,renderCell:n||t.renderCell,renderFooter:l||t.renderFooter,renderData:r,slots:t.slots}),i){const{computeProxyOpts:e}=i.getComputeMaps(),t=e.value;t.beforeColumn&&t.beforeColumn({$grid:i,column:this})}}getTitle(){return z(this.title||("seq"===this.type?c.i18n("vxe.table.seqTitle"):""))}getKey(){return this.field||(this.type?`type=${this.type}`:null)}update(e,t){"filters"!==e&&("field"===e&&(this.property=t),this[e]=t)}}const X={},Y=a().browse();function G(e,t){return e?a().isFunction(e)?e(t):e:""}function K(e){return X[e]||(X[e]=new RegExp(`(?:^|\\s)${e}(?!\\S)`,"g")),X[e]}function Z(e,t,o){if(e){const n=e.parentNode;if(o.top+=e.offsetTop,o.left+=e.offsetLeft,n&&n!==document.documentElement&&n!==document.body&&(o.top-=n.scrollTop,o.left-=n.scrollLeft),(!t||e!==t&&e.offsetParent!==t)&&e.offsetParent)return Z(e.offsetParent,t,o)}return o}function J(e){return e&&/^\d+(px)?$/.test(e)}function Q(e){return e&&/^\d+%$/.test(e)}function ee(e,t){return e&&e.className&&e.className.match&&e.className.match(K(t))}function te(e,t){e&&ee(e,t)&&(e.className=e.className.replace(K(t),""))}function oe(e,t){e&&!ee(e,t)&&(te(e,t),e.className=`${e.className} ${t}`)}function ne(){const e=document.documentElement,t=document.body;return{scrollTop:e.scrollTop||t.scrollTop,scrollLeft:e.scrollLeft||t.scrollLeft,visibleHeight:e.clientHeight||t.clientHeight,visibleWidth:e.clientWidth||t.clientWidth}}function le(e){return e?e.offsetHeight:0}function re(e){if(e){const t=getComputedStyle(e),o=a().toNumber(t.paddingTop),n=a().toNumber(t.paddingBottom);return o+n}return 0}function ae(e,t){e&&(e.scrollTop=t)}function ie(e,t){e&&(e.scrollLeft=t)}function se(e,t){const o="html"===t.type?e.innerText:e.textContent;e.getAttribute("title")!==o&&e.setAttribute("title",o)}function ce(e,t,o,n){let l,r=e.target.shadowRoot&&e.composed&&e.composedPath()[0]||e.target;while(r&&r.nodeType&&r!==document){if(o&&ee(r,o)&&(!n||n(r)))l=r;else if(r===t)return{flag:!o||!!l,container:t,targetElem:l};r=r.parentNode}return{flag:!1}}function ue(e,t){return Z(e,t,{left:0,top:0})}function de(e){const t=e.getBoundingClientRect(),o=t.top,n=t.left,{scrollTop:l,scrollLeft:r,visibleHeight:a,visibleWidth:i}=ne();return{boundingTop:o,top:l+o,boundingLeft:n,left:r+n,visibleHeight:a,visibleWidth:i}}const pe="scrollIntoViewIfNeeded",fe="scrollIntoView";function me(e){e&&(e[pe]?e[pe]():e[fe]&&e[fe]())}function he(e,t){e&&e.dispatchEvent(new Event(t))}function ge(e){return e&&1===e.nodeType}function ve(e,t,o){const{internalData:n}=e;return e.clearScroll().then((()=>{if(t||o)return n.lastScrollLeft=0,n.lastScrollTop=0,e.scrollTo(t,o)}))}function xe(e){e&&e._onscroll&&(e.onscroll=null)}function be(e){e&&e._onscroll&&(e.onscroll=e._onscroll)}function we(){return a().uniqueId("row_")}function Ce(e){const{props:t}=e,{computeRowOpts:o}=e.getComputeMaps(),{rowId:n}=t,l=o.value;return n||l.keyField||"_X_ROW_KEY"}function ye(e,t){const o=a().get(t,Ce(e));return a().eqNull(o)?"":encodeURIComponent(o)}const Te=(e,t)=>t?a().isString(t)?e.getColumnByField(t):t:null;function Ee(e){if(e){const t=getComputedStyle(e),o=a().toNumber(t.paddingLeft),n=a().toNumber(t.paddingRight);return o+n}return 0}function Se(e){if(e){const t=getComputedStyle(e),o=a().toNumber(t.marginLeft),n=a().toNumber(t.marginRight);return e.offsetWidth+o+n}return 0}function ke(e,t){return e.querySelector(".vxe-cell"+t)}function Re(e){return e&&a().isArray(e)?e.map((({label:e,value:t,data:o,resetValue:n,checked:l})=>({label:e,value:t,data:o,resetValue:n,checked:!!l,_checked:!!l}))):e}function Oe(e){return e.map(((e,t)=>t%2===0?Number(e)+1:".")).join("")}function Me(e,t){return a().get(e,t.field)}function $e(e,t,o){return a().set(e,t.field,o)}function Ie(e){const{$table:t,column:o,cell:n}=e,{props:l}=t,{computeResizableOpts:r}=t.getComputeMaps(),i=r.value,{minWidth:s}=i;if(s){const t=a().isFunction(s)?s(e):s;if("auto"!==t)return Math.max(1,a().toNumber(t))}const{showHeaderOverflow:c}=l,{showHeaderOverflow:u,minWidth:d}=o,p=a().isUndefined(u)||a().isNull(u)?c:u,f="ellipsis"===p,m="title"===p,h=!0===p||"tooltip"===p,g=m||h||f,v=a().floor(1.6*(a().toNumber(getComputedStyle(n).fontSize)||14)),x=Ee(n)+Ee(ke(n,""));let b=v+x;if(g){const e=Ee(ke(n,"--title>.vxe-cell--checkbox")),t=Se(ke(n,">.vxe-cell--required-icon")),o=Se(ke(n,">.vxe-cell--edit-icon")),l=Se(ke(n,">.vxe-cell-help-icon")),r=Se(ke(n,">.vxe-cell--sort")),a=Se(ke(n,">.vxe-cell--filter"));b+=e+t+o+l+a+r}if(d){const{refTableBody:e}=t.getRefMaps(),o=e.value,n=o?o.$el:null;if(n){if(Q(d)){const e=n.clientWidth-1,t=e/100;return Math.max(b,Math.floor(a().toInteger(d)*t))}if(J(d))return Math.max(b,a().toInteger(d))}}return b}function De(e){return e&&(e.constructor===U||e instanceof U)}function Fe(e,t,o){return De(t)?t:(0,g.reactive)(new U(e,t,o))}function Ne(e,t,o){Object.keys(t).forEach((n=>{(0,g.watch)((()=>t[n]),(t=>{o.update(n,t),e&&("filters"===n?(e.setFilter(o,t),e.handleUpdateDataQueue()):["visible","fixed","width","minWidth","maxWidth"].includes(n)&&e.handleRefreshColumnQueue())}))}))}function Le(e,t,o,n){const{reactData:l}=e,{staticColumns:r}=l,i=t.parentNode,s=n?n.column:null,c=s?s.children:r;i&&c&&(c.splice(a().arrayIndexOf(i.children,t),0,o),l.staticColumns=r.slice(0))}function Ae(e,t){const{reactData:o}=e,{staticColumns:n}=o,l=a().findTree(n,(e=>e.id===t.id),{children:"children"});l&&l.items.splice(l.index,1),o.staticColumns=n.slice(0)}function Pe(e,t){const{internalData:o}=e,{fullColumnIdData:n}=o;if(!t)return null;let l=t.parentId;while(n[l]){const e=n[l].column;if(l=e.parentId,!l)return e}return t}function Ve(e,t,o){for(let n=0;n<e.length;n++){const{row:l,col:r,rowspan:a,colspan:i}=e[n];if(r>-1&&l>-1&&a&&i){if(l===t&&r===o)return{rowspan:a,colspan:i};if(t>=l&&t<l+a&&o>=r&&o<r+i)return{rowspan:0,colspan:0}}}}function _e(e){const{props:t,internalData:o}=e;return o.initStatus=!1,e.clearSort(),e.clearCurrentRow(),e.clearCurrentColumn(),e.clearRadioRow(),e.clearRadioReserve(),e.clearCheckboxRow(),e.clearCheckboxReserve(),e.clearRowExpand(),e.clearTreeExpand(),e.clearTreeExpandReserve(),e.clearPendingRow(),e.clearFilter&&e.clearFilter(),e.clearSelected&&(t.keyboardConfig||t.mouseConfig)&&e.clearSelected(),e.clearCellAreas&&t.mouseConfig&&(e.clearCellAreas(),e.clearCopyCellArea()),e.clearScroll()}function je(e){return e.clearFilter&&e.clearFilter(),_e(e)}function He(e,t){const{reactData:o,internalData:n}=e,{refTableBody:l}=e.getRefMaps(),{scrollYLoad:r}=o,{afterFullData:a,scrollYStore:i}=n,s=l.value,c=s?s.$el:null;if(c){const o=c.querySelector(`[rowid="${ye(e,t)}"]`);if(o){const t=c.clientHeight,n=c.scrollTop,l=o.offsetParent,r=o.offsetTop+(l?l.offsetTop:0),a=o.clientHeight;if(r<n||r>n+t)return e.scrollTo(null,r);if(r+a>=t+n)return e.scrollTo(null,n+a)}else if(r)return e.scrollTo(null,(a.indexOf(t)-1)*i.rowHeight)}return Promise.resolve()}function Be(e,t){const{reactData:o,internalData:n}=e,{refTableBody:l}=e.getRefMaps(),{scrollXLoad:r}=o,{visibleColumn:a}=n,i=l.value,s=i?i.$el:null;if(s){const o=s.querySelector(`.${t.id}`);if(o){const t=s.clientWidth,n=s.scrollLeft,l=o.offsetParent,r=o.offsetLeft+(l?l.offsetLeft:0),a=o.clientWidth;if(r<n||r>n+t)return e.scrollTo(r);if(r+a>=t+n)return e.scrollTo(n+a)}else if(r){let o=0;for(let e=0;e<a.length;e++){if(a[e]===t)break;o+=a[e].renderWidth}return e.scrollTo(o)}}return Promise.resolve()}function ze(e){return"on"+e.substring(0,1).toLocaleUpperCase()+e.substring(1)}function We(e){return a().isArray(e)?e:[e]}const qe="modelValue",Ue={transfer:!0};function Xe(e){switch(e.name){case"input":case"textarea":return"input"}return"update:modelValue"}function Ye(e){switch(e.name){case"input":case"textarea":case"$input":case"$textarea":return"input"}return"change"}function Ge(e,t){return e&&t.valueFormat?a().toStringDate(e,t.valueFormat):e}function Ke(e,t,o){const{dateConfig:n={}}=t;return a().toDateString(Ge(e,t),n.labelFormat||o)}function Ze(e,t){return Ke(e,t,c.i18n(`vxe.input.date.labelFormat.${t.type}`))}function Je(e){return`vxe-${e.replace("$","")}`}function Qe({name:e}){return(0,g.resolveComponent)(Je(e))}function et(e,t,o){const{$panel:n}=e;n.changeOption({},t,o)}function tt(e){let{name:t,attrs:o}=e;return"input"===t&&(o=Object.assign({type:"text"},o)),o}function ot(e){const{name:t,immediate:o,props:n}=e;if(!o){if("$input"===t){const{type:e}=n||{};return!(!e||"text"===e||"number"===e||"integer"===e||"float"===e)}return"input"!==t&&"textarea"!==t&&"$textarea"!==t}return o}function nt(e,t,o,n){return a().assign({immediate:ot(e)},Ue,n,e.props,{[qe]:o})}function lt(e,t,o,n){return a().assign({},Ue,n,e.props,{[qe]:o})}function rt(e,t,o,n){return a().assign({},Ue,n,e.props,{[qe]:o})}function at(e,t){return"cell"===t.$type||ot(e)}function it(e,t,o){const{placeholder:n}=e;return[(0,g.h)("span",{class:"vxe-cell--label"},n&&V(o)?[(0,g.h)("span",{class:"vxe-cell--placeholder"},W(z(n),1))]:W(o,1))]}function st(e,t,o,n){const{events:l}=e,r=Xe(e),i=Ye(e),s=i===r,c={};return l&&a().objectEach(l,((e,o)=>{c[ze(o)]=function(...o){e(t,...o)}})),o&&(c[ze(r)]=function(e){o(e),s&&n&&n(e),l&&l[r]&&l[r](t,e)}),!s&&n&&(c[ze(i)]=function(...e){n(...e),l&&l[i]&&l[i](t,...e)}),c}function ct(e,t,o,n){const{events:l}=e,r=Xe(e),i=Ye(e),s={};return a().objectEach(l,((e,o)=>{s[ze(o)]=function(...o){a().isFunction(e)||f("vxe.error.errFunc",[e]),e(t,...o)}})),o&&(s[ze(r)]=function(e){o(e),l&&l[r]&&l[r](t,e)}),n&&(s[ze(i)]=function(...e){n(...e),l&&l[i]&&l[i](t,...e)}),s}function ut(e,t){const{$table:o,row:n,column:l}=t,{name:r}=e,{model:a}=l,i=at(e,t);return ct(e,t,(e=>{i?$e(n,l,e):(a.update=!0,a.value=e)}),(e=>{if(i||"$input"!==r&&"$textarea"!==r)o.updateStatus(t);else{const n=e.value;a.update=!0,a.value=n,o.updateStatus(t,n)}}))}function dt(e,t,o){return ct(e,t,(e=>{o.data=e}),(()=>{et(t,!a().eqNull(o.data),o)}))}function pt(e,t){const{$form:o,data:n,property:l}=t;return ct(e,t,(e=>{a().set(n,l,e)}),(()=>{o.updateStatus(t)}))}function ft(e,t){const{$table:o,row:n,column:l}=t,{model:r}=l;return st(e,t,(o=>{const a=o.target.value;at(e,t)?$e(n,l,a):(r.update=!0,r.value=a)}),(e=>{const n=e.target.value;o.updateStatus(t,n)}))}function mt(e,t,o){return st(e,t,(e=>{o.data=e.target.value}),(()=>{et(t,!a().eqNull(o.data),o)}))}function ht(e,t){const{$form:o,data:n,property:l}=t;return st(e,t,(e=>{const t=e.target.value;a().set(n,l,t)}),(()=>{o.updateStatus(t)}))}function gt(e,t){const{row:o,column:n}=t,{name:l}=e,r=at(e,t)?Me(o,n):n.model.value;return[(0,g.h)(l,{class:`vxe-default-${l}`,...tt(e),value:r,...ft(e,t)})]}function vt(e,t){const{row:o,column:n}=t,l=Me(o,n);return[(0,g.h)(Qe(e),{...nt(e,t,l),...ut(e,t)})]}function xt(e,t){return[(0,g.h)((0,g.resolveComponent)("vxe-button"),{...nt(e,t,null),...ct(e,t)})]}function bt(e,t){return e.children.map((e=>xt(e,t)[0]))}function wt(e,t,o){const{optionGroups:n,optionGroupProps:l={}}=e,r=l.options||"options",a=l.label||"label";return n.map(((n,l)=>(0,g.h)("optgroup",{key:l,label:n[a]},o(n[r],e,t))))}function Ct(e,t,o){const{optionProps:n={}}=t,{row:l,column:r}=o,a=n.label||"label",i=n.value||"value",s=n.disabled||"disabled",c=at(t,o)?Me(l,r):r.model.value;return e.map(((e,t)=>(0,g.h)("option",{key:t,value:e[i],disabled:e[s],selected:e[i]==c},e[a])))}function yt(e,t){const{column:o}=t,{name:n}=e,l=tt(e);return o.filters.map(((o,r)=>(0,g.h)(n,{key:r,class:`vxe-default-${n}`,...l,value:o.data,...mt(e,t,o)})))}function Tt(e,t){const{column:o}=t;return o.filters.map(((o,n)=>{const l=o.data;return(0,g.h)(Qe(e),{key:n,...lt(e,e,l),...dt(e,t,o)})}))}function Et({option:e,row:t,column:o}){const{data:n}=e,l=a().get(t,o.property);return l==n}function St(e,t){return[(0,g.h)("select",{class:"vxe-default-select",...tt(e),...ft(e,t)},e.optionGroups?wt(e,t,Ct):Ct(e.options,e,t))]}function kt(e,t){const{row:o,column:n}=t,{options:l,optionProps:r,optionGroups:a,optionGroupProps:i}=e,s=Me(o,n);return[(0,g.h)(Qe(e),{...nt(e,t,s,{options:l,optionProps:r,optionGroups:a,optionGroupProps:i}),...ut(e,t)})]}function Rt(e,{row:t,column:o}){const{props:n={},options:l,optionGroups:r,optionProps:i={},optionGroupProps:s={}}=e,c=a().get(t,o.property);let u;const d=i.label||"label",p=i.value||"value";return V(c)?"":a().map(n.multiple?c:[c],r?e=>{const t=s.options||"options";for(let o=0;o<r.length;o++)if(u=a().find(r[o][t],(t=>t[p]==e)),u)break;return u?u[d]:e}:e=>(u=a().find(l,(t=>t[p]==e)),u?u[d]:e)).join(", ")}function Ot(e,t){const{data:o,property:n}=t,{name:l}=e,r=tt(e),i=a().get(o,n);return[(0,g.h)(l,{class:`vxe-default-${l}`,...r,value:!r||"input"!==l||"submit"!==r.type&&"reset"!==r.type?i:null,...ht(e,t)})]}function Mt(e,t){const{data:o,property:n}=t,l=a().get(o,n);return[(0,g.h)(Qe(e),{...rt(e,t,l),...pt(e,t)})]}function $t(e,t){return[(0,g.h)((0,g.resolveComponent)("vxe-button"),{...rt(e,t,null),...ct(e,t)})]}function It(e,t){return e.children.map((e=>$t(e,t)[0]))}function Dt(e,t,o){const{data:n,property:l}=o,{optionProps:r={}}=t,i=r.label||"label",s=r.value||"value",c=r.disabled||"disabled",u=a().get(n,l);return e.map(((e,t)=>(0,g.h)("option",{key:t,value:e[s],disabled:e[c],selected:e[s]==u},e[i])))}function Ft(e){const{row:t,column:o,options:n}=e;return n.original?Me(t,o):Rt(o.editRender||o.cellRender,e)}function Nt(e,t){const{name:o,options:n,optionProps:l={}}=e,{data:r,property:i}=t,s=l.label||"label",c=l.value||"value",u=l.disabled||"disabled",d=a().get(r,i),p=Je(o);return n?[(0,g.h)((0,g.resolveComponent)(`${p}-group`),{...rt(e,t,d),...pt(e,t)},{default:()=>n.map(((e,t)=>(0,g.h)((0,g.resolveComponent)(p),{key:t,label:e[c],content:e[s],disabled:e[u]})))})]:[(0,g.h)((0,g.resolveComponent)(p),{...rt(e,t,d),...pt(e,t)})]}const Lt={input:{autofocus:"input",renderEdit:gt,renderDefault:gt,renderFilter:yt,defaultFilterMethod:Et,renderItemContent:Ot},textarea:{autofocus:"textarea",renderEdit:gt,renderItemContent:Ot},select:{renderEdit:St,renderDefault:St,renderCell(e,t){return it(e,t,Rt(e,t))},renderFilter(e,t){const{column:o}=t;return o.filters.map(((o,n)=>(0,g.h)("select",{key:n,class:"vxe-default-select",...tt(e),...mt(e,t,o)},e.optionGroups?wt(e,t,Ct):Ct(e.options,e,t))))},defaultFilterMethod:Et,renderItemContent(e,t){return[(0,g.h)("select",{class:"vxe-default-select",...tt(e),...ht(e,t)},e.optionGroups?wt(e,t,Dt):Dt(e.options,e,t))]},cellExportMethod:Ft},$input:{autofocus:".vxe-input--inner",renderEdit:vt,renderCell(e,t){const{props:o={}}=e,{row:n,column:l}=t,r=o.digits||c.input.digits;let i=a().get(n,l.property);if(i)switch(o.type){case"date":case"week":case"month":case"year":i=Ze(i,o);break;case"float":i=a().toFixed(a().floor(i,r),r);break}return it(e,t,i)},renderDefault:vt,renderFilter:Tt,defaultFilterMethod:Et,renderItemContent:Mt},$textarea:{autofocus:".vxe-textarea--inner",renderItemContent:Mt},$button:{renderDefault:xt,renderItemContent:$t},$buttons:{renderDefault:bt,renderItemContent:It},$select:{autofocus:".vxe-input--inner",renderEdit:kt,renderDefault:kt,renderCell(e,t){return it(e,t,Rt(e,t))},renderFilter(e,t){const{column:o}=t,{options:n,optionProps:l,optionGroups:r,optionGroupProps:a}=e;return o.filters.map(((o,i)=>{const s=o.data;return(0,g.h)(Qe(e),{key:i,...lt(e,t,s,{options:n,optionProps:l,optionGroups:r,optionGroupProps:a}),...dt(e,t,o)})}))},defaultFilterMethod:Et,renderItemContent(e,t){const{data:o,property:n}=t,{options:l,optionProps:r,optionGroups:i,optionGroupProps:s}=e,c=a().get(o,n);return[(0,g.h)(Qe(e),{...rt(e,t,c,{options:l,optionProps:r,optionGroups:i,optionGroupProps:s}),...pt(e,t)})]},cellExportMethod:Ft},$radio:{autofocus:".vxe-radio--input",renderItemContent:Nt},$checkbox:{autofocus:".vxe-checkbox--input",renderItemContent:Nt},$switch:{autofocus:".vxe-switch--button",renderEdit:vt,renderDefault:vt,renderItemContent:Mt}},At={mixin(e){return a().each(e,((e,t)=>At.add(t,e))),At},get(e){return Lt[e]||null},add(e,t){if(e&&t){const o=Lt[e];o?(a().each(t,((t,n)=>{a().eqNull(o[n])||o[n]===t||p("vxe.error.coverProp",[`Renderer.${e}`,n])})),Object.assign(o,t)):Lt[e]=t}return At},delete(e){return delete Lt[e],At}};class Pt{constructor(){v(this,"store",{})}mixin(e){return a().each(e,((e,t)=>{this.add(t,e)})),this}has(e){return!!this.get(e)}get(e){return this.store[e]}add(e,t){const o=this.store[e];a().isFunction(t)&&(p("vxe.error.delProp",["commands -> callback","commandMethod"]),t={commandMethod:t});{const n=a().keys(o);a().each(t,((t,o)=>{n.includes(o)&&p("vxe.error.coverProp",[e,o])}))}return this.store[e]=o?a().merge(o,t):t,this}delete(e){delete this.store[e]}forEach(e){a().objectEach(this.store,e)}}const Vt=new Pt;Object.assign(Vt,{_name:"Commands"});class _t{constructor(){v(this,"store",{})}mixin(e){return a().each(e,((e,t)=>{this.add(t,e)})),this}has(e){return!!this.get(e)}get(e){return this.store[e]}add(e,t){const o=this.store[e];a().isFunction(t)&&(p("vxe.error.delProp",["menus -> callback","menuMethod"]),t={menuMethod:t});{const n=a().keys(o);a().each(t,((t,o)=>{n.includes(o)&&p("vxe.error.coverProp",[e,o])}))}return this.store[e]=o?a().merge(o,t):t,this}delete(e){delete this.store[e]}forEach(e){a().objectEach(this.store,e)}}const jt=new _t;Object.assign(jt,{_name:"Menus"});class Ht{constructor(){v(this,"store",{})}mixin(e){return a().each(e,((e,t)=>{this.add(t,e)})),this}has(e){return!!this.get(e)}get(e){return this.store[e]}add(e,t){const o=this.store[e];{const n=a().keys(o);a().each(t,((t,o)=>{n.includes(o)&&p("vxe.error.coverProp",[e,o])}))}return this.store[e]=o?a().merge(o,t):t,this}delete(e){delete this.store[e]}forEach(e){a().objectEach(this.store,e)}}var Bt=Ht;const zt=new Bt;Object.assign(zt,{_name:"Validators"});const Wt=new Bt,qt=e=>(e&&e.zIndex&&A.setCurrent(e.zIndex),a().merge(c,e));function Ut(e,t){const o=[];return a().objectEach(e,((e,n)=>{0!==e&&e!==t||o.push(n)})),o}const Xt=[];function Yt(e,t){return e&&e.install&&-1===Xt.indexOf(e)&&(e.install(oo,t),Xt.push(e)),oo}function Gt(e,t){return c.i18n(e,t)}function Kt(e,t){return e?a().toValueString(c.translate?c.translate(e,t):e):""}class Zt{get zIndex(){return H()}get nextZIndex(){return j()}get exportTypes(){return Ut(c["export"].types,1)}get importTypes(){return Ut(c["export"].types,2)}}const Jt=new Zt,Qt="v4",eo=qt,to={},oo={v:Qt,version:"4.5.18",setup:eo,globalStore:to,interceptor:h,renderer:At,commands:Vt,formats:b,validators:zt,menus:jt,hooks:Wt,use:Yt,t:Gt,_t:Kt,config:qt,globalConfs:Jt};var no=(0,g.defineComponent)({name:"VxeTableFilter",props:{filterStore:Object},setup(e){const t=(0,g.inject)("$xetable",{}),{reactData:o,internalData:n}=t,l=(0,g.computed)((()=>{const{filterStore:t}=e;return t&&t.options.some((e=>e.checked))})),r=(t,o)=>{const{filterStore:n}=e;n.options.forEach((e=>{e._checked=o,e.checked=o})),n.isAllSelected=o,n.isIndeterminate=!1},a=o=>{const{filterStore:n}=e;n.options.forEach((e=>{e.checked=e._checked})),t.confirmFilterEvent(o)},i=(o,n,l)=>{const{filterStore:r}=e;r.options.forEach((e=>{e._checked=!1})),l._checked=n,t.checkFilterOptions(),a(o)},s=o=>{const{filterStore:n}=e;t.handleClearFilter(n.column),t.confirmFilterEvent(o)},u=(e,o,n)=>{n._checked=o,t.checkFilterOptions()},d=(t,o,n)=>{const{filterStore:l}=e;l.multiple?u(t,o,n):i(t,o,n)},p=(t,o)=>{const{filterStore:n}=e;n.multiple?r(t,o):s(t)},f={changeRadioOption:i,changeMultipleOption:u,changeAllOption:p,changeOption:d,confirmFilter:a,resetFilter:s},m=(o,l)=>{const{filterStore:r}=e,{column:a,multiple:i,maxHeight:s}=r,{slots:u}=a,m=u?u.filter:null,h=Object.assign({},n._currFilterParams,{$panel:f,$table:t});if(m)return[(0,g.h)("div",{class:"vxe-table--filter-template"},t.callSlot(m,h))];if(l&&l.renderFilter)return[(0,g.h)("div",{class:"vxe-table--filter-template"},We(l.renderFilter(o,h)))];const v=i?r.isAllSelected:!r.options.some((e=>e._checked)),x=i&&r.isIndeterminate;return[(0,g.h)("ul",{class:"vxe-table--filter-header"},[(0,g.h)("li",{class:["vxe-table--filter-option",{"is--checked":v,"is--indeterminate":x}],title:c.i18n(i?"vxe.table.allTitle":"vxe.table.allFilter"),onClick:e=>{p(e,!r.isAllSelected)}},(i?[(0,g.h)("span",{class:["vxe-checkbox--icon",x?c.icon.TABLE_CHECKBOX_INDETERMINATE:v?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,g.h)("span",{class:"vxe-checkbox--label"},c.i18n("vxe.table.allFilter"))]))]),(0,g.h)("ul",{class:"vxe-table--filter-body",style:s?{maxHeight:`${s}px`}:{}},r.options.map((e=>{const t=e._checked,o=!1;return(0,g.h)("li",{class:["vxe-table--filter-option",{"is--checked":e._checked}],title:e.label,onClick:t=>{d(t,!e._checked,e)}},(i?[(0,g.h)("span",{class:["vxe-checkbox--icon",o?c.icon.TABLE_CHECKBOX_INDETERMINATE:t?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,g.h)("span",{class:"vxe-checkbox--label"},W(e.label,1))]))})))]},h=()=>{const{filterStore:t}=e,{column:o,multiple:n}=t,r=l.value,i=o.filterRender,u=i?oo.renderer.get(i.name):null,d=!r&&!t.isAllSelected&&!t.isIndeterminate;return!n||u&&!1===u.showFilterFooter?[]:[(0,g.h)("div",{class:"vxe-table--filter-footer"},[(0,g.h)("button",{class:{"is--disabled":d},disabled:d,onClick:a},c.i18n("vxe.table.confirmFilter")),(0,g.h)("button",{onClick:s},c.i18n("vxe.table.resetFilter"))])]},v=()=>{const{filterStore:l}=e,{initStore:r}=o,{column:a}=l,i=a?a.filterRender:null,s=i?oo.renderer.get(i.name):null,c=s?s.filterClassName:"",u=Object.assign({},n._currFilterParams,{$panel:f,$table:t});return(0,g.h)("div",{class:["vxe-table--filter-wrapper","filter--prevent-default",G(c,u),{"is--animat":t.props.animat,"is--multiple":l.multiple,"is--active":l.visible}],style:l.style},r.filter&&l.visible?m(i,s).concat(h()):[])};return v}});const lo=["setFilter","clearFilter","getCheckedFilters"],ro={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{refTableBody:l,refTableFilter:r}=e.getRefMaps(),{computeFilterOpts:i,computeMouseOpts:s}=e.getComputeMaps(),c={checkFilterOptions(){const{filterStore:e}=o;e.isAllSelected=e.options.every((e=>e._checked)),e.isIndeterminate=!e.isAllSelected&&e.options.some((e=>e._checked))},triggerFilterEvent(t,a,i){const{initStore:s,filterStore:c}=o;if(c.column===a&&c.visible)c.visible=!1;else{const{target:o,pageX:u}=t,{visibleWidth:d}=ne(),{filters:p,filterMultiple:f,filterRender:m}=a,h=m?oo.renderer.get(m.name):null,v=a.filterRecoverMethod||(h?h.filterRecoverMethod:null);n._currFilterParams=i,Object.assign(c,{multiple:f,options:p,column:a,style:null}),c.options.forEach((t=>{const{_checked:o,checked:n}=t;t._checked=n,n||o===n||v&&v({option:t,column:a,$table:e})})),this.checkFilterOptions(),c.visible=!0,s.filter=!0,(0,g.nextTick)((()=>{const e=l.value,t=e.$el,n=r.value,i=n?n.$el:null;let s=0,p=0,f=null,m=null;i&&(s=i.offsetWidth,p=i.offsetHeight,f=i.querySelector(".vxe-table--filter-header"),m=i.querySelector(".vxe-table--filter-footer"));const h=s/2,g=10,v=t.clientWidth-s-g;let x,b;const w={top:`${o.offsetTop+o.offsetParent.offsetTop+o.offsetHeight+8}px`};let C=null;if(p>=t.clientHeight&&(C=Math.max(60,t.clientHeight-(m?m.offsetHeight:0)-(f?f.offsetHeight:0))),"left"===a.fixed?x=o.offsetLeft+o.offsetParent.offsetLeft-h:"right"===a.fixed?b=o.offsetParent.offsetWidth-o.offsetLeft+(o.offsetParent.offsetParent.offsetWidth-o.offsetParent.offsetLeft)-a.renderWidth-h:x=o.offsetLeft+o.offsetParent.offsetLeft-h-t.scrollLeft,x){const e=u+s-h+g-d;e>0&&(x-=e),w.left=`${Math.min(v,Math.max(g,x))}px`}else if(b){const e=u+s-h+g-d;e>0&&(b+=e),w.right=`${Math.max(g,b)}px`}c.style=w,c.maxHeight=C}))}e.dispatchEvent("filter-visible",{column:a,field:a.field,property:a.field,filterList:e.getCheckedFilters(),visible:c.visible},t)},handleClearFilter(t){if(t){const{filters:o,filterRender:n}=t;if(o){const l=n?oo.renderer.get(n.name):null,r=t.filterResetMethod||(l?l.filterResetMethod:null);o.forEach((e=>{e._checked=!1,e.checked=!1,r||(e.data=a().clone(e.resetValue,!0))})),r&&r({options:o,column:t,$table:e})}}},confirmFilterEvent(n){const{mouseConfig:l}=t,{filterStore:r,scrollXLoad:a,scrollYLoad:c}=o,u=i.value,d=s.value,{column:p}=r,{field:f}=p,m=[],h=[];p.filters.forEach((e=>{e.checked&&(m.push(e.value),h.push(e.data))}));const g=e.getCheckedFilters(),v={$table:e,$event:n,column:p,field:f,property:f,values:m,datas:h,filters:g,filterList:g};u.remote||(e.handleTableData(!0),e.checkSelectionStatus()),l&&d.area&&e.handleFilterEvent&&e.handleFilterEvent(n,v),e.dispatchEvent("filter-change",v,n),e.closeFilter(),e.updateFooter().then((()=>{const{scrollXLoad:t,scrollYLoad:n}=o;if(a||t||c||n)return(a||t)&&e.updateScrollXSpace(),(c||n)&&e.updateScrollYSpace(),e.refreshScroll()})).then((()=>(e.updateCellAreas(),e.recalculate(!0)))).then((()=>{setTimeout((()=>e.recalculate()),50)}))}},u={openFilter(t){const o=Te(e,t);if(o&&o.filters){const{elemStore:t}=n,{fixed:l}=o;return e.scrollToColumn(o).then((()=>{const e=t[`${l||"main"}-header-wrapper`]||t["main-header-wrapper"],n=e?e.value:null;if(n){const e=n.querySelector(`.vxe-header--column.${o.id} .vxe-filter--btn`);he(e,"click")}}))}return(0,g.nextTick)()},setFilter(t,o){const n=Te(e,t);return n&&n.filters&&(n.filters=Re(o||[])),(0,g.nextTick)()},clearFilter(t){const{filterStore:l}=o,{tableFullColumn:r}=n,a=i.value;let s;return t?(s=Te(e,t),s&&c.handleClearFilter(s)):r.forEach(c.handleClearFilter),t&&s===l.column||Object.assign(l,{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1}),a.remote?(0,g.nextTick)():e.updateData()},getCheckedFilters(){const{tableFullColumn:e}=n,t=[];return e.forEach((e=>{const{field:o,filters:n}=e,l=[],r=[];n&&n.length&&(n.forEach((e=>{e.checked&&(l.push(e.value),r.push(e.data))})),l.length&&t.push({column:e,field:o,property:o,values:l,datas:r}))})),t}};return{...u,...c}},setupGrid(e){return e.extendTableMethods(lo)}};var ao=ro;let io;const so=(0,g.reactive)({modals:[]}),co=(0,g.defineComponent)({setup(){return()=>{const{modals:e}=so;return(0,g.h)("div",{class:"vxe-dynamics--modal"},e.map((e=>(0,g.h)((0,g.resolveComponent)("vxe-modal"),e))))}}}),uo=(0,g.createApp)(co);function po(){io||(io=document.createElement("div"),io.className="vxe-dynamics",document.body.appendChild(io),uo.mount(io))}const fo={Panel:no,install(e){oo.hooks.add("$tableFilter",ao),e.component(no.name,no)}},mo=fo;uo.component(no.name,no);var ho=(0,g.defineComponent)({name:"VxeTableContextMenu",setup(e,t){const o=a().uniqueId(),n=(0,g.inject)("$xetable",{}),{reactData:l}=n,r=(0,g.ref)(),i={refElem:r},s={xID:o,props:e,context:t,getRefMaps:()=>i},c=()=>{const{ctxMenuStore:e}=l,{computeMenuOpts:t}=n.getComputeMaps(),o=t.value;return(0,g.h)(g.Teleport,{to:"body",disabled:!1},[(0,g.h)("div",{ref:r,class:["vxe-table--context-menu-wrapper",o.className,{"is--visible":e.visible}],style:e.style},e.list.map(((t,o)=>t.every((e=>!1===e.visible))?(0,g.createCommentVNode)():(0,g.h)("ul",{class:"vxe-context-menu--option-wrapper",key:o},t.map(((t,l)=>{const r=t.children&&t.children.some((e=>!1!==e.visible));return!1===t.visible?null:(0,g.h)("li",{class:[t.className,{"link--disabled":t.disabled,"link--active":t===e.selected}],key:`${o}_${l}`},[(0,g.h)("a",{class:"vxe-context-menu--link",onClick(e){n.ctxMenuLinkEvent(e,t)},onMouseover(e){n.ctxMenuMouseoverEvent(e,t)},onMouseout(e){n.ctxMenuMouseoutEvent(e,t)}},[(0,g.h)("i",{class:["vxe-context-menu--link-prefix",t.prefixIcon]}),(0,g.h)("span",{class:"vxe-context-menu--link-content"},z(t.name)),(0,g.h)("i",{class:["vxe-context-menu--link-suffix",r?t.suffixIcon||"suffix--haschild":t.suffixIcon]})]),r?(0,g.h)("ul",{class:["vxe-table--context-menu-clild-wrapper",{"is--show":t===e.selected&&e.showChild}]},t.children.map(((r,a)=>!1===r.visible?null:(0,g.h)("li",{class:[r.className,{"link--disabled":r.disabled,"link--active":r===e.selectChild}],key:`${o}_${l}_${a}`},[(0,g.h)("a",{class:"vxe-context-menu--link",onClick(e){n.ctxMenuLinkEvent(e,r)},onMouseover(e){n.ctxMenuMouseoverEvent(e,t,r)},onMouseout(e){n.ctxMenuMouseoutEvent(e,t)}},[(0,g.h)("i",{class:["vxe-context-menu--link-prefix",r.prefixIcon]}),(0,g.h)("span",{class:"vxe-context-menu--link-content"},z(r.name))])])))):null])}))))))])};return s.renderVN=c,s},render(){return this.renderVN()}});const go={F2:"F2",ESCAPE:"Escape",ENTER:"Enter",TAB:"Tab",DELETE:"Delete",BACKSPACE:"Backspace",SPACEBAR:" ",CONTEXT_MENU:"ContextMenu",ARROW_UP:"ArrowUp",ARROW_DOWN:"ArrowDown",ARROW_LEFT:"ArrowLeft",ARROW_RIGHT:"ArrowRight",PAGE_UP:"PageUp",PAGE_DOWN:"PageDown"},vo={" ":"Spacebar",Apps:go.CONTEXT_MENU,Del:go.DELETE,Up:go.ARROW_UP,Down:go.ARROW_DOWN,Left:go.ARROW_LEFT,Right:go.ARROW_RIGHT},xo=Y.firefox?"DOMMouseScroll":"mousewheel",bo=[],wo=(e,t)=>{const{key:o}=e;return t=t.toLowerCase(),!!o&&(t===o.toLowerCase()||!(!vo[o]||vo[o].toLowerCase()!==t))};function Co(e){const t=e.type===xo;bo.forEach((({type:o,cb:n})=>{e.cancelBubble||(o===e.type||t&&"mousewheel"===o)&&n(e)}))}const yo={on(e,t,o){bo.push({comp:e,type:t,cb:o})},off(e,t){a().remove(bo,(o=>o.comp===e&&o.type===t))},trigger:Co,eqKeypad(e,t){const{key:o}=e;return t.toLowerCase()===o.toLowerCase()}};Y.isDoc&&(Y.msie||(document.addEventListener("copy",Co,!1),document.addEventListener("cut",Co,!1),document.addEventListener("paste",Co,!1)),document.addEventListener("keydown",Co,!1),document.addEventListener("contextmenu",Co,!1),window.addEventListener("mousedown",Co,!1),window.addEventListener("blur",Co,!1),window.addEventListener("resize",Co,!1),window.addEventListener(xo,a().throttle(Co,100,{leading:!0,trailing:!1}),{passive:!0,capture:!1}));const To=["closeMenu"],Eo={setupTable(e){const{xID:t,props:o,reactData:n,internalData:l}=e,{refElem:r,refTableFilter:i,refTableMenu:s}=e.getRefMaps(),{computeMouseOpts:c,computeIsMenu:u,computeMenuOpts:d}=e.getComputeMaps();let p={},f={};const m=(t,o,r)=>{const{ctxMenuStore:a}=n,i=u.value,c=d.value,f=c[o],m=c.visibleMethod;if(f){const{options:o,disabled:n}=f;n?t.preventDefault():i&&o&&o.length&&(r.options=o,e.preventEvent(t,"event.showMenu",r,(()=>{if(!m||m(r)){t.preventDefault(),e.updateZindex();const{scrollTop:n,scrollLeft:i,visibleHeight:c,visibleWidth:u}=ne();let d=t.clientY+n,p=t.clientX+i;const f=()=>{l._currMenuParams=r,Object.assign(a,{visible:!0,list:o,selected:null,selectChild:null,showChild:!1,style:{zIndex:l.tZindex,top:`${d}px`,left:`${p}px`}}),(0,g.nextTick)((()=>{const e=s.value,t=e.getRefMaps().refElem.value,o=t.clientHeight,l=t.clientWidth,{boundingTop:r,boundingLeft:f}=de(t),m=r+o-c,h=f+l-u;m>-10&&(a.style.top=`${Math.max(n+2,d-o-2)}px`),h>-10&&(a.style.left=`${Math.max(i+2,p-l-2)}px`)}))},{keyboard:m,row:h,column:v}=r;m&&h&&v?e.scrollToRow(h,v).then((()=>{const t=e.getCell(h,v);if(t){const{boundingTop:e,boundingLeft:o}=de(t);d=e+n+Math.floor(t.offsetHeight/2),p=o+i+Math.floor(t.offsetWidth/2)}f()})):f()}else p.closeMenu()})))}e.closeFilter()};return p={closeMenu(){return Object.assign(n.ctxMenuStore,{visible:!1,selected:null,selectChild:null,showChild:!1}),(0,g.nextTick)()}},f={moveCtxMenu(e,t,o,n,l,r){let i;const s=a().findIndexOf(r,(e=>t[o]===e));if(n)l&&B(t.selected)?t.showChild=!0:(t.showChild=!1,t.selectChild=null);else if(wo(e,go.ARROW_UP)){for(let e=s-1;e>=0;e--)if(!1!==r[e].visible){i=r[e];break}t[o]=i||r[r.length-1]}else if(wo(e,go.ARROW_DOWN)){for(let e=s+1;e<r.length;e++)if(!1!==r[e].visible){i=r[e];break}t[o]=i||r[0]}else t[o]&&(wo(e,go.ENTER)||wo(e,go.SPACEBAR))&&f.ctxMenuLinkEvent(e,t[o])},handleGlobalContextmenuEvent(a){const{mouseConfig:u,menuConfig:f}=o,{editStore:h,ctxMenuStore:g}=n,{visibleColumn:v}=l,x=i.value,b=s.value,w=c.value,C=d.value,y=r.value,{selected:T}=h,E=["header","body","footer"];if(P(f)){if(g.visible&&b&&ce(a,b.getRefMaps().refElem.value).flag)return void a.preventDefault();if(l._keyCtx){const t="body",o={type:t,$table:e,keyboard:!0,columns:v.slice(0),$event:a};if(u&&w.area){const n=e.getActiveCellArea();if(n&&n.row&&n.column)return o.row=n.row,o.column=n.column,void m(a,t,o)}else if(u&&w.selected&&T.row&&T.column)return o.row=T.row,o.column=T.column,void m(a,t,o)}for(let o=0;o<E.length;o++){const n=E[o],l=ce(a,y,`vxe-${n}--column`,(e=>e.parentNode.parentNode.parentNode.getAttribute("xid")===t)),r={type:n,$table:e,columns:v.slice(0),$event:a};if(l.flag){const t=l.targetElem,o=e.getColumnNode(t),i=o?o.item:null;let s=`${n}-`;if(i&&Object.assign(r,{column:i,columnIndex:e.getColumnIndex(i),cell:t}),"body"===n){const o=e.getRowNode(t.parentNode),n=o?o.item:null;s="",n&&(r.row=n,r.rowIndex=e.getRowIndex(n))}const c=`${s}cell-menu`;return m(a,n,r),void e.dispatchEvent(c,r,a)}if(ce(a,y,`vxe-table--${n}-wrapper`,(e=>e.getAttribute("xid")===t)).flag)return void("cell"===C.trigger?a.preventDefault():m(a,n,r))}}x&&!ce(a,x.$el).flag&&e.closeFilter(),p.closeMenu()},ctxMenuMouseoverEvent(e,t,o){const l=e.currentTarget,{ctxMenuStore:r}=n;e.preventDefault(),e.stopPropagation(),r.selected=t,r.selectChild=o,o||(r.showChild=B(t),r.showChild&&(0,g.nextTick)((()=>{const e=l.nextElementSibling;if(e){const{boundingTop:t,boundingLeft:o,visibleHeight:n,visibleWidth:r}=de(l),a=t+l.offsetHeight,i=o+l.offsetWidth;let s="",c="";i+e.offsetWidth>r-10&&(s="auto",c=`${l.offsetWidth}px`);let u="",d="";a+e.offsetHeight>n-10&&(u="auto",d="0"),e.style.left=s,e.style.right=c,e.style.top=u,e.style.bottom=d}})))},ctxMenuMouseoutEvent(e,t){const{ctxMenuStore:o}=n;t.children||(o.selected=null),o.selectChild=null},ctxMenuLinkEvent(t,o){if(!o.disabled&&(o.code||!o.children||!o.children.length)){const n=oo.menus.get(o.code),r=Object.assign({},l._currMenuParams,{menu:o,$table:e,$grid:e.xegrid,$event:t});n&&n.menuMethod&&n.menuMethod(r,t),e.dispatchEvent("menu-click",r,t),p.closeMenu()}}},{...p,...f}},setupGrid(e){return e.extendTableMethods(To)}};var So=Eo;const ko={Panel:ho,install(e){oo.hooks.add("$tableMenu",So),e.component(ho.name,ho)}},Ro=ko;uo.component(ho.name,ho);l(541);const Oo=["insert","insertAt","insertNextAt","remove","removeCheckboxRow","removeRadioRow","removeCurrentRow","getRecordset","getInsertRecords","getRemoveRecords","getUpdateRecords","getEditRecord","getActiveRecord","getSelectedCell","clearEdit","clearActived","clearSelected","isEditByRow","isActiveByRow","setEditRow","setActiveRow","setEditCell","setActiveCell","setSelectCell"],Mo={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{refElem:l}=e.getRefMaps(),{computeMouseOpts:r,computeEditOpts:i,computeCheckboxOpts:s,computeTreeOpts:d}=e.getComputeMaps();let m={},h={};const v=(e,t)=>{const{model:o,editRender:n}=t;n&&(o.value=Me(e,t),o.update=!1)},x=(e,t)=>{const{model:o,editRender:n}=t;n&&o.update&&($e(e,t,o.value),o.update=!1,o.value=null)},b=()=>{const e=l.value;if(e){const t=e.querySelector(".col--selected");t&&te(t,"col--selected")}};function w(){const{editStore:e,tableColumn:t}=o,n=i.value,{actived:l}=e,{row:r,column:a}=l;(r||a)&&("row"===n.mode?t.forEach((e=>x(r,e))):x(r,a))}function C(t,o){const{tableFullTreeData:l,afterFullData:r,fullDataRowIdData:i,fullAllDataRowIdData:s}=n,c=d.value,{rowField:u,parentField:f,mapChildrenField:m}=c,h=c.children||c.childrenField,g=o?"push":"unshift";t.forEach((t=>{const o=t[f],n=ye(e,t),c=o?a().findTree(l,(e=>o===e[u]),{children:m}):null;if(c){const{item:o}=c,l=s[ye(e,o)],r=l?l.level:0;let u=o[h],d=o[m];a().isArray(u)||(u=o[h]=[]),a().isArray(d)||(d=o[h]=[]),u[g](t),d[g](t);const p={row:t,rowid:n,seq:-1,index:-1,_index:-1,$index:-1,items:u,parent:parent,level:r+1};i[n]=p,s[n]=p}else{o&&p("vxe.error.unableInsert"),r[g](t),l[g](t);const e={row:t,rowid:n,seq:-1,index:-1,_index:-1,$index:-1,items:l,parent:null,level:0};i[n]=e,s[n]=e}}))}const y=(l,r,i)=>{const{treeConfig:s}=t,{mergeList:c,editStore:m}=o,{tableFullTreeData:h,afterFullData:v,tableFullData:x,fullDataRowIdData:b,fullAllDataRowIdData:w}=n,y=d.value,{transform:T,rowField:E,mapChildrenField:S}=y,k=y.children||y.childrenField;a().isArray(l)||(l=[l]);const R=(0,g.reactive)(e.defineField(l.map((e=>Object.assign(s&&T?{[S]:[],[k]:[]}:{},e)))));if(r)if(-1===r)s&&T?C(R,!0):(v.push(...R),x.push(...R),c.forEach((e=>{const{row:t,rowspan:o}=e;t+o>v.length&&(e.rowspan=o+R.length)})));else if(s&&T){const t=a().findTree(h,(e=>r[E]===e[E]),{children:S});if(t){const{parent:o}=t,n=o?o[S]:h,l=w[ye(e,o)],s=l?l.level:0;if(R.forEach(((l,r)=>{const a=ye(e,l);l[y.parentField]&&o&&l[y.parentField]!==o[E]&&f("vxe.error.errProp",[`${y.parentField}=${l[y.parentField]}`,`${y.parentField}=${o[E]}`]),o&&(l[y.parentField]=o[E]);let c=t.index+r;i&&(c+=1),n.splice(c,0,l);const u={row:l,rowid:a,seq:-1,index:-1,_index:-1,$index:-1,items:n,parent:o,level:s+1};b[a]=u,w[a]=u})),o){const e=a().findTree(h,(e=>r[E]===e[E]),{children:k});if(e){const t=e.items;let o=e.index;i&&(o+=1),t.splice(o,0,...R)}}}else p("vxe.error.unableInsert"),C(R,!0)}else{if(s)throw new Error(u("vxe.error.noTree",["insert"]));let t=-1;if(a().isNumber(r)?r<v.length&&(t=r):t=e.findRowIndexOf(v,r),i&&(t=Math.min(v.length,t+1)),-1===t)throw new Error(f("vxe.error.unableInsert"));v.splice(t,0,...R),x.splice(e.findRowIndexOf(x,r),0,...R),c.forEach((e=>{const{row:o,rowspan:n}=e;o>t?e.row=o+R.length:o+n>t&&(e.rowspan=n+R.length)}))}else s&&T?C(R,!1):(v.unshift(...R),x.unshift(...R),c.forEach((e=>{const{row:t}=e;t>0&&(e.row=t+R.length)})));const{insertMaps:O}=m;return R.forEach((t=>{const o=ye(e,t);O[o]=t})),e.cacheRowMap(),e.updateScrollYStatus(),e.handleTableData(s&&T),s&&T||e.updateAfterDataIndex(),e.updateFooter(),e.checkSelectionStatus(),o.scrollYLoad&&e.updateScrollYSpace(),(0,g.nextTick)().then((()=>(e.updateCellAreas(),e.recalculate()))).then((()=>({row:R.length?R[R.length-1]:null,rows:R})))};return m={insert(e){return y(e,null)},insertAt(e,t){return y(e,t)},insertNextAt(e,t){return y(e,t,!0)},remove(l){const{treeConfig:r}=t,{mergeList:i,editStore:c,selectCheckboxMaps:u}=o,{tableFullTreeData:p,afterFullData:f,tableFullData:h}=n,v=s.value,x=d.value,{transform:b,mapChildrenField:w}=x,C=x.children||x.childrenField,{actived:y,removeMaps:T,insertMaps:E}=c,{checkField:S}=v;let k=[];if(l?a().isArray(l)||(l=[l]):l=h,l.forEach((t=>{if(!e.isInsertByRow(t)){const o=ye(e,t);T[o]=t}})),!S){const t={...u};l.forEach((o=>{const n=ye(e,o);t[n]&&delete t[n]})),o.selectCheckboxMaps=t}return h===l?(l=k=h.slice(0),n.tableFullData=[],n.afterFullData=[],e.clearMergeCells()):r&&b?l.forEach((t=>{const o=ye(e,t),n=a().findTree(p,(t=>o===ye(e,t)),{children:w});if(n){const e=n.items.splice(n.index,1);k.push(e[0])}const l=a().findTree(p,(t=>o===ye(e,t)),{children:C});l&&l.items.splice(l.index,1);const r=e.findRowIndexOf(f,t);r>-1&&f.splice(r,1)})):l.forEach((t=>{const o=e.findRowIndexOf(h,t);if(o>-1){const e=h.splice(o,1);k.push(e[0])}const n=e.findRowIndexOf(f,t);n>-1&&(i.forEach((e=>{const{row:t,rowspan:o}=e;t>n?e.row=t-1:t+o>n&&(e.rowspan=o-1)})),f.splice(n,1))})),y.row&&e.findRowIndexOf(l,y.row)>-1&&m.clearEdit(),l.forEach((t=>{const o=ye(e,t);E[o]&&delete E[o]})),e.updateFooter(),e.cacheRowMap(),e.handleTableData(r&&b),r&&b||e.updateAfterDataIndex(),e.checkSelectionStatus(),o.scrollYLoad&&e.updateScrollYSpace(),(0,g.nextTick)().then((()=>(e.updateCellAreas(),e.recalculate()))).then((()=>({row:k.length?k[k.length-1]:null,rows:k})))},removeCheckboxRow(){return m.remove(e.getCheckboxRecords()).then((t=>(e.clearCheckboxRow(),t)))},removeRadioRow(){const t=e.getRadioRecord();return m.remove(t||[]).then((t=>(e.clearRadioRow(),t)))},removeCurrentRow(){const t=e.getCurrentRecord();return m.remove(t||[]).then((t=>(e.clearCurrentRow(),t)))},getRecordset(){return{insertRecords:m.getInsertRecords(),removeRecords:m.getRemoveRecords(),updateRecords:m.getUpdateRecords(),pendingRecords:e.getPendingRecords()}},getInsertRecords(){const{editStore:e}=o,{fullAllDataRowIdData:t}=n,{insertMaps:l}=e,r=[];return a().each(l,((e,o)=>{t[o]&&r.push(e)})),r},getRemoveRecords(){const{editStore:e}=o,{removeMaps:t}=e,n=[];return a().each(t,(e=>{n.push(e)})),n},getUpdateRecords(){const{keepSource:o,treeConfig:l}=t,{tableFullData:r}=n,i=d.value;return o?(w(),l?a().filterTree(r,(t=>e.isUpdateByRow(t)),i):r.filter((t=>e.isUpdateByRow(t)))):[]},getActiveRecord(){return this.getEditRecord()},getEditRecord(){const{editStore:t}=o,{afterFullData:r}=n,a=l.value,{args:i,row:s}=t.actived;return i&&e.findRowIndexOf(r,s)>-1&&a.querySelectorAll(".vxe-body--column.col--active").length?Object.assign({},i):null},getSelectedCell(){const{editStore:e}=o,{args:t,column:n}=e.selected;return t&&n?Object.assign({},t):null},clearActived(e){return this.clearEdit(e)},clearEdit(t){const{editStore:n}=o,{actived:l}=n,{row:r,column:a}=l;return(r||a)&&(w(),l.args=null,l.row=null,l.column=null,e.updateFooter(),e.dispatchEvent("edit-closed",{row:r,rowIndex:e.getRowIndex(r),$rowIndex:e.getVMRowIndex(r),column:a,columnIndex:e.getColumnIndex(a),$columnIndex:e.getVMColumnIndex(a)},t||null)),"obsolete"===c.cellVaildMode&&e.clearValidate?e.clearValidate():(0,g.nextTick)()},clearSelected(){const{editStore:e}=o,{selected:t}=e;return t.row=null,t.column=null,b(),(0,g.nextTick)()},isActiveByRow(e){return this.isEditByRow(e)},isEditByRow(e){const{editStore:t}=o;return t.actived.row===e},setActiveRow(e){return m.setEditRow(e)},setEditRow(t,o){const{visibleColumn:l}=n;let r=a().find(l,(e=>P(e.editRender)));return o&&(r=a().isString(o)?e.getColumnByField(o):o),e.setEditCell(t,r)},setActiveCell(e,t){return m.setEditCell(e,t)},setEditCell(o,l){const{editConfig:r}=t,i=a().isString(l)?e.getColumnByField(l):l;return o&&i&&P(r)&&P(i.editRender)?e.scrollToRow(o,i).then((()=>{const t=e.getCell(o,i);return t&&(h.handleActived({row:o,rowIndex:e.getRowIndex(o),column:i,columnIndex:e.getColumnIndex(i),cell:t,$table:e}),n._lastCallTime=Date.now()),(0,g.nextTick)()})):(0,g.nextTick)()},setSelectCell(t,n){const{tableData:l}=o,r=i.value,s=a().isString(n)?e.getColumnByField(n):n;if(t&&s&&"manual"!==r.trigger){const o=e.findRowIndexOf(l,t);if(o>-1&&s){const n=e.getCell(t,s),l={row:t,rowIndex:o,column:s,columnIndex:e.getColumnIndex(s),cell:n};e.handleSelected(l,{})}}return(0,g.nextTick)()}},h={handleActived(n,l){const{editConfig:r,mouseConfig:a}=t,{editStore:s,tableColumn:c}=o,u=i.value,{mode:d}=u,{actived:p}=s,{row:f,column:x}=n,{editRender:b}=x,w=n.cell||e.getCell(f,x),C=u.beforeEditMethod||u.activeMethod;if(n.cell=w,P(r)&&P(b)&&!e.hasPendingByRow(f)&&w){if(p.row!==f||"cell"===d&&p.column!==x){let t="edit-disabled";C&&!C({...n,$table:e,$grid:e.xegrid})||(a&&(m.clearSelected(),e.clearCellAreas&&(e.clearCellAreas(),e.clearCopyCellArea())),e.closeTooltip(),p.column&&m.clearEdit(l),t="edit-activated",x.renderHeight=w.offsetHeight,p.args=n,p.row=f,p.column=x,"row"===d?c.forEach((e=>v(f,e))):v(f,x),(0,g.nextTick)((()=>{h.handleFocus(n,l)}))),e.dispatchEvent(t,{row:f,rowIndex:e.getRowIndex(f),$rowIndex:e.getVMRowIndex(f),column:x,columnIndex:e.getColumnIndex(x),$columnIndex:e.getVMColumnIndex(x)},l),"edit-activated"===t&&e.dispatchEvent("edit-actived",{row:f,rowIndex:e.getRowIndex(f),$rowIndex:e.getVMRowIndex(f),column:x,columnIndex:e.getColumnIndex(x),$columnIndex:e.getVMColumnIndex(x)},l)}else{const{column:t}=p;if(a&&(m.clearSelected(),e.clearCellAreas&&(e.clearCellAreas(),e.clearCopyCellArea())),t!==x){const{model:o}=t;o.update&&$e(f,t,o.value),e.clearValidate&&e.clearValidate(f,x)}x.renderHeight=w.offsetHeight,p.args=n,p.column=x,setTimeout((()=>{h.handleFocus(n,l)}))}e.focus()}return(0,g.nextTick)()},handleFocus(t){const{row:o,column:n,cell:l}=t,{editRender:r}=n;if(P(r)){const i=At.get(r.name);let s,{autofocus:c,autoselect:u}=r;if(!c&&i&&(c=i.autofocus),!u&&i&&(u=i.autoselect),a().isFunction(c)?s=c.call(this,t):c&&(s=l.querySelector(c),s&&s.focus()),s){if(u)s.select();else if(Y.msie){const e=s.createTextRange();e.collapse(!1),e.select()}}else e.scrollToRow(o,n)}},handleSelected(n,l){const{mouseConfig:a}=t,{editStore:s}=o,c=r.value,u=i.value,{actived:d,selected:p}=s,{row:f,column:v}=n,x=a&&c.selected,b=()=>(!x||p.row===f&&p.column===v||(d.row!==f||"cell"===u.mode&&d.column!==v)&&(m.clearEdit(l),m.clearSelected(),e.clearCellAreas&&(e.clearCellAreas(),e.clearCopyCellArea()),p.args=n,p.row=f,p.column=v,x&&h.addCellSelectedClass(),e.focus(),l&&e.dispatchEvent("cell-selected",n,l)),(0,g.nextTick)());return b()},addCellSelectedClass(){const{editStore:t}=o,{selected:n}=t,{row:l,column:r}=n;if(b(),l&&r){const t=e.getCell(l,r);t&&oe(t,"col--selected")}}},{...m,...h}},setupGrid(e){return e.extendTableMethods(Oo)}};var $o=Mo;const Io={install(){oo.hooks.add("$tableEdit",$o)}},Do=Io;function Fo(e){const t=(0,g.inject)("xesize",null),o=(0,g.computed)((()=>e.size||(t?t.value:null)));return(0,g.provide)("xesize",o),o}var No=(0,g.defineComponent)({name:"VxeButton",props:{type:String,className:[String,Function],popupClassName:[String,Function],size:{type:String,default:()=>c.button.size||c.size},name:[String,Number],content:String,placement:String,status:String,icon:String,round:Boolean,circle:Boolean,disabled:Boolean,loading:Boolean,destroyOnClose:Boolean,transfer:{type:Boolean,default:()=>c.button.transfer}},emits:["click","dropdown-click"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=Fo(e),i=(0,g.reactive)({inited:!1,showPanel:!1,animatVisible:!1,panelIndex:0,panelStyle:{},panelPlacement:""}),s={showTime:null},u=(0,g.ref)(),d=(0,g.ref)(),p=(0,g.ref)(),f={refElem:u},m={xID:l,props:e,context:t,reactData:i,internalData:s,getRefMaps:()=>f};let h={};const v=(0,g.computed)((()=>{const{type:t}=e;return!!t&&["submit","reset","button"].indexOf(t)>-1})),x=(0,g.computed)((()=>{const{type:t}=e;return t&&"text"===t?t:"button"})),b=()=>{i.panelIndex<H()&&(i.panelIndex=j())},w=()=>(0,g.nextTick)().then((()=>{const{transfer:t,placement:o}=e,{panelIndex:n}=i,l=d.value,r=p.value;if(r&&l){const e=l.offsetHeight,a=l.offsetWidth,s=r.offsetHeight,c=r.offsetWidth,u=5,d={zIndex:n},{top:p,left:f,boundingTop:m,visibleHeight:h,visibleWidth:v}=de(l);let x="bottom";if(t){let t=f+a-c,n=p+e;"top"===o?(x="top",n=p-s):o||(m+e+s+u>h&&(x="top",n=p-s),n<u&&(x="bottom",n=p+e)),t+c+u>v&&(t-=t+c+u-v),t<u&&(t=u),Object.assign(d,{left:`${t}px`,right:"auto",top:`${n}px`,minWidth:`${a}px`})}else"top"===o?(x="top",d.bottom=`${e}px`):o||m+e+s>h&&m-e-s>u&&(x="top",d.bottom=`${e}px`);return i.panelStyle=d,i.panelPlacement=x,(0,g.nextTick)()}})),C=e=>{h.dispatchEvent("click",{$event:e},e)},y=e=>{const t=0===e.button;t&&e.stopPropagation()},T=e=>{const t=e.currentTarget,o=p.value,{flag:n,targetElem:l}=ce(e,t,"vxe-button");n&&(o&&(o.dataset.active="N"),i.showPanel=!1,setTimeout((()=>{o&&"Y"===o.dataset.active||(i.animatVisible=!1)}),350),h.dispatchEvent("dropdown-click",{name:l.getAttribute("name"),$event:e},e))},E=()=>{const e=p.value;e&&(e.dataset.active="Y",i.animatVisible=!0,setTimeout((()=>{"Y"===e.dataset.active&&(i.showPanel=!0,b(),w(),setTimeout((()=>{i.showPanel&&w()}),50))}),20))},S=()=>{const e=p.value;e&&(e.dataset.active="Y",i.inited||(i.inited=!0),s.showTime=setTimeout((()=>{"Y"===e.dataset.active?E():i.animatVisible=!1}),250))},k=()=>{const e=p.value;clearTimeout(s.showTime),e?(e.dataset.active="N",setTimeout((()=>{"Y"!==e.dataset.active&&(i.showPanel=!1,setTimeout((()=>{"Y"!==e.dataset.active&&(i.animatVisible=!1)}),350))}),100)):(i.animatVisible=!1,i.showPanel=!1)},R=()=>{k()},O=()=>{const{content:t,icon:n,loading:l}=e,r=[];return l?r.push((0,g.h)("i",{class:["vxe-button--loading-icon",c.icon.BUTTON_LOADING]})):o.icon?r.push((0,g.h)("span",{class:"vxe-button--custom-icon"},o.icon({}))):n&&r.push((0,g.h)("i",{class:["vxe-button--icon",n]})),o.default?r.push((0,g.h)("span",{class:"vxe-button--content"},o.default({}))):t&&r.push((0,g.h)("span",{class:"vxe-button--content"},z(t))),r};h={dispatchEvent(e,t,o){n(e,Object.assign({$button:m,$event:o},t))},focus(){const e=d.value;return e.focus(),(0,g.nextTick)()},blur(){const e=d.value;return e.blur(),(0,g.nextTick)()}},Object.assign(m,h),(0,g.onMounted)((()=>{yo.on(m,"mousewheel",(e=>{const t=p.value;i.showPanel&&!ce(e,t).flag&&k()}))})),(0,g.onUnmounted)((()=>{yo.off(m,"mousewheel")}));const M=()=>{const{className:t,popupClassName:n,transfer:l,type:s,round:f,circle:h,destroyOnClose:b,status:w,name:k,disabled:M,loading:$}=e,{inited:I,showPanel:D}=i,F=v.value,N=x.value,L=r.value;return o.dropdowns?(0,g.h)("div",{ref:u,class:["vxe-button--dropdown",t?a().isFunction(t)?t({$button:m}):t:"",{[`size--${L}`]:L,"is--active":D}]},[(0,g.h)("button",{ref:d,class:["vxe-button",`type--${N}`,{[`size--${L}`]:L,[`theme--${w}`]:w,"is--round":f,"is--circle":h,"is--disabled":M||$,"is--loading":$}],name:k,type:F?s:"button",disabled:M||$,onMouseenter:S,onMouseleave:R,onClick:C},O().concat([(0,g.h)("i",{class:`vxe-button--dropdown-arrow ${c.icon.BUTTON_DROPDOWN}`})])),(0,g.h)(g.Teleport,{to:"body",disabled:!l||!I},[(0,g.h)("div",{ref:p,class:["vxe-button--dropdown-panel",n?a().isFunction(n)?n({$button:m}):n:"",{[`size--${L}`]:L,"animat--leave":i.animatVisible,"animat--enter":D}],placement:i.panelPlacement,style:i.panelStyle},I?[(0,g.h)("div",{class:"vxe-button--dropdown-wrapper",onMousedown:y,onClick:T,onMouseenter:E,onMouseleave:R},b&&!D?[]:o.dropdowns({}))]:[])])]):(0,g.h)("button",{ref:d,class:["vxe-button",`type--${N}`,{[`size--${L}`]:L,[`theme--${w}`]:w,"is--round":f,"is--circle":h,"is--disabled":M||$,"is--loading":$}],name:k,type:F?s:"button",disabled:M||$,onClick:C},O())};return m.renderVN=M,m},render(){return this.renderVN()}}),Lo=(0,g.defineComponent)({name:"VxeLoading",props:{modelValue:Boolean,icon:String,text:String},setup(e,{slots:t}){const o=(0,g.computed)((()=>e.icon||c.icon.LOADING)),n=(0,g.computed)((()=>{const t=c.loadingText;return e.text||(null===t?t:c.i18n("vxe.loading.text"))}));return()=>{const l=o.value,r=n.value;return(0,g.h)("div",{class:["vxe-loading",{"is--visible":e.modelValue}]},t.default?[(0,g.h)("div",{class:"vxe-loading--warpper"},t.default({}))]:[(0,g.h)("div",{class:"vxe-loading--chunk"},[l?(0,g.h)("i",{class:l}):(0,g.h)("div",{class:"vxe-loading--spinner"}),r?(0,g.h)("div",{class:"vxe-loading--text"},`${r}`):null])])}}});const Ao=Object.assign(Lo,{install(e){e.component(Lo.name,Lo)}});var Po=Ao;const Vo=[],_o=[];var jo=(0,g.defineComponent)({name:"VxeModal",props:{modelValue:Boolean,id:String,type:{type:String,default:"modal"},loading:{type:Boolean,default:null},status:String,iconStatus:String,className:String,top:{type:[Number,String],default:()=>c.modal.top},position:[String,Object],title:String,duration:{type:[Number,String],default:()=>c.modal.duration},message:[Number,String],content:[Number,String],cancelButtonText:{type:String,default:()=>c.modal.cancelButtonText},confirmButtonText:{type:String,default:()=>c.modal.confirmButtonText},lockView:{type:Boolean,default:()=>c.modal.lockView},lockScroll:Boolean,mask:{type:Boolean,default:()=>c.modal.mask},maskClosable:{type:Boolean,default:()=>c.modal.maskClosable},escClosable:{type:Boolean,default:()=>c.modal.escClosable},resize:Boolean,showHeader:{type:Boolean,default:()=>c.modal.showHeader},showFooter:{type:Boolean,default:()=>c.modal.showFooter},showZoom:Boolean,showClose:{type:Boolean,default:()=>c.modal.showClose},dblclickZoom:{type:Boolean,default:()=>c.modal.dblclickZoom},width:[Number,String],height:[Number,String],minWidth:{type:[Number,String],default:()=>c.modal.minWidth},minHeight:{type:[Number,String],default:()=>c.modal.minHeight},zIndex:Number,marginSize:{type:[Number,String],default:()=>c.modal.marginSize},fullscreen:Boolean,draggable:{type:Boolean,default:()=>c.modal.draggable},remember:{type:Boolean,default:()=>c.modal.remember},destroyOnClose:{type:Boolean,default:()=>c.modal.destroyOnClose},showTitleOverflow:{type:Boolean,default:()=>c.modal.showTitleOverflow},transfer:{type:Boolean,default:()=>c.modal.transfer},storage:{type:Boolean,default:()=>c.modal.storage},storageKey:{type:String,default:()=>c.modal.storageKey},animat:{type:Boolean,default:()=>c.modal.animat},size:{type:String,default:()=>c.modal.size||c.size},beforeHideMethod:{type:Function,default:()=>c.modal.beforeHideMethod},slots:Object},emits:["update:modelValue","show","hide","before-hide","close","confirm","cancel","zoom"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=Fo(e),i=(0,g.reactive)({inited:!1,visible:!1,contentVisible:!1,modalTop:0,modalZindex:0,zoomLocat:null,firstOpen:!0}),s=(0,g.ref)(),u=(0,g.ref)(),d=(0,g.ref)(),p=(0,g.ref)(),m={refElem:s},h={xID:l,props:e,context:t,reactData:i,getRefMaps:()=>m};let v={};const x=(0,g.computed)((()=>"message"===e.type)),b=()=>{const e=u.value;return e},w=()=>{const{width:t,height:o}=e,n=b();return n.style.width=`${t?isNaN(t)?t:`${t}px`:""}`,n.style.height=`${o?isNaN(o)?o:`${o}px`:""}`,(0,g.nextTick)()},C=()=>{const{zIndex:t}=e,{modalZindex:o}=i;t?i.modalZindex=t:o<H()&&(i.modalZindex=j())},y=()=>(0,g.nextTick)().then((()=>{const{position:t}=e,o=a().toNumber(e.marginSize),n=b(),l=document.documentElement.clientWidth||document.body.clientWidth,r=document.documentElement.clientHeight||document.body.clientHeight,i="center"===t,{top:s,left:c}=a().isString(t)?{top:t,left:t}:Object.assign({},t),u=i||"center"===s,d=i||"center"===c;let p="",f="";f=c&&!d?isNaN(c)?c:`${c}px`:`${Math.max(o,l/2-n.offsetWidth/2)}px`,p=s&&!u?isNaN(s)?s:`${s}px`:`${Math.max(o,r/2-n.offsetHeight/2)}px`,n.style.top=p,n.style.left=f})),T=()=>{(0,g.nextTick)((()=>{let e=0;_o.forEach((t=>{const o=t.getBox();e+=a().toNumber(t.props.top),t.reactData.modalTop=e,e+=o.clientHeight}))}))},E=()=>{_o.indexOf(h)>-1&&a().remove(_o,(e=>e===h)),T()},S=t=>{const{remember:o,beforeHideMethod:l}=e,{visible:r}=i,s=x.value,c={type:t};return r&&Promise.resolve(l?l(c):null).then((e=>{a().isError(e)||(s&&E(),i.contentVisible=!1,o||(i.zoomLocat=null),a().remove(Vo,(e=>e===h)),v.dispatchEvent("before-hide",c),setTimeout((()=>{i.visible=!1,n("update:modelValue",!1),v.dispatchEvent("hide",c)}),200))})).catch((e=>e)),(0,g.nextTick)()},k=e=>{const t="close";v.dispatchEvent(t,{type:t},e),S(t)},R=e=>{const t="confirm";v.dispatchEvent(t,{type:t},e),S(t)},O=e=>{const t="cancel";v.dispatchEvent(t,{type:t},e),S(t)},M=e=>{const t=c.version,o=a().toStringJSON(localStorage.getItem(e)||"");return o&&o._v===t?o:{_v:t}},$=()=>{const{id:t,remember:o,storage:n,storageKey:l}=e;return!!(t&&o&&n&&M(l)[t])},I=()=>{const{id:t,remember:o,storage:n,storageKey:l}=e;if(t&&o&&n){const e=M(l)[t];if(e){const t=b(),[o,n,l,r,a,s,c,u]=e.split(",");o&&(t.style.left=`${o}px`),n&&(t.style.top=`${n}px`),l&&(t.style.width=`${l}px`),r&&(t.style.height=`${r}px`),a&&s&&(i.zoomLocat={left:a,top:s,width:c,height:u})}}},D=()=>{-1===_o.indexOf(h)&&_o.push(h),T()},F=()=>{const{id:t,remember:o,storage:n,storageKey:l}=e,{zoomLocat:r}=i;if(t&&o&&n){const e=b(),o=M(l);o[t]=[e.style.left,e.style.top,e.style.width,e.style.height].concat(r?[r.left,r.top,r.width,r.height]:[]).map((e=>e?a().toNumber(e):"")).join(","),localStorage.setItem(l,a().toJSONString(o))}},N=()=>(0,g.nextTick)().then((()=>{if(!i.zoomLocat){const t=Math.max(0,a().toNumber(e.marginSize)),o=b(),{visibleHeight:n,visibleWidth:l}=ne();i.zoomLocat={top:o.offsetTop,left:o.offsetLeft,width:o.offsetWidth+(o.style.width?0:1),height:o.offsetHeight+(o.style.height?0:1)},Object.assign(o.style,{top:`${t}px`,left:`${t}px`,width:l-2*t+"px",height:n-2*t+"px"}),F()}})),L=()=>{const{duration:t,remember:o,showFooter:l}=e,{inited:r,visible:s}=i,c=x.value;return r||(i.inited=!0),s||(o||w(),i.visible=!0,i.contentVisible=!1,C(),Vo.push(h),setTimeout((()=>{i.contentVisible=!0,(0,g.nextTick)((()=>{if(l){const e=d.value,t=p.value,o=e||t;o&&o.focus()}const e="",t={type:e};n("update:modelValue",!0),v.dispatchEvent("show",t)}))}),10),c?(D(),-1!==t&&setTimeout((()=>S("close")),a().toNumber(t))):(0,g.nextTick)((()=>{const{fullscreen:t}=e,{firstOpen:n}=i;o&&!n||y().then((()=>{setTimeout((()=>y()),20)})),n?(i.firstOpen=!1,$()?I():t&&(0,g.nextTick)((()=>N()))):t&&(0,g.nextTick)((()=>N()))}))),(0,g.nextTick)()},A=t=>{const o=s.value;if(e.maskClosable&&t.target===o){const e="mask";S(e)}},P=e=>{const t=wo(e,go.ESCAPE);if(t){const e=a().max(Vo,(e=>e.reactData.modalZindex));e&&setTimeout((()=>{e===h&&e.props.escClosable&&S("exit")}),10)}},V=()=>!!i.zoomLocat,_=()=>(0,g.nextTick)().then((()=>{const{zoomLocat:e}=i;if(e){const t=b();i.zoomLocat=null,Object.assign(t.style,{top:`${e.top}px`,left:`${e.left}px`,width:`${e.width}px`,height:`${e.height}px`}),F()}})),B=()=>i.zoomLocat?_().then((()=>V())):N().then((()=>V())),W=e=>{const{zoomLocat:t}=i,o={type:t?"revert":"max"};return B().then((()=>{v.dispatchEvent("zoom",o,e)}))},q=()=>{const e=x.value;if(!e){const e=b();if(e)return{top:e.offsetTop,left:e.offsetLeft}}return null},U=(e,t)=>{const o=x.value;if(!o){const o=b();a().isNumber(e)&&(o.style.top=`${e}px`),a().isNumber(t)&&(o.style.left=`${t}px`)}return(0,g.nextTick)()},X=()=>{const{modalZindex:e}=i;Vo.some((t=>t.reactData.visible&&t.reactData.modalZindex>e))&&C()},Y=t=>{const{remember:o,storage:n}=e,{zoomLocat:l}=i,r=a().toNumber(e.marginSize),s=b();if(!l&&0===t.button&&!ce(t,s,"trigger--btn").flag){t.preventDefault();const e=document.onmousemove,l=document.onmouseup,a=t.clientX-s.offsetLeft,i=t.clientY-s.offsetTop,{visibleHeight:c,visibleWidth:u}=ne();document.onmousemove=e=>{e.preventDefault();const t=s.offsetWidth,o=s.offsetHeight,n=r,l=u-t-r-1,d=r,p=c-o-r-1;let f=e.clientX-a,m=e.clientY-i;f>l&&(f=l),f<n&&(f=n),m>p&&(m=p),m<d&&(m=d),s.style.left=`${f}px`,s.style.top=`${m}px`,s.className=s.className.replace(/\s?is--drag/,"")+" is--drag"},document.onmouseup=()=>{document.onmousemove=e,document.onmouseup=l,o&&n&&(0,g.nextTick)((()=>{F()})),setTimeout((()=>{s.className=s.className.replace(/\s?is--drag/,"")}),50)}}},G=t=>{t.preventDefault();const{remember:o,storage:n}=e,{visibleHeight:l,visibleWidth:r}=ne(),s=a().toNumber(e.marginSize),c=t.target,u=c.getAttribute("type"),d=a().toNumber(e.minWidth),p=a().toNumber(e.minHeight),f=r,m=l,h=b(),g=document.onmousemove,x=document.onmouseup,w=h.clientWidth,C=h.clientHeight,y=t.clientX,T=t.clientY,E=h.offsetTop,S=h.offsetLeft,k={type:"resize"};document.onmousemove=e=>{let t,a,i,c;switch(e.preventDefault(),u){case"wl":t=y-e.clientX,i=t+w,S-t>s&&i>d&&(h.style.width=`${i<f?i:f}px`,h.style.left=S-t+"px");break;case"swst":t=y-e.clientX,a=T-e.clientY,i=t+w,c=a+C,S-t>s&&i>d&&(h.style.width=`${i<f?i:f}px`,h.style.left=S-t+"px"),E-a>s&&c>p&&(h.style.height=`${c<m?c:m}px`,h.style.top=E-a+"px");break;case"swlb":t=y-e.clientX,a=e.clientY-T,i=t+w,c=a+C,S-t>s&&i>d&&(h.style.width=`${i<f?i:f}px`,h.style.left=S-t+"px"),E+c+s<l&&c>p&&(h.style.height=`${c<m?c:m}px`);break;case"st":a=T-e.clientY,c=C+a,E-a>s&&c>p&&(h.style.height=`${c<m?c:m}px`,h.style.top=E-a+"px");break;case"wr":t=e.clientX-y,i=t+w,S+i+s<r&&i>d&&(h.style.width=`${i<f?i:f}px`);break;case"sest":t=e.clientX-y,a=T-e.clientY,i=t+w,c=a+C,S+i+s<r&&i>d&&(h.style.width=`${i<f?i:f}px`),E-a>s&&c>p&&(h.style.height=`${c<m?c:m}px`,h.style.top=E-a+"px");break;case"selb":t=e.clientX-y,a=e.clientY-T,i=t+w,c=a+C,S+i+s<r&&i>d&&(h.style.width=`${i<f?i:f}px`),E+c+s<l&&c>p&&(h.style.height=`${c<m?c:m}px`);break;case"sb":a=e.clientY-T,c=a+C,E+c+s<l&&c>p&&(h.style.height=`${c<m?c:m}px`);break}h.className=h.className.replace(/\s?is--drag/,"")+" is--drag",o&&n&&F(),v.dispatchEvent("zoom",k,e)},document.onmouseup=()=>{i.zoomLocat=null,document.onmousemove=g,document.onmouseup=x,setTimeout((()=>{h.className=h.className.replace(/\s?is--drag/,"")}),50)}},K=()=>{const{slots:t={},showClose:n,showZoom:l,title:r}=e,{zoomLocat:a}=i,s=o.title||t.title,u=o.corner||t.corner,d=[(0,g.h)("div",{class:"vxe-modal--header-title"},s?We(s({$modal:h})):r?z(r):c.i18n("vxe.alert.title"))],p=[];return u&&p.push((0,g.h)("span",{class:"vxe-modal--corner-warpper"},We(u({$modal:h})))),l&&p.push((0,g.h)("i",{class:["vxe-modal--zoom-btn","trigger--btn",a?c.icon.MODAL_ZOOM_OUT:c.icon.MODAL_ZOOM_IN],title:c.i18n("vxe.modal.zoom"+(a?"Out":"In")),onClick:W})),n&&p.push((0,g.h)("i",{class:["vxe-modal--close-btn","trigger--btn",c.icon.MODAL_CLOSE],title:c.i18n("vxe.modal.close"),onClick:k})),d.push((0,g.h)("div",{class:"vxe-modal--header-right"},p)),d},Z=()=>{const{slots:t={},showZoom:n,draggable:l}=e,r=x.value,a=o.header||t.header,s=[];if(e.showHeader){const t={};l&&(t.onMousedown=Y),n&&e.dblclickZoom&&"modal"===e.type&&(t.onDblclick=W),s.push((0,g.h)("div",{class:["vxe-modal--header",{"is--draggable":l,"is--ellipsis":!r&&e.showTitleOverflow}],...t},a?!i.inited||e.destroyOnClose&&!i.visible?[]:We(a({$modal:h})):K()))}return s},J=()=>{const{slots:t={},status:n,message:l}=e,r=e.content||l,a=x.value,s=o.default||t.default,u=[];return n&&u.push((0,g.h)("div",{class:"vxe-modal--status-wrapper"},[(0,g.h)("i",{class:["vxe-modal--status-icon",e.iconStatus||c.icon[`MODAL_${n}`.toLocaleUpperCase()]]})])),u.push((0,g.h)("div",{class:"vxe-modal--content"},s?!i.inited||e.destroyOnClose&&!i.visible?[]:We(s({$modal:h})):z(r))),a||u.push((0,g.h)(Po,{class:"vxe-modal--loading",modelValue:e.loading})),[(0,g.h)("div",{class:"vxe-modal--body"},u)]},Q=()=>{const{type:t}=e,o=[];return"confirm"===t&&o.push((0,g.h)(No,{ref:p,content:e.cancelButtonText||c.i18n("vxe.button.cancel"),onClick:O})),o.push((0,g.h)(No,{ref:d,status:"primary",content:e.confirmButtonText||c.i18n("vxe.button.confirm"),onClick:R})),o},ee=()=>{const{slots:t={}}=e,n=x.value,l=o.footer||t.footer,r=[];return e.showFooter&&r.push((0,g.h)("div",{class:"vxe-modal--footer"},l?!i.inited||e.destroyOnClose&&!i.visible?[]:We(l({$modal:h})):Q())),!n&&e.resize&&r.push((0,g.h)("span",{class:"vxe-modal--resize"},["wl","wr","swst","sest","st","swlb","selb","sb"].map((e=>(0,g.h)("span",{class:`${e}-resize`,type:e,onMousedown:G}))))),r};v={dispatchEvent(e,t,o){n(e,Object.assign({$modal:h,$event:o},t))},open:L,close(){return S("close")},getBox:b,getPosition:q,setPosition:U,isMaximized:V,zoom:B,maximize:N,revert:_},Object.assign(h,v),(0,g.watch)((()=>e.width),w),(0,g.watch)((()=>e.height),w),(0,g.watch)((()=>e.modelValue),(e=>{e?L():S("model")})),(0,g.onMounted)((()=>{(0,g.nextTick)((()=>{e.storage&&!e.id&&f("vxe.error.reqProp",["modal.id"]),e.modelValue&&L(),w()})),e.escClosable&&yo.on(h,"keydown",P)})),(0,g.onUnmounted)((()=>{yo.off(h,"keydown"),E()}));const te=()=>{const{className:t,type:o,animat:n,loading:l,status:a,lockScroll:c,lockView:d,mask:p,resize:f}=e,{inited:m,zoomLocat:h,modalTop:v,contentVisible:x,visible:b}=i,w=r.value;return(0,g.h)(g.Teleport,{to:"body",disabled:!e.transfer||!m},[(0,g.h)("div",{ref:s,class:["vxe-modal--wrapper",`type--${o}`,t||"",{[`size--${w}`]:w,[`status--${a}`]:a,"is--animat":n,"lock--scroll":c,"lock--view":d,"is--resize":f,"is--mask":p,"is--maximize":h,"is--visible":x,"is--active":b,"is--loading":l}],style:{zIndex:i.modalZindex,top:v?`${v}px`:null},onClick:A},[(0,g.h)("div",{ref:u,class:"vxe-modal--box",onMousedown:X},Z().concat(J(),ee()))])])};return h.renderVN=te,h},render(){return this.renderVN()}});function Ho(e){if(e){const t=new Date;let o=0,n=0,l=0;if(a().isDate(e))o=e.getHours(),n=e.getMinutes(),l=e.getSeconds();else{e=a().toValueString(e);const t=e.match(/^(\d{1,2})(:(\d{1,2}))?(:(\d{1,2}))?/);t&&(o=a().toNumber(t[1]),n=a().toNumber(t[3]),l=a().toNumber(t[5]))}return t.setHours(o),t.setMinutes(n),t.setSeconds(l),t}return new Date("")}function Bo(e){const t=e.getMonth();return t<3?1:t<6?2:t<9?3:4}function zo(e){return a().isString(e)?e.replace(/,/g,""):e}function Wo(e,t){return/^-/.test(""+e)?a().toFixed(a().ceil(e,t),t):a().toFixed(a().floor(e,t),t)}const qo=12,Uo=20,Xo=8;var Yo=(0,g.defineComponent)({name:"VxeInput",props:{modelValue:[String,Number,Date],immediate:{type:Boolean,default:!0},name:String,type:{type:String,default:"text"},clearable:{type:Boolean,default:()=>c.input.clearable},readonly:Boolean,disabled:Boolean,placeholder:{type:String,default:()=>a().eqNull(c.input.placeholder)?c.i18n("vxe.base.pleaseInput"):c.input.placeholder},maxlength:[String,Number],autocomplete:{type:String,default:"off"},align:String,form:String,className:String,size:{type:String,default:()=>c.input.size||c.size},multiple:Boolean,showWordCount:Boolean,countMethod:Function,min:{type:[String,Number],default:null},max:{type:[String,Number],default:null},step:[String,Number],exponential:{type:Boolean,default:()=>c.input.exponential},controls:{type:Boolean,default:()=>c.input.controls},digits:{type:[String,Number],default:()=>c.input.digits},startDate:{type:[String,Number,Date],default:()=>c.input.startDate},endDate:{type:[String,Number,Date],default:()=>c.input.endDate},minDate:[String,Number,Date],maxDate:[String,Number,Date],startWeek:Number,startDay:{type:[String,Number],default:()=>c.input.startDay},labelFormat:{type:String,default:()=>c.input.labelFormat},valueFormat:{type:String,default:()=>c.input.valueFormat},editable:{type:Boolean,default:!0},festivalMethod:{type:Function,default:()=>c.input.festivalMethod},disabledMethod:{type:Function,default:()=>c.input.disabledMethod},selectDay:{type:[String,Number],default:()=>c.input.selectDay},prefixIcon:String,suffixIcon:String,placement:String,transfer:{type:Boolean,default:()=>c.input.transfer}},emits:["update:modelValue","input","change","keydown","keyup","wheel","click","focus","blur","clear","search-click","toggle-visible","prev-number","next-number","prefix-click","suffix-click","date-prev","date-today","date-next"],setup(e,t){const{slots:o,emit:n}=t,l=(0,g.inject)("$xeform",null),r=(0,g.inject)("$xeformiteminfo",null),i=a().uniqueId(),s=Fo(e),u=(0,g.reactive)({inited:!1,panelIndex:0,showPwd:!1,visiblePanel:!1,animatVisible:!1,panelStyle:null,panelPlacement:"",isActivated:!1,inputValue:e.modelValue,datetimePanelValue:null,datePanelValue:null,datePanelLabel:"",datePanelType:"day",selectMonth:null,currentDate:null}),d=(0,g.ref)(),p=(0,g.ref)(),f=(0,g.ref)(),m=(0,g.ref)(),h={refElem:d,refInput:p},v={xID:i,props:e,context:t,reactData:u,getRefMaps:()=>h};let x={};const b=(t,o)=>{const{type:n}=e;return"time"===n?Ho(t):a().toStringDate(t,o)},w=(0,g.computed)((()=>{const{type:t}=e;return"time"===t||"datetime"===t})),C=(0,g.computed)((()=>["number","integer","float"].indexOf(e.type)>-1)),y=(0,g.computed)((()=>a().getSize(u.inputValue))),T=(0,g.computed)((()=>{const t=y.value;return e.maxlength&&t>a().toNumber(e.maxlength)})),E=(0,g.computed)((()=>{const t=w.value;return t||["date","week","month","quarter","year"].indexOf(e.type)>-1})),S=(0,g.computed)((()=>"password"===e.type)),k=(0,g.computed)((()=>"search"===e.type)),R=(0,g.computed)((()=>a().toInteger(e.digits)||1)),O=(0,g.computed)((()=>{const{type:t}=e,o=R.value,n=e.step;return"integer"===t?a().toInteger(n)||1:"float"===t?a().toNumber(n)||1/Math.pow(10,o):a().toNumber(n)||1})),M=(0,g.computed)((()=>{const{type:t}=e,o=C.value,n=E.value,l=S.value;return e.clearable&&(l||o||n||"text"===t||"search"===t)})),$=(0,g.computed)((()=>e.startDate?a().toStringDate(e.startDate):null)),I=(0,g.computed)((()=>e.endDate?a().toStringDate(e.endDate):null)),D=(0,g.computed)((()=>["date","week","month","quarter","year"].includes(e.type))),F=(0,g.computed)((()=>{const{modelValue:t,multiple:o}=e,n=E.value,l=A.value;return o&&t&&n?a().toValueString(t).split(",").map((e=>{const t=b(e,l);return a().isValidDate(t)?t:null})):[]})),N=(0,g.computed)((()=>{const e=F.value,t=A.value;return e.map((e=>a().toDateString(e,t)))})),L=(0,g.computed)((()=>{const e=F.value,t=q.value;return e.map((e=>a().toDateString(e,t))).join(", ")})),A=(0,g.computed)((()=>{const{type:t}=e;return"time"===t?"HH:mm:ss":e.valueFormat||("datetime"===t?"yyyy-MM-dd HH:mm:ss":"yyyy-MM-dd")})),P=(0,g.computed)((()=>{const{modelValue:t}=e,o=E.value,n=A.value;let l=null;if(t&&o){const e=b(t,n);a().isValidDate(e)&&(l=e)}return l})),V=(0,g.computed)((()=>{const e=$.value,{selectMonth:t}=u;return!(!t||!e)&&t<=e})),_=(0,g.computed)((()=>{const e=I.value,{selectMonth:t}=u;return!(!t||!e)&&t>=e})),B=(0,g.computed)((()=>{const{datetimePanelValue:e}=u;return e?a().toDateString(e,"HH:mm:ss"):""})),W=(0,g.computed)((()=>{const e=P.value,t=w.value;return e&&t?1e3*(3600*e.getHours()+60*e.getMinutes()+e.getSeconds()):0})),q=(0,g.computed)((()=>{const t=E.value;return t?e.labelFormat||c.i18n(`vxe.input.date.labelFormat.${e.type}`):null})),U=(0,g.computed)((()=>{const{selectMonth:e,currentDate:t}=u,o=[];if(e&&t){const n=t.getFullYear(),l=e.getFullYear(),r=new Date(l-l%qo,0,1);for(let e=-4;e<qo+4;e++){const t=a().getWhatYear(r,e,"first"),l=t.getFullYear();o.push({date:t,isCurrent:!0,isPrev:e<0,isNow:n===l,isNext:e>=qo,year:l})}}return o})),X=(0,g.computed)((()=>{const e=E.value;if(e){const{datePanelType:e,selectMonth:t}=u,o=U.value;let n,l="";return t&&(l=t.getFullYear(),n=t.getMonth()+1),"quarter"===e?c.i18n("vxe.input.date.quarterLabel",[l]):"month"===e?c.i18n("vxe.input.date.monthLabel",[l]):"year"===e?o.length?`${o[0].year} - ${o[o.length-1].year}`:"":c.i18n("vxe.input.date.dayLabel",[l,n?c.i18n(`vxe.input.date.m${n}`):"-"])}return""})),Y=(0,g.computed)((()=>{const{startDay:t,startWeek:o}=e;return a().toNumber(a().isNumber(t)||a().isString(t)?t:o)})),G=(0,g.computed)((()=>{const e=[],t=E.value;if(t){let t=Y.value;e.push(t);for(let o=0;o<6;o++)t>=6?t=0:t++,e.push(t)}return e})),K=(0,g.computed)((()=>{const e=E.value;if(e){const e=G.value;return e.map((e=>({value:e,label:c.i18n(`vxe.input.date.weeks.w${e}`)})))}return[]})),Z=(0,g.computed)((()=>{const e=E.value;if(e){const e=K.value;return[{label:c.i18n("vxe.input.date.weeks.w")}].concat(e)}return[]})),J=(0,g.computed)((()=>{const e=U.value;return a().chunk(e,4)})),Q=(0,g.computed)((()=>{const{selectMonth:e,currentDate:t}=u,o=[];if(e&&t){const n=t.getFullYear(),l=Bo(t),r=a().getWhatYear(e,0,"first"),i=r.getFullYear();for(let e=-2;e<Xo-2;e++){const t=a().getWhatQuarter(r,e),s=t.getFullYear(),c=Bo(t),u=s<i;o.push({date:t,isPrev:u,isCurrent:s===i,isNow:s===n&&c===l,isNext:!u&&s>i,quarter:c})}}return o})),te=(0,g.computed)((()=>{const e=Q.value;return a().chunk(e,2)})),oe=(0,g.computed)((()=>{const{selectMonth:e,currentDate:t}=u,o=[];if(e&&t){const n=t.getFullYear(),l=t.getMonth(),r=a().getWhatYear(e,0,"first").getFullYear();for(let t=-4;t<Uo-4;t++){const i=a().getWhatYear(e,0,t),s=i.getFullYear(),c=i.getMonth(),u=s<r;o.push({date:i,isPrev:u,isCurrent:s===r,isNow:s===n&&c===l,isNext:!u&&s>r,month:c})}}return o})),ne=(0,g.computed)((()=>{const e=oe.value;return a().chunk(e,4)})),le=(0,g.computed)((()=>{const{selectMonth:e,currentDate:t}=u,o=[];if(e&&t){const n=W.value,l=G.value,r=t.getFullYear(),i=t.getMonth(),s=t.getDate(),c=e.getFullYear(),u=e.getMonth(),d=e.getDay(),p=-l.indexOf(d),f=new Date(a().getWhatDay(e,p).getTime()+n);for(let t=0;t<42;t++){const n=a().getWhatDay(f,t),l=n.getFullYear(),d=n.getMonth(),p=n.getDate(),m=n<e;o.push({date:n,isPrev:m,isCurrent:l===c&&d===u,isNow:l===r&&d===i&&p===s,isNext:!m&&u!==d,label:p})}}return o})),re=(0,g.computed)((()=>{const e=le.value;return a().chunk(e,7)})),ae=(0,g.computed)((()=>{const e=re.value,t=Y.value;return e.map((e=>{const o=e[0],n={date:o.date,isWeekNumber:!0,isPrev:!1,isCurrent:!1,isNow:!1,isNext:!1,label:a().getYearWeek(o.date,t)};return[n].concat(e)}))})),ie=(0,g.computed)((()=>{const e=[],t=w.value;if(t)for(let o=0;o<24;o++)e.push({value:o,label:(""+o).padStart(2,"0")});return e})),se=(0,g.computed)((()=>{const e=[],t=w.value;if(t)for(let o=0;o<60;o++)e.push({value:o,label:(""+o).padStart(2,"0")});return e})),ue=(0,g.computed)((()=>{const e=se.value;return e})),pe=(0,g.computed)((()=>{const{type:t,readonly:o,editable:n,multiple:l}=e;return o||l||!n||"week"===t||"quarter"===t})),fe=(0,g.computed)((()=>{const{type:t}=e,{showPwd:o}=u,n=C.value,l=E.value,r=S.value;return l||n||r&&o||"number"===t?"text":t})),me=(0,g.computed)((()=>{const{placeholder:t}=e;return t?z(t):""})),he=(0,g.computed)((()=>{const{maxlength:t}=e,o=C.value;return o&&!a().toNumber(t)?16:t})),ge=(0,g.computed)((()=>{const{type:t,immediate:o}=e;return o||!("text"===t||"number"===t||"integer"===t||"float"===t)})),ve=(0,g.computed)((()=>{const{type:t}=e,{inputValue:o}=u,n=C.value;return n?"integer"===t?a().toInteger(zo(o)):a().toNumber(zo(o)):0})),xe=(0,g.computed)((()=>{const{min:t}=e,{inputValue:o}=u,n=C.value,l=ve.value;return!(!o&&0!==o||!n||null===t)&&l<=a().toNumber(t)})),be=(0,g.computed)((()=>{const{max:t}=e,{inputValue:o}=u,n=C.value,l=ve.value;return!(!o&&0!==o||!n||null===t)&&l>=a().toNumber(t)})),we=t=>{const{type:o,exponential:n}=e,l=he.value,r=R.value,i="float"===o?Wo(t,r):a().toValueString(t);return!n||t!==i&&a().toValueString(t).toLowerCase()!==a().toNumber(i).toExponential()?i.slice(0,l):t},Ce=e=>{const{inputValue:t}=u;x.dispatchEvent(e.type,{value:t},e)},ye=(t,o)=>{u.inputValue=t,n("update:modelValue",t),x.dispatchEvent("input",{value:t},o),a().toValueString(e.modelValue)!==t&&(x.dispatchEvent("change",{value:t},o),l&&r&&l.triggerItemEvent(o,r.itemConfig.field,t))},Te=(e,t)=>{const o=E.value,n=ge.value;u.inputValue=e,o||(n?ye(e,t):x.dispatchEvent("input",{value:e},t))},Ee=e=>{const t=e.target,o=t.value;Te(o,e)},Se=e=>{const t=ge.value;t||Ce(e)},ke=e=>{u.isActivated=!0;const t=E.value;t&&Ot(e),Ce(e)},Re=t=>{const{disabled:o}=e;if(!o){const{inputValue:e}=u;x.dispatchEvent("prefix-click",{value:e},t)}};let Oe;const Me=()=>new Promise((e=>{u.visiblePanel=!1,Oe=window.setTimeout((()=>{u.animatVisible=!1,e()}),350)})),$e=(t,o)=>{const{type:n}=e,l=C.value,r=E.value;r&&Me(),(l||["text","search","password"].indexOf(n)>-1)&&focus(),x.dispatchEvent("clear",{value:o},t)},Ie=t=>{const{disabled:o}=e;if(!o)if(ee(t.currentTarget,"is--clear"))ye("",t),$e(t,"");else{const{inputValue:e}=u;x.dispatchEvent("suffix-click",{value:e},t)}},De=t=>{const{type:o}=e,{valueFormat:n}=e,l=q.value,r=Y.value;let i=null,s="";if(t&&(i=b(t,n)),a().isValidDate(i)){if(s=a().toDateString(i,l,{firstDay:r}),l&&"week"===o){const e=a().getWhatWeek(i,0,r,r);if(e.getFullYear()<i.getFullYear()){const e=l.indexOf("yyyy");if(e>-1){const t=Number(s.substring(e,e+4));t&&!isNaN(t)&&(s=s.replace(`${t}`,""+(t-1)))}}}}else i=null;u.datePanelValue=i,u.datePanelLabel=s},Fe=()=>{const t=E.value,{inputValue:o}=u;t&&(De(o),u.inputValue=e.multiple?L.value:u.datePanelLabel)},Ne=()=>{const{type:t}=e,{inputValue:o}=u,n=E.value,l=R.value;if(n)Fe();else if("float"===t&&o){const e=Wo(o,l);o!==e&&ye(e,{type:"init"})}},Le=t=>null===e.max||a().toNumber(t)<=a().toNumber(e.max),Ae=t=>null===e.min||a().toNumber(t)>=a().toNumber(e.min),Pe=()=>{u.inputValue=e.multiple?L.value:u.datePanelLabel},Ve=e=>{const t=a().getWhatMonth(e,0,"first");a().isEqual(t,u.selectMonth)||(u.selectMonth=t)},_e=t=>{const{modelValue:o,multiple:n}=e,{datetimePanelValue:l}=u,r=w.value,i=A.value,s=Y.value;if("week"===e.type){const o=a().toNumber(e.selectDay);t=a().getWhatWeek(t,0,o,s)}else r&&(t.setHours(l.getHours()),t.setMinutes(l.getMinutes()),t.setSeconds(l.getSeconds()));const c=a().toDateString(t,i,{firstDay:s});if(Ve(t),n){const e=N.value;if(r){const e=[...F.value],o=[],n=a().findIndexOf(e,(e=>a().isDateSame(t,e,"yyyyMMdd")));-1===n?e.push(t):e.splice(n,1),e.forEach((e=>{e&&(e.setHours(l.getHours()),e.setMinutes(l.getMinutes()),e.setSeconds(l.getSeconds()),o.push(e))})),ye(o.map((e=>a().toDateString(e,i))).join(","),{type:"update"})}else e.some((e=>a().isEqual(e,c)))?ye(e.filter((e=>!a().isEqual(e,c))).join(","),{type:"update"}):ye(e.concat([c]).join(","),{type:"update"})}else a().isEqual(o,c)||ye(c,{type:"update"})},je=()=>{const{type:t,min:o,max:n,exponential:l}=e,{inputValue:r,datetimePanelValue:i}=u,s=C.value,c=E.value,d=q.value,p=pe.value;if(!p)if(s){if(r){let e="integer"===t?a().toInteger(zo(r)):a().toNumber(zo(r));if(Ae(e)?Le(e)||(e=n):e=o,l){const t=a().toValueString(r).toLowerCase();t===a().toNumber(e).toExponential()&&(e=t)}ye(we(e),{type:"check"})}}else if(c)if(r){let e=b(r,d);if(a().isValidDate(e))if("time"===t)e=a().toDateString(e,d),r!==e&&ye(e,{type:"check"}),u.inputValue=e;else{let o=!1;const n=Y.value;if("datetime"===t){const t=P.value;r===a().toDateString(t,d)&&r===a().toDateString(e,d)||(o=!0,i.setHours(e.getHours()),i.setMinutes(e.getMinutes()),i.setSeconds(e.getSeconds()))}else o=!0;u.inputValue=a().toDateString(e,d,{firstDay:n}),o&&_e(e)}else Pe()}else ye("",{type:"check"})},He=e=>{const{inputValue:t}=u,o=ge.value;o||ye(t,e),je(),u.visiblePanel||(u.isActivated=!1),x.dispatchEvent("blur",{value:t},e)},Be=t=>{const{readonly:o,disabled:n}=e,{showPwd:l}=u;n||o||(u.showPwd=!l),x.dispatchEvent("toggle-visible",{visible:u.showPwd},t)},ze=e=>{x.dispatchEvent("search-click",{},e)},We=(t,o)=>{const{min:n,max:l,type:r}=e,{inputValue:i}=u,s=O.value,c="integer"===r?a().toInteger(zo(i)):a().toNumber(zo(i)),d=t?a().add(c,s):a().subtract(c,s);let p;p=Ae(d)?Le(d)?d:l:n,Te(we(p),o)};let qe;const Ue=t=>{const{readonly:o,disabled:n}=e,l=xe.value;clearTimeout(qe),n||o||l||We(!1,t),x.dispatchEvent("next-number",{},t)},Xe=e=>{qe=window.setTimeout((()=>{Ue(e),Xe(e)}),60)},Ye=t=>{const{readonly:o,disabled:n}=e,l=be.value;clearTimeout(qe),n||o||l||We(!0,t),x.dispatchEvent("prev-number",{},t)},Ge=e=>{const t=wo(e,go.ARROW_UP),o=wo(e,go.ARROW_DOWN);(t||o)&&(e.preventDefault(),t?Ye(e):Ue(e))},Ke=t=>{const{exponential:o,controls:n}=e,l=C.value;if(l){const e=t.ctrlKey,l=t.shiftKey,r=t.altKey,a=t.keyCode;e||l||r||!(wo(t,go.SPACEBAR)||(!o||69!==a)&&a>=65&&a<=90||a>=186&&a<=188||a>=191)||t.preventDefault(),n&&Ge(t)}Ce(t)},Ze=e=>{Ce(e)},Je=()=>{clearTimeout(qe)},Qe=e=>{qe=window.setTimeout((()=>{Ye(e),Qe(e)}),60)},et=e=>{if(Je(),0===e.button){const t=ee(e.currentTarget,"is--prev");t?Ye(e):Ue(e),qe=window.setTimeout((()=>{t?Qe(e):Xe(e)}),500)}},tt=t=>{const o=C.value;if(o&&e.controls&&u.isActivated){const e=t.deltaY;e>0?Ue(t):e<0&&Ye(t),t.preventDefault()}Ce(t)},ot=(e,t)=>{u.selectMonth=a().getWhatMonth(e,t,"first")},nt=()=>{const e=a().getWhatDay(Date.now(),0,"first");u.currentDate=e,ot(e,0)},lt=()=>{let{datePanelType:e}=u;e="month"===e||"quarter"===e?"year":"month",u.datePanelType=e},rt=t=>{const{type:o}=e,{datePanelType:n,selectMonth:l}=u,r=V.value;r||(u.selectMonth="year"===o?a().getWhatYear(l,-qo,"first"):"month"===o||"quarter"===o?"year"===n?a().getWhatYear(l,-qo,"first"):a().getWhatYear(l,-1,"first"):"year"===n?a().getWhatYear(l,-qo,"first"):"month"===n?a().getWhatYear(l,-1,"first"):a().getWhatMonth(l,-1,"first"),x.dispatchEvent("date-prev",{type:o},t))},at=t=>{nt(),e.multiple||(_e(u.currentDate),Me()),x.dispatchEvent("date-today",{type:e.type},t)},it=t=>{const{type:o}=e,{datePanelType:n,selectMonth:l}=u,r=_.value;r||(u.selectMonth="year"===o?a().getWhatYear(l,qo,"first"):"month"===o||"quarter"===o?"year"===n?a().getWhatYear(l,qo,"first"):a().getWhatYear(l,1,"first"):"year"===n?a().getWhatYear(l,qo,"first"):"month"===n?a().getWhatYear(l,1,"first"):a().getWhatMonth(l,1,"first"),x.dispatchEvent("date-next",{type:o},t))},st=t=>{const{disabledMethod:o}=e,{datePanelType:n}=u;return o&&o({type:n,viewType:n,date:t.date,$input:v})},ct=t=>{const{type:o,multiple:n}=e,{datePanelType:l}=u;"month"===o?"year"===l?(u.datePanelType="month",Ve(t)):(_e(t),n||Me()):"year"===o?(_e(t),n||Me()):"quarter"===o?"year"===l?(u.datePanelType="quarter",Ve(t)):(_e(t),n||Me()):"month"===l?(u.datePanelType="week"===o?o:"day",Ve(t)):"year"===l?(u.datePanelType="month",Ve(t)):(_e(t),"datetime"===o||n||Me())},ut=e=>{st(e)||ct(e.date)},dt=e=>{if(!st({date:e})){const t=le.value;t.some((t=>a().isDateSame(t.date,e,"yyyyMMdd")))||Ve(e),De(e)}},pt=e=>{if(!st({date:e})){const t=U.value;t.some((t=>a().isDateSame(t.date,e,"yyyy")))||Ve(e),De(e)}},ft=e=>{if(!st({date:e})){const t=Q.value;t.some((t=>a().isDateSame(t.date,e,"yyyyq")))||Ve(e),De(e)}},mt=e=>{if(!st({date:e})){const t=oe.value;t.some((t=>a().isDateSame(t.date,e,"yyyyMM")))||Ve(e),De(e)}},ht=e=>{if(!st(e)){const{datePanelType:t}=u;"month"===t?mt(e.date):"quarter"===t?ft(e.date):"year"===t?pt(e.date):dt(e.date)}},gt=e=>{if(e){const t=e.offsetHeight,o=e.parentNode;o.scrollTop=e.offsetTop-4*t}},vt=e=>{u.datetimePanelValue=new Date(u.datetimePanelValue.getTime()),gt(e.currentTarget)},xt=(e,t)=>{u.datetimePanelValue.setHours(t.value),vt(e)},bt=()=>{const{multiple:t}=e,{datetimePanelValue:o}=u,n=P.value,l=w.value;if(l){const e=A.value;if(t){const t=N.value;if(l){const t=[...F.value],n=[];t.forEach((e=>{e&&(e.setHours(o.getHours()),e.setMinutes(o.getMinutes()),e.setSeconds(o.getSeconds()),n.push(e))})),ye(n.map((t=>a().toDateString(t,e))).join(","),{type:"update"})}else ye(t.join(","),{type:"update"})}else _e(n||u.currentDate)}Me()},wt=(e,t)=>{u.datetimePanelValue.setMinutes(t.value),vt(e)},Ct=(e,t)=>{u.datetimePanelValue.setSeconds(t.value),vt(e)},yt=e=>{const{isActivated:t,datePanelValue:o,datePanelType:n}=u;if(t){e.preventDefault();const t=wo(e,go.ARROW_LEFT),l=wo(e,go.ARROW_UP),r=wo(e,go.ARROW_RIGHT),i=wo(e,go.ARROW_DOWN);if("year"===n){let e=a().getWhatYear(o||Date.now(),0,"first");t?e=a().getWhatYear(e,-1):l?e=a().getWhatYear(e,-4):r?e=a().getWhatYear(e,1):i&&(e=a().getWhatYear(e,4)),pt(e)}else if("quarter"===n){let e=a().getWhatQuarter(o||Date.now(),0,"first");t?e=a().getWhatQuarter(e,-1):l?e=a().getWhatQuarter(e,-2):r?e=a().getWhatQuarter(e,1):i&&(e=a().getWhatQuarter(e,2)),ft(e)}else if("month"===n){let e=a().getWhatMonth(o||Date.now(),0,"first");t?e=a().getWhatMonth(e,-1):l?e=a().getWhatMonth(e,-4):r?e=a().getWhatMonth(e,1):i&&(e=a().getWhatMonth(e,4)),mt(e)}else{let e=o||a().getWhatDay(Date.now(),0,"first");const n=Y.value;t?e=a().getWhatDay(e,-1):l?e=a().getWhatWeek(e,-1,n):r?e=a().getWhatDay(e,1):i&&(e=a().getWhatWeek(e,1,n)),dt(e)}}},Tt=e=>{const{isActivated:t}=u;if(t){const t=wo(e,go.PAGE_UP);e.preventDefault(),t?rt(e):it(e)}},Et=()=>{const{type:t}=e,o=w.value,n=P.value;["year","quarter","month","week"].indexOf(t)>-1?u.datePanelType=t:u.datePanelType="day",u.currentDate=a().getWhatDay(Date.now(),0,"first"),n?(ot(n,0),De(n)):nt(),o&&(u.datetimePanelValue=u.datePanelValue||a().getWhatDay(Date.now(),0,"first"),(0,g.nextTick)((()=>{const e=m.value;a().arrayEach(e.querySelectorAll("li.is--selected"),gt)})))},St=()=>{u.panelIndex<H()&&(u.panelIndex=j())},kt=()=>(0,g.nextTick)().then((()=>{const{transfer:t,placement:o}=e,{panelIndex:n}=u,l=p.value,r=f.value;if(l&&r){const e=l.offsetHeight,a=l.offsetWidth,i=r.offsetHeight,s=r.offsetWidth,c=5,d={zIndex:n},{boundingTop:p,boundingLeft:f,visibleHeight:m,visibleWidth:h}=de(l);let v="bottom";if(t){let t=f,n=p+e;"top"===o?(v="top",n=p-i):o||(n+i+c>m&&(v="top",n=p-i),n<c&&(v="bottom",n=p+e)),t+s+c>h&&(t-=t+s+c-h),t<c&&(t=c),Object.assign(d,{left:`${t}px`,top:`${n}px`,minWidth:`${a}px`})}else"top"===o?(v="top",d.bottom=`${e}px`):o||p+e+i>m&&p-e-i>c&&(v="top",d.bottom=`${e}px`);return u.panelStyle=d,u.panelPlacement=v,(0,g.nextTick)()}})),Rt=()=>{const{disabled:t}=e,{visiblePanel:o}=u,n=E.value;return t||o?(0,g.nextTick)():(u.inited||(u.inited=!0),clearTimeout(Oe),u.isActivated=!0,u.animatVisible=!0,n&&Et(),setTimeout((()=>{u.visiblePanel=!0}),10),St(),kt())},Ot=t=>{const{readonly:o}=e;o||(t.preventDefault(),Rt())},Mt=e=>{Ce(e)},$t=t=>{const{disabled:o}=e,{visiblePanel:n,isActivated:l}=u,r=E.value,a=d.value,i=f.value;!o&&l&&(u.isActivated=ce(t,a).flag||ce(t,i).flag,u.isActivated||(r?n&&(Me(),je()):je()))},It=t=>{const{clearable:o,disabled:n}=e,{visiblePanel:l}=u,r=E.value;if(!n){const e=wo(t,go.TAB),n=wo(t,go.DELETE),a=wo(t,go.ESCAPE),i=wo(t,go.ENTER),s=wo(t,go.ARROW_LEFT),c=wo(t,go.ARROW_UP),d=wo(t,go.ARROW_RIGHT),p=wo(t,go.ARROW_DOWN),f=wo(t,go.PAGE_UP),m=wo(t,go.PAGE_DOWN),h=s||c||d||p;let g=u.isActivated;e?(g&&je(),g=!1,u.isActivated=g):h?r&&g&&(l?yt(t):(c||p)&&Ot(t)):i?r&&(l?u.datePanelValue?ct(u.datePanelValue):Me():g&&Ot(t)):(f||m)&&r&&g&&Tt(t),e||a?l&&Me():n&&o&&g&&$e(t,null)}},Dt=t=>{const{disabled:o}=e,{visiblePanel:n}=u;if(!o&&n){const e=f.value;ce(t,e).flag?kt():(Me(),je())}},Ft=()=>{const{isActivated:e,visiblePanel:t}=u;t?(Me(),je()):e&&je()},Nt=(t,o)=>{const{festivalMethod:n}=e;if(n){const{datePanelType:e}=u,l=n({type:e,viewType:e,date:t.date,$input:v}),r=l?a().isString(l)?{label:l}:l:{},i=r.extra?a().isString(r.extra)?{label:r.extra}:r.extra:null,s=[(0,g.h)("span",{class:["vxe-input--date-label",{"is-notice":r.notice}]},i&&i.label?[(0,g.h)("span",o),(0,g.h)("span",{class:["vxe-input--date-label--extra",i.important?"is-important":"",i.className],style:i.style},a().toValueString(i.label))]:o)],c=r.label;if(c){const e=a().toValueString(c).split(",");s.push((0,g.h)("span",{class:["vxe-input--date-festival",r.important?"is-important":"",r.className],style:r.style},[e.length>1?(0,g.h)("span",{class:["vxe-input--date-festival--overlap",`overlap--${e.length}`]},e.map((e=>(0,g.h)("span",e.substring(0,3))))):(0,g.h)("span",{class:"vxe-input--date-festival--label"},e[0].substring(0,3))]))}return s}return o},Lt=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=P.value,r=K.value,i=re.value,s=F.value,c="yyyyMMdd";return[(0,g.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,g.h)("thead",[(0,g.h)("tr",r.map((e=>(0,g.h)("th",e.label))))]),(0,g.h)("tbody",i.map((e=>(0,g.h)("tr",e.map((e=>(0,g.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":t?s.some((t=>a().isDateSame(t,e.date,c))):a().isDateSame(l,e.date,c),"is--hover":a().isDateSame(n,e.date,c)},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,e.label))))))))])]},At=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=P.value,r=Z.value,i=ae.value,s=F.value,c="yyyyMMdd";return[(0,g.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,g.h)("thead",[(0,g.h)("tr",r.map((e=>(0,g.h)("th",e.label))))]),(0,g.h)("tbody",i.map((e=>{const o=t?e.some((e=>s.some((t=>a().isDateSame(t,e.date,c))))):e.some((e=>a().isDateSame(l,e.date,c))),r=e.some((e=>a().isDateSame(n,e.date,c)));return(0,g.h)("tr",e.map((e=>(0,g.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":o,"is--hover":r},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,e.label)))))})))])]},Pt=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=P.value,r=ne.value,i=F.value,s="yyyyMM";return[(0,g.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,g.h)("tbody",r.map((e=>(0,g.h)("tr",e.map((e=>(0,g.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":t?i.some((t=>a().isDateSame(t,e.date,s))):a().isDateSame(l,e.date,s),"is--hover":a().isDateSame(n,e.date,s)},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,c.i18n(`vxe.input.date.months.m${e.month}`)))))))))])]},Vt=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=P.value,r=te.value,i=F.value,s="yyyyq";return[(0,g.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,g.h)("tbody",r.map((e=>(0,g.h)("tr",e.map((e=>(0,g.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":t?i.some((t=>a().isDateSame(t,e.date,s))):a().isDateSame(l,e.date,s),"is--hover":a().isDateSame(n,e.date,s)},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,c.i18n(`vxe.input.date.quarters.q${e.quarter}`)))))))))])]},_t=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=P.value,r=J.value,i=F.value,s="yyyy";return[(0,g.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,g.h)("tbody",r.map((e=>(0,g.h)("tr",e.map((e=>(0,g.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":t?i.some((t=>a().isDateSame(t,e.date,s))):a().isDateSame(l,e.date,s),"is--hover":a().isDateSame(n,e.date,s)},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,e.year))))))))])]},jt=()=>{const{datePanelType:e}=u;switch(e){case"week":return At();case"month":return Pt();case"quarter":return Vt();case"year":return _t()}return Lt()},Ht=()=>{const{multiple:t}=e,{datePanelType:o}=u,n=V.value,l=_.value,r=X.value;return[(0,g.h)("div",{class:"vxe-input--date-picker-header"},[(0,g.h)("div",{class:"vxe-input--date-picker-type-wrapper"},["year"===o?(0,g.h)("span",{class:"vxe-input--date-picker-label"},r):(0,g.h)("span",{class:"vxe-input--date-picker-btn",onClick:lt},r)]),(0,g.h)("div",{class:"vxe-input--date-picker-btn-wrapper"},[(0,g.h)("span",{class:["vxe-input--date-picker-btn vxe-input--date-picker-prev-btn",{"is--disabled":n}],onClick:rt},[(0,g.h)("i",{class:"vxe-icon-caret-left"})]),(0,g.h)("span",{class:"vxe-input--date-picker-btn vxe-input--date-picker-current-btn",onClick:at},[(0,g.h)("i",{class:"vxe-icon-dot"})]),(0,g.h)("span",{class:["vxe-input--date-picker-btn vxe-input--date-picker-next-btn",{"is--disabled":l}],onClick:it},[(0,g.h)("i",{class:"vxe-icon-caret-right"})]),t&&D.value?(0,g.h)("span",{class:"vxe-input--date-picker-btn vxe-input--date-picker-confirm-btn"},[(0,g.h)("button",{class:"vxe-input--date-picker-confirm",type:"button",onClick:bt},c.i18n("vxe.button.confirm"))]):null])]),(0,g.h)("div",{class:"vxe-input--date-picker-body"},jt())]},Bt=()=>{const{datetimePanelValue:e}=u,t=B.value,o=ie.value,n=se.value,l=ue.value;return[(0,g.h)("div",{class:"vxe-input--time-picker-header"},[(0,g.h)("span",{class:"vxe-input--time-picker-title"},t),(0,g.h)("button",{class:"vxe-input--time-picker-confirm",type:"button",onClick:bt},c.i18n("vxe.button.confirm"))]),(0,g.h)("div",{ref:m,class:"vxe-input--time-picker-body"},[(0,g.h)("ul",{class:"vxe-input--time-picker-hour-list"},o.map(((t,o)=>(0,g.h)("li",{key:o,class:{"is--selected":e&&e.getHours()===t.value},onClick:e=>xt(e,t)},t.label)))),(0,g.h)("ul",{class:"vxe-input--time-picker-minute-list"},n.map(((t,o)=>(0,g.h)("li",{key:o,class:{"is--selected":e&&e.getMinutes()===t.value},onClick:e=>wt(e,t)},t.label)))),(0,g.h)("ul",{class:"vxe-input--time-picker-second-list"},l.map(((t,o)=>(0,g.h)("li",{key:o,class:{"is--selected":e&&e.getSeconds()===t.value},onClick:e=>Ct(e,t)},t.label))))])]},zt=()=>{const{type:t,transfer:o}=e,{inited:n,animatVisible:l,visiblePanel:r,panelPlacement:a,panelStyle:i}=u,c=s.value,d=E.value,p=[];return d?("datetime"===t?p.push((0,g.h)("div",{class:"vxe-input--panel-layout-wrapper"},[(0,g.h)("div",{class:"vxe-input--panel-left-wrapper"},Ht()),(0,g.h)("div",{class:"vxe-input--panel-right-wrapper"},Bt())])):"time"===t?p.push((0,g.h)("div",{class:"vxe-input--panel-wrapper"},Bt())):p.push((0,g.h)("div",{class:"vxe-input--panel-wrapper"},Ht())),(0,g.h)(g.Teleport,{to:"body",disabled:!o||!n},[(0,g.h)("div",{ref:f,class:["vxe-table--ignore-clear vxe-input--panel",`type--${t}`,{[`size--${c}`]:c,"is--transfer":o,"animat--leave":l,"animat--enter":r}],placement:a,style:i},p)])):null},Wt=()=>{const e=be.value,t=xe.value;return(0,g.h)("span",{class:"vxe-input--number-suffix"},[(0,g.h)("span",{class:["vxe-input--number-prev is--prev",{"is--disabled":e}],onMousedown:et,onMouseup:Je,onMouseleave:Je},[(0,g.h)("i",{class:["vxe-input--number-prev-icon",c.icon.INPUT_PREV_NUM]})]),(0,g.h)("span",{class:["vxe-input--number-next is--next",{"is--disabled":t}],onMousedown:et,onMouseup:Je,onMouseleave:Je},[(0,g.h)("i",{class:["vxe-input--number-next-icon",c.icon.INPUT_NEXT_NUM]})])])},qt=()=>(0,g.h)("span",{class:"vxe-input--date-picker-suffix",onClick:Ot},[(0,g.h)("i",{class:["vxe-input--date-picker-icon",c.icon.INPUT_DATE]})]),Ut=()=>(0,g.h)("span",{class:"vxe-input--search-suffix",onClick:ze},[(0,g.h)("i",{class:["vxe-input--search-icon",c.icon.INPUT_SEARCH]})]),Xt=()=>{const{showPwd:e}=u;return(0,g.h)("span",{class:"vxe-input--password-suffix",onClick:Be},[(0,g.h)("i",{class:["vxe-input--password-icon",e?c.icon.INPUT_SHOW_PWD:c.icon.INPUT_PWD]})])},Yt=()=>{const{prefixIcon:t}=e,n=o.prefix,l=[];return n?l.push((0,g.h)("span",{class:"vxe-input--prefix-icon"},n({}))):t&&l.push((0,g.h)("i",{class:["vxe-input--prefix-icon",t]})),l.length?(0,g.h)("span",{class:"vxe-input--prefix",onClick:Re},l):null},Gt=()=>{const{disabled:t,suffixIcon:n}=e,{inputValue:l}=u,r=o.suffix,i=M.value,s=[];return r?s.push((0,g.h)("span",{class:"vxe-input--suffix-icon"},r({}))):n&&s.push((0,g.h)("i",{class:["vxe-input--suffix-icon",n]})),i&&s.push((0,g.h)("i",{class:["vxe-input--clear-icon",c.icon.INPUT_CLEAR]})),s.length?(0,g.h)("span",{class:["vxe-input--suffix",{"is--clear":i&&!t&&!(""===l||a().eqNull(l))}],onClick:Ie},s):null},Kt=()=>{const{controls:t}=e,o=C.value,n=E.value,l=S.value,r=k.value;let a;return l?a=Xt():o?t&&(a=Wt()):n?a=qt():r&&(a=Ut()),a?(0,g.h)("span",{class:"vxe-input--extra-suffix"},[a]):null};x={dispatchEvent(e,t,o){n(e,Object.assign({$input:v,$event:o},t))},focus(){const e=p.value;return u.isActivated=!0,e.focus(),(0,g.nextTick)()},blur(){const e=p.value;return e.blur(),u.isActivated=!1,(0,g.nextTick)()},select(){const e=p.value;return e.select(),u.isActivated=!1,(0,g.nextTick)()},showPanel:Rt,hidePanel:Me,updatePlacement:kt},Object.assign(v,x),(0,g.watch)((()=>e.modelValue),(e=>{u.inputValue=e,Fe()})),(0,g.watch)((()=>e.type),(()=>{Object.assign(u,{inputValue:e.modelValue,datetimePanelValue:null,datePanelValue:null,datePanelLabel:"",datePanelType:"day",selectMonth:null,currentDate:null}),Ne()})),(0,g.watch)(q,(()=>{const t=E.value;t&&(De(u.datePanelValue),u.inputValue=e.multiple?L.value:u.datePanelLabel)})),(0,g.nextTick)((()=>{yo.on(v,"mousewheel",Dt),yo.on(v,"mousedown",$t),yo.on(v,"keydown",It),yo.on(v,"blur",Ft)})),(0,g.onUnmounted)((()=>{Je(),yo.off(v,"mousewheel"),yo.off(v,"mousedown"),yo.off(v,"keydown"),yo.off(v,"blur")})),Ne();const Zt=()=>{const{className:t,controls:o,type:n,align:l,showWordCount:r,countMethod:a,name:i,disabled:c,readonly:f,autocomplete:m}=e,{inputValue:h,visiblePanel:v,isActivated:x}=u,b=s.value,w=T.value,C=y.value,S=E.value,k=pe.value,R=he.value,O=fe.value,M=me.value,$=[],I=Yt(),D=Gt();I&&$.push(I),$.push((0,g.h)("input",{ref:p,class:"vxe-input--inner",value:h,name:i,type:O,placeholder:M,maxlength:R,readonly:k,disabled:c,autocomplete:m,onKeydown:Ke,onKeyup:Ze,onWheel:tt,onClick:Mt,onInput:Ee,onChange:Se,onFocus:ke,onBlur:He})),D&&$.push(D),$.push(Kt()),S&&$.push(zt());let F=!1;return r&&["text","search"].includes(n)&&(F=!0,$.push((0,g.h)("span",{class:["vxe-input--count",{"is--error":w}]},a?`${a({value:h})}`:`${C}${R?`/${R}`:""}`))),(0,g.h)("div",{ref:d,class:["vxe-input",`type--${n}`,t,{[`size--${b}`]:b,[`is--${l}`]:l,"is--controls":o,"is--prefix":!!I,"is--suffix":!!D,"is--readonly":f,"is--visivle":v,"is--count":F,"is--disabled":c,"is--active":x}]},$)};return v.renderVN=Zt,v},render(){return this.renderVN()}}),Go=(0,g.defineComponent)({name:"VxeCheckbox",props:{modelValue:[String,Number,Boolean],label:{type:[String,Number],default:null},indeterminate:Boolean,title:[String,Number],checkedValue:{type:[String,Number,Boolean],default:!0},uncheckedValue:{type:[String,Number,Boolean],default:!1},content:[String,Number],disabled:Boolean,size:{type:String,default:()=>c.checkbox.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,g.inject)("$xeform",null),r=(0,g.inject)("$xeformiteminfo",null),i=a().uniqueId(),s={xID:i,props:e,context:t};let c={};const u=Fo(e),d=(0,g.inject)("$xecheckboxgroup",null),p=(0,g.computed)((()=>d?a().includes(d.props.modelValue,e.label):e.modelValue===e.checkedValue)),f=(0,g.computed)((()=>{if(e.disabled)return!0;if(d){const{props:e}=d,{computeIsMaximize:t}=d.getComputeMaps(),o=t.value,n=p.value;return e.disabled||o&&!n}return!1})),m=t=>{const{checkedValue:o,uncheckedValue:a}=e,i=f.value;if(!i){const i=t.target.checked,s=i?o:a,u={checked:i,value:s,label:e.label};d?d.handleChecked(u,t):(n("update:modelValue",s),c.dispatchEvent("change",u,t),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,s))}};c={dispatchEvent(e,t,o){n(e,Object.assign({$checkbox:s,$event:o},t))}},Object.assign(s,c);const h=()=>{const t=u.value,n=f.value,l=p.value,r=e.indeterminate;return(0,g.h)("label",{class:["vxe-checkbox",{[`size--${t}`]:t,"is--indeterminate":r,"is--disabled":n,"is--checked":l}],title:e.title},[(0,g.h)("input",{class:"vxe-checkbox--input",type:"checkbox",disabled:n,checked:l,onChange:m}),(0,g.h)("span",{class:["vxe-checkbox--icon",r?"vxe-icon-checkbox-indeterminate":l?"vxe-icon-checkbox-checked":"vxe-icon-checkbox-unchecked"]}),(0,g.h)("span",{class:"vxe-checkbox--label"},o.default?o.default({}):z(e.content))])};return s.renderVN=h,s},render(){return this.renderVN()}});function Ko(e){return!1!==e.visible}function Zo(){return a().uniqueId("opt_")}var Jo=(0,g.defineComponent)({name:"VxeSelect",props:{modelValue:null,clearable:Boolean,placeholder:{type:String,default:()=>a().eqNull(c.select.placeholder)?c.i18n("vxe.base.pleaseSelect"):c.select.placeholder},loading:Boolean,disabled:Boolean,multiple:Boolean,multiCharOverflow:{type:[Number,String],default:()=>c.select.multiCharOverflow},prefixIcon:String,placement:String,options:Array,optionProps:Object,optionGroups:Array,optionGroupProps:Object,optionConfig:Object,className:[String,Function],popupClassName:[String,Function],max:{type:[String,Number],default:null},size:{type:String,default:()=>c.select.size||c.size},filterable:Boolean,filterMethod:Function,remote:Boolean,remoteMethod:Function,emptyText:String,optionId:{type:String,default:()=>c.select.optionId},optionKey:Boolean,transfer:{type:Boolean,default:()=>c.select.transfer}},emits:["update:modelValue","change","clear","blur","focus"],setup(e,t){const{slots:o,emit:n}=t,l=(0,g.inject)("$xeform",null),r=(0,g.inject)("$xeformiteminfo",null),i=a().uniqueId(),s=Fo(e),u=(0,g.reactive)({inited:!1,staticOptions:[],fullGroupList:[],fullOptionList:[],visibleGroupList:[],visibleOptionList:[],remoteValueList:[],panelIndex:0,panelStyle:{},panelPlacement:null,currentOption:null,currentValue:null,visiblePanel:!1,animatVisible:!1,isActivated:!1,searchValue:"",searchLoading:!1}),d=(0,g.ref)(),p=(0,g.ref)(),f=(0,g.ref)(),m=(0,g.ref)(),h=(0,g.ref)(),v={refElem:d},x={xID:i,props:e,context:t,reactData:u,getRefMaps:()=>v};let b={};const w=(0,g.computed)((()=>e.optionProps||{})),C=(0,g.computed)((()=>e.optionGroupProps||{})),y=(0,g.computed)((()=>{const e=w.value;return e.label||"label"})),T=(0,g.computed)((()=>{const e=w.value;return e.value||"value"})),E=(0,g.computed)((()=>{const e=C.value;return e.label||"label"})),S=(0,g.computed)((()=>{const e=C.value;return e.options||"options"})),k=(0,g.computed)((()=>{const{modelValue:t,multiple:o,max:n}=e;return!(!o||!n)&&(t?t.length:0)>=a().toNumber(n)})),R=(0,g.computed)((()=>Object.assign({},c.select.optionConfig,e.optionConfig))),O=(0,g.computed)((()=>u.fullGroupList.some((e=>e.options&&e.options.length)))),M=(0,g.computed)((()=>a().toNumber(e.multiCharOverflow))),$=(e,t)=>e&&(a().isString(e)&&(e=o[e]||null),a().isFunction(e))?We(e(t)):[],I=e=>{const{fullOptionList:t,fullGroupList:o}=u,n=O.value,l=T.value;if(n)for(let r=0;r<o.length;r++){const t=o[r];if(t.options)for(let o=0;o<t.options.length;o++){const n=t.options[o];if(e===n[l])return n}}return t.find((t=>e===t[l]))},D=e=>{const{remoteValueList:t}=u,o=y.value,n=t.find((t=>e===t.key)),l=n?n.result:null;return a().toValueString(l?l[o]:e)},F=e=>{const t=y.value,o=I(e);return a().toValueString(o?o[t]:e)},N=(0,g.computed)((()=>{const{modelValue:t,multiple:o,remote:n}=e,l=M.value;if(t&&o){const e=a().isArray(t)?t:[t];return n?e.map((e=>D(e))).join(", "):e.map((e=>{const t=F(e);return l>0&&t.length>l?`${t.substring(0,l)}...`:t})).join(", ")}return n?D(t):F(t)})),L=()=>{const t=R.value;return t.keyField||e.optionId||"_X_OPTION_KEY"},A=e=>{const t=e[L()];return t?encodeURIComponent(t):""},P=()=>{const{filterable:t,filterMethod:o}=e,{fullOptionList:n,fullGroupList:l,searchValue:r}=u,a=O.value,i=E.value,s=y.value;return a?u.visibleGroupList=t&&o?l.filter((e=>Ko(e)&&o({group:e,option:null,searchValue:r}))):t?l.filter((e=>Ko(e)&&(!r||`${e[i]}`.indexOf(r)>-1))):l.filter(Ko):u.visibleOptionList=t&&o?n.filter((e=>Ko(e)&&o({group:null,option:e,searchValue:r}))):t?n.filter((e=>Ko(e)&&(!r||`${e[s]}`.indexOf(r)>-1))):n.filter(Ko),(0,g.nextTick)()},V=()=>{const{fullOptionList:e,fullGroupList:t}=u,o=S.value,n=L(),l=e=>{A(e)||(e[n]=Zo())};t.length?t.forEach((e=>{l(e),e[o]&&e[o].forEach(l)})):e.length&&e.forEach(l),P()},_=e=>{const t=T.value;e&&(u.currentOption=e,u.currentValue=e[t])},B=(e,t)=>(0,g.nextTick)().then((()=>{if(e){const o=m.value,n=h.value,l=n.querySelector(`[optid='${A(e)}']`);if(o&&l){const e=o.offsetHeight,n=5;t?l.offsetTop+l.offsetHeight-o.scrollTop>e&&(o.scrollTop=l.offsetTop+l.offsetHeight-e):(l.offsetTop+n<o.scrollTop||l.offsetTop+n>o.scrollTop+o.clientHeight)&&(o.scrollTop=l.offsetTop-n)}}})),q=()=>{u.panelIndex<H()&&(u.panelIndex=j())},U=()=>(0,g.nextTick)().then((()=>{const{transfer:t,placement:o}=e,{panelIndex:n}=u,l=d.value,r=h.value;if(r&&l){const e=l.offsetHeight,a=l.offsetWidth,i=r.offsetHeight,s=r.offsetWidth,c=5,d={zIndex:n},{boundingTop:p,boundingLeft:f,visibleHeight:m,visibleWidth:h}=de(l);let v="bottom";if(t){let t=f,n=p+e;"top"===o?(v="top",n=p-i):o||(n+i+c>m&&(v="top",n=p-i),n<c&&(v="bottom",n=p+e)),t+s+c>h&&(t-=t+s+c-h),t<c&&(t=c),Object.assign(d,{left:`${t}px`,top:`${n}px`,minWidth:`${a}px`})}else"top"===o?(v="top",d.bottom=`${e}px`):o||p+e+i>m&&p-e-i>c&&(v="top",d.bottom=`${e}px`);return u.panelStyle=d,u.panelPlacement=v,(0,g.nextTick)()}}));let X;const Y=()=>{const{loading:t,disabled:o,filterable:n}=e;t||o||(clearTimeout(X),u.inited||(u.inited=!0),u.isActivated=!0,u.animatVisible=!0,n&&P(),setTimeout((()=>{const{modelValue:t,multiple:o}=e,n=I(o&&t?t[0]:t);u.visiblePanel=!0,n&&(_(n),B(n)),re()}),10),q(),U())},G=()=>{u.searchValue="",u.searchLoading=!1,u.visiblePanel=!1,X=window.setTimeout((()=>{u.animatVisible=!1}),350)},K=(t,o)=>{o!==e.modelValue&&(n("update:modelValue",o),b.dispatchEvent("change",{value:o},t),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,o))},Z=(e,t)=>{u.remoteValueList=[],K(e,t),b.dispatchEvent("clear",{value:t},e)},J=(e,t)=>{Z(t,null),G()},Q=(t,o,n)=>{const{modelValue:l,multiple:r}=e,{remoteValueList:a}=u;if(r){let e;e=l?-1===l.indexOf(o)?l.concat([o]):l.filter((e=>e!==o)):[o];const r=a.find((e=>e.key===o));r?r.result=n:a.push({key:o,result:n}),K(t,e)}else u.remoteValueList=[{key:o,result:n}],K(t,o),G()},ee=t=>{const{disabled:o}=e,{visiblePanel:n}=u;if(!o&&n){const e=h.value;ce(t,e).flag?U():G()}},te=t=>{const{disabled:o}=e,{visiblePanel:n}=u;if(!o){const e=d.value,o=h.value;u.isActivated=ce(t,e).flag||ce(t,o).flag,n&&!u.isActivated&&G()}},oe=(e,t)=>{const{visibleOptionList:o,visibleGroupList:n}=u,l=O.value,r=T.value,a=S.value;let i,s,c,d;if(l)for(let u=0;u<n.length;u++){const o=n[u],l=o[a],p=o.disabled;if(l)for(let n=0;n<l.length;n++){const o=l[n],a=Ko(o),u=p||o.disabled;if(i||u||(i=o),d&&a&&!u&&(c=o,!t))return{offsetOption:c};if(e===o[r]){if(d=o,t)return{offsetOption:s}}else a&&!u&&(s=o)}}else for(let u=0;u<o.length;u++){const n=o[u],l=n.disabled;if(i||l||(i=n),d&&!l&&(c=n,!t))return{offsetOption:c};if(e===n[r]){if(d=n,t)return{offsetOption:s}}else l||(s=n)}return{firstOption:i}},ne=t=>{const{clearable:o,disabled:n}=e,{visiblePanel:l,currentValue:r,currentOption:a}=u;if(!n){const e=wo(t,go.TAB),n=wo(t,go.ENTER),i=wo(t,go.ESCAPE),s=wo(t,go.ARROW_UP),c=wo(t,go.ARROW_DOWN),d=wo(t,go.DELETE),p=wo(t,go.SPACEBAR);if(e&&(u.isActivated=!1),l)if(i||e)G();else if(n)t.preventDefault(),t.stopPropagation(),Q(t,r,a);else if(s||c){t.preventDefault();let{firstOption:e,offsetOption:o}=oe(r,s);o||I(r)||(o=e),_(o),B(o,c)}else p&&t.preventDefault();else(s||c||n||p)&&u.isActivated&&(t.preventDefault(),Y());u.isActivated&&d&&o&&Z(t,null)}},le=()=>{G()},re=()=>{e.filterable&&(0,g.nextTick)((()=>{const e=f.value;e&&e.focus()}))},ae=t=>{e.disabled||(u.isActivated=!0),b.dispatchEvent("focus",{},t)},ie=e=>{u.isActivated=!1,b.dispatchEvent("blur",{},e)},se=e=>{u.searchValue=e},ue=()=>{u.isActivated=!0},pe=e=>{const{$event:t}=e,o=wo(t,go.ENTER);o&&(t.preventDefault(),t.stopPropagation())},fe=a().debounce((function(){const{remote:t,remoteMethod:o}=e,{searchValue:n}=u;t&&o?(u.searchLoading=!0,Promise.resolve(o({searchValue:n})).then((()=>(0,g.nextTick)())).catch((()=>(0,g.nextTick)())).finally((()=>{u.searchLoading=!1,P()}))):P()}),350,{trailing:!0}),me=e=>{const{$event:t}=e;t.preventDefault(),u.visiblePanel?G():Y()},he=(e,t,o)=>{if(t.disabled)return!0;if(o&&o.disabled)return!0;const n=k.value;return!(!n||e)},ge=(t,n)=>{const{optionKey:l,modelValue:r,multiple:i}=e,{currentValue:s}=u,c=R.value,d=y.value,p=T.value,f=O.value,{useKey:m}=c,h=o.option;return t.map(((e,t)=>{const{slots:o,className:c}=e,u=e[p],v=i?r&&r.indexOf(u)>-1:r===u,b=!f||Ko(e),w=he(v,e,n),C=A(e),y=o?o.default:null,T={option:e,group:null,$select:x};return b?(0,g.h)("div",{key:m||l?C:t,class:["vxe-select-option",c?a().isFunction(c)?c(T):c:"",{"is--disabled":w,"is--selected":v,"is--hover":s===u}],optid:C,onMousedown:e=>{const t=0===e.button;t&&e.stopPropagation()},onClick:t=>{w||Q(t,u,e)},onMouseenter:()=>{w||_(e)}},h?$(h,T):y?$(y,T):W(z(e[d]))):null}))},ve=()=>{const{optionKey:t}=e,{visibleGroupList:n}=u,l=R.value,r=E.value,i=S.value,{useKey:s}=l,c=o.option;return n.map(((e,o)=>{const{slots:n,className:l}=e,u=A(e),d=e.disabled,p=n?n.default:null,f={option:e,group:e,$select:x};return(0,g.h)("div",{key:s||t?u:o,class:["vxe-optgroup",l?a().isFunction(l)?l(f):l:"",{"is--disabled":d}],optid:u},[(0,g.h)("div",{class:"vxe-optgroup--title"},c?$(c,f):p?$(p,f):z(e[r])),(0,g.h)("div",{class:"vxe-optgroup--wrapper"},ge(e[i]||[],e))])}))},xe=()=>{const{visibleGroupList:t,visibleOptionList:o,searchLoading:n}=u,l=O.value;if(n)return[(0,g.h)("div",{class:"vxe-select--search-loading"},[(0,g.h)("i",{class:["vxe-select--search-icon",c.icon.SELECT_LOADED]}),(0,g.h)("span",{class:"vxe-select--search-text"},c.i18n("vxe.select.loadingText"))])];if(l){if(t.length)return ve()}else if(o.length)return ge(o);return[(0,g.h)("div",{class:"vxe-select--empty-placeholder"},e.emptyText||c.i18n("vxe.select.emptyText"))]};b={dispatchEvent(e,t,o){n(e,Object.assign({$select:x,$event:o},t))},isPanelVisible(){return u.visiblePanel},togglePanel(){return u.visiblePanel?G():Y(),(0,g.nextTick)()},hidePanel(){return u.visiblePanel&&G(),(0,g.nextTick)()},showPanel(){return u.visiblePanel||Y(),(0,g.nextTick)()},refreshOption:P,focus(){const e=p.value;return u.isActivated=!0,e.blur(),(0,g.nextTick)()},blur(){const e=p.value;return e.blur(),u.isActivated=!1,(0,g.nextTick)()}},Object.assign(x,b),(0,g.watch)((()=>u.staticOptions),(e=>{e.some((e=>e.options&&e.options.length))?(u.fullOptionList=[],u.fullGroupList=e):(u.fullGroupList=[],u.fullOptionList=e||[]),V()})),(0,g.watch)((()=>e.options),(e=>{u.fullGroupList=[],u.fullOptionList=e||[],V()})),(0,g.watch)((()=>e.optionGroups),(e=>{u.fullOptionList=[],u.fullGroupList=e||[],V()})),(0,g.onMounted)((()=>{(0,g.nextTick)((()=>{const{options:t,optionGroups:o}=e;o?u.fullGroupList=o:t&&(u.fullOptionList=t),V()})),yo.on(x,"mousewheel",ee),yo.on(x,"mousedown",te),yo.on(x,"keydown",ne),yo.on(x,"blur",le)})),(0,g.onUnmounted)((()=>{yo.off(x,"mousewheel"),yo.off(x,"mousedown"),yo.off(x,"keydown"),yo.off(x,"blur")}));const be=()=>{const{className:t,popupClassName:n,transfer:l,disabled:r,loading:i,filterable:v}=e,{inited:b,isActivated:w,visiblePanel:C}=u,y=s.value,T=N.value,E=o.default,S=o.header,k=o.footer,R=o.prefix;return(0,g.h)("div",{ref:d,class:["vxe-select",t?a().isFunction(t)?t({$select:x}):t:"",{[`size--${y}`]:y,"is--visivle":C,"is--disabled":r,"is--filter":v,"is--loading":i,"is--active":w}]},[(0,g.h)("div",{class:"vxe-select-slots",ref:"hideOption"},E?E({}):[]),(0,g.h)(Yo,{ref:p,clearable:e.clearable,placeholder:e.placeholder,readonly:!0,disabled:r,type:"text",prefixIcon:e.prefixIcon,suffixIcon:i?c.icon.SELECT_LOADED:C?c.icon.SELECT_OPEN:c.icon.SELECT_CLOSE,modelValue:T,onClear:J,onClick:me,onFocus:ae,onBlur:ie,onSuffixClick:me},R?{prefix:()=>R({})}:{}),(0,g.h)(g.Teleport,{to:"body",disabled:!l||!b},[(0,g.h)("div",{ref:h,class:["vxe-table--ignore-clear vxe-select--panel",n?a().isFunction(n)?n({$select:x}):n:"",{[`size--${y}`]:y,"is--transfer":l,"animat--leave":!i&&u.animatVisible,"animat--enter":!i&&C}],placement:u.panelPlacement,style:u.panelStyle},b?[v?(0,g.h)("div",{class:"vxe-select--panel-search"},[(0,g.h)(Yo,{ref:f,class:"vxe-select-search--input",modelValue:u.searchValue,clearable:!0,placeholder:c.i18n("vxe.select.search"),prefixIcon:c.icon.INPUT_SEARCH,"onUpdate:modelValue":se,onFocus:ue,onKeydown:pe,onChange:fe,onSearch:fe})]):(0,g.createCommentVNode)(),(0,g.h)("div",{class:"vxe-select--panel-wrapper"},[S?(0,g.h)("div",{class:"vxe-select--panel-header"},S({})):(0,g.createCommentVNode)(),(0,g.h)("div",{class:"vxe-select--panel-body"},[(0,g.h)("div",{ref:m,class:"vxe-select-option--wrapper"},xe())]),k?(0,g.h)("div",{class:"vxe-select--panel-footer"},k({})):(0,g.createCommentVNode)()])]:[])])])};return x.renderVN=be,(0,g.provide)("$xeselect",x),x},render(){return this.renderVN()}}),Qo=(0,g.defineComponent)({name:"VxeExportPanel",props:{defaultOptions:Object,storeData:Object},setup(e){const t=(0,g.inject)("$xetable",{}),{computeExportOpts:o,computePrintOpts:n}=t.getComputeMaps(),l=(0,g.reactive)({isAll:!1,isIndeterminate:!1,loading:!1}),r=(0,g.ref)(),i=(0,g.ref)(),s=(0,g.ref)(),u=(0,g.computed)((()=>{const{storeData:t}=e;return t.columns.every((e=>e.checked))})),d=(0,g.computed)((()=>{const{defaultOptions:t}=e;return["html","xml","xlsx","pdf"].indexOf(t.type)>-1})),p=(0,g.computed)((()=>{const{storeData:t,defaultOptions:o}=e;return!o.original&&"current"===o.mode&&(t.isPrint||["html","xlsx"].indexOf(o.type)>-1)})),f=(0,g.computed)((()=>{const{defaultOptions:t}=e;return!t.original&&["xlsx"].indexOf(t.type)>-1})),m=t=>{const{storeData:o}=e,n=a().findTree(o.columns,(e=>e===t));if(n&&n.parent){const{parent:e}=n;e.children&&e.children.length&&(e.checked=e.children.every((e=>e.checked)),e.halfChecked=!e.checked&&e.children.some((e=>e.checked||e.halfChecked)),m(e))}},h=()=>{const{storeData:t}=e,o=t.columns;l.isAll=o.every((e=>e.disabled||e.checked)),l.isIndeterminate=!l.isAll&&o.some((e=>!e.disabled&&(e.checked||e.halfChecked)))},v=e=>{const t=!e.checked;a().eachTree([e],(e=>{e.checked=t,e.halfChecked=!1})),m(e),h()},x=()=>{const{storeData:t}=e,o=!l.isAll;a().eachTree(t.columns,(e=>{e.disabled||(e.checked=o,e.halfChecked=!1)})),l.isAll=o,h()},b=()=>{(0,g.nextTick)((()=>{const e=i.value,t=s.value,o=r.value,n=e||t||o;n&&n.focus()})),h()},w=()=>{const{storeData:t,defaultOptions:o}=e,{hasMerge:n,columns:l}=t,r=u.value,i=p.value,s=a().searchTree(l,(e=>e.checked),{children:"children",mapChildren:"childNodes",original:!0});return Object.assign({},o,{columns:s,isMerge:!!(n&&i&&r)&&o.isMerge})},C=()=>{const{storeData:o}=e,l=n.value;o.visible=!1,t.print(Object.assign({},l,w()))},y=()=>{const{storeData:n}=e,r=o.value;l.loading=!0,t.exportData(Object.assign({},r,w())).then((()=>{l.loading=!1,n.visible=!1})).catch((()=>{l.loading=!1}))},T=()=>{const{storeData:t}=e;t.visible=!1},E=()=>{const{storeData:t}=e;t.isPrint?C():y()},S=()=>{const{defaultOptions:t,storeData:o}=e,{isAll:n,isIndeterminate:m}=l,{hasTree:h,hasMerge:w,isPrint:C,hasColgroup:y}=o,{isHeader:S}=t,k=[],R=u.value,O=d.value,M=p.value,$=f.value;return a().eachTree(o.columns,(e=>{const t=W(e.getTitle(),1),o=e.children&&e.children.length,n=e.checked,l=e.halfChecked;k.push((0,g.h)("li",{class:["vxe-export--panel-column-option",`level--${e.level}`,{"is--group":o,"is--checked":n,"is--indeterminate":l,"is--disabled":e.disabled}],title:t,onClick:()=>{e.disabled||v(e)}},[(0,g.h)("span",{class:["vxe-checkbox--icon",l?c.icon.TABLE_CHECKBOX_INDETERMINATE:n?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]}),(0,g.h)("span",{class:"vxe-checkbox--label"},t)]))})),(0,g.h)(jo,{modelValue:o.visible,title:c.i18n(C?"vxe.export.printTitle":"vxe.export.expTitle"),width:660,mask:!0,lockView:!0,showFooter:!1,escClosable:!0,maskClosable:!0,loading:l.loading,"onUpdate:modelValue"(e){o.visible=e},onShow:b},{default:()=>(0,g.h)("div",{class:"vxe-export--panel"},[(0,g.h)("table",{cellspacing:0,cellpadding:0,border:0},[(0,g.h)("tbody",[[C?(0,g.createCommentVNode)():(0,g.h)("tr",[(0,g.h)("td",c.i18n("vxe.export.expName")),(0,g.h)("td",[(0,g.h)(Yo,{ref:i,modelValue:t.filename,type:"text",clearable:!0,placeholder:c.i18n("vxe.export.expNamePlaceholder"),"onUpdate:modelValue"(e){t.filename=e}})])]),C?(0,g.createCommentVNode)():(0,g.h)("tr",[(0,g.h)("td",c.i18n("vxe.export.expType")),(0,g.h)("td",[(0,g.h)(Jo,{modelValue:t.type,options:o.typeList.map((e=>({value:e.value,label:c.i18n(e.label)}))),"onUpdate:modelValue"(e){t.type=e}})])]),C||O?(0,g.h)("tr",[(0,g.h)("td",c.i18n("vxe.export.expSheetName")),(0,g.h)("td",[(0,g.h)(Yo,{ref:s,modelValue:t.sheetName,type:"text",clearable:!0,placeholder:c.i18n("vxe.export.expSheetNamePlaceholder"),"onUpdate:modelValue"(e){t.sheetName=e}})])]):(0,g.createCommentVNode)(),(0,g.h)("tr",[(0,g.h)("td",c.i18n("vxe.export.expMode")),(0,g.h)("td",[(0,g.h)(Jo,{modelValue:t.mode,options:o.modeList.map((e=>({value:e.value,label:c.i18n(e.label)}))),"onUpdate:modelValue"(e){t.mode=e}})])]),(0,g.h)("tr",[(0,g.h)("td",[c.i18n("vxe.export.expColumn")]),(0,g.h)("td",[(0,g.h)("div",{class:"vxe-export--panel-column"},[(0,g.h)("ul",{class:"vxe-export--panel-column-header"},[(0,g.h)("li",{class:["vxe-export--panel-column-option",{"is--checked":n,"is--indeterminate":m}],title:c.i18n("vxe.table.allTitle"),onClick:x},[(0,g.h)("span",{class:["vxe-checkbox--icon",m?c.icon.TABLE_CHECKBOX_INDETERMINATE:n?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]}),(0,g.h)("span",{class:"vxe-checkbox--label"},c.i18n("vxe.export.expCurrentColumn"))])]),(0,g.h)("ul",{class:"vxe-export--panel-column-body"},k)])])]),(0,g.h)("tr",[(0,g.h)("td",c.i18n("vxe.export.expOpts")),(0,g.h)("td",[(0,g.h)("div",{class:"vxe-export--panel-option-row"},[(0,g.h)(Go,{modelValue:t.isHeader,title:c.i18n("vxe.export.expHeaderTitle"),content:c.i18n("vxe.export.expOptHeader"),"onUpdate:modelValue"(e){t.isHeader=e}}),(0,g.h)(Go,{modelValue:t.isFooter,disabled:!o.hasFooter,title:c.i18n("vxe.export.expFooterTitle"),content:c.i18n("vxe.export.expOptFooter"),"onUpdate:modelValue"(e){t.isFooter=e}}),(0,g.h)(Go,{modelValue:t.original,title:c.i18n("vxe.export.expOriginalTitle"),content:c.i18n("vxe.export.expOptOriginal"),"onUpdate:modelValue"(e){t.original=e}})]),(0,g.h)("div",{class:"vxe-export--panel-option-row"},[(0,g.h)(Go,{modelValue:!!(S&&y&&M)&&t.isColgroup,title:c.i18n("vxe.export.expColgroupTitle"),disabled:!S||!y||!M,content:c.i18n("vxe.export.expOptColgroup"),"onUpdate:modelValue"(e){t.isColgroup=e}}),(0,g.h)(Go,{modelValue:!!(w&&M&&R)&&t.isMerge,title:c.i18n("vxe.export.expMergeTitle"),disabled:!w||!M||!R,content:c.i18n("vxe.export.expOptMerge"),"onUpdate:modelValue"(e){t.isMerge=e}}),C?(0,g.createCommentVNode)():(0,g.h)(Go,{modelValue:!!$&&t.useStyle,disabled:!$,title:c.i18n("vxe.export.expUseStyleTitle"),content:c.i18n("vxe.export.expOptUseStyle"),"onUpdate:modelValue"(e){t.useStyle=e}}),(0,g.h)(Go,{modelValue:!!h&&t.isAllExpand,disabled:!h,title:c.i18n("vxe.export.expAllExpandTitle"),content:c.i18n("vxe.export.expOptAllExpand"),"onUpdate:modelValue"(e){t.isAllExpand=e}})])])])]])]),(0,g.h)("div",{class:"vxe-export--panel-btns"},[(0,g.h)(No,{content:c.i18n("vxe.export.expCancel"),onClick:T}),(0,g.h)(No,{ref:r,status:"primary",content:c.i18n(C?"vxe.export.expPrint":"vxe.export.expConfirm"),onClick:E})])])})};return S}}),en=(0,g.defineComponent)({name:"VxeRadioGroup",props:{modelValue:[String,Number,Boolean],disabled:Boolean,strict:{type:Boolean,default:()=>c.radio.strict},size:{type:String,default:()=>c.radio.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,g.inject)("$xeform",null),r=(0,g.inject)("$xeformiteminfo",null),i=a().uniqueId(),s={xID:i,props:e,context:t,name:a().uniqueId("xegroup_")};let c={};Fo(e);const u={handleChecked(e,t){n("update:modelValue",e.label),c.dispatchEvent("change",e),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,e.label)}};c={dispatchEvent(e,t,o){n(e,Object.assign({$radioGroup:s,$event:o},t))}};const d=()=>(0,g.h)("div",{class:"vxe-radio-group"},o.default?o.default({}):[]);return Object.assign(s,u,{renderVN:d,dispatchEvent:dispatchEvent}),(0,g.provide)("$xeradiogroup",s),d}}),tn=(0,g.defineComponent)({name:"VxeRadio",props:{modelValue:[String,Number,Boolean],label:{type:[String,Number,Boolean],default:null},title:[String,Number],content:[String,Number],disabled:Boolean,name:String,strict:{type:Boolean,default:()=>c.radio.strict},size:{type:String,default:()=>c.radio.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,g.inject)("$xeform",null),r=(0,g.inject)("$xeformiteminfo",null),i=a().uniqueId(),s={xID:i,props:e,context:t},c=Fo(e),u=(0,g.inject)("$xeradiogroup",null);let d={};const p=(0,g.computed)((()=>e.disabled||u&&u.props.disabled)),f=(0,g.computed)((()=>u?u.name:e.name)),m=(0,g.computed)((()=>u?u.props.strict:e.strict)),h=(0,g.computed)((()=>{const{modelValue:t,label:o}=e;return u?u.props.modelValue===o:t===o})),v=(e,t)=>{u?u.handleChecked({label:e},t):(n("update:modelValue",e),d.dispatchEvent("change",{label:e},t),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,e))},x=t=>{const o=p.value;o||v(e.label,t)},b=t=>{const o=p.value,n=m.value;o||n||e.label===(u?u.props.modelValue:e.modelValue)&&v(null,t)};d={dispatchEvent(e,t,o){n(e,Object.assign({$radio:s,$event:o},t))}},Object.assign(s,d);const w=()=>{const t=c.value,n=p.value,l=f.value,r=h.value;return(0,g.h)("label",{class:["vxe-radio",{[`size--${t}`]:t,"is--checked":r,"is--disabled":n}],title:e.title},[(0,g.h)("input",{class:"vxe-radio--input",type:"radio",name:l,checked:r,disabled:n,onChange:x,onClick:b}),(0,g.h)("span",{class:["vxe-radio--icon",r?"vxe-icon-radio-checked":"vxe-icon-radio-unchecked"]}),(0,g.h)("span",{class:"vxe-radio--label"},o.default?o.default({}):z(e.content))])};return s.renderVN=w,s},render(){return this.renderVN()}}),on=(0,g.defineComponent)({name:"VxeImportPanel",props:{defaultOptions:Object,storeData:Object},setup(e){const t=(0,g.inject)("$xetable",{}),{computeImportOpts:o}=t.getComputeMaps(),n=(0,g.reactive)({loading:!1}),l=(0,g.ref)(),r=(0,g.computed)((()=>{const{storeData:t}=e;return`${t.filename}.${t.type}`})),i=(0,g.computed)((()=>{const{storeData:t}=e;return t.file&&t.type})),s=(0,g.computed)((()=>{const{storeData:t}=e,{type:o,typeList:n}=t;if(o){const e=a().find(n,(e=>o===e.value));return e?c.i18n(e.label):"*.*"}return`*.${n.map((e=>e.value)).join(", *.")}`})),u=()=>{const{storeData:t}=e;Object.assign(t,{filename:"",sheetName:"",type:""})},d=()=>{const{storeData:o,defaultOptions:n}=e;t.readFile(n).then((e=>{const{file:t}=e;Object.assign(o,_(t),{file:t})})).catch((e=>e))},p=()=>{(0,g.nextTick)((()=>{const e=l.value;e&&e.focus()}))},f=()=>{const{storeData:t}=e;t.visible=!1},m=()=>{const{storeData:l,defaultOptions:r}=e,a=o.value;n.loading=!0,t.importByFile(l.file,Object.assign({},a,r)).then((()=>{n.loading=!1,l.visible=!1})).catch((()=>{n.loading=!1}))},h=()=>{const{defaultOptions:t,storeData:o}=e,a=r.value,h=i.value,v=s.value;return(0,g.h)(jo,{modelValue:o.visible,title:c.i18n("vxe.import.impTitle"),width:440,mask:!0,lockView:!0,showFooter:!1,escClosable:!0,maskClosable:!0,loading:n.loading,"onUpdate:modelValue"(e){o.visible=e},onShow:p},{default:()=>(0,g.h)("div",{class:"vxe-export--panel"},[(0,g.h)("table",{cellspacing:0,cellpadding:0,border:0},[(0,g.h)("tbody",[(0,g.h)("tr",[(0,g.h)("td",c.i18n("vxe.import.impFile")),(0,g.h)("td",[h?(0,g.h)("div",{class:"vxe-import-selected--file",title:a},[(0,g.h)("span",a),(0,g.h)("i",{class:c.icon.INPUT_CLEAR,onClick:u})]):(0,g.h)("button",{ref:l,class:"vxe-import-select--file",onClick:d},c.i18n("vxe.import.impSelect"))])]),(0,g.h)("tr",[(0,g.h)("td",c.i18n("vxe.import.impType")),(0,g.h)("td",v)]),(0,g.h)("tr",[(0,g.h)("td",c.i18n("vxe.import.impOpts")),(0,g.h)("td",[(0,g.h)(en,{modelValue:t.mode,"onUpdate:modelValue"(e){t.mode=e}},{default:()=>o.modeList.map((e=>(0,g.h)(tn,{label:e.value,content:c.i18n(e.label)})))})])])])]),(0,g.h)("div",{class:"vxe-export--panel-btns"},[(0,g.h)(No,{content:c.i18n("vxe.import.impCancel"),onClick:f}),(0,g.h)(No,{status:"primary",disabled:!h,content:c.i18n("vxe.import.impConfirm"),onClick:m})])])})};return h}});let nn,ln,rn;const an='body{margin:0;padding: 0 1px;color:#333333;font-size:14px;font-family:"Microsoft YaHei",微软雅黑,"MicrosoftJhengHei",华文细黑,STHeiti,MingLiu}body *{-webkit-box-sizing:border-box;box-sizing:border-box}.vxe-table{border-collapse:collapse;text-align:left;border-spacing:0}.vxe-table:not(.is--print){table-layout:fixed}.vxe-table,.vxe-table th,.vxe-table td,.vxe-table td{border-color:#D0D0D0;border-style:solid;border-width:0}.vxe-table.is--print{width:100%}.border--default,.border--full,.border--outer{border-top-width:1px}.border--default,.border--full,.border--outer{border-left-width:1px}.border--outer,.border--default th,.border--default td,.border--full th,.border--full td,.border--outer th,.border--inner th,.border--inner td{border-bottom-width:1px}.border--default,.border--outer,.border--full th,.border--full td{border-right-width:1px}.border--default th,.border--full th,.border--outer th{background-color:#f8f8f9}.vxe-table td>div,.vxe-table th>div{padding:.5em .4em}.col--center{text-align:center}.col--right{text-align:right}.vxe-table:not(.is--print) .col--ellipsis>div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:break-all}.vxe-table--tree-node{text-align:left}.vxe-table--tree-node-wrapper{position:relative}.vxe-table--tree-icon-wrapper{position:absolute;top:50%;width:1em;height:1em;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer}.vxe-table--tree-unfold-icon,.vxe-table--tree-fold-icon{position:absolute;width:0;height:0;border-style:solid;border-width:.5em;border-right-color:transparent;border-bottom-color:transparent}.vxe-table--tree-unfold-icon{left:.3em;top:0;border-left-color:#939599;border-top-color:transparent}.vxe-table--tree-fold-icon{left:0;top:.3em;border-left-color:transparent;border-top-color:#939599}.vxe-table--tree-cell{display:block;padding-left:1.5em}.vxe-table input[type="checkbox"]{margin:0}.vxe-table input[type="checkbox"],.vxe-table input[type="radio"],.vxe-table input[type="checkbox"]+span,.vxe-table input[type="radio"]+span{vertical-align:middle;padding-left:0.4em}';function sn(){const e=document.createElement("iframe");return e.className="vxe-table--print-frame",e}function cn(e,t){return new Blob([e],{type:`text/${t.type};charset=utf-8;`})}function un(e,t){const{style:o}=e;return["<!DOCTYPE html><html>","<head>",'<meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,minimal-ui">',`<title>${e.sheetName}</title>`,`<style>${an}</style>`,o?`<style>${o}</style>`:"","</head>",`<body>${t}</body>`,"</html>"].join("")}const dn=e=>{const t=Object.assign({},e);return nn||(nn=document.createElement("form"),ln=document.createElement("input"),nn.className="vxe-table--file-form",ln.name="file",ln.type="file",nn.appendChild(ln),document.body.appendChild(nn)),new Promise(((e,o)=>{const n=t.types||[],l=!n.length||n.some((e=>"*"===e));ln.multiple=!!t.multiple,ln.accept=l?"":`.${n.join(", .")}`,ln.onchange=r=>{const{files:i}=r.target,s=i[0];let u="";if(!l)for(let e=0;e<i.length;e++){const{type:t}=_(i[e]);if(!a().includes(n,t)){u=t;break}}if(u){!1!==t.message&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({content:c.i18n("vxe.error.notType",[u]),status:"error"}));const e={status:!1,files:i,file:s};o(e)}else e({status:!0,files:i,file:s})},nn.reset(),ln.click()}))};function pn(){if(rn){if(rn.parentNode){try{rn.contentDocument.write("")}catch(e){}rn.parentNode.removeChild(rn)}rn=null}}function fn(){rn.parentNode||document.body.appendChild(rn)}function mn(){requestAnimationFrame(pn)}function hn(e,t,o=""){const{beforePrintMethod:n}=t;n&&(o=n({content:o,options:t,$table:e})||""),o=un(t,o);const l=cn(o,t);Y.msie?(pn(),rn=sn(),fn(),rn.contentDocument.write(o),rn.contentDocument.execCommand("print")):(rn||(rn=sn(),rn.onload=e=>{e.target.src&&(e.target.contentWindow.onafterprint=mn,e.target.contentWindow.print())}),fn(),rn.src=URL.createObjectURL(l))}const gn=e=>{const{filename:t,type:o,content:n}=e,l=`${t}.${o}`;if(window.Blob){const t=n instanceof Blob?n:cn(a().toValueString(n),e);if(navigator.msSaveBlob)navigator.msSaveBlob(t,l);else{const e=URL.createObjectURL(t),o=document.createElement("a");o.target="_blank",o.download=l,o.href=e,document.body.appendChild(o),o.click(),requestAnimationFrame((()=>{o.parentNode&&o.parentNode.removeChild(o),URL.revokeObjectURL(e)}))}return Promise.resolve()}return Promise.reject(new Error(u("vxe.error.notExp")))};let vn;const xn="\ufeff",bn="\r\n";function wn(e){return e.property||["seq","checkbox","radio"].indexOf(e.type)>-1}const Cn=e=>{const t=[];return e.forEach((e=>{e.childNodes&&e.childNodes.length?(t.push(e),t.push(...Cn(e.childNodes))):t.push(e)})),t},yn=e=>{let t=1;const o=(e,n)=>{if(n&&(e._level=n._level+1,t<e._level&&(t=e._level)),e.childNodes&&e.childNodes.length){let t=0;e.childNodes.forEach((n=>{o(n,e),t+=n._colSpan})),e._colSpan=t}else e._colSpan=1};e.forEach((e=>{e._level=1,o(e)}));const n=[];for(let r=0;r<t;r++)n.push([]);const l=Cn(e);return l.forEach((e=>{e.childNodes&&e.childNodes.length?e._rowSpan=1:e._rowSpan=t-e._level+1,n[e._level-1].push(e)})),n};function Tn(e){return!0===e?"full":e||"default"}function En(e){return"TRUE"===e||"true"===e||!0===e}function Sn(e,t){const{footerFilterMethod:o}=e;return o?t.filter(((e,t)=>o({items:e,$rowIndex:t}))):t}function kn(e,t){if(t){if("seq"===e.type)return`\t${t}`;switch(e.cellType){case"string":if(!isNaN(t))return`\t${t}`;break;case"number":break;default:if(t.length>=12&&!isNaN(t))return`\t${t}`;break}}return t}function Rn(e){return/[",\s\n]/.test(e)?`"${e.replace(/"/g,'""')}"`:e}function On(e,t){return e.getElementsByTagName(t)}function Mn(e){return`#${e}@${a().uniqueId()}`}function $n(e,t){return e.replace(/#\d+@\d+/g,(e=>a().hasOwnProp(t,e)?t[e]:e))}function In(e,t){const o=$n(e,t);return o.replace(/^"+$/g,(e=>'"'.repeat(Math.ceil(e.length/2))))}function Dn(e,t,o){const n=t.split(bn),l=[];let r=[];if(n.length){const e={},t=Date.now();n.forEach((n=>{if(n){const a={};n=n.replace(/("")|(\n)/g,((o,n)=>{const l=Mn(t);return e[l]=n?'"':"\n",l})).replace(/"(.*?)"/g,((o,n)=>{const l=Mn(t);return e[l]=$n(n,e),l}));const i=n.split(o);r.length?(i.forEach(((t,o)=>{o<r.length&&(a[r[o]]=In(t.trim(),e))})),l.push(a)):r=i.map((t=>In(t.trim(),e)))}}))}return{fields:r,rows:l}}function Fn(e,t){return Dn(e,t,",")}function Nn(e,t){return Dn(e,t,"\t")}function Ln(e,t){const o=new DOMParser,n=o.parseFromString(t,"text/html"),l=On(n,"body"),r=[],i=[];if(l.length){const e=On(l[0],"table");if(e.length){const t=On(e[0],"thead");if(t.length){a().arrayEach(On(t[0],"tr"),(e=>{a().arrayEach(On(e,"th"),(e=>{i.push(e.textContent)}))}));const o=On(e[0],"tbody");o.length&&a().arrayEach(On(o[0],"tr"),(e=>{const t={};a().arrayEach(On(e,"td"),((e,o)=>{i[o]&&(t[i[o]]=e.textContent||"")})),r.push(t)}))}}}return{fields:i,rows:r}}function An(e,t){const o=new DOMParser,n=o.parseFromString(t,"application/xml"),l=On(n,"Worksheet"),r=[],i=[];if(l.length){const e=On(l[0],"Table");if(e.length){const t=On(e[0],"Row");t.length&&(a().arrayEach(On(t[0],"Cell"),(e=>{i.push(e.textContent)})),a().arrayEach(t,((e,t)=>{if(t){const t={},o=On(e,"Cell");a().arrayEach(o,((e,o)=>{i[o]&&(t[i[o]]=e.textContent)})),r.push(t)}})))}}return{fields:i,rows:r}}function Pn(e){a().eachTree(e,(e=>{delete e._level,delete e._colSpan,delete e._rowSpan,delete e._children,delete e.childNodes}),{children:"children"})}function Vn(e,t){const o=[];return e.forEach((e=>{const t=e.property;t&&o.push(t)})),t.some((e=>o.indexOf(e)>-1))}const _n=["exportData","importByFile","importData","saveFile","readFile","print","openImport","openExport","openPrint"],jn={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{computeTreeOpts:l,computePrintOpts:r,computeExportOpts:i,computeImportOpts:s,computeCustomOpts:u,computeSeqOpts:d,computeRadioOpts:m,computeCheckboxOpts:h,computeColumnOpts:v}=e.getComputeMaps(),x=(0,g.inject)("$xegrid",null),b=e=>{const t=l.value,o=t.children||t.childrenField;return e[o]&&e[o].length},w=(t,o,n,l)=>{const r=d.value,a=r.seqMethod||n.seqMethod;return a?a({row:t,rowIndex:e.getRowIndex(t),$rowIndex:o,column:n,columnIndex:e.getColumnIndex(n),$columnIndex:l}):e.getRowSeq(t)};function C(t,o){const n=v.value,l=o.headerExportMethod||n.headerExportMethod;return l?l({column:o,options:t,$table:e}):(t.original?o.property:o.getTitle())||""}const y=e=>a().isBoolean(e)?e?"TRUE":"FALSE":e,T=(o,n,r)=>{const{isAllExpand:i,mode:s}=o,{treeConfig:c}=t,u=m.value,d=h.value,p=l.value,f=v.value;if(vn||(vn=document.createElement("div")),c){const t=p.children||p.childrenField,l=[],c=new Map;return a().eachTree(r,((t,r,p,m,h,g)=>{const v=t._row||t,x=h&&h._row?h._row:h;if(i||!x||c.has(x)&&e.isTreeExpandByRow(x)){const t=b(v),i={_row:v,_level:g.length-1,_hasChild:t,_expand:t&&e.isTreeExpandByRow(v)};n.forEach(((t,n)=>{let l="";const c=t.editRender||t.cellRender;let p=t.exportMethod;if(!p&&c&&c.name){const e=oo.renderer.get(c.name);e&&(p=e.exportMethod)}if(p||(p=f.exportMethod),p)l=p({$table:e,row:v,column:t,options:o});else switch(t.type){case"seq":l="all"===s?m.map(((e,t)=>t%2===0?Number(e)+1:".")).join(""):w(v,r,t,n);break;case"checkbox":l=y(e.isCheckedByCheckboxRow(v)),i._checkboxLabel=d.labelField?a().get(v,d.labelField):"",i._checkboxDisabled=d.checkMethod&&!d.checkMethod({row:v});break;case"radio":l=y(e.isCheckedByRadioRow(v)),i._radioLabel=u.labelField?a().get(v,u.labelField):"",i._radioDisabled=u.checkMethod&&!u.checkMethod({row:v});break;default:if(o.original)l=Me(v,t);else if(l=e.getCellLabel(v,t),"html"===t.type)vn.innerHTML=l,l=vn.innerText.trim();else{const o=e.getCell(v,t);o&&(l=o.innerText.trim())}}i[t.id]=a().toValueString(l)})),c.set(v,1),l.push(Object.assign(i,v))}}),{children:t}),l}return r.map(((t,l)=>{const r={_row:t};return n.forEach(((n,i)=>{let c="";const p=n.editRender||n.cellRender;let f=n.exportMethod;if(!f&&p&&p.name){const e=oo.renderer.get(p.name);e&&(f=e.exportMethod)}if(f)c=f({$table:e,row:t,column:n,options:o});else switch(n.type){case"seq":c="all"===s?l+1:w(t,l,n,i);break;case"checkbox":c=y(e.isCheckedByCheckboxRow(t)),r._checkboxLabel=d.labelField?a().get(t,d.labelField):"",r._checkboxDisabled=d.checkMethod&&!d.checkMethod({row:t});break;case"radio":c=y(e.isCheckedByRadioRow(t)),r._radioLabel=u.labelField?a().get(t,u.labelField):"",r._radioDisabled=u.checkMethod&&!u.checkMethod({row:t});break;default:if(o.original)c=Me(t,n);else if(c=e.getCellLabel(t,n),"html"===n.type)vn.innerHTML=c,c=vn.innerText.trim();else{const o=e.getCell(t,n);o&&(c=o.innerText.trim())}}r[n.id]=a().toValueString(c)})),r}))},E=e=>{const{columns:t,dataFilterMethod:o}=e;let n=e.data;return o&&(n=n.filter(((e,t)=>o({row:e,$rowIndex:t})))),T(e,t,n)},S=(t,o,n)=>{const l=v.value,r=n.editRender||n.cellRender;let i=n.footerExportMethod;if(!i&&r&&r.name){const e=oo.renderer.get(r.name);e&&(i=e.footerExportMethod)}i||(i=l.footerExportMethod);const s=e.getVTColumnIndex(n),c=i?i({$table:e,items:o,itemIndex:s,_columnIndex:s,column:n,options:t}):a().toValueString(o[s]);return c},k=(e,t,n)=>{let l=xn;if(e.isHeader&&(l+=t.map((t=>Rn(C(e,t)))).join(",")+bn),n.forEach((e=>{l+=t.map((t=>Rn(kn(t,e[t.id])))).join(",")+bn})),e.isFooter){const{footerTableData:n}=o,r=Sn(e,n);r.forEach((o=>{l+=t.map((t=>Rn(S(e,o,t)))).join(",")+bn}))}return l},R=(e,t,n)=>{let l="";if(e.isHeader&&(l+=t.map((t=>Rn(C(e,t)))).join("\t")+bn),n.forEach((e=>{l+=t.map((t=>Rn(e[t.id]))).join("\t")+bn})),e.isFooter){const{footerTableData:n}=o,r=Sn(e,n);r.forEach((o=>{l+=t.map((t=>Rn(S(e,o,t)))).join(",")+bn}))}return l},O=(e,t,n)=>{const l=e[t],r=a().isUndefined(l)||a().isNull(l)?n:l,i="ellipsis"===r,s="title"===r,c=!0===r||"tooltip"===r;let u=s||c||i;const{scrollXLoad:d,scrollYLoad:p}=o;return!d&&!p||u||(u=!0),u},M=(n,r,i)=>{const{id:s,border:c,treeConfig:u,headerAlign:d,align:p,footerAlign:f,showOverflow:m,showHeaderOverflow:h}=t,{isAllSelected:g,isIndeterminate:v,mergeList:x}=o,b=l.value,{print:w,isHeader:y,isFooter:T,isColgroup:E,isMerge:k,colgroups:R,original:M}=n,$="check-all",I=["vxe-table",`border--${Tn(c)}`,w?"is--print":"",y?"is--header":""].filter((e=>e)),D=[`<table class="${I.join(" ")}" border="0" cellspacing="0" cellpadding="0">`,`<colgroup>${r.map((e=>`<col style="width:${e.renderWidth}px">`)).join("")}</colgroup>`];if(y&&(D.push("<thead>"),E&&!M?R.forEach((e=>{D.push(`<tr>${e.map((e=>{const t=e.headerAlign||e.align||d||p,o=O(e,"showHeaderOverflow",h)?["col--ellipsis"]:[],l=C(n,e);let r=0,i=0;a().eachTree([e],(t=>{t.childNodes&&e.childNodes.length||i++,r+=t.renderWidth}),{children:"childNodes"});const s=r-i;return t&&o.push(`col--${t}`),"checkbox"===e.type?`<th class="${o.join(" ")}" colspan="${e._colSpan}" rowspan="${e._rowSpan}"><div ${w?"":`style="width: ${s}px"`}><input type="checkbox" class="${$}" ${g?"checked":""}><span>${l}</span></div></th>`:`<th class="${o.join(" ")}" colspan="${e._colSpan}" rowspan="${e._rowSpan}" title="${l}"><div ${w?"":`style="width: ${s}px"`}><span>${W(l,!0)}</span></div></th>`})).join("")}</tr>`)})):D.push(`<tr>${r.map((e=>{const t=e.headerAlign||e.align||d||p,o=O(e,"showHeaderOverflow",h)?["col--ellipsis"]:[],l=C(n,e);return t&&o.push(`col--${t}`),"checkbox"===e.type?`<th class="${o.join(" ")}"><div ${w?"":`style="width: ${e.renderWidth}px"`}><input type="checkbox" class="${$}" ${g?"checked":""}><span>${l}</span></div></th>`:`<th class="${o.join(" ")}" title="${l}"><div ${w?"":`style="width: ${e.renderWidth}px"`}><span>${W(l,!0)}</span></div></th>`})).join("")}</tr>`),D.push("</thead>")),i.length&&(D.push("<tbody>"),u?i.forEach((e=>{D.push("<tr>"+r.map((t=>{const o=t.align||p,n=O(t,"showOverflow",m)?["col--ellipsis"]:[],l=e[t.id];if(o&&n.push(`col--${o}`),t.treeNode){let o="";return e._hasChild&&(o=`<i class="${e._expand?"vxe-table--tree-fold-icon":"vxe-table--tree-unfold-icon"}"></i>`),n.push("vxe-table--tree-node"),"radio"===t.type?`<td class="${n.join(" ")}" title="${l}"><div ${w?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${e._level*b.indent}px"><div class="vxe-table--tree-icon-wrapper">${o}</div><div class="vxe-table--tree-cell"><input type="radio" name="radio_${s}" ${e._radioDisabled?"disabled ":""}${En(l)?"checked":""}><span>${e._radioLabel}</span></div></div></div></td>`:"checkbox"===t.type?`<td class="${n.join(" ")}" title="${l}"><div ${w?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${e._level*b.indent}px"><div class="vxe-table--tree-icon-wrapper">${o}</div><div class="vxe-table--tree-cell"><input type="checkbox" ${e._checkboxDisabled?"disabled ":""}${En(l)?"checked":""}><span>${e._checkboxLabel}</span></div></div></div></td>`:`<td class="${n.join(" ")}" title="${l}"><div ${w?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${e._level*b.indent}px"><div class="vxe-table--tree-icon-wrapper">${o}</div><div class="vxe-table--tree-cell">${l}</div></div></div></td>`}return"radio"===t.type?`<td class="${n.join(" ")}"><div ${w?"":`style="width: ${t.renderWidth}px"`}><input type="radio" name="radio_${s}" ${e._radioDisabled?"disabled ":""}${En(l)?"checked":""}><span>${e._radioLabel}</span></div></td>`:"checkbox"===t.type?`<td class="${n.join(" ")}"><div ${w?"":`style="width: ${t.renderWidth}px"`}><input type="checkbox" ${e._checkboxDisabled?"disabled ":""}${En(l)?"checked":""}><span>${e._checkboxLabel}</span></div></td>`:`<td class="${n.join(" ")}" title="${l}"><div ${w?"":`style="width: ${t.renderWidth}px"`}>${W(l,!0)}</div></td>`})).join("")+"</tr>")})):i.forEach((t=>{D.push("<tr>"+r.map((o=>{const n=o.align||p,l=O(o,"showOverflow",m)?["col--ellipsis"]:[],r=t[o.id];let a=1,i=1;if(k&&x.length){const n=e.getVTRowIndex(t._row),l=e.getVTColumnIndex(o),r=Ve(x,n,l);if(r){const{rowspan:e,colspan:t}=r;if(!e||!t)return"";e>1&&(a=e),t>1&&(i=t)}}return n&&l.push(`col--${n}`),"radio"===o.type?`<td class="${l.join(" ")}" rowspan="${a}" colspan="${i}"><div ${w?"":`style="width: ${o.renderWidth}px"`}><input type="radio" name="radio_${s}" ${t._radioDisabled?"disabled ":""}${En(r)?"checked":""}><span>${t._radioLabel}</span></div></td>`:"checkbox"===o.type?`<td class="${l.join(" ")}" rowspan="${a}" colspan="${i}"><div ${w?"":`style="width: ${o.renderWidth}px"`}><input type="checkbox" ${t._checkboxDisabled?"disabled ":""}${En(r)?"checked":""}><span>${t._checkboxLabel}</span></div></td>`:`<td class="${l.join(" ")}" rowspan="${a}" colspan="${i}" title="${r}"><div ${w?"":`style="width: ${o.renderWidth}px"`}>${W(r,!0)}</div></td>`})).join("")+"</tr>")})),D.push("</tbody>")),T){const{footerTableData:e}=o,t=Sn(n,e);t.length&&(D.push("<tfoot>"),t.forEach((e=>{D.push(`<tr>${r.map((t=>{const o=t.footerAlign||t.align||f||p,l=O(t,"showOverflow",m)?["col--ellipsis"]:[],r=S(n,e,t);return o&&l.push(`col--${o}`),`<td class="${l.join(" ")}" title="${r}"><div ${w?"":`style="width: ${t.renderWidth}px"`}>${W(r,!0)}</div></td>`})).join("")}</tr>`)})),D.push("</tfoot>"))}const F=!g&&v?`<script>(function(){var a=document.querySelector(".${$}");if(a){a.indeterminate=true}})()<\/script>`:"";return D.push("</table>",F),w?D.join(""):un(n,D.join(""))},$=(e,t,n)=>{let l=['<?xml version="1.0"?>','<?mso-application progid="Excel.Sheet"?>','<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">','<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">',"<Version>16.00</Version>","</DocumentProperties>",'<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">',"<WindowHeight>7920</WindowHeight>","<WindowWidth>21570</WindowWidth>","<WindowTopX>32767</WindowTopX>","<WindowTopY>32767</WindowTopY>","<ProtectStructure>False</ProtectStructure>","<ProtectWindows>False</ProtectWindows>","</ExcelWorkbook>",`<Worksheet ss:Name="${e.sheetName}">`,"<Table>",t.map((e=>`<Column ss:Width="${e.renderWidth}"/>`)).join("")].join("");if(e.isHeader&&(l+=`<Row>${t.map((t=>`<Cell><Data ss:Type="String">${C(e,t)}</Data></Cell>`)).join("")}</Row>`),n.forEach((e=>{l+="<Row>"+t.map((t=>`<Cell><Data ss:Type="String">${e[t.id]}</Data></Cell>`)).join("")+"</Row>"})),e.isFooter){const{footerTableData:n}=o,r=Sn(e,n);r.forEach((o=>{l+=`<Row>${t.map((t=>`<Cell><Data ss:Type="String">${S(e,o,t)}</Data></Cell>`)).join("")}</Row>`}))}return`${l}</Table></Worksheet></Workbook>`},I=(e,t,o)=>{if(t.length)switch(e.type){case"csv":return k(e,t,o);case"txt":return R(e,t,o);case"html":return M(e,t,o);case"xml":return $(e,t,o)}return""},D=(e,t)=>{const{filename:o,type:n,download:l}=e;if(!l){const o=cn(t,e);return Promise.resolve({type:n,content:t,blob:o})}gn({filename:o,type:n,content:t}).then((()=>{!1!==e.message&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({content:c.i18n("vxe.table.expSuccess"),status:"success"}))}))},F=t=>{const{remote:o,columns:n,colgroups:l,exportMethod:r,afterExportMethod:a}=t;return new Promise((a=>{if(o){const o={options:t,$table:e,$grid:x};a(r?r(o):o)}else{const o=E(t);a(e.preventEvent(null,"event.export",{options:t,columns:n,colgroups:l,datas:o},(()=>D(t,I(t,n,o)))))}})).then((o=>(Pn(n),t.print||a&&a({status:!0,options:t,$table:e,$grid:x}),Object.assign({status:!0},o)))).catch((()=>{Pn(n),t.print||a&&a({status:!1,options:t,$table:e,$grid:x});const o={status:!1};return Promise.reject(o)}))},N=(t,o)=>{const{tableFullColumn:l,_importResolve:r,_importReject:a}=n;let i={fields:[],rows:[]};switch(o.type){case"csv":i=Fn(l,t);break;case"txt":i=Nn(l,t);break;case"html":i=Ln(l,t);break;case"xml":i=An(l,t);break}const{fields:s,rows:u}=i,d=Vn(l,s);d?e.createData(u).then((t=>{let n;return n="insert"===o.mode?e.insert(t):e.reloadData(t),!1!==o.message&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({content:c.i18n("vxe.table.impSuccess",[u.length]),status:"success"})),n.then((()=>{r&&r({status:!0})}))})):!1!==o.message&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({content:c.i18n("vxe.error.impFields"),status:"error"}),a&&a({status:!1}))},L=(t,o)=>{const{importMethod:l,afterImportMethod:r}=o,{type:i,filename:s}=_(t);if(!l&&!a().includes(oo.globalConfs.importTypes,i)){!1!==o.message&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({content:c.i18n("vxe.error.notType",[i]),status:"error"}));const e={status:!1};return Promise.reject(e)}const u=new Promise(((r,a)=>{const c=e=>{r(e),n._importResolve=null,n._importReject=null},u=e=>{a(e),n._importResolve=null,n._importReject=null};if(n._importResolve=c,n._importReject=u,window.FileReader){const r=Object.assign({mode:"insert"},o,{type:i,filename:s});if(r.remote)l?Promise.resolve(l({file:t,options:r,$table:e})).then((()=>{c({status:!0})})).catch((()=>{c({status:!0})})):c({status:!0});else{const{tableFullColumn:o}=n;e.preventEvent(null,"event.import",{file:t,options:r,columns:o},(()=>{const e=new FileReader;e.onerror=()=>{f("vxe.error.notType",[i]),u({status:!1})},e.onload=e=>{N(e.target.result,r)},e.readAsText(t,r.encoding||"UTF-8")}))}}else f("vxe.error.notExp"),c({status:!0})}));return u.then((()=>{r&&r({status:!0,options:o,$table:e})})).catch((t=>(r&&r({status:!1,options:o,$table:e}),Promise.reject(t))))},A=(l,r)=>{const{treeConfig:i,showHeader:s,showFooter:c}=t,{initStore:d,mergeList:p,isGroup:f,footerTableData:m,exportStore:h,exportParams:v}=o,{collectColumn:x}=n,b=i,w=u.value,C=e.getCheckboxRecords(),y=!!m.length,T=!b&&p.length,E=Object.assign({message:!0,isHeader:s,isFooter:c},l),S=E.types||oo.globalConfs.exportTypes,k=E.modes,R=w.checkMethod,O=x.slice(0),{columns:M}=E,$=S.map((e=>({value:e,label:`vxe.export.types.${e}`}))),I=k.map((e=>({value:e,label:`vxe.export.modes.${e}`})));return a().eachTree(O,((e,t,o,n,l)=>{const r=e.children&&e.children.length;(r||wn(e))&&(e.checked=M?M.some((t=>{if(De(t))return e===t;if(a().isString(t))return e.field===t;{const o=t.id||t.colId,n=t.type,l=t.property||t.field;if(o)return e.id===o;if(l&&n)return e.property===l&&e.type===n;if(l)return e.property===l;if(n)return e.type===n}return!1})):e.visible,e.halfChecked=!1,e.disabled=l&&l.disabled||!!R&&!R({column:e}))})),Object.assign(h,{columns:O,typeList:$,modeList:I,hasFooter:y,hasMerge:T,hasTree:b,isPrint:r,hasColgroup:f,visible:!0}),Object.assign(v,{mode:C.length?"selected":"current"},E),-1===k.indexOf(v.mode)&&(v.mode=k[0]),-1===S.indexOf(v.type)&&(v.type=S[0]),d.export=!0,(0,g.nextTick)()},P={exportData(r){const{treeConfig:s}=t,{isGroup:u,tableGroupColumn:d}=o,{tableFullColumn:m,afterFullData:h}=n,g=i.value,v=l.value,b=Object.assign({isHeader:!0,isFooter:!0,isColgroup:!0,download:!0,type:"csv",mode:"current"},g,{print:!1},r),{type:w,mode:C,columns:y,original:T,beforeExportMethod:E}=b;let S=[];const k=y&&y.length?y:null;let R=b.columnFilterMethod;k||R||(R=T?({column:e})=>e.property:({column:e})=>wn(e)),k?(b._isCustomColumn=!0,S=a().searchTree(a().mapTree(k,(t=>{let o;if(t){if(De(t))o=t;else if(a().isString(t))o=e.getColumnByField(t);else{const n=t.id||t.colId,l=t.type,r=t.property||t.field;n?o=e.getColumnById(n):r&&l?o=m.find((e=>e.property===r&&e.type===l)):r?o=e.getColumnByField(r):l&&(o=m.find((e=>e.type===l)))}return o||{}}}),{children:"childNodes",mapChildren:"_children"}),((e,t)=>De(e)&&(!R||R({column:e,$columnIndex:t}))),{children:"_children",mapChildren:"childNodes",original:!0})):S=a().searchTree(u?d:m,((e,t)=>e.visible&&(!R||R({column:e,$columnIndex:t}))),{children:"children",mapChildren:"childNodes",original:!0});const O=[];if(a().eachTree(S,(e=>{const t=e.children&&e.children.length;t||O.push(e)}),{children:"childNodes"}),b.columns=O,b.colgroups=yn(S),b.filename||(b.filename=c.i18n(b.original?"vxe.table.expOriginFilename":"vxe.table.expFilename",[a().toDateString(Date.now(),"yyyyMMddHHmmss")])),b.sheetName||(b.sheetName=document.title),!b.exportMethod&&!a().includes(oo.globalConfs.exportTypes,w)){f("vxe.error.notType",[w]);const e={status:!1};return Promise.reject(e)}if(b.print||E&&E({options:b,$table:e,$grid:x}),!b.data)if(b.data=h,"selected"===C){const t=e.getCheckboxRecords();["html","pdf"].indexOf(w)>-1&&s?b.data=a().searchTree(e.getTableData().fullData,(o=>e.findRowIndexOf(t,o)>-1),Object.assign({},v,{data:"_row"})):b.data=t}else if("all"===C&&(x||p("vxe.error.errProp",["all","mode=current,selected"]),x&&!b.remote)){const{reactData:t}=x,{computeProxyOpts:o}=x.getComputeMaps(),n=o.value,{beforeQueryAll:l,afterQueryAll:r,ajax:i={},props:s={}}=n,c=i.queryAll;if(c||p("vxe.error.notFunc",["proxy-config.ajax.queryAll"]),c){const o={$table:e,$grid:x,sort:t.sortData,filters:t.filterData,form:t.formData,target:c,options:b};return Promise.resolve((l||c)(o)).catch((e=>e)).then((e=>(b.data=(s.list?a().get(e,s.list):e)||[],r&&r(o),F(b))))}}return F(b)},importByFile(t,o){const n=Object.assign({},o),{beforeImportMethod:l}=n;return l&&l({options:n,$table:e}),L(t,n)},importData(t){const o=s.value,n=Object.assign({types:oo.globalConfs.importTypes},o,t),{beforeImportMethod:l,afterImportMethod:r}=n;return l&&l({options:n,$table:e}),dn(n).catch((t=>(r&&r({status:!1,options:n,$table:e}),Promise.reject(t)))).then((e=>{const{file:t}=e;return L(t,n)}))},saveFile(e){return gn(e)},readFile(e){return dn(e)},print(t){const o=r.value,n=Object.assign({original:!1},o,t,{type:"html",download:!1,remote:!1,print:!0});return n.sheetName||(n.sheetName=document.title),new Promise((t=>{n.content?t(hn(e,n,n.content)):t(P.exportData(n).then((({content:t})=>hn(e,n,t))))}))},openImport(e){const{treeConfig:n,importConfig:l}=t,{initStore:r,importStore:a,importParams:i}=o,u=s.value,d=Object.assign({mode:"insert",message:!0,types:oo.globalConfs.importTypes},e,u),{types:p}=d,m=!!n;if(m)return void(d.message&&oo.modal.message({content:c.i18n("vxe.error.treeNotImp"),status:"error"}));l||f("vxe.error.reqProp",["import-config"]);const h=p.map((e=>({value:e,label:`vxe.export.types.${e}`}))),g=d.modes.map((e=>({value:e,label:`vxe.import.modes.${e}`})));Object.assign(a,{file:null,type:"",filename:"",modeList:g,typeList:h,visible:!0}),Object.assign(i,d),r.import=!0},openExport(e){const o=i.value;t.exportConfig||f("vxe.error.reqProp",["export-config"]),A(Object.assign({},o,e))},openPrint(e){const o=r.value;t.printConfig||f("vxe.error.reqProp",["print-config"]),A(Object.assign({},o,e),!0)}};return P},setupGrid(e){return e.extendTableMethods(_n)}};var Hn=jn;const Bn=e=>{const t=Object.assign({},e,{type:"html"});hn(null,t,t.content)},zn={ExportPanel:Qo,ImportPanel:on,install(e){oo.saveFile=gn,oo.readFile=dn,oo.print=Bn,oo.setup({export:{types:{csv:0,html:0,xml:0,txt:0}}}),oo.hooks.add("$tableExport",Hn),e.component(Qo.name,Qo),e.component(on.name,on)}},Wn=zn;uo.component(Qo.name,Qo),uo.component(on.name,on);function qn(e,t){let o=0,n=0;const l=!Y.firefox&&ee(e,"vxe-checkbox--label");if(l){const t=getComputedStyle(e);o-=a().toNumber(t.paddingTop),n-=a().toNumber(t.paddingLeft)}while(e&&e!==t)if(o+=e.offsetTop,n+=e.offsetLeft,e=e.offsetParent,l){const t=getComputedStyle(e);o-=a().toNumber(t.paddingTop),n-=a().toNumber(t.paddingLeft)}return{offsetTop:o,offsetLeft:n}}const Un={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{refElem:l}=e.getRefMaps(),{computeEditOpts:r,computeCheckboxOpts:i,computeMouseOpts:s,computeTreeOpts:c}=e.getComputeMaps();function u(t,l,r){let a=0,i=[];const s=r>0,c=r>0?r:Math.abs(r)+l.offsetHeight,{scrollYLoad:u}=o,{afterFullData:d,scrollYStore:p}=n;if(u){const o=e.getVTRowIndex(t.row);i=s?d.slice(o,o+Math.ceil(c/p.rowHeight)):d.slice(o-Math.floor(c/p.rowHeight)+1,o+1)}else{const t=s?"next":"previous";while(l&&a<c){const o=e.getRowNode(l);o&&(i.push(o.item),a+=l.offsetHeight,l=l[`${t}ElementSibling`])}}return i}const d=(t,o)=>{const{column:r,cell:a}=o;if("checkbox"===r.type){const i=l.value,{elemStore:s}=n,c=t.clientX,d=t.clientY,p=s[`${r.fixed||"main"}-body-wrapper`]||s["main-body-wrapper"],f=p?p.value:null;if(!f)return;const m=f.querySelector(".vxe-table--checkbox-range"),h=document.onmousemove,g=document.onmouseup,v=a.parentNode,x=e.getCheckboxRecords();let b=[];const w=1,C=qn(t.target,f),y=C.offsetTop+t.offsetY,T=C.offsetLeft+t.offsetX,E=f.scrollTop,S=v.offsetHeight;let k=null,R=!1,O=1;const M=(t,o)=>{e.dispatchEvent(`checkbox-range-${t}`,{records:e.getCheckboxRecords(),reserves:e.getCheckboxReserveRecords()},o)},$=t=>{const{clientX:n,clientY:l}=t,r=n-c,a=l-d+(f.scrollTop-E);let i=Math.abs(a),s=Math.abs(r),p=y,h=T;a<w?(p+=a,p<w&&(p=w,i=y)):i=Math.min(i,f.scrollHeight-y-w),r<w?(h+=r,s>T&&(h=w,s=T)):s=Math.min(s,f.clientWidth-T-w),m.style.height=`${i}px`,m.style.width=`${s}px`,m.style.left=`${h}px`,m.style.top=`${p}px`,m.style.display="block";const g=u(o,v,a<w?-i:i);i>10&&g.length!==b.length&&(b=g,t.ctrlKey?g.forEach((t=>{e.handleSelectRow({row:t},-1===x.indexOf(t))})):(e.setAllCheckboxRow(!1),e.handleCheckedCheckboxRow(g,!0,!1)),M("change",t))},I=()=>{clearTimeout(k),k=null},D=t=>{I(),k=setTimeout((()=>{if(k){const{scrollLeft:o,scrollTop:n,clientHeight:l,scrollHeight:r}=f,a=Math.ceil(50*O/S);R?n+l<r?(e.scrollTo(o,n+a),D(t),$(t)):I():n?(e.scrollTo(o,n-a),D(t),$(t)):I()}}),50)};oe(i,"drag--range"),document.onmousemove=e=>{e.preventDefault(),e.stopPropagation();const{clientY:t}=e,{boundingTop:o}=de(f);t<o?(R=!1,O=o-t,k||D(e)):t>o+f.clientHeight?(R=!0,O=t-o-f.clientHeight,k||D(e)):k&&I(),$(e)},document.onmouseup=e=>{I(),te(i,"drag--range"),m.removeAttribute("style"),document.onmousemove=h,document.onmouseup=g,M("end",e)},M("start",t)}},p=(o,n)=>{const{editConfig:l,checkboxConfig:a,mouseConfig:c}=t,u=i.value,p=s.value,f=r.value;if(c&&p.area&&e.handleCellAreaEvent)return e.handleCellAreaEvent(o,n);a&&u.range&&d(o,n),c&&p.selected&&(l&&"cell"!==f.mode||e.handleSelected(n,o))},f={moveTabSelected(o,l,a){const{editConfig:i}=t,{afterFullData:s,visibleColumn:c}=n,u=r.value;let d,p,f;const m=Object.assign({},o),h=e.getVTRowIndex(m.row),g=e.getVTColumnIndex(m.column);a.preventDefault(),l?g<=0?h>0&&(p=h-1,d=s[p],f=c.length-1):f=g-1:g>=c.length-1?h<s.length-1&&(p=h+1,d=s[p],f=0):f=g+1;const v=c[f];v&&(d?(m.rowIndex=p,m.row=d):m.rowIndex=h,m.columnIndex=f,m.column=v,m.cell=e.getCell(m.row,m.column),i?"click"!==u.trigger&&"dblclick"!==u.trigger||("row"===u.mode?e.handleActived(m,a):e.scrollToRow(m.row,m.column).then((()=>e.handleSelected(m,a)))):e.scrollToRow(m.row,m.column).then((()=>e.handleSelected(m,a))))},moveCurrentRow(l,r,i){const{treeConfig:s}=t,{currentRow:u}=o,{afterFullData:d}=n,p=c.value,f=p.children||p.childrenField;let m;if(i.preventDefault(),u)if(s){const{index:e,items:t}=a().findTree(d,(e=>e===u),{children:f});l&&e>0?m=t[e-1]:r&&e<t.length-1&&(m=t[e+1])}else{const t=e.getVTRowIndex(u);l&&t>0?m=d[t-1]:r&&t<d.length-1&&(m=d[t+1])}else m=d[0];if(m){const t={$table:e,row:m,rowIndex:e.getRowIndex(m),$rowIndex:e.getVMRowIndex(m)};e.scrollToRow(m).then((()=>e.triggerCurrentRowEvent(i,t)))}},moveSelected(t,o,l,r,a,i){const{afterFullData:s,visibleColumn:c}=n,u=Object.assign({},t),d=e.getVTRowIndex(u.row),p=e.getVTColumnIndex(u.column);i.preventDefault(),l&&d>0?(u.rowIndex=d-1,u.row=s[u.rowIndex]):a&&d<s.length-1?(u.rowIndex=d+1,u.row=s[u.rowIndex]):o&&p?(u.columnIndex=p-1,u.column=c[u.columnIndex]):r&&p<c.length-1&&(u.columnIndex=p+1,u.column=c[u.columnIndex]),e.scrollToRow(u.row,u.column).then((()=>{u.cell=e.getCell(u.row,u.column),e.handleSelected(u,i)}))},triggerHeaderCellMousedownEvent(o,n){const{mouseConfig:l}=t,r=s.value;if(l&&r.area&&e.handleHeaderCellAreaEvent){const t=o.currentTarget,l=ce(o,t,"vxe-cell--sort").flag,r=ce(o,t,"vxe-cell--filter").flag;e.handleHeaderCellAreaEvent(o,Object.assign({cell:t,triggerSort:l,triggerFilter:r},n))}e.focus(),e.closeMenu&&e.closeMenu()},triggerCellMousedownEvent(t,o){const n=t.currentTarget;o.cell=n,p(t,o),e.focus(),e.closeFilter(),e.closeMenu&&e.closeMenu()}};return f}};var Xn=Un;const Yn={install(){oo.hooks.add("$tableKeyboard",Xn)}},Gn=Yn;class Kn{constructor(e){Object.assign(this,{$options:e,required:e.required,min:e.min,max:e.max,type:e.type,pattern:e.pattern,validator:e.validator,trigger:e.trigger,maxWidth:e.maxWidth})}get content(){return z(this.$options.content||this.$options.message)}get message(){return this.content}}const Zn=["fullValidate","validate","clearValidate"],Jn={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{refValidTooltip:l}=e.getRefMaps(),{computeValidOpts:r,computeTreeOpts:i,computeEditOpts:s}=e.getComputeMaps();let u,d={},m={};const h=t=>new Promise((o=>{const n=r.value;!1===n.autoPos?(e.dispatchEvent("valid-error",t,null),o()):e.handleActived(t,{type:"valid-error",trigger:"call"}).then((()=>{o(m.showValidTooltip(t))}))})),v=e=>{const t=r.value;if("single"===t.msgMode){const t=Object.keys(e),o=e;if(t.length){const n=t[0];o[n]=e[n]}return o}return e},x=(l,s,p)=>{const f={},{editRules:x,treeConfig:b}=t,{afterFullData:w,visibleColumn:C}=n,y=i.value,T=y.children||y.childrenField,E=r.value;let S;!0===l?S=w:l&&(a().isFunction(l)?s=l:S=a().isArray(l)?l:[l]),S||(S=e.getInsertRecords?e.getInsertRecords().concat(e.getUpdateRecords()):[]);const k=[];n._lastCallTime=Date.now(),u=!1,d.clearValidate();const R={};if(x){const t=e.getColumns(),n=o=>{if(p||!u){const n=[];t.forEach((t=>{!p&&u||!a().has(x,t.property)||n.push(m.validCellRules("all",o,t).catch((({rule:n,rules:l})=>{const r={rule:n,rules:l,rowIndex:e.getRowIndex(o),row:o,columnIndex:e.getColumnIndex(t),column:t,field:t.property,$table:e};if(f[t.property]||(f[t.property]=[]),R[`${ye(e,o)}:${t.id}`]={column:t,row:o,rule:n,content:n.content},f[t.property].push(r),!p)return u=!0,Promise.reject(r)})))})),k.push(Promise.all(n))}};return b?a().eachTree(S,n,{children:T}):S.forEach(n),Promise.all(k).then((()=>{const e=Object.keys(f);return o.validErrorMaps=v(R),(0,g.nextTick)().then((()=>{if(e.length)return Promise.reject(f[e[0]][0]);s&&s()}))})).catch((t=>new Promise(((o,n)=>{const l=()=>{(0,g.nextTick)((()=>{s?(s(f),o()):"obsolete"===c.validToReject?n(f):o(f)}))},r=()=>{t.cell=e.getCell(t.row,t.column),me(t.cell),h(t).then(l)};if(!1===E.autoPos)l();else{const o=t.row,n=t.column,l=w.indexOf(o),a=C.indexOf(n),i=l>0?w[l-1]:o,s=a>0?C[l-1]:n;e.scrollToRow(i,s).then(r)}}))))}return o.validErrorMaps={},(0,g.nextTick)().then((()=>{s&&s()}))};d={fullValidate(e,t){return a().isFunction(t)&&p("vxe.error.notValidators",["fullValidate(rows, callback)","fullValidate(rows)"]),x(e,t,!0)},validate(e,t){return a().isFunction(t)&&p("vxe.error.notValidators",["validate(rows, callback)","validate(rows)"]),x(e,t)},clearValidate(t,n){const{validErrorMaps:i}=o,s=l.value,c=r.value,u=a().isArray(t)?t:t?[t]:[],d=a().isArray(n)?n:(n?[n]:[]).map((t=>Te(e,t)));let p={};if(s&&s.reactData.visible&&s.close(),"single"===c.msgMode)return o.validErrorMaps={},(0,g.nextTick)();if(u.length&&d.length)p=Object.assign({},i),u.forEach((t=>{d.forEach((o=>{const n=`${ye(e,t)}:${o.id}`;p[n]&&delete p[n]}))}));else if(u.length){const t=u.map((t=>`${ye(e,t)}`));a().each(i,((e,o)=>{t.indexOf(o.split(":")[0])>-1&&(p[o]=e)}))}else if(d.length){const e=d.map((e=>`${e.id}`));a().each(i,((t,o)=>{e.indexOf(o.split(":")[1])>-1&&(p[o]=t)}))}return o.validErrorMaps=p,(0,g.nextTick)()}};const b=(e,t)=>{const{type:o,min:n,max:l,pattern:r}=e,i="number"===o,s=i?a().toNumber(t):a().getSize(t);return!(!i||!isNaN(t))||(!a().eqNull(n)&&s<a().toNumber(n)||(!a().eqNull(l)&&s>a().toNumber(l)||!(!r||(a().isRegExp(r)?r:new RegExp(r)).test(t))))};return m={validCellRules(o,n,l,r){const{editRules:i}=t,{field:s}=l,c=[],d=[];if(s&&i){const t=a().get(i,s);if(t){const i=a().isUndefined(r)?a().get(n,s):r;t.forEach((r=>{const{type:s,trigger:m,required:h,validator:g}=r;if("all"===o||!m||o===m)if(g){const o={cellValue:i,rule:r,rules:t,row:n,rowIndex:e.getRowIndex(n),column:l,columnIndex:e.getColumnIndex(l),field:l.field,$table:e,$grid:e.xegrid};let s;if(a().isString(g)){const e=oo.validators.get(g);e?e.cellValidatorMethod?s=e.cellValidatorMethod(o):p("vxe.error.notValidators",[g]):f("vxe.error.notValidators",[g])}else s=g(o);s&&(a().isError(s)?(u=!0,c.push(new Kn({type:"custom",trigger:m,content:s.message,rule:new Kn(r)}))):s.catch&&d.push(s.catch((e=>{u=!0,c.push(new Kn({type:"custom",trigger:m,content:e&&e.message?e.message:r.content||r.message,rule:new Kn(r)}))}))))}else{const e="array"===s,t=a().isArray(i);let o=!0;o=e||t?!t||!i.length:a().isString(i)?q(i.trim()):q(i),(h?o||b(r,i):!o&&b(r,i))&&(u=!0,c.push(new Kn(r)))}}))}}return Promise.all(d).then((()=>{if(c.length){const e={rules:c,rule:c[0]};return Promise.reject(e)}}))},hasCellRules(e,o,n){const{editRules:l}=t,{field:r}=n;if(r&&l){const t=a().get(l,r);return t&&!!a().find(t,(t=>"all"===e||!t.trigger||e===t.trigger))}return!1},triggerValidate(e){const{editConfig:n,editRules:l}=t,{editStore:a}=o,{actived:i}=a,c=s.value,u=r.value;if(l&&"single"===u.msgMode&&(o.validErrorMaps={}),n&&l&&i.row){const{row:t,column:o,cell:n}=i.args;if(m.hasCellRules(e,t,o))return m.validCellRules(e,t,o).then((()=>{"row"===c.mode&&d.clearValidate(t,o)})).catch((({rule:l})=>{if(!l.trigger||e===l.trigger){const e={rule:l,row:t,column:o,cell:n};return m.showValidTooltip(e),Promise.reject(e)}return Promise.resolve()}))}return Promise.resolve()},showValidTooltip(n){const{height:a}=t,{tableData:i,validStore:s,validErrorMaps:c}=o,{rule:u,row:d,column:p,cell:f}=n,m=r.value,h=l.value,v=u.content;return s.visible=!0,"single"===m.msgMode?o.validErrorMaps={[`${ye(e,d)}:${p.id}`]:{column:p,row:d,rule:u,content:v}}:o.validErrorMaps=Object.assign({},c,{[`${ye(e,d)}:${p.id}`]:{column:p,row:d,rule:u,content:v}}),e.dispatchEvent("valid-error",n,null),h&&h&&("tooltip"===m.message||"default"===m.message&&!a&&i.length<2)?h.open(f,v):(0,g.nextTick)()}},{...d,...m}},setupGrid(e){return e.extendTableMethods(Zn)}};var Qn=Jn;const el={install(){oo.hooks.add("$tableValidator",Qn)}},tl=el;var ol=(0,g.defineComponent)({name:"VxeIcon",props:{name:String,roll:Boolean,status:String},emits:["click"],setup(e,{emit:t}){const o=e=>{t("click",{$event:e})};return()=>{const{name:t,roll:n,status:l}=e;return(0,g.h)("i",{class:[`vxe-icon-${t}`,n||"",l?[`theme--${l}`]:""],onClick:o})}}});const nl=Object.assign(ol,{install(e){e.component(ol.name,ol)}}),ll=nl;uo.component(nl.name,nl);function rl(e){const{$table:t,column:o}=e,n=o.titlePrefix||o.titleHelp;return n?[(0,g.h)("i",{class:["vxe-cell-help-icon",n.icon||c.icon.TABLE_HELP],onMouseenter(o){t.triggerHeaderHelpEvent(o,e)},onMouseleave(e){t.handleTargetLeaveEvent(e)}})]:[]}function al(e,t){const{$table:o,column:n}=e,{props:l,reactData:r}=o,{computeTooltipOpts:i}=o.getComputeMaps(),{showHeaderOverflow:s}=l,{type:c,showHeaderOverflow:u}=n,d=i.value,p=d.showAll,f=a().isUndefined(u)||a().isNull(u)?s:u,m="title"===f,h=!0===f||"tooltip"===f,v={};return(m||h||p)&&(v.onMouseenter=t=>{r._isResize||(m?se(t.currentTarget,n):(h||p)&&o.triggerHeaderTooltipEvent(t,e))}),(h||p)&&(v.onMouseleave=e=>{r._isResize||(h||p)&&o.handleTargetLeaveEvent(e)}),["html"===c&&a().isString(t)?(0,g.h)("span",{class:"vxe-cell--title",innerHTML:t,...v}):(0,g.h)("span",{class:"vxe-cell--title",...v},We(t))]}function il(e){const{$table:t,column:o,_columnIndex:n,items:l}=e,{slots:r,editRender:a,cellRender:i}=o,s=a||i,c=r?r.footer:null;if(c)return t.callSlot(c,e);if(s){const t=oo.renderer.get(s.name);if(t&&t.renderFooter)return We(t.renderFooter(s,e))}return[W(l[n],1)]}function sl(e){const{$table:t,row:o,column:n}=e;return W(t.getCellLabel(o,n),1)}const cl={createColumn(e,t){const{type:o,sortable:n,filters:l,editRender:r,treeNode:a}=t,{props:i}=e,{editConfig:s}=i,{computeEditOpts:c,computeCheckboxOpts:u}=e.getComputeMaps(),d=u.value,p=c.value,f={renderHeader:cl.renderDefaultHeader,renderCell:a?cl.renderTreeCell:cl.renderDefaultCell,renderFooter:cl.renderDefaultFooter};switch(o){case"seq":f.renderHeader=cl.renderSeqHeader,f.renderCell=a?cl.renderTreeIndexCell:cl.renderSeqCell;break;case"radio":f.renderHeader=cl.renderRadioHeader,f.renderCell=a?cl.renderTreeRadioCell:cl.renderRadioCell;break;case"checkbox":f.renderHeader=cl.renderCheckboxHeader,f.renderCell=d.checkField?a?cl.renderTreeSelectionCellByProp:cl.renderCheckboxCellByProp:a?cl.renderTreeSelectionCell:cl.renderCheckboxCell;break;case"expand":f.renderCell=cl.renderExpandCell,f.renderData=cl.renderExpandData;break;case"html":f.renderCell=a?cl.renderTreeHTMLCell:cl.renderHTMLCell,l&&n?f.renderHeader=cl.renderSortAndFilterHeader:n?f.renderHeader=cl.renderSortHeader:l&&(f.renderHeader=cl.renderFilterHeader);break;default:s&&r?(f.renderHeader=cl.renderEditHeader,f.renderCell="cell"===p.mode?a?cl.renderTreeCellEdit:cl.renderCellEdit:a?cl.renderTreeRowEdit:cl.renderRowEdit):l&&n?f.renderHeader=cl.renderSortAndFilterHeader:n?f.renderHeader=cl.renderSortHeader:l&&(f.renderHeader=cl.renderFilterHeader)}return Fe(e,t,f)},renderHeaderTitle(e){const{$table:t,column:o}=e,{slots:n,editRender:l,cellRender:r}=o,a=l||r,i=n?n.header:null;if(i)return al(e,t.callSlot(i,e));if(a){const t=oo.renderer.get(a.name);if(t&&t.renderHeader)return al(e,We(t.renderHeader(a,e)))}return al(e,W(o.getTitle(),1))},renderDefaultHeader(e){return rl(e).concat(cl.renderHeaderTitle(e))},renderDefaultCell(e){const{$table:t,row:o,column:n}=e,{slots:l,editRender:r,cellRender:a}=n,i=r||a,s=l?l.default:null;if(s)return t.callSlot(s,e);if(i){const t=r?"renderCell":"renderDefault",o=oo.renderer.get(i.name),n=o?o[t]:null;if(n)return We(n(i,Object.assign({$type:r?"edit":"cell"},e)))}const c=t.getCellLabel(o,n),u=r?r.placeholder:"";return[(0,g.h)("span",{class:"vxe-cell--label"},r&&q(c)?[(0,g.h)("span",{class:"vxe-cell--placeholder"},W(z(u),1))]:W(c,1))]},renderTreeCell(e){return cl.renderTreeIcon(e,cl.renderDefaultCell(e))},renderDefaultFooter(e){return[(0,g.h)("span",{class:"vxe-cell--item"},il(e))]},renderTreeIcon(e,t){const{$table:o,isHidden:n}=e,{reactData:l}=o,{computeTreeOpts:r}=o.getComputeMaps(),{treeExpandedMaps:a,treeExpandLazyLoadedMaps:i}=l,s=r.value,{row:u,column:d,level:p}=e,{slots:f}=d,{indent:m,lazy:h,trigger:v,iconLoaded:x,showIcon:b,iconOpen:w,iconClose:C}=s,y=s.children||s.childrenField,T=s.hasChild||s.hasChildField,E=u[y],S=f?f.icon:null;let k=!1,R=!1,O=!1;const M={};if(S)return o.callSlot(S,e);if(!n){const e=ye(o,u);R=!!a[e],h&&(O=!!i[e],k=u[T])}return v&&"default"!==v||(M.onClick=t=>{t.stopPropagation(),o.triggerTreeExpandEvent(t,e)}),[(0,g.h)("div",{class:["vxe-cell--tree-node",{"is--active":R}],style:{paddingLeft:p*m+"px"}},[b&&(E&&E.length||k)?[(0,g.h)("div",{class:"vxe-tree--btn-wrapper",...M},[(0,g.h)("i",{class:["vxe-tree--node-btn",O?x||c.icon.TABLE_TREE_LOADED:R?w||c.icon.TABLE_TREE_OPEN:C||c.icon.TABLE_TREE_CLOSE]})])]:null,(0,g.h)("div",{class:"vxe-tree-cell"},t)])]},renderSeqHeader(e){const{$table:t,column:o}=e,{slots:n}=o,l=n?n.header:null;return al(e,l?t.callSlot(l,e):W(o.getTitle(),1))},renderSeqCell(e){const{$table:t,column:o}=e,{props:n}=t,{treeConfig:l}=n,{computeSeqOpts:r}=t.getComputeMaps(),a=r.value,{slots:i}=o,s=i?i.default:null;if(s)return t.callSlot(s,e);const{seq:c}=e,u=a.seqMethod;return[W(u?u(e):l?c:(a.startIndex||0)+c,1)]},renderTreeIndexCell(e){return cl.renderTreeIcon(e,cl.renderSeqCell(e))},renderRadioHeader(e){const{$table:t,column:o}=e,{slots:n}=o,l=n?n.header:null,r=n?n.title:null;return al(e,l?t.callSlot(l,e):[(0,g.h)("span",{class:"vxe-radio--label"},r?t.callSlot(r,e):W(o.getTitle(),1))])},renderRadioCell(e){const{$table:t,column:o,isHidden:n}=e,{reactData:l}=t,{computeRadioOpts:r}=t.getComputeMaps(),{selectRadioRow:i}=l,s=r.value,{slots:u}=o,{labelField:d,checkMethod:p,visibleMethod:f}=s,{row:m}=e,h=u?u.default:null,v=u?u.radio:null,x=t.eqRow(m,i),b=!f||f({row:m});let w,C=!!p;n||(w={onClick(o){!C&&b&&(o.stopPropagation(),t.triggerRadioRowEvent(o,e))}},p&&(C=!p({row:m})));const y={...e,checked:x,disabled:C,visible:b};if(v)return t.callSlot(v,y);const T=[];return b&&T.push((0,g.h)("span",{class:["vxe-radio--icon",x?c.icon.TABLE_RADIO_CHECKED:c.icon.TABLE_RADIO_UNCHECKED]})),(h||d)&&T.push((0,g.h)("span",{class:"vxe-radio--label"},h?t.callSlot(h,y):a().get(m,d))),[(0,g.h)("span",{class:["vxe-cell--radio",{"is--checked":x,"is--disabled":C}],...w},T)]},renderTreeRadioCell(e){return cl.renderTreeIcon(e,cl.renderRadioCell(e))},renderCheckboxHeader(e){const{$table:t,column:o,isHidden:n}=e,{reactData:l}=t,{computeIsAllCheckboxDisabled:r,computeCheckboxOpts:a}=t.getComputeMaps(),{isAllSelected:i,isIndeterminate:s}=l,u=r.value,{slots:d}=o,p=d?d.header:null,f=d?d.title:null,m=a.value,h=o.getTitle();let v;n||(v={onClick(e){u||(e.stopPropagation(),t.triggerCheckAllEvent(e,!i))}});const x={...e,checked:i,disabled:u,indeterminate:s};return p?al(x,t.callSlot(p,x)):(m.checkStrictly?m.showHeader:!1!==m.showHeader)?al(x,[(0,g.h)("span",{class:["vxe-cell--checkbox",{"is--checked":i,"is--disabled":u,"is--indeterminate":s}],title:c.i18n("vxe.table.allTitle"),...v},[(0,g.h)("span",{class:["vxe-checkbox--icon",s?c.icon.TABLE_CHECKBOX_INDETERMINATE:i?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})].concat(f||h?[(0,g.h)("span",{class:"vxe-checkbox--label"},f?t.callSlot(f,x):h)]:[]))]):al(x,[(0,g.h)("span",{class:"vxe-checkbox--label"},f?t.callSlot(f,x):h)])},renderCheckboxCell(e){const{$table:t,row:o,column:n,isHidden:l}=e,{props:r,reactData:i}=t,{treeConfig:s}=r,{selectCheckboxMaps:u,treeIndeterminateMaps:d}=i,{computeCheckboxOpts:p}=t.getComputeMaps(),f=p.value,{labelField:m,checkMethod:h,visibleMethod:v}=f,{slots:x}=n,b=x?x.default:null,w=x?x.checkbox:null;let C=!1,y=!1;const T=!v||v({row:o});let E,S=!!h;if(!l){const n=ye(t,o);y=!!u[n],E={onClick(o){!S&&T&&(o.stopPropagation(),t.triggerCheckRowEvent(o,e,!y))}},h&&(S=!h({row:o})),s&&(C=!!d[n])}const k={...e,checked:y,disabled:S,visible:T,indeterminate:C};if(w)return t.callSlot(w,k);const R=[];return T&&R.push((0,g.h)("span",{class:["vxe-checkbox--icon",C?c.icon.TABLE_CHECKBOX_INDETERMINATE:y?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})),(b||m)&&R.push((0,g.h)("span",{class:"vxe-checkbox--label"},b?t.callSlot(b,k):a().get(o,m))),[(0,g.h)("span",{class:["vxe-cell--checkbox",{"is--checked":y,"is--disabled":S,"is--indeterminate":C}],...E},R)]},renderTreeSelectionCell(e){return cl.renderTreeIcon(e,cl.renderCheckboxCell(e))},renderCheckboxCellByProp(e){const{$table:t,row:o,column:n,isHidden:l}=e,{props:r,reactData:i}=t,{treeConfig:s}=r,{treeIndeterminateMaps:u}=i,{computeCheckboxOpts:d}=t.getComputeMaps(),p=d.value,{labelField:f,checkField:m,checkMethod:h,visibleMethod:v}=p,x=p.indeterminateField||p.halfField,{slots:b}=n,w=b?b.default:null,C=b?b.checkbox:null;let y=!1,T=!1;const E=!v||v({row:o});let S,k=!!h;if(!l){const n=ye(t,o);T=a().get(o,m),S={onClick(o){!k&&E&&(o.stopPropagation(),t.triggerCheckRowEvent(o,e,!T))}},h&&(k=!h({row:o})),s&&(y=!!u[n])}const R={...e,checked:T,disabled:k,visible:E,indeterminate:y};if(C)return t.callSlot(C,R);const O=[];return E&&(O.push((0,g.h)("span",{class:["vxe-checkbox--icon",y?c.icon.TABLE_CHECKBOX_INDETERMINATE:T?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})),(w||f)&&O.push((0,g.h)("span",{class:"vxe-checkbox--label"},w?t.callSlot(w,R):a().get(o,f)))),[(0,g.h)("span",{class:["vxe-cell--checkbox",{"is--checked":T,"is--disabled":k,"is--indeterminate":x&&!T?o[x]:y}],...S},O)]},renderTreeSelectionCellByProp(e){return cl.renderTreeIcon(e,cl.renderCheckboxCellByProp(e))},renderExpandCell(e){const{$table:t,isHidden:o,row:n,column:l}=e,{reactData:r}=t,{rowExpandedMaps:i,rowExpandLazyLoadedMaps:s}=r,{computeExpandOpts:u}=t.getComputeMaps(),d=u.value,{lazy:p,labelField:f,iconLoaded:m,showIcon:h,iconOpen:v,iconClose:x,visibleMethod:b}=d,{slots:w}=l,C=w?w.default:null,y=w?w.icon:null;let T=!1,E=!1;if(y)return t.callSlot(y,e);if(!o){const e=ye(t,n);T=!!i[e],p&&(E=!!s[e])}return[!h||b&&!b(e)?null:(0,g.h)("span",{class:["vxe-table--expanded",{"is--active":T}],onClick(o){o.stopPropagation(),t.triggerRowExpandEvent(o,e)}},[(0,g.h)("i",{class:["vxe-table--expand-btn",E?m||c.icon.TABLE_EXPAND_LOADED:T?v||c.icon.TABLE_EXPAND_OPEN:x||c.icon.TABLE_EXPAND_CLOSE]})]),C||f?(0,g.h)("span",{class:"vxe-table--expand-label"},C?t.callSlot(C,e):a().get(n,f)):null]},renderExpandData(e){const{$table:t,column:o}=e,{slots:n,contentRender:l}=o,r=n?n.content:null;if(r)return t.callSlot(r,e);if(l){const t=oo.renderer.get(l.name);if(t&&t.renderExpand)return We(t.renderExpand(l,e))}return[]},renderHTMLCell(e){const{$table:t,column:o}=e,{slots:n}=o,l=n?n.default:null;return l?t.callSlot(l,e):[(0,g.h)("span",{class:"vxe-cell--html",innerHTML:sl(e)})]},renderTreeHTMLCell(e){return cl.renderTreeIcon(e,cl.renderHTMLCell(e))},renderSortAndFilterHeader(e){return cl.renderDefaultHeader(e).concat(cl.renderSortIcon(e)).concat(cl.renderFilterIcon(e))},renderSortHeader(e){return cl.renderDefaultHeader(e).concat(cl.renderSortIcon(e))},renderSortIcon(e){const{$table:t,column:o}=e,{computeSortOpts:n}=t.getComputeMaps(),l=n.value,{showIcon:r,iconAsc:a,iconDesc:i}=l,{order:s}=o;return r?[(0,g.h)("span",{class:"vxe-cell--sort"},[(0,g.h)("i",{class:["vxe-sort--asc-btn",a||c.icon.TABLE_SORT_ASC,{"sort--active":"asc"===s}],title:c.i18n("vxe.table.sortAsc"),onClick(e){e.stopPropagation(),t.triggerSortEvent(e,o,"asc")}}),(0,g.h)("i",{class:["vxe-sort--desc-btn",i||c.icon.TABLE_SORT_DESC,{"sort--active":"desc"===s}],title:c.i18n("vxe.table.sortDesc"),onClick(e){e.stopPropagation(),t.triggerSortEvent(e,o,"desc")}})])]:[]},renderFilterHeader(e){return cl.renderDefaultHeader(e).concat(cl.renderFilterIcon(e))},renderFilterIcon(e){const{$table:t,column:o,hasFilter:n}=e,{reactData:l}=t,{filterStore:r}=l,{computeFilterOpts:a}=t.getComputeMaps(),i=a.value,{showIcon:s,iconNone:u,iconMatch:d}=i;return s?[(0,g.h)("span",{class:["vxe-cell--filter",{"is--active":r.visible&&r.column===o}]},[(0,g.h)("i",{class:["vxe-filter--btn",n?d||c.icon.TABLE_FILTER_MATCH:u||c.icon.TABLE_FILTER_NONE],title:c.i18n("vxe.table.filter"),onClick(o){t.triggerFilterEvent&&t.triggerFilterEvent(o,e.column,e)}})])]:[]},renderEditHeader(e){const{$table:t,column:o}=e,{props:n}=t,{computeEditOpts:l}=t.getComputeMaps(),{editConfig:r,editRules:i}=n,s=l.value,{sortable:u,filters:d,editRender:p}=o;let f=!1;if(i){const e=a().get(i,o.field);e&&(f=e.some((e=>e.required)))}return(P(r)?[f&&s.showAsterisk?(0,g.h)("i",{class:"vxe-cell--required-icon"}):null,P(p)&&s.showIcon?(0,g.h)("i",{class:["vxe-cell--edit-icon",s.icon||c.icon.TABLE_EDIT]}):null]:[]).concat(cl.renderDefaultHeader(e)).concat(u?cl.renderSortIcon(e):[]).concat(d?cl.renderFilterIcon(e):[])},renderRowEdit(e){const{$table:t,column:o}=e,{reactData:n}=t,{editStore:l}=n,{actived:r}=l,{editRender:a}=o;return cl.runRenderer(e,P(a)&&r&&r.row===e.row)},renderTreeRowEdit(e){return cl.renderTreeIcon(e,cl.renderRowEdit(e))},renderCellEdit(e){const{$table:t,column:o}=e,{reactData:n}=t,{editStore:l}=n,{actived:r}=l,{editRender:a}=o;return cl.runRenderer(e,P(a)&&r&&r.row===e.row&&r.column===e.column)},renderTreeCellEdit(e){return cl.renderTreeIcon(e,cl.renderCellEdit(e))},runRenderer(e,t){const{$table:o,column:n}=e,{slots:l,editRender:r,formatter:a}=n,i=l?l.default:null,s=l?l.edit:null,c=oo.renderer.get(r.name);return t?s?o.callSlot(s,e):c&&c.renderEdit?We(c.renderEdit(r,Object.assign({$type:"edit"},e))):[]:i?o.callSlot(i,e):a?[(0,g.h)("span",{class:"vxe-cell--label"},sl(e))]:cl.renderDefaultCell(e)}};var ul=cl;const dl={colId:[String,Number],type:String,field:String,title:String,width:[Number,String],minWidth:[Number,String],maxWidth:[Number,String],resizable:{type:Boolean,default:null},fixed:String,align:String,headerAlign:String,footerAlign:String,showOverflow:{type:[Boolean,String],default:null},showHeaderOverflow:{type:[Boolean,String],default:null},showFooterOverflow:{type:[Boolean,String],default:null},className:[String,Function],headerClassName:[String,Function],footerClassName:[String,Function],formatter:[Function,Array,String],sortable:Boolean,sortBy:[String,Function],sortType:String,filters:{type:Array,default:null},filterMultiple:{type:Boolean,default:!0},filterMethod:Function,filterResetMethod:Function,filterRecoverMethod:Function,filterRender:Object,treeNode:Boolean,visible:{type:Boolean,default:null},headerExportMethod:Function,exportMethod:Function,footerExportMethod:Function,titleHelp:Object,titlePrefix:Object,cellType:String,cellRender:Object,editRender:Object,contentRender:Object,params:Object};var pl=(0,g.defineComponent)({name:"VxeColumn",props:dl,setup(e,{slots:t}){const o=(0,g.ref)(),n=(0,g.inject)("$xetable",{}),l=(0,g.inject)("xecolgroup",null),r=ul.createColumn(n,e);r.slots=t,(0,g.provide)("$xegrid",null),Ne(n,e,r),(0,g.onMounted)((()=>{Le(n,o.value,r,l)})),(0,g.onUnmounted)((()=>{Ae(n,r)}));const a=()=>(0,g.h)("div",{ref:o});return a}});const fl=Object.assign(pl,{install(e){e.component(pl.name,pl),e.component("VxeTableColumn",pl)}}),ml=fl;uo.component(pl.name,pl),uo.component("VxeTableColumn",pl);var hl=(0,g.defineComponent)({name:"VxeColgroup",props:dl,setup(e,{slots:t}){const o=(0,g.ref)(),n=(0,g.inject)("$xetable",{}),l=(0,g.inject)("xecolgroup",null),r=ul.createColumn(n,e),a={};t.header&&(a.header=t.header);const i={column:r};r.slots=a,r.children=[],(0,g.provide)("xecolgroup",i),(0,g.provide)("$xegrid",null),Ne(n,e,r),(0,g.onMounted)((()=>{Le(n,o.value,r,l)})),(0,g.onUnmounted)((()=>{Ae(n,r)}));const s=()=>(0,g.h)("div",{ref:o},t.default?t.default():[]);return s}});const gl=Object.assign(hl,{install(e){e.component(hl.name,hl),e.component("VxeTableColgroup",hl)}}),vl=gl;uo.component(hl.name,hl),uo.component("VxeTableColgroup",hl);var xl={id:String,data:Array,height:[Number,String],minHeight:{type:[Number,String],default:()=>c.table.minHeight},maxHeight:[Number,String],resizable:{type:Boolean,default:()=>c.table.resizable},stripe:{type:Boolean,default:()=>c.table.stripe},border:{type:[Boolean,String],default:()=>c.table.border},round:{type:Boolean,default:()=>c.table.round},size:{type:String,default:()=>c.table.size||c.size},fit:{type:Boolean,default:()=>c.table.fit},loading:Boolean,align:{type:String,default:()=>c.table.align},headerAlign:{type:String,default:()=>c.table.headerAlign},footerAlign:{type:String,default:()=>c.table.footerAlign},showHeader:{type:Boolean,default:()=>c.table.showHeader},highlightCurrentRow:{type:Boolean,default:()=>c.table.highlightCurrentRow},highlightHoverRow:{type:Boolean,default:()=>c.table.highlightHoverRow},highlightCurrentColumn:{type:Boolean,default:()=>c.table.highlightCurrentColumn},highlightHoverColumn:{type:Boolean,default:()=>c.table.highlightHoverColumn},highlightCell:Boolean,showFooter:Boolean,footerMethod:Function,rowClassName:[String,Function],cellClassName:[String,Function],headerRowClassName:[String,Function],headerCellClassName:[String,Function],footerRowClassName:[String,Function],footerCellClassName:[String,Function],cellStyle:[Object,Function],headerCellStyle:[Object,Function],footerCellStyle:[Object,Function],rowStyle:[Object,Function],headerRowStyle:[Object,Function],footerRowStyle:[Object,Function],mergeCells:Array,mergeFooterItems:Array,spanMethod:Function,footerSpanMethod:Function,showOverflow:{type:[Boolean,String],default:()=>c.table.showOverflow},showHeaderOverflow:{type:[Boolean,String],default:()=>c.table.showHeaderOverflow},showFooterOverflow:{type:[Boolean,String],default:()=>c.table.showFooterOverflow},columnKey:Boolean,rowKey:Boolean,rowId:{type:String,default:()=>c.table.rowId},zIndex:Number,emptyText:{type:String,default:()=>c.table.emptyText},keepSource:{type:Boolean,default:()=>c.table.keepSource},autoResize:{type:Boolean,default:()=>c.table.autoResize},syncResize:[Boolean,String,Number],resizeConfig:Object,columnConfig:Object,rowConfig:Object,resizableConfig:Object,seqConfig:Object,sortConfig:Object,filterConfig:Object,radioConfig:Object,checkboxConfig:Object,tooltipConfig:Object,exportConfig:Object,importConfig:Object,printConfig:Object,expandConfig:Object,treeConfig:Object,menuConfig:Object,mouseConfig:Object,areaConfig:Object,keyboardConfig:Object,clipConfig:Object,fnrConfig:Object,editConfig:Object,validConfig:Object,editRules:Object,loadingConfig:Object,emptyRender:Object,customConfig:Object,scrollX:Object,scrollY:Object,animat:{type:Boolean,default:()=>c.table.animat},delayHover:{type:Number,default:()=>c.table.delayHover},params:Object},bl=["update:data","keydown-start","keydown","keydown-end","paste","copy","cut","current-change","radio-change","checkbox-change","checkbox-all","checkbox-range-start","checkbox-range-change","checkbox-range-end","checkbox-range-select","cell-click","cell-dblclick","cell-menu","cell-mouseenter","cell-mouseleave","cell-selected","header-cell-click","header-cell-dblclick","header-cell-menu","footer-cell-click","footer-cell-dblclick","footer-cell-menu","clear-merge","sort-change","clear-sort","filter-change","filter-visible","clear-filter","resizable-change","toggle-row-expand","toggle-tree-expand","menu-click","edit-closed","edit-actived","edit-activated","edit-disabled","valid-error","scroll","custom","change-fnr","open-fnr","fnr-change","fnr-find","fnr-find-all","fnr-replace","fnr-replace-all","cell-area-copy","cell-area-cut","cell-area-paste","cell-area-merge","clear-cell-area-merge","header-cell-area-selection","cell-area-selection-start","cell-area-selection-drag","cell-area-selection-end","cell-area-extension-start","cell-area-extension-drag","cell-area-extension-end","cell-area-selection-all-start","cell-area-selection-all-end","cell-area-arrows-start","cell-area-arrows-end","active-cell-change-start","active-cell-change-end"];const wl=Object.keys(xl),Cl=["clearAll","syncData","updateData","loadData","reloadData","reloadRow","loadColumn","reloadColumn","getRowNode","getColumnNode","getRowIndex","getVTRowIndex","getVMRowIndex","getColumnIndex","getVTColumnIndex","getVMColumnIndex","createData","createRow","revertData","clearData","isInsertByRow","isUpdateByRow","getColumns","getColumnById","getColumnByField","getTableColumn","getData","getCheckboxRecords","getParentRow","getRowSeq","getRowById","getRowid","getTableData","setColumnFixed","clearColumnFixed","setColumnWidth","getColumnWidth","hideColumn","showColumn","resetColumn","refreshColumn","refreshScroll","recalculate","closeTooltip","isAllCheckboxChecked","isAllCheckboxIndeterminate","getCheckboxIndeterminateRecords","setCheckboxRow","isCheckedByCheckboxRow","isIndeterminateByCheckboxRow","toggleCheckboxRow","setAllCheckboxRow","getRadioReserveRecord","clearRadioReserve","getCheckboxReserveRecords","clearCheckboxReserve","toggleAllCheckboxRow","clearCheckboxRow","setCurrentRow","isCheckedByRadioRow","setRadioRow","clearCurrentRow","clearRadioRow","getCurrentRecord","getRadioRecord","getCurrentColumn","setCurrentColumn","clearCurrentColumn","setPendingRow","getPendingRecords","clearPendingRow","sort","clearSort","isSort","getSortColumns","closeFilter","isFilter","isActiveFilterByColumn","isRowExpandLoaded","clearRowExpandLoaded","reloadRowExpand","reloadRowExpand","toggleRowExpand","setAllRowExpand","setRowExpand","isExpandByRow","isRowExpandByRow","clearRowExpand","clearRowExpandReserve","getRowExpandRecords","getTreeExpandRecords","isTreeExpandLoaded","clearTreeExpandLoaded","reloadTreeExpand","reloadTreeChilds","toggleTreeExpand","setAllTreeExpand","setTreeExpand","isTreeExpandByRow","clearTreeExpand","clearTreeExpandReserve","getScroll","scrollTo","scrollToRow","scrollToColumn","clearScroll","updateFooter","updateStatus","setMergeCells","removeInsertRow","removeMergeCells","getMergeCells","clearMergeCells","setMergeFooterItems","removeMergeFooterItems","getMergeFooterItems","clearMergeFooterItems","openTooltip","focus","blur","connect"],yl=[...bl,"page-change","form-submit","form-submit-invalid","form-reset","form-collapse","form-toggle-collapse","proxy-query","proxy-delete","proxy-save","toolbar-button-click","toolbar-tool-click","zoom"];var Tl=(0,g.defineComponent)({name:"VxeGrid",props:{...xl,columns:Array,pagerConfig:Object,proxyConfig:Object,toolbarConfig:Object,formConfig:Object,zoomConfig:Object,size:{type:String,default:()=>c.grid.size||c.size}},emits:yl,setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=(0,g.getCurrentInstance)(),i=Fo(e),s=(0,g.reactive)({tableLoading:!1,proxyInited:!1,isZMax:!1,tableData:[],filterData:[],formData:{},sortData:[],tZindex:0,tablePage:{total:0,pageSize:c.pager.pageSize||10,currentPage:1}}),u=(0,g.ref)(),d=(0,g.ref)(),p=(0,g.ref)(),m=(0,g.ref)(),h=(0,g.ref)(),v=(0,g.ref)(),x=(0,g.ref)(),b=(0,g.ref)(),w=(0,g.ref)(),C=(0,g.ref)(),y=e=>{const t={};return e.forEach((e=>{t[e]=(...t)=>{const o=d.value;if(o&&o[e])return o[e](...t)}})),t},T=y(Cl);Cl.forEach((e=>{T[e]=(...t)=>{const o=d.value;if(o&&o[e])return o&&o[e](...t)}}));const E=(0,g.computed)((()=>Object.assign({},c.grid.proxyConfig,e.proxyConfig))),S=(0,g.computed)((()=>{const e=E.value;return!1!==e.message})),k=(0,g.computed)((()=>Object.assign({},c.grid.pagerConfig,e.pagerConfig))),R=(0,g.computed)((()=>Object.assign({},c.grid.formConfig,e.formConfig))),O=(0,g.computed)((()=>Object.assign({},c.grid.toolbarConfig,e.toolbarConfig))),M=(0,g.computed)((()=>Object.assign({},c.grid.zoomConfig,e.zoomConfig))),$=(0,g.computed)((()=>s.isZMax?{zIndex:s.tZindex}:null)),I=(0,g.computed)((()=>{const t={},o=e;return wl.forEach((e=>{t[e]=o[e]})),t})),D={refElem:u,refTable:d,refForm:p,refToolbar:m,refPager:h},F={computeProxyOpts:E,computePagerOpts:k,computeFormOpts:R,computeToolbarOpts:O,computeZoomOpts:M},N={xID:l,props:e,context:t,instance:r,reactData:s,getRefMaps:()=>D,getComputeMaps:()=>F};let L={};const A=(0,g.computed)((()=>{const{seqConfig:t,pagerConfig:o,loading:n,editConfig:l,proxyConfig:r}=e,{isZMax:a,tableLoading:i,tablePage:c,tableData:u}=s,d=I.value,p=E.value,f=k.value,m=Object.assign({},d);return a&&(d.maxHeight?m.maxHeight="auto":m.height="auto"),r&&P(p)&&(m.loading=n||i,m.data=u,o&&p.seq&&P(f)&&(m.seqConfig=Object.assign({},t,{startIndex:(c.currentPage-1)*c.pageSize}))),l&&(m.editConfig=Object.assign({},l)),m})),V=()=>{const t=O.value;e.toolbarConfig&&P(t)&&(0,g.nextTick)((()=>{const e=d.value,t=m.value;e&&t&&e.connect(t)}))},_=()=>{const{tablePage:t}=s,{pagerConfig:o}=e,n=k.value,{currentPage:l,pageSize:r}=n;o&&P(n)&&(l&&(t.currentPage=l),r&&(t.pageSize=r))},B=e=>{const t=S.value,o=d.value,n=o.getCheckboxRecords();n.length?(o.togglePendingRow(n),T.clearCheckboxRow()):t&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({id:e,content:c.i18n("vxe.grid.selectOneRecord"),status:"warning"}))},z=(e,t)=>{const o=E.value,{props:n={}}=o;let l;return e&&n.message&&(l=a().get(e,n.message)),l||c.i18n(t)},W=(e,t,o)=>{const n=S.value,l=T.getCheckboxRecords();if(n){if(l.length)return oo.modal.confirm({id:`cfm_${e}`,content:c.i18n(t),escClosable:!0}).then((e=>{if("confirm"===e)return o()}));oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({id:`msg_${e}`,content:c.i18n("vxe.grid.selectOneRecord"),status:"warning"})}else l.length&&o();return Promise.resolve()},q=t=>{const{proxyConfig:o}=e,{tablePage:n}=s,{currentPage:l,pageSize:r}=t,a=E.value;n.currentPage=l,n.pageSize=r,L.dispatchEvent("page-change",t),o&&P(a)&&L.commitProxy("query").then((e=>{L.dispatchEvent("proxy-query",e,t.$event)}))},U=t=>{const o=d.value,{proxyConfig:n}=e,{computeSortOpts:l}=o.getComputeMaps(),r=E.value,a=l.value;a.remote&&(s.sortData=t.sortList,n&&P(r)&&(s.tablePage.currentPage=1,L.commitProxy("query").then((e=>{L.dispatchEvent("proxy-query",e,t.$event)})))),L.dispatchEvent("sort-change",t)},X=t=>{const o=d.value,{proxyConfig:n}=e,{computeFilterOpts:l}=o.getComputeMaps(),r=E.value,a=l.value;a.remote&&(s.filterData=t.filterList,n&&P(r)&&(s.tablePage.currentPage=1,L.commitProxy("query").then((e=>{L.dispatchEvent("proxy-query",e,t.$event)})))),L.dispatchEvent("filter-change",t)},Y=t=>{const{proxyConfig:o}=e,n=E.value;o&&P(n)&&L.commitProxy("reload").then((e=>{L.dispatchEvent("proxy-query",{...e,isReload:!0},t.$event)})),L.dispatchEvent("form-submit",t)},G=t=>{const{proxyConfig:o}=e,n=E.value;o&&P(n)&&L.commitProxy("reload").then((e=>{L.dispatchEvent("proxy-query",{...e,isReload:!0},t.$event)})),L.dispatchEvent("form-reset",t)},K=e=>{L.dispatchEvent("form-submit-invalid",e)},Z=e=>{(0,g.nextTick)((()=>T.recalculate(!0))),L.dispatchEvent("form-toggle-collapse",e),L.dispatchEvent("form-collapse",e)},J=e=>{const{isZMax:t}=s;return(e?!t:t)&&(s.isZMax=!t,s.tZindex<H()&&(s.tZindex=j())),(0,g.nextTick)().then((()=>T.recalculate(!0))).then((()=>s.isZMax))},Q=(e,t)=>{const n=e[t];if(n){if(!a().isString(n))return n;if(o[n])return o[n];f("vxe.error.notSlot",[n])}return null},ee=()=>{const{formConfig:t,proxyConfig:n}=e,{formData:l}=s,r=E.value,i=R.value,c=[];if(t&&P(i)||o.form){let e=[];if(o.form)e=o.form({$grid:N});else if(i.items){const t={};if(!i.inited){i.inited=!0;const e=r.beforeItem;r&&e&&i.items.forEach((t=>{e({$grid:N,item:t})}))}i.items.forEach((e=>{a().each(e.slots,(e=>{a().isFunction(e)||o[e]&&(t[e]=o[e])}))})),e.push((0,g.h)((0,g.resolveComponent)("vxe-form"),{ref:p,...Object.assign({},i,{data:n&&P(r)&&r.form?l:i.data}),onSubmit:Y,onReset:G,onSubmitInvalid:K,onCollapse:Z},t))}c.push((0,g.h)("div",{ref:v,class:"vxe-grid--form-wrapper"},e))}return c},te=()=>{const{toolbarConfig:t}=e,n=O.value,l=[];if(t&&P(n)||o.toolbar){let e=[];if(o.toolbar)e=o.toolbar({$grid:N});else{const t=n.slots;let o,l;const r={};t&&(o=Q(t,"buttons"),l=Q(t,"tools"),o&&(r.buttons=o),l&&(r.tools=l)),e.push((0,g.h)((0,g.resolveComponent)("vxe-toolbar"),{ref:m,...n},r))}l.push((0,g.h)("div",{ref:x,class:"vxe-grid--toolbar-wrapper"},e))}return l},oe=()=>o.top?[(0,g.h)("div",{ref:b,class:"vxe-grid--top-wrapper"},o.top({$grid:N}))]:[],ae={};bl.forEach((e=>{const t=a().camelCase(`on-${e}`);ae[t]=(...t)=>n(e,...t)}));const ie=()=>{const{proxyConfig:t}=e,n=A.value,l=E.value,r=Object.assign({},ae),a=o.empty,i=o.loading;t&&P(l)&&(l.sort&&(r.onSortChange=U),l.filter&&(r.onFilterChange=X));const s={};return a&&(s.empty=()=>a({})),i&&(s.loading=()=>i({})),[(0,g.h)((0,g.resolveComponent)("vxe-table"),{ref:d,...n,...r},s)]},se=()=>o.bottom?[(0,g.h)("div",{ref:w,class:"vxe-grid--bottom-wrapper"},o.bottom({$grid:N}))]:[],ce=()=>{const{proxyConfig:t,pagerConfig:n}=e,l=E.value,r=k.value,a=[];if(n&&P(r)||o.pager){let e=[];if(o.pager)e=o.pager({$grid:N});else{const o=r.slots,n={};let a,i;o&&(a=Q(o,"left"),i=Q(o,"right"),a&&(n.left=a),i&&(n.right=i)),e.push((0,g.h)((0,g.resolveComponent)("vxe-pager"),{ref:h,...r,...t&&P(l)?s.tablePage:{},onPageChange:q},n))}a.push((0,g.h)("div",{ref:C,class:"vxe-grid--pager-wrapper"},e))}return a},ue=()=>{const{proxyConfig:t,formConfig:o}=e,{proxyInited:n}=s,l=E.value,r=R.value;if(t&&P(l)){if(o&&P(r)&&l.form&&r.items){const e={};r.items.forEach((t=>{const{field:o,itemRender:n}=t;if(o){let l=null;if(n){const{defaultValue:e}=n;a().isFunction(e)?l=e({item:t}):a().isUndefined(e)||(l=e)}e[o]=l}})),s.formData=e}n||(s.proxyInited=!0,!1!==l.autoLoad&&(0,g.nextTick)().then((()=>L.commitProxy("_init"))).then((e=>{L.dispatchEvent("proxy-query",{...e,isInited:!0},new Event("init"))})))}};L={dispatchEvent(e,t,o){n(e,Object.assign({$grid:N,$event:o},t))},commitProxy(t,...o){const{toolbarConfig:n,pagerConfig:l,editRules:r}=e,{tablePage:i,formData:u}=s,p=S.value,m=E.value,h=k.value,v=O.value,{beforeQuery:x,afterQuery:b,beforeDelete:w,afterDelete:C,beforeSave:y,afterSave:R,ajax:M={},props:$={}}=m,I=d.value;let D=null,F=null;if(a().isString(t)){const{buttons:e}=v,o=n&&P(v)&&e?a().findTree(e,(e=>e.code===t),{children:"dropdowns"}):null;D=o?o.item:null,F=t}else D=t,F=D.code;const A=D?D.params:null;switch(F){case"insert":return I.insert({});case"insert_edit":return I.insert({}).then((({row:e})=>I.setEditRow(e)));case"insert_actived":return I.insert({}).then((({row:e})=>I.setEditRow(e)));case"mark_cancel":B(F);break;case"remove":return W(F,"vxe.grid.removeSelectRecord",(()=>I.removeCheckboxRow()));case"import":I.importData(A);break;case"open_import":I.openImport(A);break;case"export":I.exportData(A);break;case"open_export":I.openExport(A);break;case"reset_custom":return I.resetColumn(!0);case"_init":case"reload":case"query":{const e=M.query;if(e){const t="_init"===F,n="reload"===F;let r=[],c=[],d={};if(l&&((t||n)&&(i.currentPage=1),P(h)&&(d={...i})),t){const{computeSortOpts:e}=I.getComputeMaps(),t=e.value;let o=t.defaultSort;o&&(a().isArray(o)||(o=[o]),r=o.map((e=>({field:e.field,property:e.field,order:e.order})))),c=I.getCheckedFilters()}else n?I.clearAll():(r=I.getSortColumns(),c=I.getCheckedFilters());const p={code:F,button:D,isInited:t,isReload:n,$grid:N,page:d,sort:r.length?r[0]:{},sorts:r,filters:c,form:u,options:e};s.sortData=r,s.filterData=c,s.tableLoading=!0;const f=[p].concat(o);return Promise.resolve((x||e)(...f)).then((e=>{if(s.tableLoading=!1,e)if(l&&P(h)){const t=a().get(e,$.total||"page.total")||0;i.total=a().toNumber(t),s.tableData=a().get(e,$.result||"result")||[];const o=Math.max(Math.ceil(t/i.pageSize),1);i.currentPage>o&&(i.currentPage=o)}else s.tableData=($.list?a().get(e,$.list):e)||[];else s.tableData=[];return b&&b(...f),{status:!0}})).catch((()=>(s.tableLoading=!1,{status:!1})))}f("vxe.error.notFunc",["proxy-config.ajax.query"]);break}case"delete":{const e=M.delete;if(e){const t=T.getCheckboxRecords(),n=t.filter((e=>!I.isInsertByRow(e))),l={removeRecords:n},r={$grid:N,code:F,button:D,body:l,form:u,options:e},a=[r].concat(o);if(t.length)return W(F,"vxe.grid.deleteSelectRecord",(()=>n.length?(s.tableLoading=!0,Promise.resolve((w||e)(...a)).then((e=>(s.tableLoading=!1,I.setPendingRow(n,!1),p&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({content:z(e,"vxe.grid.delSuccess"),status:"success"})),C?C(...a):L.commitProxy("query"),{status:!0}))).catch((e=>(s.tableLoading=!1,p&&(oo.modal.message||f("vxe.error.reqModule",["Modal"]),oo.modal.message({id:F,content:z(e,"vxe.grid.operError"),status:"error"})),{status:!1})))):I.remove(t)));p&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({id:F,content:c.i18n("vxe.grid.selectOneRecord"),status:"warning"}))}else f("vxe.error.notFunc",["proxy-config.ajax.delete"]);break}case"save":{const e=M.save;if(e){const t=I.getRecordset(),{insertRecords:n,removeRecords:l,updateRecords:a,pendingRecords:i}=t,d={$grid:N,code:F,button:D,body:t,form:u,options:e},m=[d].concat(o);n.length&&(t.pendingRecords=i.filter((e=>-1===I.findRowIndexOf(n,e)))),i.length&&(t.insertRecords=n.filter((e=>-1===I.findRowIndexOf(i,e))));let h=Promise.resolve();return r&&(h=I.validate(t.insertRecords.concat(a))),h.then((o=>{if(!o)return t.insertRecords.length||l.length||a.length||t.pendingRecords.length?(s.tableLoading=!0,Promise.resolve((y||e)(...m)).then((e=>(s.tableLoading=!1,I.clearPendingRow(),p&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({content:z(e,"vxe.grid.saveSuccess"),status:"success"})),R?R(...m):L.commitProxy("query"),{status:!0}))).catch((e=>(s.tableLoading=!1,p&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({id:F,content:z(e,"vxe.grid.operError"),status:"error"})),{status:!1})))):void(p&&(oo.modal||f("vxe.error.reqModule",["Modal"]),oo.modal.message({id:F,content:c.i18n("vxe.grid.dataUnchanged"),status:"info"})))}))}f("vxe.error.notFunc",["proxy-config.ajax.save"]);break}default:{const e=oo.commands.get(F);e&&(e.commandMethod?e.commandMethod({code:F,button:D,$grid:N,$table:I},...o):f("vxe.error.notCommands",[F]))}}return(0,g.nextTick)()},zoom(){return s.isZMax?L.revert():L.maximize()},isMaximized(){return s.isZMax},maximize(){return J(!0)},revert(){return J()},getFormItems(t){const o=R.value,{formConfig:n}=e,{items:l}=o,r=[];return a().eachTree(n&&P(o)&&l?l:[],(e=>{r.push(e)}),{children:"children"}),a().isUndefined(t)?r:r[t]},getProxyInfo(){const t=d.value;if(e.proxyConfig){const{sortData:e}=s;return{data:s.tableData,filter:s.filterData,form:s.formData,sort:e.length?e[0]:{},sorts:e,pager:s.tablePage,pendingRecords:t?t.getPendingRecords():[]}}return null}},L.loadColumn=e=>{const t=d.value;return a().eachTree(e,(e=>{e.slots&&a().each(e.slots,(e=>{a().isFunction(e)||o[e]||f("vxe.error.notSlot",[e])}))})),t.loadColumn(e)},L.reloadColumn=e=>(T.clearAll(),L.loadColumn(e));const de={extendTableMethods:y,callSlot(e,t){return e&&(a().isString(e)&&(e=o[e]||null),a().isFunction(e))?We(e(t)):[]},getExcludeHeight(){const{height:t}=e,{isZMax:o}=s,n=u.value,l=v.value,r=x.value,a=b.value,i=w.value,c=C.value,d=o||"auto"!==t?0:re(n.parentNode);return d+re(n)+le(l)+le(r)+le(a)+le(i)+le(c)},getParentHeight(){const e=u.value;return e?(s.isZMax?ne().visibleHeight:a().toNumber(getComputedStyle(e.parentNode).height))-de.getExcludeHeight():0},triggerToolbarCommitEvent(e,t){const{code:o}=e;return L.commitProxy(e,t).then((e=>{o&&e&&e.status&&["query","reload","delete","save"].includes(o)&&L.dispatchEvent("delete"===o||"save"===o?`proxy-${o}`:"proxy-query",{...e,isReload:"reload"===o},t)}))},triggerToolbarBtnEvent(e,t){de.triggerToolbarCommitEvent(e,t),L.dispatchEvent("toolbar-button-click",{code:e.code,button:e},t)},triggerToolbarTolEvent(e,t){de.triggerToolbarCommitEvent(e,t),L.dispatchEvent("toolbar-tool-click",{code:e.code,tool:e,$event:t})},triggerZoomEvent(e){L.zoom(),L.dispatchEvent("zoom",{type:s.isZMax?"max":"revert"},e)}};Object.assign(N,T,L,de);const pe=(0,g.ref)(0);(0,g.watch)((()=>e.columns?e.columns.length:-1),(()=>{pe.value++})),(0,g.watch)((()=>e.columns),(()=>{pe.value++})),(0,g.watch)(pe,(()=>{(0,g.nextTick)((()=>N.loadColumn(e.columns||[])))})),(0,g.watch)((()=>e.toolbarConfig),(()=>{V()})),(0,g.watch)((()=>e.pagerConfig),(()=>{_()})),(0,g.watch)((()=>e.proxyConfig),(()=>{ue()}));const fe=e=>{const t=M.value,o=wo(e,go.ESCAPE);o&&s.isZMax&&!1!==t.escRestore&&de.triggerZoomEvent(e)};oo.hooks.forEach((e=>{const{setupGrid:t}=e;if(t){const e=t(N);e&&a().isObject(e)&&Object.assign(N,e)}})),_(),(0,g.onMounted)((()=>{(0,g.nextTick)((()=>{const{data:t,columns:o,proxyConfig:n}=e,l=E.value,r=R.value;P(n)&&(t||l.form&&r.data)&&f("vxe.error.errConflicts",["grid.data","grid.proxy-config"]),o&&o.length&&N.loadColumn(o),V()})),yo.on(N,"keydown",fe)})),(0,g.onUnmounted)((()=>{yo.off(N,"keydown")})),(0,g.nextTick)((()=>{ue()}));const me=()=>{const t=i.value,o=$.value;return(0,g.h)("div",{ref:u,class:["vxe-grid",{[`size--${t}`]:t,"is--animat":!!e.animat,"is--round":e.round,"is--maximize":s.isZMax,"is--loading":e.loading||s.tableLoading}],style:o},ee().concat(te(),oe(),ie(),se(),ce()))};return N.renderVN=me,(0,g.provide)("$xegrid",N),N},render(){return this.renderVN()}});const El=Object.assign(Tl,{install(e){e.component(Tl.name,Tl)}}),Sl=El;uo.component(Tl.name,Tl);var kl=(0,g.defineComponent)({name:"VxeToolbar",props:{loading:Boolean,refresh:[Boolean,Object],import:[Boolean,Object],export:[Boolean,Object],print:[Boolean,Object],zoom:[Boolean,Object],custom:[Boolean,Object],buttons:{type:Array,default:()=>c.toolbar.buttons},tools:{type:Array,default:()=>c.toolbar.tools},perfect:{type:Boolean,default:()=>c.toolbar.perfect},size:{type:String,default:()=>c.toolbar.size||c.size},className:[String,Function]},emits:["button-click","tool-click"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=Fo(e),i=(0,g.reactive)({isRefresh:!1,columns:[]}),s=(0,g.ref)(),u=(0,g.ref)(),d=(0,g.reactive)({isAll:!1,isIndeterminate:!1,activeBtn:!1,activeWrapper:!1,visible:!1}),m={refElem:s},h={xID:l,props:e,context:t,reactData:i,getRefMaps:()=>m};let v={};const x=(0,g.inject)("$xegrid",null);let b;const w=(0,g.computed)((()=>Object.assign({},c.toolbar.refresh,e.refresh))),C=(0,g.computed)((()=>Object.assign({},c.toolbar["import"],e.import))),y=(0,g.computed)((()=>Object.assign({},c.toolbar["export"],e.export))),T=(0,g.computed)((()=>Object.assign({},c.toolbar.print,e.print))),E=(0,g.computed)((()=>Object.assign({},c.toolbar.zoom,e.zoom))),S=(0,g.computed)((()=>Object.assign({},c.toolbar.custom,e.custom))),k=()=>{if(b)return!0;f("vxe.error.barUnableLink")},R=()=>{const{columns:e}=i,{computeCustomOpts:t}=b.getComputeMaps(),o=t.value,{checkMethod:n}=o;d.isAll=e.every((e=>!!n&&!n({column:e})||e.visible)),d.isIndeterminate=!d.isAll&&e.some((e=>(!n||n({column:e}))&&(e.visible||e.halfVisible)))},O=()=>{d.visible=!0,R()},M=()=>{b.handleCustom()},$=()=>{const{custom:t}=e,o=S.value;d.visible&&(d.visible=!1,t&&!o.immediate&&M())},I=(e,t)=>{const o=x||b;o.dispatchEvent("custom",{type:e},t)},D=e=>{$(),I("confirm",e)},F=e=>{k()&&(d.visible||(O(),I("open",e)))},N=e=>{d.visible&&($(),I("close",e))},L=e=>{b.resetColumn(!0),$(),I("reset",e)},A=e=>{const{columns:t}=i,o=a().findTree(t,(t=>t===e));if(o&&o.parent){const{parent:e}=o;e.children&&e.children.length&&(e.visible=e.children.every((e=>e.visible)),e.halfVisible=!e.visible&&e.children.some((e=>e.visible||e.halfVisible)),A(e))}},P=t=>{const o=!t.visible,n=S.value;a().eachTree([t],(e=>{e.visible=o,e.halfVisible=!1})),A(t),e.custom&&n.immediate&&M(),R()},V=(e,t)=>{const{computeIsMaxFixedColumn:o}=b.getComputeMaps(),n=o.value;e.fixed===t?b.clearColumnFixed(e):n&&!e.fixed||b.setColumnFixed(e,t)},_=()=>{const{columns:e}=i,{computeCustomOpts:t}=b.getComputeMaps(),o=t.value,{checkMethod:n}=o,l=!d.isAll;a().eachTree(e,(e=>{n&&!n({column:e})||(e.visible=l,e.halfVisible=!1)})),d.isAll=l,R()},j=e=>{const t=u.value;ce(e,t).flag||N(e)},H=e=>{N(e)},B=e=>{d.visible?N(e):F(e)},z=e=>{d.activeBtn=!0,F(e)},q=e=>{d.activeBtn=!1,setTimeout((()=>{d.activeBtn||d.activeWrapper||N(e)}),300)},U=e=>{d.activeWrapper=!0,F(e)},X=e=>{d.activeWrapper=!1,setTimeout((()=>{d.activeBtn||d.activeWrapper||N(e)}),300)},Y=e=>{const{isRefresh:t}=i,o=w.value;if(!t){const t=o.queryMethod||o.query;if(t){i.isRefresh=!0;try{Promise.resolve(t({})).catch((e=>e)).then((()=>{i.isRefresh=!1}))}catch(n){i.isRefresh=!1}}else x&&(i.isRefresh=!0,x.triggerToolbarCommitEvent({code:o.code||"reload"},e).catch((e=>e)).then((()=>{i.isRefresh=!1})))}},G=e=>{x&&x.triggerZoomEvent(e)},K=(e,t)=>{const{code:o}=t;if(o)if(x)x.triggerToolbarBtnEvent(t,e);else{const n=oo.commands.get(o),l={code:o,button:t,$table:b,$grid:x,$event:e};n&&(n.commandMethod?n.commandMethod(l):f("vxe.error.notCommands",[o])),h.dispatchEvent("button-click",l,e)}},Z=(e,t)=>{const{code:o}=t;if(o)if(x)x.triggerToolbarTolEvent(t,e);else{const n=oo.commands.get(o),l={code:o,tool:t,$table:b,$grid:x,$event:e};n&&(n.commandMethod?n.commandMethod(l):f("vxe.error.notCommands",[o])),h.dispatchEvent("tool-click",l,e)}},J=()=>{k()&&b.openImport()},Q=()=>{k()&&b.openExport()},ee=()=>{k()&&b.openPrint()},te=(e,t)=>{const{dropdowns:o}=e,n=[];return o?o.map(((e,o)=>!1===e.visible?(0,g.createCommentVNode)():(0,g.h)((0,g.resolveComponent)("vxe-button"),{key:o,disabled:e.disabled,loading:e.loading,type:e.type,icon:e.icon,circle:e.circle,round:e.round,status:e.status,content:e.name,onClick:o=>t?K(o,e):Z(o,e)}))):n},oe=()=>{const{buttons:t}=e,n=o.buttons;if(n)return We(n({$grid:x,$table:b}));const l=[];return t&&t.forEach((e=>{const{dropdowns:t,buttonRender:o}=e;if(!1!==e.visible){const n=o?oo.renderer.get(o.name):null;if(o&&n&&n.renderToolbarButton){const t=n.toolbarButtonClassName,r={$grid:x,$table:b,button:e};l.push((0,g.h)("span",{class:["vxe-button--item",t?a().isFunction(t)?t(r):t:""]},We(n.renderToolbarButton(o,r))))}else l.push((0,g.h)((0,g.resolveComponent)("vxe-button"),{disabled:e.disabled,loading:e.loading,type:e.type,icon:e.icon,circle:e.circle,round:e.round,status:e.status,content:e.name,destroyOnClose:e.destroyOnClose,placement:e.placement,transfer:e.transfer,onClick:t=>K(t,e)},t&&t.length?{dropdowns:()=>te(e,!0)}:{}))}})),l},ne=()=>{const{tools:t}=e,n=o.tools;if(n)return We(n({$grid:x,$table:b}));const l=[];return t&&t.forEach((e=>{const{dropdowns:t,toolRender:o}=e;if(!1!==e.visible){const n=o?oo.renderer.get(o.name):null;if(o&&n&&n.renderToolbarTool){const t=n.toolbarToolClassName,r={$grid:x,$table:b,tool:e};l.push((0,g.h)("span",{class:["vxe-tool--item",t?a().isFunction(t)?t(r):t:""]},We(n.renderToolbarTool(o,r))))}else l.push((0,g.h)((0,g.resolveComponent)("vxe-button"),{disabled:e.disabled,loading:e.loading,type:e.type,icon:e.icon,circle:e.circle,round:e.round,status:e.status,content:e.name,destroyOnClose:e.destroyOnClose,placement:e.placement,transfer:e.transfer,onClick:t=>Z(t,e)},t&&t.length?{dropdowns:()=>te(e,!1)}:{}))}})),l},le=()=>{const{columns:e}=i,t=S.value;let o=!0;const n=[],l={},r={};let s;if(b){const{computeCustomOpts:e,computeIsMaxFixedColumn:t}=b.getComputeMaps(),n=e.value;s=n.checkMethod,o=t.value}"manual"===t.trigger||("hover"===t.trigger?(l.onMouseenter=z,l.onMouseleave=q,r.onMouseenter=U,r.onMouseleave=X):l.onClick=B),a().eachTree(e,((e,l,r,a,i)=>{const u=W(e.getTitle(),1),d=e.getKey(),p=e.children&&e.children.length,f=!!s&&!s({column:e});if(p||d){const l=e.visible,r=e.halfVisible;n.push((0,g.h)("li",{class:["vxe-custom--option",`level--${e.level}`,{"is--group":p}]},[(0,g.h)("div",{title:u,class:["vxe-custom--checkbox-option",{"is--checked":l,"is--indeterminate":r,"is--disabled":f}],onClick:()=>{f||P(e)}},[(0,g.h)("span",{class:["vxe-checkbox--icon",r?c.icon.TABLE_CHECKBOX_INDETERMINATE:l?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]}),(0,g.h)("span",{class:"vxe-checkbox--label"},u)]),!i&&t.allowFixed?(0,g.h)("div",{class:"vxe-custom--fixed-option"},[(0,g.h)("span",{class:["vxe-custom--fixed-left-option","left"===e.fixed?c.icon.TOOLBAR_TOOLS_FIXED_LEFT_ACTIVED:c.icon.TOOLBAR_TOOLS_FIXED_LEFT,{"is--checked":"left"===e.fixed,"is--disabled":o&&!e.fixed}],title:c.i18n("left"===e.fixed?"vxe.toolbar.cancelfixed":"vxe.toolbar.fixedLeft"),onClick:()=>{V(e,"left")}}),(0,g.h)("span",{class:["vxe-custom--fixed-right-option","right"===e.fixed?c.icon.TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVED:c.icon.TOOLBAR_TOOLS_FIXED_RIGHT,{"is--checked":"right"===e.fixed,"is--disabled":o&&!e.fixed}],title:c.i18n("right"===e.fixed?"vxe.toolbar.cancelfixed":"vxe.toolbar.fixedRight"),onClick:()=>{V(e,"right")}})]):null]))}}));const p=d.isAll,f=d.isIndeterminate;return(0,g.h)("div",{class:["vxe-custom--wrapper",{"is--active":d.visible}],ref:u},[(0,g.h)((0,g.resolveComponent)("vxe-button"),{circle:!0,icon:t.icon||c.icon.TOOLBAR_TOOLS_CUSTOM,title:c.i18n("vxe.toolbar.custom"),...l}),(0,g.h)("div",{class:"vxe-custom--option-wrapper"},[(0,g.h)("ul",{class:"vxe-custom--header"},[(0,g.h)("li",{class:"vxe-custom--option"},[(0,g.h)("div",{class:["vxe-custom--checkbox-option",{"is--checked":p,"is--indeterminate":f}],title:c.i18n("vxe.table.allTitle"),onClick:_},[(0,g.h)("span",{class:["vxe-checkbox--icon",f?c.icon.TABLE_CHECKBOX_INDETERMINATE:p?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]}),(0,g.h)("span",{class:"vxe-checkbox--label"},c.i18n("vxe.toolbar.customAll"))])])]),(0,g.h)("ul",{class:"vxe-custom--body",...r},n),t.showFooter||t.isFooter?(0,g.h)("div",{class:"vxe-custom--footer"},[(0,g.h)("button",{class:"btn--reset",onClick:L},t.resetButtonText||c.i18n("vxe.toolbar.customRestore")),(0,g.h)("button",{class:"btn--confirm",onClick:D},t.confirmButtonText||c.i18n("vxe.toolbar.customConfirm"))]):null])])};v={dispatchEvent(e,t,o){n(e,Object.assign({$toolbar:h,$event:o},t))},syncUpdate(e){const{collectColumn:t}=e;b=e.$table,i.columns=t}},Object.assign(h,v),(0,g.onMounted)((()=>{yo.on(h,"mousedown",j),yo.on(h,"blur",H)})),(0,g.onUnmounted)((()=>{yo.off(h,"mousedown"),yo.off(h,"blur")})),(0,g.nextTick)((()=>{const{refresh:t}=e,o=w.value,n=o.queryMethod||o.query;!t||x||n||p("vxe.error.notFunc",["queryMethod"]);const l=S.value;l.isFooter&&p("vxe.error.notValidators",["custom.isFooter","custom.showFooter"])}));const re=()=>{const{perfect:t,loading:o,refresh:n,zoom:l,custom:u,className:d}=e,p=r.value,f=w.value,m=C.value,v=y.value,b=T.value,S=E.value;return(0,g.h)("div",{ref:s,class:["vxe-toolbar",d?a().isFunction(d)?d({$toolbar:h}):d:"",{[`size--${p}`]:p,"is--perfect":t,"is--loading":o}]},[(0,g.h)("div",{class:"vxe-buttons--wrapper"},oe()),(0,g.h)("div",{class:"vxe-tools--wrapper"},ne()),(0,g.h)("div",{class:"vxe-tools--operate"},[e.import?(0,g.h)((0,g.resolveComponent)("vxe-button"),{circle:!0,icon:m.icon||c.icon.TOOLBAR_TOOLS_IMPORT,title:c.i18n("vxe.toolbar.import"),onClick:J}):(0,g.createCommentVNode)(),e.export?(0,g.h)((0,g.resolveComponent)("vxe-button"),{circle:!0,icon:v.icon||c.icon.TOOLBAR_TOOLS_EXPORT,title:c.i18n("vxe.toolbar.export"),onClick:Q}):(0,g.createCommentVNode)(),e.print?(0,g.h)((0,g.resolveComponent)("vxe-button"),{circle:!0,icon:b.icon||c.icon.TOOLBAR_TOOLS_PRINT,title:c.i18n("vxe.toolbar.print"),onClick:ee}):(0,g.createCommentVNode)(),n?(0,g.h)((0,g.resolveComponent)("vxe-button"),{circle:!0,icon:i.isRefresh?f.iconLoading||c.icon.TOOLBAR_TOOLS_REFRESH_LOADING:f.icon||c.icon.TOOLBAR_TOOLS_REFRESH,title:c.i18n("vxe.toolbar.refresh"),onClick:Y}):(0,g.createCommentVNode)(),l&&x?(0,g.h)((0,g.resolveComponent)("vxe-button"),{circle:!0,icon:x.isMaximized()?S.iconOut||c.icon.TOOLBAR_TOOLS_MINIMIZE:S.iconIn||c.icon.TOOLBAR_TOOLS_FULLSCREEN,title:c.i18n("vxe.toolbar.zoom"+(x.isMaximized()?"Out":"In")),onClick:G}):(0,g.createCommentVNode)(),u?le():(0,g.createCommentVNode)()])])};return h.renderVN=re,h},render(){return this.renderVN()}});const Rl=Object.assign(kl,{install:function(e){e.component(kl.name,kl)}}),Ol=Rl;uo.component(kl.name,kl);var Ml=(0,g.defineComponent)({name:"VxePager",props:{size:{type:String,default:()=>c.pager.size||c.size},layouts:{type:Array,default:()=>c.pager.layouts||["PrevJump","PrevPage","Jump","PageCount","NextPage","NextJump","Sizes","Total"]},currentPage:{type:Number,default:1},loading:Boolean,pageSize:{type:Number,default:()=>c.pager.pageSize||10},total:{type:Number,default:0},pagerCount:{type:Number,default:()=>c.pager.pagerCount||7},pageSizes:{type:Array,default:()=>c.pager.pageSizes||[10,15,20,50,100]},align:{type:String,default:()=>c.pager.align},border:{type:Boolean,default:()=>c.pager.border},background:{type:Boolean,default:()=>c.pager.background},perfect:{type:Boolean,default:()=>c.pager.perfect},autoHidden:{type:Boolean,default:()=>c.pager.autoHidden},transfer:{type:Boolean,default:()=>c.pager.transfer},className:[String,Function],iconPrevPage:String,iconJumpPrev:String,iconJumpNext:String,iconNextPage:String,iconJumpMore:String},emits:["update:pageSize","update:currentPage","page-change"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=Fo(e),i=(0,g.inject)("$xegrid",null),s=(0,g.reactive)({inpCurrPage:e.currentPage}),u=(0,g.ref)(),d={refElem:u},p={xID:l,props:e,context:t,getRefMaps:()=>d};let f={},m={};const h=(e,t)=>Math.max(Math.ceil(e/t),1),v=(0,g.computed)((()=>h(e.total,e.pageSize))),x=(t,o)=>{n("update:currentPage",o),t&&o!==e.currentPage&&f.dispatchEvent("page-change",{type:"current",pageSize:e.pageSize,currentPage:o},t)},b=(t,o)=>{n("update:currentPage",t),o&&t!==e.currentPage&&f.dispatchEvent("page-change",{type:"current",pageSize:e.pageSize,currentPage:t},o)},w=e=>{const t=e.target,o=a().toInteger(t.value),n=v.value,l=o<=0?1:o>=n?n:o,r=a().toValueString(l);t.value=r,s.inpCurrPage=r,b(l,e)},C=(0,g.computed)((()=>{const{pagerCount:t}=e,o=v.value,n=o>t?t-2:t,l=[];for(let e=0;e<n;e++)l.push(e);return l})),y=(0,g.computed)((()=>Math.floor((e.pagerCount-2)/2))),T=(0,g.computed)((()=>e.pageSizes.map((e=>a().isNumber(e)?{value:e,label:`${c.i18n("vxe.pager.pagesize",[e])}`}:{value:"",label:"",...e})))),E=t=>{const{currentPage:o}=e,n=v.value;o>1&&b(Math.min(n,Math.max(o-1,1)),t)},S=t=>{const{currentPage:o}=e,n=v.value;o<n&&b(Math.min(n,o+1),t)},k=t=>{const o=C.value;b(Math.max(e.currentPage-o.length,1),t)},R=t=>{const o=v.value,n=C.value;b(Math.min(e.currentPage+n.length,o),t)},O=t=>{const{value:o}=t,l=a().toNumber(o),r=h(e.total,l);let i=e.currentPage;i>r&&(i=r,n("update:currentPage",r)),n("update:pageSize",l),f.dispatchEvent("page-change",{type:"size",pageSize:l,currentPage:i})},M=e=>{const t=e.target;s.inpCurrPage=t.value},$=e=>{wo(e,go.ENTER)?w(e):wo(e,go.ARROW_UP)?(e.preventDefault(),S(e)):wo(e,go.ARROW_DOWN)&&(e.preventDefault(),E(e))},I=()=>(0,g.h)("button",{class:["vxe-pager--prev-btn",{"is--disabled":e.currentPage<=1}],type:"button",title:c.i18n("vxe.pager.prevPage"),onClick:E},[(0,g.h)("i",{class:["vxe-pager--btn-icon",e.iconPrevPage||c.icon.PAGER_PREV_PAGE]})]),D=t=>(0,g.h)(t||"button",{class:["vxe-pager--jump-prev",{"is--fixed":!t,"is--disabled":e.currentPage<=1}],type:"button",title:c.i18n("vxe.pager.prevJump"),onClick:k},[t?(0,g.h)("i",{class:["vxe-pager--jump-more-icon",e.iconJumpMore||c.icon.PAGER_JUMP_MORE]}):null,(0,g.h)("i",{class:["vxe-pager--jump-icon",e.iconJumpPrev||c.icon.PAGER_JUMP_PREV]})]),F=t=>{const o=v.value;return(0,g.h)(t||"button",{class:["vxe-pager--jump-next",{"is--fixed":!t,"is--disabled":e.currentPage>=o}],type:"button",title:c.i18n("vxe.pager.nextJump"),onClick:R},[t?(0,g.h)("i",{class:["vxe-pager--jump-more-icon",e.iconJumpMore||c.icon.PAGER_JUMP_MORE]}):null,(0,g.h)("i",{class:["vxe-pager--jump-icon",e.iconJumpNext||c.icon.PAGER_JUMP_NEXT]})])},N=()=>{const t=v.value;return(0,g.h)("button",{class:["vxe-pager--next-btn",{"is--disabled":e.currentPage>=t}],type:"button",title:c.i18n("vxe.pager.nextPage"),onClick:S},[(0,g.h)("i",{class:["vxe-pager--btn-icon",e.iconNextPage||c.icon.PAGER_NEXT_PAGE]})])},L=t=>{const{currentPage:o,pagerCount:n}=e,l=[],r=v.value,a=C.value,i=y.value,s=r>n,c=s&&o>i+1,u=s&&o<r-i;let d=1;return s&&(d=o>=r-i?Math.max(r-a.length+1,1):Math.max(o-i,1)),t&&c&&l.push((0,g.h)("button",{class:"vxe-pager--num-btn",type:"button",onClick:e=>x(e,1)},1),D("span")),a.forEach(((e,t)=>{const n=d+t;n<=r&&l.push((0,g.h)("button",{key:n,class:["vxe-pager--num-btn",{"is--active":o===n}],type:"button",onClick:e=>x(e,n)},n))})),t&&u&&l.push(F("button"),(0,g.h)("button",{class:"vxe-pager--num-btn",type:"button",onClick:e=>x(e,r)},r)),(0,g.h)("span",{class:"vxe-pager--btn-wrapper"},l)},A=()=>L(!0),P=()=>{const t=T.value;return(0,g.h)((0,g.resolveComponent)("vxe-select"),{class:"vxe-pager--sizes",modelValue:e.pageSize,placement:"top",transfer:e.transfer,options:t,onChange:O})},V=e=>(0,g.h)("span",{class:"vxe-pager--jump"},[e?(0,g.h)("span",{class:"vxe-pager--goto-text"},c.i18n("vxe.pager.goto")):null,(0,g.h)("input",{class:"vxe-pager--goto",value:s.inpCurrPage,type:"text",autocomplete:"off",onInput:M,onKeydown:$,onBlur:w}),e?(0,g.h)("span",{class:"vxe-pager--classifier-text"},c.i18n("vxe.pager.pageClassifier")):null]),_=()=>V(!0),j=()=>{const e=v.value;return(0,g.h)("span",{class:"vxe-pager--count"},[(0,g.h)("span",{class:"vxe-pager--separator"}),(0,g.h)("span",e)])},H=()=>(0,g.h)("span",{class:"vxe-pager--total"},c.i18n("vxe.pager.total",[e.total]));f={dispatchEvent(e,t,o){n(e,Object.assign({$pager:p,$event:o},t))},prevPage(){return E(),(0,g.nextTick)()},nextPage(){return S(),(0,g.nextTick)()},prevJump(){return k(),(0,g.nextTick)()},nextJump(){return R(),(0,g.nextTick)()}},m={handlePrevPage:E,handleNextPage:S,handlePrevJump:k,handleNextJump:R},Object.assign(p,f,m),(0,g.watch)((()=>e.currentPage),(e=>{s.inpCurrPage=e}));const B=()=>{const{align:t,layouts:n,className:l}=e,s=[],c=r.value,d=v.value;return o.left&&s.push((0,g.h)("span",{class:"vxe-pager--left-wrapper"},o.left({$grid:i}))),n.forEach((e=>{let t;switch(e){case"PrevPage":t=I;break;case"PrevJump":t=D;break;case"Number":t=L;break;case"JumpNumber":t=A;break;case"NextJump":t=F;break;case"NextPage":t=N;break;case"Sizes":t=P;break;case"FullJump":t=_;break;case"Jump":t=V;break;case"PageCount":t=j;break;case"Total":t=H;break}t&&s.push(t())})),o.right&&s.push((0,g.h)("span",{class:"vxe-pager--right-wrapper"},o.right({$grid:i}))),(0,g.h)("div",{ref:u,class:["vxe-pager",l?a().isFunction(l)?l({$pager:p}):l:"",{[`size--${c}`]:c,[`align--${t}`]:t,"is--border":e.border,"is--background":e.background,"is--perfect":e.perfect,"is--hidden":e.autoHidden&&1===d,"is--loading":e.loading}]},[(0,g.h)("div",{class:"vxe-pager--wrapper"},s)])};return p.renderVN=B,p},render(){return this.renderVN()}});const $l=Object.assign(Ml,{install:function(e){e.component(Ml.name,Ml)}}),Il=$l;uo.component(Ml.name,Ml);const Dl=Object.assign(Go,{install(e){e.component(Go.name,Go)}}),Fl=Dl;uo.component(Go.name,Go);var Nl=(0,g.defineComponent)({name:"VxeCheckboxGroup",props:{modelValue:Array,disabled:Boolean,max:{type:[String,Number],default:null},size:{type:String,default:()=>c.checkbox.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,g.inject)("$xeform",null),r=(0,g.inject)("$xeformiteminfo",null),i=a().uniqueId(),s=(0,g.computed)((()=>{const{modelValue:t,max:o}=e;return!!o&&(t?t.length:0)>=a().toNumber(o)})),c={computeIsMaximize:s},u={xID:i,props:e,context:t,getComputeMaps:()=>c};Fo(e);const d={dispatchEvent(e,t,o){n(e,Object.assign({$checkboxGroup:u,$event:o},t))}},p={handleChecked(t,o){const{checked:a,label:i}=t,s=e.modelValue||[],c=s.indexOf(i);a?-1===c&&s.push(i):s.splice(c,1),n("update:modelValue",s),u.dispatchEvent("change",Object.assign({checklist:s},t),o),l&&r&&l.triggerItemEvent(o,r.itemConfig.field,s)}};Object.assign(u,d,p);const f=()=>(0,g.h)("div",{class:"vxe-checkbox-group"},o.default?o.default({}):[]);return u.renderVN=f,(0,g.provide)("$xecheckboxgroup",u),f}});const Ll=Object.assign(Nl,{install(e){e.component(Nl.name,Nl)}}),Al=Ll;uo.component(Nl.name,Nl);const Pl=Object.assign(tn,{install:function(e){e.component(tn.name,tn)}}),Vl=Pl;uo.component(tn.name,tn);const _l=Object.assign(en,{install:function(e){e.component(en.name,en)}}),jl=_l;uo.component(en.name,en);var Hl=(0,g.defineComponent)({name:"VxeRadioButton",props:{modelValue:[String,Number,Boolean],label:{type:[String,Number,Boolean],default:null},title:[String,Number],content:[String,Number],disabled:Boolean,strict:{type:Boolean,default:()=>c.radioButton.strict},size:{type:String,default:()=>c.radioButton.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,g.inject)("$xeform",null),r=(0,g.inject)("$xeformiteminfo",null),i=a().uniqueId(),s=Fo(e),c={xID:i,props:e,context:t};let u={};const d=(0,g.inject)("$xeradiogroup",null),p=(0,g.computed)((()=>e.disabled||d&&d.props.disabled)),f=(0,g.computed)((()=>d?d.name:null)),m=(0,g.computed)((()=>d?d.props.strict:e.strict)),h=(0,g.computed)((()=>{const{modelValue:t,label:o}=e;return d?d.props.modelValue===o:t===o}));u={dispatchEvent(e,t,o){n(e,Object.assign({$radioButton:c,$event:o},t))}},Object.assign(c,u);const v=(e,t)=>{d?d.handleChecked({label:e},t):(n("update:modelValue",e),u.dispatchEvent("change",{label:e},t),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,e))},x=t=>{const o=p.value;o||v(e.label,t)},b=t=>{const o=p.value,n=m.value;o||n||e.label===(d?d.props.modelValue:e.modelValue)&&v(null,t)},w=()=>{const t=s.value,n=p.value,l=f.value,r=h.value;return(0,g.h)("label",{class:["vxe-radio","vxe-radio-button",{[`size--${t}`]:t,"is--disabled":n}],title:e.title},[(0,g.h)("input",{class:"vxe-radio--input",type:"radio",name:l,checked:r,disabled:n,onChange:x,onClick:b}),(0,g.h)("span",{class:"vxe-radio--label"},o.default?o.default({}):z(e.content))])};return Object.assign(c,{renderVN:w,dispatchEvent:dispatchEvent}),w}});const Bl=Object.assign(Hl,{install:function(e){e.component(Hl.name,Hl)}}),zl=Bl;uo.component(Hl.name,Hl);const Wl=Object.assign(Yo,{install(e){e.component(Yo.name,Yo)}}),ql=Wl;uo.component(Yo.name,Yo);let Ul;var Xl=(0,g.defineComponent)({name:"VxeTextarea",props:{modelValue:[String,Number],className:String,immediate:{type:Boolean,default:!0},name:String,readonly:Boolean,disabled:Boolean,placeholder:{type:String,default:()=>a().eqNull(c.textarea.placeholder)?c.i18n("vxe.base.pleaseInput"):c.textarea.placeholder},maxlength:[String,Number],rows:{type:[String,Number],default:2},cols:{type:[String,Number],default:null},showWordCount:Boolean,countMethod:Function,autosize:[Boolean,Object],form:String,resize:{type:String,default:()=>c.textarea.resize},size:{type:String,default:()=>c.textarea.size||c.size}},emits:["update:modelValue","input","keydown","keyup","click","change","focus","blur"],setup(e,t){const{emit:o}=t,n=(0,g.inject)("$xeform",null),l=(0,g.inject)("$xeformiteminfo",null),r=a().uniqueId(),i=Fo(e),s=(0,g.reactive)({inputValue:e.modelValue}),u=(0,g.ref)(),d=(0,g.ref)(),p={refElem:u,refTextarea:d},f={xID:r,props:e,context:t,reactData:s,getRefMaps:()=>p};let m={};const h=(0,g.computed)((()=>a().getSize(s.inputValue))),v=(0,g.computed)((()=>{const t=h.value;return e.maxlength&&t>a().toNumber(e.maxlength)})),x=(0,g.computed)((()=>Object.assign({minRows:1,maxRows:10},c.textarea.autosize,e.autosize))),b=()=>{const{size:t,autosize:o}=e,{inputValue:n}=s;if(o){Ul||(Ul=document.createElement("div")),Ul.parentNode||document.body.appendChild(Ul);const e=d.value,o=getComputedStyle(e);Ul.className=["vxe-textarea--autosize",t?`size--${t}`:""].join(" "),Ul.style.width=`${e.clientWidth}px`,Ul.style.padding=o.padding,Ul.innerText=(""+(n||"　")).replace(/\n$/,"\n　")}},w=()=>{e.autosize&&(0,g.nextTick)((()=>{const e=x.value,{minRows:t,maxRows:o}=e,n=d.value,l=Ul.clientHeight,r=getComputedStyle(n),i=a().toNumber(r.lineHeight),s=a().toNumber(r.paddingTop),c=a().toNumber(r.paddingBottom),u=a().toNumber(r.borderTopWidth),p=a().toNumber(r.borderBottomWidth),f=s+c+u+p,m=(l-f)/i,h=m&&/[0-9]/.test(""+m)?m:Math.floor(m)+1;let g=h;h<t?g=t:h>o&&(g=o),n.style.height=`${g*i+f}px`}))},C=e=>{const t=s.inputValue;f.dispatchEvent(e.type,{value:t},e)},y=(t,r)=>{s.inputValue=t,o("update:modelValue",t),a().toValueString(e.modelValue)!==t&&(m.dispatchEvent("change",{value:t},r),n&&l&&n.triggerItemEvent(r,l.itemConfig.field,t))},T=t=>{const{immediate:o}=e,n=t.target,l=n.value;s.inputValue=l,o&&y(l,t),f.dispatchEvent("input",{value:l},t),w()},E=t=>{const{immediate:o}=e;o?C(t):y(s.inputValue,t)},S=t=>{const{immediate:o}=e,{inputValue:n}=s;o||y(n,t),f.dispatchEvent("blur",{value:n},t)};m={dispatchEvent(e,t,n){o(e,Object.assign({$textarea:f,$event:n},t))},focus(){const e=d.value;return e.focus(),(0,g.nextTick)()},blur(){const e=d.value;return e.blur(),(0,g.nextTick)()}},Object.assign(f,m),(0,g.watch)((()=>e.modelValue),(e=>{s.inputValue=e,b()})),(0,g.nextTick)((()=>{const{autosize:t}=e;t&&(b(),w())}));const k=()=>{const{className:t,resize:o,placeholder:n,disabled:l,maxlength:r,autosize:c,showWordCount:p,countMethod:f,rows:m,cols:x}=e,{inputValue:b}=s,w=i.value,y=v.value,k=h.value;return(0,g.h)("div",{ref:u,class:["vxe-textarea",t,{[`size--${w}`]:w,"is--autosize":c,"is--count":p,"is--disabled":l,"def--rows":!a().eqNull(m),"def--cols":!a().eqNull(x)}]},[(0,g.h)("textarea",{ref:d,class:"vxe-textarea--inner",value:b,name:e.name,placeholder:n?z(n):null,maxlength:r,readonly:e.readonly,disabled:l,rows:m,cols:x,style:o?{resize:o}:null,onInput:T,onChange:E,onKeydown:C,onKeyup:C,onClick:C,onFocus:C,onBlur:S}),p?(0,g.h)("span",{class:["vxe-textarea--count",{"is--error":y}]},f?`${f({value:b})}`:`${k}${r?`/${r}`:""}`):null])};return f.renderVN=k,f},render(){return this.renderVN()}});const Yl=Object.assign(Xl,{install:function(e){e.component(Xl.name,Xl)}}),Gl=Yl;uo.component(Xl.name,Xl);const Kl=Object.assign(No,{install(e){e.component(No.name,No)}}),Zl=Kl;uo.component(No.name,No);function Jl(e){return po(),new Promise((t=>{if(e&&e.id&&Vo.some((t=>t.props.id===e.id)))t("exist");else{const o=e.onHide,n=Object.assign(e,{key:a().uniqueId(),modelValue:!0,onHide(e){const l=so.modals;o&&o(e),so.modals=l.filter((e=>e.key!==n.key)),t(e.type)}});so.modals.push(n)}}))}function Ql(e){return a().find(Vo,(t=>t.props.id===e))}function er(e){const t=e?[Ql(e)]:Vo,o=[];return t.forEach((e=>{e&&o.push(e.close())})),Promise.all(o)}function tr(e,t,o,n){let l;return l=a().isObject(t)?t:{content:a().toValueString(t),title:o},Jl({...e,...n,...l})}function or(e,t,o){return tr({type:"alert",showFooter:!0},e,t,o)}function nr(e,t,o){return tr({type:"confirm",status:"question",showFooter:!0},e,t,o)}function lr(e,t){return tr({type:"message",mask:!1,lockView:!1,showHeader:!1},e,"",t)}const rr={get:Ql,close:er,open:Jl,alert:or,confirm:nr,message:lr},ar=rr,ir=Object.assign(jo,{install:function(e){e.component(jo.name,jo),oo.modal=rr}}),sr=ir;uo.component(jo.name,jo);var cr=(0,g.defineComponent)({name:"VxeTooltip",props:{modelValue:Boolean,size:{type:String,default:()=>c.tooltip.size||c.size},trigger:{type:String,default:()=>c.tooltip.trigger},theme:{type:String,default:()=>c.tooltip.theme},content:{type:[String,Number],default:null},useHTML:Boolean,zIndex:[String,Number],popupClassName:[String,Function],isArrow:{type:Boolean,default:!0},enterable:Boolean,enterDelay:{type:Number,default:()=>c.tooltip.enterDelay},leaveDelay:{type:Number,default:()=>c.tooltip.leaveDelay}},emits:["update:modelValue"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=Fo(e),i=(0,g.reactive)({target:null,isUpdate:!1,visible:!1,tipContent:"",tipActive:!1,tipTarget:null,tipZindex:0,tipStore:{style:{},placement:"",arrowStyle:{}}}),s=(0,g.ref)(),c={refElem:s},u={xID:l,props:e,context:t,reactData:i,getRefMaps:()=>c};let d={};const p=()=>{const{tipTarget:e,tipStore:t}=i;if(e){const{scrollTop:o,scrollLeft:n,visibleWidth:l}=ne(),{top:r,left:a}=de(e),i=s.value,c=6,u=i.offsetHeight,d=i.offsetWidth;let p=a,f=r-u-c;p=Math.max(c,a+Math.floor((e.offsetWidth-d)/2)),p+d+c>n+l&&(p=n+l-d-c),r-u<o+c&&(t.placement="bottom",f=r+e.offsetHeight+c),t.style.top=`${f}px`,t.style.left=`${p}px`,t.arrowStyle.left=a-p+e.offsetWidth/2+"px"}},f=e=>{e!==i.visible&&(i.visible=e,i.isUpdate=!0,n("update:modelValue",e))},m=()=>{i.tipZindex<H()&&(i.tipZindex=j())},h=()=>{i.visible?d.close():d.open()},v=()=>{d.open()},x=()=>{const{trigger:t,enterable:o,leaveDelay:n}=e;i.tipActive=!1,o&&"hover"===t?setTimeout((()=>{i.tipActive||d.close()}),n):d.close()},b=()=>{i.tipActive=!0},w=()=>{const{trigger:t,enterable:o,leaveDelay:n}=e;i.tipActive=!1,o&&"hover"===t&&setTimeout((()=>{i.tipActive||d.close()}),n)},C=()=>{const{tipStore:t}=i,o=s.value;if(o){const e=o.parentNode;e||document.body.appendChild(o)}return f(!0),m(),t.placement="top",t.style={width:"auto",left:0,top:0,zIndex:e.zIndex||i.tipZindex},t.arrowStyle={left:"50%"},d.updatePlacement()},y=a().debounce((()=>{i.tipActive&&C()}),e.enterDelay,{leading:!1,trailing:!0});d={dispatchEvent(e,t,o){n(e,Object.assign({$tooltip:u,$event:o},t))},open(e,t){return d.toVisible(e||i.target,t)},close(){return i.tipTarget=null,i.tipActive=!1,Object.assign(i.tipStore,{style:{},placement:"",arrowStyle:null}),f(!1),(0,g.nextTick)()},toVisible(t,o){if(t){const{trigger:n,enterDelay:l}=e;if(i.tipActive=!0,i.tipTarget=t,o&&(i.tipContent=o),!l||"hover"!==n)return C();y()}return(0,g.nextTick)()},updatePlacement(){return(0,g.nextTick)().then((()=>{const{tipTarget:e}=i,t=s.value;if(e&&t)return p(),(0,g.nextTick)().then(p)}))},isActived(){return i.tipActive},setActived(e){i.tipActive=!!e}},Object.assign(u,d),(0,g.watch)((()=>e.content),(()=>{i.tipContent=e.content})),(0,g.watch)((()=>e.modelValue),(()=>{i.isUpdate||(e.modelValue?d.open():d.close()),i.isUpdate=!1})),(0,g.onMounted)((()=>{(0,g.nextTick)((()=>{const{trigger:t,content:o,modelValue:n}=e,l=s.value;if(l){const e=l.parentNode;if(e){i.tipContent=o,i.tipZindex=j(),a().arrayEach(l.children,((t,o)=>{o>1&&(e.insertBefore(t,l),i.target||(i.target=t))})),e.removeChild(l);const{target:r}=i;r&&("hover"===t?(r.onmouseenter=v,r.onmouseleave=x):"click"===t&&(r.onclick=h)),n&&d.open()}}}))})),(0,g.onBeforeUnmount)((()=>{const{trigger:t}=e,{target:o}=i,n=s.value;if(n){const e=n.parentNode;e&&e.removeChild(n)}o&&("hover"===t?(o.onmouseenter=null,o.onmouseleave=null):"click"===t&&(o.onclick=null))}));const T=()=>{const{useHTML:t}=e,{tipContent:n}=i,l=o.content;return l?(0,g.h)("div",{key:1,class:"vxe-table--tooltip-content"},We(l({}))):t?(0,g.h)("div",{key:2,class:"vxe-table--tooltip-content",innerHTML:n}):(0,g.h)("div",{key:3,class:"vxe-table--tooltip-content"},W(n))},E=()=>{const{popupClassName:t,theme:n,isArrow:l,enterable:c}=e,{tipActive:d,visible:p,tipStore:f}=i,m=o.default,h=r.value;let v;return c&&(v={onMouseenter:b,onMouseleave:w}),(0,g.h)("div",{ref:s,class:["vxe-table--tooltip-wrapper",`theme--${n}`,t?a().isFunction(t)?t({$tooltip:u}):t:"",{[`size--${h}`]:h,[`placement--${f.placement}`]:f.placement,"is--enterable":c,"is--visible":p,"is--arrow":l,"is--active":d}],style:f.style,...v},[T(),(0,g.h)("div",{class:"vxe-table--tooltip-arrow",style:f.arrowStyle}),...m?We(m({})):[]])};return u.renderVN=E,u},render(){return this.renderVN()}});const ur=Object.assign(cr,{install:function(e){oo.tooltip=!0,e.component(cr.name,cr)}}),dr=ur;uo.component(cr.name,cr);class pr{constructor(e,t){Object.assign(this,{id:a().uniqueId("item_"),title:t.title,field:t.field,span:t.span,align:t.align,titleAlign:t.titleAlign,titleWidth:t.titleWidth,titleColon:t.titleColon,titleAsterisk:t.titleAsterisk,titlePrefix:t.titlePrefix,titleSuffix:t.titleSuffix,titleOverflow:t.titleOverflow,showTitle:t.showTitle,resetValue:t.resetValue,visibleMethod:t.visibleMethod,visible:t.visible,folding:t.folding,collapseNode:t.collapseNode,className:t.className,contentClassName:t.contentClassName,contentStyle:t.contentStyle,titleClassName:t.titleClassName,titleStyle:t.titleStyle,itemRender:t.itemRender,showError:!1,errRule:null,slots:t.slots,children:[]})}update(e,t){this[e]=t}}function fr(e){return e instanceof pr}function mr(e,t){return fr(t)?t:new pr(e,t)}function hr(e,t){return t?a().isString(t)?e.getItemByField(t):t:null}function gr(e,t){const{reactData:o}=e,{collapseAll:n}=o,{folding:l,visible:r}=t;return!1===r||l&&n}function vr(e,t){let{visibleMethod:o,itemRender:n,visible:l,field:r}=t;if(!1===l)return l;const a=P(n)?oo.renderer.get(n.name):null;if(!o&&a&&a.itemVisibleMethod&&(o=a.itemVisibleMethod),!o)return!0;const{data:i}=e.props;return o({data:i,field:r,property:r,item:t,$form:e,$grid:e.xegrid})}function xr(e,t){Object.keys(e).forEach((o=>{(0,g.watch)((()=>e[o]),(e=>{t.update(o,e)}))}))}function br(e,t,o,n){const{reactData:l}=e,{staticItems:r}=l,i=t.parentNode,s=n?n.formItem:null,c=s?s.children:r;i&&(c.splice(a().arrayIndexOf(i.children,t),0,o),l.staticItems=r.slice(0))}function wr(e,t){const{reactData:o}=e,{staticItems:n}=o,l=a().findIndexOf(n,(e=>e.id===t.id));l>-1&&n.splice(l,1),o.staticItems=n.slice(0)}function Cr(e){return(0,g.h)("span",{class:"vxe-form--item-title-prefix"},[(0,g.h)("i",{class:e.icon||c.icon.FORM_PREFIX})])}function yr(e){return(0,g.h)("span",{class:"vxe-form--item-title-suffix"},[(0,g.h)("i",{class:e.icon||c.icon.FORM_SUFFIX})])}function Tr(e,t){const{data:o}=e.props,{computeTooltipOpts:n}=e.getComputeMaps(),{slots:l,field:r,itemRender:a,titlePrefix:i,titleSuffix:s}=t,c=n.value,u=P(a)?oo.renderer.get(a.name):null,d={data:o,field:r,property:r,item:t,$form:e,$grid:e.xegrid},p=l?l.title:null,f=[],m=[];i&&m.push(i.content||i.message?(0,g.h)((0,g.resolveComponent)("vxe-tooltip"),{...c,...i,content:z(i.content||i.message)},{default:()=>Cr(i)}):Cr(i)),m.push((0,g.h)("span",{class:"vxe-form--item-title-label"},u&&u.renderItemTitle?We(u.renderItemTitle(a,d)):p?e.callSlot(p,d):z(t.title))),f.push((0,g.h)("div",{class:"vxe-form--item-title-content"},m));const h=[];return s&&h.push(s.content||s.message?(0,g.h)((0,g.resolveComponent)("vxe-tooltip"),{...c,...s,content:z(s.content||s.message)},{default:()=>yr(s)}):yr(s)),f.push((0,g.h)("div",{class:"vxe-form--item-title-postfix"},h)),f}const Er=(0,g.defineComponent)({name:"VxeFormConfigItem",props:{itemConfig:Object},setup(e){const t=(0,g.inject)("$xeform",{}),o={itemConfig:e.itemConfig};(0,g.provide)("$xeformiteminfo",o),(0,g.provide)("$xeformgather",null);const n=()=>{const{reactData:o}=t,{data:n,rules:l,span:r,align:i,titleAlign:s,titleWidth:u,titleColon:d,titleAsterisk:p,titleOverflow:f,vertical:m}=t.props,{computeValidOpts:h}=t.getComputeMaps(),v=e.itemConfig,{collapseAll:x}=o,b=h.value,{slots:w,title:C,visible:y,folding:T,field:E,collapseNode:S,itemRender:k,showError:R,errRule:O,className:M,titleOverflow:$,vertical:I,children:D,showTitle:F,contentClassName:N,contentStyle:L,titleClassName:A,titleStyle:V}=v,_=P(k)?oo.renderer.get(k.name):null,j=_?_.itemClassName:"",H=_?_.itemStyle:null,B=_?_.itemContentClassName:"",W=_?_.itemContentStyle:null,q=_?_.itemTitleClassName:"",U=_?_.itemTitleStyle:null,X=w?w.default:null,Y=w?w.title:null,G=v.span||r,K=v.align||i,Z=a().eqNull(v.titleAlign)?s:v.titleAlign,J=a().eqNull(v.titleWidth)?u:v.titleWidth,Q=a().eqNull(v.titleColon)?d:v.titleColon,ee=a().eqNull(v.titleAsterisk)?p:v.titleAsterisk,te=a().isUndefined($)||a().isNull($)?f:$,oe=a().isUndefined(I)||a().isNull(I)?m:I,ne="ellipsis"===te,le="title"===te,re=!0===te||"tooltip"===te,ae=le||re||ne,ie={data:n,field:E,property:E,item:v,$form:t,$grid:t.xegrid};if(!1===y)return(0,g.createCommentVNode)();let se=!1;if(l){const e=l[E];e&&(se=e.some((e=>e.required)))}const ce=D&&D.length>0;if(ce){const e=D.map(((e,t)=>(0,g.h)(Er,{key:t,itemConfig:e})));return e.length?(0,g.h)("div",{class:["vxe-form--gather vxe-row",v.id,G?`vxe-col--${G} is--span`:"",M?a().isFunction(M)?M(ie):M:""]},e):(0,g.createCommentVNode)()}let ue=[];X?ue=t.callSlot(X,ie):_&&_.renderItemContent?ue=We(_.renderItemContent(k,ie)):E&&(ue=[a().toValueString(a().get(n,E))]),S&&ue.push((0,g.h)("div",{class:"vxe-form--item-trigger-node",onClick:t.toggleCollapseEvent},[(0,g.h)("span",{class:"vxe-form--item-trigger-text"},x?c.i18n("vxe.form.unfolding"):c.i18n("vxe.form.folding")),(0,g.h)("i",{class:["vxe-form--item-trigger-icon",x?c.icon.FORM_FOLDING:c.icon.FORM_UNFOLDING]})])),O&&b.showMessage&&ue.push((0,g.h)("div",{class:"vxe-form--item-valid",style:O.maxWidth?{width:`${O.maxWidth}px`}:null},O.content));const de=re?{onMouseenter(e){t.triggerTitleTipEvent(e,ie)},onMouseleave:t.handleTitleTipLeaveEvent}:{};return(0,g.h)("div",{class:["vxe-form--item",v.id,G?`vxe-col--${G} is--span`:"",M?a().isFunction(M)?M(ie):M:"",j?a().isFunction(j)?j(ie):j:"",{"is--title":C,"is--colon":Q,"is--vertical":oe,"is--asterisk":ee,"is--required":se,"is--hidden":T&&x,"is--active":vr(t,v),"is--error":R}],style:a().isFunction(H)?H(ie):H},[(0,g.h)("div",{class:"vxe-form--item-inner"},[!1!==F&&(C||Y)?(0,g.h)("div",{class:["vxe-form--item-title",Z?`align--${Z}`:"",ae?"is--ellipsis":"",q?a().isFunction(q)?q(ie):q:"",A?a().isFunction(A)?A(ie):A:""],style:Object.assign({},a().isFunction(U)?U(ie):U,a().isFunction(V)?V(ie):V,J?{width:isNaN(J)?J:`${J}px`}:null),title:le?z(C):null,...de},Tr(t,v)):null,(0,g.h)("div",{class:["vxe-form--item-content",K?`align--${K}`:"",B?a().isFunction(B)?B(ie):B:"",N?a().isFunction(N)?N(ie):N:""],style:Object.assign({},a().isFunction(W)?W(ie):W,a().isFunction(L)?L(ie):L)},ue)])])},l={renderVN:n};return l},render(){return this.renderVN()}});var Sr=Er;class kr{constructor(e){Object.assign(this,{$options:e,required:e.required,min:e.min,max:e.min,type:e.type,pattern:e.pattern,validator:e.validator,trigger:e.trigger,maxWidth:e.maxWidth})}get content(){return z(this.$options.content||this.$options.message)}get message(){return this.content}}const Rr=(e,t)=>{const{type:o,min:n,max:l,pattern:r}=e,i="number"===o,s=i?a().toNumber(t):a().getSize(t);return!(!i||!isNaN(t))||(!a().eqNull(n)&&s<a().toNumber(n)||(!a().eqNull(l)&&s>a().toNumber(l)||!(!r||(a().isRegExp(r)?r:new RegExp(r)).test(t))))};function Or(e,t){return a().isArray(e)&&(t=[]),t}var Mr=(0,g.defineComponent)({name:"VxeForm",props:{collapseStatus:{type:Boolean,default:!0},loading:Boolean,data:Object,size:{type:String,default:()=>c.form.size||c.size},span:{type:[String,Number],default:()=>c.form.span},align:{type:String,default:()=>c.form.align},titleAlign:{type:String,default:()=>c.form.titleAlign},titleWidth:{type:[String,Number],default:()=>c.form.titleWidth},titleColon:{type:Boolean,default:()=>c.form.titleColon},titleAsterisk:{type:Boolean,default:()=>c.form.titleAsterisk},titleOverflow:{type:[Boolean,String],default:null},vertical:{type:Boolean,default:null},className:[String,Function],readonly:Boolean,items:Array,rules:Object,preventSubmit:{type:Boolean,default:()=>c.form.preventSubmit},validConfig:Object,tooltipConfig:Object,customLayout:{type:Boolean,default:()=>c.form.customLayout}},emits:["update:collapseStatus","collapse","toggle-collapse","submit","submit-invalid","reset"],setup(e,t){const o=oo.tooltip,{slots:n,emit:l}=t,r=a().uniqueId(),i=Fo(e),s=(0,g.reactive)({collapseAll:e.collapseStatus,staticItems:[],formItems:[]}),u=(0,g.reactive)({tooltipTimeout:null,tooltipStore:{item:null,visible:!1}}),d=(0,g.inject)("$xegrid",null),m=(0,g.ref)(),h=(0,g.ref)();let v={};const x=(0,g.computed)((()=>Object.assign({},c.form.validConfig,e.validConfig))),b=(0,g.computed)((()=>Object.assign({},c.tooltip,c.form.tooltipConfig,e.tooltipConfig))),w={refElem:m},C={computeSize:i,computeValidOpts:x,computeTooltipOpts:b},y={xID:r,props:e,context:t,reactData:s,xegrid:d,getRefMaps:()=>w,getComputeMaps:()=>C},T=(e,t)=>e&&(a().isString(e)&&(e=n[e]||null),a().isFunction(e))?We(e(t)):[],E=e=>(e.length&&(e.forEach((e=>{e.slots&&a().each(e.slots,(e=>{a().isFunction(e)||n[e]||f("vxe.error.notSlot",[e])}))})),s.staticItems=a().mapTree(e,(e=>mr(y,e)),{children:"children"})),(0,g.nextTick)()),S=()=>{const e=[];return a().eachTree(s.formItems,(t=>{e.push(t)}),{children:"children"}),e},k=e=>{const t=a().findTree(s.formItems,(t=>t.field===e),{children:"children"});return t?t.item:null},R=()=>s.collapseAll,O=()=>{const e=!R();return s.collapseAll=e,l("update:collapseStatus",e),(0,g.nextTick)()},M=t=>{O();const o=R();v.dispatchEvent("toggle-collapse",{status:o,collapse:o,data:e.data},t),v.dispatchEvent("collapse",{status:o,collapse:o,data:e.data},t)},$=e=>{if(e){let t=e;a().isArray(e)||(t=[e]),t.forEach((e=>{if(e){const t=hr(y,e);t&&(t.showError=!1)}}))}else S().forEach((e=>{e.showError=!1}));return(0,g.nextTick)()},I=()=>{const{data:t}=e,o=S();return t&&o.forEach((e=>{const{field:o,resetValue:n,itemRender:l}=e;if(P(l)){const r=oo.renderer.get(l.name);r&&r.itemResetMethod?r.itemResetMethod({data:t,field:o,property:o,item:e,$form:y,$grid:y.xegrid}):o&&a().set(t,o,null===n?Or(a().get(t,o),void 0):a().clone(n,!0))}})),$()},D=t=>{t.preventDefault(),I(),v.dispatchEvent("reset",{data:e.data},t)},F=e=>{const t=m.value;for(let o=0;o<e.length;o++){const n=e[o],l=k(n);if(l&&P(l.itemRender)){const{itemRender:e}=l,n=oo.renderer.get(e.name);let r=null;if(o||me(t.querySelector(`.${l.id}`)),e.autofocus&&(r=t.querySelector(`.${l.id} ${e.autofocus}`)),!r&&n&&n.autofocus&&(r=t.querySelector(`.${l.id} ${n.autofocus}`)),r){r.focus();break}}}},N=(t,o,n)=>{const{data:l,rules:r}=e,i={};return a().isArray(o)||(o=[o]),Promise.all(o.map((e=>{const o=[],s=[];if(e&&r){const i=a().get(r,e);if(i){const r=a().isUndefined(n)?a().get(l,e):n;i.forEach((n=>{const{type:c,trigger:u,required:d,validator:m}=n;if("all"===t||!u||t===u)if(m){const t={itemValue:r,rule:n,rules:i,data:l,field:e,property:e,$form:y};let c;if(a().isString(m)){const e=oo.validators.get(m);e?e.itemValidatorMethod?c=e.itemValidatorMethod(t):p("vxe.error.notValidators",[m]):f("vxe.error.notValidators",[m])}else c=m(t);c&&(a().isError(c)?o.push(new kr({type:"custom",trigger:u,content:c.message,rule:new kr(n)})):c.catch&&s.push(c.catch((e=>{o.push(new kr({type:"custom",trigger:u,content:e?e.message:n.content||n.message,rule:new kr(n)}))}))))}else{const e="array"===c,t=a().isArray(r);let l=!0;l=e||t?!t||!r.length:a().isString(r)?q(r.trim()):q(r),(d?l||Rr(n,r):!l&&Rr(n,r))&&o.push(new kr(n))}}))}}return Promise.all(s).then((()=>{o.length&&(i[e]=o.map((t=>({$form:y,rule:t,data:l,field:e,property:e}))))}))}))).then((()=>{if(!a().isEmpty(i))return Promise.reject(i)}))};let L;const A=(t,o,n)=>{const{data:l,rules:r}=e,a=x.value,i={},s=[],c=[];return clearTimeout(L),l&&r?(t.forEach((e=>{const{field:t}=e;t&&!gr(y,e)&&vr(y,e)&&c.push(N(o||"all",t).then((()=>{e.errRule=null})).catch((o=>{const n=o[t];return i[t]||(i[t]=[]),i[t].push(n),s.push(t),e.errRule=n[0].rule,Promise.reject(n)})))})),Promise.all(c).then((()=>{n&&n()})).catch((()=>new Promise((e=>{L=window.setTimeout((()=>{t.forEach((e=>{e.errRule&&(e.showError=!0)}))}),20),!1!==a.autoPos&&(0,g.nextTick)((()=>{F(s)})),n?(n(i),e()):e(i)}))))):(n&&n(),Promise.resolve())},V=e=>($(),A(S(),"",e)),_=(e,t)=>{let o=[];return o=a().isArray(e)?e:[e],A(o.map((e=>hr(y,e))),"",t)},j=t=>{t.preventDefault(),e.preventSubmit||($(),A(S()).then((o=>{o?v.dispatchEvent("submit-invalid",{data:e.data,errMap:o},t):v.dispatchEvent("submit",{data:e.data},t)})))},H=()=>{const{tooltipStore:e}=u,t=h.value;return e.visible&&(Object.assign(e,{item:null,visible:!1}),t&&t.close()),(0,g.nextTick)()},B=(e,t)=>{const{item:o}=t,{tooltipStore:n}=u,l=h.value,r=e.currentTarget.children[0],a=(r.textContent||"").trim(),i=r.scrollWidth>r.clientWidth;clearTimeout(u.tooltipTimeout),n.item!==o&&H(),a&&i&&(Object.assign(n,{item:o,visible:!0}),l&&l.open(r,a))},z=()=>{const e=b.value;let t=h.value;t&&t.setActived(!1),e.enterable?u.tooltipTimeout=setTimeout((()=>{t=h.value,t&&!t.isActived()&&H()}),e.leaveDelay):H()},W=(e,t,o)=>t?N(e?["blur"].includes(e.type)?"blur":"change":"all",t,o).then((()=>{$(t)})).catch((e=>{const o=e[t],n=k(t);o&&n&&(n.showError=!0,n.errRule=o[0].rule)})):(0,g.nextTick)(),U=(e,t)=>{const{field:o}=e;return W(new Event("change"),o,t)};v={dispatchEvent(e,t,o){l(e,Object.assign({$form:y,$grid:d,$event:o},t))},reset:I,validate:V,validateField:_,clearValidate:$,updateStatus:U,toggleCollapse:O,getItems:S,getItemByField:k,closeTooltip:H};const X={callSlot:T,triggerItemEvent:W,toggleCollapseEvent:M,triggerTitleTipEvent:B,handleTitleTipLeaveEvent:z};Object.assign(y,v,X);const Y=(0,g.ref)(0);(0,g.watch)((()=>s.staticItems.length),(()=>{Y.value++})),(0,g.watch)((()=>s.staticItems),(()=>{Y.value++})),(0,g.watch)(Y,(()=>{s.formItems=s.staticItems}));const G=(0,g.ref)(0);(0,g.watch)((()=>e.items?e.items.length:-1),(()=>{G.value++})),(0,g.watch)((()=>e.items),(()=>{G.value++})),(0,g.watch)(G,(()=>{E(e.items||[])})),(0,g.watch)((()=>e.collapseStatus),(e=>{s.collapseAll=!!e})),(0,g.onMounted)((()=>{(0,g.nextTick)((()=>{e.customLayout&&e.items&&f("vxe.error.errConflicts",["custom-layout","items"]),E(e.items||[])}))}));const K=()=>{const{loading:t,className:l,data:r,customLayout:c}=e,{formItems:u}=s,d=i.value,p=b.value,f=n.default;return(0,g.h)("form",{ref:m,class:["vxe-form",l?a().isFunction(l)?l({items:u,data:r,$form:y}):l:"",{[`size--${d}`]:d,"is--loading":t}],onSubmit:j,onReset:D},[(0,g.h)("div",{class:"vxe-form--wrapper vxe-row"},c?f?f({}):[]:u.map(((e,t)=>(0,g.h)(Sr,{key:t,itemConfig:e})))),(0,g.h)("div",{class:"vxe-form-slots",ref:"hideItem"},c?[]:f?f({}):[]),(0,g.h)(Po,{class:"vxe-form--loading",modelValue:t}),o?(0,g.h)((0,g.resolveComponent)("vxe-tooltip"),{ref:h,...p}):(0,g.createCommentVNode)()])};return y.renderVN=K,(0,g.provide)("$xeform",y),(0,g.provide)("$xeformgather",null),(0,g.provide)("$xeformitem",null),(0,g.provide)("$xeformiteminfo",null),y},render(){return this.renderVN()}});const $r=Object.assign(Mr,{install(e){e.component(Mr.name,Mr)}}),Ir=$r;uo.component(Mr.name,Mr);const Dr={title:String,field:String,span:[String,Number],align:String,titleAlign:{type:String,default:null},titleWidth:{type:[String,Number],default:null},titleColon:{type:Boolean,default:null},titleAsterisk:{type:Boolean,default:null},showTitle:{type:Boolean,default:!0},vertical:{type:Boolean,default:null},className:[String,Function],contentClassName:[String,Function],contentStyle:[Object,Function],titleClassName:[String,Function],titleStyle:[Object,Function],titleOverflow:{type:[Boolean,String],default:null},titlePrefix:Object,titleSuffix:Object,resetValue:{default:null},visibleMethod:Function,visible:{type:Boolean,default:null},folding:Boolean,collapseNode:Boolean,itemRender:Object};var Fr=(0,g.defineComponent)({name:"VxeFormItem",props:Dr,setup(e,{slots:t}){const o=(0,g.ref)(),n=(0,g.inject)("$xeform",{}),l=(0,g.inject)("$xeformgather",null),r=(0,g.reactive)(mr(n,e)),i={formItem:r},s={itemConfig:r};r.slots=t,(0,g.provide)("$xeformiteminfo",s),(0,g.provide)("$xeformitem",i),(0,g.provide)("$xeformgather",null),xr(e,r),(0,g.onMounted)((()=>{br(n,o.value,r,l)})),(0,g.onUnmounted)((()=>{wr(n,r)}));const u=(e,t)=>{const{props:n,reactData:l}=e,{data:r,rules:i,titleAlign:s,titleWidth:u,titleColon:d,titleAsterisk:p,titleOverflow:f,vertical:m}=n,{collapseAll:h}=l,{computeValidOpts:v}=e.getComputeMaps(),x=v.value,{slots:b,title:w,visible:C,folding:y,field:T,collapseNode:E,itemRender:S,showError:k,errRule:R,className:O,titleOverflow:M,vertical:$,showTitle:I,contentClassName:D,contentStyle:F,titleClassName:N,titleStyle:L}=t,A=P(S)?oo.renderer.get(S.name):null,V=A?A.itemClassName:"",_=A?A.itemStyle:null,j=A?A.itemContentClassName:"",H=A?A.itemContentStyle:null,B=A?A.itemTitleClassName:"",W=A?A.itemTitleStyle:null,q=b?b.default:null,U=b?b.title:null,X=t.span||n.span,Y=t.align||n.align,G=a().eqNull(t.titleAlign)?s:t.titleAlign,K=a().eqNull(t.titleWidth)?u:t.titleWidth,Z=a().eqNull(t.titleColon)?d:t.titleColon,J=a().eqNull(t.titleAsterisk)?p:t.titleAsterisk,Q=a().isUndefined(M)||a().isNull(M)?f:M,ee=a().isUndefined($)||a().isNull($)?m:$,te="ellipsis"===Q,oe="title"===Q,ne=!0===Q||"tooltip"===Q,le=oe||ne||te,re={data:r,field:T,property:T,item:t,$form:e,$grid:e.xegrid};let ae=!1;if(!1===C)return(0,g.createCommentVNode)();if(i){const e=i[T];e&&(ae=e.some((e=>e.required)))}let ie=[];q?ie=e.callSlot(q,re):A&&A.renderItemContent?ie=We(A.renderItemContent(S,re)):T&&(ie=[`${a().get(r,T)}`]),E&&ie.push((0,g.h)("div",{class:"vxe-form--item-trigger-node",onClick:e.toggleCollapseEvent},[(0,g.h)("span",{class:"vxe-form--item-trigger-text"},h?c.i18n("vxe.form.unfolding"):c.i18n("vxe.form.folding")),(0,g.h)("i",{class:["vxe-form--item-trigger-icon",h?c.icon.FORM_FOLDING:c.icon.FORM_UNFOLDING]})])),R&&x.showMessage&&ie.push((0,g.h)("div",{class:"vxe-form--item-valid",style:R.maxWidth?{width:`${R.maxWidth}px`}:null},R.message));const se=ne?{onMouseenter(t){e.triggerTitleTipEvent(t,re)},onMouseleave:e.handleTitleTipLeaveEvent}:{};return(0,g.h)("div",{ref:o,class:["vxe-form--item",t.id,X?`vxe-col--${X} is--span`:"",O?a().isFunction(O)?O(re):O:"",V?a().isFunction(V)?V(re):V:"",{"is--title":w,"is--colon":Z,"is--vertical":ee,"is--asterisk":J,"is--required":ae,"is--hidden":y&&h,"is--active":vr(e,t),"is--error":k}],style:a().isFunction(_)?_(re):_},[(0,g.h)("div",{class:"vxe-form--item-inner"},[!1!==I&&(w||U)?(0,g.h)("div",{class:["vxe-form--item-title",G?`align--${G}`:"",le?"is--ellipsis":"",B?a().isFunction(B)?B(re):B:"",N?a().isFunction(N)?N(re):N:""],style:Object.assign({},a().isFunction(W)?W(re):W,a().isFunction(L)?L(re):L,K?{width:isNaN(K)?K:`${K}px`}:null),title:oe?z(w):null,...se},Tr(e,t)):null,(0,g.h)("div",{class:["vxe-form--item-content",Y?`align--${Y}`:"",j?a().isFunction(j)?j(re):j:"",D?a().isFunction(D)?D(re):D:""],style:Object.assign({},a().isFunction(H)?H(re):H,a().isFunction(F)?F(re):F)},ie)])])},d=()=>{const e=n?n.props:null;return e&&e.customLayout?u(n,r):(0,g.h)("div",{ref:o})},p={renderVN:d};return p},render(){return this.renderVN()}});const Nr=Object.assign(Fr,{install(e){e.component(Fr.name,Fr)}}),Lr=Nr;uo.component(Fr.name,Fr);var Ar=(0,g.defineComponent)({name:"VxeFormGather",props:Dr,setup(e,{slots:t}){const o=(0,g.ref)(),n=(0,g.inject)("$xeform",{}),l=(0,g.inject)("$xeformgather",null),r=t.default,a=(0,g.reactive)(mr(n,e)),i={formItem:a},s={itemConfig:a};a.children=[],(0,g.provide)("$xeformiteminfo",s),(0,g.provide)("$xeformgather",i),(0,g.provide)("$xeformitem",null),xr(e,a),(0,g.onMounted)((()=>{br(n,o.value,a,l)})),(0,g.onUnmounted)((()=>{wr(n,a)})),(0,g.nextTick)((()=>{n&&n.props.customLayout&&f("vxe.error.errConflicts",["custom-layout","<form-gather ...>"])}));const c=()=>(0,g.h)("div",{ref:o},r?r():[]),u={renderVN:c};return u},render(){return this.renderVN()}});const Pr=Object.assign(Ar,{install(e){e.component(Ar.name,Ar)}}),Vr=Pr;uo.component(Ar.name,Ar);const _r=Object.assign(Jo,{install:function(e){e.component(Jo.name,Jo)}}),jr=_r;uo.component(Jo.name,Jo);class Hr{constructor(e,t){Object.assign(this,{id:a().uniqueId("option_"),value:t.value,label:t.label,visible:t.visible,className:t.className,disabled:t.disabled})}update(e,t){this[e]=t}}function Br(e){return e instanceof Hr}function zr(e,t){return Br(t)?t:new Hr(e,t)}function Wr(e,t){Object.keys(e).forEach((o=>{(0,g.watch)((()=>e[o]),(e=>{t.update(o,e)}))}))}function qr(e,t,o,n){const{reactData:l}=e,{staticOptions:r}=l,i=t.parentNode,s=n?n.option:null,c=s?s.options:r;i&&c&&(c.splice(a().arrayIndexOf(i.children,t),0,o),l.staticOptions=r.slice(0))}function Ur(e,t){const{reactData:o}=e,{staticOptions:n}=o,l=a().findTree(n,(e=>e.id===t.id),{children:"options"});l&&l.items.splice(l.index,1),o.staticOptions=n.slice(0)}var Xr=(0,g.defineComponent)({name:"VxeOptgroup",props:{label:{type:[String,Number,Boolean],default:""},visible:{type:Boolean,default:null},className:[String,Function],disabled:Boolean},setup(e,{slots:t}){const o=(0,g.ref)(),n=(0,g.inject)("$xeselect",{}),l=zr(n,e),r={option:l};return l.options=[],(0,g.provide)("xeoptgroup",r),Wr(e,l),(0,g.onMounted)((()=>{qr(n,o.value,l)})),(0,g.onUnmounted)((()=>{Ur(n,l)})),()=>(0,g.h)("div",{ref:o},t.default?t.default():[])}});const Yr=Object.assign(Xr,{install:function(e){e.component(Xr.name,Xr)}}),Gr=Yr;uo.component(Xr.name,Xr);var Kr=(0,g.defineComponent)({name:"VxeOption",props:{value:null,label:{type:[String,Number,Boolean],default:""},visible:{type:Boolean,default:null},className:[String,Function],disabled:Boolean},setup(e,{slots:t}){const o=(0,g.ref)(),n=(0,g.inject)("$xeselect",{}),l=(0,g.inject)("xeoptgroup",null),r=zr(n,e);return r.slots=t,Wr(e,r),(0,g.onMounted)((()=>{qr(n,o.value,r,l)})),(0,g.onUnmounted)((()=>{Ur(n,r)})),()=>(0,g.h)("div",{ref:o})}});const Zr=Object.assign(Kr,{install:function(e){e.component(Kr.name,Kr)}}),Jr=Zr;uo.component(Kr.name,Kr);var Qr=(0,g.defineComponent)({name:"VxeSwitch",props:{modelValue:[String,Number,Boolean],disabled:Boolean,size:{type:String,default:()=>c["switch"].size||c.size},openLabel:String,closeLabel:String,openValue:{type:[String,Number,Boolean],default:!0},closeValue:{type:[String,Number,Boolean],default:!1},openIcon:String,closeIcon:String},emits:["update:modelValue","change","focus","blur"],setup(e,t){const{emit:o}=t,n=(0,g.inject)("$xeform",null),l=(0,g.inject)("$xeformiteminfo",null),r=a().uniqueId(),i=Fo(e),s=(0,g.reactive)({isActivated:!1,hasAnimat:!1,offsetLeft:0}),c={xID:r,props:e,context:t,reactData:s},u=(0,g.ref)();let d={};const p=(0,g.computed)((()=>z(e.openLabel))),f=(0,g.computed)((()=>z(e.closeLabel))),m=(0,g.computed)((()=>e.modelValue===e.openValue));let h;const v=t=>{if(!e.disabled){const r=m.value;clearTimeout(h);const a=r?e.closeValue:e.openValue;s.hasAnimat=!0,o("update:modelValue",a),d.dispatchEvent("change",{value:a},t),n&&l&&n.triggerItemEvent(t,l.itemConfig.field,a),h=setTimeout((()=>{s.hasAnimat=!1}),400)}},x=t=>{s.isActivated=!0,d.dispatchEvent("focus",{value:e.modelValue},t)},b=t=>{s.isActivated=!1,d.dispatchEvent("blur",{value:e.modelValue},t)};d={dispatchEvent(e,t,n){o(e,Object.assign({$switch:c,$event:n},t))},focus(){const e=u.value;return s.isActivated=!0,e.focus(),(0,g.nextTick)()},blur(){const e=u.value;return e.blur(),s.isActivated=!1,(0,g.nextTick)()}},Object.assign(c,d);const w=()=>{const{disabled:t,openIcon:o,closeIcon:n}=e,l=m.value,r=i.value,a=p.value,c=f.value;return(0,g.h)("div",{class:["vxe-switch",l?"is--on":"is--off",{[`size--${r}`]:r,"is--disabled":t,"is--animat":s.hasAnimat}]},[(0,g.h)("button",{ref:u,class:"vxe-switch--button",type:"button",disabled:t,onClick:v,onFocus:x,onBlur:b},[(0,g.h)("span",{class:"vxe-switch--label vxe-switch--label-on"},[o?(0,g.h)("i",{class:["vxe-switch--label-icon",o]}):(0,g.createCommentVNode)(),a]),(0,g.h)("span",{class:"vxe-switch--label vxe-switch--label-off"},[n?(0,g.h)("i",{class:["vxe-switch--label-icon",n]}):(0,g.createCommentVNode)(),c]),(0,g.h)("span",{class:"vxe-switch--icon"})])])};return c.renderVN=w,c},render(){return this.renderVN()}});const ea=Object.assign(Qr,{install:function(e){e.component(Qr.name,Qr)}}),ta=ea;uo.component(Qr.name,Qr);let oa;const na=[],la=500;function ra(){na.length&&(na.forEach((e=>{e.tarList.forEach((t=>{const{target:o,width:n,heighe:l}=t,r=o.clientWidth,a=o.clientHeight,i=r&&n!==r,s=a&&l!==a;(i||s)&&(t.width=r,t.heighe=a,setTimeout(e.callback))}))})),aa())}function aa(){clearTimeout(oa),oa=setTimeout(ra,c.resizeInterval||la)}class ia{constructor(e){v(this,"tarList",[]),v(this,"callback",void 0),this.callback=e}observe(e){if(e){const{tarList:t}=this;t.some((t=>t.target===e))||t.push({target:e,width:e.clientWidth,heighe:e.clientHeight}),na.length||aa(),na.some((e=>e===this))||na.push(this)}}unobserve(e){a().remove(na,(t=>t.tarList.some((t=>t.target===e))))}disconnect(){a().remove(na,(e=>e===this))}}function sa(e){return window.ResizeObserver?new window.ResizeObserver(e):new ia(e)}var ca=(0,g.defineComponent)({name:"VxeList",props:{data:Array,height:[Number,String],maxHeight:[Number,String],loading:Boolean,className:[String,Function],size:{type:String,default:()=>c.list.size||c.size},autoResize:{type:Boolean,default:()=>c.list.autoResize},syncResize:[Boolean,String,Number],scrollY:Object},emits:["scroll"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=Fo(e),i=(0,g.reactive)({scrollYLoad:!1,bodyHeight:0,rowHeight:0,topSpaceHeight:0,items:[]}),s=(0,g.ref)(),u=(0,g.ref)(),d=(0,g.ref)(),p={fullData:[],lastScrollLeft:0,lastScrollTop:0,scrollYStore:{startIndex:0,endIndex:0,visibleSize:0,offsetSize:0,rowHeight:0}},f={refElem:s},m={xID:l,props:e,context:t,reactData:i,internalData:p,getRefMaps:()=>f};let h={};const v=(0,g.computed)((()=>Object.assign({},c.list.scrollY,e.scrollY))),x=(0,g.computed)((()=>{const{height:t,maxHeight:o}=e,n={};return t?n.height=`${isNaN(t)?t:`${t}px`}`:o&&(n.height="auto",n.maxHeight=`${isNaN(o)?o:`${o}px`}`),n})),b=()=>{const{scrollYLoad:e}=i,{scrollYStore:t,fullData:o}=p;i.bodyHeight=e?o.length*t.rowHeight:0,i.topSpaceHeight=e?Math.max(t.startIndex*t.rowHeight,0):0},w=()=>{const{scrollYLoad:e}=i,{fullData:t,scrollYStore:o}=p;return i.items=e?t.slice(o.startIndex,o.endIndex):t.slice(0),(0,g.nextTick)()},C=()=>{w(),b()},y=()=>(0,g.nextTick)().then((()=>{const{scrollYLoad:e}=i,{scrollYStore:t}=p,o=d.value,n=v.value;let l,r=0;if(o&&(n.sItem&&(l=o.querySelector(n.sItem)),l||(l=o.children[0])),l&&(r=l.offsetHeight),r=Math.max(20,r),t.rowHeight=r,e){const e=u.value,o=Math.max(8,Math.ceil(e.clientHeight/r)),l=n.oSize?a().toNumber(n.oSize):Y.edge?10:0;t.offsetSize=l,t.visibleSize=o,t.endIndex=Math.max(t.startIndex,o+l,t.endIndex),C()}else b();i.rowHeight=r})),T=()=>{const e=u.value;return e&&(e.scrollTop=0),(0,g.nextTick)()},E=(e,t)=>{const o=u.value;return a().isNumber(e)&&(o.scrollLeft=e),a().isNumber(t)&&(o.scrollTop=t),i.scrollYLoad?new Promise((e=>{setTimeout((()=>{(0,g.nextTick)((()=>{e()}))}),50)})):(0,g.nextTick)()},S=()=>{const{lastScrollLeft:e,lastScrollTop:t}=p;return T().then((()=>{if(e||t)return p.lastScrollLeft=0,p.lastScrollTop=0,E(e,t)}))},k=()=>{const e=s.value;return e.clientWidth&&e.clientHeight?y():Promise.resolve()},R=e=>{const{scrollYStore:t}=p,{startIndex:o,endIndex:n,visibleSize:l,offsetSize:r,rowHeight:a}=t,i=e.target,s=i.scrollTop,c=Math.floor(s/a),u=Math.max(0,c-1-r),d=c+l+r;(c<=o||c>=n-l-1)&&(o===u&&n===d||(t.startIndex=u,t.endIndex=d,C()))},O=e=>{const t=e.target,o=t.scrollTop,n=t.scrollLeft,l=n!==p.lastScrollLeft,r=o!==p.lastScrollTop;p.lastScrollTop=o,p.lastScrollLeft=n,i.scrollYLoad&&R(e),h.dispatchEvent("scroll",{scrollLeft:n,scrollTop:o,isX:l,isY:r},e)};h={dispatchEvent(e,t,o){n(e,Object.assign({$list:m,$event:o},t))},loadData(e){const{scrollYStore:t}=p,o=v.value,n=e||[];return Object.assign(t,{startIndex:0,endIndex:1,visibleSize:0}),p.fullData=n,i.scrollYLoad=!!o.enabled&&o.gt>-1&&(0===o.gt||o.gt<=n.length),w(),y().then((()=>{S()}))},reloadData(e){return T(),h.loadData(e)},recalculate:k,scrollTo:E,refreshScroll:S,clearScroll:T},Object.assign(m,h);const M=(0,g.ref)(0);let $;(0,g.watch)((()=>e.data?e.data.length:-1),(()=>{M.value++})),(0,g.watch)((()=>e.data),(()=>{M.value++})),(0,g.watch)(M,(()=>{h.loadData(e.data||[])})),(0,g.watch)((()=>e.syncResize),(e=>{e&&(k(),(0,g.nextTick)((()=>setTimeout((()=>k())))))})),(0,g.onActivated)((()=>{k().then((()=>S()))})),(0,g.nextTick)((()=>{if(yo.on(m,"resize",(()=>{k()})),e.autoResize){const e=s.value;$=sa((()=>k())),$.observe(e)}h.loadData(e.data||[])})),(0,g.onUnmounted)((()=>{$&&$.disconnect(),yo.off(m,"resize")}));const I=()=>{const{className:t,loading:n}=e,{bodyHeight:l,topSpaceHeight:c,items:p}=i,f=r.value,h=x.value;return(0,g.h)("div",{ref:s,class:["vxe-list",t?a().isFunction(t)?t({$list:m}):t:"",{[`size--${f}`]:f,"is--loading":n}]},[(0,g.h)("div",{ref:u,class:"vxe-list--virtual-wrapper",style:h,onScroll:O},[(0,g.h)("div",{class:"vxe-list--y-space",style:{height:l?`${l}px`:""}}),(0,g.h)("div",{ref:d,class:"vxe-list--body",style:{marginTop:c?`${c}px`:""}},o.default?o.default({items:p,$list:m}):[])]),(0,g.h)(Po,{class:"vxe-list--loading",modelValue:n})])};return m.renderVN=I,m},render(){return this.renderVN()}});const ua=Object.assign(ca,{install(e){e.component(ca.name,ca)}}),da=ua;uo.component(ca.name,ca);var pa=(0,g.defineComponent)({name:"VxePulldown",props:{modelValue:Boolean,disabled:Boolean,placement:String,size:{type:String,default:()=>c.size},className:[String,Function],popupClassName:[String,Function],destroyOnClose:Boolean,transfer:Boolean},emits:["update:modelValue","hide-panel"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=Fo(e),i=(0,g.reactive)({inited:!1,panelIndex:0,panelStyle:null,panelPlacement:null,visiblePanel:!1,animatVisible:!1,isActivated:!1}),s=(0,g.ref)(),c=(0,g.ref)(),u=(0,g.ref)(),d={refElem:s},p={xID:l,props:e,context:t,reactData:i,getRefMaps:()=>d};let f={};const m=()=>{i.panelIndex<H()&&(i.panelIndex=j())},h=()=>i.visiblePanel,v=()=>(0,g.nextTick)().then((()=>{const{transfer:t,placement:o}=e,{panelIndex:n,visiblePanel:l}=i;if(l){const e=c.value,l=u.value;if(l&&e){const r=e.offsetHeight,a=e.offsetWidth,s=l.offsetHeight,c=l.offsetWidth,u=5,d={zIndex:n},{boundingTop:p,boundingLeft:f,visibleHeight:m,visibleWidth:h}=de(e);let g="bottom";if(t){let e=f,t=p+r;"top"===o?(g="top",t=p-s):o||(t+s+u>m&&(g="top",t=p-s),t<u&&(g="bottom",t=p+r)),e+c+u>h&&(e-=e+c+u-h),e<u&&(e=u),Object.assign(d,{left:`${e}px`,top:`${t}px`,minWidth:`${a}px`})}else"top"===o?(g="top",d.bottom=`${r}px`):o||p+r+s>m&&p-r-s>u&&(g="top",d.bottom=`${r}px`);i.panelStyle=d,i.panelPlacement=g}}return(0,g.nextTick)()}));let x;const b=()=>(i.inited||(i.inited=!0),new Promise((t=>{e.disabled?(0,g.nextTick)((()=>{t()})):(clearTimeout(x),i.isActivated=!0,i.animatVisible=!0,setTimeout((()=>{i.visiblePanel=!0,n("update:modelValue",!0),v(),setTimeout((()=>{t(v())}),40)}),10),m())}))),w=()=>(i.visiblePanel=!1,n("update:modelValue",!1),new Promise((e=>{i.animatVisible?x=window.setTimeout((()=>{i.animatVisible=!1,(0,g.nextTick)((()=>{e()}))}),350):(0,g.nextTick)((()=>{e()}))}))),C=()=>i.visiblePanel?w():b(),y=t=>{const{disabled:o}=e,{visiblePanel:n}=i,l=u.value;o||n&&(ce(t,l).flag?v():(w(),f.dispatchEvent("hide-panel",{},t)))},T=t=>{const{disabled:o}=e,{visiblePanel:n}=i,l=s.value,r=u.value;o||(i.isActivated=ce(t,l).flag||ce(t,r).flag,n&&!i.isActivated&&(w(),f.dispatchEvent("hide-panel",{},t)))},E=e=>{i.visiblePanel&&(i.isActivated=!1,w(),f.dispatchEvent("hide-panel",{},e))};f={dispatchEvent(e,t,o){n(e,Object.assign({$pulldown:p,$event:o},t))},isPanelVisible:h,togglePanel:C,showPanel:b,hidePanel:w},Object.assign(p,f),(0,g.watch)((()=>e.modelValue),(e=>{e?b():w()})),(0,g.nextTick)((()=>{yo.on(p,"mousewheel",y),yo.on(p,"mousedown",T),yo.on(p,"blur",E)})),(0,g.onUnmounted)((()=>{yo.off(p,"mousewheel"),yo.off(p,"mousedown"),yo.off(p,"blur")}));const S=()=>{const{className:t,popupClassName:n,destroyOnClose:l,transfer:d,disabled:f}=e,{inited:m,isActivated:h,animatVisible:v,visiblePanel:x,panelStyle:b,panelPlacement:w}=i,C=r.value,y=o.default,T=o.header,E=o.footer,S=o.dropdown;return(0,g.h)("div",{ref:s,class:["vxe-pulldown",t?a().isFunction(t)?t({$pulldown:p}):t:"",{[`size--${C}`]:C,"is--visivle":x,"is--disabled":f,"is--active":h}]},[(0,g.h)("div",{ref:c,class:"vxe-pulldown--content"},y?y({$pulldown:p}):[]),(0,g.h)(g.Teleport,{to:"body",disabled:!d||!m},[(0,g.h)("div",{ref:u,class:["vxe-table--ignore-clear vxe-pulldown--panel",n?a().isFunction(n)?n({$pulldown:p}):n:"",{[`size--${C}`]:C,"is--transfer":d,"animat--leave":v,"animat--enter":x}],placement:w,style:b},S?[(0,g.h)("div",{class:"vxe-pulldown--panel-wrapper"},!m||l&&!x&&!v?[]:[T?(0,g.h)("div",{class:"vxe-pulldown--panel-header"},T({$pulldown:p})):(0,g.createCommentVNode)(),(0,g.h)("div",{class:"vxe-pulldown--panel-body"},S({$pulldown:p})),E?(0,g.h)("div",{class:"vxe-pulldown--panel-footer"},E({$pulldown:p})):(0,g.createCommentVNode)()])]:[])])])};return p.renderVN=S,p},render(){return this.renderVN()}});const fa=Object.assign(pa,{install:function(e){e.component(pa.name,pa)}}),ma=fa;uo.component(pa.name,pa);const ha="body",ga={mini:3,small:2,medium:1};var va=(0,g.defineComponent)({name:"VxeTableBody",props:{tableData:Array,tableColumn:Array,fixedColumn:Array,fixedType:{type:String,default:null}},setup(e){const t=(0,g.inject)("$xetable",{}),o=(0,g.inject)("xesize",null),{xID:n,props:l,context:r,reactData:i,internalData:s}=t,{refTableHeader:u,refTableBody:d,refTableFooter:p,refTableLeftBody:f,refTableRightBody:m,refValidTooltip:h}=t.getRefMaps(),{computeEditOpts:v,computeMouseOpts:x,computeSYOpts:b,computeEmptyOpts:w,computeKeyboardOpts:C,computeTooltipOpts:y,computeRadioOpts:T,computeExpandOpts:E,computeTreeOpts:S,computeCheckboxOpts:k,computeValidOpts:R,computeRowOpts:O,computeColumnOpts:M}=t.getComputeMaps(),$=(0,g.ref)(),I=(0,g.ref)(),D=(0,g.ref)(),F=(0,g.ref)(),N=(0,g.ref)(),L=(0,g.ref)(),A=(0,g.ref)(),V=()=>{if(o){const e=o.value;if(e)return ga[e]||0}return 0},_=()=>{const{delayHover:e}=l,{lastScrollTime:t,_isResize:o}=i;return!!(o||t&&Date.now()<t+e)},j=(e,o)=>{let n=1;if(!e)return n;const l=S.value,r=l.children||l.childrenField,a=e[r];if(a&&t.isTreeExpandByRow(e))for(let t=0;t<a.length;t++)n+=j(a[t],o);return n},H=(e,t,o)=>{let n=1;return o&&(n=j(t[o-1],e)),i.rowHeight*n-(o?1:12-V())},B=e=>{const{row:o,column:n}=e,{treeConfig:r}=l,a=S.value,{slots:i,treeNode:c}=n,{fullAllDataRowIdData:u}=s,d=ye(t,o),p=u[d];let f=0,m=0,h=[];return p&&(f=p.level,m=p._index,h=p.items),i&&i.line?t.callSlot(i.line,e):r&&c&&(a.showLine||a.line)?[(0,g.h)("div",{class:"vxe-tree--line-wrapper"},[(0,g.h)("div",{class:"vxe-tree--line",style:{height:`${H(e,h,m)}px`,left:f*a.indent+(f?2-V():0)+16+"px"}})])]:[]},z=(e,o,n,r,c,u,d,p,f,m,h,x)=>{const{columnKey:w,height:C,showOverflow:T,cellClassName:E,cellStyle:S,align:$,spanMethod:I,mouseConfig:D,editConfig:F,editRules:N,tooltipConfig:L}=l,{tableData:A,overflowX:V,scrollYLoad:j,currentColumn:H,mergeList:z,editStore:W,isAllOverflow:q,validErrorMaps:U}=i,{afterFullData:X}=s,Y=R.value,K=k.value,Z=v.value,J=y.value,Q=O.value,ee=b.value,te=M.value,{type:oe,cellRender:ne,editRender:le,align:re,showOverflow:ae,className:ie,treeNode:ce}=f,{actived:ue}=W,{rHeight:de}=ee,{height:pe}=Q,fe=le||ne,me=fe?oo.renderer.get(fe.name):null,he=me?me.cellClassName:"",ge=me?me.cellStyle:"",ve=J.showAll,xe=t.getColumnIndex(f),be=t.getVTColumnIndex(f),we=P(le);let Ce=n?f.fixed!==n:f.fixed&&V;const ye=a().isUndefined(ae)||a().isNull(ae)?T:ae;let Te="ellipsis"===ye;const Ee="title"===ye,Se=!0===ye||"tooltip"===ye;let ke,Re=Ee||Se||Te;const Oe={},Me=re||$,$e=U[`${o}:${f.id}`],Ie=N&&Y.showMessage&&("default"===Y.message?C||A.length>1:"inline"===Y.message),De={colid:f.id},Fe={$table:t,$grid:t.xegrid,seq:e,rowid:o,row:c,rowIndex:u,$rowIndex:d,_rowIndex:p,column:f,columnIndex:xe,$columnIndex:m,_columnIndex:be,fixed:n,type:ha,isHidden:Ce,level:r,visibleData:X,data:A,items:x};if(j&&!Re&&(Te=Re=!0),(Ee||Se||ve||L)&&(Oe.onMouseenter=e=>{_()||(Ee?se(e.currentTarget,f):(Se||ve)&&t.triggerBodyTooltipEvent(e,Fe),t.dispatchEvent("cell-mouseenter",Object.assign({cell:e.currentTarget},Fe),e))}),(Se||ve||L)&&(Oe.onMouseleave=e=>{_()||((Se||ve)&&t.handleTargetLeaveEvent(e),t.dispatchEvent("cell-mouseleave",Object.assign({cell:e.currentTarget},Fe),e))}),(K.range||D)&&(Oe.onMousedown=e=>{t.triggerCellMousedownEvent(e,Fe)}),Oe.onClick=e=>{t.triggerCellClickEvent(e,Fe)},Oe.onDblclick=e=>{t.triggerCellDblclickEvent(e,Fe)},z.length){const e=Ve(z,p,be);if(e){const{rowspan:t,colspan:o}=e;if(!t||!o)return null;t>1&&(De.rowspan=t),o>1&&(De.colspan=o)}}else if(I){const{rowspan:e=1,colspan:t=1}=I(Fe)||{};if(!e||!t)return null;e>1&&(De.rowspan=e),t>1&&(De.colspan=t)}Ce&&z&&(De.colspan>1||De.rowspan>1)&&(Ce=!1),!Ce&&F&&(le||ne)&&(Z.showStatus||Z.showUpdateStatus)&&(ke=t.isUpdateByRow(c,f.field));const Ne=[];return Ce&&(T?q:T)?Ne.push((0,g.h)("div",{class:["vxe-cell",{"c--title":Ee,"c--tooltip":Se,"c--ellipsis":Te}],style:{maxHeight:Re&&(de||pe)?`${de||pe}px`:""}})):(Ne.push(...B(Fe),(0,g.h)("div",{class:["vxe-cell",{"c--title":Ee,"c--tooltip":Se,"c--ellipsis":Te}],style:{maxHeight:Re&&(de||pe)?`${de||pe}px`:""},title:Ee?t.getCellLabel(c,f):null},f.renderCell(Fe))),Ie&&$e&&Ne.push((0,g.h)("div",{class:"vxe-cell--valid-error-hint",style:$e.rule&&$e.rule.maxWidth?{width:`${$e.rule.maxWidth}px`}:null},[(0,g.h)("span",{class:"vxe-cell--valid-error-msg"},$e.content)]))),(0,g.h)("td",{class:["vxe-body--column",f.id,{[`col--${Me}`]:Me,[`col--${oe}`]:oe,"col--last":m===h.length-1,"col--tree-node":ce,"col--edit":we,"col--ellipsis":Re,"fixed--hidden":Ce,"col--dirty":ke,"col--active":F&&we&&ue.row===c&&(ue.column===f||"row"===Z.mode),"col--valid-error":!!$e,"col--current":H===f},G(he,Fe),G(ie,Fe),G(E,Fe)],key:w||te.useKey?f.id:m,...De,style:Object.assign({height:Re&&(de||pe)?`${de||pe}px`:""},a().isFunction(ge)?ge(Fe):ge,a().isFunction(S)?S(Fe):S),...Oe},Ne)},W=(e,o,n)=>{const{stripe:r,rowKey:c,highlightHoverRow:u,rowClassName:d,rowStyle:p,showOverflow:f,editConfig:m,treeConfig:h}=l,{hasFixedColumn:x,treeExpandedMaps:b,scrollYLoad:w,rowExpandedMaps:C,expandColumn:y,selectRadioRow:R,pendingRowMaps:M,pendingRowList:$}=i,{fullAllDataRowIdData:I}=s,D=k.value,F=T.value,N=S.value,L=v.value,A=O.value,{transform:P}=N,V=N.children||N.childrenField,j=[];return o.forEach(((l,i)=>{const s={};let v=i;v=t.getRowIndex(l),(A.isHover||u)&&(s.onMouseenter=e=>{_()||t.triggerHoverEvent(e,{row:l,rowIndex:v})},s.onMouseleave=()=>{_()||t.clearHoverRow()});const T=ye(t,l),S=I[T];let k=0,O=-1,H=0;S&&(k=S.level,O=S.seq,H=S._index);const B={$table:t,seq:O,rowid:T,fixed:e,type:ha,level:k,row:l,rowIndex:v,$rowIndex:i,_rowIndex:H},q=y&&!!C[T];let U=!1,X=[],Y=!1;if(m&&(Y=t.isInsertByRow(l)),!h||w||P||(X=l[V],U=X&&X.length&&!!b[T]),j.push((0,g.h)("tr",{class:["vxe-body--row",h?`row--level-${k}`:"",{"row--stripe":r&&(t.getVTRowIndex(l)+1)%2===0,"is--new":Y,"is--expand-row":q,"is--expand-tree":U,"row--new":Y&&(L.showStatus||L.showInsertStatus),"row--radio":F.highlight&&t.eqRow(R,l),"row--checked":D.highlight&&t.isCheckedByCheckboxRow(l),"row--pending":$.length&&!!M[T]},G(d,B)],rowid:T,style:p?a().isFunction(p)?p(B):p:null,key:c||A.useKey||h?T:i,...s},n.map(((t,r)=>z(O,T,e,k,l,v,i,H,t,r,n,o))))),q){const o=E.value,{height:r}=o,c={};r&&(c.height=`${r}px`),h&&(c.paddingLeft=k*N.indent+30+"px");const{showOverflow:u}=y,d=a().isUndefined(u)||a().isNull(u)?f:u,m={$table:t,seq:O,column:y,fixed:e,type:ha,level:k,row:l,rowIndex:v,$rowIndex:i,_rowIndex:H};j.push((0,g.h)("tr",{class:"vxe-body--expanded-row",key:`expand_${T}`,style:p?a().isFunction(p)?p(m):p:null,...s},[(0,g.h)("td",{class:{"vxe-body--expanded-column":1,"fixed--hidden":e&&!x,"col--ellipsis":d},colspan:n.length},[(0,g.h)("div",{class:{"vxe-body--expanded-cell":1,"is--ellipsis":r},style:c},[y.renderData(m)])])]))}U&&j.push(...W(e,X,n))})),j};let q;const U=(e,t,o,n)=>{(o||n)&&(o&&(xe(o),o.scrollTop=t),n&&(xe(n),n.scrollTop=t),clearTimeout(q),q=setTimeout((()=>{be(o),be(n),i.lastScrollTime=Date.now()}),300))},X=null,Y=o=>{const{fixedType:n}=e,{highlightHoverRow:r}=l,{scrollXLoad:a,scrollYLoad:c}=i,{elemStore:g,lastScrollTop:v,lastScrollLeft:x}=s,b=O.value,w=u.value,C=d.value,y=p.value,T=f.value,E=m.value,S=h.value,k=$.value,R=w?w.$el:null,M=y?y.$el:null,I=C.$el,D=T?T.$el:null,F=E?E.$el:null,N=g["main-body-ySpace"],L=N?N.value:null,A=g["main-body-xSpace"],P=A?A.value:null,V=c&&L?L.clientHeight:I.clientHeight,_=a&&P?P.clientWidth:I.clientWidth;let j=k.scrollTop;const H=I.scrollLeft,B=H!==x,z=j!==v;s.lastScrollTop=j,s.lastScrollLeft=H,i.lastScrollTime=Date.now(),(b.isHover||r)&&t.clearHoverRow(),D&&"left"===n?(j=D.scrollTop,U(n,j,I,F)):F&&"right"===n?(j=F.scrollTop,U(n,j,I,D)):(B&&(R&&(R.scrollLeft=I.scrollLeft),M&&(M.scrollLeft=I.scrollLeft)),(D||F)&&(t.checkScrolling(),z&&U(n,j,D,F))),a&&B&&t.triggerScrollXEvent(o),c&&z&&t.triggerScrollYEvent(o),null!==X&&clearTimeout(X),B&&S&&S.reactData.visible&&S.updatePlacement(),t.dispatchEvent("scroll",{type:ha,fixed:n,scrollTop:j,scrollLeft:H,scrollHeight:I.scrollHeight,scrollWidth:I.scrollWidth,bodyHeight:V,bodyWidth:_,isX:B,isY:z},o)};let K,Z=0,J=0,Q=0,ee=!1;const te=(o,n,l,r,a)=>{const{elemStore:c}=s,{scrollXLoad:u,scrollYLoad:p}=i,h=d.value,g=f.value,v=m.value,x=g?g.$el:null,b=v?v.$el:null,w=h.$el,C=c["main-body-ySpace"],y=C?C.value:null,T=c["main-body-xSpace"],E=T?T.value:null,S=p&&y?y.clientHeight:w.clientHeight,k=u&&E?E.clientWidth:w.clientWidth,R=ee===n?Math.max(0,Z-Q):0;ee=n,Z=Math.abs(n?l-R:l+R),J=0,Q=0,clearTimeout(K);const O=()=>{if(Q<Z){const{fixedType:l}=e;J=Math.max(5,Math.floor(1.5*J)),Q+=J,Q>Z&&(J-=Q-Z);const{scrollTop:i,clientHeight:s,scrollHeight:c}=w,u=i+J*(n?-1:1);w.scrollTop=u,x&&(x.scrollTop=u),b&&(b.scrollTop=u),(n?u<c-s:u>=0)&&(K=setTimeout(O,10)),t.dispatchEvent("scroll",{type:ha,fixed:l,scrollTop:w.scrollTop,scrollLeft:w.scrollLeft,scrollHeight:w.scrollHeight,scrollWidth:w.scrollWidth,bodyHeight:S,bodyWidth:k,isX:r,isY:a},o)}};O()},oe=e=>{const{deltaY:o,deltaX:n}=e,{highlightHoverRow:r}=l,{scrollYLoad:a}=i,{lastScrollTop:c,lastScrollLeft:u}=s,p=O.value,f=d.value,m=$.value,h=f.$el,g=o,v=n,x=g<0;if(x?m.scrollTop<=0:m.scrollTop>=m.scrollHeight-m.clientHeight)return;const b=m.scrollTop+g,w=h.scrollLeft+v,C=w!==u,y=b!==c;y&&(e.preventDefault(),s.lastScrollTop=b,s.lastScrollLeft=w,i.lastScrollTime=Date.now(),(p.isHover||r)&&t.clearHoverRow(),te(e,x,g,C,y),a&&t.triggerScrollYEvent(e))};(0,g.onMounted)((()=>{(0,g.nextTick)((()=>{const{fixedType:t}=e,{elemStore:o}=s,n=`${t||"main"}-body-`,l=$.value;o[`${n}wrapper`]=$,o[`${n}table`]=I,o[`${n}colgroup`]=D,o[`${n}list`]=F,o[`${n}xSpace`]=N,o[`${n}ySpace`]=L,o[`${n}emptyBlock`]=A,l&&(l.onscroll=Y,l._onscroll=Y)}))})),(0,g.onBeforeUnmount)((()=>{const e=$.value;clearTimeout(K),e&&(e._onscroll=null,e.onscroll=null)})),(0,g.onUnmounted)((()=>{const{fixedType:t}=e,{elemStore:o}=s,n=`${t||"main"}-body-`;o[`${n}wrapper`]=null,o[`${n}table`]=null,o[`${n}colgroup`]=null,o[`${n}list`]=null,o[`${n}xSpace`]=null,o[`${n}ySpace`]=null,o[`${n}emptyBlock`]=null}));const ne=()=>{let{fixedColumn:o,fixedType:a,tableColumn:u}=e;const{keyboardConfig:d,showOverflow:p,spanMethod:f,mouseConfig:m}=l,{tableData:h,mergeList:v,scrollYLoad:y,isAllOverflow:T}=i,{visibleColumn:E}=s,{slots:S}=r,k=b.value,R=w.value,O=C.value,M=x.value;let P;if(a&&(u=i.expandColumn||!y&&!(p?T:p)||v.length||f||d&&O.isMerge?E:o),S.empty)P=t.callSlot(S.empty,{$table:t,$grid:t.xegrid});else{const e=R.name?oo.renderer.get(R.name):null,o=e?e.renderEmpty:null;P=o?We(o(R,{$table:t})):l.emptyText||c.i18n("vxe.table.emptyText")}return(0,g.h)("div",{ref:$,class:["vxe-table--body-wrapper",a?`fixed-${a}--wrapper`:"body--wrapper"],xid:n,..."wheel"===k.mode?{onWheel:oe}:{}},[a?(0,g.createCommentVNode)():(0,g.h)("div",{ref:N,class:"vxe-body--x-space"}),(0,g.h)("div",{ref:L,class:"vxe-body--y-space"}),(0,g.h)("table",{ref:I,class:"vxe-table--body",xid:n,cellspacing:0,cellpadding:0,border:0},[(0,g.h)("colgroup",{ref:D},u.map(((e,t)=>(0,g.h)("col",{name:e.id,key:t})))),(0,g.h)("tbody",{ref:F},W(a,h,u))]),(0,g.h)("div",{class:"vxe-table--checkbox-range"}),m&&M.area?(0,g.h)("div",{class:"vxe-table--cell-area"},[(0,g.h)("span",{class:"vxe-table--cell-main-area"},M.extension?[(0,g.h)("span",{class:"vxe-table--cell-main-area-btn",onMousedown(e){t.triggerCellExtendMousedownEvent(e,{$table:t,fixed:a,type:ha})}})]:[]),(0,g.h)("span",{class:"vxe-table--cell-copy-area"}),(0,g.h)("span",{class:"vxe-table--cell-extend-area"}),(0,g.h)("span",{class:"vxe-table--cell-multi-area"}),(0,g.h)("span",{class:"vxe-table--cell-active-area"})]):null,a?null:(0,g.h)("div",{class:"vxe-table--empty-block",ref:A},[(0,g.h)("div",{class:"vxe-table--empty-content"},P)])])};return ne}});const xa=(e,t)=>{const o=[];return e.forEach((e=>{e.parentId=t?t.id:null,e.visible&&(e.children&&e.children.length&&e.children.some((e=>e.visible))?(o.push(e),o.push(...xa(e.children,e))):o.push(e))})),o},ba=e=>{let t=1;const o=(e,n)=>{if(n&&(e.level=n.level+1,t<e.level&&(t=e.level)),e.children&&e.children.length&&e.children.some((e=>e.visible))){let t=0;e.children.forEach((n=>{n.visible&&(o(n,e),t+=n.colSpan)})),e.colSpan=t}else e.colSpan=1};e.forEach((e=>{e.level=1,o(e)}));const n=[];for(let r=0;r<t;r++)n.push([]);const l=xa(e);return l.forEach((e=>{e.children&&e.children.length&&e.children.some((e=>e.visible))?e.rowSpan=1:e.rowSpan=t-e.level+1,n[e.level-1].push(e)})),n},wa="header";var Ca=(0,g.defineComponent)({name:"VxeTableHeader",props:{tableData:Array,tableColumn:Array,tableGroupColumn:Array,fixedColumn:Array,fixedType:{type:String,default:null}},setup(e){const t=(0,g.inject)("$xetable",{}),{xID:o,props:n,reactData:l,internalData:r}=t,{refElem:i,refTableBody:s,refLeftContainer:c,refRightContainer:u,refCellResizeBar:d}=t.getRefMaps(),{computeColumnOpts:p}=t.getComputeMaps(),f=(0,g.ref)([]),m=(0,g.ref)(),h=(0,g.ref)(),v=(0,g.ref)(),x=(0,g.ref)(),b=(0,g.ref)(),w=(0,g.ref)(),C=()=>{const{isGroup:t}=l;f.value=t?ba(e.tableGroupColumn):[]},y=(o,n)=>{const{column:a}=n,{fixedType:p}=e,f=s.value,h=c.value,g=u.value,v=d.value,{clientX:x}=o,b=m.value,w=o.target,C=n.cell=w.parentNode;let y=0;const T=f.$el,E=ue(w,b),S=w.clientWidth,k=Math.floor(S/2),R=Ie(n)-k;let O=E.left-C.clientWidth+S+R,M=E.left+k;const $=document.onmousemove,I=document.onmouseup,D="left"===p,F="right"===p,N=i.value;let L=0;if(D||F){const e=D?"nextElementSibling":"previousElementSibling";let t=C[e];while(t){if(ee(t,"fixed--hidden"))break;ee(t,"col--group")||(L+=t.offsetWidth),t=t[e]}F&&g&&(M=g.offsetLeft+L)}const A=function(e){e.stopPropagation(),e.preventDefault();const t=e.clientX-x;let o=M+t;const n=p?0:T.scrollLeft;D?o=Math.min(o,(g?g.offsetLeft:T.clientWidth)-L-R):F?(O=(h?h.clientWidth:0)+L+R,o=Math.min(o,M+C.clientWidth-R)):O=Math.max(T.scrollLeft,O),y=Math.max(o,O),v.style.left=y-n+"px"};l._isResize=!0,oe(N,"drag--resize"),v.style.display="block",document.onmousemove=A,document.onmouseup=function(e){document.onmousemove=$,document.onmouseup=I;const o=a.renderWidth+(F?M-y:y-M);a.resizeWidth=o,v.style.display="none",l._isResize=!1,r._lastResizeTime=Date.now(),t.analyColumnWidth(),t.recalculate(!0).then((()=>{t.saveCustomResizable(),t.updateCellAreas(),t.dispatchEvent("resizable-change",{...n,resizeWidth:o},e)})),te(N,"drag--resize")},A(o),t.closeMenu&&t.closeMenu()};(0,g.watch)((()=>e.tableColumn),C),(0,g.onMounted)((()=>{(0,g.nextTick)((()=>{const{fixedType:o}=e,{internalData:n}=t,{elemStore:l}=n,r=`${o||"main"}-header-`;l[`${r}wrapper`]=m,l[`${r}table`]=h,l[`${r}colgroup`]=v,l[`${r}list`]=x,l[`${r}xSpace`]=b,l[`${r}repair`]=w,C()}))})),(0,g.onUnmounted)((()=>{const{fixedType:o}=e,{internalData:n}=t,{elemStore:l}=n,r=`${o||"main"}-header-`;l[`${r}wrapper`]=null,l[`${r}table`]=null,l[`${r}colgroup`]=null,l[`${r}list`]=null,l[`${r}xSpace`]=null,l[`${r}repair`]=null}));const T=()=>{const{fixedType:i,fixedColumn:s,tableColumn:c}=e,{resizable:u,border:d,columnKey:C,headerRowClassName:T,headerCellClassName:E,headerRowStyle:S,headerCellStyle:k,showHeaderOverflow:R,headerAlign:O,align:M,mouseConfig:$}=n,{isGroup:I,currentColumn:D,scrollXLoad:F,overflowX:N,scrollbarWidth:L}=l,{visibleColumn:A}=r,P=p.value;let V=f.value,_=c;return I?_=A:(i&&(F||R)&&(_=s),V=[_]),(0,g.h)("div",{ref:m,class:["vxe-table--header-wrapper",i?`fixed-${i}--wrapper`:"body--wrapper"],xid:o},[i?(0,g.createCommentVNode)():(0,g.h)("div",{ref:b,class:"vxe-body--x-space"}),(0,g.h)("table",{ref:h,class:"vxe-table--header",xid:o,cellspacing:0,cellpadding:0,border:0},[(0,g.h)("colgroup",{ref:v},_.map(((e,t)=>(0,g.h)("col",{name:e.id,key:t}))).concat(L?[(0,g.h)("col",{name:"col_gutter"})]:[])),(0,g.h)("thead",{ref:x},V.map(((e,o)=>(0,g.h)("tr",{class:["vxe-header--row",T?a().isFunction(T)?T({$table:t,$rowIndex:o,fixed:i,type:wa}):T:""],style:S?a().isFunction(S)?S({$table:t,$rowIndex:o,fixed:i,type:wa}):S:null},e.map(((n,l)=>{const{type:r,showHeaderOverflow:s,headerAlign:c,align:p,headerClassName:f}=n,m=n.children&&n.children.length,h=i?n.fixed!==i&&!m:!!n.fixed&&N,v=a().isUndefined(s)||a().isNull(s)?R:s,x=c||p||O||M;let b="ellipsis"===v;const w="title"===v,T=!0===v||"tooltip"===v;let S=w||T||b;const I=n.filters&&n.filters.some((e=>e.checked)),L=t.getColumnIndex(n),A=t.getVTColumnIndex(n),V={$table:t,$grid:t.xegrid,$rowIndex:o,column:n,columnIndex:L,$columnIndex:l,_columnIndex:A,fixed:i,type:wa,isHidden:h,hasFilter:I},_={onClick:e=>t.triggerHeaderCellClickEvent(e,V),onDblclick:e=>t.triggerHeaderCellDblclickEvent(e,V)};return F&&!S&&(b=S=!0),$&&(_.onMousedown=e=>t.triggerHeaderCellMousedownEvent(e,V)),(0,g.h)("th",{class:["vxe-header--column",n.id,{[`col--${x}`]:x,[`col--${r}`]:r,"col--last":l===e.length-1,"col--fixed":n.fixed,"col--group":m,"col--ellipsis":S,"fixed--hidden":h,"is--sortable":n.sortable,"col--filter":!!n.filters,"is--filter-active":I,"col--current":D===n},f?a().isFunction(f)?f(V):f:"",E?a().isFunction(E)?E(V):E:""],colid:n.id,colspan:n.colSpan>1?n.colSpan:null,rowspan:n.rowSpan>1?n.rowSpan:null,style:k?a().isFunction(k)?k(V):k:null,..._,key:C||P.useKey||m?n.id:l},[(0,g.h)("div",{class:["vxe-cell",{"c--title":w,"c--tooltip":T,"c--ellipsis":b}]},n.renderHeader(V)),h||m||!(a().isBoolean(n.resizable)?n.resizable:P.resizable||u)?null:(0,g.h)("div",{class:["vxe-resizable",{"is--line":!d||"none"===d}],onMousedown:e=>y(e,V)})])})).concat(L?[(0,g.h)("th",{class:"vxe-header--gutter col--gutter"})]:[])))))]),(0,g.h)("div",{ref:w,class:"vxe-table--header-border-line"})])};return T}});const ya=Object.assign(Ca,{install(e){e.component(Ca.name,Ca)}});uo.component(Ca.name,Ca);var Ta=ya;const Ea="footer";function Sa(e,t,o){for(let n=0;n<e.length;n++){const{row:l,col:r,rowspan:a,colspan:i}=e[n];if(r>-1&&l>-1&&a&&i){if(l===t&&r===o)return{rowspan:a,colspan:i};if(t>=l&&t<l+a&&o>=r&&o<r+i)return{rowspan:0,colspan:0}}}}var ka=(0,g.defineComponent)({name:"VxeTableFooter",props:{footerTableData:{type:Array,default:()=>[]},tableColumn:{type:Array,default:()=>[]},fixedColumn:{type:Array,default:()=>[]},fixedType:{type:String,default:null}},setup(e){const t=(0,g.inject)("$xetable",{}),{xID:o,props:n,reactData:l,internalData:r}=t,{refTableHeader:i,refTableBody:s,refValidTooltip:c}=t.getRefMaps(),{computeTooltipOpts:u,computeColumnOpts:d}=t.getComputeMaps(),p=(0,g.ref)(),f=(0,g.ref)(),m=(0,g.ref)(),h=(0,g.ref)(),v=(0,g.ref)(),x=o=>{const{fixedType:n}=e,{scrollXLoad:a}=l,{lastScrollLeft:u}=r,d=c.value,f=i.value,m=s.value,h=f?f.$el:null,g=p.value,v=m.$el,x=g.scrollLeft,b=x!==u;r.lastScrollLeft=x,l.lastScrollTime=Date.now(),h&&(h.scrollLeft=x),v&&(v.scrollLeft=x),a&&b&&t.triggerScrollXEvent(o),b&&d&&d.reactData.visible&&d.updatePlacement(),t.dispatchEvent("scroll",{type:Ea,fixed:n,scrollTop:v.scrollTop,scrollLeft:x,isX:b,isY:!1},o)};(0,g.onMounted)((()=>{(0,g.nextTick)((()=>{const{fixedType:t}=e,{elemStore:o}=r,n=`${t||"main"}-footer-`;o[`${n}wrapper`]=p,o[`${n}table`]=f,o[`${n}colgroup`]=m,o[`${n}list`]=h,o[`${n}xSpace`]=v}))})),(0,g.onUnmounted)((()=>{const{fixedType:t}=e,{elemStore:o}=r,n=`${t||"main"}-footer-`;o[`${n}wrapper`]=null,o[`${n}table`]=null,o[`${n}colgroup`]=null,o[`${n}list`]=null,o[`${n}xSpace`]=null}));const b=()=>{let{fixedType:i,fixedColumn:s,tableColumn:c,footerTableData:b}=e;const{footerRowClassName:w,footerCellClassName:C,footerRowStyle:y,footerCellStyle:T,footerAlign:E,footerSpanMethod:S,align:k,columnKey:R,showFooterOverflow:O}=n,{visibleColumn:M}=r,{scrollXLoad:$,overflowX:I,scrollbarWidth:D,currentColumn:F,mergeFooterList:N}=l,L=u.value,A=d.value;return i&&(c=l.expandColumn||!$&&!O||N.length&&S?M:s),(0,g.h)("div",{ref:p,class:["vxe-table--footer-wrapper",i?`fixed-${i}--wrapper`:"body--wrapper"],xid:o,onScroll:x},[i?(0,g.createCommentVNode)():(0,g.h)("div",{ref:v,class:"vxe-body--x-space"}),(0,g.h)("table",{ref:f,class:"vxe-table--footer",xid:o,cellspacing:0,cellpadding:0,border:0},[(0,g.h)("colgroup",{ref:m},c.map(((e,t)=>(0,g.h)("col",{name:e.id,key:t}))).concat(D?[(0,g.h)("col",{name:"col_gutter"})]:[])),(0,g.h)("tfoot",{ref:h},b.map(((e,o)=>{const n=o;return(0,g.h)("tr",{class:["vxe-footer--row",w?a().isFunction(w)?w({$table:t,_rowIndex:o,$rowIndex:n,fixed:i,type:Ea}):w:""],style:y?a().isFunction(y)?y({$table:t,_rowIndex:o,$rowIndex:n,fixed:i,type:Ea}):y:null},c.map(((l,r)=>{const{type:s,showFooterOverflow:u,footerAlign:d,align:p,footerClassName:f}=l,m=L.showAll,h=l.children&&l.children.length,v=i?l.fixed!==i&&!h:l.fixed&&I,x=a().isUndefined(u)||a().isNull(u)?O:u,w=d||p||E||k;let y="ellipsis"===x;const M="title"===x,D=!0===x||"tooltip"===x;let P=M||D||y;const V={colid:l.id},_={},j=t.getColumnIndex(l),H=t.getVTColumnIndex(l),B=H,z={$table:t,$grid:t.xegrid,_rowIndex:o,$rowIndex:n,column:l,columnIndex:j,$columnIndex:r,_columnIndex:H,itemIndex:B,items:e,fixed:i,type:Ea,data:b};if($&&!P&&(y=P=!0),(M||D||m)&&(_.onMouseenter=e=>{M?se(e.currentTarget,l):(D||m)&&t.triggerFooterTooltipEvent(e,z)}),(D||m)&&(_.onMouseleave=e=>{(D||m)&&t.handleTargetLeaveEvent(e)}),_.onClick=e=>{t.dispatchEvent("footer-cell-click",Object.assign({cell:e.currentTarget},z),e)},_.onDblclick=e=>{t.dispatchEvent("footer-cell-dblclick",Object.assign({cell:e.currentTarget},z),e)},N.length){const e=Sa(N,o,H);if(e){const{rowspan:t,colspan:o}=e;if(!t||!o)return null;t>1&&(V.rowspan=t),o>1&&(V.colspan=o)}}else if(S){const{rowspan:e=1,colspan:t=1}=S(z)||{};if(!e||!t)return null;e>1&&(V.rowspan=e),t>1&&(V.colspan=t)}return(0,g.h)("td",{class:["vxe-footer--column",l.id,{[`col--${w}`]:w,[`col--${s}`]:s,"col--last":r===c.length-1,"fixed--hidden":v,"col--ellipsis":P,"col--current":F===l},G(f,z),G(C,z)],...V,style:T?a().isFunction(T)?T(z):T:null,..._,key:R||A.useKey?l.id:r},[(0,g.h)("div",{class:["vxe-cell",{"c--title":M,"c--tooltip":D,"c--ellipsis":y}]},l.renderFooter(z))])})).concat(D?[(0,g.h)("td",{class:"vxe-footer--gutter col--gutter"})]:[]))})))])])};return b}});const Ra=Object.assign(ka,{install(e){e.component(ka.name,ka)}});uo.component(ka.name,ka);var Oa=Ra;const Ma=Y["-webkit"]&&!Y.edge,$a="VXE_TABLE_CUSTOM_COLUMN_WIDTH",Ia="VXE_TABLE_CUSTOM_COLUMN_VISIBLE",Da="VXE_TABLE_CUSTOM_COLUMN_FIXED",Fa="VXE_TABLE_CUSTOM_COLUMN_ORDER";var Na=(0,g.defineComponent)({name:"VxeTable",props:xl,emits:bl,setup(e,t){const{slots:o,emit:n}=t,l=oo.tooltip,r=a().uniqueId(),i=Fo(e),s=(0,g.getCurrentInstance)(),u=(0,g.reactive)({staticColumns:[],tableGroupColumn:[],tableColumn:[],tableData:[],scrollXLoad:!1,scrollYLoad:!1,overflowY:!0,overflowX:!1,scrollbarWidth:0,scrollbarHeight:0,lastScrollTime:0,rowHeight:0,parentHeight:0,isGroup:!1,isAllOverflow:!1,isAllSelected:!1,isIndeterminate:!1,selectCheckboxMaps:{},currentRow:null,currentColumn:null,selectRadioRow:null,footerTableData:[],expandColumn:null,treeNodeColumn:null,hasFixedColumn:!1,rowExpandedMaps:{},rowExpandLazyLoadedMaps:{},treeExpandedMaps:{},treeExpandLazyLoadedMaps:{},treeIndeterminateMaps:{},mergeList:[],mergeFooterList:[],upDataFlag:0,reColumnFlag:0,pendingRowMaps:{},pendingRowList:[],initStore:{filter:!1,import:!1,export:!1},filterStore:{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1,maxHeight:null},columnStore:{leftList:[],centerList:[],rightList:[],resizeList:[],pxList:[],pxMinList:[],scaleList:[],scaleMinList:[],autoList:[]},ctxMenuStore:{selected:null,visible:!1,showChild:!1,selectChild:null,list:[],style:null},editStore:{indexs:{columns:[]},titles:{columns:[]},selected:{row:null,column:null},copyed:{cut:!1,rows:[],columns:[]},actived:{row:null,column:null},insertMaps:{},removeMaps:{}},tooltipStore:{row:null,column:null,content:null,visible:!1,currOpts:null},validStore:{visible:!1},validErrorMaps:{},importStore:{inited:!1,file:null,type:"",modeList:[],typeList:[],filename:"",visible:!1},importParams:{mode:"",types:null,message:!0},exportStore:{inited:!1,name:"",modeList:[],typeList:[],columns:[],isPrint:!1,hasFooter:!1,hasMerge:!1,hasTree:!1,hasColgroup:!1,visible:!1},exportParams:{filename:"",sheetName:"",mode:"",type:"",isColgroup:!1,isMerge:!1,isAllExpand:!1,useStyle:!1,original:!1,message:!0,isHeader:!1,isFooter:!1},scrollVMLoading:!1,_isResize:!1}),d={tZindex:0,elemStore:{},scrollXStore:{offsetSize:0,visibleSize:0,startIndex:0,endIndex:0},scrollYStore:{rowHeight:0,offsetSize:0,visibleSize:0,startIndex:0,endIndex:0},tableWidth:0,tableHeight:0,headerHeight:0,footerHeight:0,customHeight:0,customMinHeight:0,customMaxHeight:0,hoverRow:null,lastScrollLeft:0,lastScrollTop:0,radioReserveRow:null,checkboxReserveRowMap:{},rowExpandedReserveRowMap:{},treeExpandedReserveRowMap:{},treeIndeterminateRowMaps:{},tableFullData:[],afterFullData:[],afterTreeFullData:[],afterFullRowMaps:{},tableFullTreeData:[],tableSynchData:[],tableSourceData:[],collectColumn:[],tableFullColumn:[],visibleColumn:[],fullAllDataRowIdData:{},sourceDataRowIdData:{},fullDataRowIdData:{},fullColumnIdData:{},fullColumnFieldData:{},inited:!1,tooltipTimeout:null,initStatus:!1,isActivated:!1};let m={},h={};const v=(0,g.ref)(),x=(0,g.ref)(),b=(0,g.ref)(),w=(0,g.ref)(),C=(0,g.ref)(),y=(0,g.ref)(),T=(0,g.ref)(),E=(0,g.ref)(),S=(0,g.ref)(),k=(0,g.ref)(),R=(0,g.ref)(),O=(0,g.ref)(),M=(0,g.ref)(),$=(0,g.ref)(),I=(0,g.ref)(),D=(0,g.ref)(),F=(0,g.ref)(),N=(0,g.ref)(),L=(0,g.ref)(),A=(0,g.inject)("$xegrid",null);let V;const _=(0,g.computed)((()=>Object.assign({},c.table.validConfig,e.validConfig))),U=(0,g.computed)((()=>Object.assign({},c.table.scrollX,e.scrollX))),X=(0,g.computed)((()=>Object.assign({},c.table.scrollY,e.scrollY))),G=(0,g.computed)((()=>({default:48,medium:44,small:40,mini:36}))),K=(0,g.computed)((()=>Object.assign({},c.table.columnConfig,e.columnConfig))),Z=(0,g.computed)((()=>Object.assign({},c.table.rowConfig,e.rowConfig))),ne=(0,g.computed)((()=>Object.assign({},c.table.resizeConfig,e.resizeConfig))),le=(0,g.computed)((()=>Object.assign({},c.table.resizableConfig,e.resizableConfig))),se=(0,g.computed)((()=>Object.assign({startIndex:0},c.table.seqConfig,e.seqConfig))),ue=(0,g.computed)((()=>Object.assign({},c.table.radioConfig,e.radioConfig))),de=(0,g.computed)((()=>Object.assign({},c.table.checkboxConfig,e.checkboxConfig)));let pe=(0,g.ref)();pe=(0,g.computed)((()=>Object.assign({},c.tooltip,c.table.tooltipConfig,e.tooltipConfig)));const fe=(0,g.computed)((()=>{const{tooltipStore:e}=u,t=pe.value;return{...t,...e.currOpts}})),me=(0,g.computed)((()=>{const e=pe.value;return Object.assign({isArrow:!1},e)})),he=(0,g.computed)((()=>Object.assign({},c.table.editConfig,e.editConfig))),xe=(0,g.computed)((()=>Object.assign({orders:["asc","desc",null]},c.table.sortConfig,e.sortConfig))),Ee=(0,g.computed)((()=>Object.assign({},c.table.filterConfig,e.filterConfig))),Se=(0,g.computed)((()=>Object.assign({},c.table.mouseConfig,e.mouseConfig))),ke=(0,g.computed)((()=>Object.assign({},c.table.areaConfig,e.areaConfig))),Re=(0,g.computed)((()=>Object.assign({},c.table.keyboardConfig,e.keyboardConfig))),Ie=(0,g.computed)((()=>Object.assign({},c.table.clipConfig,e.clipConfig))),De=(0,g.computed)((()=>Object.assign({},c.table.fnrConfig,e.fnrConfig))),Fe=(0,g.computed)((()=>Object.assign({},c.table.menuConfig,e.menuConfig))),Ne=(0,g.computed)((()=>{const e=Fe.value,t=e.header;return t&&t.options?t.options:[]})),Le=(0,g.computed)((()=>{const e=Fe.value,t=e.body;return t&&t.options?t.options:[]})),Ae=(0,g.computed)((()=>{const e=Fe.value,t=e.footer;return t&&t.options?t.options:[]})),Ve=(0,g.computed)((()=>{const t=Fe.value,o=Ne.value,n=Le.value,l=Ae.value;return!!(e.menuConfig&&P(t)&&(o.length||n.length||l.length))})),_e=(0,g.computed)((()=>{const{ctxMenuStore:e}=u,t=[];return e.list.forEach((e=>{e.forEach((e=>{t.push(e)}))})),t})),ze=(0,g.computed)((()=>Object.assign({},c.table.exportConfig,e.exportConfig))),qe=(0,g.computed)((()=>Object.assign({},c.table.importConfig,e.importConfig))),Ue=(0,g.computed)((()=>Object.assign({},c.table.printConfig,e.printConfig))),Xe=(0,g.computed)((()=>Object.assign({},c.table.expandConfig,e.expandConfig))),Ye=(0,g.computed)((()=>Object.assign({},c.table.treeConfig,e.treeConfig))),Ge=(0,g.computed)((()=>Object.assign({},c.table.emptyRender,e.emptyRender))),Ke=(0,g.computed)((()=>Object.assign({},c.table.loadingConfig,e.loadingConfig))),Ze=(0,g.computed)((()=>e.border?Math.max(2,Math.ceil(u.scrollbarWidth/u.tableColumn.length)):1)),Je=(0,g.computed)((()=>Object.assign({},c.table.customConfig,e.customConfig))),Qe=(0,g.computed)((()=>{const{tableFullColumn:e}=d;let t=0;return e.forEach((e=>{e.fixed&&t++})),t})),et=(0,g.computed)((()=>{const e=Qe.value,t=K.value,{maxFixedSize:o}=t;return!!o&&e>=o})),tt=(0,g.computed)((()=>{const{border:t}=e;return!0===t?"full":t||"default"})),ot=(0,g.computed)((()=>{const{treeConfig:t}=e,{tableData:o}=u,{tableFullData:n}=d,l=de.value,{strict:r,checkMethod:a}=l;return!!r&&(!o.length&&!n.length||!!a&&n.every((e=>!a({row:e}))))})),nt={refElem:v,refTooltip:x,refValidTooltip:w,refTableFilter:C,refTableMenu:y,refTableHeader:T,refTableBody:E,refTableFooter:S,refTableLeftHeader:k,refTableLeftBody:R,refTableLeftFooter:O,refTableRightHeader:M,refTableRightBody:$,refTableRightFooter:I,refLeftContainer:D,refRightContainer:F,refCellResizeBar:N},lt={computeSize:i,computeValidOpts:_,computeSXOpts:U,computeSYOpts:X,computeColumnOpts:K,computeRowOpts:Z,computeResizeleOpts:ne,computeResizableOpts:le,computeSeqOpts:se,computeRadioOpts:ue,computeCheckboxOpts:de,computeTooltipOpts:pe,computeEditOpts:he,computeSortOpts:xe,computeFilterOpts:Ee,computeMouseOpts:Se,computeAreaOpts:ke,computeKeyboardOpts:Re,computeClipOpts:Ie,computeFNROpts:De,computeHeaderMenu:Ne,computeBodyMenu:Le,computeFooterMenu:Ae,computeIsMenu:Ve,computeMenuOpts:Fe,computeExportOpts:ze,computeImportOpts:qe,computePrintOpts:Ue,computeExpandOpts:Xe,computeTreeOpts:Ye,computeEmptyOpts:Ge,computeLoadingOpts:Ke,computeCustomOpts:Je,computeFixedColumnSize:Qe,computeIsMaxFixedColumn:et,computeIsAllCheckboxDisabled:ot},rt={xID:r,props:e,context:t,instance:s,reactData:u,internalData:d,getRefMaps:()=>nt,getComputeMaps:()=>lt,xegrid:A},at=(e,t,o)=>{const n=a().get(e,o),l=a().get(t,o);return!(!q(n)||!q(l))||(a().isString(n)||a().isNumber(n)?""+n===""+l:a().isEqual(n,l))},it=e=>{const t=xe.value,{orders:o}=t,n=e.order||null,l=o.indexOf(n)+1;return o[l<o.length?l:0]},st=e=>{const t=c.version,o=a().toStringJSON(localStorage.getItem(e)||"");return o&&o._v===t?o:{_v:t}},ct=e=>{const{fullAllDataRowIdData:t}=d,o={};return a().each(e,((e,n)=>{t[n]&&(o[n]=e)})),o},ut=e=>{const{fullDataRowIdData:t}=d,o=[];return a().each(e,((e,n)=>{t[n]&&-1===rt.findRowIndexOf(o,t[n].row)&&o.push(t[n].row)})),o},dt=()=>{const{visibleColumn:e}=d,t=E.value,o=t?t.$el:null;if(o){const{scrollLeft:t,clientWidth:n}=o,l=t+n;let r=-1,a=0,i=0;for(let o=0,s=e.length;o<s;o++)if(a+=e[o].renderWidth,-1===r&&t<a&&(r=o),r>=0&&(i++,a>l))break;return{toVisibleIndex:Math.max(0,r),visibleSize:Math.max(8,i)}}return{toVisibleIndex:0,visibleSize:8}},pt=()=>{const e=T.value,t=E.value,o=t?t.$el:null,n=i.value,l=G.value;if(o){const t=e?e.$el:null;let r,a=0;r=o.querySelector("tr"),!r&&t&&(r=t.querySelector("tr")),r&&(a=r.clientHeight),a||(a=l[n||"default"]);const i=Math.max(8,Math.ceil(o.clientHeight/a)+2);return{rowHeight:a,visibleSize:i}}return{rowHeight:0,visibleSize:8}},ft=(e,t,o)=>{for(let n=0,l=e.length;n<l;n++){const l=e[n],{startIndex:r,endIndex:a}=t,i=l[o],s=l[o+"span"],c=i+s;i<r&&r<c&&(t.startIndex=i),i<a&&a<c&&(t.endIndex=c),t.startIndex===r&&t.endIndex===a||(n=-1)}},mt=(t,o,n)=>{if(t){const{treeConfig:l}=e,{visibleColumn:r}=d;a().isArray(t)||(t=[t]),l&&t.length&&f("vxe.error.noTree",["merge-cells | merge-footer-items"]),t.forEach((e=>{let{row:t,col:l,rowspan:i,colspan:s}=e;if(n&&a().isNumber(t)&&(t=n[t]),a().isNumber(l)&&(l=r[l]),(n?t:a().isNumber(t))&&l&&(i||s)&&(i=a().toNumber(i)||1,s=a().toNumber(s)||1,i>1||s>1)){const e=a().findIndexOf(o,(e=>(e._row===t||ye(rt,e._row)===ye(rt,t))&&(e._col.id===l||e._col.id===l.id))),r=o[e];if(r)r.rowspan=i,r.colspan=s,r._rowspan=i,r._colspan=s;else{const e=n?rt.findRowIndexOf(n,t):t,r=m.getVTColumnIndex(l);o.push({row:e,col:r,rowspan:i,colspan:s,_row:t,_col:l,_rowspan:i,_colspan:s})}}}))}},ht=(t,o,n)=>{const l=[];if(t){const{treeConfig:r}=e,{visibleColumn:i}=d;a().isArray(t)||(t=[t]),r&&t.length&&f("vxe.error.noTree",["merge-cells | merge-footer-items"]),t.forEach((e=>{let{row:t,col:r}=e;n&&a().isNumber(t)&&(t=n[t]),a().isNumber(r)&&(r=i[r]);const s=a().findIndexOf(o,(e=>(e._row===t||ye(rt,e._row)===ye(rt,t))&&(e._col.id===r||e._col.id===r.id)));if(s>-1){const e=o.splice(s,1);l.push(e[0])}}))}return l},gt=()=>{const{tableFullColumn:e}=d;e.forEach((e=>{e.order=null}))},vt=t=>{const{parentHeight:o}=u,n=e[t];let l=0;if(n)if("auto"===n)l=o;else{const e=rt.getExcludeHeight();l=Q(n)?Math.floor((a().toInteger(n)||1)/100*o):a().toNumber(n),l=Math.max(40,l-e)}return l},xt=()=>{const{id:t,customConfig:o}=e,{collectColumn:n}=d,l=Je.value,{storage:r}=l,i=!0===r||r&&r.resizable,s=!0===r||r&&r.visible,c=!0===r||r&&r.fixed,u=!0===r||r&&r.order;if(o&&(i||s||c||u)){const e={};if(!t)return void f("vxe.error.reqProp",["id"]);if(i){const o=st($a)[t];o&&a().each(o,((t,o)=>{e[o]={resizeWidth:t}}))}if(c){const o=st(Da)[t];if(o){const t=o.split(",");t.forEach((t=>{const[o,n]=t.split("|");e[o]?e[o].fixed=n:e[o]={fixed:n}}))}}if(u){st(Fa)[t]}if(s){const o=st(Ia)[t];if(o){const t=o.split("|"),n=t[0]?t[0].split(","):[],l=t[1]?t[1].split(","):[];n.forEach((t=>{e[t]?e[t].visible=!1:e[t]={visible:!1}})),l.forEach((t=>{e[t]?e[t].visible=!0:e[t]={visible:!0}}))}}const o={};a().eachTree(n,(e=>{const t=e.getKey();t&&(o[t]=e)})),a().each(e,(({visible:e,resizeWidth:t,fixed:n,order:l},r)=>{const i=o[r];i&&(a().isNumber(t)&&(i.resizeWidth=t),a().isBoolean(e)&&(i.visible=e),n&&(i.fixed=n),l&&(i.customOrder=l))}))}},bt=()=>{const{tableFullColumn:t,collectColumn:o}=d,n=d.fullColumnIdData={},l=d.fullColumnFieldData={},r=Se.value,i=K.value,s=Z.value,c=o.some(B);let m,h,g,v,x,b,w=!!e.showOverflow;const C=(e,t,o,r,a)=>{const{id:i,field:s,fixed:c,type:u,treeNode:d}=e,C={column:e,colid:i,index:t,items:o,parent:a};s&&(l[s]&&p("vxe.error.colRepet",["field",s]),l[s]=C),!b&&c&&(b=c),x||"html"!==u||(x=e),d?(h&&p("vxe.error.colRepet",["tree-node",d]),h||(h=e)):"expand"===u&&(m&&p("vxe.error.colRepet",["type",u]),m||(m=e)),"checkbox"===u?(g&&p("vxe.error.colRepet",["type",u]),g||(g=e)):"radio"===u&&(v&&p("vxe.error.colRepet",["type",u]),v||(v=e)),w&&!1===e.showOverflow&&(w=!1),n[i]&&f("vxe.error.colRepet",["colId",i]),n[i]=C};c?a().eachTree(o,((e,t,o,n,l,r)=>{e.level=r.length,C(e,t,o,n,l)})):t.forEach(C),m&&r.area&&f("vxe.error.errConflicts",["mouse-config.area","column.type=expand"]),x&&(i.useKey||f("vxe.error.reqProp",["column-config.useKey","column.type=html"]),s.useKey||f("vxe.error.reqProp",["row-config.useKey","column.type=html"])),u.isGroup=c,u.treeNodeColumn=h,u.expandColumn=m,u.isAllOverflow=w},wt=()=>{d.customHeight=vt("height"),d.customMinHeight=vt("minHeight"),d.customMaxHeight=vt("maxHeight")},Ct=()=>{const t=T.value,o=E.value,n=S.value,l=o?o.$el:null,r=t?t.$el:null,i=n?n.$el:null;if(!l)return;let s=0;const c=40,p=l.clientWidth-1;let f=p,m=f/100;const{fit:v}=e,{columnStore:x}=u,{resizeList:b,pxMinList:w,pxList:C,scaleList:y,scaleMinList:k,autoList:R}=x;if(w.forEach((e=>{const t=a().toInteger(e.minWidth);s+=t,e.renderWidth=t})),k.forEach((e=>{const t=Math.floor(a().toInteger(e.minWidth)*m);s+=t,e.renderWidth=t})),y.forEach((e=>{const t=Math.floor(a().toInteger(e.width)*m);s+=t,e.renderWidth=t})),C.forEach((e=>{const t=a().toInteger(e.width);s+=t,e.renderWidth=t})),b.forEach((e=>{const t=a().toInteger(e.resizeWidth);s+=t,e.renderWidth=t})),f-=s,m=f>0?Math.floor(f/(k.length+w.length+R.length)):0,v?f>0&&k.concat(w).forEach((e=>{s+=m,e.renderWidth+=m})):m=c,R.forEach((e=>{const t=Math.max(m,c);e.renderWidth=t,s+=t})),v){const e=y.concat(k).concat(w).concat(R);let t=e.length-1;if(t>0){let o=p-s;if(o>0){while(o>0&&t>=0)o--,e[t--].renderWidth++;s=p}}}const O=l.offsetHeight,M=l.scrollHeight>l.clientHeight;let $=0;M&&($=Math.max(l.offsetWidth-l.clientWidth,0)),u.scrollbarWidth=$,u.overflowY=M,d.tableWidth=s,d.tableHeight=O;let I=0;r&&(I=r.clientHeight,(0,g.nextTick)((()=>{r&&l&&r.scrollLeft!==l.scrollLeft&&(r.scrollLeft=l.scrollLeft)}))),d.headerHeight=I;let D=!1,F=0,N=0;i?(F=i.offsetHeight,D=s>i.clientWidth,D&&(N=Math.max(F-i.clientHeight,0))):(D=s>p,D&&(N=Math.max(O-l.clientHeight,0))),d.footerHeight=F,u.overflowX=D,u.scrollbarHeight=N,wt(),u.parentHeight=Math.max(d.headerHeight+F+20,h.getParentHeight()),D&&h.checkScrolling()},yt=e=>{const{sortBy:t,sortType:o}=e;return n=>{let l;return l=t?a().isFunction(t)?t({row:n,column:e}):a().get(n,t):h.getCellLabel(n,e),o&&"auto"!==o?"number"===o?a().toNumber(l):"string"===o?a().toValueString(l):l:isNaN(l)?l:a().toNumber(l)}},Tt=()=>{const{treeConfig:t}=e,{afterFullData:o,fullDataRowIdData:n,fullAllDataRowIdData:l}=d,{afterTreeFullData:r}=d,i=Ye.value,s=i.children||i.childrenField,c={};t?a().eachTree(r,((e,t,o,r)=>{const a=ye(rt,e),i=l[a],s=r.map(((e,t)=>t%2===0?Number(e)+1:".")).join("");if(i)i.seq=s,i._index=t;else{const o={row:e,rowid:a,seq:s,index:-1,$index:-1,_index:t,items:[],parent:null,level:0};l[a]=o,n[a]=o}c[a]=e}),{children:i.transform?i.mapChildrenField:s}):o.forEach(((e,t)=>{const o=ye(rt,e),r=l[o],a=t+1;if(r)r.seq=a,r._index=t;else{const r={row:e,rowid:o,seq:a,index:-1,$index:-1,_index:t,items:[],parent:null,level:0};l[o]=r,n[o]=r}c[o]=e})),d.afterFullRowMaps=c},Et=()=>{const{treeConfig:t}=e,{treeExpandedMaps:o}=u,n=Ye.value;if(t&&n.transform){const e=[],t={};return a().eachTree(d.afterTreeFullData,((n,l,r,a,i)=>{const s=ye(rt,n),c=ye(rt,i);(!i||t[c]&&o[c])&&(t[s]=1,e.push(n))}),{children:n.mapChildrenField}),d.afterFullData=e,to(e),e}return d.afterFullData},St=()=>{const{treeConfig:t}=e,{tableFullColumn:o,tableFullData:n,tableFullTreeData:l}=d,r=Ee.value,i=xe.value,s=Ye.value,{transform:c}=s,{remote:u,filterMethod:p}=r,{remote:f,sortMethod:m,multiple:h,chronological:g}=i;let v=[],x=[];if(u&&f)t&&c?(x=a().searchTree(l,(()=>!0),{...s,original:!0}),v=x):(v=t?l.slice(0):n.slice(0),x=v);else{const e=[];let r=[];if(o.forEach((t=>{const{field:o,sortable:n,order:l,filters:a}=t;if(!u&&a&&a.length){const o=[],n=[];a.forEach((e=>{e.checked&&(n.push(e),o.push(e.value))})),n.length&&e.push({column:t,valueList:o,itemList:n})}!f&&n&&l&&r.push({column:t,field:o,property:o,order:l,sortTime:t.sortTime})})),h&&g&&r.length>1&&(r=a().orderBy(r,"sortTime")),!u&&e.length){const o=t=>e.every((({column:e,valueList:o,itemList:n})=>{const{filterMethod:l,filterRender:r}=e,i=r?oo.renderer.get(r.name):null,s=i?i.filterMethod:null,c=i?i.defaultFilterMethod:null,u=Me(t,e);return l?n.some((o=>l({value:o.value,option:o,cellValue:u,row:t,column:e,$table:rt}))):s?n.some((o=>s({value:o.value,option:o,cellValue:u,row:t,column:e,$table:rt}))):p?p({options:n,values:o,cellValue:u,row:t,column:e}):c?n.some((o=>c({value:o.value,option:o,cellValue:u,row:t,column:e,$table:rt}))):o.indexOf(a().get(t,e.field))>-1}));t&&c?(x=a().searchTree(l,o,{...s,original:!0}),v=x):(v=t?l.filter(o):n.filter(o),x=v)}else t&&c?(x=a().searchTree(l,(()=>!0),{...s,original:!0}),v=x):(v=t?l.slice(0):n.slice(0),x=v);if(!f&&r.length)if(t&&c){if(m){const e=m({data:x,sortList:r,$table:rt});x=a().isArray(e)?e:x}else x=a().orderBy(x,r.map((({column:e,order:t})=>[yt(e),t])));v=x}else{if(m){const e=m({data:v,sortList:r,$table:rt});v=a().isArray(e)?e:v}else v=a().orderBy(v,r.map((({column:e,order:t})=>[yt(e),t])));x=v}}d.afterFullData=v,d.afterTreeFullData=x,Tt()},kt=()=>{const{border:t,showFooter:o,showOverflow:n,showHeaderOverflow:l,showFooterOverflow:r,mouseConfig:i,spanMethod:s,footerSpanMethod:c,keyboardConfig:p}=e,{isGroup:f,currentRow:h,tableColumn:v,scrollXLoad:x,scrollYLoad:b,scrollbarWidth:w,scrollbarHeight:C,columnStore:y,editStore:T,mergeList:E,mergeFooterList:S,isAllOverflow:k}=u;let{visibleColumn:R,fullColumnIdData:O,tableHeight:M,tableWidth:$,headerHeight:I,footerHeight:N,elemStore:A,customHeight:P,customMinHeight:V,customMaxHeight:_}=d;const j=["main","left","right"],H=L.value,B=Ze.value,z=Se.value,W=Re.value,q=A["main-body-wrapper"],U=q?q.value:null;return H&&(H.style.top=`${I}px`,H.style.height=U?U.offsetHeight-C+"px":""),P>0&&o&&(P+=C),j.forEach(((e,i)=>{const d=i>0?e:"",h=["header","body","footer"],g="left"===d;let T,L=[];d&&(L=g?y.leftList:y.rightList,T=g?D.value:F.value),h.forEach((i=>{const h=A[`${e}-${i}-wrapper`],y=h?h.value:null,D=A[`${e}-${i}-table`],F=D?D.value:null;if("header"===i){let o=$,n=v;f?n=R:d&&(x||l)&&(n=L),o=n.reduce(((e,t)=>e+t.renderWidth),0),F&&(F.style.width=o?`${o+w}px`:"");const r=A[`${e}-${i}-repair`],s=r?r.value:null;s&&(s.style.width=`${$}px`);const c=A[`${e}-${i}-list`],u=c?c.value:null;f&&u&&a().arrayEach(u.querySelectorAll(".col--group"),(e=>{const o=m.getColumnNode(e);if(o){const n=o.item,{showHeaderOverflow:r}=n,i=a().isBoolean(r)?r:l,s="ellipsis"===i,c="title"===i,u=!0===i||"tooltip"===i,d=c||u||s;let p=0,f=0;d&&a().eachTree(n.children,(e=>{e.children&&n.children.length||f++,p+=e.renderWidth}),{children:"children"}),e.style.width=d?p-f-(t?2:0)+"px":""}}))}else if("body"===i){const t=A[`${e}-${i}-emptyBlock`],l=t?t.value:null;if(ge(y)){let e=0;const t=V-I-N;if(_&&(e=_-I-N,d&&(e-=o?0:C),e=Math.max(t,e),y.style.maxHeight=`${e}px`),P){let n=P-I-N;d&&(n-=o?0:C),e&&(n=Math.min(e,n)),y.style.height=`${Math.max(t,n)}px`}else y.style.height="";y.style.minHeight=`${t}px`}T&&(ge(y)&&(y.style.top=`${I}px`),T.style.height=(P>0?P-I-N:M)+I+N-C*(o?2:1)+"px",T.style.width=`${L.reduce(((e,t)=>e+t.renderWidth),g?0:w)}px`);let r=$,a=v;d&&(a=u.expandColumn||!b&&!(n?k:n)||E.length||s||p&&W.isMerge?R:L),r=a.reduce(((e,t)=>e+t.renderWidth),0),F&&(F.style.width=r?`${r}px`:"",F.style.paddingRight=w&&d&&(Y["-moz"]||Y.safari)?`${w}px`:""),l&&(l.style.width=r?`${r}px`:"")}else if("footer"===i){let e=$,t=v;d&&(t=u.expandColumn||!x&&!r||S.length&&c?R:L),e=t.reduce(((e,t)=>e+t.renderWidth),0),ge(y)&&(T&&(y.style.top=`${P>0?P-N:M+I}px`),y.style.marginTop=-Math.max(1,C)+"px"),F&&(F.style.width=e?`${e+w}px`:"")}const j=A[`${e}-${i}-colgroup`],H=j?j.value:null;H&&a().arrayEach(H.children,(t=>{const o=t.getAttribute("name");if("col_gutter"===o&&(t.style.width=`${w}px`),O[o]){const s=O[o].column,{showHeaderOverflow:c,showFooterOverflow:u,showOverflow:d}=s;let p;t.style.width=`${s.renderWidth}px`,p="header"===i?a().isUndefined(c)||a().isNull(c)?l:c:"footer"===i?a().isUndefined(u)||a().isNull(u)?r:u:a().isUndefined(d)||a().isNull(d)?n:d;const f="ellipsis"===p,h="title"===p,g=!0===p||"tooltip"===p;let v=h||g||f;const x=A[`${e}-${i}-list`],w=x?x.value:null;b&&!v&&(v=!0),w&&a().arrayEach(w.querySelectorAll(`.${s.id}`),(e=>{const t=parseInt(e.getAttribute("colspan")||1),o=e.querySelector(".vxe-cell");let n=s.renderWidth;if(o){if(t>1){const e=m.getColumnIndex(s);for(let o=1;o<t;o++){const t=m.getColumns(e+o);t&&(n+=t.renderWidth)}}o.style.width=v?n-B*t+"px":""}}))}}))}))})),h&&m.setCurrentRow(h),i&&z.selected&&T.selected.row&&T.selected.column&&rt.addCellSelectedClass(),(0,g.nextTick)()},Rt=e=>rt.triggerValidate?rt.triggerValidate(e):(0,g.nextTick)(),Ot=(e,t)=>{Rt("blur").catch((e=>e)).then((()=>{rt.handleActived(t,e).then((()=>Rt("change"))).catch((e=>e))}))},Mt=()=>{const{sortConfig:t}=e;if(t){const e=xe.value;let{defaultSort:o}=e;o&&(a().isArray(o)||(o=[o]),o.length&&((t.multiple?o:o.slice(0,1)).forEach(((e,t)=>{const{field:o,order:n}=e;if(o&&n){const e=m.getColumnByField(o);e&&e.sortable&&(e.order=n,e.sortTime=Date.now()+t)}})),e.remote||h.handleTableData(!0).then(kt)))}},$t=()=>{const{checkboxConfig:t}=e;if(t){const{fullDataRowIdData:e}=d,t=de.value,{checkAll:o,checkRowKeys:n}=t;if(o)Pt(!0,!0);else if(n){const t=[];n.forEach((o=>{e[o]&&t.push(e[o].row)})),At(t,!0,!0)}}},It=()=>{const{radioConfig:t}=e;if(t){const{fullDataRowIdData:e}=d,t=ue.value,{checkRowKey:o,reserve:n}=t;if(o&&(e[o]&&Lt(e[o].row,!0),n)){const e=Ce(rt);d.radioReserveRow={[e]:o}}}},Dt=()=>{const{expandConfig:t}=e;if(t){const{fullDataRowIdData:e}=d,t=Xe.value,{expandAll:o,expandRowKeys:n}=t;if(o)m.setAllRowExpand(!0);else if(n){const t=[];n.forEach((o=>{e[o]&&t.push(e[o].row)})),m.setRowExpand(t,!0)}}},Ft=e=>{const t=ue.value;t.reserve&&(d.radioReserveRow=e)},Nt=(e,t)=>{const{checkboxReserveRowMap:o}=d,n=de.value;if(n.reserve){const n=ye(rt,e);t?o[n]=e:o[n]&&delete o[n]}},Lt=(e,t)=>{const o=ue.value,{checkMethod:n}=o;return e&&(t||!n||n({row:e}))&&(u.selectRadioRow=e,Ft(e)),(0,g.nextTick)()},At=(e,t,o)=>(e&&!a().isArray(e)&&(e=[e]),e.forEach((e=>h.handleSelectRow({row:e},!!t,o))),(0,g.nextTick)()),Pt=(t,o)=>{const{treeConfig:n}=e,{selectCheckboxMaps:l}=u,{afterFullData:r,afterFullRowMaps:i,checkboxReserveRowMap:s}=d,c=Ye.value,p=c.children||c.childrenField,f=de.value,{checkField:m,reserve:v,checkStrictly:x,checkMethod:b}=f,w=f.indeterminateField||f.halfField,C={};if(n||a().each(l,((e,t)=>{i[t]||(C[t]=e)})),x)u.isAllSelected=t;else{if(m){const e=e=>{(o||!b||b({row:e}))&&(t&&(C[ye(rt,e)]=e),a().set(e,m,t)),n&&w&&a().set(e,w,!1)};n?a().eachTree(r,e,{children:p}):r.forEach(e)}else n?t?a().eachTree(r,(e=>{(o||!b||b({row:e}))&&(C[ye(rt,e)]=e)}),{children:p}):!o&&b&&a().eachTree(r,(e=>{const t=ye(rt,e);!b({row:e})&&l[t]&&(C[t]=e)}),{children:p}):t?!o&&b?r.forEach((e=>{const t=ye(rt,e);(l[t]||b({row:e}))&&(C[t]=e)})):r.forEach((e=>{C[ye(rt,e)]=e})):!o&&b&&r.forEach((e=>{const t=ye(rt,e);!b({row:e})&&l[t]&&(C[t]=e)}));v&&(t?a().each(C,((e,t)=>{s[t]=e})):r.forEach((e=>Nt(e,!1)))),u.selectCheckboxMaps=m?{}:C}return u.treeIndeterminateMaps={},d.treeIndeterminateRowMaps={},h.checkSelectionStatus(),(0,g.nextTick)()},Vt=()=>{const{treeConfig:t}=e,{expandColumn:o,currentRow:n,selectCheckboxMaps:l,selectRadioRow:r,rowExpandedMaps:a,treeExpandedMaps:i}=u,{fullDataRowIdData:s,fullAllDataRowIdData:c,radioReserveRow:p}=d,f=Xe.value,h=Ye.value,g=ue.value,v=de.value;if(r&&!c[ye(rt,r)]&&(u.selectRadioRow=null),g.reserve&&p){const e=ye(rt,p);s[e]&&Lt(s[e].row,!0)}u.selectCheckboxMaps=ct(l),v.reserve&&At(ut(d.checkboxReserveRowMap),!0,!0),n&&!c[ye(rt,n)]&&(u.currentRow=null),u.rowExpandedMaps=o?ct(a):{},o&&f.reserve&&m.setRowExpand(ut(d.rowExpandedReserveRowMap),!0),u.treeExpandedMaps=t?ct(i):{},t&&h.reserve&&m.setTreeExpand(ut(d.treeExpandedReserveRowMap),!0)},_t=()=>{const{treeConfig:t}=e;if(t){const{tableFullData:e}=d,t=Ye.value,{expandAll:o,expandRowKeys:n}=t,l=t.children||t.childrenField;if(o)m.setAllTreeExpand(!0);else if(n){const t=[],o=Ce(rt);n.forEach((n=>{const r=a().findTree(e,(e=>n===a().get(e,o)),{children:l});r&&t.push(r.item)})),m.setTreeExpand(t,!0)}}},jt=e=>{const t=Ye.value,o=de.value,{transform:n,loadMethod:l}=t,{checkStrictly:r}=o;return new Promise((t=>{if(l){const{treeExpandLazyLoadedMaps:o}=u,{fullAllDataRowIdData:i}=d,s=ye(rt,e),c=i[s];o[s]=e,l({$table:rt,row:e}).then((t=>{if(c.treeLoaded=!0,o[s]&&delete o[s],a().isArray(t)||(t=[]),t)return m.loadTreeChildren(e,t).then((t=>{const{treeExpandedMaps:o}=u;return t.length&&!o[s]&&(o[s]=e),!r&&m.isCheckedByCheckboxRow(e)&&At(t,!0),(0,g.nextTick)().then((()=>{if(n)return h.handleTableData()}))}))})).catch((()=>{const{treeExpandLazyLoadedMaps:e}=u;c.treeLoaded=!1,e[s]&&delete e[s]})).finally((()=>{(0,g.nextTick)().then((()=>m.recalculate())).then((()=>t()))}))}else t()}))},Ht=(e,t)=>{const{treeExpandedReserveRowMap:o}=d,n=Ye.value;if(n.reserve){const n=ye(rt,e);t?o[n]=e:o[n]&&delete o[n]}},Bt=e=>new Promise((t=>{const o=Xe.value,{loadMethod:n}=o;if(n){const{fullAllDataRowIdData:o}=d,{rowExpandLazyLoadedMaps:l}=u,r=ye(rt,e),a=o[r];l[r]=e,n({$table:rt,row:e,rowIndex:m.getRowIndex(e),$rowIndex:m.getVMRowIndex(e)}).then((()=>{const{rowExpandedMaps:t}=u;a.expandLoaded=!0,t[r]=e})).catch((()=>{a.expandLoaded=!1})).finally((()=>{const{rowExpandLazyLoadedMaps:e}=u;e[r]&&delete e[r],(0,g.nextTick)().then((()=>m.recalculate())).then((()=>t()))}))}else t()})),zt=(e,t)=>{const{rowExpandedReserveRowMap:o}=d,n=Xe.value;if(n.reserve){const n=ye(rt,e);t?o[n]=e:o[n]&&delete o[n]}},Wt=()=>{const{mergeCells:t}=e;t&&m.setMergeCells(t)},qt=()=>{const{mergeFooterItems:t}=e;t&&m.setMergeFooterItems(t)},Ut=()=>(0,g.nextTick)().then((()=>{const{scrollXLoad:e,scrollYLoad:t}=u,{scrollXStore:o,scrollYStore:n}=d,l=X.value,r=U.value;if(e){const{visibleSize:e}=dt(),t=r.oSize?a().toNumber(r.oSize):Y.edge?5:0;o.offsetSize=t,o.visibleSize=e,o.endIndex=Math.max(o.startIndex+o.visibleSize+t,o.endIndex),h.updateScrollXData()}else h.updateScrollXSpace();const{rowHeight:i,visibleSize:s}=pt();if(n.rowHeight=i,t){const e=l.oSize?a().toNumber(l.oSize):Y.edge?10:0;n.offsetSize=e,n.visibleSize=s,n.endIndex=Math.max(n.startIndex+s+e,n.endIndex),h.updateScrollYData()}else h.updateScrollYSpace();u.rowHeight=i,(0,g.nextTick)(kt)})),Xt=t=>{const{keepSource:o,treeConfig:n}=e,{editStore:l,scrollYLoad:r}=u,{scrollYStore:i,scrollXStore:s,lastScrollLeft:c,lastScrollTop:v}=d,x=Ye.value,{transform:b}=x,w=x.children||x.childrenField;let C=[],y=(0,g.reactive)(t?t.slice(0):[]);n&&(b?(x.rowField||f("vxe.error.reqProp",["tree-config.rowField"]),x.parentField||f("vxe.error.reqProp",["tree-config.parentField"]),w||f("vxe.error.reqProp",["tree-config.childrenField"]),x.mapChildrenField||f("vxe.error.reqProp",["tree-config.mapChildrenField"]),w===x.mapChildrenField&&f("vxe.error.errConflicts",["tree-config.childrenField","tree-config.mapChildrenField"]),C=a().toArrayTree(y,{key:x.rowField,parentKey:x.parentField,children:w,mapChildren:x.mapChildrenField}),y=C.slice(0)):C=y.slice(0)),i.startIndex=0,i.endIndex=1,s.startIndex=0,s.endIndex=1,u.scrollVMLoading=!1,l.insertMaps={},l.removeMaps={};const T=to(y);return u.scrollYLoad=T,d.tableFullData=y,d.tableFullTreeData=C,h.cacheRowMap(!0),d.tableSynchData=t,o&&h.cacheSourceMap(y),T&&(e.height||e.maxHeight||f("vxe.error.reqProp",["table.height | table.max-height | table.scroll-y={enabled: false}"]),e.showOverflow||p("vxe.error.reqProp",["table.show-overflow"]),e.spanMethod&&p("vxe.error.scrollErrProp",["table.span-method"])),rt.clearCellAreas&&e.mouseConfig&&(rt.clearCellAreas(),rt.clearCopyCellArea()),m.clearMergeCells(),m.clearMergeFooterItems(),h.handleTableData(!0),m.updateFooter(),(0,g.nextTick)().then((()=>{wt(),kt()})).then((()=>{Ut()})).then((()=>(T&&(i.endIndex=i.visibleSize),Vt(),h.checkSelectionStatus(),new Promise((e=>{(0,g.nextTick)().then((()=>m.recalculate())).then((()=>{let t=c,o=v;const n=U.value,l=X.value;n.scrollToLeftOnChange&&(t=0),l.scrollToTopOnChange&&(o=0),r===T?ve(rt,t,o).then(e):setTimeout((()=>ve(rt,t,o).then(e)))}))})))))},Yt=()=>{$t(),It(),Dt(),_t(),Wt(),qt(),(0,g.nextTick)((()=>setTimeout((()=>m.recalculate()))))},Gt=()=>{Mt()},Kt=()=>{const{scrollXLoad:e}=u,{visibleColumn:t,scrollXStore:o,fullColumnIdData:n}=d,l=e?t.slice(o.startIndex,o.endIndex):t.slice(0);l.forEach(((e,t)=>{const o=e.id,l=n[o];l&&(l.$index=t)})),u.tableColumn=l},Zt=()=>{const{mergeList:e,mergeFooterList:t}=u,{scrollXStore:o}=d,{startIndex:n,endIndex:l,offsetSize:r}=o,{toVisibleIndex:a,visibleSize:i}=dt(),s={startIndex:Math.max(0,a-1-r),endIndex:a+i+r};ft(e.concat(t),s,"col");const{startIndex:c,endIndex:p}=s;(a<=n||a>=l-i-1)&&(n===c&&l===p||(o.startIndex=c,o.endIndex=p,h.updateScrollXData())),m.closeTooltip()},Jt=e=>{const t=[];return e.forEach((e=>{t.push(...e.children&&e.children.length?Jt(e.children):[e])})),t},Qt=()=>{const t=[],o=[],n=[],{isGroup:l,columnStore:r}=u,i=U.value,{collectColumn:s,tableFullColumn:c,scrollXStore:h,fullColumnIdData:g}=d;if(l){const e=[],l=[],r=[];a().eachTree(s,((e,l,r,i,s)=>{const c=B(e);s&&s.fixed&&(e.fixed=s.fixed),s&&e.fixed!==s.fixed&&f("vxe.error.groupFixed"),c?e.visible=!!a().findTree(e.children,(e=>!B(e)&&e.visible)):e.visible&&("left"===e.fixed?t.push(e):"right"===e.fixed?n.push(e):o.push(e))})),s.forEach((t=>{t.visible&&("left"===t.fixed?e.push(t):"right"===t.fixed?r.push(t):l.push(t))})),u.tableGroupColumn=e.concat(l).concat(r)}else c.forEach((e=>{e.visible&&("left"===e.fixed?t.push(e):"right"===e.fixed?n.push(e):o.push(e))}));const v=t.concat(o).concat(n),x=!!i.enabled&&i.gt>-1&&(0===i.gt||i.gt<c.length);if(u.hasFixedColumn=t.length>0||n.length>0,Object.assign(r,{leftList:t,centerList:o,rightList:n}),x){e.showHeader&&!e.showHeaderOverflow&&p("vxe.error.reqProp",["show-header-overflow"]),e.showFooter&&!e.showFooterOverflow&&p("vxe.error.reqProp",["show-footer-overflow"]),e.spanMethod&&p("vxe.error.scrollErrProp",["span-method"]),e.footerSpanMethod&&p("vxe.error.scrollErrProp",["footer-span-method"]);const{visibleSize:t}=dt();h.startIndex=0,h.endIndex=t,h.visibleSize=t}return v.length===d.visibleColumn.length&&d.visibleColumn.every(((e,t)=>e===v[t]))||(m.clearMergeCells(),m.clearMergeFooterItems()),u.scrollXLoad=x,v.forEach(((e,t)=>{const o=e.id,n=g[o];n&&(n._index=t)})),d.visibleColumn=v,Kt(),m.updateFooter().then((()=>m.recalculate())).then((()=>(m.updateCellAreas(),m.recalculate())))},eo=e=>{d.collectColumn=e;const t=Jt(e);return d.tableFullColumn=t,bt(),xt(),Qt().then((()=>{u.scrollXLoad&&Zt()})),m.clearMergeCells(),m.clearMergeFooterItems(),h.handleTableData(!0),(u.scrollXLoad||u.scrollYLoad)&&u.expandColumn&&p("vxe.error.scrollErrProp",["column.type=expand"]),(0,g.nextTick)().then((()=>(V&&V.syncUpdate({collectColumn:e,$table:rt}),m.recalculate())))},to=t=>{const{treeConfig:o}=e,n=X.value,l=Ye.value,{transform:r}=l,a=t||d.tableFullData,i=(r||!o)&&!!n.enabled&&n.gt>-1&&(0===n.gt||n.gt<a.length);return u.scrollYLoad=i,i},no=(e,t)=>{const{treeExpandedMaps:o,treeExpandLazyLoadedMaps:n,treeNodeColumn:l}=u,r={...o},{fullAllDataRowIdData:i,tableFullData:s}=d,c=Ye.value,{reserve:p,lazy:f,accordion:h,toggleMethod:g}=c,v=c.children||c.childrenField,x=c.hasChild||c.hasChildField,b=[],w=m.getColumnIndex(l),C=m.getVMColumnIndex(l);let y=g?e.filter((e=>g({$table:rt,expanded:t,column:l,columnIndex:w,$columnIndex:C,row:e}))):e;if(h){y=y.length?[y[y.length-1]]:[];const e=a().findTree(s,(e=>e===y[0]),{children:v});e&&e.items.forEach((e=>{const t=ye(rt,e);r[t]&&delete r[t]}))}return t?y.forEach((e=>{const t=ye(rt,e);if(!r[t]){const o=i[t],l=f&&e[x]&&!o.treeLoaded&&!n[t];l?b.push(jt(e)):e[v]&&e[v].length&&(r[t]=e)}})):y.forEach((e=>{const t=ye(rt,e);r[t]&&delete r[t]})),p&&y.forEach((e=>Ht(e,t))),u.treeExpandedMaps=r,Promise.all(b).then((()=>m.recalculate()))},lo=(e,t)=>no(e,t).then((()=>(Et(),h.handleTableData()))).then((()=>m.recalculate())),ro=e=>{const{mergeList:t}=u,{scrollYStore:o}=d,{startIndex:n,endIndex:l,visibleSize:r,offsetSize:a,rowHeight:i}=o,s=e.currentTarget||e.target,c=s.scrollTop,p=Math.floor(c/i),f={startIndex:Math.max(0,p-1-a),endIndex:p+r+a};ft(t,f,"row");const{startIndex:m,endIndex:g}=f;(p<=n||p>=l-r-1)&&(n===m&&l===g||(o.startIndex=m,o.endIndex=g,h.updateScrollYData()))},ao=e=>function(t){const{fullAllDataRowIdData:o}=d;if(t){const n=ye(rt,t),l=o[n];if(l)return l[e]}return-1},io=e=>function(t){const{fullColumnIdData:o}=d;if(t){const n=o[t.id];if(n)return n[e]}return-1},so=a().debounce((function(e){ro(e)}),20,{leading:!1,trailing:!0});let co;m={dispatchEvent(e,t,o){n(e,Object.assign({$table:rt,$grid:A,$event:o},t))},clearAll(){return je(rt)},syncData(){return p("vxe.error.delFunc",["syncData","getData"]),(0,g.nextTick)().then((()=>(u.tableData=[],n("update:data",d.tableFullData),(0,g.nextTick)())))},updateData(){const{scrollXLoad:e,scrollYLoad:t}=u;return h.handleTableData(!0).then((()=>{if(m.updateFooter(),e||t)return e&&h.updateScrollXSpace(),t&&h.updateScrollYSpace(),m.refreshScroll()})).then((()=>(m.updateCellAreas(),m.recalculate(!0)))).then((()=>{setTimeout((()=>rt.recalculate()),50)}))},loadData(e){const{inited:t,initStatus:o}=d;return Xt(e).then((()=>(d.inited=!0,d.initStatus=!0,o||Yt(),t||Gt(),m.recalculate())))},reloadData(e){const{inited:t}=d;return m.clearAll().then((()=>(d.inited=!0,d.initStatus=!0,Xt(e)))).then((()=>(Yt(),t||Gt(),m.recalculate())))},reloadRow(t,o,n){const{keepSource:l}=e,{tableData:r}=u,{tableSourceData:i}=d;if(l){const e=m.getRowIndex(t),l=i[e];if(l&&t)if(n){const e=a().get(o||t,n);a().set(t,n,e),a().set(l,n,e)}else{const e=a().clone({...o},!0);a().destructuring(l,Object.assign(t,e))}u.tableData=r.slice(0)}else p("vxe.error.reqProp",["keep-source"]);return(0,g.nextTick)()},loadTreeChildren(t,o){const{keepSource:n}=e,{tableSourceData:l,fullDataRowIdData:r,fullAllDataRowIdData:i,sourceDataRowIdData:s}=d,c=Ye.value,{transform:u,mapChildrenField:p}=c,f=c.children||c.childrenField,h=i[ye(rt,t)],g=h?h.level:0;return m.createData(o).then((e=>{if(n){const o=ye(rt,t),n=a().findTree(l,(e=>o===ye(rt,e)),{children:f});n&&(n.item[f]=a().clone(e,!0)),e.forEach((e=>{const t=ye(rt,e);s[t]=a().clone(e,!0)}))}return a().eachTree(e,((e,t,o,n,l,a)=>{const s=ye(rt,e),c={row:e,rowid:s,seq:-1,index:t,_index:-1,$index:-1,items:o,parent:l,level:g+a.length};r[s]=c,i[s]=c}),{children:f}),t[f]=e,u&&(t[p]=e),Tt(),e}))},loadColumn(e){const t=a().mapTree(e,(e=>(0,g.reactive)(ul.createColumn(rt,e))));return eo(t)},reloadColumn(e){return m.clearAll().then((()=>m.loadColumn(e)))},getRowNode(e){if(e){const{fullAllDataRowIdData:t}=d,o=e.getAttribute("rowid");if(o){const e=t[o];if(e)return{rowid:e.rowid,item:e.row,index:e.index,items:e.items,parent:e.parent}}}return null},getColumnNode(e){if(e){const{fullColumnIdData:t}=d,o=e.getAttribute("colid");if(o){const e=t[o];if(e)return{colid:e.colid,item:e.column,index:e.index,items:e.items,parent:e.parent}}}return null},getRowSeq:ao("seq"),getRowIndex:ao("index"),getVTRowIndex:ao("_index"),getVMRowIndex:ao("$index"),getColumnIndex:io("index"),getVTColumnIndex:io("_index"),getVMColumnIndex:io("$index"),createData(e){return(0,g.nextTick)().then((()=>(0,g.reactive)(h.defineField(e))))},createRow(e){const t=a().isArray(e);return t||(e=[e||{}]),m.createData(e).then((e=>t?e:e[0]))},revertData(t,o){const{keepSource:n}=e,{tableSourceData:l,sourceDataRowIdData:r}=d;if(!n)return p("vxe.error.reqProp",["keep-source"]),(0,g.nextTick)();let i=t;return t?a().isArray(t)||(i=[t]):i=a().toArray(rt.getUpdateRecords()),i.length&&i.forEach((e=>{if(!m.isInsertByRow(e)){const t=ye(rt,e),n=r[t];n&&e&&(o?a().set(e,o,a().clone(a().get(n,o),!0)):a().destructuring(e,a().clone(n,!0)))}})),t?(0,g.nextTick)():m.reloadData(l)},clearData(e,t){const{tableFullData:o,visibleColumn:n}=d;return arguments.length?e&&!a().isArray(e)&&(e=[e]):e=o,t?e.forEach((e=>a().set(e,t,null))):e.forEach((e=>{n.forEach((t=>{t.field&&$e(e,t,null)}))})),(0,g.nextTick)()},isInsertByRow(e){const{editStore:t}=u,o=ye(rt,e);return t.insertMaps[o]},removeInsertRow(){const{editStore:e}=u;return e.insertMaps={},rt.remove(rt.getInsertRecords())},isUpdateByRow(t,o){const{keepSource:n}=e,{tableFullColumn:l,fullDataRowIdData:r,sourceDataRowIdData:a}=d;if(n){const e=ye(rt,t);if(!r[e])return!1;const n=a[e];if(n){if(arguments.length>1)return!at(n,t,o);for(let e=0,o=l.length;e<o;e++){const o=l[e].field;if(o&&!at(n,t,o))return!0}}}return!1},getColumns(e){const t=d.visibleColumn;return a().isUndefined(e)?t.slice(0):t[e]},getColumnById(e){const t=d.fullColumnIdData;return t[e]?t[e].column:null},getColumnByField(e){const t=d.fullColumnFieldData;return t[e]?t[e].column:null},getTableColumn(){return{collectColumn:d.collectColumn.slice(0),fullColumn:d.tableFullColumn.slice(0),visibleColumn:d.visibleColumn.slice(0),tableColumn:u.tableColumn.slice(0)}},getData(t){const o=e.data||d.tableSynchData;return a().isUndefined(t)?o.slice(0):o[t]},getCheckboxRecords(t){const{treeConfig:o}=e,{tableFullData:n,afterFullData:l,afterTreeFullData:r,tableFullTreeData:i,fullDataRowIdData:s,afterFullRowMaps:c}=d,p=Ye.value,f=de.value,{transform:m,mapChildrenField:h}=p,{checkField:g}=f,v=p.children||p.childrenField;let x=[];const b=t?m?i:n:m?r:l;if(g)x=o?a().filterTree(b,(e=>a().get(e,g)),{children:m?h:v}):b.filter((e=>a().get(e,g)));else{const{selectCheckboxMaps:e}=u;a().each(e,((e,o)=>{(t?s[o]:c[o])&&x.push(e)}))}return x},getParentRow(t){const{treeConfig:o}=e,{fullDataRowIdData:n}=d;if(t&&o){let e;if(e=a().isString(t)?t:ye(rt,t),e)return n[e]?n[e].parent:null}return null},getRowById(e){const{fullDataRowIdData:t}=d,o=a().eqNull(e)?"":encodeURIComponent(e);return t[o]?t[o].row:null},getRowid(e){return ye(rt,e)},getTableData(){const{tableData:e,footerTableData:t}=u,{tableFullData:o,afterFullData:n}=d;return{fullData:o.slice(0),visibleData:n.slice(0),tableData:e.slice(0),footerData:t.slice(0)}},setColumnFixed(e,t){const o=Te(rt,e),n=Pe(rt,o),l=et.value;return n&&n.fixed!==t?!n.fixed&&l?(oo.modal&&oo.modal.message({status:"error",content:c.i18n("vxe.table.maxFixedCol")}),(0,g.nextTick)()):(a().eachTree([n],(e=>{e.fixed=t})),h.saveCustomFixed(),m.refreshColumn()):(0,g.nextTick)()},clearColumnFixed(e){const t=Te(rt,e),o=Pe(rt,t);return o&&o.fixed?(a().eachTree([o],(e=>{e.fixed=null})),h.saveCustomFixed(),m.refreshColumn()):(0,g.nextTick)()},hideColumn(e){const t=Te(rt,e);return t&&t.visible?(t.visible=!1,h.handleCustom()):(0,g.nextTick)()},showColumn(e){const t=Te(rt,e);return t&&!t.visible?(t.visible=!0,h.handleCustom()):(0,g.nextTick)()},setColumnWidth(e,t){const o=Te(rt,e);if(o){const e=a().toInteger(t);let n=e;if(Q(t)){const t=E.value,o=t?t.$el:null,l=o?o.clientWidth-1:0;n=Math.floor(e*l)}o.renderWidth=n}return(0,g.nextTick)()},getColumnWidth(e){const t=Te(rt,e);return t?t.renderWidth:0},resetColumn(e){const{collectColumn:t}=d,o=Je.value,{checkMethod:n}=o,l=Object.assign({visible:!0,resizable:!0===e,fixed:!0===e},e);return a().eachTree(t,(e=>{l.resizable&&(e.resizeWidth=0),l.fixed&&(e.fixed=e.defaultFixed),n&&!n({column:e})||(e.visible=e.defaultVisible)})),l.resizable&&h.saveCustomResizable(!0),l.fixed&&h.saveCustomFixed(),h.handleCustom()},refreshColumn(){return Qt().then((()=>m.refreshScroll())).then((()=>m.recalculate()))},refreshScroll(){const{lastScrollLeft:e,lastScrollTop:t}=d,o=E.value,n=S.value,l=R.value,r=$.value,a=o?o.$el:null,i=l?l.$el:null,s=r?r.$el:null,c=n?n.$el:null;return new Promise((o=>{if(e||t)return ve(rt,e,t).then().then((()=>{setTimeout(o,30)}));ae(a,t),ae(i,t),ae(s,t),ie(c,e),setTimeout(o,30)}))},recalculate(e){return Ct(),!0===e?Ut().then((()=>(Ct(),Ut()))):Ut()},openTooltip(e,t){const o=b.value;return o?o.open(e,t):(0,g.nextTick)()},closeTooltip(){const{tooltipStore:e}=u,t=x.value,o=b.value;return e.visible&&(Object.assign(e,{row:null,column:null,content:null,visible:!1}),t&&t.close()),o&&o.close(),(0,g.nextTick)()},isAllCheckboxChecked(){return u.isAllSelected},isAllCheckboxIndeterminate(){return!u.isAllSelected&&u.isIndeterminate},getCheckboxIndeterminateRecords(t){const{treeConfig:o}=e,{fullDataRowIdData:n}=d,{treeIndeterminateMaps:l}=u;if(o){const e=[],o=[];return a().each(l,((t,l)=>{t&&(e.push(t),n[l]&&o.push(t))})),t?e:o}return[]},setCheckboxRow(e,t){return At(e,t,!0)},isCheckedByCheckboxRow(e){const{selectCheckboxMaps:t}=u,o=de.value,{checkField:n}=o;return n?a().get(e,n):!!t[ye(rt,e)]},isIndeterminateByCheckboxRow(e){const{treeIndeterminateMaps:t}=u;return!!t[ye(rt,e)]&&!m.isCheckedByCheckboxRow(e)},toggleCheckboxRow(e){const{selectCheckboxMaps:t}=u,o=de.value,{checkField:n}=o,l=n?!a().get(e,n):!t[ye(rt,e)];return h.handleSelectRow({row:e},l,!0),(0,g.nextTick)()},setAllCheckboxRow(e){return Pt(e,!0)},getRadioReserveRecord(t){const{treeConfig:o}=e,{fullDataRowIdData:n,radioReserveRow:l,afterFullData:r}=d,i=ue.value,s=Ye.value,c=s.children||s.childrenField;if(i.reserve&&l){const e=ye(rt,l);if(t){if(!n[e])return l}else{const t=Ce(rt);if(o){const o=a().findTree(r,(o=>e===a().get(o,t)),{children:c});if(o)return l}else if(!r.some((o=>e===a().get(o,t))))return l}}return null},clearRadioReserve(){return d.radioReserveRow=null,(0,g.nextTick)()},getCheckboxReserveRecords(t){const{treeConfig:o}=e,{afterFullData:n,fullDataRowIdData:l,checkboxReserveRowMap:r}=d,i=de.value,s=Ye.value,c=s.children||s.childrenField,u=[];if(i.reserve){const e={};o?a().eachTree(n,(t=>{e[ye(rt,t)]=1}),{children:c}):n.forEach((t=>{e[ye(rt,t)]=1})),a().each(r,((o,n)=>{o&&(t?l[n]||u.push(o):e[n]||u.push(o))}))}return u},clearCheckboxReserve(){return d.checkboxReserveRowMap={},(0,g.nextTick)()},toggleAllCheckboxRow(){return h.triggerCheckAllEvent(null,!u.isAllSelected),(0,g.nextTick)()},clearCheckboxRow(){const{treeConfig:t}=e,{tableFullData:o}=d,n=Ye.value,l=n.children||n.childrenField,r=de.value,{checkField:i,reserve:s}=r,c=r.indeterminateField||r.halfField;if(i){const e=e=>{t&&c&&a().set(e,c,!1),a().set(e,i,!1)};t?a().eachTree(o,e,{children:l}):o.forEach(e)}return s&&o.forEach((e=>Nt(e,!1))),u.isAllSelected=!1,u.isIndeterminate=!1,u.selectCheckboxMaps={},u.treeIndeterminateMaps={},(0,g.nextTick)()},setCurrentRow(t){const o=Z.value,n=v.value;return m.clearCurrentRow(),u.currentRow=t,(o.isCurrent||e.highlightCurrentRow)&&n&&a().arrayEach(n.querySelectorAll(`[rowid="${ye(rt,t)}"]`),(e=>oe(e,"row--current"))),(0,g.nextTick)()},isCheckedByRadioRow(e){return rt.eqRow(u.selectRadioRow,e)},setRadioRow(e){return Lt(e,!0)},clearCurrentRow(){const e=v.value;return u.currentRow=null,d.hoverRow=null,e&&a().arrayEach(e.querySelectorAll(".row--current"),(e=>te(e,"row--current"))),(0,g.nextTick)()},clearRadioRow(){return u.selectRadioRow=null,(0,g.nextTick)()},getCurrentRecord(){const t=Z.value;return t.isCurrent||e.highlightCurrentRow?u.currentRow:null},getRadioRecord(e){const{fullDataRowIdData:t,afterFullRowMaps:o}=d,{selectRadioRow:n}=u;if(n){const l=ye(rt,n);if(e){if(!t[l])return n}else if(o[l])return n}return null},getCurrentColumn(){const t=K.value;return t.isCurrent||e.highlightCurrentColumn?u.currentColumn:null},setCurrentColumn(e){const t=Te(rt,e);return t&&(m.clearCurrentColumn(),u.currentColumn=t),(0,g.nextTick)()},clearCurrentColumn(){return u.currentColumn=null,(0,g.nextTick)()},setPendingRow(e,t){const o={...u.pendingRowMaps},n=[...u.pendingRowList];return e&&!a().isArray(e)&&(e=[e]),t?e.forEach((e=>{const t=ye(rt,e);t&&!o[t]&&(n.push(e),o[t]=e)})):e.forEach((e=>{const t=ye(rt,e);if(t&&o[t]){const l=rt.findRowIndexOf(n,e);l>-1&&n.splice(l,1),delete o[t]}})),u.pendingRowMaps=o,u.pendingRowList=n,(0,g.nextTick)()},togglePendingRow(e){const t={...u.pendingRowMaps},o=[...u.pendingRowList];return e&&!a().isArray(e)&&(e=[e]),e.forEach((e=>{const n=ye(rt,e);if(n)if(t[n]){const l=rt.findRowIndexOf(o,e);l>-1&&o.splice(l,1),delete t[n]}else o.push(e),t[n]=e})),u.pendingRowMaps=t,u.pendingRowList=o,(0,g.nextTick)()},hasPendingByRow(e){const{pendingRowMaps:t}=u,o=ye(rt,e);return!!t[o]},getPendingRecords(){const{pendingRowList:e}=u;return e.slice(0)},clearPendingRow(){return u.pendingRowMaps={},u.pendingRowList=[],(0,g.nextTick)()},sort(e,t){const o=xe.value,{multiple:n,remote:l,orders:r}=o;return e&&a().isString(e)&&(e=[{field:e,order:t}]),a().isArray(e)||(e=[e]),e.length?(n||gt(),(n?e:[e[0]]).forEach(((e,t)=>{let{field:o,order:n}=e,l=o;a().isString(o)&&(l=m.getColumnByField(o)),l&&l.sortable&&(-1===r.indexOf(n)&&(n=it(l)),l.order!==n&&(l.order=n),l.sortTime=Date.now()+t)})),l||h.handleTableData(!0),(0,g.nextTick)().then((()=>(m.updateCellAreas(),kt())))):(0,g.nextTick)()},clearSort(e){const t=xe.value;if(e){const t=Te(rt,e);t&&(t.order=null)}else gt();return t.remote||h.handleTableData(!0),(0,g.nextTick)().then(kt)},isSort(e){if(e){const t=Te(rt,e);return!!t&&(t.sortable&&!!t.order)}return m.getSortColumns().length>0},getSortColumns(){const e=xe.value,{multiple:t,chronological:o}=e,n=[],{tableFullColumn:l}=d;return l.forEach((e=>{const{field:t,order:o}=e;e.sortable&&o&&n.push({column:e,field:t,property:t,order:o,sortTime:e.sortTime})})),t&&o&&n.length>1?a().orderBy(n,"sortTime"):n},closeFilter(){const{filterStore:e}=u,{column:t,visible:o}=e;return Object.assign(e,{isAllSelected:!1,isIndeterminate:!1,options:[],visible:!1}),o&&rt.dispatchEvent("filter-visible",{column:t,property:t.field,field:t.field,filterList:rt.getCheckedFilters(),visible:!1},null),(0,g.nextTick)()},isActiveFilterByColumn(e){const t=Te(rt,e);return t?t.filters&&t.filters.some((e=>e.checked)):rt.getCheckedFilters().length>0},isFilter(e){return m.isActiveFilterByColumn(e)},isRowExpandLoaded(e){const{fullAllDataRowIdData:t}=d,o=t[ye(rt,e)];return o&&!!o.expandLoaded},clearRowExpandLoaded(e){const{rowExpandLazyLoadedMaps:t}=u,{fullAllDataRowIdData:o}=d,n=Xe.value,{lazy:l}=n,r=ye(rt,e),a=o[r];return l&&a&&(a.expandLoaded=!1,delete t[r]),(0,g.nextTick)()},reloadRowExpand(e){const{rowExpandLazyLoadedMaps:t}=u,o=Xe.value,{lazy:n}=o,l=ye(rt,e);return n&&!t[l]&&m.clearRowExpandLoaded(e).then((()=>Bt(e))),(0,g.nextTick)()},reloadExpandContent(e){return p("vxe.error.delFunc",["reloadExpandContent","reloadRowExpand"]),m.reloadRowExpand(e)},toggleRowExpand(e){return m.setRowExpand(e,!m.isRowExpandByRow(e))},setAllRowExpand(t){const o=Ye.value,{tableFullData:n,tableFullTreeData:l}=d,r=o.children||o.childrenField;let i=[];return e.treeConfig?a().eachTree(l,(e=>{i.push(e)}),{children:r}):i=n,m.setRowExpand(i,t)},setRowExpand(e,t){const{rowExpandedMaps:o,rowExpandLazyLoadedMaps:n,expandColumn:l}=u,{fullAllDataRowIdData:r}=d;let i={...o};const s=Xe.value,{reserve:c,lazy:p,accordion:f,toggleMethod:h}=s,g=[],v=m.getColumnIndex(l),x=m.getVMColumnIndex(l);if(e){a().isArray(e)||(e=[e]),f&&(i={},e=e.slice(e.length-1,e.length));const o=h?e.filter((e=>h({$table:rt,expanded:t,column:l,columnIndex:v,$columnIndex:x,row:e,rowIndex:m.getRowIndex(e),$rowIndex:m.getVMRowIndex(e)}))):e;t?o.forEach((e=>{const t=ye(rt,e);if(!i[t]){const o=r[t],l=p&&!o.expandLoaded&&!n[t];l?g.push(Bt(e)):i[t]=e}})):o.forEach((e=>{const t=ye(rt,e);i[t]&&delete i[t]})),c&&o.forEach((e=>zt(e,t)))}return u.rowExpandedMaps=i,Promise.all(g).then((()=>m.recalculate()))},isRowExpandByRow(e){const{rowExpandedMaps:t}=u,o=ye(rt,e);return!!t[o]},isExpandByRow(e){return m.isRowExpandByRow(e)},clearRowExpand(){const{tableFullData:e}=d,t=Xe.value,{reserve:o}=t,n=m.getRowExpandRecords();return u.rowExpandedMaps={},o&&e.forEach((e=>zt(e,!1))),(0,g.nextTick)().then((()=>{n.length&&m.recalculate()}))},clearRowExpandReserve(){return d.rowExpandedReserveRowMap={},(0,g.nextTick)()},getRowExpandRecords(){const e=[];return a().each(u.rowExpandedMaps,(t=>{t&&e.push(t)})),e},getTreeExpandRecords(){const e=[];return a().each(u.treeExpandedMaps,(t=>{t&&e.push(t)})),e},isTreeExpandLoaded(e){const{fullAllDataRowIdData:t}=d,o=t[ye(rt,e)];return o&&!!o.treeLoaded},clearTreeExpandLoaded(e){const{treeExpandedMaps:t}=u,{fullAllDataRowIdData:o}=d,n=Ye.value,{transform:l,lazy:r}=n,a=ye(rt,e),i=o[a];return r&&i&&(i.treeLoaded=!1,t[a]&&delete t[a]),l?(Et(),h.handleTableData()):(0,g.nextTick)()},reloadTreeExpand(e){const{treeExpandLazyLoadedMaps:t}=u,o=Ye.value,n=o.hasChild||o.hasChildField,{transform:l,lazy:r}=o,a=ye(rt,e);return r&&e[n]&&!t[a]&&m.clearTreeExpandLoaded(e).then((()=>jt(e))).then((()=>{if(l)return Et(),h.handleTableData()})).then((()=>m.recalculate())),(0,g.nextTick)()},reloadTreeChilds(e){return p("vxe.error.delFunc",["reloadTreeChilds","reloadTreeExpand"]),m.reloadTreeExpand(e)},toggleTreeExpand(e){return m.setTreeExpand(e,!m.isTreeExpandByRow(e))},setAllTreeExpand(e){const{tableFullData:t}=d,o=Ye.value,{transform:n,lazy:l}=o,r=o.children||o.childrenField,i=[];return a().eachTree(t,(e=>{const t=e[r];(l||t&&t.length)&&i.push(e)}),{children:r}),m.setTreeExpand(i,e).then((()=>{if(n)return Et(),m.recalculate()}))},setTreeExpand(e,t){const o=Ye.value,{transform:n}=o;return e&&(a().isArray(e)||(e=[e]),e.length)?n?lo(e,t):no(e,t):(0,g.nextTick)()},isTreeExpandByRow(e){const{treeExpandedMaps:t}=u;return!!t[ye(rt,e)]},clearTreeExpand(){const{tableFullTreeData:e}=d,t=Ye.value,o=t.children||t.childrenField,{transform:n,reserve:l}=t,r=m.getTreeExpandRecords();return u.treeExpandedMaps={},l&&a().eachTree(e,(e=>Ht(e,!1)),{children:o}),h.handleTableData().then((()=>{if(n)return Et(),h.handleTableData()})).then((()=>{if(r.length)return m.recalculate()}))},clearTreeExpandReserve(){return d.treeExpandedReserveRowMap={},(0,g.nextTick)()},getScroll(){const{scrollXLoad:e,scrollYLoad:t}=u,o=E.value,n=o.$el;return{virtualX:e,virtualY:t,scrollTop:n.scrollTop,scrollLeft:n.scrollLeft}},scrollTo(e,t){const o=E.value,n=S.value,l=$.value,r=o?o.$el:null,i=l?l.$el:null,s=n?n.$el:null;return a().isNumber(e)&&ie(s||r,e),a().isNumber(t)&&ae(i||r,t),u.scrollXLoad||u.scrollYLoad?new Promise((e=>{setTimeout((()=>{(0,g.nextTick)((()=>{e()}))}),50)})):(0,g.nextTick)()},scrollToRow(t,o){const n=[];return t&&(e.treeConfig?n.push(h.scrollToTreeRow(t)):n.push(He(rt,t))),o&&n.push(m.scrollToColumn(o)),Promise.all(n)},scrollToColumn(e){const{fullColumnIdData:t}=d,o=Te(rt,e);return o&&t[o.id]?Be(rt,o):(0,g.nextTick)()},clearScroll(){const{scrollXStore:e,scrollYStore:t}=d,o=E.value,n=S.value,l=$.value,r=o?o.$el:null,a=l?l.$el:null,i=n?n.$el:null;return a&&(be(a),a.scrollTop=0),i&&(i.scrollLeft=0),r&&(be(r),r.scrollTop=0,r.scrollLeft=0),e.startIndex=0,t.startIndex=0,(0,g.nextTick)()},updateFooter(){const{showFooter:t,footerMethod:o}=e,{visibleColumn:n,afterFullData:l}=d;return t&&o&&(u.footerTableData=n.length?o({columns:n,data:l,$table:rt,$grid:A}):[]),(0,g.nextTick)()},updateStatus(t,o){const n=!a().isUndefined(o);return(0,g.nextTick)().then((()=>{const{editRules:l}=e,{validStore:r}=u,a=E.value;if(t&&a&&l){const{row:e,column:l}=t,a="change";if(rt.hasCellRules&&rt.hasCellRules(a,e,l)){const t=h.getCell(e,l);if(t)return rt.validCellRules(a,e,l,o).then((()=>{n&&r.visible&&$e(e,l,o),rt.clearValidate(e,l)})).catch((({rule:r})=>{n&&$e(e,l,o),rt.showValidTooltip({rule:r,row:e,column:l,cell:t})}))}}}))},setMergeCells(t){return e.spanMethod&&f("vxe.error.errConflicts",["merge-cells","span-method"]),mt(t,u.mergeList,d.afterFullData),(0,g.nextTick)().then((()=>m.updateCellAreas()))},removeMergeCells(t){e.spanMethod&&f("vxe.error.errConflicts",["merge-cells","span-method"]);const o=ht(t,u.mergeList,d.afterFullData);return(0,g.nextTick)().then((()=>(m.updateCellAreas(),o)))},getMergeCells(){return u.mergeList.slice(0)},clearMergeCells(){return u.mergeList=[],(0,g.nextTick)()},setMergeFooterItems(t){return e.footerSpanMethod&&f("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]),mt(t,u.mergeFooterList),(0,g.nextTick)().then((()=>m.updateCellAreas()))},removeMergeFooterItems(t){e.footerSpanMethod&&f("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]);const o=ht(t,u.mergeFooterList);return(0,g.nextTick)().then((()=>(m.updateCellAreas(),o)))},getMergeFooterItems(){return u.mergeFooterList.slice(0)},clearMergeFooterItems(){return u.mergeFooterList=[],(0,g.nextTick)()},updateCellAreas(){const{mouseConfig:t}=e,o=Se.value;return t&&o.area&&rt.handleUpdateCellAreas?rt.handleUpdateCellAreas():(0,g.nextTick)()},focus(){return d.isActivated=!0,(0,g.nextTick)()},blur(){return d.isActivated=!1,(0,g.nextTick)()},connect(e){return e?(V=e,V.syncUpdate({collectColumn:d.collectColumn,$table:rt})):f("vxe.error.barUnableLink"),(0,g.nextTick)()}};const uo=t=>{const{editStore:o,ctxMenuStore:n,filterStore:l}=u,{mouseConfig:r,editRules:a}=e,i=v.value,s=he.value,c=_.value,{actived:p}=o,f=w.value,g=C.value,x=y.value;if(g&&(ce(t,i,"vxe-cell--filter").flag||ce(t,g.$el).flag||ce(t,document.body,"vxe-table--ignore-clear").flag||h.preventEvent(t,"event.clearFilter",l.args,m.closeFilter)),p.row){if(!1!==s.autoClear){const o=p.args.cell;o&&ce(t,o).flag||f&&ce(t,f.$el).flag||(!d._lastCallTime||d._lastCallTime+50<Date.now())&&(ce(t,document.body,"vxe-table--ignore-clear").flag||h.preventEvent(t,"event.clearActived",p.args,(()=>{let o;if("row"===s.mode){const e=ce(t,i,"vxe-body--row"),n=e.flag?m.getRowNode(e.targetElem):null;o=!!n&&!rt.eqRow(n.item,p.args.row)}else o=!ce(t,i,"col--edit").flag;if(o||(o=ce(t,i,"vxe-header--row").flag),o||(o=ce(t,i,"vxe-footer--row").flag),!o&&e.height&&!u.overflowY){const e=t.target;ee(e,"vxe-table--body-wrapper")&&(o=t.offsetY<e.clientHeight)}!o&&ce(t,i).flag||setTimeout((()=>rt.clearEdit(t)))})))}}else r&&(ce(t,i).flag||A&&ce(t,A.getRefMaps().refElem.value).flag||x&&ce(t,x.getRefMaps().refElem.value).flag||V&&ce(t,V.getRefMaps().refElem.value).flag||(rt.clearSelected(),rt.clearCellAreas&&(ce(t,document.body,"vxe-table--ignore-areas-clear").flag||h.preventEvent(t,"event.clearAreas",{},(()=>{rt.clearCellAreas(),rt.clearCopyCellArea()})))));rt.closeMenu&&n.visible&&x&&!ce(t,x.getRefMaps().refElem.value).flag&&rt.closeMenu();const b=ce(t,A?A.getRefMaps().refElem.value:i).flag;!b&&a&&c.autoClear&&(u.validErrorMaps={}),d.isActivated=b},po=()=>{m.closeFilter(),rt.closeMenu&&rt.closeMenu()},fo=()=>{m.closeTooltip(),rt.closeMenu&&rt.closeMenu()},mo=t=>{const{mouseConfig:o,keyboardConfig:n}=e,{filterStore:l,ctxMenuStore:r,editStore:a}=u,i=Se.value,s=Re.value,{actived:c}=a,d=wo(t,go.ESCAPE);d&&h.preventEvent(t,"event.keydown",null,(()=>{if(m.dispatchEvent("keydown-start",{},t),n&&o&&i.area&&rt.handleKeyboardEvent)rt.handleKeyboardEvent(t);else if((c.row||l.visible||r.visible)&&(t.stopPropagation(),rt.closeMenu&&rt.closeMenu(),m.closeFilter(),n&&s.isEsc&&c.row)){const e=c.args;rt.clearEdit(t),i.selected&&(0,g.nextTick)((()=>rt.handleSelected(e,t)))}m.dispatchEvent("keydown",{},t),m.dispatchEvent("keydown-end",{},t)}))},ho=t=>{d.isActivated&&h.preventEvent(t,"event.keydown",null,(()=>{const{mouseConfig:o,keyboardConfig:n,treeConfig:l,editConfig:r,highlightCurrentRow:i}=e,{ctxMenuStore:s,editStore:c,currentRow:p}=u,f=Ve.value,v=Le.value,x=Re.value,b=Se.value,w=he.value,C=Ye.value,y=_e.value,T=Z.value,{selected:E,actived:S}=c,k=C.children||C.childrenField,R=t.keyCode,O=wo(t,go.ESCAPE),M=wo(t,go.BACKSPACE),$=wo(t,go.TAB),I=wo(t,go.ENTER),D=wo(t,go.SPACEBAR),F=wo(t,go.ARROW_LEFT),N=wo(t,go.ARROW_UP),L=wo(t,go.ARROW_RIGHT),V=wo(t,go.ARROW_DOWN),_=wo(t,go.DELETE),j=wo(t,go.F2),H=wo(t,go.CONTEXT_MENU),z=t.metaKey,W=t.ctrlKey,q=t.shiftKey,U=t.altKey,X=F||N||L||V,Y=f&&s.visible&&(I||D||X),G=P(r)&&S.column&&S.row;let K;if(Y)t.preventDefault(),s.showChild&&B(s.selected)?rt.moveCtxMenu(t,s,"selectChild",F,!1,s.selected.children):rt.moveCtxMenu(t,s,"selected",L,!0,y);else if(n&&o&&b.area&&rt.handleKeyboardEvent)rt.handleKeyboardEvent(t);else if(O){if(rt.closeMenu&&rt.closeMenu(),m.closeFilter(),n&&x.isEsc&&S.row){const e=S.args;rt.clearEdit(t),b.selected&&(0,g.nextTick)((()=>rt.handleSelected(e,t)))}}else if(D&&n&&x.isChecked&&E.row&&E.column&&("checkbox"===E.column.type||"radio"===E.column.type))t.preventDefault(),"checkbox"===E.column.type?h.handleToggleCheckRowEvent(t,E.args):h.triggerRadioRowEvent(t,E.args);else if(j&&P(r))G||E.row&&E.column&&(t.preventDefault(),rt.handleActived(E.args,t));else if(H)d._keyCtx=E.row&&E.column&&v.length,clearTimeout(co),co=setTimeout((()=>{d._keyCtx=!1}),1e3);else if(I&&!U&&n&&x.isEnter&&(E.row||S.row||l&&(T.isCurrent||i)&&p)){if(W)S.row&&(K=S.args,rt.clearEdit(t),b.selected&&(0,g.nextTick)((()=>rt.handleSelected(K,t))));else if(E.row||S.row){const e=E.row?E.args:S.args;q?x.enterToTab?rt.moveTabSelected(e,q,t):rt.moveSelected(e,F,!0,L,!1,t):x.enterToTab?rt.moveTabSelected(e,q,t):rt.moveSelected(e,F,!1,L,!0,t)}else if(l&&(T.isCurrent||i)&&p){const e=p[k];if(e&&e.length){t.preventDefault();const o=e[0];K={$table:rt,row:o,rowIndex:m.getRowIndex(o),$rowIndex:m.getVMRowIndex(o)},m.setTreeExpand(p,!0).then((()=>m.scrollToRow(o))).then((()=>h.triggerCurrentRowEvent(t,K)))}}}else if(X&&n&&x.isArrow)G||(E.row&&E.column?rt.moveSelected(E.args,F,N,L,V,t):(N||V)&&(T.isCurrent||i)&&rt.moveCurrentRow(N,V,t));else if($&&n&&x.isTab)E.row||E.column?rt.moveTabSelected(E.args,q,t):(S.row||S.column)&&rt.moveTabSelected(S.args,q,t);else if(n&&P(r)&&(_||(l&&(T.isCurrent||i)&&p?M&&x.isArrow:M))){if(!G){const{delMethod:e,backMethod:o}=x;if(x.isDel&&(E.row||E.column))e?e({row:E.row,rowIndex:m.getRowIndex(E.row),column:E.column,columnIndex:m.getColumnIndex(E.column),$table:rt}):$e(E.row,E.column,null),M?o?o({row:E.row,rowIndex:m.getRowIndex(E.row),column:E.column,columnIndex:m.getColumnIndex(E.column),$table:rt}):rt.handleActived(E.args,t):_&&m.updateFooter();else if(M&&x.isArrow&&l&&(T.isCurrent||i)&&p){const{parent:e}=a().findTree(d.afterFullData,(e=>e===p),{children:k});e&&(t.preventDefault(),K={$table:rt,row:e,rowIndex:m.getRowIndex(e),$rowIndex:m.getVMRowIndex(e)},m.setTreeExpand(e,!1).then((()=>m.scrollToRow(e))).then((()=>h.triggerCurrentRowEvent(t,K))))}}}else if(n&&P(r)&&x.isEdit&&!W&&!z&&(D||R>=48&&R<=57||R>=65&&R<=90||R>=96&&R<=111||R>=186&&R<=192||R>=219&&R<=222)){const{editMethod:e}=x;if(E.column&&E.row&&P(E.column.editRender)){const o=w.beforeEditMethod||w.activeMethod;o&&!o({...E.args,$table:rt,$grid:A})||(e?e({row:E.row,rowIndex:m.getRowIndex(E.row),column:E.column,columnIndex:m.getColumnIndex(E.column),$table:rt,$grid:A}):($e(E.row,E.column,null),rt.handleActived(E.args,t)))}}m.dispatchEvent("keydown",{},t)}))},vo=t=>{const{keyboardConfig:o,mouseConfig:n}=e,{editStore:l,filterStore:r}=u,{isActivated:a}=d,i=Se.value,s=Re.value,{actived:c}=l;a&&!r.visible&&(c.row||c.column||o&&s.isClip&&n&&i.area&&rt.handlePasteCellAreaEvent&&rt.handlePasteCellAreaEvent(t),m.dispatchEvent("paste",{},t))},xo=t=>{const{keyboardConfig:o,mouseConfig:n}=e,{editStore:l,filterStore:r}=u,{isActivated:a}=d,i=Se.value,s=Re.value,{actived:c}=l;a&&!r.visible&&(c.row||c.column||o&&s.isClip&&n&&i.area&&rt.handleCopyCellAreaEvent&&rt.handleCopyCellAreaEvent(t),m.dispatchEvent("copy",{},t))},bo=t=>{const{keyboardConfig:o,mouseConfig:n}=e,{editStore:l,filterStore:r}=u,{isActivated:a}=d,i=Se.value,s=Re.value,{actived:c}=l;a&&!r.visible&&(c.row||c.column||o&&s.isClip&&n&&i.area&&rt.handleCutCellAreaEvent&&rt.handleCutCellAreaEvent(t),m.dispatchEvent("cut",{},t))},Co=()=>{rt.closeMenu&&rt.closeMenu(),m.updateCellAreas(),m.recalculate(!0)},To=e=>{const t=x.value;clearTimeout(d.tooltipTimeout),e?m.closeTooltip():t&&t.setActived(!0)},Eo=(e,t,o,n,l)=>{l.cell=t;const{tooltipStore:r}=u,i=pe.value,{column:s,row:c}=l,{showAll:d,contentMethod:p}=i,f=p?p(l):null,m=p&&!a().eqNull(f),h=m?f:a().toString("html"===s.type?o.innerText:o.textContent).trim(),v=o.scrollWidth>o.clientWidth;return h&&(d||m||v)&&(Object.assign(r,{row:c,column:s,visible:!0,currOpts:null}),(0,g.nextTick)((()=>{const e=x.value;e&&e.open(v?o:n||o,W(h))}))),(0,g.nextTick)()};h={getSetupOptions(){return c},updateAfterDataIndex:Tt,callSlot(e,t){if(e){if(A)return A.callSlot(e,t);if(a().isFunction(e))return We(e(t))}return[]},getParentElem(){const e=v.value;if(A){const e=A.getRefMaps().refElem.value;return e?e.parentNode:null}return e?e.parentNode:null},getParentHeight(){const{height:t}=e,o=v.value;if(o){const e=o.parentNode,n="auto"===t?re(e):0;return Math.floor(A?A.getParentHeight():a().toNumber(getComputedStyle(e).height)-n)}return 0},getExcludeHeight(){return A?A.getExcludeHeight():0},defineField(t){const{treeConfig:o}=e,n=Xe.value,l=Ye.value,r=ue.value,i=de.value,s=l.children||l.childrenField,c=Ce(rt);return a().isArray(t)||(t=[t]),t.map((e=>{d.tableFullColumn.forEach((t=>{const{field:o,editRender:n}=t;if(o&&!a().has(e,o)&&!e[o]){let l=null;if(n){const{defaultValue:e}=n;a().isFunction(e)?l=e({column:t}):a().isUndefined(e)||(l=e)}a().set(e,o,l)}}));const t=[r.labelField,i.checkField,i.labelField,n.labelField];return t.forEach((t=>{t&&q(a().get(e,t))&&a().set(e,t,null)})),o&&l.lazy&&a().isUndefined(e[s])&&(e[s]=null),q(a().get(e,c))&&a().set(e,c,we()),e}))},handleTableData(e){const{scrollYLoad:t}=u,{scrollYStore:o,fullDataRowIdData:n}=d;let l=d.afterFullData;e&&(St(),l=Et());const r=t?l.slice(o.startIndex,o.endIndex):l.slice(0);return r.forEach(((e,t)=>{const o=ye(rt,e),l=n[o];l&&(l.$index=t)})),u.tableData=r,(0,g.nextTick)()},cacheRowMap(t){const{treeConfig:o}=e,n=Ye.value;let{fullDataRowIdData:l,fullAllDataRowIdData:r,tableFullData:i,tableFullTreeData:s}=d;const c=n.children||n.childrenField,u=n.hasChild||n.hasChildField,p=Ce(rt),f=o&&n.lazy,m=(e,n,i,s,d,m)=>{let h=ye(rt,e);const g=o&&s?Oe(s):n+1,v=m?m.length-1:0;q(h)&&(h=we(),a().set(e,p,h)),f&&e[u]&&a().isUndefined(e[c])&&(e[c]=null);const x={row:e,rowid:h,seq:g,index:o&&d?-1:n,_index:-1,$index:-1,items:i,parent:d,level:v};t&&(l[h]=x),r[h]=x};t&&(l=d.fullDataRowIdData={}),r=d.fullAllDataRowIdData={},o?a().eachTree(s,m,{children:c}):i.forEach(m)},cacheSourceMap(t){const{treeConfig:o}=e,n=Ye.value;let{sourceDataRowIdData:l}=d;const r=a().clone(t,!0),i=Ce(rt);l=d.sourceDataRowIdData={};const s=e=>{let t=ye(rt,e);q(t)&&(t=we(),a().set(e,i,t)),l[t]=e};if(o){const e=n.children||n.childrenField;a().eachTree(r,s,{children:n.transform?n.mapChildrenField:e})}else r.forEach(s);d.tableSourceData=r},analyColumnWidth(){const{tableFullColumn:e}=d,t=K.value,{width:o,minWidth:n}=t,l=[],r=[],a=[],i=[],s=[],c=[];e.forEach((e=>{o&&!e.width&&(e.width=o),n&&!e.minWidth&&(e.minWidth=n),e.visible&&(e.resizeWidth?l.push(e):J(e.width)?r.push(e):Q(e.width)?i.push(e):J(e.minWidth)?a.push(e):Q(e.minWidth)?s.push(e):c.push(e))})),Object.assign(u.columnStore,{resizeList:l,pxList:r,pxMinList:a,scaleList:i,scaleMinList:s,autoList:c})},saveCustomResizable(t){const{id:o,customConfig:n}=e,l=Je.value,{collectColumn:r}=d,{storage:i}=l,s=!0===i||i&&i.resizable;if(n&&s){const e=st($a);let n;if(!o)return void f("vxe.error.reqProp",["id"]);t||(n=a().isPlainObject(e[o])?e[o]:{},a().eachTree(r,(e=>{if(e.resizeWidth){const t=e.getKey();t&&(n[t]=e.renderWidth)}}))),e[o]=a().isEmpty(n)?void 0:n,localStorage.setItem($a,a().toJSONString(e))}},saveCustomFixed(){const{id:t,customConfig:o}=e,{collectColumn:n}=d,l=Je.value,{storage:r}=l,i=!0===r||r&&r.fixed;if(o&&i){const e=st(Da),o=[];if(!t)return void f("vxe.error.reqProp",["id"]);a().eachTree(n,(e=>{if(e.fixed&&e.fixed!==e.defaultFixed){const t=e.getKey();t&&o.push(`${t}|${e.fixed}`)}})),e[t]=o.join(",")||void 0,localStorage.setItem(Da,a().toJSONString(e))}},saveCustomVisible(){const{id:t,customConfig:o}=e,{collectColumn:n}=d,l=Je.value,{checkMethod:r,storage:i}=l,s=!0===i||i&&i.visible;if(o&&s){const e=st(Ia),o=[],l=[];if(!t)return void f("vxe.error.reqProp",["id"]);a().eachTree(n,(e=>{if(!r||r({column:e}))if(!e.visible&&e.defaultVisible){const t=e.getKey();t&&o.push(t)}else if(e.visible&&!e.defaultVisible){const t=e.getKey();t&&l.push(t)}})),e[t]=[o.join(",")].concat(l.length?[l.join(",")]:[]).join("|")||void 0,localStorage.setItem(Ia,a().toJSONString(e))}},handleCustom(){return h.saveCustomVisible(),h.analyColumnWidth(),m.refreshColumn()},handleUpdateDataQueue(){u.upDataFlag++},handleRefreshColumnQueue(){u.reColumnFlag++},preventEvent(e,t,o,n,l){const r=oo.interceptor.get(t);let a;return r.some((t=>!1===t(Object.assign({$grid:A,$table:rt,$event:e},o))))||n&&(a=n()),l&&l(),a},checkSelectionStatus(){const{treeConfig:t}=e,{selectCheckboxMaps:o,treeIndeterminateMaps:n}=u,{afterFullData:l}=d,r=de.value,{checkField:i,checkStrictly:s,checkMethod:c}=r,p=r.indeterminateField||r.halfField;if(!s){const e=[],r=[];let s=!1,d=!1,f=!1;i?(s=l.every(c?t=>c({row:t})?!!a().get(t,i)&&(r.push(t),!0):(e.push(t),!0):e=>a().get(e,i)),d=s&&l.length!==e.length,f=t?p?!d&&l.some((e=>a().get(e,i)||a().get(e,p)||!!n[ye(rt,e)])):!d&&l.some((e=>a().get(e,i)||!!n[ye(rt,e)])):p?!d&&l.some((e=>a().get(e,i)||a().get(e,p))):!d&&l.some((e=>a().get(e,i)))):(s=l.every(c?t=>c({row:t})?!!o[ye(rt,t)]&&(r.push(t),!0):(e.push(t),!0):e=>o[ye(rt,e)]),d=s&&l.length!==e.length,f=t?!d&&l.some((e=>{const t=ye(rt,e);return n[t]||o[t]})):!d&&l.some((e=>o[ye(rt,e)]))),u.isAllSelected=d,u.isIndeterminate=f}},handleSelectRow({row:t},o,n){const{treeConfig:l}=e,{selectCheckboxMaps:r,treeIndeterminateMaps:i}=u,s={...r},{afterFullData:c}=d,p=Ye.value,f=p.children||p.childrenField,m=de.value,{checkField:g,checkStrictly:v,checkMethod:x}=m,b=m.indeterminateField||m.halfField,w=ye(rt,t);if(g)if(l&&!v){-1===o?(i[w]||(b&&a().set(t,b,!0),i[w]=t),a().set(t,g,!1)):a().eachTree([t],(e=>{(rt.eqRow(e,t)||n||!x||x({row:e}))&&(a().set(e,g,o),b&&a().set(t,b,!1),delete i[ye(rt,e)],Nt(t,o))}),{children:f});const e=a().findTree(c,(e=>rt.eqRow(e,t)),{children:f});if(e&&e.parent){let t;const l=[],r={};!n&&x?e.items.forEach((e=>{if(x({row:e})){const t=ye(rt,e);r[t]=e,l.push(e)}})):e.items.forEach((e=>{const t=ye(rt,e);r[t]=e,l.push(e)}));const c=a().find(e.items,(e=>!!i[ye(rt,e)]));if(c)t=-1;else{const n=[];e.items.forEach((e=>{a().get(e,g)&&n.push(e)})),t=n.filter((e=>r[ye(rt,e)])).length===l.length||!(!n.length&&-1!==o)&&-1}return u.selectCheckboxMaps=s,h.handleSelectRow({row:e.parent},t,n)}}else(n||!x||x({row:t}))&&(a().set(t,g,o),Nt(t,o));else if(l&&!v){-1===o?(i[w]||(b&&a().set(t,b,!0),i[w]=t),s[w]&&delete s[w]):a().eachTree([t],(e=>{const l=ye(rt,e);(rt.eqRow(e,t)||n||!x||x({row:e}))&&(o?s[l]=e:s[l]&&delete s[l],b&&a().set(t,b,!1),delete i[ye(rt,e)],Nt(t,o))}),{children:f});const e=a().findTree(c,(e=>rt.eqRow(e,t)),{children:f});if(e&&e.parent){let t;const l=[],r={};!n&&x?e.items.forEach((e=>{if(x({row:e})){const t=ye(rt,e);r[t]=e,l.push(e)}})):e.items.forEach((e=>{const t=ye(rt,e);r[t]=e,l.push(e)}));const c=a().find(e.items,(e=>!!i[ye(rt,e)]));if(c)t=-1;else{const n=[];e.items.forEach((e=>{const t=ye(rt,e);s[t]&&n.push(e)})),t=n.filter((e=>r[ye(rt,e)])).length===l.length||!(!n.length&&-1!==o)&&-1}return u.selectCheckboxMaps=s,h.handleSelectRow({row:e.parent},t,n)}}else(n||!x||x({row:t}))&&(o?s[w]||(s[w]=t):s[w]&&delete s[w],Nt(t,o));u.selectCheckboxMaps=s,h.checkSelectionStatus()},triggerHeaderHelpEvent(e,t){const{column:o}=t,n=o.titlePrefix||o.titleHelp;if(n.content||n.message){const{tooltipStore:t}=u,o=z(n.content||n.message);To(!0),t.visible=!0,t.currOpts={...n,content:null},(0,g.nextTick)((()=>{const t=x.value;t&&t.open(e.currentTarget,o)}))}},triggerHeaderTooltipEvent(e,t){const{tooltipStore:o}=u,{column:n}=t,l=e.currentTarget;To(!0),o.column===n&&o.visible||Eo(e,l,l,null,t)},triggerBodyTooltipEvent(t,o){const{editConfig:n}=e,{editStore:l}=u,{tooltipStore:r}=u,a=he.value,{actived:i}=l,{row:s,column:c}=o,d=t.currentTarget;if(To(r.column!==c||r.row!==s),c.editRender&&P(n)){if("row"===a.mode&&i.row===s)return;if(i.row===s&&i.column===c)return}if(r.column!==c||r.row!==s||!r.visible){let e,n;c.treeNode?(e=d.querySelector(".vxe-tree-cell"),"html"===c.type&&(n=d.querySelector(".vxe-cell--html"))):n=d.querySelector("html"===c.type?".vxe-cell--html":".vxe-cell--label"),Eo(t,d,e||d.children[0],n,o)}},triggerFooterTooltipEvent(e,t){const{column:o}=t,{tooltipStore:n}=u,l=e.currentTarget;To(n.column!==o||!!n.row),n.column===o&&n.visible||Eo(e,l,l.querySelector(".vxe-cell--item")||l.children[0],null,t)},handleTargetLeaveEvent(){const e=pe.value;let t=x.value;t&&t.setActived(!1),e.enterable?d.tooltipTimeout=setTimeout((()=>{t=x.value,t&&!t.isActived()&&m.closeTooltip()}),e.leaveDelay):m.closeTooltip()},triggerHeaderCellClickEvent(t,o){const{_lastResizeTime:n}=d,l=xe.value,r=K.value,{column:a}=o,i=t.currentTarget,s=n&&n>Date.now()-300,c=ce(t,i,"vxe-cell--sort").flag,u=ce(t,i,"vxe-cell--filter").flag;"cell"!==l.trigger||s||c||u||h.triggerSortEvent(t,a,it(a)),m.dispatchEvent("header-cell-click",Object.assign({triggerResizable:s,triggerSort:c,triggerFilter:u,cell:i},o),t),(r.isCurrent||e.highlightCurrentColumn)&&m.setCurrentColumn(a)},triggerHeaderCellDblclickEvent(e,t){m.dispatchEvent("header-cell-dblclick",Object.assign({cell:e.currentTarget},t),e)},triggerCellClickEvent(t,o){const{highlightCurrentRow:n,editConfig:l}=e,{editStore:r}=u,a=Xe.value,i=he.value,s=Ye.value,c=ue.value,d=de.value,p=Z.value,{actived:f}=r,{row:g,column:v}=o,{type:x,treeNode:b}=v,w="radio"===x,C="checkbox"===x,y="expand"===x,T=t.currentTarget,E=w&&ce(t,T,"vxe-cell--radio").flag,S=C&&ce(t,T,"vxe-cell--checkbox").flag,k=b&&ce(t,T,"vxe-tree--btn-wrapper").flag,R=y&&ce(t,T,"vxe-table--expanded").flag;o=Object.assign({cell:T,triggerRadio:E,triggerCheckbox:S,triggerTreeNode:k,triggerExpandNode:R},o),S||E||(!R&&("row"===a.trigger||y&&"cell"===a.trigger)&&h.triggerRowExpandEvent(t,o),("row"===s.trigger||b&&"cell"===s.trigger)&&h.triggerTreeExpandEvent(t,o)),k||(R||((p.isCurrent||n)&&(S||E||h.triggerCurrentRowEvent(t,o)),!E&&("row"===c.trigger||w&&"cell"===c.trigger)&&h.triggerRadioRowEvent(t,o),!S&&("row"===d.trigger||C&&"cell"===d.trigger)&&h.handleToggleCheckRowEvent(t,o)),P(l)&&("manual"===i.trigger?f.args&&f.row===g&&v!==f.column&&Ot(t,o):f.args&&g===f.row&&v===f.column||("click"===i.trigger||"dblclick"===i.trigger&&"row"===i.mode&&f.row===g)&&Ot(t,o))),m.dispatchEvent("cell-click",o,t)},triggerCellDblclickEvent(t,o){const{editConfig:n}=e,{editStore:l}=u,r=he.value,{actived:a}=l,i=t.currentTarget;o=Object.assign({cell:i},o),P(n)&&"dblclick"===r.trigger&&(a.args&&t.currentTarget===a.args.cell||("row"===r.mode?Rt("blur").catch((e=>e)).then((()=>{rt.handleActived(o,t).then((()=>Rt("change"))).catch((e=>e))})):"cell"===r.mode&&rt.handleActived(o,t).then((()=>Rt("change"))).catch((e=>e)))),m.dispatchEvent("cell-dblclick",o,t)},handleToggleCheckRowEvent(e,t){const{selectCheckboxMaps:o}=u,n=de.value,{checkField:l}=n,{row:r}=t;let i=!1;i=l?!a().get(r,l):!o[ye(rt,r)],e?h.triggerCheckRowEvent(e,t,i):h.handleSelectRow(t,i)},triggerCheckRowEvent(t,o,n){const l=de.value,{row:r}=o,{afterFullData:a}=d,{checkMethod:i}=l;if(l.isShiftKey&&t.shiftKey&&!e.treeConfig){const e=m.getCheckboxRecords();if(e.length){const n=e[0],l=m.getVTRowIndex(r),i=m.getVTRowIndex(n);if(l!==i){m.setAllCheckboxRow(!1);const e=l<i?a.slice(l,i+1):a.slice(i,l+1);return At(e,!0,!1),void m.dispatchEvent("checkbox-range-select",Object.assign({rangeRecords:e},o),t)}}}i&&!i({row:r})||(h.handleSelectRow(o,n),m.dispatchEvent("checkbox-change",Object.assign({records:m.getCheckboxRecords(),reserves:m.getCheckboxReserveRecords(),indeterminates:m.getCheckboxIndeterminateRecords(),checked:n},o),t))},triggerCheckAllEvent(e,t){Pt(t),e&&m.dispatchEvent("checkbox-all",{records:m.getCheckboxRecords(),reserves:m.getCheckboxReserveRecords(),indeterminates:m.getCheckboxIndeterminateRecords(),checked:t},e)},triggerRadioRowEvent(e,t){const{selectRadioRow:o}=u,{row:n}=t,l=ue.value;let r=n,a=o!==r;a?Lt(r):l.strict||(a=o===r,a&&(r=null,m.clearRadioRow())),a&&m.dispatchEvent("radio-change",{oldValue:o,newValue:r,...t},e)},triggerCurrentRowEvent(e,t){const{currentRow:o}=u,{row:n}=t,l=o!==n;m.setCurrentRow(n),l&&m.dispatchEvent("current-change",{oldValue:o,newValue:n,...t},e)},triggerRowExpandEvent(e,t){const{rowExpandLazyLoadedMaps:o,expandColumn:n}=u,l=Xe.value,{row:r}=t,{lazy:a}=l,i=ye(rt,r);if(!a||!o[i]){const t=!m.isExpandByRow(r),o=m.getColumnIndex(n),l=m.getVMColumnIndex(n);m.setRowExpand(r,t),m.dispatchEvent("toggle-row-expand",{expanded:t,column:n,columnIndex:o,$columnIndex:l,row:r,rowIndex:m.getRowIndex(r),$rowIndex:m.getVMRowIndex(r)},e)}},triggerTreeExpandEvent(e,t){const{treeExpandLazyLoadedMaps:o}=u,n=Ye.value,{row:l,column:r}=t,{lazy:a}=n,i=ye(rt,l);if(!a||!o[i]){const t=!m.isTreeExpandByRow(l),o=m.getColumnIndex(r),n=m.getVMColumnIndex(r);m.setTreeExpand(l,t),m.dispatchEvent("toggle-tree-expand",{expanded:t,column:r,columnIndex:o,$columnIndex:n,row:l},e)}},triggerSortEvent(t,o,n){const{mouseConfig:l}=e,r=xe.value,a=Se.value,{field:i,sortable:s}=o;if(s){n&&o.order!==n?m.sort({field:i,order:n}):m.clearSort(r.multiple?o:null);const e={$table:rt,$event:t,column:o,field:i,property:i,order:o.order,sortList:m.getSortColumns(),sortTime:o.sortTime};l&&a.area&&rt.handleSortEvent&&rt.handleSortEvent(t,e),m.dispatchEvent("sort-change",e,t)}},triggerScrollXEvent(){Zt()},triggerScrollYEvent(e){const{scrollYStore:t}=d,{adaptive:o,offsetSize:n,visibleSize:l}=t;Ma&&o&&2*n+l<=40?ro(e):so(e)},scrollToTreeRow(t){const{treeConfig:o}=e,{tableFullData:n}=d,l=[];if(o){const e=Ye.value,o=e.children||e.childrenField,r=a().findTree(n,(e=>rt.eqRow(e,t)),{children:o});if(r){const e=r.nodes;e.forEach(((t,o)=>{o<e.length-1&&!m.isTreeExpandByRow(t)&&l.push(m.setTreeExpand(t,!0))}))}}return Promise.all(l).then((()=>He(rt,t)))},updateScrollYStatus:to,updateScrollXSpace(){const{isGroup:e,scrollXLoad:t,scrollbarWidth:o}=u,{visibleColumn:n,scrollXStore:l,elemStore:r,tableWidth:a}=d,i=T.value,s=E.value,c=S.value,p=s?s.$el:null;if(p){const s=i?i.$el:null,u=c?c.$el:null,d=s?s.querySelector(".vxe-table--header"):null,f=p.querySelector(".vxe-table--body"),m=u?u.querySelector(".vxe-table--footer"):null,h=n.slice(0,l.startIndex).reduce(((e,t)=>e+t.renderWidth),0);let v="";t&&(v=`${h}px`),d&&(d.style.marginLeft=e?"":v),f.style.marginLeft=v,m&&(m.style.marginLeft=v);const x=["main"];x.forEach((e=>{const n=["header","body","footer"];n.forEach((n=>{const l=r[`${e}-${n}-xSpace`],i=l?l.value:null;i&&(i.style.width=t?`${a+("header"===n?o:0)}px`:"")}))})),(0,g.nextTick)(kt)}},updateScrollYSpace(){const{scrollYLoad:e}=u,{scrollYStore:t,elemStore:o,afterFullData:n}=d,{startIndex:l,rowHeight:r}=t,a=n.length*r,i=Math.max(0,l*r),s=["main","left","right"];let c="",p="";e&&(c=`${i}px`,p=`${a}px`),s.forEach((e=>{const t=["header","body","footer"],n=o[`${e}-body-table`],l=n?n.value:null;l&&(l.style.marginTop=c),t.forEach((t=>{const n=o[`${e}-${t}-ySpace`],l=n?n.value:null;l&&(l.style.height=p)}))})),(0,g.nextTick)(kt)},updateScrollXData(){(0,g.nextTick)((()=>{Kt(),h.updateScrollXSpace()}))},updateScrollYData(){(0,g.nextTick)((()=>{h.handleTableData(),h.updateScrollYSpace()}))},checkScrolling(){const e=D.value,t=F.value,o=E.value,n=o?o.$el:null;n&&(e&&(n.scrollLeft>0?oe(e,"scrolling--middle"):te(e,"scrolling--middle")),t&&(n.clientWidth<n.scrollWidth-Math.ceil(n.scrollLeft)?oe(t,"scrolling--middle"):te(t,"scrolling--middle")))},updateZindex(){e.zIndex?d.tZindex=e.zIndex:d.tZindex<H()&&(d.tZindex=j())},handleCheckedCheckboxRow:At,triggerHoverEvent(e,{row:t}){h.setHoverRow(t)},setHoverRow(e){const t=ye(rt,e),o=v.value;h.clearHoverRow(),o&&a().arrayEach(o.querySelectorAll(`[rowid="${t}"]`),(e=>oe(e,"row--hover"))),d.hoverRow=e},clearHoverRow(){const e=v.value;e&&a().arrayEach(e.querySelectorAll(".vxe-body--row.row--hover"),(e=>te(e,"row--hover"))),d.hoverRow=null},getCell(e,t){const o=ye(rt,e),n=E.value,l=R.value,r=$.value;let a;return t&&(t.fixed&&("left"===t.fixed?l&&(a=l.$el):r&&(a=r.$el)),a||(a=n.$el),a)?a.querySelector(`.vxe-body--row[rowid="${o}"] .${t.id}`):null},getCellLabel(e,t){const o=t.formatter,n=Me(e,t);let l=n;if(o){let r;const{fullAllDataRowIdData:i}=d,s=ye(rt,e),c=t.id,u=i[s];if(u&&(r=u.formatData,r||(r=i[s].formatData={}),u&&r[c]&&r[c].value===n))return r[c].label;const p={cellValue:n,row:e,rowIndex:m.getRowIndex(e),column:t,columnIndex:m.getColumnIndex(t)};if(a().isString(o)){const e=oo.formats.get(o);l=e&&e.cellFormatMethod?e.cellFormatMethod(p):""}else if(a().isArray(o)){const e=oo.formats.get(o[0]);l=e&&e.cellFormatMethod?e.cellFormatMethod(p,...o.slice(1)):""}else l=o(p);r&&(r[c]={value:n,label:l})}return l},findRowIndexOf(e,t){return t?a().findIndexOf(e,(e=>rt.eqRow(e,t))):-1},eqRow(e,t){return!(!e||!t)&&(e===t||ye(rt,e)===ye(rt,t))}},"openExport,openPrint,exportData,openImport,importData,saveFile,readFile,importByFile,print".split(",").forEach((e=>{rt[e]=function(){f("vxe.error.reqModule",["Export"])}})),"clearValidate,fullValidate,validate".split(",").forEach((e=>{rt[e]=function(){f("vxe.error.reqModule",["Validator"])}})),Object.assign(rt,m,h);const So=t=>{const{showHeader:o,showFooter:n}=e,{tableData:l,tableColumn:r,tableGroupColumn:a,columnStore:i,footerTableData:s}=u,c="left"===t,d=c?i.leftList:i.rightList;return(0,g.h)("div",{ref:c?D:F,class:`vxe-table--fixed-${t}-wrapper`},[o?(0,g.h)(Ta,{ref:c?k:M,fixedType:t,tableData:l,tableColumn:r,tableGroupColumn:a,fixedColumn:d}):(0,g.createCommentVNode)(),(0,g.h)(va,{ref:c?R:$,fixedType:t,tableData:l,tableColumn:r,fixedColumn:d}),n?(0,g.h)(Oa,{ref:c?O:I,footerTableData:s,tableColumn:r,fixedColumn:d,fixedType:t}):(0,g.createCommentVNode)()])},ko=()=>{const t=Ge.value,n={$table:rt};if(o.empty)return o.empty(n);{const e=t.name?oo.renderer.get(t.name):null,o=e?e.renderEmpty:null;if(o)return We(o(t,n))}return z(e.emptyText)||c.i18n("vxe.table.emptyText")};function Ro(){const e=v.value;e&&e.clientWidth&&e.clientHeight&&m.recalculate()}const Oo=(0,g.ref)(0);(0,g.watch)((()=>e.data?e.data.length:-1),(()=>{Oo.value++})),(0,g.watch)((()=>e.data),(()=>{Oo.value++})),(0,g.watch)(Oo,(()=>{const{inited:t,initStatus:o}=d;Xt(e.data||[]).then((()=>{const{scrollXLoad:e,scrollYLoad:n,expandColumn:l}=u;d.inited=!0,d.initStatus=!0,o||Yt(),t||Gt(),(e||n)&&l&&p("vxe.error.scrollErrProp",["column.type=expand"]),m.recalculate()}))}));const Mo=(0,g.ref)(0);(0,g.watch)((()=>u.staticColumns.length),(()=>{Mo.value++})),(0,g.watch)((()=>u.staticColumns),(()=>{Mo.value++})),(0,g.watch)(Mo,(()=>{eo(u.staticColumns)}));const $o=(0,g.ref)(0);(0,g.watch)((()=>u.tableColumn.length),(()=>{$o.value++})),(0,g.watch)((()=>u.tableColumn),(()=>{$o.value++})),(0,g.watch)($o,(()=>{h.analyColumnWidth()})),(0,g.watch)((()=>u.upDataFlag),(()=>{(0,g.nextTick)((()=>{m.updateData()}))})),(0,g.watch)((()=>u.reColumnFlag),(()=>{(0,g.nextTick)((()=>{m.refreshColumn()}))})),(0,g.watch)((()=>e.showHeader),(()=>{(0,g.nextTick)((()=>{m.recalculate(!0).then((()=>m.refreshScroll()))}))})),(0,g.watch)((()=>e.showFooter),(()=>{(0,g.nextTick)((()=>{m.recalculate(!0).then((()=>m.refreshScroll()))}))})),(0,g.watch)((()=>e.height),(()=>{(0,g.nextTick)((()=>m.recalculate(!0)))})),(0,g.watch)((()=>e.maxHeight),(()=>{(0,g.nextTick)((()=>m.recalculate(!0)))})),(0,g.watch)((()=>e.syncResize),(e=>{e&&(Ro(),(0,g.nextTick)((()=>{Ro(),setTimeout((()=>Ro()))})))}));const Io=(0,g.ref)(0);(0,g.watch)((()=>e.mergeCells?e.mergeCells.length:-1),(()=>{Io.value++})),(0,g.watch)((()=>e.mergeCells),(()=>{Io.value++})),(0,g.watch)(Io,(()=>{m.clearMergeCells(),(0,g.nextTick)((()=>{e.mergeCells&&m.setMergeCells(e.mergeCells)}))}));const Do=(0,g.ref)(0);let No;(0,g.watch)((()=>e.mergeFooterItems?e.mergeFooterItems.length:-1),(()=>{Do.value++})),(0,g.watch)((()=>e.mergeFooterItems),(()=>{Do.value++})),(0,g.watch)(Do,(()=>{m.clearMergeFooterItems(),(0,g.nextTick)((()=>{e.mergeFooterItems&&m.setMergeFooterItems(e.mergeFooterItems)}))})),oo.hooks.forEach((e=>{const{setupTable:t}=e;if(t){const e=t(rt);e&&a().isObject(e)&&Object.assign(rt,e)}})),h.preventEvent(null,"created",{$table:rt}),(0,g.onActivated)((()=>{m.recalculate().then((()=>m.refreshScroll())),h.preventEvent(null,"activated",{$table:rt})})),(0,g.onDeactivated)((()=>{d.isActivated=!1,h.preventEvent(null,"deactivated",{$table:rt})})),(0,g.onMounted)((()=>{(0,g.nextTick)((()=>{const{data:t,treeConfig:o,showOverflow:n}=e,{scrollXStore:l,scrollYStore:r}=d,i=X.value,s=he.value,c=Ye.value,u=ue.value,g=de.value,x=Xe.value,b=Z.value;{e.rowId||b.keyField||!(g.reserve||g.checkRowKeys||u.reserve||u.checkRowKey||x.expandRowKeys||c.expandRowKeys)||p("vxe.error.reqProp",["row-config.keyField"]),e.editConfig&&(s.showStatus||s.showUpdateStatus||s.showInsertStatus)&&!e.keepSource&&p("vxe.error.reqProp",["keep-source"]),!o||!c.showLine&&!c.line||(e.rowKey||b.useKey)&&n||p("vxe.error.reqProp",["row-config.useKey | show-overflow"]),o&&e.stripe&&p("vxe.error.noTree",["stripe"]),e.showFooter&&!e.footerMethod&&p("vxe.error.reqProp",["footer-method"]);const{exportConfig:t,importConfig:l}=e,r=ze.value,i=qe.value;l&&i.types&&!i.importMethod&&!a().includeArrays(oo.globalConfs.importTypes,i.types)&&p("vxe.error.errProp",[`export-config.types=${i.types.join(",")}`,i.types.filter((e=>a().includes(oo.globalConfs.importTypes,e))).join(",")||oo.globalConfs.importTypes.join(",")]),t&&r.types&&!r.exportMethod&&!a().includeArrays(oo.globalConfs.exportTypes,r.types)&&p("vxe.error.errProp",[`export-config.types=${r.types.join(",")}`,r.types.filter((e=>a().includes(oo.globalConfs.exportTypes,e))).join(",")||oo.globalConfs.exportTypes.join(",")])}{const t=Je.value,o=Se.value,n=Z.value;if(!e.id&&e.customConfig&&(!0===t.storage||t.storage&&t.storage.resizable||t.storage&&t.storage.visible)&&f("vxe.error.reqProp",["id"]),e.treeConfig&&g.range&&f("vxe.error.noTree",["checkbox-config.range"]),n.height&&!e.showOverflow&&p("vxe.error.notProp",["table.show-overflow"]),!rt.handleUpdateCellAreas&&(e.clipConfig&&p("vxe.error.notProp",["clip-config"]),e.fnrConfig&&p("vxe.error.notProp",["fnr-config"]),o.area))return void f("vxe.error.notProp",["mouse-config.area"]);e.treeConfig&&c.children&&p("vxe.error.delProp",["tree-config.children","tree-config.childrenField"]),e.treeConfig&&c.line&&p("vxe.error.delProp",["tree-config.line","tree-config.showLine"]),o.area&&o.selected&&p("vxe.error.errConflicts",["mouse-config.area","mouse-config.selected"]),o.area&&g.range&&p("vxe.error.errConflicts",["mouse-config.area","checkbox-config.range"]),e.treeConfig&&o.area&&f("vxe.error.noTree",["mouse-config.area"]),e.editConfig&&s.activeMethod&&p("vxe.error.delProp",["edit-config.activeMethod","edit-config.beforeEditMethod"]),e.treeConfig&&g.isShiftKey&&f("vxe.error.errConflicts",["tree-config","checkbox-config.isShiftKey"]),g.halfField&&p("vxe.error.delProp",["checkbox-config.halfField","checkbox-config.indeterminateField"])}if(e.editConfig&&!rt.insert&&f("vxe.error.reqModule",["Edit"]),e.editRules&&!rt.validate&&f("vxe.error.reqModule",["Validator"]),(g.range||e.keyboardConfig||e.mouseConfig)&&!rt.triggerCellMousedownEvent&&f("vxe.error.reqModule",["Keyboard"]),(e.printConfig||e.importConfig||e.exportConfig)&&!rt.exportData&&f("vxe.error.reqModule",["Export"]),Object.assign(r,{startIndex:0,endIndex:0,visibleSize:0,adaptive:!1!==i.adaptive}),Object.assign(l,{startIndex:0,endIndex:0,visibleSize:0}),Xt(t||[]).then((()=>{t&&t.length&&(d.inited=!0,d.initStatus=!0,Yt(),Gt()),kt()})),e.autoResize){const t=ne.value,{refreshDelay:o}=t,n=v.value,l=h.getParentElem(),r=o?a().throttle((()=>m.recalculate(!0)),o,{leading:!0,trailing:!0}):null;No=sa(r?()=>{e.autoResize&&requestAnimationFrame(r)}:()=>{e.autoResize&&m.recalculate(!0)}),n&&No.observe(n),l&&No.observe(l)}})),yo.on(rt,"paste",vo),yo.on(rt,"copy",xo),yo.on(rt,"cut",bo),yo.on(rt,"mousedown",uo),yo.on(rt,"blur",po),yo.on(rt,"mousewheel",fo),yo.on(rt,"keydown",ho),yo.on(rt,"resize",Co),rt.handleGlobalContextmenuEvent&&yo.on(rt,"contextmenu",rt.handleGlobalContextmenuEvent),h.preventEvent(null,"mounted",{$table:rt})})),(0,g.onBeforeUnmount)((()=>{No&&No.disconnect(),m.closeFilter(),rt.closeMenu&&rt.closeMenu(),h.preventEvent(null,"beforeUnmount",{$table:rt})})),(0,g.onUnmounted)((()=>{yo.off(rt,"paste"),yo.off(rt,"copy"),yo.off(rt,"cut"),yo.off(rt,"mousedown"),yo.off(rt,"blur"),yo.off(rt,"mousewheel"),yo.off(rt,"keydown"),yo.off(rt,"resize"),yo.off(rt,"contextmenu"),h.preventEvent(null,"unmounted",{$table:rt})}));const Lo=()=>{const{loading:t,stripe:n,showHeader:a,height:s,treeConfig:d,mouseConfig:p,showFooter:f,highlightCell:m,highlightHoverRow:h,highlightHoverColumn:k,editConfig:R,editRules:O}=e,{isGroup:M,overflowX:$,overflowY:I,scrollXLoad:D,scrollYLoad:F,scrollbarHeight:P,tableData:V,tableColumn:j,tableGroupColumn:H,footerTableData:B,initStore:z,columnStore:W,filterStore:q}=u,{leftList:U,rightList:X}=W,Y=o.loading,G=fe.value,J=_.value,Q=Ye.value,ee=Z.value,te=K.value,oe=i.value,ne=tt.value,le=Se.value,re=me.value,ae=Ke.value,ie=Ve.value;return(0,g.h)("div",{ref:v,class:["vxe-table","vxe-table--render-default",`tid_${r}`,`border--${ne}`,{[`size--${oe}`]:oe,[`vaild-msg--${J.msgMode}`]:!!O,"vxe-editable":!!R,"old-cell-valid":O&&"obsolete"===c.cellVaildMode,"cell--highlight":m,"cell--selected":p&&le.selected,"cell--area":p&&le.area,"row--highlight":ee.isHover||h,"column--highlight":te.isHover||k,"is--header":a,"is--footer":f,"is--group":M,"is--tree-line":d&&(Q.showLine||Q.line),"is--fixed-left":U.length,"is--fixed-right":X.length,"is--animat":!!e.animat,"is--round":e.round,"is--stripe":!d&&n,"is--loading":t,"is--empty":!t&&!V.length,"is--scroll-y":I,"is--scroll-x":$,"is--virtual-x":D,"is--virtual-y":F}],onKeydown:mo},[(0,g.h)("div",{class:"vxe-table-slots"},o.default?o.default({}):[]),(0,g.h)("div",{class:"vxe-table--render-wrapper"},[(0,g.h)("div",{class:"vxe-table--main-wrapper"},[a?(0,g.h)(Ta,{ref:T,tableData:V,tableColumn:j,tableGroupColumn:H}):(0,g.createCommentVNode)(),(0,g.h)(va,{ref:E,tableData:V,tableColumn:j}),f?(0,g.h)(Oa,{ref:S,footerTableData:B,tableColumn:j}):(0,g.createCommentVNode)()]),(0,g.h)("div",{class:"vxe-table--fixed-wrapper"},[U&&U.length&&$?So("left"):(0,g.createCommentVNode)(),X&&X.length&&$?So("right"):(0,g.createCommentVNode)()])]),(0,g.h)("div",{ref:L,class:"vxe-table--empty-placeholder"},[(0,g.h)("div",{class:"vxe-table--empty-content"},ko())]),(0,g.h)("div",{class:"vxe-table--border-line"}),(0,g.h)("div",{ref:N,class:"vxe-table--resizable-bar",style:$?{"padding-bottom":`${P}px`}:null}),(0,g.h)(Po,{class:"vxe-table--loading",modelValue:t,icon:ae.icon,text:ae.text},Y?{default:()=>Y({$table:rt,$grid:A})}:{}),z.filter?(0,g.h)((0,g.resolveComponent)("vxe-table-filter"),{ref:C,filterStore:q}):(0,g.createCommentVNode)(),z.import&&e.importConfig?(0,g.h)((0,g.resolveComponent)("vxe-import-panel"),{defaultOptions:u.importParams,storeData:u.importStore}):(0,g.createCommentVNode)(),z.export&&(e.exportConfig||e.printConfig)?(0,g.h)((0,g.resolveComponent)("vxe-export-panel"),{defaultOptions:u.exportParams,storeData:u.exportStore}):(0,g.createCommentVNode)(),ie?(0,g.h)((0,g.resolveComponent)("vxe-table-context-menu"),{ref:y}):(0,g.createCommentVNode)(),l?(0,g.h)((0,g.resolveComponent)("vxe-tooltip"),{ref:b,isArrow:!1,enterable:!1}):(0,g.createCommentVNode)(),l?(0,g.h)((0,g.resolveComponent)("vxe-tooltip"),{ref:x,...G}):(0,g.createCommentVNode)(),l&&e.editRules&&J.showMessage&&("default"===J.message?!s:"tooltip"===J.message)?(0,g.h)((0,g.resolveComponent)("vxe-tooltip"),{ref:w,class:[{"old-cell-valid":O&&"obsolete"===c.cellVaildMode},"vxe-table--valid-error"],..."tooltip"===J.message||1===V.length?re:{}}):(0,g.createCommentVNode)()])};return rt.renderVN=Lo,(0,g.provide)("xecolgroup",null),(0,g.provide)("$xetable",rt),rt},render(){return this.renderVN()}});const La=Object.assign(Na,{install:function(e){e.component(Na.name,Na)}}),Aa=La;uo.component(Na.name,Na);var Pa={vxe:{base:{pleaseInput:"请输入",pleaseSelect:"请选择"},loading:{text:"加载中..."},error:{groupFixed:"如果使用分组表头，固定列必须按组设置",groupMouseRange:'分组表头与 "{0}" 不能同时使用，这可能会出现错误',groupTag:'分组列头应该使用 "{0}" 而不是 "{1}"，这可能会出现错误',scrollErrProp:'启用虚拟滚动后不支持该参数 "{0}"',errConflicts:'参数 "{0}" 与 "{1}" 有冲突',unableInsert:"无法插入到指定位置，请检查参数是否正确",useErr:'安装 "{0}" 模块时发生错误，可能顺序不正确，依赖的模块需要在 Table 之前安装',barUnableLink:"工具栏无法关联表格",expandContent:'展开行的插槽应该是 "content"，请检查是否正确',reqModule:'缺少 "{0}" 模块',reqProp:'缺少必要的 "{0}" 参数，这可能会导致出现错误',emptyProp:'参数 "{0}" 不允许为空',errProp:'不支持的参数 "{0}"，可能为 "{1}"',colRepet:'column.{0}="{1}" 重复了，这可能会导致某些功能无法使用',notFunc:'方法 "{0}" 不存在',errFunc:'参数 "{0}" 不是一个方法',notValidators:'全局校验 "{0}" 不存在',notFormats:'全局格式化 "{0}" 不存在',notCommands:'全局指令 "{0}" 不存在',notSlot:'插槽 "{0}" 不存在',noTree:'树结构不支持 "{0}"',notProp:'不支持的参数 "{0}"',checkProp:'当数据量过大时可能会导致复选框卡顿，建议设置参数 "{0}" 提升渲染速度',coverProp:'"{0}" 的参数 "{1}" 被覆盖，这可能会出现错误',delFunc:'方法 "{0}" 已废弃，请使用 "{1}"',delProp:'参数 "{0}" 已废弃，请使用 "{1}"',delEvent:'事件 "{0}" 已废弃，请使用 "{1}"',removeProp:'参数 "{0}" 已废弃，不建议使用，这可能会导致出现错误',errFormat:'全局的格式化内容应该使用 "VXETable.formats" 定义，挂载 "formatter={0}" 的方式已不建议使用',notType:'不支持的文件类型 "{0}"',notExp:"该浏览器不支持导入/导出功能",impFields:"导入失败，请检查字段名和数据格式是否正确",treeNotImp:"树表格不支持导入"},renderer:{search:"搜索",cases:{equal:"等于",unequal:"不等于",gt:"大于",ge:"大于或等于",lt:"小于",le:"小于或等于",begin:"开头是",notbegin:"开头不是",endin:"结尾是",notendin:"结尾不是",include:"包含",exclude:"不包含",between:"介于",custom:"自定义筛选",insensitive:"不区分大小写",isSensitive:"区分大小写"},combination:{menus:{clearSort:"清除排序",sortAsc:"升序",sortDesc:"降序",fixedColumn:"锁定列",fixedGroup:"锁定组",cancelFixed:"取消锁定",fixedLeft:"锁定左侧",fixedRight:"锁定右侧",clearFilter:"清除筛选",textOption:"文本筛选",numberOption:"数值筛选"},popup:{title:"自定义筛选的方式",currColumnTitle:"当前列：",and:"与",or:"或",describeHtml:"可用 ? 代表单个字符<br/>用 * 代表任意多个字符"},empty:"(空白)",notData:"无匹配项"}},pro:{area:{mergeErr:"无法对合并单元格进行该操作",multiErr:"无法对多重选择区域进行该操作",extendErr:"如果延伸的区域包含被合并的单元格，所有合并的单元格需大小相同",pasteMultiErr:"无法粘贴，需要相同大小的复制的区域和粘贴的区域才能执行此操作"},fnr:{title:"查找和替换",findLabel:"查找",replaceLabel:"替换",findTitle:"查找内容：",replaceTitle:"替换为：",tabs:{find:"查找",replace:"替换"},filter:{re:"正则表达式",whole:"全词匹配",sensitive:"区分大小写"},btns:{findNext:"查找下一个",findAll:"查找全部",replace:"替换",replaceAll:"替换全部",cancel:"取消"},header:{seq:"#",cell:"单元格",value:"值"},empty:"(空值)",reError:"无效的正则表达式",recordCount:"已找到 {0} 个单元格",notCell:"找不到匹配的单元格",replaceSuccess:"成功替换 {0} 个单元格"}},table:{emptyText:"暂无数据",allTitle:"全选/取消",seqTitle:"#",confirmFilter:"筛选",resetFilter:"重置",allFilter:"全部",sortAsc:"升序：最低到最高",sortDesc:"降序：最高到最低",filter:"对所选的列启用筛选",impSuccess:"成功导入 {0} 条记录",expLoading:"正在导出中",expSuccess:"导出成功",expFilename:"导出_{0}",expOriginFilename:"导出_源_{0}",customTitle:"列设置",customAll:"全部",customConfirm:"确认",customRestore:"重置",maxFixedCol:"最大固定列的数量不能超过 {0} 个"},grid:{selectOneRecord:"请至少选择一条记录！",deleteSelectRecord:"您确定要删除所选记录吗？",removeSelectRecord:"您确定要移除所选记录吗？",dataUnchanged:"数据未改动！",delSuccess:"成功删除所选记录！",saveSuccess:"保存成功！",operError:"发生错误，操作失败！"},select:{search:"搜索",loadingText:"加载中",emptyText:"暂无数据"},pager:{goto:"前往",pagesize:"{0}条/页",total:"共 {0} 条记录",pageClassifier:"页",prevPage:"上一页",nextPage:"下一页",prevJump:"向上跳页",nextJump:"向下跳页"},alert:{title:"消息提示"},button:{confirm:"确认",cancel:"取消"},import:{modes:{covering:"覆盖",insert:"新增"},impTitle:"导入数据",impFile:"文件名",impSelect:"选择文件",impType:"文件类型",impOpts:"参数设置",impConfirm:"导入",impCancel:"取消"},export:{types:{csv:"CSV (逗号分隔)(*.csv)",html:"网页(*.html)",xml:"XML 数据(*.xml)",txt:"文本文件(制表符分隔)(*.txt)",xls:"Excel 97-2003 工作簿(*.xls)",xlsx:"Excel 工作簿(*.xlsx)",pdf:"PDF (*.pdf)"},modes:{current:"当前数据（当前页的数据）",selected:"选中数据（当前页选中的数据）",all:"全量数据（包括所有分页的数据）"},printTitle:"打印数据",expTitle:"导出数据",expName:"文件名",expNamePlaceholder:"请输入文件名",expSheetName:"标题",expSheetNamePlaceholder:"请输入标题",expType:"保存类型",expMode:"选择数据",expCurrentColumn:"全部字段",expColumn:"选择字段",expOpts:"参数设置",expOptHeader:"表头",expHeaderTitle:"是否需要表头",expOptFooter:"表尾",expFooterTitle:"是否需要表尾",expOptColgroup:"分组表头",expColgroupTitle:"如果存在，则支持带有分组结构的表头",expOptMerge:"合并",expMergeTitle:"如果存在，则支持带有合并结构的单元格",expOptAllExpand:"展开层级",expAllExpandTitle:"如果存在，则支持将带有层级结构的数据全部展开",expOptUseStyle:"样式",expUseStyleTitle:"如果存在，则支持带样式的单元格",expOptOriginal:"源数据",expOriginalTitle:"如果为源数据，则支持导入到表格中",expPrint:"打印",expConfirm:"导出",expCancel:"取消"},modal:{zoomIn:"最大化",zoomOut:"还原",close:"关闭"},form:{folding:"收起",unfolding:"展开"},toolbar:{import:"导入",export:"导出",print:"打印",refresh:"刷新",zoomIn:"全屏",zoomOut:"还原",custom:"列设置",customAll:"全部",customConfirm:"确认",customRestore:"重置",fixedLeft:"固定在左侧",fixedRight:"固定在右侧",cancelfixed:"取消固定"},input:{date:{m1:"01 月",m2:"02 月",m3:"03 月",m4:"04 月",m5:"05 月",m6:"06 月",m7:"07 月",m8:"08 月",m9:"09 月",m10:"10 月",m11:"11 月",m12:"12 月",quarterLabel:"{0} 年",monthLabel:"{0} 年",dayLabel:"{0} 年 {1}",labelFormat:{date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss",week:"yyyy 年第 WW 周",month:"yyyy-MM",quarter:"yyyy 年第 q 季度",year:"yyyy"},weeks:{w:"周",w0:"周日",w1:"周一",w2:"周二",w3:"周三",w4:"周四",w5:"周五",w6:"周六"},months:{m0:"一月",m1:"二月",m2:"三月",m3:"四月",m4:"五月",m5:"六月",m6:"七月",m7:"八月",m8:"九月",m9:"十月",m10:"十一月",m11:"十二月"},quarters:{q1:"第一季度",q2:"第二季度",q3:"第三季度",q4:"第四季度"}}}}};const Va=[fo,ko,Io,zn,Yn,el,nl,fl,gl,El,Rl,$l,Dl,Ll,Pl,_l,Bl,Wl,Yl,Kl,ir,ur,$r,Nr,Pr,_r,Yr,Zr,ea,ua,fa,La];function _a(e,t){a().isPlainObject(t)&&eo(t),Va.forEach((t=>t.install(e)))}eo({i18n:(e,t)=>a().toFormatString(a().get(Pa,e),t)});var ja=e,Ha=ja}(),r}()}));
