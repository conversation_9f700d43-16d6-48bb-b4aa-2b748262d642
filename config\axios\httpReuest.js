const apiEnvironment = 'http://**************';
  //  const apiEnvironment = 'http://*************'; // 正式环境地址 谨慎使用
// 测试
// const apiEnvironment = 'http://*************';
// const apiEnvironment = 'http://localhost:5003/';
 //const apiEnvironment = '';
const instance = axios.create({
  baseURL: apiEnvironment,
  timeout: 300000,
  headers: { 'X-Custom-Header': 'foobar' },
});

function post(params) {
  const defaultParams = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
  };
  const realParams = Object.assign({}, defaultParams, params);
  return instance({
    method: 'post',
    url: '/api/Do/DoAPI',
    data: realParams,
  });
}

export {
  post,
  apiEnvironment
};

