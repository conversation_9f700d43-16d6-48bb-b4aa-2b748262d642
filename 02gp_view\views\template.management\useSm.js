import { getSM } from '../../api/index.js'

export function useSm(tableData, tableRef) {
  // define sm dialog state
  const smState = Vue.reactive({
    visible: false,
    sm: '',
    currentRow: null,
    option: [],
  })
  // get sm option
  const getSmOption = async () => {
    smState.option = await getSM()
  }
  /* 获取当前排序 */
  const getCurrentSort = () => {
    if (tableData.value.length === 0) {
      const data = tableRef.value.getTableData().tableData
      return data.filter((item) => item.int_level === 3).length
    }
    return tableData.value.filter((item) => item.int_level === 3).length
  }
  // handle save sm
  const handleSaveSm = async () => {
    const $table = tableRef.value
    const rid = Date.now()
    const count = getCurrentSort()
    const record = {
      id: rid,
      int_level: 3,
      str_node: smState.sm,
      int_tat: '',
      id_task_ago: '',
      str_task_ago: '',
      id_task: '',
      id_root: smState.currentRow.id,
      int_sort: count + 1,
    }
    await $table.insertAt(record, -1)
    // 将父节点展开
    await $table.setTreeExpand(smState.currentRow, true)
    smState.visible = false
    handleClearSm()
  }
  // handle clear sm
  const handleClearSm = () => {
    smState.sm = ''
  }

  return {
    smState,
    getSmOption,
    handleSaveSm,
    handleClearSm,
  }
}
