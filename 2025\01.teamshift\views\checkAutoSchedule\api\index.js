import { post } from '../../../utils/request.js'
/**
 * 任务明细对象
 * @typedef {Object} TaskObject
 * @property {string} id 任务ID
 * @property {string} str_shift 班次名称
 * @property {string} str_skill 能力
 * @property {string} str_task 任务名称
 * @property {string} str_wo WO
 * @property {string} str_esn ESN
 * @property {string} str_task_type 检验类型
 * @property {number} dec_tat 工时
 * @property {string} dt_shift 排班时间
 * @property {string|null} [dt_up]  null
 * @property {string|null} [id_by]  null
 * @property {string} id_shift 班次id
 * @property {string} id_skill 任务id
 * @property {string} id_task 任务id
 * @property {string} id_team 班组id
 * @property {string} id_wo WO
 * @property {number} is_delete 是否删除
 * @property {string} str_flow FLOW
 * @property {string} str_sm 单元体
 *
 */
/**
 * 检验对象
 * @typedef {Object} CheckObject
 * @property {string} key 唯一标识
 * @property {string} dt_project_end GP结束日期
 * @property {string} dt_project_start GP开始日期
 * @property {number} int_sort 排序
 * @property {string} str_sm 单元体
 * @property {string} str_esn ESN
 * @property {string} str_wo WO
 * @property {string} str_engine_type 机型 eg:CFM56
 * @property {string} str_flow FLOW eg:F4-1
 * @property {string} str_group Type
 * @property {string} id_task 班组
 * @type {TaskObject[]}
 * @property {Array<TaskObject>} tasks 任务明细
 */
/**
 * 获取检验排班数据
 * @param {Object} params
 * @param {Array<string>} params.dt_range 日期范围
 * @param {string} params.str_engine_type 机型 eg:CFM56
 * @param {string} params.str_group 班组 eg:B23
 * @param {string} params.str_flow FLOW eg:F4-1
 * @type {CheckObject[]}
 * @returns {Promise<CheckObject[]>}
 */
export const getCheckAutoScheduleData = async (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_chcek_query_dispatch_page_list',
    param_data: params,
  })
}

/**
 * 获取人员选项
 * @param {Object} params
 * @param {string} params.id_skill
 * @param {string} params.dt_shift
 * @param {string} params.id_shift
 * @returns {Promise<Array>}
 */
export const fetchPersonnelOptions = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_members_by_skill',
    id_skill: params.id_skill,
    dt_shift: params.dt_shift,
    id_shift: params.id_shift,
  })
}

/**
 * 分配的人员信息对象
 * @typedef {Object} AssignedStaffInfo
 * @property {string} id_staff 员工ID
 * @property {string} str_staff 员工姓名
 * @property {string} id_team 班组ID
 */

/**
 * 任务分配参数对象。此对象用于描述单个任务及其人员分配的详细信息。
 * @typedef {Object} TaskAssignmentPayload
 * @property {string} str_shift 班次名称 (例如: "晚班")
 * @property {string|null} str_skill 技能名称 (如果适用)
 * @property {string|null} str_task 任务描述 (如果适用)
 * @property {string|null} str_team 班组名称 (如果适用)
 * @property {string} str_wo WO号 (例如: "E20220109")
 * @property {string} str_esn ESN号 (例如: "962918")
 * @property {string} str_task_type 检验类型 (例如: "过程检验")
 * @property {number} dec_tat 标准工时
 * @property {string} dt_shift 排班日期 (格式: "YYYY-MM-DD", 例如: "2025-04-17")
 * @property {string|null} dt_up 更新时间 (如果适用)
 * @property {string|null} id 此条任务分配记录的唯一ID (新增时可能为null)
 * @property {string|null} id_by 操作人ID (如果适用)
 * @property {string} id_shift 班次ID
 * @property {string} id_skill 技能ID
 * @property {string} id_task 任务ID
 * @property {string} id_team 班组ID (可能为空字符串，表示未直接关联到特定班组)
 * @property {string} id_wo WO ID
 * @property {number} is_delete 是否删除标记 (0: 未删除, 1: 已删除)
 * @property {string} str_flow 流程名称 (例如: "F4-1")
 * @property {string} str_sm 单元体名称 (例如: "SM22")
 * @property {AssignedStaffInfo[]} staffs 分配到此任务的员工列表
 */

/**
 * 保存任务的人员分配信息。
 * 后端接口期望一个名为 `param_data` 的字段，其值即为此函数的 `params` 参数。
 * @param {TaskAssignmentPayload[]} params - 一个任务分配对象的数组。每个对象包含一个任务的详细信息以及要分配给该任务的员工列表。
 * @returns {Promise<Boolean>} 后端接口返回的响应，通常用于指示操作状态。
 */
export const savePersonnelOptions = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_check_schedule',
    param_data: params,
  })
}

/**
 * 甘特图任务对象
 * @typedef {Object} GanttTask
 * @property {string} id 任务ID
 * @property {string|null} id_wo WO ID
 * @property {string|null} str_flow FLOW
 * @property {string|null} str_group Type/Group
 * @property {string|null} str_sm 单元体
 * @property {string} id_root 根节点ID (通常为 "0")
 * @property {number} int_level 层级
 * @property {string} dt_begin 开始日期 (例如: "2025-05-07")
 * @property {string} dt_end 结束日期 (例如: "2025-05-09")
 * @property {string|null} str_node 节点名称/任务文本
 * @property {number} int_check_type 检查类型
 */

/**
 * 甘特图链接对象
 * @typedef {Object} GanttLink
 * @property {string} id 链接ID
 * @property {string} source 源任务ID
 * @property {string} target 目标任务ID
 */

/**
 * 甘特图对象
 * @typedef {Object} GanttObject
 * @property {Array<GanttTask>} nodes 任务数据
 * @property {Array<GanttLink>} links 链接数据
 */

/**
 * 获取甘特图数据
 * @param {Object} params
 * @param {string} params.dt_range 日期范围
 * @param {string} params.str_engine_type 机型 eg:CFM56
 * @param {string} params.str_group 班组 eg:B23
 * @param {string} params.str_flow FLOW eg:F4-1
 * @returns {Promise<GanttObject>}
 */
export const getGanttData = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_check_gantt_chart',
    param_data: params,
  })
}

/**
 * 更新任务
 * @param {TaskAssignmentPayload} params - 一个任务分配对象。
 */
export const updateTask = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_single_check_schedule',
    param_data: params,
  })
}

/**
 * 获取班次
 */
export function getShiftList() {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_shift',
  })
}

/**
 * 获取Team下拉框数据
 */
export function getTeamList(str_dept) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_query_team_by_dept',
    str_dept,
  })
}

/**
 * 获取Staff下拉框数据
 */
export function getStaffList(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_team_member',
    ...params,
  })
}

/**
 * 重排
 * @param {Object} params
 * @param {string} params.id_wo
 * @param {string} params.str_engine_type 机型 eg:CFM56
 * @param {string} params.str_group 班组 eg:B23
 * @param {string} params.str_flow FLOW eg:F4-1
 */
export const reSchedule = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_check_automati_scheduling',
    ...params,
  })
}
