/**
 * @description project计划演算API
 */
import { post } from '../../config/axios/httpReuest.js'

export const queryCalculationList = async (queryLists = []) => {
  const list = []
  const params = {
    ac: 'gp_plan_search',
    queryLists,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return list
  }
  list.push(...data.data)
  return list
}

/**
 * @description 根据id获取gantt
 * @param {string} id
 * @param {string} str_node
 */
export const queryGanttById = async (id, str_node) => {
  let res = null
  const params = {
    ac: 'gp_plan_gantt_search_by_id',
    id,
    str_node,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return res
  }
  res = data.data
  return res
}

/**
 * @description 保存Project
 * @param {object} data
 * @return {Promise<boolean>}
 */
export const saveProject = async (data) => {
  const params = {
    ac: 'gp_plan_add',
    data,
  }
  const { data: res } = await post(params)
  if (res.code === 'error') {
    ElementPlus.ElMessage.error(res.text)
    return false
  }
  ElementPlus.ElMessage.success(res.text)
  return true
}

/**
 * @description 更新Project
 * @param {object}data
 * @return {Promise<boolean>}
 */
export const updateProject = async (data) => {
  const params = {
    ac: 'gp_plan_edit',
    data,
  }
  const { data: res } = await post(params)
  if (res.code === 'error') {
    ElementPlus.ElMessage.error(res.text)
    return false
  }
  ElementPlus.ElMessage.success(res.text)
  return true
}

/**
 * @description 查询Project List
 * @param queryLists
 * @return {Promise<*[]>}
 */
export const queryProjectByQuery = async (queryLists = []) => {
  const list = []
  const params = {
    ac: 'gp_plan_search',
    queryLists,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return list
  }
  list.push(...data.data)
  return list
}

/**
 * @description 根据id获取Project
 * @param {string} id
 */
export const queryProjectById = async (id) => {
  let res = null
  const params = {
    ac: 'gp_plan_search_by_id',
    id,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return res
  }
  res = data.data
  return res
}

/**
 * @description 删除Project
 * @param {array<string>} ids
 * @return {Promise<boolean>}
 */
export const delProject = async (ids) => {
  const params = {
    ac: 'gp_plan_del',
    lists: ids,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}

/**
 * @description 正是应用Project
 * @param {array<string>} ids
 * @return {Promise<boolean>}
 */
export const activeProjectApi = async (ids) => {
  const params = {
    ac: 'gp_plan_Active',
    planids: ids,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}

/**
 * @description 演算 计划甘特图保存
 * @param {array<string>} datas
 * @return {Promise<boolean>}
 */
export const saveGantApi = async (datas) => {
  const params = {
    ac: 'gp_plan_ganttupdate',
    data: datas,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}
/**
 * @description 从wip 手动同步数据
 * @param {array<string>} id_plans
 * @return {Promise<boolean>}
 */
export const activeWipToProjectApi = async (id_plans) => {
  const params = {
    ac: 'gp_auto_add_plan',
    id_plans,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}

/**
 * @description 冻结解冻
 * @param {string} is_lock
 * @param {array<string>} id_plans
 * @return {Promise<boolean>}
 */
export const freezeAndUnfreezeApi = async (is_lock, id_plans) => {
  const params = {
    ac: 'gp_lock',
    is_lock,
    id_plans,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}

/**
 * @description 已知晓F3变化
 * @param {array<string>} id_plans
 * @return {Promise<boolean>}
 */
export const knowF3ChangeApi = async (id_plans) => {
  const params = {
    ac: 'gp_f3_read',
    id_plans,
  }
  const { data } = await post(params)
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}
