import { convertData } from './conversionData.js'
export function useTask(tableData, tableRef) {
  // define task dialog state
  const taskState = Vue.reactive({
    visible: false,
    taskObj: {},
    int_tat: '',
    preTaskId: '',
    preTask: '',
    currentRow: null,
    option: [],
  })

  // 过滤出int_level为4的数据
  function filterLevel4(data) {
    const list = JSON.parse(JSON.stringify(data))
    const level4List = list.filter((item) => item.int_level === 4)
    return level4List
  }
  // handle save task
  const handleSaveTask = async () => {
    const $table = tableRef.value
    const rid = Date.now()
    let count = 0
    const dataT = $table.getTableData().tableData
    const convertList = convertData(dataT)
    const level4List = filterLevel4(convertList)
    // 获取level4List中int_sortz最大值
    const maxSort = Math.max(...level4List.map((item) => item.int_sort))
    count = maxSort + 1
    const record = {
      id: rid,
      int_level: 4,
      str_node: taskState.taskObj.str_task_name,
      int_tat: taskState.taskObj.int_tat,
      id_task_ago: taskState.taskObj.preTaskId,
      str_task_ago: taskState.taskObj.preTask,
      id_task: taskState.taskObj.id,
      id_root: taskState.currentRow.id,
      int_sort: count + 1,
    }
    await $table.insertAt(record, -1)
    // 将父节点展开
    await $table.setTreeExpand(taskState.currentRow, true)
    taskState.visible = false
    handleClearTask()
  }
  // handle clear task
  const handleClearTask = () => {
    taskState.taskName = ''
    taskState.int_tat = ''
    taskState.preTask = ''
  }

  return {
    taskState,
    handleSaveTask,
    handleClearTask,
  }
}
