import { getTaskGantt, updateTaskGantt, ptResourseStaff } from './api/index.api.js'
import { useDynamicColumn } from '../../../../02gp_view/composables/useDynamicColumn.js'


const { ref, reactive, onMounted, nextTick, computed, onUnmounted, defineAsyncComponent } = Vue

// 甘特图基础配置
const GANTT_CONFIG = {
  // open_split_tasks: true,

  auto_types: true,
  grid_width: 480,
  date_format: '%Y-%m-%d %H:%i',
  date_grid: '%Y-%m-%d',
  drag_links: false,
  drag_project: true,
  drag_progress: false,
  drag_move: true,
  drag_resize: false,
  auto_type: true,
  auto_scheduling: true,
  auto_scheduling_compatibility: true,
  grid_elastic_columns: true,
  duration_unit: "hour",//an hour
  duration_step: 12,
  // 启动撤销功能
  undo: true,
  // 撤销类型
  undo_types: {
    // 任务撤销
    task: true,
    // 链接撤销
    link: false,
  },
  // 设置滚动到今天
  scroll_position: new Date(),
  start_date: new Date(),
  // 结束时间为当前年份的最后一天
  end_date: moment().endOf('year').format('YYYY-MM-DD'),
  columns: [
    { name: 'text', label: 'ESN', tree: true, width: '145', resize: true },
    // { name: 'wo', label: 'WO', align: 'center', width: 100 },
    // { name: 'engine_type', label: '机型', align: 'center', width: 100 },
    // { name: 'client', label: '客户', align: 'center', width: 100 },
    // { name: 'level', label: '修理级别', align: 'center', width: 100 },
    { name: 'start_date', label: 'Start', align: 'center', width: 85, resize: true },
    { name: 'end_date', label: 'End', align: 'center', width: 85, resize: true },
    { name: 'duration', label: 'TAT', align: 'center', width: 50, resize: true },
    {
      name: "deadline", label: "Deadline", width: 100, resize: true, align: "left", template: function (task) {
        if (task.deadline) {
          // const deadlineString = gantt.date.date_to_str(gantt.config.date_grid)(task.deadline);
          let overdueIndicator = "";
          if (moment(task.end_date).format('YYYY-MM-DD') > moment(task.deadline).format('YYYY-MM-DD')) {
            overdueIndicator = `<div style="width: 18px;margin-top: 8px;height: 18px;border-radius: 12px;
           color: white;background: red;display: flex;justify-content: center;align-items: center;
            font-size: 18px;flex-shrink: 0;">!</div>`;
          }
          return overdueIndicator + task.deadline;
        }
      }
    },
  ],
  min_column_width: 50,
  scale_height: 100,
  // 时间分割
  scales: [

    { unit: 'day', step: 1, format: '%m-%d' },
    { unit: "hour", step: 12, format: "%a" },//1天 按 2格
  ],
  // open_tree_initially: true, // 全部层级展开
  initial_expand_level: 4,
  buttons_left: [],
  buttons_right: ["dhx_save_btn", "dhx_cancel_btn"],
}

export const TaskPlanGantt = {
  props: {
    id_wo_input: {
      type: String,
      required: true,
    },
    str_flow_input: {
      type: String,
      required: true,
    },
    str_engine_type_input: {
      type: String,
      required: true,
    },
    str_group_input: {
      type: String,
      required: true,
    },
  },
  components: {
    // DynamicColumnConfigurator,
    HtDrawer: defineAsyncComponent(() => import('../../components/ht.drawer.js')),
  },
  setup(props, { emit }) {
    const formSearch = reactive({
      str_node: '',
    })
    /** 放行时间 */
    const dateValue = ref([])
    /** 甘特数据 */
    const ganttParse = ref({
      data: [],
      links: [],
    })
    /** 人员排班情况 */
    const staffShift = ref([])
    const isCollapseGrid = ref(false)

    // 计算属性优化按钮文本显示
    const gridButtonText = computed(() => (isCollapseGrid.value ? '展开左侧' : '收起左侧'))
    const dynamicColumn = useDynamicColumn(gantt, GANTT_CONFIG.columns)

    // 初始化甘特图配置
    const initGanttConfig = () => {
      gantt.plugins({
        auto_scheduling: true,
        critical_path: true,
        marker: true,
        undo: true,
      })
      // 配置 甘特图周期维度
      // 禁用进度条拖拽
      Object.entries(GANTT_CONFIG).forEach(([key, value]) => {
        gantt.config[key] = value
      })
      // 拖动结束后的事件处理
      gantt.attachEvent('onAfterTaskDrag', function (id, mode) {
        const task = gantt.getTask(id)
        task._changed = true
        // gantt.showDate(task.start_date)
      })

      // ren
      // gantt.templates.task_class = function (start, end, task) {
      //   if (task.deadline && end.valueOf() > task.deadline.valueOf()) {
      //     return 'overdue';
      //   }
      // };

      // gantt.templates.rightside_text = function (start, end, task) {
      //   if (task.deadline) {
      //     if (end.valueOf() > task.deadline.valueOf()) {
      //       const overdue = gantt.calculateDuration(task.deadline, end);
      //       const text = "<b>Overdue: " + overdue + " days</b>";
      //       return text;
      //     }
      //   }
      // };

      gantt.templates.scale_cell_class = function (date) {
        if (date.getDay() == 0 || date.getDay() == 6) {
          return 'weekend'
        }
      }

    }

    /**获取 数据 */
    const getGanttData = async () => {
      // 清除甘特图数据
      gantt.clearAll()
      try {
        loading.value = true
        let startDate, endDate
        if (Array.isArray(dateValue.value)) {
          startDate = dateValue.value[0]
          endDate = dateValue.value[1]
        } else {
          startDate = endDate = dateValue.value
        }

        const data = await getTaskGantt({ start_date: startDate, end_date: endDate, ...formSearch })
        // 获取当前数据中最小的日期
        const minDate = Math.min(...data.datas.map((item) => moment(item.start_date).valueOf()))
        gantt.config.start_date = new Date(minDate)
        ganttParse.value.data = data.datas
        // .map((item) => {
        //   return Object.assign(item, { owner: [{ resource_id: "1", value: 1 }] })
        // })

        ganttParse.value.links = data.links;

        // 资源 load resource values
        ganttParse.value.resources = data.resources;
        //  [
        //   { id: 1, text: "RUPB2/B3", parent: null },
        //   { id: 2, text: "RUPB1", parent: null },

        // ]
        staffShift.value = data.stafftotal || [] // 人员情况
        await nextTick()
        renderGantt()
      } catch (error) {

        ElementPlus.ElMessage.error('无数据')
      } finally {
        loading.value = false
      }
    }
    //#region  资源配置算法
    const getResourceAssignments = (resourceId) => {
      let assignments;
      const store = gantt.getDatastore(gantt.config.resource_store);
      if (store.hasChild(resourceId)) {
        assignments = [];
        store.getChildren(resourceId).forEach(function (childId) {
          assignments = assignments.concat(gantt.getResourceAssignments(childId));
        });
      } else {
        assignments = gantt.getResourceAssignments(resourceId);
      }
      return assignments;
    }
    const resourceConfig = {
      scale_height: 30,
      row_height: 45,
      scales: [
        { unit: "day", step: 1, date: "%d %M" },
        { unit: "hour", step: 12, format: "%a" },

      ],
      columns: [
        {
          name: "name", label: "Name", tree: true, width: 200, template: function (resource) {
            return resource.text;
          }, resize: true
        }
      ],

    };

    const UNASSIGNED_ID = 5;
    const WORK_DAY = 8;
    const cap = {};
    // 获取实际出勤人数 分早晚班
    const getCapacity = (date, resource) => {
      /* it is sample function your could to define your own function for get Capability of resources in day */
      // if (gantt.$resourcesStore.hasChild(resource.id)) {
      //   return -1;
      // }

      // let val = date.valueOf();
      // if (!cap[val + resource.id]) {
      //   cap[val + resource.id] = [0, 1, 2, 3][Math.floor(Math.random() * 100) % 4];
      // }
      // return cap[val + resource.id] ;
      let str_shift = "晚班"
      if (moment(date).format('YYYY-MM-DD HH:mm:ss').includes('00:00:00')) {
        str_shift = "早班,行政班";
      }
      const total = staffShift.value.find((item) => item.id_dept == resource.id
        && item.dt_plan == moment(date).format('YYYY-MM-DD') && str_shift.includes(item.str_shift))?.total || 0;
      return total
    }
    // 获取资源分配值
    const getAllocatedValue = (tasks, resource) => {
      let result = 0;
      tasks.forEach(function (item) {
        const assignments = gantt.getResourceAssignments(resource.id, item.id);
        assignments.forEach(function (assignment) {
          result += Number(assignment.value);
        });
      });
      return result;
    }

    const initReourceConfig = () => {
      const resourceTemplates = {
        grid_row_class: function (start, end, resource) {
          const css = [];
          if (gantt.$resourcesStore.hasChild(resource.id)) {
            css.push("folder_row");
            css.push("group_row");
          }
          return css.join(" ");
        },
        task_row_class: function (start, end, resource) {
          const css = [];
          if (gantt.$resourcesStore.hasChild(resource.id)) {
            css.push("group_row");
          }

          return css.join(" ");
        }
      };
      gantt.config.resource_store = "resource";
      gantt.config.resource_property = "owner";
      gantt.config.order_branch = true;

      gantt.config.scale_height = 30;
      gantt.config.layout = {
        css: "gantt_container",
        rows: [
          {
            gravity: 3,
            cols: [
              { view: "grid", group: "grids", scrollY: "scrollVer" },
              { resizer: true, width: 1 },
              { view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer" },
              { view: "scrollbar", id: "scrollVer", group: "vertical" }
            ]
          },
          // { resizer: true, width: 1, next: "resources" },
          {
            height: 30,
            cols: [
              { html: "<label>Resource", css: "resource-select-panel", group: "grids" }, // <select class='resource-select'></select>
              { resizer: true, width: 1 },
              { html: "" }
            ]
          },

          {
            gravity: 1,
            id: "resources",
            config: resourceConfig,
            templates: resourceTemplates,
            cols: [
              { view: "resourceGrid", group: "grids", scrollY: "resourceVScroll" },
              { resizer: true, width: 1 },
              { view: "resourceHistogram", capacity: 24, scrollX: "scrollHor", scrollY: "resourceVScroll" },
              { view: "scrollbar", id: "resourceVScroll", group: "vertical" }
            ]
          },
          { view: "scrollbar", id: "scrollHor" }
        ]
      };
      gantt.templates.histogram_cell_label = function (start_date, end_date, resource, tasks) {
        if (tasks.length && !gantt.$resourcesStore.hasChild(resource.id)) {
          // return `<div onclick='openResource("${moment(start_date).format('YYYY-MM-DD HH:mm:ss')}",${resource.id})'>` + tasks.length * 8 + "h</div>"
          // 使用自定义事件触发资源详情弹窗
          return `<div>
            <span class="allocated-value" data-date="${moment(start_date).format('YYYY-MM-DD HH:mm:ss')}" data-resource="${resource.id}"   data-status="1" title="已排资源人数" style="color:#006400;font-size:16px;cursor:pointer;">
              ${getAllocatedValue(tasks, resource)}
            </span> | 
            <span class="allocated-value" data-date="${moment(start_date).format('YYYY-MM-DD HH:mm:ss')}" data-resource="${resource.id}"   data-status="0" title="排班可用总数">${getCapacity(start_date, resource)} </span>
            </div>`;
        } else {
          if (!gantt.$resourcesStore.hasChild(resource.id)) {
            return '–';
          }
          return '';
        }
      };

    }

    //#endregion


    // 数据转换优化
    const transformData = (data) => {

    }

    // 添加当前日期标记
    const addCurrentDateMarker = () => {
      const today = new Date()
      gantt.addMarker({
        start_date: today,
        css: 'current-date-marker',
        text: moment(today).format('YYYY-MM-DD'),
        title: '今天',
      })
    }
    /**加载甘特图 */
    const renderGantt = () => {
      gantt.init('gantt-container')
      addCurrentDateMarker() // 添加当前日期标记
      gantt.parse(ganttParse.value) // 加载数据
      expandGantt()
      gantt.render()// 渲染甘特图
    }

    const loading = ref(false)

    // 切换甘特图的列
    const toggleGrid = () => {
      isCollapseGrid.value = !isCollapseGrid.value
      // 重新配置列,并且改变列的宽度
      gantt.config.columns = isCollapseGrid.value
        ? [{ name: 'text', label: 'ESN', tree: true, width: '*', resize: true }]
        : GANTT_CONFIG.columns
      gantt.config.grid_width = isCollapseGrid.value ? 200 : 480
      // 重新渲染
      gantt.render()
    }
    // 重置
    const reset = () => {
      formSearch.str_node = ''
      dateValue.value = []
      getGanttData()
    }

    // 添加容器高度响应式变量
    const containerHeight = ref('100%')

    // 检查是否在 iframe 中
    const isInIframe = () => {
      try {
        return window !== window.top
      } catch (e) {
        return true
      }
    }

    // 计算并设置容器高度
    const updateContainerHeight = () => {
      try {
        if (isInIframe()) {
          // iframe 环下的高度计算
          // 获取iframe的可视区域高度
          const iframeHeight = window.innerHeight || document.documentElement.clientHeight
          // 减去顶部搜索区域的高度(48px)和内边距(32px) 80px = header高度 + padding等
          containerHeight.value = `${iframeHeight - 230}px`
        } else {
          // 非 iframe 环境下的高度计算
          const viewportHeight = window.innerHeight
          containerHeight.value = `${viewportHeight - 230}px` // 80px = header高度 + padding等
        }
      } catch (error) {
        console.error('更新容器高度失败:', error)
        containerHeight.value = '100%'
      }
    }
    /** 滚动到当前时间 */
    const scrollToToday = () => {
      const today = new Date()
      gantt.showDate(today)
    }
    /**保存数据 */
    const handleSave = async () => {
      // 获取甘特图数据
      const { data } = gantt.serialize()
      const params = data
        .filter((item) => item._changed)
        .filter((item) => item.type !== 'project') // 过滤掉项目类型的任务
        .map((item) => ({
          id: item.id,
          pt_dt: item.start_date,
        }))
      await updateTaskGantt(params)
      ElementPlus.ElMessage.success('保存成功')
      getGanttData()
      emit('save_task')


    }
    const isCollapse = ref(false)
    // 折叠甘特图
    const collapseGantt = () => {
      isCollapse.value = false
      // 使用批量操作优化性能
      gantt.batchUpdate(() => {
        // 只折叠第一层，保持数据结构
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.close(taskId)
          }
        })
      })
    }
    // 展开甘特图
    const expandGantt = () => {
      isCollapse.value = true
      // 使用批量操作来优化性能
      gantt.batchUpdate(() => {
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.open(taskId)
            // 展开第二层
            const children = gantt.getChildren(taskId)
            children.forEach((childId) => gantt.open(childId))
          }
        })
      })
    }

    // 打开人员列表
    const isResourceDialogVisible = ref(false)
    const resourceDetails = ref([])
    const openResource = async (id_resource, date, int_status) => {
      isResourceDialogVisible.value = true
      const data = await ptResourseStaff({ id_dept: id_resource, dt_plan: date, int_status: int_status, id_wos: formSearch.id_wos })
      resourceDetails.value = data

      // 比如弹出一个模态框显示资源分配详情

    }

    onMounted(() => {
      formSearch.str_flow = props.str_flow_input;
      formSearch.str_engine_type = props.str_engine_type_input;
      formSearch.id_wos = props.id_wo_input;
      // 初始化搜索条件
      formSearch.start_date = moment().format('YYYY-MM-DD');
      formSearch.end_date = moment().add(30, 'days').format('YYYY-MM-DD');// 默认查询30天内的数据
      if (props.str_group_input) {
        if (props.str_group_input == 'B1') {
          formSearch.str_group = ['B1']
        } else if (props.str_group_input == 'Core') {
          formSearch.str_group = ['Core']
        } else if (props.str_group_input == 'FAN') {
          formSearch.str_group = ['FAN']
        } else if (props.str_group_input == 'LPT') {
          formSearch.str_group = ['LPT']
        } else {
          formSearch.str_group = ['Core', 'FAN', 'LPT']
        }
      }

      initGanttConfig()// 初始化甘特图配置
      initReourceConfig();// 初始化资源配置
      getGanttData()// 获取甘特图数据
      updateContainerHeight()// 初始化容器高度
      window.addEventListener('resize', updateContainerHeight)// 监听窗口大小变化
      gantt.attachEvent('onTaskSave', (id, data) => {
        // 处理任务保存事件
        console.log('任务保存:', id, data)
        // 这里可以添加保存逻辑
        return true // 返回 true 以允许保存
      })
      window.addEventListener("click", function (event) {
        if (event.target.className && event.target.className.includes("allocated-value")) {
          const resourceId = event.target.getAttribute('data-resource')
          const date = event.target.getAttribute('data-date')
          const int_status = event.target.getAttribute('data-status')
          openResource(resourceId, date, int_status)
        }
      },true)


    })

    // 组件销毁时移除事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', updateContainerHeight)
    })

    return {
      formSearch,
      dateValue,
      search: getGanttData,
      toggleGrid,
      gridButtonText,
      reset,
      loading,
      containerHeight,
      scrollToToday,
      handleSave,
      ...dynamicColumn,
      initReourceConfig,
      collapseGantt,
      expandGantt,
      isCollapse,
      openResource,
      isResourceDialogVisible,
      resourceDetails,

    }
  },
  template: /*html*/ `
    <div class="flex flex-col gap-4 p-4">
      <div class="flex items-center justify-between">
        <!-- 左侧搜索区域 -->
        <div class="flex items-center gap-2">
          <label class="el-form-item__label text-right"></label>
          <el-input
            style="width:140px"
            v-model="formSearch.str_node"
            placeholder="ESN | Group | SM"
            clearable
          ></el-input>
          <label class="el-form-item__label text-right"></label>
         <el-select clearable multiple v-model="formSearch.str_group" placeholder=" Group" style="width: 180px" >
            <el-option key="Core" label="Core" value="Core"/>
            <el-option key="FAN" label="FAN" value="FAN"/>
            <el-option key="LPT" label="LPT" value="LPT"/>
            <el-option key="B1" label="B1" value="B1"/>
          </el-select>
        <label class="el-form-item__label text-right"></label>
         <el-select v-model="formSearch.str_flow" placeholder=" Flow" style="width: 120px">
            <el-option key="F1-1" label="F1-1" value="F1-1"/>
            <el-option key="F4-1" label="F4-1" value="F4-1"/>
          </el-select>
          <label class="el-form-item__label text-right"></label>
          <el-date-picker style="width: 120px"
            v-model="formSearch.start_date"
            type="date"
            placeholder="开始时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
          <label class="el-form-item__label text-right"></label>
          <el-date-picker style="width: 120px"
            v-model="formSearch.end_date"
            type="date"
            placeholder="结束时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
          <el-button type="primary" :loading="loading" @click="search">搜索</el-button>
        
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>

        <!-- 右侧功能按钮区域 -->
        <div class="flex items-center gap-2">
          
         <el-button v-if="isCollapse" type="primary" @click="collapseGantt">折叠甘特图</el-button>
          <el-button v-else type="primary" @click="expandGantt">展开甘特图</el-button>
       
          <el-button plain type="default" :loading="loading" @click="toggleGrid"> {{ gridButtonText }} </el-button>
          <el-button plain type="primary" :loading="loading" @click="scrollToToday">回到今天</el-button>
        </div>
      </div>
   
      <!-- 甘特图区域 -->
      <div v-loading="loading" class="relative" :style="{ height: containerHeight }">
        <div id="gantt-container" class="h-full w-full"></div>
      </div>
    </div>

       <HtDrawer title="人员详情" size="50%" :is-show-save="false" v-model="isResourceDialogVisible" @clear="isResourceDialogVisible = false">
   
       <el-table :data="resourceDetails"   height="800" style="width: 100%">
        <el-table-column type="index" width="50" />
        <el-table-column prop="str_dept" label="部门" ></el-table-column>
        <el-table-column prop="dt_plan" label="日期" width="180"></el-table-column>
        <el-table-column prop="str_name" label="人员" width="120"></el-table-column>
          <el-table-column prop="str_shift" label="班次" width="120"></el-table-column>
        <el-table-column prop="str_status" label="状态" sortable  width="120"></el-table-column>
      </el-table>
    </HtDrawer>
  `,
}
