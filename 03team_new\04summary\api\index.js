import { post } from '../../../config/axios/httpReuest.js'
/**
 * 站位管理列表
 */
export const queryStationList = (start, end) => {
  const params = {
    ac: 'pt_get_site_manage_list',
    startDate: start,
    endDate: end,
  }
  return post(params)
}

/**
 * 获取所有发动机
 */
export const queryAllEngine = () => {
  const params = {
    ac: 'pt_get_esn_list',
  }
  return post(params)
}

/**
 * 添加站位列
 */
export const insertStation = (params) => {
  const queryParams = {
    ac: 'pt_add_site_manage',
    ...params,
  }
  return post(queryParams)
}
