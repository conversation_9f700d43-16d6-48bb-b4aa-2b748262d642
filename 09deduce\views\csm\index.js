import { post } from "../../../config/axios/httpReuest.js";
import HtVxeTable from "../../../components/VxeTable/HtVxeTable.js";

const {
  defineAsyncComponent,
  reactive,
  toRefs,
  onMounted,
  ref,
  nextTick,
  watch,
} = Vue;
const BasePage = {
  components: {
    HtVxeTable,
    PagePager: defineAsyncComponent(() =>
      import("../../../components/VxePager/PagePager.js")
    ),
    HtDrawer: defineAsyncComponent(() =>
      import("../../../components/ht.drawer.js")
    ),
    BasePageAddAndEdit: defineAsyncComponent(() =>
      import("./BasePageAddAndEdit.js")
    ),
    BasePageView: defineAsyncComponent(() => import("./view.js")),
  },
  setup() {
    const tableColumns = [
      {
        title: 'PN',
        field: 'str_pn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'Pn Name',
        field: 'str_part_name',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件来源',
        field: 'str_source_type',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标ESN',
        field: 'str_esn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标客户',
        field: 'str_client',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },{
        title: '原ESN',
        field: 'str_esn_sp',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'str_client_sp',
        title: '原客户',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        // filterRender: { name: 'FilterInput' },
        // formatter: ({ cellValue }) => {
        //   return cellValue.toUpperCase();
        // },
      },
      {
        title: 'QTY',
        field: 'int_num_inuse',
        minWidth: 50,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PKPID',
        field: 'id',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标EKD',
        field: 'dt_ekd_before',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原EKD',
        field: 'dt_ekd',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      // {
      //   type: 'html',
      //   title: '状态',
      //   field: 'is_state',
      //   minWidth: 100,
      //   filters: [{ label: '启用', value: 1 }, { label: '停用', value: 0 }],
      //   filterMultiple: false,
      //   // filterRender: { name: 'FilterInput' },
      //   formatter: ({ cellValue }) => {
      //     return `<span style="color: ${cellValue === 1 ? 'green' : 'red'}">${cellValue === 1 ? '启用' : '停用'}</span>`;
      //   },
      // },
    ];
    // 表格组件的dom
    const basePageTableRef = ref(null);
    // 表格数据
    const tableState = reactive({
      tableData: [],
      tableColumns: tableColumns,
    });

    // 获取表格数据
    const getTableData = async (queryLists) => {
      queryLists.push({ str_key: 'int_csm_result', str_value: 301 })
      const tableData = [];
      const param = {
        ac: "de_getcsmlist",
        filter_fields: Object.assign(queryLists, ), // 默认查询待确认的
      };
      const { data } = await post(param);

      if (data.code === "error") {
        ElementPlus.ElMessage.error(data.text);
        return [];
      }
      return data.data;
    };
    // 获取表格数据通过前端分页
    const getTableDataByFrontPage = async (
      currentPage,
      pageSize,
      queryLists = []
    ) => {
      const tableData = await getTableData(queryLists);

      const start = (currentPage - 1) * pageSize;
      const end = currentPage * pageSize;
      tableState.tableData = tableData.slice(start, end);
      pagerState.total = tableData.length;
    };

    // 分页数据
    const pagerState = reactive({
      currentPage: 1,
      pageSize: 20,
      total: 0,
    });
    // 分页改变
    const handlePageChange = async ({ currentPage, pageSize }) => {
      pagerState.currentPage = currentPage;
      pagerState.pageSize = pageSize;
      await getTableDataByFrontPage(
        pagerState.currentPage,
        pagerState.pageSize
      );
    };

    /**
     * @description 修改状态
     * @param {object} row - 行数据
     */
    const changeState = async (row, int_csm_result) => {
      const { id } = row;
      const res = await ElementPlus.ElMessageBox.confirm(
        `是否${int_csm_result == 1 ? "同意" : "拒绝"}`,
        "确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      );
      if (res === "confirm") {
        const param = {
          ac: "de_set_sim_details_status",
          ids: id,
          int_csm_result: int_csm_result,
        };
        const { data } = await post(param);
        if (data.code === "error") {
          return;
        }
        getTableDataByFrontPage(pagerState.currentPage, pagerState.pageSize);
      }
    };



    /**
     * @description 过滤条件改变
     * @param { object } panel
     */
    const handleFilterChange = async (panel) => {
      const { filterList } = panel;
      const queryLists = filterList.map((item) => {
        return {
          str_key: item.field,
          str_value: item.values[0] ?? item.datas[0],
        };
      });
      await getTableDataByFrontPage(
        pagerState.currentPage,
        pagerState.pageSize,
        queryLists
      );
    };

    onMounted(async () => {
      await getTableDataByFrontPage(
        pagerState.currentPage,
        pagerState.pageSize
      );
    });

    return {
      basePageTableRef,
      ...toRefs(tableState),
      ...toRefs(pagerState),
      handlePageChange,
      getTableData,

      handleFilterChange,
      changeState,
    };
  },
  /*HTML*/
  template: `
    <!--    头部按钮-->
    <div class="flex items-center flex-wrap">
      <article class="mx-4 my-2">
      <!--  <el-button type="primary" @click="handleAdd">同意</el-button>
        <el-button type="warning" @click="handleEdit">拒绝</el-button>-->
       
      </article>
    </div>
    <div class="border-b-2 mb-2"></div>
    <!--    任务基础数据表格-->
    <div class="mx-4" style="height: calc(100vh - 140px);">
      <HtVxeTable
        ref="basePageTableRef"
        :tableData
        :tableColumns
        :remote="true"
        @filterChange="handleFilterChange">
       <!-- <template #checkbox>
          <vxe-column type="checkbox" width="60"></vxe-column>
        </template>-->
        <template #operation>
          <vxe-column title="操作" width="180">
            <template #default="{row}">
              <el-button size="mini" @click="changeState(row,-1)">
                <span  style="color: red">拒绝</span>
              </el-button>
              <el-button style="float: right;" size="mini" @click="changeState(row,1)">
              <span  style="color: green">同意</span>
            </el-button>
            </template>
          </vxe-column>
        </template>
      </HtVxeTable>
      <PagePager
        :currentPage="currentPage"
        :pageSize="pageSize"
        :total="total"
        @pageChange="handlePageChange"
      ></PagePager>
    </div>


 

  `,
};
export default BasePage;
