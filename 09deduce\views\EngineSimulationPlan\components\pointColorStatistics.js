import { post } from '../../../../config/axios/httpReuest.js'
// 引入点位颜色抽屉组件
import PointColotDrawer from './pointColorDrawer.js'

const { ref, reactive, toRefs } = Vue
const PointColorStatistics = {
  components: {
    PointColotDrawer,
  },
  props: {
    idWo: {
      type: String,
      required: true,
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    // 点位颜色统计
    const pointColorStatistics = reactive({
      black: 0,
      blue: 0,
      green: 0,
      red: 0,
      purple: 0,
      redBack: 0,
      // 差异
      diff: 0,
      orange: 0,
      // 未扫描零件
      unscan: 0,
      // 未集件零件
      uncollect: 0,
      // 未关闭零件
      unclose: 0,
    })
    // 模拟前or模拟后
    const simulationType = ref('0')
    // 获取点位颜色统计
    const pointColorMap = {
      0: 'black',
      1: 'red',
      2: 'blue',
      3: 'green',
      4: 'purple',
      110: 'redBack',
      120: 'diff',
      111: 'orange',
      130: 'unscan',
      140: 'uncollect',
      150: 'unclose',
    }
    const labelMap = {
      0: '',
      1: '不能串',
      2: '有条件串',
      3: '可串件',
      4: '不具备串件条件',
      110: '红框后零件',
      120: 'onlog/offlog差异',
      111: '有延迟风险零件',
      130: '未扫描零件',
      140: '未集件零件',
      150: '未关闭零件',
    }
    // ! 父组件的查询条件
    const parentFilterFields = ref([])
    const getPointColorStatistics = async (id, filterFields = [], type = '0') => {
      simulationType.value = type
      parentFilterFields.value = filterFields
      const params = {
        ac: 'de_getpngroup',
        id_wo: id,
        int_ekd_type: type, // 0:推演前 1:推演后 2:模拟前 3:模拟后
        filter_fields: filterFields,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        const { pnCounts } = data.data
        pnCounts.forEach(({ int_point_type, int_num }) => {
          const color = pointColorMap[int_point_type]
          if (color) {
            pointColorStatistics[color] = int_num
          }
        })
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    // 点位颜色抽屉
    const pointColotDrawerVisible = ref(false)
    // 点位颜色类型
    const pointColorType = ref('')
    // 点位id
    const pointId = ref('')
    // 打开抽屉
    const handleOpenDrawer = (type, id) => {
      pointColorType.value = type
      pointId.value = id
      pointColotDrawerVisible.value = true
    }

    // 提交
    const handleSumbit = async (id) => {
      await getPointColorStatistics(id)
      emit('submit', id)
      pointColotDrawerVisible.value = false
    }

    return {
      getPointColorStatistics,
      ...toRefs(pointColorStatistics),
      simulationType,
      pointColotDrawerVisible,
      pointColorType,
      handleOpenDrawer,
      handleSumbit,
      parentFilterFields,
      pointId,
      labelMap,
    }
  },
  template: /*html*/ `
    <div :id="'point-'+idWo">
      <div class="grid grid-flow-col gap-4">
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4 rounded-full bg-green-500"></div>
          <div
            class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline"
            @click="handleOpenDrawer('3')"
          >
            {{ green }}
          </div>
        </div>
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4 rounded-full bg-blue-500"></div>
          <div
            class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline"
            @click="handleOpenDrawer('2')"
          >
            {{ blue }}
          </div>
        </div>
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4 rounded-full bg-red-500"></div>
          <div
            class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline"
            @click="handleOpenDrawer('1')"
          >
            {{ red }}
          </div>
        </div>
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4 rounded-full bg-purple-500"></div>
          <div
            class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline"
            @click="handleOpenDrawer('4')"
          >
            {{ purple }}
          </div>
        </div>
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4 rounded-full bg-black"></div>
          <div
            class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline"
            title="此项为本台发动机的全量零件"
            @click="handleOpenDrawer('0')"
          >
            {{ black }}
          </div>
        </div>
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4" style="border: 1px solid red; "></div>
          <div
            class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline"
            title="红框后零件"
            @click="handleOpenDrawer('110')"
          >
            {{ redBack }}
          </div>
        </div>
        <!-- 橙红色 111-->
         <div class="flex items-center">
          <div class="mr-2 h-4 w-4 border border-orange-500"></div>
          <div
            class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline"
            title="有延迟风险零件"
            @click="handleOpenDrawer('111')"
          >
            {{ orange }}
          </div>
        </div>
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4 rounded-full border border-gray-500 ring-offset-2 ring-2"></div>
          <div
            class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline"
            title="onlog/offlog差异清单"
            @click="handleOpenDrawer('120')"
          >
            {{ diff }}
          </div>
        </div>
        <!-- 150 未关闭零件 -->
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4 rounded-full bg-red-500"></div>
          <div class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline" title="未关闭零件" @click="handleOpenDrawer('150')">
            {{ unclose }}
          </div>
        </div>
        <!-- 140 未集件零件 -->
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4 rounded-full bg-red-500"></div>
          <div class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline" title="未集件零件" @click="handleOpenDrawer('140')">
            {{ uncollect }}
          </div>
        </div>

        <!-- 130 未扫描零件 -->
        <div class="flex items-center">
          <div class="mr-2 h-4 w-4 rounded-full bg-red-500"></div>
          <div class="text-base hover:cursor-pointer hover:text-blue-300 hover:underline" title="未扫描零件" @click="handleOpenDrawer('130')">
            {{ unscan }}
          </div>
        </div>
      </div>
      <!-- 点位颜色抽屉 -->
      <PointColotDrawer
        v-if="pointColotDrawerVisible"
        v-model:visible="pointColotDrawerVisible"
        :type="pointColorType"
        :simulationType="simulationType"
        :id="pointId"
        :idWo="idWo"
        :filterFields="parentFilterFields"
        :title="labelMap[pointColorType]"
        @submit="handleSumbit"
      ></PointColotDrawer>
    </div>
  `,
}
export default PointColorStatistics
