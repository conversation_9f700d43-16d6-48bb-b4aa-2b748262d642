const { ref, reactive, onMounted, toRefs, defineComponent, computed, nextTick, onUnmounted, watch } = Vue
import {
  queryTeamLeaderAndStaff,
  queryTeamPlan,
  queryHeadData,
  submitFeedback,
  saveFeedBack,
} from '../../api/teams/index.js'
import TeamHandover from './components/team.handover.js'
import TeamPlanSee from './components/team.plan.see.js'
import TeamPlanEdit from './components/team.plan.edit.js'
import TeamFeedback from './components/team.feedback.js'
import PendingHandover from '../handover/pending.handover.js'
// 搜索表单组件
const SearchForm = defineComponent({
  name: 'SearchForm',
  emits: ['search', 'toggle-header'],
  setup(props, { emit }) {
    const searchFormRef = ref(null)
    const searchParams = ref({
      dt_date: [moment().format('YYYY-MM-DD'), moment().add(14, 'days').format('YYYY-MM-DD')],
      str_esn: '',
    })

    // 从父组件接收表头展开/收起状态
    const isHeaderExpanded = ref(false)

    // 切换表头显示/隐藏状态
    const toggleHeader = () => {
      isHeaderExpanded.value = !isHeaderExpanded.value
      emit('toggle-header', isHeaderExpanded.value)
    }

    return {
      searchFormRef,
      searchParams,
      isHeaderExpanded,
      toggleHeader,
    }
  },
  template: /*html*/ `
    <el-form ref="searchFormRef" :model="searchParams" label-width="auto" inline label-position="right">
      <el-form-item label="Date:">
        <el-date-picker
          style="width: 100%"
          clearable
          type="daterange"
          value-format="YYYY-MM-DD"
          v-model="searchParams.dt_date"
          start-placeholder="起始日期"
          end-placeholder="截止日期"
        />
      </el-form-item>
      <el-form-item label="ESN:">
        <el-input v-model="searchParams.str_esn" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="$emit('search', searchParams)" icon="Search" />
      </el-form-item>
      <!-- 添加展开/收起按钮 -->
      <!-- <el-form-item>
        <el-button 
          v-if="!isHeaderExpanded" 
          type="primary" 
          @click="toggleHeader"
          class="flex items-center"
        >
          <el-icon class="mr-1"><ArrowDown /></el-icon>
          展开表头
        </el-button>
        <el-button 
          v-else 
          type="primary" 
          @click="toggleHeader"
          class="flex items-center"
        >
          <el-icon class="mr-1"><ArrowUp /></el-icon>
          收起表头
        </el-button>
      </el-form-item> -->
   </el-form>
  `,
})

// 团队信息组件
const TeamInfo = defineComponent({
  name: 'TeamInfo',
  setup() {
    const teamInfo = ref({
      baseHours: '',
      member: [],
      groupLeader: '',
      teamType: '',
    })

    const getTeamLeaderAndStaff = async () => {
      try {
        const res = await queryTeamLeaderAndStaff()
        teamInfo.value.groupLeader = res.groupLeader
        teamInfo.value.teamType = res.teamType
        teamInfo.value.baseHours = res.baseHours
        teamInfo.value.member = res.member
      } catch (error) {
        console.error(error)
      }
    }

    onMounted(() => {
      getTeamLeaderAndStaff()
    })

    return {
      teamInfo,
    }
  },
  template: /*html*/ `
    <el-row class="bg-[#5d7092] text-white">
      <el-row>
        <span>{{ teamInfo.groupLeader }} {{ teamInfo.teamType }}</span>
        &nbsp;&nbsp;&nbsp;&nbsp;
        <span>标准工时：{{ teamInfo.bashHours }}H</span>
      </el-row>
      <el-row>
        <span>{{ teamInfo.member.join(',') }}</span>
      </el-row>
    </el-row>
  `,
})

// ESN下的单元格内容组件
const EsnCell = defineComponent({
  name: 'EsnCell',
  components: {
    PendingHandover,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
    inputStrFlow: String,
    inputGroupType: String,
  },
  setup(props) {
    // 查看支持检验
    const handleOpenIsShowInspectSupport = (row) => {
      console.log(row)
    }

    // 确认排班等调整完毕
    const handleChangeVg = (row) => {
      console.log(row)
    }

    const openStartWorkDialog = ref(false)
    const startWorkIdWo = ref('')
    const business = ref('')
    const isStart = ref(false)
    // 开工
    const handleStartWork = (row) => {
      startWorkIdWo.value = row.id_wo
      isStart.value = true
      openStartWorkDialog.value = true
      if (props.inputStrFlow === 'F1-1' && props.inputGroupType === 'B1') {
        business.value = '103'
      } else if (props.inputStrFlow === 'F1-1' && props.inputGroupType === 'B2/3') {
        business.value = '104'
      } else if (props.inputStrFlow === 'F4-1' && props.inputGroupType === 'B1') {
        business.value = '105'
      } else if (props.inputStrFlow === 'F4-1' && props.inputGroupType === 'B2/3') {
        business.value = '106'
      }
    }

    const handleCloseStartWorkDialog = () => {
      openStartWorkDialog.value = false
      isStart.value = false
    }

    return {
      handleOpenIsShowInspectSupport,
      handleChangeVg,
      handleStartWork,
      handleCloseStartWorkDialog,
      openStartWorkDialog,
      startWorkIdWo,
      business,
      isStart,
    }
  },
  template: /*html*/ `
    <div class="flex h-full flex-col">
      <!-- ESN 标题 -->
      <div class="m-2 h-full text-center">
        <div class="rounded px-2">
          <span class="text-[14px] font-semibold">{{ row.esn }}</span>
        </div>
      </div>

      <!-- 操作按钮区域 - 固定在底部 -->
      <div class="absolute bottom-0 flex w-full items-center justify-center">
        <el-button text type="primary" @click="handleStartWork(row)">开工</el-button>
      </div>
    </div>
    <el-drawer
      class="common-drawer"
      v-model="openStartWorkDialog"
      title="开工"
      size="80%"
      :append-to-body="true"
      @close="handleCloseStartWorkDialog"
    >
      <pending-handover
        v-if="openStartWorkDialog"
        :id-wo="startWorkIdWo"
        :is-start="isStart"
        :start-work-row="row"
        :flow="inputStrFlow"
        :business="business"
      />
    </el-drawer>
  `,
})

// Start Date 单元格内容组件
const StartDateCell = defineComponent({
  name: 'StartDateCell',
  props: {
    row: {
      type: Object,
      required: true,
    },
  },
  template: /*html*/ `
    <div class="h-full flex flex-col divide-y divide-gray-300">
      <div class="font-semibold p-2">{{ row.project_start }}</div>
    </div>
  `,
})

// End Date 单元格内容组件
const EndDateCell = defineComponent({
  name: 'EndDateCell',
  props: {
    row: {
      type: Object,
      required: true,
    },
  },
  template: /*html*/ `
    <div class="h-full flex flex-col divide-y divide-gray-300">
      <div class="font-semibold p-2">{{ row.project_end }}</div>
    </div>
  `,
})

// 任务项组件
const TaskItem = defineComponent({
  name: 'TaskItem',
  components: {
    TeamHandover,
    TeamPlanSee,
    TeamPlanEdit,
    TeamFeedback,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    inputStrFlow: String,
    inputGroupType: String,
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const { ElMessage } = ElementPlus
    const planItem = computed(() => {
      return props.row.plan.find((item) => item.plan_date === props.column.day)
    })

    // 获取team staff 中的staff_name 并拼接成字符串
    const getTeamStaffName = (teamstaff) => {
      return teamstaff.map((item) => item.staff_name).join(',')
    }

    const business = ref('')
    // 打开交接弹窗 - 现在跳转到团队交接页面
    const handleOpenHandover = (task) => {
      teamHandoverVisible.value = true
      if (props.inputStrFlow === 'F1-1' && props.inputGroupType === 'B1') {
        business.value = '103'
      } else if (props.inputStrFlow === 'F1-1' && props.inputGroupType === 'B2/3') {
        business.value = '104'
      } else if (props.inputStrFlow === 'F4-1' && props.inputGroupType === 'B1') {
        business.value = '105'
      } else if (props.inputStrFlow === 'F4-1' && props.inputGroupType === 'B2/3') {
        business.value = '106'
      }
    }

    const teamPlanSeeRef = ref(null)
    const teamPlanSeeVisible = ref(false)

    // 打开班组计划查看弹窗
    const handleOpenTeamPlanSee = (task) => {
      teamPlanSeeVisible.value = true
    }

    const teamPlanEditRef = ref(null)
    const teamPlanEditVisible = ref(false)

    // 打开班组计划编辑弹窗
    const handleOpenTeamPlanEdit = (task) => {
      teamPlanEditVisible.value = true
    }

    // 处理任务详情操作
    const handleOpenTaskDetail = (task) => {
      console.log('打开任务详情', task)
    }

    const teamFeedbackVisible = ref(false)
    const feedbackTask = ref(null)

    const teamHandoverVisible = ref(false)
    // 打开团队交接弹窗
    const handleOpenTeamHandover = (task) => {
      teamHandoverVisible.value = true
    }

    // 处理颜色选择
    const handleColorCommand = (command) => {
      if (props.inputStrFlow === 'F1-1' && props.inputGroupType === 'B1') {
        business.value = '103'
      } else if (props.inputStrFlow === 'F1-1' && props.inputGroupType === 'B2/3') {
        business.value = '104'
      } else if (props.inputStrFlow === 'F4-1' && props.inputGroupType === 'B1') {
        business.value = '105'
      } else if (props.inputStrFlow === 'F4-1' && props.inputGroupType === 'B2/3') {
        business.value = '106'
      }
      if (command === '-2' || command === '-1') {
        teamFeedbackVisible.value = true
        const data = {
          row: props.row,
          column: props.column,
          business: business.value,
          inputStrFlow: props.inputStrFlow,
          inputGroupType: props.inputGroupType,
          status: command,
          str_content: '',
        }
        feedbackTask.value = data
        // feedbackTask.value.command = command
      }
      if (command === '1') {
        //TO DO: 反馈时组装反馈记录数据2025-05-18
        const taskids = Array.from(new Set(planItem.value.task.map((item) => item.taskId)))
        const feedbackData = []
        taskids.forEach((taskId) => {
          const feedback = {
            id: '',
            id_wo: props.row.id_wo || '',
            str_wo: props.row.wo || '',
            id_team: props.row.id_team || '',
            dt_feed: props.column.day,
            id_sm: planItem.value.task
              .filter((item) => item.taskId === taskId)
              .map((item) => item.modelId)
              .join(','),
            id_task: taskId,
            str_task: planItem.value.task
              .filter((item) => item.taskId === taskId)
              .map((item) => item.taskname)
              .join(','),
            id_shift: props.row.id_shift,
            str_shift: props.row.shift_name,
            str_feed_back_type: business.value,
            str_flow: props.inputStrFlow,
            str_group: props.inputGroupType,
            int_status: command,
            str_content: '',
          }
          feedbackData.push(feedback)
        })
        try {
          saveFeedBack(feedbackData).then(() => {
            handleRefresh()
          })
        } catch (error) {
          ElMessage.error('反馈失败')
        }
      }
    }

    const statusClass = computed(() => {
      const statusMap = {
        1: 'bg-green-500 text-white',
        '-1': 'bg-yellow-500 text-white',
        '-2': 'bg-red-500 text-white',
      }
      return statusMap[planItem.value.fedbackstatus] || ''
    })

    const handleRefresh = () => {
      emit('refresh')
    }

    return {
      planItem,
      getTeamStaffName,
      handleOpenHandover,
      business,
      teamPlanSeeRef,
      teamPlanSeeVisible,
      handleOpenTeamPlanSee,
      teamPlanEditRef,
      teamPlanEditVisible,
      handleOpenTeamPlanEdit,
      handleOpenTaskDetail,
      handleColorCommand,
      handleRefresh,
      teamFeedbackVisible,
      feedbackTask,
      teamHandoverVisible,
      handleOpenTeamHandover,
      statusClass,
    }
  },
  template: /*html*/ `
    <div v-if="planItem.task.length > 0" class="h-full" :class="statusClass">
      <!-- 任务内容区域 -->
      <div class="p-2 pb-10">
        <div class="my-2 flex flex-col rounded-md border border-gray-300" v-for="task in planItem.task" :key="task.id">
          <div class="flex justify-between gap-2">
            <!-- 任务名称 太长 显示省略号 -->
            <div class="truncate pl-2 font-semibold" :title="task.taskname">{{ task.taskname }}</div>
            <!-- 任务人员 -->
            <div class="truncate pr-2" :title="getTeamStaffName(task.teamstaff)">
              {{ getTeamStaffName(task.teamstaff) }}
            </div>
          </div>

          <!-- 底部按钮 - 使用boxBottom类 -->
          <div class="boxBottom">
            <div class="span" @click="handleOpenTeamPlanSee(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <View />
              </el-icon>
            </div>
            <!-- <div class="span" @click="handleOpenTeamPlanEdit(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <Edit />
              </el-icon>
            </div> -->
            <div class="span" @click="handleOpenHandover(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <Folder />
              </el-icon>
            </div>
            <div class="span">
              <el-dropdown trigger="click" @command="(command) => handleColorCommand(command, task)">
                <el-icon class="cursor-pointer hover:text-blue-600" :size="18" color="#409EFF">
                  <More />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="-2" class="!text-red-500">红色</el-dropdown-item>
                    <el-dropdown-item command="-1" class="!text-yellow-500">黄色</el-dropdown-item>
                    <el-dropdown-item command="1" class="!text-green-500">绿色</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
    <TeamHandover
      v-if="teamHandoverVisible"
      v-model:visible="teamHandoverVisible"
      :row="row"
      :business="business"
      :planItem="planItem"
    />
    <TeamPlanSee
      v-if="teamPlanSeeVisible"
      ref="teamPlanSeeRef"
      v-model:visible="teamPlanSeeVisible"
      :planItem="planItem"
    />
    <TeamPlanEdit
      v-if="teamPlanEditVisible"
      ref="teamPlanEditRef"
      v-model:visible="teamPlanEditVisible"
      :planItem="planItem"
      @refresh="handleRefresh"
    />
    <TeamFeedback
      v-if="teamFeedbackVisible"
      v-model:visible="teamFeedbackVisible"
      :task="feedbackTask"
      @refresh="handleRefresh"
    />
  `,
})

// 磨削（VG）头部内容组件
const VgHeader = defineComponent({
  name: 'VgHeader',
  props: {
    column: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const vgData = computed(() => {
      return props.column.grind.gingdata.map((item) => {
        return {
          ...item,
          sm: item.sm.filter((item) => item.str_type === 'VG'),
        }
      })
    })
    // 是否显示编辑按钮
    const isShowEdit = computed(() => {
      return props.column.grind.is_vg === '0'
    })

    // 获取VG下的str_esn和str_team
    const getVgStrEsnAndStrTeam = (vgItem) => {
      if (vgItem.sm.length > 0) {
        return `${vgItem.str_esn}${vgItem.str_team}:`
      }
      return ''
    }

    // 获取vgItem中sm的str_sm 拼接成字符串
    const getSmStrSm = (vgItem) => {
      return vgItem.sm.map((item) => item.str_sm).join(',')
    }

    // 判断是否闪烁
    const isBlink = computed(() => {
      return props.column.grind.vgfullflag === '1'
    })

    return {
      vgData,
      isShowEdit,
      getSmStrSm,
      isBlink,
      getVgStrEsnAndStrTeam,
    }
  },
  template: /*html*/ `
    <div class="h-full flex flex-col" :class="{ 'vg-blink-bg': isBlink }">
      <div v-if="isShowEdit">
        <el-icon :size="18" color="#FFF" class="cursor-pointer hover:text-blue-600">
          <Edit />
        </el-icon>
      </div>
      <div v-if="vgData && vgData.length > 0">
        <div v-for="vgItem in vgData" :key="vgItem.id_wo" class="flex items-center">
          <el-icon v-if="vgItem.sm.length > 0 && vgItem.is_hide_edit === '0'" :size="18" color="#FFF" class="cursor-pointer hover:text-blue-600">
            <Edit />
          </el-icon>
          <!-- 超出显示省略号 -->
          <div class="truncate"> 
            <span class="font-semibold">{{ getVgStrEsnAndStrTeam(vgItem) }}</span>
            <span class="font-semibold" :title="getSmStrSm(vgItem)">{{ getSmStrSm(vgItem) }}</span>
          </div>
        </div>
      </div>
    </div>
  `,
})
// 主组件
export default defineComponent({
  name: 'F41B1ShiftPage',
  components: {
    SearchForm,
    TeamInfo,
    TaskItem,
    EsnCell,
    StartDateCell,
    EndDateCell,
    VgHeader,
  },
  props: {
    strFlow: String,
    inputIdWo: String,
    inputPlanDate: String,
    inputIdMain: String,
    inputStrFlow: String,
    inputStrEsnType: String,
    inspectType: String,
    inputGroupType: String,
  },
  setup(props) {
    const loading = ref(false)
    const state = reactive({
      columnList: [],
      showHeader: true,
      loadingData: false,
      tableMaxHeight: 850,
      tableData: [],
    })

    // 添加控制表头显示/隐藏的状态
    const isHeaderExpanded = ref(false)

    // 切换表头显示/隐藏状态
    const toggleHeader = (value) => {
      if (value !== undefined) {
        isHeaderExpanded.value = value
      } else {
        isHeaderExpanded.value = !isHeaderExpanded.value
      }
    }

    // 判断是否是周末
    const isWeekend = (weekday) => {
      return weekday === '星期六' || weekday === '星期日'
    }

    const xTable = ref(null)
    const searchFormRef = ref(null)
    const teamInfoRef = ref(null)

    // 计算表格高度
    const calculateTableHeight = () => {
      // 获取视口高度
      const viewportHeight = window.innerHeight
      // 获取搜索表单高度
      const searchFormHeight = searchFormRef.value?.$el?.offsetHeight || 0
      // 获取团队信息高度
      const teamInfoHeight = teamInfoRef.value?.$el?.offsetHeight || 0

      // 计算表格可用高度
      let availableHeight = viewportHeight - searchFormHeight - teamInfoHeight - 32 // 32是padding的总高度(上下各16px)

      // 如果在iframe中，需要考虑iframe的padding和margin
      if (window.self !== window.top) {
        availableHeight -= 40 // iframe的padding和margin的总高度
      }

      // 设置最小高度
      state.tableMaxHeight = Math.max(availableHeight, 400)
    }

    // 监听窗口大小变化
    const handleResize = () => {
      calculateTableHeight()
    }

    // 监听表头展开/收起状态变化，重新计算表格高度
    watch(isHeaderExpanded, () => {
      nextTick(() => {
        calculateTableHeight()
      })
    })

    const loadList = async () => {
      loading.value = true
      await initColumns()
      const data = await mockList()

      if (xTable.value) {
        await xTable.value.reloadData(data)
        loading.value = false
        // 数据加载完成后重新计算高度
        nextTick(() => {
          calculateTableHeight()
        })
      }
    }

    const initColumns = async () => {
      const searchParams = searchFormRef.value?.searchParams
      const params = {
        str_flow: props.inputStrFlow,
        start_date: (searchParams.dt_date && searchParams.dt_date[0]) || moment().format('YYYY-MM-DD'),
        end_date: (searchParams.dt_date && searchParams.dt_date[1]) || moment().add(14, 'days').format('YYYY-MM-DD'),
      }
      try {
        const response = await queryHeadData(params)
        state.columnList = response
      } catch (error) {
        console.error(error)
      }
    }

    const mockList = async () => {
      state.loadingData = true
      const searchParams = searchFormRef.value?.searchParams
      try {
        const params = {
          str_flow: props.inputStrFlow,
          str_type: props.inspectType,
          start_date: (searchParams.dt_date && searchParams.dt_date[0]) || moment().format('YYYY-MM-DD'),
          end_date: (searchParams.dt_date && searchParams.dt_date[1]) || moment().add(14, 'days').format('YYYY-MM-DD'),
          str_esn: searchParams.str_esn,
          str_esn_type: props.inputStrEsnType,
          str_group_type: props.inputGroupType,
        }
        const response = await queryTeamPlan(params)
        state.tableData = response || []
        state.loadingData = false
      } catch (error) {
        console.error(error)
        state.loadingData = false
      }
    }

    const handleHeaderCellClassName = ({ column }) => {
      // 只处理展开状态下的周末列
      if (column && column.title) {
        if (column.title === '星期六' || column.title === '星期日') {
          return 'bg-weekend'
        }
      }
      return ''
    }

    const handleSearch = (searchParams) => {
      state.searchParams = searchParams
      loadList()
    }

    const isExistData = (row, column) => {
      const planList = row.plan
      const planDay = column.day
      const taskIndex = planList.findIndex((item) => item.plan_date === planDay)
      return taskIndex !== -1
    }

    onMounted(() => {
      calculateTableHeight()
      loadList()
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', handleResize)
    })

    const handleRefresh = () => {
      handleSearch(state.searchParams)
    }

    return {
      ...toRefs(state),
      loading,
      handleSearch,
      handleHeaderCellClassName,
      isExistData,
      searchFormRef,
      teamInfoRef,
      handleRefresh,
      isHeaderExpanded,
      toggleHeader,
      isWeekend,
    }
  },
  template: /*html*/ `
    <div class="p-4">
      <SearchForm ref="searchFormRef" @search="handleSearch" @toggle-header="toggleHeader" />

      <!-- <TeamInfo ref="teamInfoRef" /> -->

      <vxe-table
        :data="tableData"
        ref="xTable"
        border
        :header-cell-class-name="handleHeaderCellClassName"
        :header-cell-style="{color:'#fff',border:'0.01rem solid #fff'}"
        :column-config="{resizable: true, minWidth: 170}"
        :loading="loadingData"
        :height="tableMaxHeight"
        :show-header="true"
      >
        <!-- 展开状态下的表格结构 -->
        <template v-if="isHeaderExpanded">
          <!-- 固定列 -->
          <vxe-colgroup fixed="left">
            <template #header="{}">
              <el-row><span>RUE/RUMC/RULA/RUQA值班人员</span></el-row>
            </template>
            <vxe-colgroup title="磨削(VG)">
              <vxe-colgroup title="磨削(HSG)">
                <vxe-colgroup title="每日人力状态 (只填休假、MES等人员状态)">
                  <vxe-column title="序号" type="seq" width="60" align="center"></vxe-column>
                  <vxe-column title="ESN" field="esn" width="100" align="center">
                    <template #default="{ row }">
                      <EsnCell :row="row" :inputStrFlow :inputGroupType />
                    </template>
                  </vxe-column>
                  <vxe-column title="Type" field="str_task_type" width="100" align="center"></vxe-column>
                  <vxe-column title="Start Date" field="start_date" width="100" align="center">
                    <template #default="{ row }">
                      <StartDateCell :row="row" />
                    </template>
                  </vxe-column>
                  <vxe-column title="End Date" field="end_date" width="100" align="center">
                    <template #default="{ row }">
                      <EndDateCell :row="row" />
                    </template>
                  </vxe-column>
                  <vxe-column title="TAT" field="tat" width="60" align="center"></vxe-column>
                </vxe-colgroup>
              </vxe-colgroup>
            </vxe-colgroup>
          </vxe-colgroup>

          <!-- 动态日期列（展开状态） -->
          <vxe-colgroup v-for="(column, index) in columnList" :key="index">
            <template #header="{}">
              <el-row>
                <!-- 暂定 -->
              </el-row>
            </template>
            <!-- 磨削(VG) -->
            <vxe-colgroup>
              <template #header>
                <VgHeader :column="column" />
              </template>
              <!-- 磨削(HSG) -->
              <vxe-colgroup>
                <!-- 每日人力状态 (只填休假、MES等人员状态) -->
                <vxe-colgroup>
                  <!-- 星期 -->
                  <vxe-colgroup :title="column.str_week">
                    <vxe-column :title="column.day">
                      <template #default="{ row }">
                        <!-- 是否有数据 -->
                        <div v-if="isExistData(row, column)" class="h-full w-full">
                          <TaskItem :row="row" :column="column" @refresh="handleRefresh" />
                        </div>
                      </template>
                    </vxe-column>
                  </vxe-colgroup>
                </vxe-colgroup>
              </vxe-colgroup>
            </vxe-colgroup>
          </vxe-colgroup>
        </template>

        <!-- 收起状态下的表格结构 -->
        <template v-else>
          <!-- 固定列 -->
          <vxe-column type="seq" title="序号" width="60" align="center" fixed="left"></vxe-column>
          <vxe-column title="ESN" field="esn" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <EsnCell :row="row" :inputStrFlow :inputGroupType />
            </template>
          </vxe-column>
          <vxe-column title="Type" field="str_task_type" width="100" align="center" fixed="left">
          <template #default="{ row }">
              <span>{{ row.str_task_type }} | {{ row.str_sm }}</span>
            </template>
          </vxe-column>
         <!-- <vxe-column title="Start Date" field="start_date" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <StartDateCell :row="row" />
            </template>
          </vxe-column>
          <vxe-column title="End Date" field="end_date" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <EndDateCell :row="row" />
            </template>
          </vxe-column>
          -->
          <vxe-column title="GP" field="start_date"  width="100" align="center" fixed="left">
          <template #default="{ row }">
              <span>{{ row.project_start }}~{{ row.project_end }}</span>
            </template>
          </vxe-column>
          <vxe-column title="TAT" field="tat" width="60" align="center" fixed="left"></vxe-column>
          <vxe-column title="Shift" field="shift_name" width="60" align="center" fixed="left"></vxe-column>

          <vxe-colgroup v-for="(column, index) in columnList" :key="index" :title="column.str_week">
            <!-- 星期 -->
            <vxe-column :title="column.day">
              <template #default="{ row }">
                <!-- 是否有数据 -->
                <div v-if="isExistData(row, column)" class="h-full w-full">
                  <TaskItem :row="row" :column="column" @refresh="handleRefresh" :inputStrFlow :inputGroupType  />
                </div>
              </template>
            </vxe-column>
          </vxe-colgroup>
        </template>
      </vxe-table>
    </div>
  `,
})
