import { formatDate, arrayToString } from '../../utils/tools.js'
import { getCurrentUserInfo, getStaffList } from '../../api/comm/index.js'
import {
  getDepartmentsOrBranches,
  getFlexibleScheduleData,
  queryScheduleData,
  saveScheduleData,
  saveFreeShiftsData,
  validateScheduleData,
} from '../../api/schedule/index.js'

export default {
  name: 'FlexibleSchedule',
  setup() {
    const { ref, onMounted, reactive, computed } = Vue
    const { Search, Plus, Delete, Timer, User, Calendar, Check } = ElementPlusIconsVue
    const { ElMessage, ElMessageBox } = ElementPlus

    const currentDate = ref(moment().toDate())
    const scheduleData = ref([])
    const isDialogVisible = ref(false)

    const searchForm = reactive({
      department: '',
      branch: '',
    })
    const departmentOptions = ref([])
    const staffOptions = ref([])

    const fetchDepartments = async () => {
      const response = await getDepartmentsOrBranches({ isBranch: 'false' })
      departmentOptions.value = response.data
    }
    const branchOptions = ref([])

    // 修改为数组，支持多个排班信息，每个排班信息包含多个时间段
    const scheduleFormList = ref([])
    // 当前选中的日期
    const selectedDate = ref('')

    // 获取某天的排班信息
    const getScheduleForDay = (day) => {
      const schedule = scheduleData.value.find((s) => s.dt_plan === day)
      return schedule ? schedule.shifts : []
    }

    // 判断是否是今天
    const checkIsToday = (data) => {
      const today = moment()
      const date = moment({ year: data.year, month: data.month - 1, day: data.day })
      return date.isSame(today, 'day')
    }

    // 获取班次的状态类
    const getShiftClassType = (status) => {
      const statusMap = {
        行政班: 'warning',
        早班: 'primary',
        晚班: 'info',
      }
      return statusMap[status] || 'primary'
    }

    // 获取ScheduleFormList
    const fetchScheduleFormList = async () => {
      const res = scheduleData.value.find((s) => s.dt_plan === selectedDate.value)
      const params = {
        dt_plan: selectedDate.value,
        id_dept: searchForm.department,
        id_sec: searchForm.branch,
        int_type: 1,
        id: res?.id,
      }
      return await queryScheduleData(params)
    }

    const mainForm = ref({
      id: '',
      dt_plan: '',
      id_dept: '',
      id_sec: '',
      int_type: '1',
      dt_continue: '',
    })

    const handleOpenForm = async (data) => {
      // 清空mainForm
      mainForm.value = {
        id: '',
        dt_plan: '',
        id_dept: '',
        id_sec: '',
        dt_continue: '',
        int_type: '1',
      }
      // 设置选中的日期
      selectedDate.value = moment(data.day).format('YYYY-MM-DD')

      // 获取排班信息
      const res = await fetchScheduleFormList()
      if (res.main) {
        mainForm.value.id = res.main.id
        mainForm.value.dt_plan = res.main.dt_plan
        mainForm.value.id_dept = res.main.id_dept
        mainForm.value.id_sec = res.main.id_sec
        mainForm.value.dt_continue = res.main.dt_continue || ''
        mainForm.value.int_type = '1'
      } else {
        mainForm.value.dt_plan = selectedDate.value
        mainForm.value.id_dept = searchForm.department
        mainForm.value.id_sec = searchForm.branch
        mainForm.value.dt_continue = ''
        mainForm.value.int_type = '1'
      }
      if (searchForm.department) {
        // 获取员工列表
        const params = {
          id_dept: searchForm.department,
          dt_plan: selectedDate.value,
          id_sec: searchForm.branch,
        }
        fetchStaffList(params)

        // 重构数据结构：适配新的API结构，每个排班项包含人员和freeShiftTimes数组（注意：保存时用FreeShiftTimes，读取时用freeShiftTimes）
        scheduleFormList.value = []
        if (res.ptShift && res.ptShift.length > 0) {
          res.ptShift.forEach((item) => {
            const timeSlots = []

            // 处理freeShiftTimes数组
            if (item.freeShiftTimes && item.freeShiftTimes.length > 0) {
              item.freeShiftTimes.forEach((timeSlot) => {
                timeSlots.push({
                  id: null,
                  optionDatetime: null,
                  startTime: timeSlot.dt_shift_start || '',
                  endTime: timeSlot.dt_shift_end || '',
                })
              })
            } else {
              // 兼容旧的数据结构
              timeSlots.push({
                id: item.id || null,
                optionDatetime: null,
                startTime: item.dt_shift_start || '',
                endTime: item.dt_shift_end || '',
              })
            }

            scheduleFormList.value.push({
              date: selectedDate.value,
              people: item.shiftList ? item.shiftList.map((staff) => staff.id_body_s) : [],
              timeSlots: timeSlots,
            })
          })
        }

        // 如果没有值,则添加一个默认值
        if (scheduleFormList.value.length === 0) {
          scheduleFormList.value.push({
            date: selectedDate.value,
            people: [],
            timeSlots: [
              {
                id: null,
                optionDatetime: null,
                startTime: '',
                endTime: '',
              },
            ],
          })
        }
        isDialogVisible.value = true
      } else {
        ElMessage.error('请先选择部门')
      }
    }

    // 获取分部
    const fetchBranchList = async () => {
      const response = await getDepartmentsOrBranches({ isbranch: 'true', id_top: searchForm.department })
      branchOptions.value = response.data
    }

    // 获取灵活排班数据
    const fetchScheduleData = async () => {
      scheduleData.value = await getFlexibleScheduleData(searchForm.branch, searchForm.department)
    }

    const isBranchDisabled = ref(false)
    // 获取当前登录人信息
    const fetchCurrentUserInfo = async () => {
      const response = await getCurrentUserInfo()
      searchForm.department = response.id_dept_new
      searchForm.branch = response.id_sec_new
      isBranchDisabled.value = response.id_sec_new ? true : false
      fetchBranchList()
      fetchScheduleData()
    }

    // 查询函数
    const handleSearch = () => {
      fetchScheduleData()
    }

    // 获取部门下的员工列表
    const fetchStaffList = async (params) => {
      try {
        const res = await getStaffList(params)
        staffOptions.value = res.map((item) => ({
          id: item.id_body_s,
          str_name: item.str_body_s,
        }))
      } catch (error) {
        console.error('获取员工列表失败:', error)
        staffOptions.value = []
      }
    }

    // 添加新的排班表单
    const addScheduleForm = () => {
      scheduleFormList.value.push({
        date: selectedDate.value,
        people: [],
        timeSlots: [
          {
            id: null,
            optionDatetime: null,
            startTime: '',
            endTime: '',
          },
        ],
      })
    }

    // 删除排班表单
    const removeScheduleForm = (index) => {
      scheduleFormList.value.splice(index, 1)
      if (scheduleFormList.value.length === 0) {
        scheduleFormList.value.push({
          date: selectedDate.value,
          people: [],
          timeSlots: [
            {
              id: null,
              optionDatetime: null,
              startTime: '',
              endTime: '',
            },
          ],
        })
      }
    }

    // 为排班项添加时间段
    const addTimeSlot = (scheduleIndex) => {
      if (scheduleFormList.value[scheduleIndex] && scheduleFormList.value[scheduleIndex].timeSlots) {
        scheduleFormList.value[scheduleIndex].timeSlots.push({
          id: null,
          optionDatetime: null,
          startTime: '',
          endTime: '',
        })
      }
    }

    // 删除时间段
    const removeTimeSlot = (scheduleIndex, timeSlotIndex) => {
      const schedule = scheduleFormList.value[scheduleIndex]
      if (schedule && schedule.timeSlots) {
        const timeSlots = schedule.timeSlots
        if (timeSlots.length > 1) {
          timeSlots.splice(timeSlotIndex, 1)
        } else {
          ElMessage.warning('至少保留一个时间段')
        }
      }
    }

    const submitLoading = ref(false)
    // 表单提交方法
    const handleSubmit = _.debounce(async () => {
      submitLoading.value = true
      try {
        // 验证是否有排班表单
        if (scheduleFormList.value.length === 0) {
          ElMessage.error('请至少添加一个排班')
          return
        }

        // 检查是否是单个空排班的情况
        const isSingleEmptySchedule =
          scheduleFormList.value.length === 1 &&
          scheduleFormList.value[0].people.length === 0 &&
          scheduleFormList.value[0].timeSlots.every((timeSlot) => !timeSlot.startTime && !timeSlot.endTime)

        // 如果不是单个空排班，则进行正常验证
        if (!isSingleEmptySchedule) {
          // 验证每个排班表单
          for (let i = 0; i < scheduleFormList.value.length; i++) {
            const form = scheduleFormList.value[i]

            // 验证人员是否选择
            if (form.people.length === 0) {
              ElMessage.error(`排班 ${i + 1} 必须选择人员`)
              return
            }

            // 验证时间段
            if (form.timeSlots && form.timeSlots.length > 0) {
              for (let j = 0; j < form.timeSlots.length; j++) {
                const timeSlot = form.timeSlots[j]
                if (!timeSlot || !timeSlot.startTime || !timeSlot.endTime) {
                  ElMessage.error(`排班 ${i + 1} 的时间段 ${j + 1} 必须设置开始时间和结束时间`)
                  return
                }
              }
            } else {
              ElMessage.error(`排班 ${i + 1} 必须至少有一个时间段`)
              return
            }
          }
        }

        // 构建保存数据 - 按照新的API结构，每个排班项包含FreeShiftTimes数组
        const saveDataList = []
        scheduleFormList.value.forEach((form) => {
          if (form && form.people && form.people.length > 0 && form.timeSlots) {
            // 过滤出有效的时间段
            const validTimeSlots = form.timeSlots.filter(
              (timeSlot) => timeSlot && timeSlot.startTime && timeSlot.endTime,
            )

            if (validTimeSlots.length > 0) {
              saveDataList.push({
                FreeShiftTimes: validTimeSlots.map((timeSlot) => ({
                  dt_shift_start: timeSlot.startTime,
                  dt_shift_end: timeSlot.endTime,
                })),
                shiftList: staffOptions.value
                  .filter((item) => form.people.includes(item.id))
                  .map((item) => ({
                    id_body_s: item.id,
                    str_body_s: item.str_name,
                  })),
              })
            }
          }
        })

        const params = {
          main: mainForm.value,
          ptShift: saveDataList,
        }

        // 直接调用新的保存接口（暂时跳过校验，因为新接口可能有不同的校验逻辑）

        const validateRes = await validateScheduleData(params)

        if (validateRes && validateRes.length > 0) {
          ElMessageBox.confirm(validateRes.replace(/(\r\n|\r|↵)/g, '<br/>'), '提示', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(async () => {
              await saveFreeShiftsData(params)
              ElMessage.success('保存成功')
              // 刷新排班数据
              fetchScheduleData()
              // 关闭弹框
              isDialogVisible.value = false
            })
            .catch(() => {
              // 用户点击取消，不做任何操作
            })
        } else {
          // 2. 如果返回字符串没有值，直接调用保存接口pt_save_ptflexible
          await saveFreeShiftsData(params)
          ElMessage.success('保存成功')
          // 刷新排班数据
          fetchScheduleData()
          // 关闭弹框
          isDialogVisible.value = false
        }
      } catch (error) {
        if (error.message) {
          console.warn(error.message)
        } else {
          ElMessage.error('保存失败，请检查填写是否完整')
        }
      } finally {
        submitLoading.value = false
      }
      //   try {
      //     await saveFreeShiftsData(params)
      //     ElMessage.success('保存成功')
      //     fetchScheduleData()
      //     isDialogVisible.value = false
      //   } catch (error) {
      //     // 如果新接口失败，可能需要特殊处理
      //     console.error('保存失败:', error)
      //     throw error
      //   }
      // } catch (error) {
      //   if (error.message) {
      //     console.warn(error.message)
      //   } else {
      //     ElMessage.error('保存失败，请检查填写是否完整')
      //   }
      // } finally {
      //   submitLoading.value = false
      // }
    }, 500)

    // 选择部门
    const handleDepartmentChange = (val) => {
      searchForm.branch = ''
      fetchBranchList()
    }

    // 判断是否是第二天
    const isNextDay = (startTime, endTime) => {
      if (startTime && endTime) {
        return endTime < startTime
      }
      return false
    }

    const optionDatetime = ref([
      { key: '03:00~07:30', value: ['03:00', '07:30'] },
      { key: '07:00~11:30', value: ['07:00', '11:30'] },
      { key: '08:00~12:00', value: ['08:00', '12:00'] },
      { key: '08:30~12:00', value: ['08:30', '12:00'] },
      { key: '08:30~12:30', value: ['08:30', '12:30'] },
      { key: '12:00~15:30', value: ['12:00', '15:30'] },
      { key: '12:30~17:00', value: ['12:30', '17:00'] },
      { key: '12:30~17:30', value: ['12:30', '17:30'] },
      { key: '13:00~17:00', value: ['13:00', '17:00'] },
      { key: '15:00~18:30', value: ['15:00', '18:30'] },
      { key: '19:00~23:30', value: ['19:00', '23:30'] },
      { key: '23:00~03:00', value: ['23:00', '03:00'] },
    ])

    // 处理快捷时间选择
    const handleTimeRangeChange = (val, timeSlot) => {
      if (val && val.length === 2) {
        timeSlot.startTime = val[0]
        timeSlot.endTime = val[1]
      }
    }

    // 统一处理删除按钮点击事件
    const handleRemoveOrDeleteSchedule = async (form, index) => {
      try {
        await ElMessageBox.confirm('确定要移除这条排班记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        // 用户确认后，直接本地移除
        removeScheduleForm(index)

        ElMessage.success('移除成功')
      } catch (error) {
        // 用户点击取消或其他错误
        if (error !== 'cancel') {
          console.error('移除排班失败:', error)
        }
      }
    }

    // 获取人员的显示文本
    const getPeopleText = (people) => {
      if (!people || people.length === 0) {
        return '未分配人员'
      }
      const selectedStaff = staffOptions.value.filter((staff) => people.includes(staff.id))
      return selectedStaff.map((staff) => staff.str_name).join(', ')
    }

    // 获取时间段的显示文本
    const getTimeSlotText = (timeSlot) => {
      if (!timeSlot.startTime || !timeSlot.endTime) {
        return '未设置时间'
      }
      return `${timeSlot.startTime} - ${timeSlot.endTime}`
    }

    // 限制日期选择器只能选择今天之后的日期
    const disabledDate = (date) => {
      const currentDate = moment(selectedDate.value).startOf('day')
      return moment(date).isBefore(currentDate)
    }

    onMounted(() => {
      fetchCurrentUserInfo()
      fetchDepartments()
    })

    return {
      currentDate,
      Search,
      Plus,
      Delete,
      Timer,
      User,
      Calendar,
      Check,
      isDialogVisible,
      scheduleFormList,
      selectedDate,
      searchForm,
      departmentOptions,
      branchOptions,
      staffOptions,
      isBranchDisabled,
      optionDatetime,
      submitLoading,
      mainForm,
      // 方法
      isNextDay,
      getScheduleForDay,
      checkIsToday,
      getShiftClassType,
      formatDate,
      handleSearch,
      handleSubmit,
      handleDepartmentChange,
      arrayToString,
      addScheduleForm,
      removeScheduleForm,
      addTimeSlot,
      removeTimeSlot,
      handleOpenForm,
      handleTimeRangeChange,
      handleRemoveOrDeleteSchedule,
      getPeopleText,
      getTimeSlotText,
      disabledDate,
    }
  },
  template: /*html*/ `
    <div class="min-h-screen bg-gray-50 p-4">
      <!-- 搜索栏 -->
      <div class="mb-6 rounded-xl bg-white p-6 shadow-sm">
        <el-form :model="searchForm" inline class="flex items-center gap-4">
          <el-form-item label="部门选择">
            <el-select
              v-model="searchForm.department"
              placeholder="请选择部门"
              clearable
              class="!w-48"
              @change="handleDepartmentChange"
            >
              <el-option v-for="item in departmentOptions" :key="item.id" :label="item.str_name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="分部选择">
            <el-select
              v-model="searchForm.branch"
              placeholder="请选择分部"
              clearable
              class="!w-48"
              :disabled="isBranchDisabled"
            >
              <el-option v-for="item in branchOptions" :key="item.id" :label="item.str_name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" class="px-6">
              <el-icon class="mr-2"><search /></el-icon>
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 日历 -->
      <div class="rounded-xl bg-white p-6 shadow-sm">
        <el-calendar v-model="currentDate" class="custom-calendar">
          <template #date-cell="{ data }">
            <div
              class="calendar-cell flex h-full cursor-pointer flex-col rounded-lg p-2 transition-colors duration-200 hover:bg-blue-50"
              @click="handleOpenForm(data)"
            >
              <!-- 日期显示 -->
              <div class="mb-2 flex items-center justify-between">
                <span class="text-sm font-medium text-black">{{ formatDate(data.day, 'MM-DD') }}</span>
                <el-icon v-if="getScheduleForDay(data.day).length > 0" class="text-xs text-green-500">
                  <User />
                </el-icon>
              </div>

              <!-- 排班信息 -->
              <div class="space-y-1 overflow-hidden">
                <template v-for="shift in getScheduleForDay(data.day)" :key="shift.time">
                  <div class="rounded-md border-l-3 border-blue-400 bg-gradient-to-r from-blue-100 to-blue-50 p-2">
                    <div class="flex items-center text-xs">
                      <el-icon class="mr-1 text-blue-600"><Timer /></el-icon>
                      <span class="font-medium text-blue-800">{{ shift.dt_shift_start }}-{{ shift.dt_shift_end }}</span>
                    </div>
                    <el-tooltip :content="arrayToString(shift.staffs)" placement="top" :show-after="200">
                      <div class="mt-1 truncate text-xs text-gray-600">
                        <el-icon class="mr-1"><User /></el-icon>
                        {{ arrayToString(shift.staffs) }}
                      </div>
                    </el-tooltip>
                  </div>
                </template>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
    </div>

    <!-- 排班编辑对话框 -->
    <el-dialog v-model="isDialogVisible" width="90%" :close-on-click-modal="false" class="common-dialog">
      <template #header>
        <div class="flex items-center">
          <div class="flex items-center">
            <el-icon class="mr-2 text-xl text-blue-600"><Calendar /></el-icon>
            <span class="text-lg font-bold">{{ selectedDate }} 排班安排</span>
          </div>
          <div class="ml-8 flex items-center">
            <label class="mr-2 text-sm text-gray-500">沿用至：</label>
            <el-date-picker
              v-model="mainForm.dt_continue"
              type="date"
              placeholder="选择沿用结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
              class="w-48"
              clearable
            />
          </div>
        </div>
      </template>
      <div class="space-y-6">
        <!-- 操作区域 -->
        <div
          class="flex items-center justify-between rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4"
        >
          <div class="flex items-center">
            <el-icon class="mr-2 text-blue-600"><Timer /></el-icon>
            <span class="text-sm text-gray-600">每个排班可以设置多个时间段</span>
          </div>
          <el-button type="primary" @click="addScheduleForm" class="px-6">
            <el-icon class="mr-2"><Plus /></el-icon>
            添加排班
          </el-button>
        </div>

        <!-- 排班列表 -->
        <div class="space-y-6">
          <div
            v-for="(form, scheduleIndex) in scheduleFormList"
            :key="scheduleIndex"
            class="rounded-lg border border-gray-200 bg-white p-6 transition-shadow duration-200 hover:shadow-md"
          >
            <!-- 排班项头部 -->
            <div class="mb-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
                  排班 {{ scheduleIndex + 1 }}
                </div>
              </div>
              <el-button
                type="danger"
                size="small"
                @click="handleRemoveOrDeleteSchedule(form, scheduleIndex)"
                class="!px-3"
              >
                <el-icon><Delete /></el-icon>
                移除排班
              </el-button>
            </div>

            <!-- 人员选择 -->
            <div class="mb-4">
              <el-form-item label="安排人员" label-width="120px">
                <el-select
                  v-model="form.people"
                  filterable
                  multiple
                  collapse-tags-tooltip
                  placeholder="选择需要安排的人员"
                  :disabled="!searchForm.department"
                  class="w-full"
                >
                  <el-option v-for="item in staffOptions" :key="item.id" :label="item.str_name" :value="item.id">
                    <div class="flex items-center">
                      <el-icon class="mr-2 text-green-500"><User /></el-icon>
                      {{ item.str_name }}
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <!-- 时间段列表 -->
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <h4 class="text-md font-medium text-gray-700">时间段安排</h4>
                <el-button type="primary" size="small" @click="addTimeSlot(scheduleIndex)" class="!px-3">
                  <el-icon class="mr-1"><Plus /></el-icon>
                  添加时间段
                </el-button>
              </div>

              <div
                v-for="(timeSlot, timeSlotIndex) in (form.timeSlots || [])"
                :key="timeSlotIndex"
                class="rounded-lg border border-gray-200 bg-gray-50 p-4"
              >
                <!-- 时间段头部 -->
                <div class="mb-3 flex items-center justify-between">
                  <div class="flex items-center">
                    <el-icon class="mr-2 text-blue-500"><Timer /></el-icon>
                    <span class="text-sm font-medium text-gray-700">时间段 {{ timeSlotIndex + 1 }}</span>
                  </div>
                  <el-button
                    v-if="form.timeSlots && form.timeSlots.length > 1"
                    type="danger"
                    size="small"
                    @click="removeTimeSlot(scheduleIndex, timeSlotIndex)"
                    class="!px-2"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>

                <!-- 时间段表单 -->
                <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <!-- 快捷时间选择 -->
                  <el-form-item label="快捷时间" label-width="80px">
                    <el-select
                      v-model="timeSlot.optionDatetime"
                      filterable
                      placeholder="选择常用时间"
                      class="w-full"
                      @change="handleTimeRangeChange(timeSlot.optionDatetime, timeSlot)"
                      clearable
                    >
                      <el-option v-for="item in optionDatetime" :key="item.key" :label="item.key" :value="item.value">
                        <div class="flex items-center">
                          <el-icon class="mr-2 text-blue-500"><Timer /></el-icon>
                          {{ item.key }}
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <!-- 开始时间 -->
                  <el-form-item label="开始时间" label-width="80px">
                    <el-time-picker
                      v-model="timeSlot.startTime"
                      format="HH:mm"
                      value-format="HH:mm"
                      placeholder="选择开始时间"
                      class="w-full"
                    />
                  </el-form-item>

                  <!-- 结束时间 -->
                  <el-form-item label="结束时间" label-width="80px">
                    <div class="flex items-center">
                      <el-time-picker
                        v-model="timeSlot.endTime"
                        format="HH:mm"
                        value-format="HH:mm"
                        placeholder="选择结束时间"
                        class="w-full"
                      />
                      <div v-if="isNextDay(timeSlot.startTime, timeSlot.endTime)" class="ml-3 flex items-center">
                        <el-tag type="warning" size="small">
                          <el-icon class="mr-1"><Timer /></el-icon>
                          跨天
                        </el-tag>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="scheduleFormList.length === 0" class="py-12 text-center">
            <el-icon class="mb-4 text-6xl text-gray-300"><Calendar /></el-icon>
            <p class="mb-4 text-gray-500">暂无排班安排</p>
            <el-button type="primary" @click="addScheduleForm">
              <el-icon class="mr-2"><Plus /></el-icon>
              添加第一个排班
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="isDialogVisible = false" size="large">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading" size="large" class="px-8">
            <el-icon v-if="!submitLoading" class="mr-2"><Check /></el-icon>
            {{ submitLoading ? '保存中...' : '保存排班' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  `,
}
