export function useType(tableData, tableRef) {
  /**
   * define type dialog state
   */
  const typeState = Vue.reactive({
    visible: false,
    type: '',
    currentRow: null,
  })
  /* 获取当前排序 */
  const getCurrentSort = () => {
    if (tableData.value.length === 0) {
      const data = tableRef.value.getTableData().tableData
      return data.filter((item) => item.int_level === 2).length
    }
    return tableData.value.filter((item) => item.int_level === 2).length
  }
  // handle save type
  const handleSaveType = async () => {
    const $table = tableRef.value
    const rid = Date.now()
    const count = getCurrentSort()
    const record = {
      id: rid,
      int_level: 2,
      str_node: typeState.type,
      int_tat: '',
      id_task_ago: '',
      str_task_ago: '',
      id_task: '',
      id_root: typeState.currentRow.id,
      int_sort: count + 1,
    }
    await $table.insertAt(record, -1)
    // 将父节点展开
    await $table.setTreeExpand(typeState.currentRow, true)
    typeState.visible = false
    handleClearType()
  }
  // handle clear type
  const handleClearType = () => {
    typeState.type = ''
  }

  return {
    typeState,
    handleSaveType,
    handleClearType,
  }
}
