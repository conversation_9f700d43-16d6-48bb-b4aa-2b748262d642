import { post } from '../../config/axios/httpReuest.js'
/** 模拟列表操作按钮 */
export function useSim() {
  /**保存模拟列表数据状态 */
  const SetSimDetailsStatusApi = async (data) => {
    const params = {
      ac: 'de_set_sim_details_status',
      is_csm: data.is_csm,
      is_lock: data.is_lock,
      int_csm_result: data.int_csm_result,
      ids: data.ids,
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      ElementPlus.ElMessage.success('保存完成')
    } else {
      ElementPlus.ElMessage.error('type:' + type + data.text)
      return []
    }
  }

  return {
    SetSimDetailsStatusApi,

  }
}
