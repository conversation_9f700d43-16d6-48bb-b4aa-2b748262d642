/**
 * @description teamRadioSummary
 */
const { ref, reactive, onMounted } = Vue
import { post } from '../../../config/axios/httpReuest.js'
export default {
  name: 'teamRadioSummary',
  setup(props, { emit }) {
    const searchForm = reactive({
      dtYear: moment().startOf('year').format('YYYY'),
    })
    const tableColumns = [
      { field: 'str_team_category', title: 'Category' },
      { field: 'str_flow', title: 'Flow' },
      { field: 'str_team', title: 'Sect/Team' },
      { field: 'dec_jan', title: 'Jan' },
      { field: 'dec_feb', title: 'Feb' },
      { field: 'dec_mar', title: 'Mar' },
      { field: 'dec_apr', title: 'Apr' },
      { field: 'dec_may', title: 'May' },
      { field: 'dec_jun', title: 'Jun' },
      { field: 'dec_jul', title: 'Jul' },
      { field: 'dec_aug', title: 'Aug' },
      { field: 'dec_sep', title: 'Sep' },
      { field: 'dec_oct', title: 'Oct' },
      { field: 'dec_nov', title: 'Nov' },
      { field: 'dec_dec', title: 'Dec' },
      { field: 'dec_avg', title: 'Avg' }
    ]
    const tableRef = ref()
    const tableData = ref([])
    const queryData = ref([])
    // 初始化表格数据
    const initTableData = async () => {
      const params = {
        ac: 'pe_search_team_jixiao',
        str_year: searchForm.dtYear,
      }
      const { data: res } = await post(params)
      const { data } = res
      queryData.value = data
    }

    const handleSearch = async () => {
      await initTableData()
      await handlePageData()
    }

    const exportEvent = () => {
      const $table = tableRef.value
      if ($table) {
        $table.exportData({
          type: 'csv',
        })
      }
    }

    const loading = ref(false)
    // 前端分页
    const handlePageData = async () => {
      loading.value = true
      const { pageSize, currentPage } = pageVO
      pageVO.total = queryData.value.length
      tableData.value = queryData.value.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize,
      )
      loading.value = false
    }
    const pageChange = ({ pageSize, currentPage }) => {
      pageVO.currentPage = currentPage
      pageVO.pageSize = pageSize
      handlePageData()
    }
    const pageVO = reactive({
      total: 0,
      currentPage: 1,
      pageSize: 10,
    })

    onMounted(async () => {
      await initTableData()
      await handlePageData()
    })
    return {
      tableRef,
      searchForm,
      pageVO,
      exportEvent,
      pageChange,
      tableColumns,
      tableData,
      handleSearch,
      handlePageData,
    }
  },
  template: /*html*/ `
    <div class="m-4">
      <el-form :model="searchForm" inline>
        <el-form-item label="Year">
          <el-date-picker
            v-model="searchForm.dtYear"
            type="year"
            placeholder="Select Year"
            value-format="YYYY"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">Search</el-button>
        </el-form-item>
        <el-form-item>
          <vxe-button @click="exportEvent">导出</vxe-button>
        </el-form-item>
        <el-form-item> </el-form-item>
      </el-form>
    </div>
    <div class="m-4">
      <vxe-table
        ref="tableRef"
        :loading="loading"
        :data="tableData"
        stripe
        height="600"
        :column-config="{resizable: true}"
        :row-config="{isHover: true}"
      >
        <vxe-column type="seq" title="Seq" width="70"></vxe-column>
        <vxe-column
          v-for="(col, index1) in tableColumns"
          :field="col.field"
          :title="col.title"
          sortable
        ></vxe-column>
      </vxe-table>
      <vxe-pager
        v-model:currentPage="pageVO.currentPage"
        v-model:pageSize="pageVO.pageSize"
        :total="pageVO.total"
        @page-change="pageChange"
      >
      </vxe-pager>
    </div>
  `,
}
