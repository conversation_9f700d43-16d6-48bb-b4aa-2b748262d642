import { post } from '../../config/axios/httpReuest.js';

const { reactive, ref, onMounted } = Vue;
export default {
  name: 'ContainerTransportationPlanComponent',
  setup() {
    const searchForm = reactive({
      date: [],
      dt_date: '',
      str_esn: '',
      str_rule: '',
      str_box: '',
      str_name: '',
      teamtype: '',
    });

    const tableData = ref([]);

    // get table data
    const getTableData = async () => {
      const tempList = [];
      //  从searchForm中先过滤出date字段
      for (const key in searchForm) {
        if (searchForm[key]) {
          tempList.push({
            key,
            value: searchForm[key],
          });
        }
      }
      const queryList = tempList.filter((item) => item.key !== 'date').map((item) => {
        return {
          str_key: item.key,
          str_value: item.value,
        };
      });
      const params = {
        ac: 'pda_node_f3_transport',
        queryList,
        dt_begin: searchForm.date[0],
        dt_end: searchForm.date[1],
      };
      const { data } = await post(params);
      tableData.value = data.data;
    };

    const getRefreshData = async () => {
      const params = {
        ac: 'pda_getpsmbegindate',
      };
      await post(params);
      await getTableData();
    };

    onMounted(() => {
      getTableData();
    });

    const tableHeaderClass = ({ row, column, rowIndex, columnIndex }) => {
     return 'table-header';
    };

    return {
      searchForm,
      tableData,
      getTableData,
      getRefreshData,
      tableHeaderClass
    };
  },
  // language=html
  template: `
    <!--    头部搜索-->
    <el-form :model="searchForm" inline label-width="100px" size="small">
      <el-form-item label="日期:">
        <el-date-picker v-model="searchForm.date" type="daterange" value-format="YYYY-MM-DD" clearable></el-date-picker>
      </el-form-item>
      <el-form-item label="交付日期:">
        <el-date-picker v-model="searchForm.dt_date" type="date" value-format="YYYY-MM-DD"
                        clearable></el-date-picker>
      </el-form-item>
      <el-form-item label="发动机:">
        <el-input v-model="searchForm.str_esn" clearable></el-input>
      </el-form-item>
      <el-form-item label="单元体:">
        <el-input v-model="searchForm.str_rule" clearable></el-input>
      </el-form-item>
      <el-form-item label="集件箱号:">
        <el-input v-model="searchForm.str_box" clearable></el-input>
      </el-form-item>
      <el-form-item label="班组长:">
        <el-input v-model="searchForm.str_name" clearable></el-input>
      </el-form-item>
      <el-form-item label="交付地点:">
        <el-input v-model="searchForm.teamtype" clearable></el-input>
      </el-form-item>
      <el-form-item label=" ">
        <el-button type="primary" circle @click="getTableData">
          <template #icon>
            <el-icon>
              <Search></Search>
            </el-icon>
          </template>
        </el-button>
        <el-button type="primary" circle @click="getRefreshData">
          <template #icon>
            <el-icon>
              <Refresh></Refresh>
            </el-icon>
          </template>
        </el-button>
      </el-form-item>
    </el-form>

    <!--    表格-->
    <el-table :data="tableData" border stipe :header-cell-class-name="tableHeaderClass">
      <el-table-column type="index" label="#" width="80"></el-table-column>
      <el-table-column prop="dt_date" label="交付日期"></el-table-column>
      <el-table-column prop="str_esn" label="发动机"></el-table-column>
      <el-table-column prop="str_rule" label="单元体"></el-table-column>
      <el-table-column prop="str_box" label="集件箱号"></el-table-column>
      <el-table-column prop="str_name" label="班组长"></el-table-column>
      <el-table-column prop="teamtype" label="交付地点"></el-table-column
    </el-table>
  `,
};
