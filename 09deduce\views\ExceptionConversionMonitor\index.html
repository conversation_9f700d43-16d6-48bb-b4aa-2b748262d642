<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!--    全部样式-->
    <link href="../../../assets/css/index.css" rel="stylesheet" />
    <!--    引入element-plus的样式-->
    <link href="../../../assets/element-plus@2.5.5/index.css" rel="stylesheet" />
    <!--    引入drawer的样式-->
    <link href="../../../assets/css/drawer.css" rel="stylesheet" />
    <!--    引入vxe-table的样式-->
    <link href="../../../assets/vxe-table/style.css" rel="stylesheet" />
    <!--    引入自定义样式-->
    <link href="./styles.css" rel="stylesheet" />
    <!--    引入VUE-->
    <script src="../../../assets/vue@3.4.15/vue.global.prod.js"></script>
    <!--    引入vxe-table组件-->
    <script src="../../../assets/vxe-table/xe-utils.js"></script>
    <script src="../../../assets/vxe-table/vxe-table.js"></script>
    <!--    引入element-plus-->
    <script src="../../../assets/element-plus@2.5.5/index.js"></script>
    <!--  引入element-plus-icon-->
    <script src="../../../assets/icons-vue@2.3.1/index.iife.min.js"></script>
    <!-- 引入element-plus-ch -->
    <script src="../../../assets/element-plus@2.5.5/lang/zh-cn.js"></script>
    <!-- 引入mock -->
    <script src="../../../assets/mock/mock.js"></script>
    <!--    引入axios-->
    <script src="../../../assets/axios@1.6.7/axios.min.js"></script>
    <!--    引入@vueuse/core-->
    <script src="../../../assets/vueuse/core@10.7.2/index.iife.min.js"></script>
    <script src="../../../assets/vueuse/shared@10.7.2/index.iife.min.js"></script>
    <!--    引入moment-->
    <script src="../../../assets/moment/moment.min.js"></script>
    <script src="../../../03team_new/comm/api_environment.js"></script>
    <!--    引入echarts-->
    <script src="../../../assets/echarts@5.5.0/echarts.min.js"></script>
    <title>异常转换监控</title>
  </head>

  <body>
    <div id="app">
      <exception-conversion-monitor></exception-conversion-monitor>
    </div>
  </body>
  <script type="module">
    try {
      console.log('开始加载组件...')
      
      const ExceptionConversionMonitor = await import('./index.js').then(module => {
        console.log('主组件加载成功')
        return module.default
      }).catch(error => {
        console.error('主组件加载失败:', error)
        throw error
      })
      
      // 可选的导入，如果失败不影响主要功能
      try {
        await import('../../../components/VxeTable/renderer/index.js')
        console.log('VxeTable renderer 加载成功')
      } catch (e) {
        console.warn('VxeTable renderer 加载失败，但不影响主要功能:', e.message)
      }
      
      moment.updateLocale('en', {
        week: {
          dow: 1,
        },
      })
      
      const { createApp } = Vue
      const app = createApp({
        components: {
          ExceptionConversionMonitor,
        },
      })
      
      // 注册Element Plus图标
      for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        app.component(key, component)
      }
      
      // 可选的directive注册
      if (window.roleDirective) {
        app.directive('role', window.roleDirective)
      }
      
      app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
      })
      app.use(VXETable)
      
      console.log('开始挂载应用...')
      app.mount('#app')
      console.log('应用挂载成功!')
      
    } catch (error) {
      console.error('应用初始化失败:', error)
      
      // 显示错误信息给用户
      document.getElementById('app').innerHTML = `
        <div style="padding: 20px; text-align: center; color: #c53030; background: #fff5f5; border: 1px solid #fed7d7; border-radius: 8px; margin: 20px;">
          <h2>页面加载失败</h2>
          <p>错误信息: ${error.message}</p>
          <p>请检查控制台获取详细错误信息</p>
          <button onclick="location.reload()" style="padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">
            重新加载页面
          </button>
        </div>
      `
    }
  </script>
</html>
