const { ref, onMounted, onBeforeUnmount } = Vue

export function usePerformanceMonitor() {
  const fps = ref(0)
  const memoryUsage = ref(0)
  const isPerformanceGood = ref(true)
  
  let frameCount = 0
  let lastTime = performance.now()
  let animationFrameId = null
  
  // FPS 监控
  const measureFPS = () => {
    frameCount++
    const currentTime = performance.now()
    
    if (currentTime - lastTime >= 1000) {
      fps.value = Math.round((frameCount * 1000) / (currentTime - lastTime))
      frameCount = 0
      lastTime = currentTime
      
      // 判断性能状态
      isPerformanceGood.value = fps.value >= 30
      
      if (fps.value < 20) {
        console.warn('性能警告: FPS过低', fps.value)
      }
    }
    
    animationFrameId = requestAnimationFrame(measureFPS)
  }
  
  // 内存使用监控
  const measureMemory = () => {
    if (performance.memory) {
      const used = performance.memory.usedJSHeapSize
      const total = performance.memory.totalJSHeapSize
      memoryUsage.value = Math.round((used / total) * 100)
      
      if (memoryUsage.value > 80) {
        console.warn('内存使用警告:', memoryUsage.value + '%')
      }
    }
  }
  
  // 节流函数
  const throttle = (func, limit) => {
    let inThrottle
    return function() {
      const args = arguments
      const context = this
      if (!inThrottle) {
        func.apply(context, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }
  
  // 防抖函数
  const debounce = (func, wait) => {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }
  
  // 优化DOM操作
  const batchDOMUpdates = (updates) => {
    requestAnimationFrame(() => {
      updates.forEach(update => update())
    })
  }
  
  // 内存清理
  const cleanup = () => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
    }
  }
  
  // 性能优化建议
  const getPerformanceAdvice = () => {
    const advice = []
    
    if (fps.value < 30) {
      advice.push('FPS过低，建议减少动画效果或降低刷新频率')
    }
    
    if (memoryUsage.value > 70) {
      advice.push('内存使用过高，建议清理不必要的数据')
    }
    
    return advice
  }
  
  onMounted(() => {
    measureFPS()
    
    // 每5秒监控一次内存
    const memoryInterval = setInterval(measureMemory, 5000)
    
    onBeforeUnmount(() => {
      cleanup()
      clearInterval(memoryInterval)
    })
  })
  
  return {
    fps,
    memoryUsage,
    isPerformanceGood,
    throttle,
    debounce,
    batchDOMUpdates,
    getPerformanceAdvice,
    cleanup
  }
} 