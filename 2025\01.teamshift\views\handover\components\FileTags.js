export default {
  name: 'FileTagsComponent',
  props: {
    files: {
      type: Array,
      required: true,
    },
    maxTagLength: {
      type: Number,
      default: 12,
    },
  },
  emits: ['view'],
  setup(props, { emit }) {
    // 获取文件名（不含扩展名）
    const getFileName = (fileName) => {
      if (!fileName) return ''
      const parts = fileName.split('.')
      if (parts.length > 1) {
        parts.pop() // 移除最后一个扩展名部分
      }
      return parts.join('.')
    }

    // 截取文件名
    const truncateFileName = (fileName, maxLength = props.maxTagLength) => {
      if (!fileName || fileName.length <= maxLength) return fileName
      return fileName.substring(0, maxLength) + '...'
    }
    
    // 处理查看事件
    const handleView = () => {
      emit('view')
    }
    
    return {
      getFileName,
      truncateFileName,
      handleView,
    }
  },
  template: /*html*/ `
    <div class="flex flex-wrap items-center gap-1">
      <!-- 显示第一个文件 -->
      <template v-if="files.length > 0">
        <el-tooltip :content="files[0].str_file_name || files[0].name" placement="top">
          <el-tag size="small" class="ml-1 cursor-pointer" @click="handleView">
            {{ truncateFileName(getFileName(files[0].str_file_name || files[0].name)) }}
          </el-tag>
        </el-tooltip>

        <!-- 如果有更多文件，显示 +N -->
        <el-tooltip
          v-if="files.length > 1"
          :content="'还有 ' + (files.length - 1) + ' 个附件'"
          placement="top"
        >
          <el-tag size="small" type="info" class="cursor-pointer" @click="handleView">
            +{{ files.length - 1 }}
          </el-tag>
        </el-tooltip>
      </template>
      <template v-else>
        <span class="text-gray-400 text-sm">无附件</span>
      </template>
    </div>
  `,
} 