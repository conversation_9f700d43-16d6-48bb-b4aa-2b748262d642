export const useFileHandler = () => {
  const { ref } = Vue
  const { ElMessage, ElLoading } = ElementPlus

  // 抽屉可见性控制
  const drawerVisible = ref(false)
  // 当前选中的文件列表
  const currentFiles = ref([])

  // 定义允许的文件类型和大小限制
  const ALLOWED_FILE_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'text/plain',
    'application/zip',
    'application/x-rar-compressed',
  ]
  const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

  // 检查文件安全性
  const checkFileSecurity = (file) => {
    // 检查文件类型
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      ElMessage.error(`不支持的文件类型: ${file.name}`)
      return false
    }

    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      ElMessage.error(
        `文件过大 (${(file.size / 1024 / 1024).toFixed(2)}MB), 最大限制 ${MAX_FILE_SIZE / 1024 / 1024}MB: ${file.name}`,
      )
      return false
    }

    // 检查文件名安全性
    const dangerousChars = /[<>:"/\\|?*\x00-\x1F]/g // 禁止包含的特殊字符
    const consecutiveDots = /\.{2,}/g // 禁止连续的点
    const reservedNames = /^(con|prn|aux|nul|com[0-9]|lpt[0-9])$/i // Windows保留文件名

    if (dangerousChars.test(file.name)) {
      ElMessage.error(`文件名包含非法字符: ${file.name}`)
      return false
    }

    if (consecutiveDots.test(file.name)) {
      ElMessage.error(`文件名不能包含连续的点: ${file.name}`)
      return false
    }

    const nameWithoutExt = file.name.split('.')[0].toLowerCase()
    if (reservedNames.test(nameWithoutExt)) {
      ElMessage.error(`文件名不能使用系统保留名: ${file.name}`)
      return false
    }

    return true
  }

  // 获取文件名（不含扩展名）
  const getFileName = (fileName) => {
    if (!fileName) return ''
    const parts = fileName.split('.')
    if (parts.length > 1) {
      parts.pop() // 移除最后一个扩展名部分
    }
    return parts.join('.')
  }

  // 截取文件名
  const truncateFileName = (fileName, maxLength = 12) => {
    if (!fileName || fileName.length <= maxLength) return fileName
    return fileName.substring(0, maxLength) + '...'
  }

  // 查看文件
  const handleViewFiles = (row) => {
    if (!row.attachment || row.attachment.length === 0) {
      ElMessage.warning('没有附件')
      return
    }

    currentFiles.value = row.attachment || []
    drawerVisible.value = true
  }

  // 下载文件
  const handleDownload = (row) => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在下载...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
      // 不用window.open，而是用a标签下载
      const a = document.createElement('a')
      a.href = row.str_path
      a.download = row.str_file_name || row.name
      a.click()
      a.remove()

      loading.close()
    } catch (error) {
      ElMessage.error('下载失败：' + (error.message || '未知错误'))
      loading.close()
    }
  }

  // 删除文件
  const handleDelete = (row) => {
    currentFiles.value = currentFiles.value.filter((file) => file.id !== row.id)
  }

  return {
    drawerVisible,
    currentFiles,
    ALLOWED_FILE_TYPES,
    MAX_FILE_SIZE,
    checkFileSecurity,
    getFileName,
    truncateFileName,
    handleViewFiles,
    handleDownload,
    handleDelete,
  }
}
