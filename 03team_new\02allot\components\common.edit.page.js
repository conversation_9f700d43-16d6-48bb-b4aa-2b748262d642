Vue.component('allot-edit-page', {
  props: ['is_allot_show', 'dialogData', 'input_page_type', 'allot_table', 'input_str_esn_type','input_group_type'],
  template: `
        <el-dialog
            ref="allot_dialog"
            title="Allot" 
            :width="dialogWidth" 
            :visible.sync="isAllotShow" 
            :close-on-press-escape='false'
            :show-close='false'
            class="self_dialog" 
        >
            <el-row  style="margin-bottom: 10px;">
                <el-col :span="6">
                    <span class='edit_heade_color'>ESN</span>:&nbsp;&nbsp;&nbsp;<span>{{dialog_data.main.str_code}}-{{dialog_data.main.str_esn}}</span>
                </el-col>
                <el-col :span='5' :offset='1'>
                    <span class='edit_heade_color'>Wip Flow</span>:&nbsp;&nbsp;&nbsp;<span>{{dialog_data.main.wip_flow}}</span>
                </el-col>
                <el-col :span='6' :offset='1' v-if="is_show_f4_1">
                <!-- fan core lpt  endTime < dt_f41_update -->
                    <span class='edit_heade_color' >F4-3</span>:&nbsp;&nbsp;&nbsp;<span>{{dialog_data.main.dt_f43_update}}</span>
                </el-col>
                <el-col v-if="is_show_f1_1" :span='6' :offset='1'>
                <!-- fan core lpt  endTime < dt_f41_update -->
                    <span class='edit_heade_color'>F1-1</span>:&nbsp;&nbsp;&nbsp;<span>{{dialog_data.main.dt_f11_update}}</span>
                </el-col>               
                <el-col v-if="is_show_f4_2" :span='6' :offset='1'>
                <!-- fan core lpt  endTime < dt_f41_update -->
                    <span class='edit_heade_color'>F4-2</span>:&nbsp;&nbsp;&nbsp;<span>{{dialog_data.main.dt_f42_update}}</span>
                </el-col>               
                <el-col :span='5' v-if="is_show_f4_1">
                <!-- b1 endTime < dt_release_update --> 
                    <span class='edit_heade_color'>Release</span>:&nbsp;&nbsp;&nbsp;<span>{{dialog_data.main.dt_release_update}}</span>
                </el-col>
            </el-row>
            <el-table 
                :data='allot_table' 
                tooltip-effect='dark' 
                border
                :cell-class-name='changeStatus'
                style="width: 100%"
                :key="allot_flow_key"
            >
                <el-table-column type='index' label='Item' fixed width='60' align="center"></el-table-column>
                <el-table-column label='Type' width="100rem">
                    <template slot-scope="{row}">
                        <span class='newheadtitle'>{{ row.str_type }}</span>
                    </template>
                </el-table-column>
                <el-table-column label='Team' >
                    <template slot-scope="scope">           
                        <el-select 
                            v-model="scope.row.id_teams"
                            filterable  
                            multiple
                            placeholder="select team"
                            @change="teamselect($event,scope.row)"
                        >
                            <el-option v-for="item in get_team_by_type(scope.row.str_type,teams)" :key="item.id" :label="item.str_name" :value="item.id"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label='Start' >
                    <template slot-scope="scope">
                        <el-date-picker  
                            v-model="scope.row.dt_start" 
                            type="date"  
                            value-format='yyyy-MM-dd' 
                            placeholder="选择日期"
                        ></el-date-picker>
                    </template>
                </el-table-column>

                <el-table-column label='End' >
                    <template slot-scope="scope">
                        <el-date-picker 
                            v-model="scope.row.dt_end" 
                            type="date" 
                             value-format='yyyy-MM-dd' 
                             placeholder="选择日期" 
                        ></el-date-picker>
                    </template>
                </el-table-column>
                <el-table-column v-if="is_show_f42_test" label='F4-2 Start' >
                    <template slot-scope="scope">
                        <el-date-picker 
                            v-model="scope.row.dt_test_start" 
                            type="date" 
                             value-format='yyyy-MM-dd' 
                             placeholder="选择日期" 
                        ></el-date-picker>
                    </template>
                </el-table-column>
                <el-table-column  v-if="is_show_f42_test" label='F4-2 End' >
                    <template slot-scope="scope">
                        <el-date-picker 
                            v-model="scope.row.dt_test_end" 
                            type="date" 
                             value-format='yyyy-MM-dd' 
                             placeholder="选择日期" 
                        ></el-date-picker>
                    </template>
                </el-table-column>

                <el-table-column v-if="is_show_bsi" label='Inspector' >
                
                    <template slot-scope="scope">
                        <el-select  
                            filterable  
                            v-model="scope.row.str_id_surveys" 
                            multiple 
                            placeholder="select surveyor"
                        >
                            <el-option v-for="item in scope.row.staffs" :key="item.id" :label="item.str_name" :value="item.id"></el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column v-if="is_show_f4_1" label='site' >
                    <template slot-scope="scope">
                        <el-select  
                            filterable  
                            v-model="scope.row.id_site" 
                            placeholder="select site"
                        >
                            <el-option v-for="item in sites" :key="item.id" :label="item.str_name" :value="item.id"></el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column v-if="!is_show_bsi && !is_show_cfm_f1_1" label='Start Project' >
                    <template slot-scope="scope">
                        <span class='newheadtitle'>{{ scope.row.dt_start_project }}</span>
                    </template>
                </el-table-column>
                <el-table-column v-if="!is_show_bsi && !is_show_cfm_f1_1" label='End Project' >
                    <template slot-scope="scope">
                        <span class='newheadtitle'>{{ scope.row.dt_end_project }}</span>
                    </template>
                </el-table-column>

                <el-table-column v-if="is_show_cfm_f1_1" label='Start Engine Plan' >
                    <template slot-scope="scope">
                        <span class='newheadtitle'>{{ scope.row.dt_start_project }}</span>
                    </template>
                </el-table-column>
                <el-table-column v-if="is_show_cfm_f1_1" label='End Engine Plan' >
                    <template slot-scope="scope">
                        <span class='newheadtitle'>{{ scope.row.dt_end_project }}</span>
                    </template>
                </el-table-column>

                <el-table-column label='Operate' fixed='right'>
                    <template slot-scope='scope'>
                        <el-button 
                            @click.native.stop='allot_reset(scope.row)' 
                            type="primary"
                            size='mini'>Reset
                        </el-button>
                        <el-button 
                            v-if="!is_show_bsi " 
                            @click.native.stop='use_project(scope.row)' 
                            type='info' 
                            size='mini'>Use Project
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div slot="footer">
                <el-button 
                    class="topButton_right" 
                    style="margin-left:20px;" 
                    size="small" 
                    type='danger'
                    @click="closeDialog()">Cancle
                </el-button>
                <el-button 
                    class="topButton_right" 
                    size="small" 
                    type='primary'
                    @click="allot_save()">Save
                </el-button>
            </div>
        </el-dialog>
    `,
  data() {
    return {
      id_wo_select: '',
      save_flag: false,
      date_max: '',
      date_b1_max: '',
      dialog_data: this.dialogData,
      dialogWidth: '90%',
      staffs: [],// 人员
      sites:[],//站点
      teams: [],// team
      allot_flow_key: 0
    }
  },
  filters: {
  },
  created() {
    if(this.dialog_data.main.str_flow =='F1-1'){
      this.date_max = this.dialog_data.main.dt_f11_update
      this.date_b1_max = this.dialog_data.main.dt_f11_close
    }else{
      this.date_max = this.dialog_data.main.dt_f41_update
      this.date_b1_max = this.dialog_data.main.dt_release_update
    }
    
    if (Number(window.screen.height) >= 1080) {
      this.dialogWidth = '70%'
    }
  },
  computed: {
    isAllotShow() {
      return this.is_allot_show
    },
    is_show_f4_1() {
      let _this = this
      let is_show = false
      if (_this.input_page_type === 'F4-1') {
        is_show = true
      }
      return is_show
    },
    is_show_f42_test() {
      let _this = this
      let is_show = false
      if (_this.input_page_type === 'F4-1' && _this.input_group_type?.toLowerCase() === "b1") {
        is_show = true
      }
      return is_show
    },
    is_show_f4_2() {
      let _this = this
      let is_show = false
      if (_this.input_page_type === 'F4-2') {
        is_show = true
      }
      return is_show
    },
    is_show_f1_1() {
      let _this = this
      let is_show = false
      if (_this.input_page_type === 'F1-1') {
        is_show = true
      }
      return is_show
    },
    is_show_bsi() {
      let _this = this
      let is_show = false
      if (_this.input_page_type === 'INSPECT') {
        is_show = true
      }
      return is_show
    },
    is_show_cfm_f1_1 () {
      let _this = this
      let is_show = false
      if(_this.input_page_type === 'F1-1' && _this.input_str_esn_type?.toLowerCase() == "cfm") {
        is_show = true
      }
      return is_show
    }
  },
  mounted() {
    this.get_team()
    // this.get_staff()
    this.get_site()
  },
  methods: {
    allot_reset(row) {
      row.id_teams = [];
      row.dt_start = "";
      row.dt_end = "";
      row.str_id_surveys = null;
    },
    async teamselect(value,row){
      if(value.length==0){
        row.staffs=[]
      }else{
        row.staffs=[]
        const params = {
          au: "ssamc",
          ap: "api2018",
          ak: "",
          ac: "pt_get_inspector_list",
          id_team:value
        }
        const { data } = await axios.post(globalApiUrl, params)
        
        row.staffs.push(...data.data)
        this.allot_flow_key += 1
      }
    },
    use_project(row) {
      if (row.dt_end_project && row.dt_start_project) {
        row.dt_end = row.dt_end_project
        row.dt_start = row.dt_start_project
      }
    },
    changeStatus({ row, column, rowIndex, columnIndex }) {
      let _this = this
      if (column.label === 'Start Project' || column.label === 'End Project') {
        if (row.is_change_date) {
          _this.save_flag = true
          return 'bgPurple'
        }
      }
      if (column.label === 'Type') {
        if (row.is_change_type) {
          _this.save_flag = true
          return 'bgPurple'
        }
      }
    },
    // 关闭弹框
    closeDialog() {
      this.$emit('close-dialog')
    },
    /**保存 分配 */
    allot_save() {
      let _this = this;
      if (_this.save_flag) {
        _this.$confirm('Project的类型或时间发生变化,是否调整完毕?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          _this.save()
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消保存'
          });
        })
      } else {
        _this.save()
      }



    },
    save() {
      let _this = this;
      let _obj = JSON.stringify(_this.allot_table)
      let save_data_t = JSON.parse(_obj);
      save_data_t.forEach((x) => {

        let id_team_t = [];
        let name_team_t = [];
        x.id_teams && x.id_teams.length > 0 && x.id_teams.forEach(t => {
          id_team_t.push(t);
          name_team_t.push(_this.teams.find(x => x.id == t)?.str_name || "")
        });
        x.id_teams = id_team_t.join(','); // 检验人员ID
        x.str_teams = name_team_t.join(',');// 检验人员

        let id_survey_t = [];
        let name_survey_t = [];
        x.str_id_surveys && x.str_id_surveys.length > 0 && x.str_id_surveys.forEach(t => {
          id_survey_t.push(t);
          name_survey_t.push(x.staffs.find(x => x.id == t)?.str_name || "")
        });

        x.str_id_surveys = id_survey_t.join(','); // 检验人员ID
        x.str_name_surveyors = name_survey_t.join(',');// 检验人员


      })
      let error_msg = [];
      // 组织判断 action
      save_data_t && save_data_t.length > 0 &&
        save_data_t.forEach((task, index) => {
          if (task.id_teams && task.id_teams.length > 0) {           
            if (!task.dt_start || !task.dt_end)
              error_msg.push(`No.${index + 1} please select start-end date`)
            if (task.dt_end && _this.input_page_type && _this.input_page_type.indexOf("F4") > -1) {
              if (task.str_type != 'B1') {
                if (_this.date_max && _this.date_max && task.dt_end > _this.date_max)
                  error_msg.push(`No.${index + 1}  (Core,LPT,FAN) please select end date < ${_this.date_max}`)
              } else {
                if (_this.date_b1_max && _this.date_b1_max && task.dt_end > _this.date_b1_max)
                  error_msg.push(`No.${index + 1}  (B1) please select end date < ${_this.date_b1_max}`)
              }
            }else if(task.dt_end && _this.input_page_type && _this.input_page_type.indexOf("F1") > -1){
              // if (_this.date_max && _this.date_max && task.dt_end > _this.date_max)
              // error_msg.push(`No.${index + 1}  please select end date < ${_this.date_max}`)
            }
            if(task.dt_test_start && task.dt_test_end){
              if(task.dt_test_end < task.dt_test_start){
                error_msg.push(`No.${index + 1}  please select F4-2 end date > F4-2 start date`)
              }
              if(task.dt_end < task.dt_test_end){
                error_msg.push(`No.${index + 1}  please select end date > F4-2 end date`)
              }
            }
          } else {
            task.dt_start = ''
            task.dt_end = ''
            task.str_id_surveys = ''
          }

        });
      if (error_msg.length > 0) {
        _this.$message({
          dangerouslyUseHTMLString: true,
          message: error_msg.join("<br/>"),
          type: "warning",
        });
      } else {
        axios.post(globalApiUrl, {
          au: "ssamc",
          ap: "api2018",
          ak: "",
          ac: "pt_save_allot",
          id_wo: _this.dialog_data.main.id_wo,
          save_data: save_data_t,
          str_flow: _this.input_page_type,
          id: _this.dialog_data.main.id
        })
          .then(function (response) {
            if (response.data.code == "success") {
              // _this.saveinspectPlan(response.data.data);
              _this.$emit('save')
            }

          })
          .catch(function (error) {
            console.log(error);
          });
      }
    },
    // async get_staff() {     
    //   const params = {
    //     au: "ssamc",
    //     ap: "api2018",
    //     ak: "",
    //     ac: "pt_get_inspector_list"
    //   }
    //   const { data } = await axios.post(globalApiUrl, params)
     

    //   this.staffs = data.data
    // },
    async get_site() {
      let _this = this;
      const params = {
        au: "ssamc",
        ap: "api2018",
        ak: "",
        ac: "pt_get_site_list"
      }
      const { data } = await axios.post(globalApiUrl, params)
      _this.sites = data.data
    },
    async get_team() {
      let _this = this;
      const params = {
        au: "ssamc",
        ap: "api2018",
        ak: "",
        ac: "pt_get_teams"
      }
      const { data } = await axios.post(globalApiUrl, params)
      _this.teams = data.data.data || [];
    },

    /** 获取对应类型 team */
    get_team_by_type(type, teams) {
      let _this = this;     
      if (teams && teams.length > 0 && _this.input_str_esn_type && _this.input_str_esn_type?.toLowerCase() != "leap") {
        if (type.toLowerCase() == "f1-inspect" || type.toLowerCase() == "f4-inspect") {
          return teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "inspect" ))
            && teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "inspect" )) || teams;
        } else if (type.toLowerCase() == "bsi-in" || type.toLowerCase() == "bsi-out") {
          return teams.filter(x => x.str_type && x.str_type.toLowerCase() == "inspect")
            && teams.filter(x => x.str_type && x.str_type.toLowerCase() == "inspect") || teams;
        } else if(type.toLowerCase() == "B1"){
            return teams.filter(x => x.str_type && x.str_type.toLowerCase() == "b1")
            && teams.filter(x => x.str_type && x.str_type.toLowerCase() == "b1") || teams;        
        }
        else if(type.toLowerCase() == "B2/3"){
          return teams.filter(x => x.str_type && x.str_type.toLowerCase() == "b2/3")
          && teams.filter(x => x.str_type && x.str_type.toLowerCase() == "b2/3") || teams;      
        }
        else{
          return teams.filter(x => x.str_type && x.str_type.toLowerCase() == type.toLowerCase())
          && teams.filter(x => x.str_type && x.str_type.toLowerCase() == type.toLowerCase()) || teams;   
        }

      }else if(_this.input_str_esn_type?.toLowerCase() == "leap"){
        if(type.toLowerCase() == "b1" ||type.toLowerCase() == "fan"){
          return teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "b1" ||x.str_type.toLowerCase() == "fan"))
          && teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "b1" ||x.str_type.toLowerCase() == "fan")) || teams;
        }       
        if(type.toLowerCase() == "core" ){
          return teams.filter(x => x.str_type && x.str_type.toLowerCase() == "core")
          && teams.filter(x => x.str_type && x.str_type.toLowerCase() == "core") || teams;
        } 
        if(type.toLowerCase() == "lpt" ){
          return teams.filter(x => x.str_type && x.str_type.toLowerCase() == "lpt")
          && teams.filter(x => x.str_type && x.str_type.toLowerCase() == "lpt") || teams;
        } 
      }
      else {
        if(type.toLowerCase() == "b1" ){
          return teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "b1"))
          && teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "b1")) || teams;
        }
        if(type.toLowerCase() == "core" ){
          return teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "core"))
          && teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "core")) || teams;
        }
        if(type.toLowerCase() == "fan" ){
          return teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "fan"))
          && teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "fan")) || teams;
        }
        if(type.toLowerCase() == "lpt" ){
          return teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "lpt"))
          && teams.filter(x => x.str_type && (x.str_type.toLowerCase() == "lpt")) || teams;
        }
        if(this.input_page_type.toLowerCase()=="inspect"){         
          return teams.filter(x => x.str_type && (x.str_type.toLowerCase() != ""))
          && teams.filter(x => x.str_type && (x.str_type.toLowerCase() != "")) || teams;
        } 
      }
     
    },

    /**保存发动机分配数据后添加检验排班任务 */
    async saveinspectPlan(successdata) {
      let _this = this;
      await axios
        .post(globalApiUrl, {
          au: "ssamc",
          ap: "api2018",
          ak: "",
          ac: "pt_save_inspect_plan",
          allot_main: successdata.id,
          allot_flow: successdata.str_flow,
          id_wo: successdata.id_wo,
        })
        .then(function (response) {
          _this.status_data = response.data?.data || [];
        })
        .catch(function (error) {

          console.log(error);
        });
    }
  }
})
