// var { ref, onMounted, inject, watch, onBeforeUnmount } = Vue

// import { useApi } from '../../../comm/hooks/useApi.js'

const BottomRightComponent = {
  setup(props, ctx) {
    const woId = inject('wo-id')
    const engine_type = inject('str_engine_type')
    const tableData = ref([])
    // const { requestDataApi } = useApi()
    const getTableData = async (idTeam) => {
      tableData.value = await requestDataApi({
        ac: 'pt_team_task',
        id_wo: woId.value,
      })
    }

    watch(
      () => woId.value,
      (value) => {
        getTableData(value)
      },
    )
    const rowClassName = ({ row, rowIndex }) => {
      // 奇数行
      if (rowIndex % 2 === 0) {
        return 'rb-table-one'
      } else {
        return 'rb-table-two'
      }
    }
    const scrollbarRef = ref(null)
    let intervalId = null
    const startTableCarousel = () => {
      intervalId = setInterval(() => {
        const scrollbar = scrollbarRef.value
        if (scrollbar) {
          const scroll = scrollbar.$el.querySelector('.el-scrollbar__wrap')
          // 滚动
          scroll.scrollTop += 1
          if (scroll.scrollTop >= scroll.scrollHeight - scroll.clientHeight) {
            setTimeout(() => {
              scroll.scrollTop = 0
            }, 100)
          }
        }
      }, 50) // 每50毫秒滚动一次
    }
    const stopTableCarousel = () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
    // 表格滚动
    onMounted(() => {
      startTableCarousel()
    })
    onBeforeUnmount(() => {
      stopTableCarousel()
    })
    return {
      tableData,
      rowClassName,
      scrollbarRef,
    }
  },
  template: /*html*/ `
    <div class="absolute h-full w-full">
      <el-table ref="scrollbarRef" :data="tableData" border stripe height="100%" :row-class-name="rowClassName">
        <el-table-column type="index" label="序号" width="70"></el-table-column>
        <el-table-column prop="esn" label="WorkOrder/Esn"></el-table-column>
        <el-table-column prop="task" label="TASK"></el-table-column>
        <el-table-column prop="date" label="DATE"></el-table-column>
        <el-table-column prop="people" label="People"></el-table-column>
      </el-table>
    </div>
  `,
}

// export { BottomRightComponent }
