const { defineAsyncComponent, ref, computed } = Vue

export default {
  name: 'ExceptionConversionMonitor',
  components: {
    TableCarousel: defineAsyncComponent(() => import('./components/TableCarousel.js'))
  },
  setup() {
    // 控制自动播放
    const autoPlayEnabled = ref(true)
    
    // 异常集件缺件零件数据
    const exceptionPartsData = ref([
      {
        id: 1,
        sequence: '否',
        partNumber: '消件件',
        workOrder: 'E20241121',
        engineNumber: '569919',
        applicationNumber: '649-784-529-0',
        partName: 'NUT CAPTIVE WASHER',
        unitCode: 'QEC',
        exceptionCode: 'SM30'
      },
      {
        id: 2,
        sequence: '否',
        partNumber: 'Hardware',
        workOrder: 'E20241018',
        engineNumber: '598327',
        applicationNumber: '2463M30P01',
        partName: 'RING EXTERNAL RETAINING',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 3,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20241018',
        engineNumber: '598327',
        applicationNumber: 'J1493P05A',
        partName: 'BOLT',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 4,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20241018',
        engineNumber: '598327',
        applicationNumber: 'J1494P05A',
        partName: 'BOLT, MACHINE',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 5,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20250308',
        engineNumber: '802636',
        applicationNumber: '1523M73P01',
        partName: 'Forward heat shield pins and nuts',
        unitCode: 'SM53',
        exceptionCode: 'SM53'
      },
      // 添加更多数据以测试分页
      {
        id: 6,
        sequence: '否',
        partNumber: '消件件',
        workOrder: 'E20250401',
        engineNumber: '802637',
        applicationNumber: '1523M74P01',
        partName: 'Engine Mount Bracket',
        unitCode: 'SM53',
        exceptionCode: 'SM53'
      },
      {
        id: 7,
        sequence: '是',
        partNumber: 'Hardware',
        workOrder: 'E20250402',
        engineNumber: '802638',
        applicationNumber: '2463M31P01',
        partName: 'Fuel Line Connector',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 8,
        sequence: '否',
        partNumber: '消件件',
        workOrder: 'E20250403',
        engineNumber: '802639',
        applicationNumber: 'J1495P05A',
        partName: 'Turbine Blade',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 9,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20250404',
        engineNumber: '802640',
        applicationNumber: 'J1496P05A',
        partName: 'Compressor Disc',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 10,
        sequence: '否',
        partNumber: 'Hardware',
        workOrder: 'E20250405',
        engineNumber: '802641',
        applicationNumber: '1523M75P01',
        partName: 'Exhaust Nozzle',
        unitCode: 'SM53',
        exceptionCode: 'SM53'
      },
      {
        id: 11,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20250406',
        engineNumber: '802642',
        applicationNumber: 'J1497P05A',
        partName: 'Oil Filter',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 12,
        sequence: '否',
        partNumber: 'Hardware',
        workOrder: 'E20250407',
        engineNumber: '802643',
        applicationNumber: '2463M32P01',
        partName: 'Ignition System',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      // 添加更多数据以展示滚动效果
      {
        id: 13,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20250408',
        engineNumber: '802644',
        applicationNumber: 'J1498P05A',
        partName: 'Hydraulic Pump',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 14,
        sequence: '否',
        partNumber: 'Hardware',
        workOrder: 'E20250409',
        engineNumber: '802645',
        applicationNumber: '2463M33P01',
        partName: 'Control Valve',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 15,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20250410',
        engineNumber: '802646',
        applicationNumber: 'J1499P05A',
        partName: 'Fuel Pump Assembly',
        unitCode: 'SM53',
        exceptionCode: 'SM53'
      },
      {
        id: 16,
        sequence: '否',
        partNumber: 'Hardware',
        workOrder: 'E20250411',
        engineNumber: '802647',
        applicationNumber: '1523M76P01',
        partName: 'Engine Control Unit',
        unitCode: 'SM53',
        exceptionCode: 'SM53'
      },
      {
        id: 17,
        sequence: '是',
        partNumber: '消件件',
        workOrder: 'E20250412',
        engineNumber: '802648',
        applicationNumber: 'J1500P05A',
        partName: 'Throttle Body',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      },
      {
        id: 18,
        sequence: '否',
        partNumber: 'Hardware',
        workOrder: 'E20250413',
        engineNumber: '802649',
        applicationNumber: '2463M34P01',
        partName: 'Sensor Assembly',
        unitCode: 'SM30',
        exceptionCode: 'SM30'
      }
    ])

    // 白转黄数据
    const generateWhiteToYellowData = (type) => {
      const baseData = [
        {
          id: 1,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250409',
          engineNumber: '598006',
          flow: 'F3',
          partNumber: 'HARDWAREX01',
          partName: 'BOLTS & NUTS (MM01)'
        },
        {
          id: 2,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250508',
          engineNumber: '599393',
          flow: 'F4-1,B1',
          partNumber: 'HARDWAREX02',
          partName: 'BOLTS & NUTS (MM02)'
        },
        {
          id: 3,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250204',
          engineNumber: '599522',
          flow: 'F4-1,B2B3',
          partNumber: 'MM03',
          partName: 'LPT ASSY'
        },
        {
          id: 4,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250310',
          engineNumber: '599849',
          flow: 'F3',
          partNumber: 'HARDWAREX00',
          partName: 'BOLTS & NUTS (MM00)'
        },
        {
          id: 5,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250310',
          engineNumber: '599849',
          flow: 'F3',
          partNumber: '362-090-002-0',
          partName: 'BRACKET'
        },
        // 添加更多数据
        {
          id: 6,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250511',
          engineNumber: '599850',
          flow: 'F4-2',
          partNumber: 'HARDWAREX03',
          partName: 'GEAR BOX ASSEMBLY'
        },
        {
          id: 7,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250512',
          engineNumber: '599851',
          flow: 'F3',
          partNumber: 'MM04',
          partName: 'HPT ROTOR'
        },
        {
          id: 8,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250513',
          engineNumber: '599852',
          flow: 'F4-1,B1',
          partNumber: 'HARDWAREX04',
          partName: 'COMBUSTOR LINER'
        },
        {
          id: 9,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250514',
          engineNumber: '599853',
          flow: 'F3',
          partNumber: 'HARDWAREX05',
          partName: 'FUEL INJECTOR'
        },
        {
          id: 10,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250515',
          engineNumber: '599854',
          flow: 'F4-2',
          partNumber: 'MM05',
          partName: 'STARTER MOTOR'
        },
        {
          id: 11,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250516',
          engineNumber: '599855',
          flow: 'F4-1,B1',
          partNumber: 'HARDWAREX06',
          partName: 'ALTERNATOR'
        },
        {
          id: 12,
          sequence: type === 'pending' ? '已验证' : '已验证错误',
          workOrder: 'E20250517',
          engineNumber: '599856',
          flow: 'F3',
          partNumber: '362-090-003-0',
          partName: 'MOUNTING BRACKET'
        }
      ]
      return baseData
    }

    // 表格列定义
    const exceptionPartsColumns = [
      { field: 'sequence', title: '序号', width: 80 },
      { field: 'partNumber', title: '集件已发', width: 120 },
      { field: 'workOrder', title: '工作指令', width: 120 },
      { field: 'engineNumber', title: '发动机号', width: 120 },
      { field: 'applicationNumber', title: '申请件号', width: 150 },
      { field: 'partName', title: '零件名称', width: 200 },
      { field: 'unitCode', title: '单元体', width: 100 },
      { field: 'exceptionCode', title: '异常编码', width: 120 }
    ]

    const whiteToYellowColumns = [
      { field: 'sequence', title: '已验证', width: 100 },
      { field: 'workOrder', title: '工作指令', width: 120 },
      { field: 'engineNumber', title: '发动机号', width: 120 },
      { field: 'flow', title: 'FLOW', width: 100 },
      { field: 'partNumber', title: '件号', width: 150 },
      { field: 'partName', title: '名称', width: 200 }
    ]

    // 轮播表格配置
    const carouselTables = computed(() => [
      {
        title: '异常集件缺件零件',
        data: exceptionPartsData.value,
        columns: exceptionPartsColumns,
        bgColor: 'from-blue-500 to-blue-600',
        autoScroll: autoPlayEnabled.value,
        scrollInterval: 2000
      },
      {
        title: '白转黄 待转PNR (RUPK)',
        data: generateWhiteToYellowData('pending'),
        columns: whiteToYellowColumns,
        bgColor: 'from-purple-500 to-purple-600',
        autoScroll: autoPlayEnabled.value,
        scrollInterval: 2200
      },
      {
        title: '白转黄 已转PNR (F1-2/RUPK)',
        data: generateWhiteToYellowData('converted'),
        columns: whiteToYellowColumns,
        bgColor: 'from-purple-500 to-purple-600',
        autoScroll: autoPlayEnabled.value,
        scrollInterval: 2400
      },
      {
        title: '白转黄 待点灯扫描 (RUPK)',
        data: generateWhiteToYellowData('scanning'),
        columns: whiteToYellowColumns,
        bgColor: 'from-purple-500 to-purple-600',
        autoScroll: autoPlayEnabled.value,
        scrollInterval: 2600
      }
    ])

    // 切换自动播放
    const toggleAutoPlay = () => {
      autoPlayEnabled.value = !autoPlayEnabled.value
      ElementPlus.ElMessage.success(
        autoPlayEnabled.value ? '已开启自动播放' : '已关闭自动播放'
      )
    }

    // 当前时间
    const currentTime = ref(moment().format('YYYY-MM-DD HH:mm:ss'))
    
    // 更新时间
    setInterval(() => {
      currentTime.value = moment().format('YYYY-MM-DD HH:mm:ss')
    }, 1000)

    return {
      carouselTables,
      autoPlayEnabled,
      toggleAutoPlay,
      currentTime
    }
  },
  template: /*html*/ `
    <div class="exception-monitor-container h-screen w-screen bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
      <!-- 页面标题栏 -->
      <div class="header-section bg-white shadow-sm border-b w-full">
        <div class="px-6 py-4 w-full">
          <div class="flex items-center justify-between w-full">
            <div class="flex items-center space-x-4">
              <div class="w-1 h-8 bg-blue-500 rounded"></div>
              <h1 class="text-2xl font-bold text-gray-800">RUPK异常件与白转黄监控表</h1>
            </div>
            
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2 text-sm text-gray-600">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>系统运行正常</span>
              </div>
              
              <div class="text-sm text-gray-500">
                {{ currentTime }}
              </div>
              
              <el-button 
                type="primary" 
                size="small"
                :icon="autoPlayEnabled ? 'VideoPause' : 'VideoPlay'"
                @click="toggleAutoPlay"
              >
                {{ autoPlayEnabled ? '暂停轮播' : '开启轮播' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 轮播内容区域 -->
      <div class="carousel-section flex-1 h-full w-full overflow-hidden">
        <TableCarousel
          :tables="carouselTables"
          :auto-play="autoPlayEnabled"
          :play-interval="6000"
        />
      </div>
    </div>
  `,
}
