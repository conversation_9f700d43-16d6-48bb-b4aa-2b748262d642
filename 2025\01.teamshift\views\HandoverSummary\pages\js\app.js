/**
 * 交接汇总主应用 - 按业务类型分块展示统计卡片
 */
document.addEventListener('DOMContentLoaded', async () => {
  // 获取并注册Element Plus图标
  const icons = window['@element-plus/icons-vue'] || {}

  // 动态导入钻取模块
  const { DrillDownComponent } = await import('./modules/drillDown.js')

  /**
   * 统计卡片组件
   */
  const StatCardComponent = {
    name: 'StatCardComponent',
    props: {
      title: {
        type: String,
        required: true,
      },
      value: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
      type: {
        type: String,
        default: 'pending',
        validator: (value) => ['pending', 'unsubmitted', 'unreceived'].includes(value),
      },
      description: {
        type: String,
        default: '',
      },
      businessType: {
        type: String,
        required: true,
      },
    },
    setup(props, { emit }) {
      const handleCardClick = () => {
        // 待提交类型的卡片不可点击
        if (props.type === 'unsubmitted') {
          return
        }
        emit('card-click', {
          type: props.type,
          businessType: props.businessType,
          value: props.value,
          title: props.title,
        })
      }

      return {
        handleCardClick,
      }
    },
    template: `
      <div class="bg-gradient-to-br border rounded-lg p-4 relative overflow-hidden transition-all duration-300 card-top-border"
           :class="{
             'from-yellow-50 to-yellow-100 border-yellow-200 hover:from-yellow-100 hover:to-yellow-200 hover:-translate-y-1 hover:shadow-lg cursor-pointer': type === 'pending',
             'from-red-50 to-red-100 border-red-200 opacity-60 cursor-not-allowed': type === 'unsubmitted', 
             'from-blue-50 to-blue-100 border-blue-200 hover:from-blue-100 hover:to-blue-200 hover:-translate-y-1 hover:shadow-lg cursor-pointer': type === 'unreceived'
           }"
           @click="handleCardClick">
        <div class="text-sm font-medium uppercase tracking-wide mb-3"
             :class="{
               'text-yellow-600': type === 'pending',
               'text-red-600': type === 'unsubmitted',
               'text-blue-600': type === 'unreceived'
             }">
          {{ title }}
        </div>
        <div class="text-3xl font-bold mb-2 leading-none"
             :class="{
               'text-yellow-800': type === 'pending',
               'text-red-800': type === 'unsubmitted',
               'text-blue-800': type === 'unreceived'
             }">
          {{ value }}
        </div>
        <div class="text-xs mb-3" v-if="description"
             :class="{
               'text-yellow-600': type === 'pending',
               'text-red-600': type === 'unsubmitted',
               'text-blue-600': type === 'unreceived'
             }">
          {{ description }}
        </div>
        <div class="mt-3 flex items-center justify-end" v-if="type !== 'unsubmitted'">
          <svg class="w-4 h-4 opacity-60" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>
    `,
  }

  /**
   * 业务类型分块组件
   */
  const BusinessTypeSectionComponent = {
    name: 'BusinessTypeSectionComponent',
    components: {
      'stat-card': StatCardComponent,
    },
    props: {
      businessType: {
        type: Object,
        required: true,
      },
      businessTypeCode: {
        type: String,
        required: true,
      },
      data: {
        type: Object,
        default: () => ({}),
      },
    },
    setup(props, { emit }) {
      const formatBusinessTypeName = (code, name) => {
        return `${name}`
      }

      const handleCardClick = (cardData) => {
        if (cardData.type === 'unsubmitted') {
          return
        }
        emit('drill-down', {
          ...cardData,
          businessTypeCode: props.businessType.code,
          businessTypeName: props.businessType.name,
        })
      }

      return {
        formatBusinessTypeName,
        handleCardClick,
      }
    },
    template: `
      <div class="bg-white rounded-xl p-5 mb-6 shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-1 fade-in-up">
        <h2 class="title-decorator text-xl font-semibold text-gray-700 mb-4 pb-2 border-b-2 border-gray-200 flex items-center">
          {{ formatBusinessTypeName(businessType.code, businessType.name) }}
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
          <stat-card
            title="Pending"
            :value="data.pending || 0"
            :total="data.total || 0"
            type="pending"
            description="等待处理的交接事项"
            :business-type="businessType.code"
            @card-click="handleCardClick"
          />
          <stat-card
            title="待提交"
            :value="data.unsubmitted || 0"
            :total="data.total || 0"
            type="unsubmitted"
            description="尚未提交的交接事项"
            :business-type="businessType.code"
            @card-click="handleCardClick"
          />
          <stat-card
            title="待接收"
            :value="data.unreceived || 0"
            :total="data.total || 0"
            type="unreceived"
            description="等待接收的交接事项"
            :business-type="businessType.code"
            @card-click="handleCardClick"
          />
        </div>
      </div>
    `,
  }

  /**
   * 主应用组件
   */
  const App = {
    name: 'HandoverSummaryApp',
    components: {
      'business-type-section': BusinessTypeSectionComponent,
      'drill-down': DrillDownComponent,
    },
    setup() {
      // 响应式数据
      const loading = Vue.ref(true)
      const businessTypes = Vue.ref([])
      const summaryData = Vue.ref({})

      // 钻取相关状态
      const drillDownVisible = Vue.ref(false)
      const drillDownData = Vue.ref({})

      // 获取业务类型数据
      const fetchBusinessTypes = async () => {
        try {
          // 检查API函数是否可用
          const { getBussinessTypes } = await import('../../api/index.js')
          const response = await getBussinessTypes()

          businessTypes.value = response.map((item) => ({
            code: item.str_value,
            name: item.str_key,
          }))
          return
        } catch (error) {
          console.error('获取业务类型失败:', error)
        }
      }

      // 获取单个业务类型的汇总数据
      const fetchBusinessTypeSummary = async (businessTypeCode) => {
        try {
          const { getHandoverSummary } = await import('../../api/index.js')
          const response = await getHandoverSummary(businessTypeCode)

          return {
            pending: response.pendingNum || 0,
            unsubmitted: response.unCommitNum || 0,
            unreceived: response.unReceiveNum || 0,
            total: (response.pendingNum || 0) + (response.unCommitNum || 0) + (response.unReceiveNum || 0),
          }
        } catch (error) {
          console.error(`获取业务类型 ${businessTypeCode} 汇总数据失败:`, error)
        }
      }

      // 获取所有业务类型的汇总数据
      const fetchSummaryData = async () => {
        try {
          loading.value = true

          // 确保业务类型数据已加载
          if (businessTypes.value.length === 0) {
            await fetchBusinessTypes()
          }

          // 并行获取所有业务类型的汇总数据
          const summaryPromises = businessTypes.value.map(async (businessType) => {
            const summary = await fetchBusinessTypeSummary(businessType.code)
            return {
              code: businessType.code,
              data: summary,
            }
          })

          const summaryResults = await Promise.all(summaryPromises)

          // 转换为对象格式
          const newSummaryData = {}
          summaryResults.forEach((result) => {
            newSummaryData[result.code] = result.data
          })

          summaryData.value = newSummaryData
        } catch (error) {
          console.error('获取汇总数据失败:', error)
          if (window.ElMessage) {
            ElMessage.error('获取数据失败，请稍后重试')
          }
        } finally {
          loading.value = false
        }
      }

      // 刷新数据
      const refreshData = async () => {
        await fetchSummaryData()
        if (window.ElMessage) {
          ElMessage.success('数据已刷新')
        }
      }

      // 处理钻取事件
      const handleDrillDown = (data) => {
        drillDownData.value = data
        drillDownVisible.value = true
      }

      // 关闭钻取弹窗
      const closeDrillDown = () => {
        drillDownVisible.value = false
        drillDownData.value = {}
      }

      // 计算总体统计
      const totalStats = Vue.computed(() => {
        const total = {
          pending: 0,
          unsubmitted: 0,
          unreceived: 0,
          total: 0,
        }

        if (summaryData.value && typeof summaryData.value === 'object') {
          Object.values(summaryData.value).forEach((data) => {
            if (data && typeof data === 'object') {
              total.pending += data.pending || 0
              total.unsubmitted += data.unsubmitted || 0
              total.unreceived += data.unreceived || 0
              total.total += data.total || 0
            }
          })
        }

        return total
      })

      // 生命周期
      Vue.onMounted(async () => {
        await fetchBusinessTypes()
        await fetchSummaryData()
      })

      return {
        loading,
        businessTypes,
        summaryData,
        totalStats,
        refreshData,
        drillDownVisible,
        drillDownData,
        handleDrillDown,
        closeDrillDown,
      }
    },
    template: /*html*/ `
      <div class="w-full mx-auto p-6 min-h-screen">
        <!-- 页面头部 -->
        <div class="gradient-header shadow-header rounded-xl p-2 mb-2">
          <div class="flex justify-between items-center">
            <h1 class="text-xl font-semibold text-white">交接汇总</h1>
            <button 
              @click="refreshData"
              :disabled="loading"
              class="px-4 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all duration-200 backdrop-blur-sm font-medium disabled:opacity-50">
              {{ loading ? '加载中...' : '刷新数据' }}
            </button>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="flex justify-center items-center h-48 text-slate-600">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mr-3"></div>
          <span>正在加载数据...</span>
        </div>

        <!-- 业务类型分块展示 -->
        <template v-else>
          <business-type-section
            v-for="businessType in businessTypes"
            :key="businessType.code"
            :business-type="businessType"
            :business-type-code="businessType.code"
            :data="summaryData[businessType.code] || {}"
            @drill-down="handleDrillDown"
          />
        </template>

        <!-- 空状态 -->
        <div v-if="!loading && businessTypes.length === 0" class="text-center py-12 text-slate-500">
          <div class="text-5xl mb-4 opacity-50">📊</div>
          <p>暂无数据</p>
        </div>

        <!-- 钻取详情弹窗 -->
        <drill-down
          :visible="drillDownVisible"
          :drill-data="drillDownData"
          @close="closeDrillDown"
        />
      </div>
    `,
  }

  // 创建Vue应用实例
  const { createApp } = Vue
  const app = createApp(App)

  // 注册Element Plus
  app.use(ElementPlus)

  // 注册Element Plus图标
  if (icons && typeof icons === 'object') {
    for (const [key, component] of Object.entries(icons)) {
      app.component(key, component)
    }
  } else {
    console.warn('Element Plus icons not loaded properly')
  }

  // 注册VXE Table
  app.use(VxeUI)
  app.use(VXETable)

  // 挂载应用
  app.mount('#app')
})
