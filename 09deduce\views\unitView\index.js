import SearchForm from './components/searchForm.js'
import { useFilter } from '../../hooks/useFilter.js'
import { post } from '../../../config/axios/httpReuest.js'
import ScatterChart from './components/scatterChart.js'
import PointColotDrawer from './components/pointColorDrawer.js'
const { ref, onMounted, reactive } = Vue
/**
 * @description 单元体视角
 * <AUTHOR>
 * @date 2024-07-20
 */
const UnitPerspective = {
  components: {
    SearchForm,
    ScatterChart,
    PointColotDrawer,
  },
  setup(props, { emit }) {
    const isFirstEnter = ref(true)
    const { recombineFilter } = useFilter()
    const parentFilterFields = ref([])
    const handleSearch = async (formData) => {
      isFirstEnter.value = true
      parentFilterFields.value = recombineFilter(formData)
      await getEngineList()
    }

    // * 使用 Intersection Observer API 监听元素是否进入视口
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          scatterChartRef.value[entry.target.id].getChartList(parentFilterFields.value)
          // 停止观察当前元素，即只触发一次
          observer.unobserve(entry.target)
        }
      })
    })

    // 升序通过GP
    const ascByGP = ref('0')
    // 隐藏GP红框
    const hideProject = ref('0')
    // 隐藏班组计划
    const hideTeam = ref('0')

    const engineList = ref([])
    // 获取发动机列表
    const getEngineList = async () => {
      const loading = ElementPlus.ElLoading.service({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      engineList.value = []
      const params = {
        ac: 'de_sm_group_wo_list',
        is_asc_project: ascByGP.value,
        is_hide_project: hideProject.value,
        is_hide_team: hideTeam.value,
        filter_fields: parentFilterFields.value,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        engineList.value = data.data.map((item) => {
          return {
            ...item,
            id: item.id_wo + '' + item.str_group,
          }
        })
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
      loading.close()
    }

    // 设置散点图ref
    const scatterChartRef = ref({})
    const setScatterChartRef = (el, id) => {
      if (el) {
        scatterChartRef.value[id] = el
        if (isFirstEnter.value) {
          // 监听元素
          observer.observe(el.$el)
        }
      }
    }
    // * 排序
    const handleChangeSort = async (val) => {}
    // * 隐藏GP红框
    const handleHideProject = (val) => {
      hideProject.value = val
      getEngineList()
    }
    // * 隐藏班组计划
    const hanldelHideTeam = (val) => {
      hideTeam.value = val
      getEngineList()
    }

    const colorPointDrawer = reactive({
      visible: false,
      type: '1',
      idWo: '',
      filterFields: [],
    })
    const openPointColorDrawer = (type, simulationType, idWo, group) => {
      isFirstEnter.value = false
      colorPointDrawer.visible = true
      colorPointDrawer.type = type
      colorPointDrawer.simulationType = simulationType
      colorPointDrawer.idWo = idWo
      colorPointDrawer.filterFields = [
        {
          str_key: 'str_group',
          str_value: group,
        },
      ]
    }
    const clickRedPoint = (item) => {
      openPointColorDrawer('1', item.int_simulation_type, item.id_wo, item.str_group)
    }
    const clickGreenPoint = (item) => {
      openPointColorDrawer('3', item.int_simulation_type, item.id_wo, item.str_group)
    }
    const clickBluePoint = (item) => {
      openPointColorDrawer('2', item.int_simulation_type, item.id_wo, item.str_group)
    }
    const clickPurplePoint = (item) => {
      openPointColorDrawer('4', item.int_simulation_type, item.id_wo, item.str_group)
    }

    // * 点击颜色按钮
    const clickPoint = (item, type) => {
      const map = {
        red: clickRedPoint,
        green: clickGreenPoint,
        blue: clickBluePoint,
        purple: clickPurplePoint,
      }
      return map[type](item)
    }

    // * 散点图点击事件
    const dotClick = (id, idWo) => {
      
    }

    onMounted(async () => {
      await getEngineList()
    })

    return {
      parentFilterFields,
      handleSearch,
      ascByGP,
      hideProject,
      hideTeam,
      engineList,
      setScatterChartRef,
      handleChangeSort,
      handleHideProject,
      hanldelHideTeam,
      clickPoint,
      colorPointDrawer,
    }
  },
  template: /*html*/ `
    <div class="m-4 border px-4">
      <SearchForm @search="handleSearch"></SearchForm>
    </div>
    <div class="m-2 flex items-center justify-between">
      <div class="flex-1 flex items-center justify-center">
        <el-radio-group v-model="ascByGP" @change="handleChangeSort">
          <el-radio label="0">放行时间</el-radio>
          <el-radio label="1">按照GP红框升序</el-radio>
          <el-radio label="2">按照班组计划升序</el-radio>
        </el-radio-group>
      </div>
      <div class="flex-1 flex items-center justify-center">
        <div class="flex items-center">
          <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
          <span>可串件</span>
        </div>
        <div class="flex items-center ml-4">
          <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
          <span>有条件串件</span>
        </div>
        <div class="flex items-center ml-4">
          <div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
          <span>不能串</span>
        </div>
        <div class="flex items-center ml-4">
          <div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
          <span>不具备串件条件</span>
        </div>
      </div>
      <div class="flex-1 flex items-center justify-center">
        <el-checkbox v-model="hideProject" true-label="1" false-label="0" @change="handleHideProject"
          >屏蔽GP红框</el-checkbox
        >
        <el-checkbox
          v-model="hideTeam"
          label="屏蔽班组计划"
          true-label="1"
          false-label="0"
          @change="hanldelHideTeam"
        ></el-checkbox>
      </div>
    </div>
    <el-empty v-if="engineList.length === 0" description="暂无数据"></el-empty>
    <div v-else>
      <div v-for="item in engineList" :key="item.id">
        <el-descriptions class="my-descriptions mx-4" border :column="8">
          <el-descriptions-item label="ESN" align="center">
            <div class="text-xl font-bold">{{ item.str_esn }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="WO" align="center">{{ item.str_wo }}</el-descriptions-item>
          <el-descriptions-item label="SM" align="center">{{ item.str_group }}</el-descriptions-item>
          <el-descriptions-item label="装配" align="center">{{ item.dt_project_start }}</el-descriptions-item>
          <el-descriptions-item label="红色" align="center">
            <el-button link type="danger" @click="clickPoint(item,'red')">{{ item.int_red }}</el-button>
          </el-descriptions-item>
          <el-descriptions-item label="绿色" align="center">
            <el-button link type="success" @click="clickPoint(item, 'green')">{{ item.int_green }}</el-button>
          </el-descriptions-item>
          <el-descriptions-item label="紫色" align="center">
            <el-button link type="info" @click="clickPoint(item, 'purple')">{{ item.int_purple }}</el-button>
          </el-descriptions-item>
          <el-descriptions-item label="蓝色" align="center">
            <el-button link type="primary" @click="clickPoint(item, 'blue')">{{ item.int_blue }}</el-button>
          </el-descriptions-item>
        </el-descriptions>
        <div class="h-[35vh] border mx-4">
          <ScatterChart
            :ref="(el) => setScatterChartRef(el, item.id)"
            :id="item.id"
            :id-wo="item.id_wo"
            :id-engine-type="item.id_engine_type"
            :type="item.str_group"
            :red-range="[item.dt_project_start, item.dt_project_end]"
            :green-range="[item.dt_adjust_start, item.dt_adjust_start]"
            :blue-range="[item.dt_team_start, item.dt_team_end]"
            :filter-fields="parentFilterFields"
            @dot-click="dotClick"
          ></ScatterChart>
        </div>
        <!-- 分割线 -->
        <div class="border-b border-gray-300 my-4"></div>
      </div>
    </div>
    <!-- 回到顶部 -->
    <el-backtop :bottom="50"></el-backtop>
    <!-- 点位颜色抽屉 -->
    <PointColotDrawer
      v-if="colorPointDrawer.visible"
      v-model:visible="colorPointDrawer.visible"
      :type="colorPointDrawer.type"
      :simulationType="colorPointDrawer.simulationType"
      :idWo="colorPointDrawer.idWo"
      :filterFields="colorPointDrawer.filterFields"
    ></PointColotDrawer>
  `,
}
export default UnitPerspective
