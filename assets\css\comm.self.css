.omit_line {
    width: 180px;
    height: 20px;
    /* margin: 50px auto; */
    overflow: hidden;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 1 !important;
    -webkit-box-orient: vertical !important;
    white-space: break-spaces;
    /* 和原vux-table 样式冲突*/


}
.el-table__column-filter-trigger i{
    color: #ffffff !important;
}
.vxe-table--body .vxe-cell{
    padding: 0 !important;
    /* height: 100% !important; */
    /* line-height: 100% !important; */
}
/* 滚动条样式*/
/* ::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-thumb {
    background: #2e6190;
    border-radius: 3px;
    
}

::-webkit-scrollbar-track-piece {
    width: 5px;
    background: #fff;
} */

/** 列表展开图标样式*/
/* .el-table__expand-icon {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
}

.el-table__expand-icon .el-icon-arrow-right::before {
    content: "edit";

}

.el-table__expand-icon--expanded .el-icon-arrow-right::before {
    content: "edit";

} */