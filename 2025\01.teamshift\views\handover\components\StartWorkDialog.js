const { h, defineComponent, inject, ref, onMounted } = Vue
const { ElDialog, ElButton, ElMessage } = ElementPlus
const { useVModel } = VueUse
import HeaderTips from '../../../components/Hanover/HeaderTips.js'
import CardInfo from '../../../components/Hanover/CardInfo.js'
import SearchForm from './SearchForm.js'
import { useDropdownOptions } from '../composables/useDropdownOptions.js'
import { fetchSmOptions, getTaskList, fetchInitHandover, saveHandover } from '../api/index.js'
import { flowNameMap } from '../utils/index.js'
import LaunchPanelDrawer from './LaunchPanelDrawer.js'

const StartWorkDialog = defineComponent({
  name: 'StartWorkDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:visible', 'refresh'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)

    const businessType = inject('businessType')

    const queryParams = ref({})

    const { esnOptions, shiftOptions, taskTypeOptions, staffOptions } = useDropdownOptions(null, businessType)

    const smOptions = ref([])
    const getSmOptions = async () => {
      if (!queryParams.value.esn) return
      const res = await fetchSmOptions(queryParams.value.esn)
      smOptions.value = res
    }

    const taskOptions = ref([])
    const getTaskOptions = async () => {
      if (!queryParams.value.esn || !queryParams.value.sm) return
      const params = {
        id_wo: queryParams.value.esn,
        str_sm: smOptions.value.find((item) => item.id === queryParams.value.sm)?.str_name,
        str_flow: flowNameMap[businessType],
      }
      const res = await getTaskList(params)
      taskOptions.value = res.map((item) => ({
        value: item.id,
        label: item.str_task + '( shift #' + item.int_sort + ')',
      }))
    }

    const headerTips = ref('')
    const teamName = ref('')
    const handoverData = ref([])

    const initTableData = async () => {
      try {
        const res = await fetchInitHandover()
        headerTips.value = res.str_title
        queryParams.value.teamId = res.id_team
        teamName.value = res.str_team_name
        queryParams.value.is_pending = '0'
        handoverData.value = res.pTHandovers
      } catch (error) {
        console.error(error)
      }
    }
    // 事件处理函数
    const handleEsnChange = (value) => {
      queryParams.value.sm = ''
      getSmOptions()
    }

    const handleSmChange = (value) => {
      getTaskOptions()
    }

    const validateRequiredFields = () => {
      if (businessType === 102) {
        if (!queryParams.value.shift || !queryParams.value.str_task_type) {
          ElMessage.error('班次和工序不能为空')
          return false
        }
      }
      if (businessType === 101) {
        if (!queryParams.value.esn || !queryParams.value.shift || !queryParams.value.str_task_type) {
          ElMessage.error('ESN、班次和工序不能为空')
          return false
        }
      }
      if (businessType != 101 && businessType != 102) {
        if (!queryParams.value.esn || !queryParams.value.shift || !queryParams.value.sm || !queryParams.value.task) {
          ElMessage.error('esn、单元体、班次、任务不能为空')
          return false
        }
      }
      return true
    }
    // 确认提交
    const handleConfirm = async () => {
      // 1. 验证必填项
      if (!validateRequiredFields()) return
      const str_wo = esnOptions.value.find((item) => item.id_wo === queryParams.value.esn)?.str_wo
      const str_shift = shiftOptions.value.find((item) => item.value === queryParams.value.shift)?.label
      const str_sm = smOptions.value.find((item) => item.id === queryParams.value.sm)?.str_name

      // 2. 构建主表数据
      const main = {
        id: '',
        id_wo: queryParams.value.esn || '',
        str_wo: str_wo || '',
        id_team: queryParams.value.teamId || '',
        dt_pt: moment().format('YYYY-MM-DD'),
        id_shift: queryParams.value.shift || '',
        str_shift: str_shift || '',
        id_model: queryParams.value.sm || '',
        str_sm: str_sm || '',
        id_task: queryParams.value.task || '',
        str_handover_type: businessType,
        id_by_to_receive: queryParams.value.id_by_to_receive || '',
        is_pending: queryParams.value.is_pending || 0,
        int_handover_status: 0,
        str_task_type: queryParams.value.str_task_type || '',
      }
      handoverData.value.forEach((item) => {
        item.pTHandover.id_model = queryParams.value.sm || ''
      })
      // 3. 提交接口
      const params = [
        {
          pTHandoverMain: main,
          pTHandovers: handoverData.value,
        },
      ]
      await saveHandover(params)
      ElMessage.success('提交成功')
      // 4. 打开开工交接抽屉
      openLaunchPanelDrawer()
    }

    const isLaunchPanelDrawerVisible = ref(false)
    const openLaunchPanelDrawer = () => {
      isLaunchPanelDrawerVisible.value = true
    }

    const handleClose = () => {
      visible.value = false
      emit('refresh')
    }

    onMounted(() => {
      initTableData()
    })

    const dialogProps = {
      class: 'common-dialog',
      modelValue: visible.value,
      title: '开工交接单',
      width: '80%',
      appendToBody: true,
      onClose: () => emit('update:visible', false),
    }

    // 底部按钮
    const renderFooter = () =>
      h('div', { class: 'flex justify-end' }, [
        h(ElButton, { type: 'primary', onClick: () => handleConfirm() }, { default: () => '确定' }),
        h(ElButton, { onClick: () => emit('update:visible', false) }, { default: () => '取消' }),
      ])

    const renderOuterContainer = () => [
      h(ElDialog, dialogProps, {
        default: () => [
          h(HeaderTips, { tips: headerTips.value }),
          h(CardInfo, { flow: flowNameMap[businessType], teamName: teamName.value }),
          h(SearchForm, {
            modelValue: queryParams.value,
            esnOptions: esnOptions.value,
            smOptions: smOptions.value,
            shiftOptions: shiftOptions.value,
            taskOptions: taskOptions.value,
            taskTypeOptions: taskTypeOptions.value,
            staffOptions: staffOptions.value,
            'onEsn-change': handleEsnChange,
            'onSm-change': handleSmChange,
          }),
        ],
        footer: () => renderFooter(),
      }),
    ]

    const renderLaunchPanelDrawer = () =>
      isLaunchPanelDrawerVisible.value &&
      h(LaunchPanelDrawer, {
        visible: isLaunchPanelDrawerVisible.value,
        queryParams: queryParams.value,
        onClose: () => handleClose(),
      })

    return () => [renderOuterContainer(), renderLaunchPanelDrawer()]
  },
})

export default StartWorkDialog
