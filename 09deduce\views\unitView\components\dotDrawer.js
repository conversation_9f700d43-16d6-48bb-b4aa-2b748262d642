import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
const { useVModel } = VueUse
/**
 * @description 点的抽屉组件
 * @date 2024年8月7日10:31:29
 */
const DotDrawer = {
  components: {
    HtVxeTable,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)

    return {
      visible,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="85%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-white">零件清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <HtVxeTable
          ref="tableRef"
          :tableData="data"
          :tableColumns="columns"
          :isShowHeaderCheckbox="true"
          @filterChange="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
  `,
}

export default DotDrawer
