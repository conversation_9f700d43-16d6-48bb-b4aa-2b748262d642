const { reactive, ref, onMounted, nextTick, onUpdated, defineAsyncComponent } = Vue
import ySmGlobal from '../../../02gp_view/00view/01global/02sm.compent.js'
// import { y_el_draw } from '../../02comm.config/comm.component/y-el-draw/y_el_draw.component.js'
import yPnGlobal from '../../../02gp_view/00view/01global/03pn.compent.js'
import { post } from '../../../config/axios/httpReuest.js';
import HtDrawer from '../../../components/ht.drawer.js';

export default {
  name: 'yMpGlobal', // 组件名称
  props: ['id', 'idContract'],
  components: {
    // 外部组件
    'y-mp-sm': ySmGlobal,
    HtDrawer,
    'y-mp-pn': yPnGlobal,
  },
  setup(props, ctx) {
    const formInline = ref({})
    const key_t = ref(0);
    const total_num = ref(0);
    const activeName = ref(["1", "2"])
    const woS = ref([]);
    /**查询 */
    const query = async () => {
      const params = {
        au: 'ssamc',
        ap: 'api2018',
        ak: '',
        ac: 'mp_get_esn_global_view',
        filter: Object.assign(formInline.value, {})
      }
      const { data } = await post(params);

      if (data.code === 'success') {
        woS.value = data.data;
      } else {
        console.log(data.message)
      }
    }
    const smDialog = reactive({
      isShowSave: false,
      visible: false,
      title: "单元体",
      form:{}

    })
    const openSmDialog = (form) => {
      smDialog.visible = true;
      smDialog.form = form;
    }
    const closeSmDialog = () => {
      smDialog.visible = false;
    }
    const pnDialog = reactive({
      isShowSave: false,
      visible: false,
      title: "零件清单",
      form: {

      }// 参数

    })
    const openPnDialog = (form) => {
      pnDialog.visible = true;
      pnDialog.form = form;
    }
    const closePnDialog = () => {
      pnDialog.visible = false;
    }
    /**时间格式 */
    const momentF = (data) => {
      return moment(data).format('YYYY-MM-DD')
    }
    /**状态样式 */
    const styleStatue = (data) => {
      if (data.dt_ekd > data.dt_mp) {
        return "border:3px solid ;border-color:red"
      } if (data.dt_ekd < data.dt_mp) {
        return "border-color:green"
      } else {
        return "my_gray";
      }
    }
    /**状态样式 */
    const styleNumberStatue = (intDay) => {
      if (intDay < 0) {
        return "my-red"
      } else if (intDay > 0) {
        return "my_green"
      } else if (intDay == 0) {
        return "my_gray"
      }  else {
        return "my_gray";
      }
    }
    onMounted(() => {
      query();

    });
    onUpdated(() => {
      nextTick(() => {

      });

    });

    return {
      query,
      formInline,
      activeName,
      smDialog,
      openSmDialog,
      closeSmDialog,
      pnDialog,
      openPnDialog,
      closePnDialog,
      woS,
      key_t,
      total_num,
      momentF,
      styleStatue,
      styleNumberStatue
    }
  },
  template:/*template*/`
  
    <el-form :inline="true" :model="formInline" class="demo-form-inline" style="margin-left: 30px;">
        <el-form-item label="key">
            <el-input v-model="formInline.key" placeholder="wo/esn" clearable />
        </el-form-item>
        <el-form-item label="Type"  style="width:10%">
            <el-select v-model="formInline.str_type" placeholder="机型" clearable>
                <el-option label="CFM" value="CFM" />
                <el-option label="LEAP" value="LEAP" />
            </el-select>
        </el-form-item>
        <el-form-item label="SM" style="width:10%">
        <el-input v-model="formInline.key" placeholder="SM" clearable />
           
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="query" size="small">   <el-icon>
            <Search></Search>
          </el-icon>
          </el-button>
        </el-form-item>
    </el-form>
    <el-divider style="margin:5px 0" />
    <el-row >
    <el-col :span="6">
      <el-statistic title="All" :value="woS.length" >
    </el-col>
    <el-col :span="6">
    <el-statistic >
        <template #title>
          <div style="display: inline-flex; align-items: center;color:red;font-weight:600">
           Delay
          </div>
        </template>
       <!-- <template #suffix>/100</template>-->
      </el-statistic>
    </el-col>
    <el-col :span="6">
      <el-statistic title="Ahead" :value="outputValue" >
      <template #title>
      <div style="display: inline-flex; align-items: center;color:green;font-weight:600">
       Ahead
      </div>
    </template>
    </el-statistic>
    </el-col>
    <el-col :span="6">
    <el-statistic title="Normal"  >
    </el-col>
  </el-row>
  <el-divider />
            <el-row style="padding:1px">
                <el-col :span="4" style="margin-bottom: 5px;padding-right:1px" v-for="(wo, index) in woS">
                    <el-card class="box-card" :style="styleStatue(wo)">
                        <template #header>
                            <div class="card-header">
                            <el-row>   
                            <el-col :span="20">  <span style="font-weight: 800">{{wo.str_esn}} </span> </el-col>
                            <el-col :span="4"> <el-button  size="small" type="primary"  @click="openSmDialog(wo)">SM</el-button> </el-col>
                               
                               </el-row>
                                <span style="font-size: 12px;font-weight: 600">MP:{{wo.dt_mp}}</span>
                                <el-col :span="12"><span style="font-size: 12px;font-weight: 600">EKD：{{momentF(wo.dt_ekd)}}</span></el-col>
                                <el-row> <span style="font-size: 12px;">Customer：{{wo.str_customer}}</span></el-row>
                                <el-row>
                                <el-col :span="12"><span style="font-size: 12px;">TAT：<span
                                style="font-weight: 800">60</span></span></el-col>
                                    <el-col :span="12"><span style="font-size: 12px;">F3 TAT：<span
                                                style="font-weight: 800">6</span></span></el-col>
                                </el-row>

                            </div>
                        </template>
                        <el-row style="margin-top: 5px;" :gutter="20">
                            <el-col :span="6">
                       
                                <el-badge :value="wo.int_delay_day_core"  :max="999" :class="styleNumberStatue(wo.int_delay_day_core)">
                                    <el-button size="small" round plain  >Core</el-button>
                                     <!-- @click="openPnDialog({group:'Core'})"-->
                                </el-badge>
                            </el-col>
                            <el-col :span="6">
                                <el-badge :value="wo.int_delay_day_fan"   :max="999" :class="styleNumberStatue(wo.int_delay_day_fan)">
                                    <el-button size="small" round plain >FAN</el-button>
                                    <!-- @click="openPnDialog({group:'FAN'})"-->
                                </el-badge>
                            </el-col>

                            <el-col :span="6">
                                <el-badge :value="wo.int_delay_day_lpt"  :max="999" :class="styleNumberStatue(wo.int_delay_day_lpt)">
                                    <el-button size="small" round plain >LPT</el-button>
                                     <!--  @click="openPnDialog({group:'LPT'})"-->
                                </el-badge>
                            </el-col>

                            <el-col :span="6">
                                <el-badge :value="wo.int_delay_day_b1"   :max="999" :class="styleNumberStatue(wo.int_delay_day_b1)">
                                    <el-button size="small" round plain >B1</el-button>
                                     <!--   @click="openPnDialog({group:'B1'})"-->
                                </el-badge>
                            </el-col>
                        </el-row>
                    </el-card>
                </el-col>
            </el-row>



<ht-drawer width="90%" :visible="smDialog.visible"  :isShowSave="smDialog.isShowSave" :title="smDialog.title" @clear="closeSmDialog">
<y-mp-sm :form="smDialog.form"></y-mp-sm>
</ht-drawer>

<ht-drawer width="90%" :visible="pnDialog.visible" :isShowSave="pnDialog.isShowSave" :title="pnDialog.title" @clear="closePnDialog">
<y-mp-pn :form="smDialog.form"></y-mp-pn>
</ht-drawer>
  `,

}
