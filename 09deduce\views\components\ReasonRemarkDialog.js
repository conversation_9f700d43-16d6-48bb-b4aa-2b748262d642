/**
 * 原因备注
 */
const { defineModel } = Vue
const ReasonRemarkDialog = {
  name: 'ReasonRemarkDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
      default: () => ({}),
    },
    title: {
      type: String,
      default: '原因备注',
    },
    disabledDate: {
      type: String,
      default: null,
    },
  },
  emits: ['saveReasonRemark', 'closeReasonRemark'],
  setup(props, { emit }) {
    const isShow = defineModel('visible', { required: true })
    const saveReasonRemark = () => {
      emit('saveReasonRemark')
    }
    const closeReasonRemark = () => {
      emit('closeReasonRemark')
    }
    const isDisabledDate = (date) => {
      return moment(date).isAfter(props.disabledDate)
    }
    return { saveReasonRemark, closeReasonRemark, isShow }
  },
  template: /*html*/ `
    <el-dialog class="my-dialog" v-model="isShow" :title="title" :show-close="false">
      <el-form class="mx-2 mt-2" :model="form" label-width="100px">
        <el-form-item v-if="title.indexOf('保交付')>-1" label="保交付">
          <el-radio-group prop="int_type" v-model="form.int_type">
            <el-radio :label="1">保交付</el-radio>
            <el-radio :label="0">取消保障</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="title.indexOf('保交付')>-1" label="保交付时间" prop="dt_delivery">
          <el-date-picker
            v-model="form.dt_delivery"
            type="date"
            placeholder="请选择交付始时间"
            value-format="YYYY-MM-DD"
            :disabled-date="isDisabledDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item v-if="title.indexOf('保交付')<0" label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="saveReasonRemark">保存</el-button>
        <el-button @click="closeReasonRemark">取消</el-button>
      </template>
    </el-dialog>
  `,
}
export default ReasonRemarkDialog
