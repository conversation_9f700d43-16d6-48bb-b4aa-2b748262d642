const { ref, shallowRef, reactive } = Vue
import { post } from '../../../../config/axios/httpReuest.js'

export const useChartConfig = () => {
  const chartRef = ref(null)
  const myChart = shallowRef(null)
  const timeRangeType = ref('1')
  const engineDeliveryData = reactive({})
  const dailyData = ref([])

  // 时间范围类型常量
  const TIME_RANGE_TYPES = {
    DAY: '0',
    MONTH: '1',
    QUARTER: '2',
    YEAR: '3',
  }

  // 日期生成器
  const dateGenerators = {
    [TIME_RANGE_TYPES.DAY]: (data) => {
      if (!data || data.length === 0) return []
      return [
        ...new Set(
          data.map((item) => {
            const date = new Date(item.dt_date)
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
          }),
        ),
      ]
        .sort()
        .map((dateStr) => {
          const [year, month, day] = dateStr.split('-')
          return `${year}/${month}/${day}`
        })
    },
    [TIME_RANGE_TYPES.MONTH]: (data) => {
      if (!data || data.length === 0) return []
      const dates = [
        ...new Set(
          data.map((item) => {
            const date = new Date(item.dt_date)
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
          }),
        ),
      ]
        .sort()
        .map((dateStr) => {
          const [year, month] = dateStr.split('-')
          return `${year}年${month}月`
        })
      return dates
    },
    [TIME_RANGE_TYPES.QUARTER]: (data) => {
      if (!data || data.length === 0) return []
      const dates = [
        ...new Set(
          data.map((item) => {
            const date = new Date(item.dt_date)
            const quarter = Math.floor(date.getMonth() / 3) + 1
            return `${date.getFullYear()}-Q${quarter}`
          }),
        ),
      ]
        .sort()
        .map((dateStr) => {
          const [year, quarter] = dateStr.split('-')
          return `${year}年${quarter}`
        })
      return dates
    },
    [TIME_RANGE_TYPES.YEAR]: (data) => {
      if (!data || data.length === 0) return []
      return [
        ...new Set(
          data.map((item) => {
            const date = new Date(item.dt_date)
            return `${date.getFullYear()}年`
          }),
        ),
      ].sort()
    },
  }

  // 数据聚合函数
  const aggregateData = (data, groupFn, key) => {
    if (!data || data.length === 0) return []

    const sortedData = [...data].sort((a, b) => new Date(a.dt_date) - new Date(b.dt_date))
    const groups = {}

    sortedData.forEach((item) => {
      const date = new Date(item.dt_date)
      const groupKey = groupFn(date)

      if (groupFn === dateGenerators[TIME_RANGE_TYPES.DAY]) {
        if (!groups[groupKey]) {
          groups[groupKey] = { sum: 0 }
        }
        if (item[key] !== null) {
          groups[groupKey].sum += Number(item[key])
        }
      } else {
        if (!groups[groupKey] || item[key] !== null) {
          groups[groupKey] = {
            sum: item[key] !== null ? Number(item[key]) : null,
          }
        }
      }
    })

    return Object.keys(groups)
      .sort()
      .map((groupKey) => groups[groupKey].sum)
  }

  // 按不同时间维度聚合数据
  const seriesDataGenerators = {
    [TIME_RANGE_TYPES.DAY]: (key) => {
      return aggregateData(
        dailyData.value,
        (date) =>
          `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`,
        key,
      )
    },
    [TIME_RANGE_TYPES.MONTH]: (key) => {
      return aggregateData(
        dailyData.value,
        (date) => `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`,
        key,
      )
    },
    [TIME_RANGE_TYPES.QUARTER]: (key) => {
      return aggregateData(
        dailyData.value,
        (date) => {
          const quarter = Math.floor(date.getMonth() / 3) + 1
          return `${date.getFullYear()}-Q${quarter}`
        },
        key,
      )
    },
    [TIME_RANGE_TYPES.YEAR]: (key) => {
      return aggregateData(dailyData.value, (date) => String(date.getFullYear()), key)
    },
  }

  // 生成每日数据
  const generateDailyData = async (filterFields = []) => {
    const params = {
      ac: 'de_delivery_forecast_report',
      filter_fields: filterFields,
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      dailyData.value = data.data
      if (data.data.length > 0) {
        engineDeliveryData['预进厂'] = '000'
        engineDeliveryData['预测'] = data.data.at(-1).int_green.toString()
        engineDeliveryData['计划'] = data.data.at(-1).int_red.toString()
      } else {
        engineDeliveryData['预进厂'] = '000'
        engineDeliveryData['预测'] = '000'
        engineDeliveryData['计划'] = '000'
      }
    } else {
      ElementPlus.ElMessage.error(data.text)
      dailyData.value = []
    }
  }

  // 图表配置
  const getChartOption = (timeType = TIME_RANGE_TYPES.MONTH) => ({
    backgroundColor: 'transparent',
    title: {
      text: '发动机交付预测趋势图',
      textStyle: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
      },
      left: 'center',
      top: 10,
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: 'rgba(255,255,255,0.2)',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      formatter: function (params) {
        let result = `<div class="font-bold">${params[0].axisValue}</div>`
        params.forEach((param) => {
          if (param.value !== null) {
            const color = param.color
            const marker = `<span style="display:inline-block;width:10px;height:10px;background:${color};margin-right:5px;border-radius:50%;"></span>`
            result += `<div style="display:flex;align-items:center;padding:3px 0">
              ${marker}${param.seriesName}: ${param.value ?? '--'}
            </div>`
          }
        })
        return result
      },
    },
    legend: {
      show: true,
      top: 40,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 20,
      data: ['发动机已交付数量', '预进厂', '计划', '预测', '目标'],
    },
    grid: {
      left: '3%',
      right: '5%',
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: timeType === TIME_RANGE_TYPES.DAY ? 30 : 100,
        height: 20,
        bottom: 10,
        borderColor: 'transparent',
        backgroundColor: 'rgba(47,69,84,0.3)',
        fillerColor: 'rgba(167,183,204,0.4)',
        handleStyle: {
          color: '#fff',
          borderColor: '#ACB8D1',
        },
        textStyle: {
          color: '#fff',
        },
        moveHandleSize: 7,
        brushSelect: false,
      },
      {
        type: 'slider',
        show: true,
        yAxisIndex: [0],
        start: 0,
        end: 100,
        width: 20,
        right: 10,
        top: 60,
        bottom: 50,
        borderColor: 'transparent',
        backgroundColor: 'rgba(47,69,84,0.3)',
        fillerColor: 'rgba(167,183,204,0.4)',
        handleStyle: {
          color: '#fff',
          borderColor: '#ACB8D1',
        },
        textStyle: {
          color: '#fff',
        },
        moveHandleSize: 7,
        brushSelect: false,
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        start: 0,
        end: timeType === TIME_RANGE_TYPES.DAY ? 30 : 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
      },
      {
        type: 'inside',
        yAxisIndex: [0],
        start: 0,
        end: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dateGenerators[timeType](dailyData.value),
      axisLine: {
        lineStyle: {
          color: 'rgba(255,255,255,0.3)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
        interval: timeType === TIME_RANGE_TYPES.DAY ? 0 : 'auto',
        rotate: timeType === TIME_RANGE_TYPES.DAY ? 45 : 0,
        fontSize: timeType === TIME_RANGE_TYPES.DAY ? 10 : 12,
        formatter: timeType === TIME_RANGE_TYPES.DAY ? (value) => value.split('/').slice(1).join('/') : undefined,
        margin: 14,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255,255,255,0.1)',
          type: 'dashed',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255,255,255,0.3)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255,255,255,0.1)',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '发动机已交付数量',
        type: timeType === TIME_RANGE_TYPES.YEAR ? 'scatter' : 'line',
        data: seriesDataGenerators[timeType]('int_delivery'),
        symbolSize: timeType === TIME_RANGE_TYPES.YEAR ? 16 : 6,
        symbol: 'circle',
        smooth: timeType !== TIME_RANGE_TYPES.YEAR,
        lineStyle:
          timeType === TIME_RANGE_TYPES.YEAR
            ? undefined
            : {
                width: 3,
                color: '#409EFF',
              },
        itemStyle: {
          color: '#409EFF',
          borderWidth: timeType === TIME_RANGE_TYPES.YEAR ? 1 : 2,
          borderColor: '#fff',
          shadowColor: timeType === TIME_RANGE_TYPES.YEAR ? 'rgba(0,0,0,0.3)' : undefined,
          shadowBlur: timeType === TIME_RANGE_TYPES.YEAR ? 5 : undefined,
        },
        areaStyle:
          timeType === TIME_RANGE_TYPES.YEAR
            ? undefined
            : {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(64,158,255,0.3)' },
                  { offset: 1, color: 'rgba(64,158,255,0.1)' },
                ]),
              },
        connectNulls: timeType !== TIME_RANGE_TYPES.YEAR,
      },
      {
        name: '预进厂',
        type: 'scatter',
        data: seriesDataGenerators[timeType]('int_coming'),
        symbolSize: 12,
        itemStyle: {
          color: '#E6A23C',
          borderColor: '#fff',
          borderWidth: 1,
          shadowColor: 'rgba(0,0,0,0.3)',
          shadowBlur: 5,
        },
      },
      {
        name: '计划',
        type: 'scatter',
        data: seriesDataGenerators[timeType]('int_red'),
        symbolSize: 12,
        itemStyle: {
          color: '#F56C6C',
          borderColor: '#fff',
          borderWidth: 1,
          shadowColor: 'rgba(0,0,0,0.3)',
          shadowBlur: 5,
        },
      },
      {
        name: '预测',
        type: 'scatter',
        data: seriesDataGenerators[timeType]('int_green'),
        symbolSize: 12,
        itemStyle: {
          color: '#67C23A',
          borderColor: '#fff',
          borderWidth: 1,
          shadowColor: 'rgba(0,0,0,0.3)',
          shadowBlur: 5,
        },
      },
      {
        name: '目标',
        type: timeType === TIME_RANGE_TYPES.YEAR ? 'scatter' : 'line',
        data: seriesDataGenerators[timeType]('int_standard'),
        symbolSize: timeType === TIME_RANGE_TYPES.YEAR ? 16 : 6,
        symbol: 'circle',
        smooth: timeType !== TIME_RANGE_TYPES.YEAR,
        lineStyle:
          timeType === TIME_RANGE_TYPES.YEAR
            ? undefined
            : {
                width: 3,
                color: '#FF9800',
              },
        itemStyle: {
          color: '#FF9800',
          borderWidth: timeType === TIME_RANGE_TYPES.YEAR ? 1 : 2,
          borderColor: '#fff',
          shadowColor: timeType === TIME_RANGE_TYPES.YEAR ? 'rgba(0,0,0,0.3)' : undefined,
          shadowBlur: timeType === TIME_RANGE_TYPES.YEAR ? 5 : undefined,
        },
        areaStyle:
          timeType === TIME_RANGE_TYPES.YEAR
            ? undefined
            : {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(64,158,255,0.3)' },
                  { offset: 1, color: 'rgba(64,158,255,0.1)' },
                ]),
              },
        connectNulls: timeType !== TIME_RANGE_TYPES.YEAR,
      },
    ],
  })

  const handleResize = () => {
    myChart.value?.resize()
  }

  const changeTimeRangeType = (val) => {
    myChart.value?.setOption(getChartOption(val))
  }

  return {
    chartRef,
    myChart,
    timeRangeType,
    engineDeliveryData,
    getChartOption,
    handleResize,
    changeTimeRangeType,
    generateDailyData,
  }
}
