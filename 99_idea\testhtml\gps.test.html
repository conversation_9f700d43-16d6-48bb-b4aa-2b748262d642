<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=d7757fcc848cb31e7cfca7f70614dae0"></script>
    <title>Document</title>
</head>
<body>
    <div id="container"  style="width: 100%; height: 100%;"></div>
</body>

<script>
    // 页面加载完毕后初始化地图
    window.onload = function() {
        // 创建地图实例
        var map = new AMap.Map('container', {
            resizeEnable: true,
            zoom: 11, // 地图显示的缩放级别
            center: [116.397428, 39.90923] // 地图中心点坐标
        });
    }
</script>
<!-- <script type="text/javascript">
   const map = new AMap.Map("container", {
  viewMode: '2D', //默认使用 2D 模式
  zoom: 11, //地图级别
  center: [116.397428, 39.90923], //地图中心点
});
  </script> -->

  
<script>
    let gpsStr=`114.677041,38.041433;114.609943,38.054698;114.591202,38.058403;114.591277,38.056564;114.591296,38.055054;114.591154,38.037099;114.546314,38.036234;114.546296,38.036307;114.546208,38.036307;114.546131,38.036305;114.546049,38.036303;114.545996,38.036229;114.544897,38.036208;114.540136,38.036115;114.538522,38.036084;114.535183,38.036019;114.532896,38.035977;114.532769,38.035942;114.531932,38.035959;114.526236,38.035855;114.524302,38.035781;114.522864,38.035754;114.517727,38.035701;114.517699,38.0357;114.517515,38.035697;114.5175,38.035695;114.517216,38.03569;114.517198,38.035691;114.511869,38.035595;114.511846,38.039006;114.511833,38.040876;114.511827,38.041799;114.511826,38.041989;114.511825,38.04212;114.511732,38.043014;114.50596,38.043243;114.503951,38.043316;114.501255,38.04339;114.498808,38.043314;114.497198,38.043356;114.497113,38.043359;114.493797,38.043457;114.492735,38.04339;114.492584,38.043611;114.493397,38.049757;114.494522,38.055729;114.494323,38.055938;114.498133,38.072211;114.495669,38.072229;114.495662,38.072502;114.495622,38.072619;114.495637,38.073549;114.495645,38.073957;114.495683,38.07399;114.496444,38.073947;114.497051,38.07394;114.497123,38.073896;114.497432,38.073883;114.498208,38.076025;114.498941,38.078206;114.500779,38.077993;114.502141,38.081578;114.493358,38.082763;114.493409,38.08306;114.493361,38.08309;114.493418,38.083115;114.494932,38.091932;114.494737,38.096628;114.494738,38.096643;114.494736,38.096658;114.494725,38.096923;114.495115,38.101639;114.494882,38.101639;114.495132,38.104546;114.492167,38.102723;114.490827,38.102393;114.489357,38.102367;114.482207,38.102943;114.480201,38.105969;114.480167,38.106238;114.480331,38.106472;114.480868,38.10667;114.485018,38.106623;114.485312,38.106727;114.485779,38.111707;114.486186,38.111775;114.487526,38.112644;114.489056,38.113343;114.484111,38.114064;114.501874,38.129363;114.503622,38.130869;114.504809,38.131352;114.507311,38.131759;114.509717,38.131817;114.511322,38.131619;114.512934,38.131608;114.51566,38.131368;114.5175,38.131379;114.518814,38.131541;114.520615,38.132387;114.521272,38.132405;114.522627,38.131659;114.523428,38.131471;114.526714,38.13107;114.529781,38.130409;114.533376,38.129304;114.536772,38.127953;114.539783,38.126687;114.541729,38.126044;114.544304,38.124575;114.547464,38.123209;114.550675,38.122497;114.556227,38.121699;114.561624,38.120305;114.566331,38.117654;114.571416,38.116389;114.577319,38.115845;114.582675,38.115735;114.58844,38.116538;114.589923,38.116472;114.592134,38.115598;114.596089,38.113045;114.599985,38.1113;114.611198,38.109227;114.62104,38.110267;114.633957,38.109545;114.641297,38.108069;114.647498,38.105929;114.653339,38.104176;114.65687,38.103098;114.663799,38.102839;114.664165,38.112787;114.669703,38.111707;114.671146,38.111476;114.678471,38.111476;114.693554,38.107407;114.693421,38.102353;114.693852,38.097153;114.703781,38.097203;114.703794,38.094157;114.705118,38.094168;114.705463,38.0939;114.705417,38.091739;114.707252,38.091554;114.707377,38.091357;114.705096,38.078555;114.699366,38.070146;114.695377,38.063386;114.694547,38.063536;114.693046,38.065169;114.691191,38.065332;114.689745,38.064666;114.687684,38.062389;114.687724,38.061481;114.687575,38.060046;114.684139,38.060488;114.683646,38.058275;114.681716,38.059273;114.679274,38.053839;114.679357,38.051579;114.679019,38.051585;114.678927,38.046291;114.678312,38.045976;114.676406,38.046182;114.676221,38.042784;114.676596,38.042359;114.677041,38.041433`;
    function getLocation() {
        let gps=gpsStr.split(";");
        let positionArr=[];
     
        for(let i=0;i<gps.length;i++){
            let gpsArr=gps[i].split(",");
            positionArr.push({x:parseFloat(gpsArr[1]),y:parseFloat(gpsArr[0])});
        }
        const jsonString = JSON.stringify(positionArr);
        console.log(jsonString);
    }
    getLocation();
   
</script>

</html>

