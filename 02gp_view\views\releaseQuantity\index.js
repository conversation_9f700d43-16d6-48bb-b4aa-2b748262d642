import { getReleaseQuantityGantt } from '../../api/index.js'
import { useDynamicColumn } from '../../composables/useDynamicColumn.js'
import DynamicColumnConfigurator from '../../components/DynamicColumnConfigurator.js'
const { ref, reactive, onMounted, nextTick, computed, onUnmounted } = Vue

// 甘特图基础配置
const GANTT_CONFIG = {
  date_format: '%Y-%m-%d',
  date_grid: '%Y-%m-%d',
  drag_links: false,
  grid_width: 800,
  open_tree_initially: true,
  drag_project: false,
  drag_progress: false,
  auto_scheduling: true,
  drag_move: false,
  columns: [
    { name: 'text', label: 'ESN', tree: true, width: '*', resize: true },
    { name: 'wo', label: 'WO', align: 'center', width: 100 },
    { name: 'engine_type', label: '发动机类型', align: 'center', width: 100 },
    { name: 'client', label: '客户', align: 'center', width: 100 },
    { name: 'level', label: '修理级别', align: 'center', width: 100 },
    { name: 'start_date', label: '开始时间', align: 'center', width: 100 },
    { name: 'dt_end', label: '结束时间', align: 'center', width: 100 },
    { name: 'duration', label: '持续时间', align: 'center', width: 100 },
    { name: 'dt_f2_3_end', label: 'F2/3结束时间', width: 100 },
  ],
  min_column_width: 100,
  scale_height: 100,
  scales: [
    // 年
    { unit: 'year', step: 1, format: '%Y年' },
    // 月
    { unit: 'month', step: 1, format: '%M' },
    // 周
    { unit: 'week', step: 1, format: 'W%w' },
    // 星期
    { unit: 'day', step: 1, format: '%D' },
    // 天
    { unit: 'day', step: 1, format: '%m-%d' },
  ],
}

export const ReleaseQuantityGantt = {
  components: {
    DynamicColumnConfigurator,
  },
  setup(props, { emit }) {
    const formSearch = reactive({
      esn: '',
      wo: '',
      engine_type: '',
      client: '',
      level: '',
    })

    const dateValue = ref([])

    const ganttParse = ref({
      data: [],
      links: [],
    })

    const isCollapseGrid = ref(false)

    // 计算属性优化按钮文本显示
    const gridButtonText = computed(() => (isCollapseGrid.value ? '展开左侧' : '收起左侧'))

    // 初始化甘特图配置
    const initGanttConfig = () => {
      gantt.plugins({
        auto_scheduling: true,
        critical_path: true,
        marker: true,
      })

      Object.entries(GANTT_CONFIG).forEach(([key, value]) => {
        gantt.config[key] = value
      })

      gantt.templates.task_class = (start, end, task) => {
        return `release-quantity-${task.int_state}`
      }
      // 禁用任务双击事件
      gantt.attachEvent('onTaskDblClick', function (id, e) {
        return false // 返回 false 阻止默认行为
      })
      gantt.config.start_date = new Date() // 设置开始日期为今天
      gantt.config.scroll_position = new Date() // 设置滚动位置到今天

      gantt.attachEvent('onGanttRender', function () {
        gantt.showDate(new Date()) // 滚动到今天
      })
    }

    const dynamicColumn = useDynamicColumn(gantt, GANTT_CONFIG.columns)

    // 数据转换优化
    const transformData = (data) => {
      return data.map(
        ({
          id,
          id_root,
          str_esn,
          dt_start,
          dt_end,
          str_wo,
          str_engine_type,
          str_client,
          str_level,
          int_state,
          duration,
        }) => ({
          id,
          text: str_esn,
          start_date: dt_start ? moment(dt_start).format('YYYY-MM-DD') : '',
          // 结束时间为23:59:59
          dt_end: dt_end ? moment(dt_end).format('YYYY-MM-DD') : '',
          duration: duration,
          wo: str_wo,
          engine_type: str_engine_type,
          client: str_client,
          level: str_level,
          parent: id_root,
          render: id_root === '0' ? 'split' : '',
          int_state: int_state,
        }),
      )
    }

    // 添加当前日期标记
    const addCurrentDateMarker = () => {
      const today = new Date()
      gantt.addMarker({
        start_date: today,
        css: 'current-date-marker',
        text: moment(today).format('YYYY-MM-DD'),
        title: '今天',
      })
    }

    const renderGantt = () => {
      gantt.init('gantt-container')
      addCurrentDateMarker()
      gantt.parse(ganttParse.value)
      gantt.render()
    }

    const loading = ref(false)

    // 应用筛选器
    const applyFilters = () => {
      gantt.refreshData()
      getGanttData()
    }

    // 设置甘特图筛选器
    const setupFilters = () => {
      gantt.attachEvent('onBeforeTaskDisplay', function (id, task) {
        // ESN 筛选
        if (formSearch.esn && formSearch.esn.trim()) {
          const taskText = task.text || ''
          if (!taskText.toLowerCase().includes(formSearch.esn.toLowerCase())) {
            return false
          }
        }
        
        // WO 筛选
        if (formSearch.wo && formSearch.wo.trim()) {
          const taskWo = task.wo || ''
          if (!taskWo.toLowerCase().includes(formSearch.wo.toLowerCase())) {
            return false
          }
        }
        
        // 发动机类型筛选
        if (formSearch.engine_type && formSearch.engine_type.trim()) {
          const taskEngineType = task.engine_type || ''
          if (!taskEngineType.toLowerCase().includes(formSearch.engine_type.toLowerCase())) {
            return false
          }
        }
        
        // 客户筛选
        if (formSearch.client && formSearch.client.trim()) {
          const taskClient = task.client || ''
          if (!taskClient.toLowerCase().includes(formSearch.client.toLowerCase())) {
            return false
          }
        }
        
        // 修理级别筛选
        if (formSearch.level && formSearch.level.trim()) {
          const taskLevel = task.level || ''
          if (!taskLevel.toLowerCase().includes(formSearch.level.toLowerCase())) {
            return false
          }
        }
        
        return true
      })
    }

    const getGanttData = async () => {
      gantt.clearAll()
      try {
        loading.value = true
        let startDate, endDate
        if (Array.isArray(dateValue.value)) {
          startDate = dateValue.value[0]
          endDate = dateValue.value[1]
        } else {
          startDate = endDate = dateValue.value
        }
        const data = await getReleaseQuantityGantt('', startDate, endDate)

        // 创建一个映射来存储每个父级的最早开始时间
        const parentStartTimes = new Map()

        // 第一次遍历：计算父级的最早开始时间
        data.subPlans.forEach((item) => {
          if (item.id_root !== '0') {
            // 如果是子项
            const parentId = item.id_root
            const startTime = item.dt_start ? new Date(item.dt_start).getTime() : Infinity

            if (!parentStartTimes.has(parentId)) {
              parentStartTimes.set(parentId, startTime)
            } else {
              parentStartTimes.set(parentId, Math.min(parentStartTimes.get(parentId), startTime))
            }
          }
        })

        // 进行排序
        data.subPlans.sort((a, b) => {
          const getEffectiveStartTime = (item) => {
            if (item.id_root === '0') {
              // 如果是父项
              // 使用自己的开始时间或子项的最早开始时间
              const ownStartTime = item.dt_start ? new Date(item.dt_start).getTime() : Infinity
              const childrenStartTime = parentStartTimes.get(item.id) || Infinity
              return Math.min(ownStartTime, childrenStartTime)
            } else {
              // 如果是子项
              return item.dt_start ? new Date(item.dt_start).getTime() : Infinity
            }
          }

          const aStartTime = getEffectiveStartTime(a)
          const bStartTime = getEffectiveStartTime(b)

          // 如果两个时间都是 Infinity，保持原有顺序
          if (aStartTime === Infinity && bStartTime === Infinity) return 0
          // 如果其中一个是 Infinity，将其排在后面
          if (aStartTime === Infinity) return 1
          if (bStartTime === Infinity) return -1
          // 正常比较时间
          return aStartTime - bStartTime
        })

        ganttParse.value.data = transformData(data.subPlans)
        ganttParse.value.links = data.links
        await nextTick()
        renderGantt()
        setupFilters() // 设置筛选器
      } catch (error) {
        console.error('获取甘特图数据失败:', error)
        ElementPlus.ElMessage.error('获取数据失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    // 切换甘特图的列
    const toggleGrid = () => {
      isCollapseGrid.value = !isCollapseGrid.value
      // 重新配置列,并且改变列的宽度
      gantt.config.columns = isCollapseGrid.value
        ? [{ name: 'text', label: 'ESN', tree: true, width: '*', resize: true }]
        : GANTT_CONFIG.columns
      gantt.config.grid_width = isCollapseGrid.value ? 200 : 800
      // 重新渲染
      gantt.render()
    }
    // 重置
    const reset = () => {
      formSearch.esn = ''
      formSearch.wo = ''
      formSearch.engine_type = ''
      formSearch.client = ''
      formSearch.level = ''
      dateValue.value = []
      applyFilters()
    }

    // 添加容器高度响应式变量
    const containerHeight = ref('100%')

    // 检查是否在 iframe 中
    const isInIframe = () => {
      try {
        return window !== window.top
      } catch (e) {
        return true
      }
    }

    // 计算并设置容器高度
    const updateContainerHeight = () => {
      try {
        if (isInIframe()) {
          // iframe 环境下的高度计算
          // 获取iframe的可视区域高度
          const iframeHeight = window.innerHeight || document.documentElement.clientHeight
          // 减去顶部搜索区域的高度(48px)和内边距(32px)
          containerHeight.value = `${iframeHeight - 130}px`
        } else {
          // 非 iframe 环境下的高度计算
          const viewportHeight = window.innerHeight
          containerHeight.value = `${viewportHeight - 130}px`
        }
      } catch (error) {
        console.error('更新容器高度失败:', error)
        containerHeight.value = '100%'
      }
    }

    // 添加防抖处理
    const debounce = (fn, delay) => {
      let timer = null
      return function (...args) {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          fn.apply(this, args)
        }, delay)
      }
    }

    // 使用防抖处理的updateContainerHeight
    const debouncedUpdateHeight = debounce(updateContainerHeight, 200)

    const scrollToToday = () => {
      const today = new Date()
      gantt.showDate(today)
    }

    onMounted(() => {
      initGanttConfig()
      getGanttData()
      updateContainerHeight() // 初始化时执行一次
      window.addEventListener('resize', debouncedUpdateHeight) // 使用防抖处理的函数
    })

    // 组件销毁时移除事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', debouncedUpdateHeight)
    })

    return {
      formSearch,
      dateValue,
      search: getGanttData,
      applyFilters,
      toggleGrid,
      gridButtonText,
      reset,
      loading,
      containerHeight,
      scrollToToday,
      ...dynamicColumn,
    }
  },
  template: /*html*/ `
    <div class="flex flex-col gap-4 p-4">
      <div class="flex items-center justify-between">
        <!-- 左侧搜索区域 -->
        <div class="flex items-center gap-2 flex-wrap">
          <label class="el-form-item__label text-right">ESN:</label>
          <el-input
            style="width:150px"
            v-model="formSearch.esn"
            placeholder="请输入ESN"
            clearable
            @input="applyFilters"
          ></el-input>
          
          <label class="el-form-item__label text-right">WO:</label>
          <el-input
            style="width:150px"
            v-model="formSearch.wo"
            placeholder="请输入WO"
            clearable
            @input="applyFilters"
          ></el-input>
          
          <label class="el-form-item__label text-right">发动机类型:</label>
          <el-input
            style="width:150px"
            v-model="formSearch.engine_type"
            placeholder="请输入发动机类型"
            clearable
            @input="applyFilters"
          ></el-input>
          
          <label class="el-form-item__label text-right">客户:</label>
          <el-input
            style="width:150px"
            v-model="formSearch.client"
            placeholder="请输入客户"
            clearable
            @input="applyFilters"
          ></el-input>
          
          <label class="el-form-item__label text-right">修理级别:</label>
          <el-input
            style="width:150px"
            v-model="formSearch.level"
            placeholder="请输入修理级别"
            clearable
            @input="applyFilters"
          ></el-input>
        </div>

        <!-- 右侧功能按钮区域 -->
        <div class="flex items-center gap-2">
          <el-button plain type="default" :loading="loading" @click="toggleGrid"> {{ gridButtonText }} </el-button>
          <el-button plain type="primary" :loading="loading" @click="scrollToToday">回到今天</el-button>
        </div>
      </div>
      
      <div class="flex items-center justify-between">
        <!-- 时间筛选区域 -->
        <div class="flex items-center gap-2">
          <label class="el-form-item__label text-right">release开始时间:</label>
          <el-date-picker
            v-model="dateValue[0]"
            type="date"
            placeholder="请选择release开始时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
          <label class="el-form-item__label text-right">release结束时间:</label>
          <el-date-picker
            v-model="dateValue[1]"
            type="date"
            placeholder="请选择release结束时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
          <el-button type="primary" :loading="loading" @click="search">搜索</el-button>
          <el-button type="info" :loading="loading" @click="reset">重置</el-button>
        </div>
        
        <div class="flex justify-end gap-2">
          <DynamicColumnConfigurator
            v-model="visibleColumns"
            :all-columns="columnConfig"
            button-text="列配置"
            button-icon="Grid"
          />
        </div>
      </div>

      <!-- 甘特图区域 -->
      <div v-loading="loading" class="relative" :style="{ height: containerHeight }">
        <div id="gantt-container" class="h-full w-full"></div>
      </div>
    </div>
  `,
}
