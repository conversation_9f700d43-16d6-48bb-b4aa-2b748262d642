<mxfile host="65bd71144e">
    <diagram id="2ROGvOKaCqkrMlAog4a_" name="第 1 页">
        <mxGraphModel dx="4610" dy="5291" grid="1" gridSize="14" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="3300" pageHeight="4681" math="1" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="" style="edgeStyle=none;html=1;fontStyle=1;fontSize=16;" parent="1" source="2" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="&lt;span&gt;F1 F4 生生产任务自动排班&lt;/span&gt;" style="ellipse;whiteSpace=wrap;html=1;fontStyle=1;fontSize=16;" parent="1" vertex="1">
                    <mxGeometry x="269" y="-3556" width="120" height="56" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="任务树管理&lt;div&gt;1、标记单元体完成&amp;nbsp;&lt;span style=&quot;background-color: transparent;&quot;&gt;2、取消某个单元体班次任务&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;3、给某个单元体添加自定义任务&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;4、调整单元体开工顺序 5、设置发动机开工顺序 6、GP计划下发，自动匹配RID单元体，生成任务树&lt;/span&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;align=left;fontSize=16;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="-266" y="-3380" width="1190" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;fontSize=16;fontStyle=1" parent="1" source="7" target="21" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="CFM&amp;nbsp;&lt;div&gt;F1-1 B1&lt;div&gt;分解&lt;/div&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;align=center;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="-266" y="-3304" width="70" height="168" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="&lt;div&gt;CFM&lt;/div&gt;&lt;div&gt;F4-1 B1&lt;/div&gt;&lt;div&gt;装配&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;align=center;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="-168" y="-3304" width="70" height="168" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="CFM&amp;nbsp;&lt;div&gt;F1-1 B23&lt;/div&gt;&lt;div&gt;分解&lt;br&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;align=left;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="450" y="-3304" width="70" height="168" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="&lt;div&gt;CFM&lt;/div&gt;&lt;div&gt;F4-1 B23&lt;/div&gt;&lt;div&gt;装配&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;align=left;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="588" y="-3304" width="70" height="168" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="LEAP&lt;div&gt;F1-1 &amp;amp;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;F4-1 B1&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;统一排序下发&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;align=left;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="-70" y="-3304" width="70" height="168" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="LEAP&lt;div&gt;F1-1 &amp;amp;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;F4-1 B23&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;统一排序下发&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;align=left;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="686" y="-3304" width="70" height="168" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="自动排班规则：&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;1、以二级任务 班次任务为主体排班&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;2、考虑任务需要什么能力&lt;/font&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;3、以team维度排班，例当该team中有上早班的人员，就可以在早班排任务 ，有晚班的人员，可以再分配一个任务&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;4、一个team一个班次， 只分派一个发动机一个任务&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;5、不考虑team中部分人员状态（请假、MES 等只要有一个人上班就可以排班）&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font&gt;&lt;font&gt;&lt;font&gt;&lt;font color=&quot;#000000&quot;&gt;6、&lt;/font&gt;&lt;font style=&quot;color: light-dark(rgb(226, 3, 3), rgb(237, 237, 237));&quot;&gt;是否考虑借调&lt;/font&gt;&lt;font color=&quot;#000000&quot;&gt;？（自动排班不考虑借调。借调是在排完任务后选择人员时考虑）&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=16;rounded=1;fontStyle=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="-294" y="-3108" width="308" height="294" as="geometry"/>
                </mxCell>
                <mxCell id="25" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.635;entryY=-0.015;entryDx=0;entryDy=0;entryPerimeter=0;fontSize=16;fontStyle=1" parent="1" source="13" target="21" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="自动排班规则：&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;1、以二级任务 班次任务为主体排班&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;2、考虑任务需要什么能力，暂时将sub team 的能力赋给人员。将来在人头上标记能力。&lt;/font&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;3、以排到人头上为目标&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;&lt;font color=&quot;#000000&quot;&gt;4、&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;/font&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;考虑人员状态（请假、MES 、借调（当前没实现）、培训、体检、出差等）&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;5、当需要多个人时（目前是需要多个Sub team）,优先同一team,同一班次，资源满足的人员（目前是sub team）&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;6、第5点不满足时，考虑同班次（早班或晚班）人员，从不同Team 拉取人员排班，不用借调&lt;/span&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=16;rounded=1;fontStyle=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="350" y="-3094" width="490" height="280" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="&lt;div style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;font&gt;下发&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;font&gt;1、按发动机排序下发，按顺序占用资源&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font&gt;2、保存班组计划&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font&gt;3、保存数据：&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font&gt;&amp;nbsp;PT_mian&lt;span style=&quot;color: rgb(63, 63, 63); background-color: transparent;&quot;&gt;维度&lt;/span&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;: wo+id_team+班次+group&amp;nbsp; ;&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;font&gt;&amp;nbsp;pt_team_plan&amp;nbsp; 关键字段： id_main + id_task +id_parent_task+班次+id_sub_teams(多个，隔开)&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;&lt;font&gt;&amp;nbsp; pt_team_plam_staff 关键字段：id_main+ id_team_plan+id_staff+人员类型（借调还是本组）&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="-294" y="-2786" width="1134" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="" style="edgeStyle=none;html=1;" parent="1" source="30" target="31" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="30" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="&lt;h2&gt;&lt;font style=&quot;color: rgb(0, 0, 255);&quot;&gt;调度页面&lt;/font&gt;&lt;/h2&gt;" style="rounded=1;whiteSpace=wrap;html=1;direction=south;fontSize=16;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="14" y="-2559" width="448" height="123" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="CFM F1-1 B1&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;CFM F1-1 B23&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;CFM F4-1 B1&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;CFM F4-1 B23&lt;br&gt;&lt;/font&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=16;rounded=1;fontStyle=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="-294" y="-2541" width="204" height="91" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="LEAP F1-1 B1&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;LEAP F1-1 B23&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;LEAP F4-1 B1&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;LEAP F4-1 B23&lt;br&gt;&lt;/font&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=16;rounded=1;fontStyle=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="636" y="-2558" width="204" height="118" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="1、新增（包含标准任务和自定义任务），如果选择的任务已排会更新，未排的任务新增&lt;div&gt;2、拖动，改变排班日期不改变资源(人或sub team)，拖动日期不能小于当前日期&lt;/div&gt;&lt;div&gt;3、编辑，可以更换资源（包含借调人员）&lt;/div&gt;&lt;div&gt;4、锁定，锁定后不能移动、编辑，重排不能重新分派资源&lt;/div&gt;&lt;div&gt;5、重排，从当前日期之后，当前发动机排序之后及自己，重新分派资源&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=16;rounded=1;fontStyle=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="-294" y="-2394" width="1120" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="&lt;h1 style=&quot;scrollbar-color: light-dark(#e2e2e2, #4b4b4b)&lt;br/&gt;&#x9;&#x9;&#x9;&#x9;&#x9;light-dark(#fbfbfb, var(--dark-panel-color));&quot;&gt;&lt;font style=&quot;scrollbar-color: light-dark(#e2e2e2, #4b4b4b)&lt;br/&gt;&#x9;&#x9;&#x9;&#x9;&#x9;light-dark(#fbfbfb, var(--dark-panel-color)); color: rgb(0, 0, 255);&quot;&gt;班组计划页面&lt;/font&gt;&lt;/h1&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="14" y="-2198" width="436" height="112" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="1、开工：以单元体维度，查询最新的交接数据，可能同一单元体同一时间班次存在多分交接。需要每份交接单，确认交接。&lt;div&gt;2、编辑：调整任务人员、借调人员&lt;/div&gt;&lt;div&gt;3、交接：&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; 1）交接数据存储维度：wo+sm+team+id_task+班次+时间（哪一天）&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; 2）考虑适配性&lt;/div&gt;&lt;div&gt;4、反馈：一个pt_main 反馈一次&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=16;rounded=1;fontStyle=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="-294" y="-1988" width="1120" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="40" value="F1-1 B1&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;F1-1 B23&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;F4-1 B1&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;F4-1 B23&lt;br&gt;&lt;/font&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=16;rounded=1;fontStyle=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="-274" y="-2184" width="204" height="98" as="geometry"/>
                </mxCell>
                <mxCell id="41" style="edgeStyle=none;html=1;entryX=1.009;entryY=0.366;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="37" target="40" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>