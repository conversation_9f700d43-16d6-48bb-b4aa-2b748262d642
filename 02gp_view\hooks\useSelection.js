const { unref } = Vue;

export function useSelection() {
  /**
   * 获取选中数据
   * @param dom 表格的dom
   * @param fn 获取选中数据的方法 -> 此方法返回的是一个数组
   * @return {*|null}
   */
  const hasSelectedData = (dom, fn) => {
    const selectedData = unref(dom)[fn]();
    // 判断selectedData是否是一个数组
    if (!Array.isArray(selectedData)) {
      ElementPlus.ElMessage.warning(`获取选中数据的方法 ${fn} 返回的不是一个数组`);
      return null;
    }
    if (selectedData.length === 0) {
      ElementPlus.ElMessage.warning('请选择数据');
      return null;
    }
    return selectedData;
  };

  /**
   * 获取选中一条数据
   * @param selected
   * @return {*|null}
   */
  const hasSelectedOneData = (selected) => {
    if (!selected) return null;
    if (selected.length > 1) {
      ElementPlus.ElMessage.warning('只能选择一条数据');
      return null;
    }
    return selected[0];
  };

  return {
    hasSelectedData,
    hasSelectedOneData,
  };
}
