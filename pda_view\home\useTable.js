import { post } from '../../config/axios/httpReuest.js';
import { useFilter } from '../hooks/useFilter.js';
import { useTableColumn } from './useTableColumn.js';

const { reactive, nextTick } = Vue;

export function useTable() {
  const { getAllFilter } = useFilter();
  const { getTableDataColumn } = useTableColumn();
  const { getTableDataColumnForP } = useTableColumn();
  const tableState = reactive({
    isLoading: true,
    data: [],
    total: 0,
  });

  // get table data
  const getTableData = async (searchForm) => {
    tableState.isLoading = true;
    const params = {
      ac: 'pda_dm_analysis',
      str_type: '',
      filter_fields: getAllFilter(searchForm),
    };
    const { data } = await post(params);
    if (data.code === 'success') {
      tableState.data = data.data;
      tableState.total = data.data.length;
      tableState.isLoading = false;
    } else {
      ElementPlus.ElMessage.error(data.text);
      tableState.isLoading = false;
    }
  };

  const tableDrawerState = reactive({
    isShowDrawer: false,
    drawerTableData: null,
    tableColumn: getTableDataColumn(),
    totalNum: 0,
  });
  const tableDrawerState1 = reactive({
    isShowDrawer: false,
    drawerTableData: null,
    tableColumn: getTableDataColumnForP(),
    totalNum: 0,
  });
  const handleCellClick = (row, column, searchForm) => {
    tableDrawerState.drawerTableData = null;
    tableDrawerState.totalNum = 0;
    tableDrawerState1.drawerTableData = null;
    tableDrawerState1.totalNum = 0;
    if (column.property === 'dbl_m_negative' || column.property === 'dbl_m_positive') {
      const filter_fields = getAllFilter(searchForm).filter((item) => item.str_key !== 'is_positive');
      filter_fields.push({ str_key: 'str_sm', str_value: row.str_sm });
      filter_fields.push({ str_key: 'str_wo', str_value: row.str_wo });
      const param = {
        ac: 'pda_dm_analysisList',
        str_type: '',
        str_class: column.property === 'dbl_m_negative' ? '1' : '0',
        filter_fields,
      };
      tableDrawerState.isShowDrawer = true;
      nextTick(() => {
        post(param).then((res) => {
          tableDrawerState.drawerTableData = res.data.data;
          tableDrawerState.totalNum = res.data.data.length;
        });
      });
    }else if(column.property === 'int_min_p') {
      const filter_fields = getAllFilter(searchForm)
      filter_fields.push({ str_key: 'str_sm', str_value: row.str_sm });
      filter_fields.push({ str_key: 'str_wo', str_value: row.str_wo });
      const param = {
        ac: 'pda_statisticalsitenegativeList',
        str_type: '',
        filter_fields,
      };
      tableDrawerState1.isShowDrawer = true;
      nextTick(() => {
        post(param).then((res) => {
          tableDrawerState1.drawerTableData = res.data.data;
          tableDrawerState1.totalNum = res.data.data.length;
        });
      });
    }
  };
  return {
    getTableData,
    tableState,
    tableDrawerState,
    tableDrawerState1,
    handleCellClick,
  };
}
