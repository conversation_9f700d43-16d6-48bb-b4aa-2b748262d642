const { ref, computed, defineComponent } = Vue
import { submitFeedback } from '../../../api/teams/index.js'
import TeamPlanSee from './team.plan.see.js'
import TeamPlan from './team.plan.js'
import TeamFeedback from './team.feedback.js'
import HandoverDrawer from './HandoverDrawer.js'

// 任务项组件
const TaskItem = defineComponent({
  name: 'TaskItem',
  components: {
    TeamPlanSee,
    TeamPlan,
    TeamFeedback,
    HandoverDrawer,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    handoverType: {
      type: String,
      required: true,
    },
    strFlow: {
      type: String,
      required: true,
    },
    strGroup: {
      type: String,
      required: true,
    },
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const { ElMessage } = ElementPlus
    const planItem = computed(() => {
      return props.row.plan.find((item) => item.plan_date === props.column.day)
    })

    // 获取team staff 中的staff_name 并拼接成字符串
    const getTeamStaffName = (teamstaff) => {
      return teamstaff?.map((item) => item.staff_name).join(',')
    }

    const handoverVisible = ref(false)

    // 打开交接弹窗
    const handleOpenHandover = (task) => {
      handoverVisible.value = true
    }

    const teamPlanSeeRef = ref(null)
    const teamPlanSeeVisible = ref(false)

    // 打开班组计划查看弹窗
    const handleOpenTeamPlanSee = (task) => {
      teamPlanSeeVisible.value = true
    }

    const teamPlanEditRef = ref(null)
    const teamPlanEditVisible = ref(false)

    // 打开班组计划编辑弹窗
    const handleOpenTeamPlanEdit = (task) => {
      teamPlanEditVisible.value = true
    }

    // 处理任务详情操作
    const handleOpenTaskDetail = (task) => {
      console.log('打开任务详情', task)
    }

    const teamFeedbackVisible = ref(false)
    const feedbackTask = ref(null)
    // 处理颜色选择
    const handleColorCommand = (command) => {
      teamFeedbackVisible.value = true
      feedbackTask.value = planItem.value
      feedbackTask.value.status = command
      feedbackTask.value.id_wo = props.row.id_wo
      feedbackTask.value.id_shift = props.row.id_shift
      feedbackTask.value.str_shift = props.row.shift_name
      feedbackTask.value.str_flow = props.strFlow
      feedbackTask.value.str_group = props.strGroup == 'B23' ? 'B2/3' : props.strGroup
      feedbackTask.value.handover_type = props.handoverType
    }

    const statusClass = computed(() => {
      const statusMap = {
        1: 'bg-green-500 text-white',
        '-1': 'bg-yellow-500 text-white',
        '-2': 'bg-red-500 text-white',
      }
      return statusMap[planItem.value.fedbackstatus] || ''
    })

    const handleRefresh = () => {
      emit('refresh')
    }

    const isHandover = computed(() => {
      const type = props.row.str_task_type?.toUpperCase() ?? ''
      return type === 'CCL' || type === 'NDT'
    })

    return {
      isHandover,
      planItem,
      getTeamStaffName,
      handleOpenHandover,
      handoverVisible,
      teamPlanSeeRef,
      teamPlanSeeVisible,
      handleOpenTeamPlanSee,
      teamPlanEditRef,
      teamPlanEditVisible,
      handleOpenTeamPlanEdit,
      handleOpenTaskDetail,
      handleColorCommand,
      handleRefresh,
      teamFeedbackVisible,
      feedbackTask,
      statusClass,
    }
  },
  template: /*html*/ `
    <div
      v-if="planItem.task.length > 0"
      class="h-full"
      :class="statusClass"
    >
      <!-- 任务内容区域 -->
      <div class="p-2 pb-10">
        <div
          class="flex flex-col rounded-md border border-gray-300 my-2"
          v-for="task in planItem.task"
          :key="task.id"
        >
          <div class="flex justify-between gap-2">
             <el-icon color="red" :size="20" v-if="task.is_abnormal"><WarningFilled /></el-icon> 
            <!-- 任务名称 太长 显示省略号 -->
            <div class="truncate font-semibold pl-2" :title="task.taskName">{{ task.taskName }}</div>
            <!-- 任务人员 -->
            <div class="truncate pr-2" :title="getTeamStaffName(task.staffs)">
              {{ getTeamStaffName(task.staffs) }}
            </div>
          </div>

          <!-- 底部按钮 - 使用boxBottom类 -->
          <div class="boxBottom">
            <div class="span" @click="handleOpenTeamPlanSee(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <View />
              </el-icon>
            </div>
            <div class="span" @click="handleOpenTeamPlanEdit(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <Edit />
              </el-icon>
            </div>
            <div v-if="isHandover" class="span" @click="handleOpenHandover(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <Folder />
              </el-icon>
            </div>
            <div class="span">
              <el-dropdown trigger="click" @command="(command) => handleColorCommand(command, task)">
                <el-icon class="cursor-pointer hover:text-blue-600" :size="18" color="#409EFF">
                  <More />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="-2" class="!text-red-500">红色</el-dropdown-item>
                    <el-dropdown-item command="-1" class="!text-yellow-500">黄色</el-dropdown-item>
                    <el-dropdown-item command="1" class="!text-green-500">绿色</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 交接弹窗 -->
    <HandoverDrawer
      v-if="handoverVisible"
      v-model:visible="handoverVisible"
      ref="handoverRef"
      :row="row"
      :column="column"
      :title="strFlow"
      :handover-type="handoverType"
    />
    <TeamPlanSee
      v-if="teamPlanSeeVisible"
      ref="teamPlanSeeRef"
      v-model:visible="teamPlanSeeVisible"
      :planItem="planItem"
      :current-row="row"
      :current-column="column"
    />
    <TeamPlan
      v-if="teamPlanEditVisible"
      ref="teamPlanEditRef"
      v-model:visible="teamPlanEditVisible"
      mode="edit"
      :planItem="planItem"
      :current-row="row"
      :current-column="column"
      @refresh="handleRefresh"
    />
    <TeamFeedback
      v-if="teamFeedbackVisible"
      v-model:visible="teamFeedbackVisible"
      :task="feedbackTask"
      :current-row="row"
      @refresh="handleRefresh"
    />
  `,
})

export default TaskItem
