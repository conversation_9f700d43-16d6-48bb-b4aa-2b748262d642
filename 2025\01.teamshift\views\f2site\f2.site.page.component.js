const { ref, reactive, onMounted, toRefs, defineComponent,computed} = Vue 
import {  queryF2Site,delF2Site,saveF2Site,queryTeam} from '../../api/f2/index.js'
import F2SiteEdit from './f2.site.page.component.edit.js'
// 主组件
export default defineComponent({
  name: 'F2SitePage',
  components: {
    F2SiteEdit
  },
  setup() {
    const state = reactive({
      areas: [],
      teams: [],
      //showDialog: false,
      currentTeam: {},
      teamOptions:[]
    })

      const currentTeam = reactive({
        id: null,
        name: '',
        position: '',
        members: 0
      })

      // 可用区域计算属性
      const availableAreas = computed(() => {
        return state.areas.filter(area => !area.teams)
      })

      // 颜色生成
      const getTeamColor = (index) => {
        return index>=0 ? `hsl(${index * 60}, 70%, 80%)` : '#eee'
      }

      // 打开新增对话框
      // const showAddDialog = () => {
      //   resetCurrentTeam()
      //   state.showDialog = true 
      // }

      // 编辑团队
      // const editTeam = (team) => {
      //   Object.assign(currentTeam, team)
      //   state.showDialog = true 
      // }

      const queryTeamOption = async ()=>{

        var res = await queryTeam();
        state.teamOptions = res.data??[]
      }

      // 删除团队
      const deleteTeam = (id) => {
        if (confirm('确定要删除这个团队吗？')) {
          var res = delF2Site(id);
          if(res.code !="success")
          {
            ElementPlus.ElMessage({
              type: 'error',
              message: res.text,
            })
          }else{
            ElementPlus.ElMessage({
              type: 'success',
              message: "删除成功",
            })
          }
          InitList()
        }
      }

      // // 提交表单
      // const submitForm = () => {
      //   if (state.isEditing) {
      //     // 更新逻辑
      //     const index = teams.value.findIndex(t => t.id === currentTeam.id)
      //     teams.value.splice(index, 1, {...currentTeam})
      //   } else {
      //     // 新增逻辑
      //     teams.value.push({
      //       ...currentTeam,
      //       id: Date.now()
      //     })
      //   } 
      //   cancelEdit()
      // }

// 取消编辑
// const cancelEdit = () => {
//   state.showDialog = false
//   resetCurrentTeam()
// }

// 重置表单
// const resetCurrentTeam = () => {
//   Object.assign(currentTeam, {
//     id: null,
//     name: '',
//     position: '',
//     members: 0
//   })
// }

 

const InitList = async() =>{

  const res = await queryF2Site();
  state.areas = res.areas || [];
  state.teams = res.teams || [];
}

const f2SiteEditVisible = ref(false)

// 打开班组计划查看弹窗
const handleOpenEdit = (task) => {
  f2SiteEditVisible.value = true
}


onMounted(() => {
   InitList();
   queryTeamOption();
})


    return { 
      state, 
      //resetCurrentTeam,
      //cancelEdit,
      currentTeam,
      availableAreas,
      getTeamColor,
      f2SiteEditVisible,
      handleOpenEdit,
      //showAddDialog,
      //editTeam,
      //submitForm,
      queryTeam,
      deleteTeam
    }
   },


  template: /*html*/ `
  <div class="container"> 
    
      <!-- 站位可视化区域 -->
      <div class="position-grid">
        <div 
          v-for="(area, index) in state.areas" 
          :key="index"
          class="position-area"
          :style="{ backgroundColor: getTeamColor(index) }"
          @click="selectArea(area)"
        >
          <div class="area-label">{{ area.str_site_code }}</div>
          <el-divider content-position="right">Team List</el-divider>
          <div  class="team-info">
          <el-descriptions :column="2" border>
          <el-descriptions-item v-for="(team, index) in area.teams" :label=" team.str_team_name"/> 
          </el-descriptions>
           
          </div>
        </div>
      </div>  

      <el-card>
      <template #header>
      <el-button type="primary" @click="handleOpenEdit" round>新增</el-button> 
  </template>
      <el-table :data="state.teams" style="width: 100%">
            <el-table-column type="index" width="50" label="Seq" min-width = "80" />
            <el-table-column prop="str_team_code" label="Team Code"  />
            <el-table-column prop="str_team_name" label="Team Name"  />
            <el-table-column prop="str_site_code" label="站点" />
            <el-table-column fixed="right" label="Operations" min-width="80">
              <template #default ="{ row }">
                <el-button link type="primary" size="small" @click = "handleOpenEdit">Edit</el-button>
                <el-button link type="primary" size="small" @click="deleteTeam(row.id)">Del</el-button>
              </template>
            </el-table-column>
      </el-table>  
  </el-card>
 </div>

 <F2SiteEdit v-if = "f2SiteEditVisible" :areas ="state.areas" v-model:visible="f2SiteEditVisible" ref="f2SiteEditRef"></F2SiteEdit>

  `,
})
