const { ref, onMounted } = Vue
const CreateAndEdit = {
  props: {
    isShowDrawer: Boolean,
    title: String,
    rowData: Object,
  },
  emits: ['update:isShowDrawer', 'update'],
  setup(props, { emit }) {
    const { useVModel } = VueUse
    const isShowDrawer = useVModel(props, 'isShowDrawer', emit)

    const tableData = ref([])
    const tableRef = ref(null)

    // 插入事件
    const insertEvent = async () => {
      const $table = tableRef.value
      if ($table) {
        const record = { taskName: '', sort: $table.getTableData().fullData.length + 1 }
        const { row: newRow } = await $table.insertAt(record)
        await $table.setEditCell(newRow, 'taskName')
      }
    }
    const deleteEvent = async (row) => {
      const $table = tableRef.value
      if ($table) {
        await $table.remove(row)
      }
    }

    // 更新行状态
    const updateRowStatus = (parmas) => {
      const $table = tableRef.value
      if ($table) {
        return $table.updateStatus(parmas)
      }
    }
    const tasksList = ref([])

    const getTasksList = () => {
      axios.post(globalApiUrl, {
        au: "ssamc",
        ap: "api2018",
        ak: "",
        ac: "pt_get_quick_schedul_task_list"
      }).then((res) => {
        tasksList.value = res.data.data.map((item) => {
          return {
            value: item.id_task,
            label: item.str_task_name+"("+item.str_flow+")",
            flow:item.str_flow
          }
        })
      })
    } 
    const handleChangeFlow = (flow) => {
      tasksList.value=tasksList.value.filter((item)=>item.flow == flow)
    }
    const getTemplateTaskList = () => {
      axios.post(globalApiUrl, {
        au: "ssamc",
        ap: "api2018",
        ak: "",
        ac: "pt_get_template_task_list",
        template_id:props.rowData.id
      }).then((res) => {
        if(res.data.data!=null && res.data.data.length>0){
          res.data.data.forEach((item) => {
            tableData.value.push({
              id:item.id,
              id_teamplate:item.id_teamplate,     
              sort:item.str_sort,      
              id_task: item.id_task.split(","),
              task_name: getSelectName(item.id_task.split(","))
            })
          })
        }
       
      })
    } 
    const getSelectName = (id_task) => {
     return tasksList.value.filter((item) => id_task.includes(item.value)).map((item) => item.label).join();
    }
    const handleChangeTask = (event, row) => {
      row.task_name = tasksList.value.filter((item) => event.value.includes(item.value)).map((item) => item.label).join();
    }
    // 保存
    const handleSave = () => {
      const $table = tableRef.value
      if(props.rowData.str_flow==null){
        ElementPlus.ElMessage.error('请选择排班Flow');
        return
      }
      if(props.rowData.str_type==null){
        ElementPlus.ElMessage.error('请选择模板类型');
        return
      }
      if ($table) {
        // 获取表格数据
        const tasks = $table.getTableData().fullData.map((item) => {
          return {
            id:item.id,
            id_teamplate:item.id_teamplate,     
            str_sort:item.sort,      
            id_task: item.id_task.join(','),
          }
        })
        axios.post(globalApiUrl, {
          au: "ssamc",
          ap: "api2018",
          ak: "",
          ac: "pt_save_quick_schedul_template",
          quick_schedul_template:{template:props.rowData,tasks:tasks}
        }).then((res) => {
          isShowDrawer.value = false
          // 给父组件传递数据
          emit('update')
        })
      
      }
    }
    Vue.onMounted(async () => {
     await getTasksList()
     await getTemplateTaskList()

    })
    return {
      isShowDrawer,
      tableData,
      tableRef,
      insertEvent,
      deleteEvent,
      updateRowStatus,
      handleSave,
      tasksList,
      getTasksList,
      getSelectName,
      getTemplateTaskList,
      handleChangeTask,
      handleChangeFlow
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="isShowDrawer" size="80%" :show-close="false" destory-on-close>
      <template #title>
        <div class="text-xl text-white">{{ title }}</div>
        <el-button type="danger" @click="isShowDrawer = false">Close</el-button>
      </template>
      <!-- 头部 -->
      <div class="border flex justify-center flex-col">
        <div class="text-xl my-2 mx-4">模板信息</div>
        <!-- 分割线 -->
        <div class="border-b mb-4"></div>
        <el-form class="mr-4" label-width="100px" :model="rowData">
          <el-form-item label="模板名称" prop="str_template_name">
            <el-input v-model="rowData.str_name" clearable placeholder="请输入模板名称"></el-input>
          </el-form-item>
          <el-form-item label="Flow" prop="str_flow">
            <el-select v-model="rowData.str_flow" clearable placeholder="Please select flow"  @change="handleChangeFlow">
              <el-option label="F1-1" value="F1-1"></el-option>
              <el-option label="F4-1" value="F4-1"></el-option>
             
            </el-select>
          </el-form-item>
          <el-form-item label="Type" prop="str_type">
            <el-select v-model="rowData.str_type" clearable placeholder="Please select type">
              <el-option label="Core" value="Core"></el-option>
              <el-option label="FAN" value="FAN"></el-option>
              <el-option label="LPT" value="LPT"></el-option>
              <el-option label="B1" value="B1"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <!-- 任务信息 -->
       <div class="border mt-4">
          <div class="flex justify-between my-2 mx-4">
            <div class="text-xl">任务信息</div>  
            <el-button text type="primary" @click="insertEvent">Add</el-button>
          </div>
          <!-- 分割线 -->
          <div class="border-b mb-4"></div>
          <vxe-table height="440" class="mx-2 mb-2" border show-overflow keep-source ref="tableRef" :data="tableData"
            :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
          >
           <!-- <vxe-column type="seq" width="60"></vxe-column> -->
           <vxe-column field="id_task" title="Task Name" :edit-render="{ autofocus: '.myinput'}">
           <template #default="{ row }">
            <!-- {{ row.id_task }} -->
            <span>{{ row.task_name }}</span>
          </template>
            <template #edit="{ row }">
              <!-- {{ row.id_task }} -->
              <!-- <vxe-input type="text" class="myinput" v-model="scope.row.taskName" @input="updateRowStatus(scope)"></vxe-input> -->
              <vxe-select class="myinput" v-model="row.id_task" multiple clearable placeholder="Please select" 
                filterable style="width: 80%"  @change="handleChangeTask($event, row)">
                <vxe-option v-for="item in tasksList" :key="item.value" :label="item.label"
                  :value="item.value"></vxe-option>
              </vxe-select>
            </template>
           </vxe-column>
           <vxe-column field="sort" title="Sort" :edit-render="{ autofocus: '.myinput'}">
            <template #edit="scope">
              <vxe-input type="text" class="myinput" v-model="scope.row.sort"></vxe-input>              
            </template>
           </vxe-column>
           <vxe-column title="操作" >
              <template #default="scope">
                <button type="primary" class="myinput" value="删除" @click="deleteEvent(scope.row)">删除</button> 
              </template>
           </vxe-column>
          </vxe-table>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleSave">Save</el-button>
        <el-button type="danger" @click="isShowDrawer = false">Cancel</el-button>
      </template>
    </el-drawer>
  `,
}

export default CreateAndEdit
