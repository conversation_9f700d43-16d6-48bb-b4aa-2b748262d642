import { post } from '../../utils/request.js'
/**获取小组班次日历 */
export function getEnum(key) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'common_get_sys_enum_list',
    key: key,
  })
}

/**
 * 获取当前登录用户信息
 * @returns {Promise<Object>} 用户信息，包含部门ID
 */
export const getCurrentUserInfo = async () => {
  const params = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'comm_user_info',
  }
  return post('/api/Do/DoAPI', params)
}

/**
 * 获取员工列表通过部门ID
 * @param {string} id_department - 部门ID
 * @returns {Promise<Array>} 员工列表
 */
export const getStaffList = async (params) => {
  const query = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_getfreestaffbydept',
    query: {
      ...params,
    },
  }
  return post('/api/Do/DoAPI', query)
}

/**
 * 获取公共的枚举列表
 * @param {string} key - 枚举类型
 * @returns {Promise<Array>} 枚举列表
 */
export const getCommonEnumList = async (key) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'common_get_sys_enum_list',
    key: key,
  })
}
