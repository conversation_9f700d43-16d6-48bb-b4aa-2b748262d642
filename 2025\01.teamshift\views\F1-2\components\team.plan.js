import { initEdit, queryTeamSecList, saveF12TeamPlan, queryF12TeamMember, initAdd } from '../api/index.js'
import { getShiftList } from '../../../api/calendar/index.js'

const { ElMessage } = ElementPlus

export default {
  name: 'TeamPlan',
  props: {
    planItem: {
      type: Object,
      default: null,
    },
    visible: Boolean,
    currentRow: {
      type: Object,
      default: () => ({}),
    },
    currentColumn: {
      type: Object,
      default: () => ({}),
    },
    mode: {
      type: String,
      default: 'add', // 'add' 或 'edit'
      validator: (value) => ['add', 'edit'].includes(value),
    },
  },
  emits: ['refresh', 'update:visible'],
  setup(props, { emit }) {
    const { reactive, onMounted, ref, computed } = Vue
    const { useVModel } = VueUse
    const teamPlanVisible = useVModel(props, 'visible', emit)

    // 判断是否为编辑模式
    const isEditMode = computed(() => props.mode === 'edit' && props.planItem)

    // 团队计划数据
    const planData = reactive({
      scheduleAdjust: '',
      isWholeMove: false,
      planItems: [],
    })

    // 计算计划项数量
    const planItemsCount = computed({
      get() {
        return planData.planItems.length
      },
      set(value) {
        planData.planItems.length = value
      },
    })

    // 添加表单引用
    const formRef = ref(null)

    // 验证规则配置
    const formRules = reactive({
      str_type: [{ required: true, message: '请选择类型', trigger: 'change' }],
      shift_start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
      shift_end_time: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
    })

    // 班次表格数据
    const tableDataShifts = ref([])

    // 初始化数据（新增模式使用）
    const initData = ref({})

    // 保存计划
    const handleSave = async () => {
      try {
        // 触发表单验证
        await formRef.value.validate()

        // 验证班次表格数据
        const validationErrors = []

        tableDataShifts.value.forEach((shift, index) => {
          if (!shift.id_shift) {
            validationErrors.push(`第${index + 1}行班次不能为空`)
          }
          if (!shift.team_staffs || shift.team_staffs.length === 0) {
            validationErrors.push(`第${index + 1}行组员不能为空`)
          }
        })

        if (validationErrors.length > 0) {
          ElMessage.error(validationErrors.join('；'))
          return
        }

        const params = planData.planItems.map((item) => ({
          id: isEditMode.value ? item.id : "0",
          id_f12_allocate: isEditMode.value ? item.id_f12_allocate : props.currentRow.id_main,
          dt_shift: isEditMode.value ? item.dt_shift : props.currentColumn.day,
          id_team: props.currentRow.id_team,
          str_notes: item.str_remark || '',
          dt_begin: beginDate.value,
          dt_end: endDate.value,
          shifts: tableDataShifts.value.map((staff) => ({
            id_shift: staff.id_shift,
            staffs: staff.team_staffs.map((staffId) => ({
              id_staff: staffId,
              id_shift: staff.id_shift,
            })),
            teamSecs: staff.team_secs.map((secId) => ({
              id_staff: secId,
              id_shift: staff.id_shift,
            })),
          })),
        }))

        await saveF12TeamPlan(params)
        teamPlanVisible.value = false
        emit('refresh')
        ElMessage.success('保存成功')
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    }

    const teamStaffs = ref([])
    // 获取团队人员
    const getTeamStaff = async () => {
      const params = {
        id_team: props.currentRow.id_team,
        dt_shift: isEditMode.value ? props.planItem.plan_date : props.currentColumn.day,
      }
      const res = await queryF12TeamMember(params)
      teamStaffs.value = res ?? []
    }

    // 加载班次选项
    const shiftOptionList = ref([])
    const loadShiftOptions = async () => {
      try {
        const shiftRes = await getShiftList()
        shiftOptionList.value = shiftRes.data
        return shiftRes.data
      } catch (error) {
        if (!isEditMode.value) {
          ElMessage.error('加载班次选项失败')
        }
        console.error('加载班次选项失败:', error)
        return []
      }
    }

    // 获取编辑页的数据
    const getTeamPlanEditList = async () => {
      try {
        const ids = props.planItem.task.map((item) => item.id)
        const res = await initEdit(ids)
        if (!res) return

        planData.planItems = res.map((item) => ({
          id: item.id,
          str_type: item.str_type,
          str_special_work: item.str_special_work,
          id_f12_allocate: item.id_f12_allocate,
          dt_shift: item.dt_shift,
          id_team: item.id_team,
          str_remark: item.str_notes,
        }))
        beginDate.value = res[0].dt_begin
        endDate.value = res[0].dt_end

        // 初始化班次表格数据
        if (res.length > 0 && res[0].shifts) {
          tableDataShifts.value = res[0].shifts.map((task) => ({
            id_shift: task.id_shift,
            team_staffs: task.staffs?.map((staff) => staff.id_staff) || [],
            team_secs: task.teamSecs?.map((sec) => sec.id_staff) || [],
          }))
        } else {
          tableDataShifts.value = [
            {
              id_shift: '',
              team_staffs: [],
              team_secs: [],
            },
          ]
        }
      } catch (error) {
        console.error('获取团队计划编辑数据失败:', error)
      }
    }

    const beginDate = ref(null)
    const endDate = ref(null)

    // 初始化新增数据
    const initAddData = async () => {
      try {
        const params = {
          id_team: props.currentRow.id_team,
          dt_shift: props.currentColumn.day,
          id_main: props.currentRow.id_main,
        }
        const res = await initAdd(params)
        initData.value = res ?? {}

        beginDate.value = res.dt_begin
        endDate.value = res.dt_end

        planData.planItems = [
          {
            str_type: res.str_type,
            str_special_work: res.str_special_work,
            str_remark: '',
            dt_shift: res.dt_shift,
          },
        ]

        // 初始化空的班次表格
        tableDataShifts.value = [
          {
            id_shift: '',
            team_staffs: [],
            team_secs: [],
          },
        ]
      } catch (error) {
        console.error('初始化新增数据失败:', error)
      }
    }

    // 增加任务
    const addTaskPlan = () => {
      if (isEditMode.value) {
        planData.planItems.push({
          str_type: '',
          str_special_work: '',
          str_remark: '',
        })
      } else {
        planData.planItems.push({
          str_type: initData.value.str_type,
          str_special_work: initData.value.str_special_work,
          str_remark: '',
        })
      }
    }

    // 删除任务
    const delTaskPlan = (task, index) => {
      planData.planItems.splice(index, 1)
    }

    // 添加班次
    const addShift = () => {
      tableDataShifts.value.push({
        id_shift: '',
        team_staffs: [],
        team_secs: [],
      })
    }

    // 删除班次
    const delShift = (index) => {
      tableDataShifts.value.splice(index, 1)
    }

    const teamSecOptions = ref([])
    const getTeamSecOptions = async () => {
      const params = {
        id_team: props.currentRow.id_team,
        pt_dt: isEditMode.value ? props.planItem.plan_date : props.currentColumn.day,
      }
      const res = await queryTeamSecList(params)
      teamSecOptions.value = res ?? []
    }

    // 人员选择变化
    const handleStaffChange = (val) => {
      if (val && val.length > 0) {
        const warningStaffs = teamStaffs.value
          .filter((staff) => val.includes(staff.id))
          .filter((staff) => staff.cl_status === 1)
        if (warningStaffs.length > 0) {
          ElMessage.warning(
            `${warningStaffs.map((staff) => staff.str_name).join('、')} 已经上班${warningStaffs.map((staff) => staff.dbl_hour).join('、')}H`,
          )
        }
      }
    }

    // 借调人员选择变化处理
    const handleTeamSecChange = (val) => {
      if (val && val.length > 0) {
        const warningStaffs = teamSecOptions.value
          .filter((staff) => val.includes(staff.id))
          .filter((staff) => staff.cl_status === 1)
        if (warningStaffs.length > 0) {
          ElMessage.warning(
            `${warningStaffs.map((staff) => staff.str_name).join('、')} 已经上班${warningStaffs.map((staff) => staff.dbl_hour).join('、')}H`,
          )
        }
      }
    }

    // 班次变化处理
    const handleShiftChange = (val, index) => {
      tableDataShifts.value[index].team_staffs = []
      tableDataShifts.value[index].team_secs = []
      if (val) {
        teamStaffs.value.forEach((staff) => {
          staff.isDisabled = staff.id_shift !== val
        })
        teamSecOptions.value.forEach((staff) => {
          staff.isDisabled = staff.id_shift !== val
        })
      }
    }

    // 编辑模式的焦点处理函数
    const handleTeamSecFocus = (id_shift) => {
      teamSecOptions.value.forEach((staff) => {
        staff.isDisabled = staff.id_shift !== id_shift
      })
    }

    const handleTeamStaffFocus = (id_shift) => {
      teamStaffs.value.forEach((staff) => {
        staff.isDisabled = staff.id_shift !== id_shift
      })
    }

    const isDisabledDate = (date) => {
      return !(moment(date).isBetween(beginDate.value, endDate.value, 'day', '[]'))
    }

    const handleDateChange = (val, index) => {
      // 初始化班次表格
      tableDataShifts.value = [
        {
          id_shift: '',
          team_staffs: [],
          team_secs: [],
        },
      ]
    }

    // 初始化数据
    const initializeData = async () => {
      await getTeamSecOptions()
      await getTeamStaff()
      await loadShiftOptions()

      if (isEditMode.value) {
        await getTeamPlanEditList()
      } else {
        await initAddData()
      }
    }

    onMounted(initializeData)

    return {
      formRef,
      formRules,
      teamStaffs,
      teamSecOptions,
      teamPlanVisible,
      planData,
      planItemsCount,
      isEditMode,
      handleSave,
      addTaskPlan,
      delTaskPlan,
      handleStaffChange,
      handleTeamSecChange,
      tableDataShifts,
      shiftOptionList,
      addShift,
      delShift,
      handleShiftChange,
      handleTeamSecFocus,
      handleTeamStaffFocus,
      handleDateChange,
      isDisabledDate,
    }
  },
  template: /*html*/ `
    <el-dialog
      v-model="teamPlanVisible"
      :title="isEditMode ? 'Team Plan Edit' : 'Team Plan Add'"
      width="80%"
      class="common-dialog"
      :fullscreen="false"
      :append-to-body="true"
    >
      <div class="pr-2">
        <el-form :model="planData" label-width="120px" ref="formRef" :rules="formRules">
          <!-- 计划项列表 -->
          <div
            v-for="(item, index) in planData.planItems"
            :key="index"
            class="my-4 rounded-md border border-gray-200 p-4"
          >
            <!-- 表单字段 -->
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              <!-- Type -->
              <el-form-item
                label="Type"
                required
                :prop="'planItems.' + index + '.str_type'"
                :rules="formRules.str_type"
              >
                <el-input v-model="item.str_type" placeholder="Input Type" disabled />
              </el-form-item>

              <!-- 任务描述 -->
              <el-form-item label="特殊工作" :prop="'planItems.' + index + '.str_special_work'">
                <el-input v-model="item.str_special_work" placeholder="特殊工作描述" disabled />
              </el-form-item>

              <!-- 日期 -->
              <el-form-item label="日期" :prop="'planItems.' + index + '.dt_shift'">
                <el-date-picker
                  v-model="item.dt_shift"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择日期"
                  :disabled="!isEditMode"
                  :disabled-date="isDisabledDate"
                  @change="handleDateChange(item.dt_shift, index)"
                />
              </el-form-item>
            </div>

            <!-- Remark -->
            <el-form-item label="Remark" class="mt-4">
              <el-input v-model="item.str_remark" type="textarea" :rows="3" placeholder="请输入备注" />
            </el-form-item>

            <!-- 班次表格 -->
            <el-form-item>
              <el-card class="w-full">
                <template #header>
                  <div class="card-header">
                    <el-button type="primary" @click="addShift">Add（新增不同班次排班）</el-button>
                  </div>
                </template>

                <el-table :data="tableDataShifts" :max-height="220">
                  <el-table-column prop="id_shift" label="Shift 班次">
                    <template #default="scope">
                      <el-select 
                        v-model="scope.row.id_shift" 
                        filterable 
                        clearable 
                        placeholder="Select Shift" 
                        class="w-full"
                        @change="handleShiftChange(scope.row.id_shift, scope.$index)"
                      >
                        <el-option 
                          v-for="shift in shiftOptionList" 
                          :key="shift.id" 
                          :label="shift.str_name" 
                          :value="shift.id" 
                        />
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column prop="team_staffs" label="Team 组员">
                    <template #default="scope">
                      <el-select 
                        v-model="scope.row.team_staffs" 
                        filterable 
                        clearable 
                        multiple
                        placeholder="Select Team Staff" 
                        @change="handleStaffChange" 
                        @focus="handleTeamStaffFocus(scope.row.id_shift)"
                        class="w-full"
                      >
                        <el-option 
                          v-for="staff in teamStaffs" 
                          :key="staff.id" 
                          :label="staff.str_name" 
                          :value="staff.id"
                          :disabled="staff.cl_status === 2 || staff.isDisabled"
                        >
                          <div :class="{'text-yellow-500': staff.cl_status === 1, 'text-red-500': staff.cl_status === 2}">
                            <span>{{ staff.str_name }}({{ staff.dbl_hour }}H) - {{ staff.str_shift}}</span>
                          </div>
                        </el-option>
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column prop="team_secs" label="Team Sec 借调人员">
                    <template #default="scope">
                      <el-select 
                        v-model="scope.row.team_secs" 
                        multiple 
                        filterable 
                        placeholder="select team sec staff"
                        @change="handleTeamSecChange" 
                        @focus="handleTeamSecFocus(scope.row.id_shift)"
                        class="w-full"
                      >
                        <el-option 
                          v-for="staff in teamSecOptions" 
                          :key="staff.id" 
                          :label="staff.str_name" 
                          :value="staff.id"
                          :disabled="staff.cl_status === 2 || staff.isDisabled"
                        >
                          <span :class="{'text-yellow-500': staff.cl_status === 1, 'text-red-500': staff.cl_status === 2}">
                            {{ staff.str_name }}({{ staff.dbl_hour }}H)
                          </span>
                        </el-option>
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column fixed="right" label="" min-width="120">
                    <template #default="scope">
                      <el-button
                        v-if="tableDataShifts.length > 1"
                        link
                        type="danger"
                        @click.prevent="delShift(scope.$index)"
                      >
                        del
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="teamPlanVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>
  `,
}
