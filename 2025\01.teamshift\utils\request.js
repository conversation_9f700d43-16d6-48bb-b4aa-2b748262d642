import apiConfig from '../config/api.config.js'
const { ElMessage } = ElementPlus

// 基础配置
const service = axios.create({
  baseURL: apiConfig.baseURL, // 环境变量配置
  timeout: 0, // 请求超时时间
  withCredentials: false, // 跨域请求携带cookie
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 设置Content-Type
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json'

    // 记录请求开始时间（用于接口耗时计算）
    config.metadata = { startTime: new Date() }

    // 添加请求标识（用于取消请求）
    config.cancelToken = new axios.CancelToken((cancel) => {
      pendingMap.set(config.url, cancel)
    })

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 计算请求耗时
    const endTime = new Date()
    const duration = endTime - response.config.metadata.startTime

    // 移除pending中的请求
    pendingMap.delete(response.config.url)

    // 处理二进制数据
    if (response.request.responseType === 'blob') {
      return response.data
    }

    // 根据后端约定处理响应结构
    const { code, data, text } = response.data
    if (code === 'success') {
      return data
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: (text && text.replace(/(\n|\r|\r\n|↵)/g, '<br/>') || 'Error') || '请求失败',
        type: 'error',
      })
      // ElMessage.error(text.replace(/(\n|\r|\r\n|↵)/g, '<br/>') || 'Error')
      return Promise.reject(new Error(text || 'Error'))
    }
  },
  (error) => {
    // 统一错误处理
    if (axios.isCancel(error)) {
      console.log('请求被取消：', error.message)
      return Promise.reject(new Error('请求已取消'))
    }

    let errorMessage = '请求失败'
    if (error.response) {
      // 服务器返回非常规2xx状态码
      switch (error.response.status) {
        case 401:
          errorMessage = '身份认证失败，请重新登录'
          // 跳转登录页
          break
        case 403:
          errorMessage = '没有操作权限'
          break
        case 404:
          errorMessage = '请求资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请检查网络'
      } else {
        errorMessage = '网络异常，请检查网络连接'
      }
    }

    // 显示错误提示
    console.error(errorMessage)
    return Promise.reject(error)
  },
)

// 请求取消相关
const pendingMap = new Map()

/**
 * 取消所有pending中的请求
 */
export function cancelAllPending() {
  pendingMap.forEach((cancel) => cancel('强制取消请求'))
  pendingMap.clear()
}

/**
 * 取消指定请求
 * @param {string} url 要取消的请求URL
 */
export function cancelPending(url) {
  if (pendingMap.has(url)) {
    pendingMap.get(url)('取消重复请求')
    pendingMap.delete(url)
  }
}

// 封装请求方法
export function get(url, params, config = {}) {
  return service({
    url,
    method: 'get',
    params,
    ...config,
  })
}

export function post(url, data, config = {}) {
  return service({
    url,
    method: 'post',
    data,
    ...config,
  })
}

export function put(url, data, config = {}) {
  return service({
    url,
    method: 'put',
    data,
    ...config,
  })
}

export function del(url, params, config = {}) {
  return service({
    url,
    method: 'delete',
    params,
    ...config,
  })
}

// 全局loading控制
let loadingCount = 0
let loadingInstance = null

const showLoading = () => {
  if (loadingCount === 0) {
    // 这里需要根据实际UI库实现，示例使用console
    loadingInstance = console.log('Loading开始')
  }
  loadingCount++
}

const hideLoading = () => {
  loadingCount--
  if (loadingCount <= 0) {
    loadingInstance = null
    console.log('Loading结束')
  }
}

// 请求重试配置
const retryConfig = {
  retries: 3, // 重试次数
  retryDelay: 1000, // 重试间隔
  shouldRetry: (error) => error.response?.status >= 500 || error.code === 'ECONNABORTED',
}

// 带重试的请求封装
export function requestWithRetry(config) {
  return new Promise((resolve, reject) => {
    const attempt = async (retryCount) => {
      try {
        const result = await service(config)
        resolve(result)
      } catch (error) {
        if (retryCount > 0 && retryConfig.shouldRetry(error)) {
          setTimeout(() => {
            attempt(retryCount - 1)
          }, retryConfig.retryDelay)
        } else {
          reject(error)
        }
      }
    }
    attempt(retryConfig.retries)
  })
}

// 类型定义（JSDoc）
/**
 * @typedef {Object} RequestConfig
 * @property {'get' | 'post' | 'put' | 'delete'} [method]
 * @property {Object} [params]
 * @property {Object} [data]
 * @property {Object} [headers]
 */

export default service
