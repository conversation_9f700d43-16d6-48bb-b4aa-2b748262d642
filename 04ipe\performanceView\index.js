import { post } from '../../config/axios/httpReuest.js'
import { useDownloadAndUpload } from '../hooks/useDownloadAndUpload.js'

/**
 * @description 绩效导入
 * <AUTHOR>
 */
const { ref } = Vue
const { genFileId } = ElementPlus
const PerformanceView = {
  setup() {
    const upload = ref(null)
    const fileList = ref([])
    const handleExceed = (files) => {
      upload.value?.clearFiles()
      const file = files[0]
      file.uid = genFileId()
      upload.value?.handleStart(file)
    }
    const { downloadTemplate, uploadXlsx } = useDownloadAndUpload()
    const map = {
      机型: 'str_engine_type',
      维修类型: 'str_repair_type',
      最小值: 'int_min',
      最大值: 'int_max',
      得分: 'dec_score',
    }
    const submitUpload = () => {
      uploadXlsx(upload, fileList, map, 'pe_qty_config_import')
    }

    // * 下载模板
    const handleDownloadTemplate = () => {
      // 使用XLXS.js生成模板
      const data = [
        ['机型', '维修类型', '最小值', '最大值', '得分'],
        ['CFM56', 'OH', 0, 999999, 1],
        ['LEAP', 'SV', 1, 30, 1],
      ]
      downloadTemplate(data, '发动机QTY导入模板.xlsx')
    }

    return {
      upload,
      fileList,
      handleExceed,
      submitUpload,
      handleDownloadTemplate,
    }
  },
  template: /*html*/ `
    <el-upload
      ref="upload"
      class="m-3"
      v-model:file-list="fileList"
      :limit="1"
      :on-exceed="handleExceed"
      :auto-upload="false"
      accpet=".xls,.xlsx"
    >
      <template #trigger>
        <el-button type="primary">select file</el-button>
      </template>  
      <el-button class="ml-3" type="success" @click="submitUpload">upload to server</el-button>
      <el-button class="ml-3" type="warning" @click="handleDownloadTemplate">download template</el-button>
      <template #tip>
        <div class="text-sm italic">only support xls/xlsx file</div>
      </template>
    </el-upload>
  `,
}

export default PerformanceView
