export default {
  name: 'FileUploadButtonComponent',
  props: {
    row: {
      type: Object,
      required: true,
    },
    hasFiles: {
      type: Boolean,
      default: false,
    }
  },
  emits: ['upload', 'view'],
  setup(props, { emit }) {
    // 处理上传事件
    const handleUpload = () => {
      emit('upload', props.row)
    }
    
    // 处理查看事件
    const handleView = () => {
      emit('view', props.row)
    }
    
    return {
      handleUpload,
      handleView,
    }
  },
  template: /*html*/ `
    <div class="flex items-center justify-center space-x-2">
      <el-tooltip content="上传附件" placement="top">
        <el-button type="primary" link @click="handleUpload">
          <el-icon><Upload /></el-icon>
        </el-button>
      </el-tooltip>
      <el-button
        v-if="hasFiles"
        type="primary"
        link
        @click="handleView"
      >
        <el-icon><View /></el-icon>
      </el-button>
    </div>
  `,
} 