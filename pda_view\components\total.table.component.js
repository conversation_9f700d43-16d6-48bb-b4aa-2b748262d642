import { post } from '../../config/axios/httpReuest.js'
import { currentDateKey, currentNodeKey, currentTypeViewKey, searchFormKey } from '../../config/keys.js'
import { DM_VIEW, PDA_VIEW_P } from '../../config/tabPaneKey.js'
import { useTable } from '../hooks/useTable.js'
import { useBusinessJump } from '../hooks/useBusinessJump.js'

const { ref, inject, unref, onMounted, computed, reactive, toRaw, nextTick } = Vue
const { useElementVisibility } = VueUse
export default {
  name: 'TotalTableComponent',
  props: {
    type: {
      type: String,
      default: 'Total-m',
    },
    date: {
      type: String,
      default: '',
      required: false,
    },
  },
  setup(props, { emit, attrs }) {
    const { currentDate } = inject(currentDateKey)
    const currentNode = inject(currentNodeKey)
    const { currentTypeView } = inject(currentTypeViewKey, { currentTypeView: ref(PDA_VIEW_P) })
    // 获取DM的过滤条件
    const { searchForm } = inject(searchFormKey)

    // 获取EDD的过滤条件
    const { searchEddForm } = inject('searchEddForm', { searchEddForm: reactive({}) })

    // dom元素
    const target = ref(null)
    // 是否显示表格
    const visibility = ref(false)

    const {
      xTable,
      columnsConfig,
      tableData,
      columnData,
      tableHeight,
      getTableData,
      filterMethod,
      totalNum,
      filterChange,
      filterRecoverMethod,
      exportDataEvent,
      getColumnData,
      handleRowClassName,
    } = useTable(true)
    onMounted(async () => {
      getColumnData(currentNode)
      let filterSearch = null
      if (currentNode === '10' && props.type === 'dm_task_p') {
        filterSearch = searchEddForm
      } else {
        filterSearch = isShowOperation.value ? attrs.searchForm : searchForm
      }
      await getTableData(
        currentNode,
        props.date,
        currentDate.value,
        props.type,
        unref(currentTypeView),
        attrs.d,
        attrs.m,
        filterSearch,
      )
      visibility.value = useElementVisibility(target)
    })

    // is show operation column
    const isShowOperation = computed(() => {
      return unref(currentTypeView) === DM_VIEW
    })

    const exchangeState = reactive({
      isShowDialog: false,
    })

    // handle exchange
    const handleExchange = row => { }

    const addExchangeState = reactive({
      isShowDialog: false,
      form: {
        str_wo: '',
        str_pn: '',
        dbl_num: 0,
        str_need_reason: '',
        str_need_remark: '',
      },
    })
    // qty initial value
    const qtyInitialValue = ref(0)
    // handle add exchange
    const handleAddExchange = row => {
      const rawRow = toRaw(row)
      addExchangeState.form.str_wo = rawRow.str_wo
      addExchangeState.form.str_pn = rawRow.str_pn
      addExchangeState.form.dbl_num = rawRow.int_num
      qtyInitialValue.value = rawRow.int_num
      addExchangeState.form.id_wo = rawRow.id_wo
      addExchangeState.form.id_pda = rawRow.id
      addExchangeState.form.str_need_reason = rawRow.str_need_reason
      addExchangeState.form.str_need_remark = rawRow.str_need_remark
      addExchangeState.isShowDialog = true
    }
    // save add exchange
    const saveAddExchangeState = async () => {
      const params = {
        ac: 'se_pda_main_create_or_update',
        request: {
          id_pda: addExchangeState.form.id_pda,
          str_pda_site: currentNode,
          str_need_remark: addExchangeState.form.str_need_remark,
          str_need_reason: addExchangeState.form.str_need_reason,
          partList: [
            {
              dbl_num: addExchangeState.form.dbl_num,
              str_pn: addExchangeState.form.str_pn,
              str_sn: '',
              id_wo: addExchangeState.form.id_wo,
              str_wo: addExchangeState.form.str_wo,
            },
          ],
        },
      }
      if (!addExchangeState.form.str_need_reason) {
        ElementPlus.ElMessage.error('请选择串件原因')
        return
      }
      await post(params)
      ElementPlus.ElMessage.success('串件申请已提交')
      addExchangeState.isShowDialog = false
      // refresh table
      refreshTable(addExchangeState.form.id_pda)
    }

    const refreshTable = id => {
      const $table = xTable.value
      const rowData = tableData.value.find(item => item.id === id)
      rowData.colortype = 1
      nextTick(() => {
        $table.loadData(XEUtils.clone(tableData.value, true))
        $table.recalculate(true)
      })
    }
    const { skipSelectEvent } = useBusinessJump()

    // skip select event click
    const skipSelectEventClick = () => {
      skipSelectEvent(xTable, currentNode)
    }
    return {
      target,
      xTable,
      columnsConfig,
      tableData,
      columnData,
      visibility,
      tableHeight,
      filterMethod,
      filterChange,
      totalNum,
      filterRecoverMethod,
      exportDataEvent,
      handleRowClassName,
      isShowOperation,
      handleExchange,
      exchangeState,
      handleAddExchange,
      addExchangeState,
      saveAddExchangeState,
      qtyInitialValue,
      skipSelectEventClick,
    }
  },
  template: /*html*/ `
    <vxe-toolbar>
      <template #buttons>
        <el-button title="下载" type="primary" circle @click="exportDataEvent">
          <el-icon>
            <Download></Download>
          </el-icon>
        </el-button>
        <el-button title="跳转" type="primary" circle @click="skipSelectEventClick">
          <el-icon>
            <Position></Position>
          </el-icon>
        </el-button>
      </template>
      <template #tools>
        <span class="text-xl">Total: {{ totalNum ?? 0 }} </span>
      </template>
    </vxe-toolbar>
    <div class="mb-2 border-b-2"></div>
    <div ref="target">
      <vxe-table
        v-if="visibility"
        ref="xTable"
        :columns-config="columnsConfig"
        :row-config="{isHover: true, isCurrent: true}"
        :show-overflow="true"
        :data="tableData"
        border
        :show-header="true"
        :height="tableHeight"
        :row-class-name="handleRowClassName"
        @filter-change="filterChange"
       :scroll-y="{enabled: true, gt: 500}"
       :scroll-x="{enabled: true, gt: 500}"
      >
        <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
        <vxe-table-column fixed="left" type="seq" width="60"></vxe-table-column>
        <vxe-column
          v-for="item in columnData"
          :key="item.id"
          :field="item.prop"
          :filter-method="filterMethod"
          :filter-reset-method="filterRecoverMethod"
          :filters="item.field"
          :fixed="item.fixed"
          :title="item.label"
          :visible="item.showColumn"
          :min-width="item.minWidth"
          show-overflow="title"
          sortable
        >
          <!--筛选插槽-->
          <template v-if="item.is_select == 'input'" #filter="{ $panel, column }">
            <div v-for="(option, index) in column.filters" :key="index">
              <el-input
                v-model="option.value"
                placeholder="按回车确认筛选"
                @input="$panel.changeOption($event, !!option.value, option)"
                @keyup.enter="$panel.confirmFilter(option.field = item.prop)"
              ></el-input>
            </div>
          </template>
          <!--单元格显示插槽-->
          <template #default="{ row }">
            <div v-if="item.prop === 'is_out' || item.prop === 'is_sp' || item.prop === 'is_aog'">
              <span v-if="row[item.prop] === 1">是</span>
              <span v-else>否</span>
            </div>
            <div v-else>
              <span>{{ row[item.prop] }}</span>
            </div>
          </template>
        </vxe-column>
        <vxe-column
          field="action"
          title="操作"
          fixed="right"
          width="300"
          align="center"
          show-overflow="title"
          :visible="isShowOperation"
        >
          <template #default="{ row }">
            <el-button text type="primary" v-if="row.colortype === 1" @click="handleExchange(row)">Exchange </el-button>
            <el-button text type="primary" v-else-if="row.int_d < 0 || row.int_m < 0" @click="handleAddExchange(row)">
              Add exchange
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
      <el-skeleton v-else :rows="10" animated></el-skeleton>
    </div>
    <!--    chuang jian shen qing -->
    <el-dialog
      v-model="addExchangeState.isShowDialog"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <template #title>
        <div class="">串件申请</div>
      </template>
      <el-form :model="addExchangeState.form" label-width="120px">
        <el-form-item label="WO">
          <el-input v-model="addExchangeState.form.str_wo" disabled></el-input>
        </el-form-item>
        <el-form-item label="PN">
          <el-input v-model="addExchangeState.form.str_pn" disabled></el-input>
        </el-form-item>
        <el-form-item label="QTY">
          <el-input-number v-model="addExchangeState.form.dbl_num" :min="1" :max="qtyInitialValue"></el-input-number>
        </el-form-item>
        <el-form-item label="串件原因">
          <el-select v-model="addExchangeState.form.str_need_reason" clearable>
            <el-option label="受体零件转包" value="1"></el-option>
            <el-option label="受体零件内修" value="2"></el-option>
            <el-option label="受体零件报废" value="3"></el-option>
            <el-option label="受体零件被串走" value="4"></el-option>
            <el-option label="受体零件排故" value="5"></el-option>
            <el-option label="受体零件平衡需求" value="6"></el-option>
            <el-option label="受体零件系统无记录" value="7"></el-option>
            <el-option label="受体零件失踪" value="8"></el-option>
            <el-option label="受体零件间隙调整" value="9"></el-option>
            <el-option label="受体零件进厂缺件" value="10"></el-option>
            <el-option label="受体零件工程要求" value="11"></el-option>
            <el-option label="受体零件工程待定" value="12"></el-option>
            <el-option label="受体零件客户要求" value="13"></el-option>
            <el-option label="受体零件客户提走" value="14"></el-option>
            <el-option label="工作指令变更" value="15"></el-option>
            <el-option label="受体零件证书有误" value="16"></el-option>
            <el-option label="受体零件转包报废" value="17"></el-option>
            <el-option label="其他-详情记录到备注中" value="20"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="addExchangeState.form.str_need_remark" type="textarea" :rows="3"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addExchangeState.isShowDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAddExchangeState">确定</el-button>
      </template>
    </el-dialog>
  `,
}
