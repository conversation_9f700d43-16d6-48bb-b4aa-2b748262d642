/* 主单元体甘特图样式 */
.main-level-0 {
  background-color: #1de9b6 !important;
  border-radius: 15px;
}
.main-level-1 {
  background-color: #ff723b !important;
  border-radius: 15px;
}
.main-level-2 {
  background-color: #50ff40 !important;
  border-radius: 15px;
}
/* 子单元体甘特图样式 */
.sub-level-0 {
  background-color: #1de9b6 !important;
  border-radius: 15px;
}
.sub-level-1 {
  background-color: #ff723b !important;
  border-radius: 15px;
}
.sub-level-2 {
  background-color: #50ff40 !important;
  border-radius: 15px;
}
.sub-level-3 {
  background-color: #ff40f0 !important;
  border-radius: 15px;
}
.sub-level-4 {
  background-color: #407cff;
  border-radius: 15px;
}
.gantt_tooltip {
  z-index: 9999 !important;
}
/* 站位甘特图 */
.station-level-0 {
  background-color: #1de9b6;
  border-radius: 15px;
}
.station-level-1 {
  background-color: #ff723b;
  border-radius: 15px;
}
/* releaseQuantity甘特图 */
:root {
  --release-quantity-1-color: #1de9b6;
  --release-quantity-2-color: #ff723b;
}

.release-quantity-1,
.release-quantity-2 {
  border-radius: 15px;
}

.release-quantity-1 {
  background-color: var(--release-quantity-1-color) !important;
}

.release-quantity-2 {
  background-color: var(--release-quantity-2-color) !important;
}