import { queryCalculationList } from '../../api/plan.manage.js'

const { reactive, onMounted, ref } = Vue
 
export function useTable() {
  const state = reactive({
    tableData: [],
    tableColumns: [],
  })
  const pagePagerState = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
  })

  // 获取表格列
  const getTableColumns = () => {
    state.tableColumns = [
      {
        field: 'str_name',
        title: 'project名称',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'str_ver',
        title: '版本',
        minWidth: 50,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'str_engine_type',
        title: '机型',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'str_wo',
        title: 'WO',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'str_esn',
        title: 'ESN',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'Flow',
        field: 'str_flow',
        minWidth: 70,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '修理级别',
        field: 'str_level',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'F3关闭时间',
        field: 'dt_f3_close',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '计划开始时间',
        field: 'dt_begin',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '计划结束时间',
        field: 'dt_end',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '操作时间',
        field: 'dt_up',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '操作人',
        field: 'str_by',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      // {
      //   type: 'html',
      //   title: 'F3变化',
      //   field: 'is_f3_changed',
      //   minWidth: 80,
      //   filters: [
      //     { label: '是', value: "1" },
      //     { label: '否', value: "0" }
      //   ],
      //   filterMultiple: false,
      //   formatter: ({ cellValue }) => {
      //     return `<span style="color: ${cellValue === 1 ? '#E6A23C' : ''}">${cellValue === 1 ? '是' : '否'}</span>`
      //   },
      // },
      {
        type: 'html',
        title: '状态',
        field: 'is_state',
        minWidth: 100,
        filters: [
          { label: '启用', value: 1 },
          { label: '停用', value: 0 },
        ],
        filterMultiple: false,
        formatter: ({ cellValue }) => {
          return `<span style="color: ${cellValue === 1 ? 'green' : 'red'}">${cellValue === 1 ? '启用' : '停用'}</span>`
        },
      },
    ]
  }
  onMounted(() => {
    getTableColumns()
  })

  const parentFilterList = ref([])
  // 过滤
  const handleFilterChange = async (panel) => {
    pagePagerState.currentPage = 1
    const { filterList } = panel
    const queryLists = filterList.map((item) => {
      return {
        str_key: item.field,
        str_value: item.values[0] ?? item.datas[0],
      }
    })
    parentFilterList.value.push(...queryLists)
    await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize)
  }
  /**
   * @description 获取表格数据通过前端分页
   * @param {number} currentPage - 当前页
   * @param {number} pageSize - 每页条数
   * @param {array} queryLists - 查询条件
   * @return {Promise<void>}
   */
  const getTableDataByFrontPage = async (currentPage = 1, pageSize = 10, queryLists = []) => {
    queryLists.push(...parentFilterList.value)
    const tableData = await queryCalculationList(queryLists)
    const start = (currentPage - 1) * pageSize
    const end = currentPage * pageSize
    state.tableData = tableData.slice(start, end)
    pagePagerState.total = tableData.length
  }
  const handlePageChange = async (currentPage) => {
    pagePagerState.currentPage = currentPage.currentPage
    pagePagerState.pageSize = currentPage.pageSize
    console.log(parentFilterList.value);
    await getTableDataByFrontPage(currentPage.currentPage, currentPage.pageSize)
  }

  return {
    state,
    pagePagerState,
    parentFilterList,
    handleFilterChange,
    getTableDataByFrontPage,
    handlePageChange,
  }
}
