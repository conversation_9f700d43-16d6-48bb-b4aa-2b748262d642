const { ref, defineAsyncComponent } = Vue
export default {
  name: 'CollectBoxTransportComponent',
  components: {
    ContainerTransportPlanComponent: defineAsyncComponent(() => import('./container_transportation_plan.js')),
    OutboundMissingPartsComponent: defineAsyncComponent(() => import('./outbound_missing_parts.js')),
  },
  setup() {
    const activeName = ref('1')
    return {
      activeName,
    }
  },
  template: /*html*/ `
    <div class="mx-2">
      <el-tabs v-model="activeName">
        <el-tab-pane label="集件箱运输计划" name="1" lazy>
          <container-transport-plan-component></container-transport-plan-component>
        </el-tab-pane>
        <el-tab-pane label="集件箱缺件出库" name="2" lazy>
          <outbound-missing-parts-component></outbound-missing-parts-component>
        </el-tab-pane>
      </el-tabs>
    </div>
  `,
}
