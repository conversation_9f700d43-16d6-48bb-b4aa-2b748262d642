/**
 * @Description 搜索表单
 * <AUTHOR>
 */
import { post } from '../../../../config/axios/httpReuest.js'
import { useOption } from '../../../hooks/useOption.js'
const { ref, reactive, onMounted } = Vue
import { LABEL_ENUM } from '../../../enum/index.js'
const PartSearchForm = {
  emits: ['search'],
  setup(props, { emit }) {
    // 折叠状态
    const collapseStatus = ref(true)
    const formRef = ref(null)
    // 表单数据
    const formData = reactive({
      str_esn: '',
      str_wo: '',
      str_sm: '',
      str_pn: '',
      str_part_name: '',
      str_client: [],
      is_result_main: '',
      id_engine_type: '',
      dt_f41_date: '',
      dt_release_date: '',
      id_b123: '',
      int_site: [],
      id_owner: '',
      int_maintenance_type: '',
      str_flow: '',
      dt_f23_date: '',
      str_group: '',
    })
    const {
      siteOptions,
      getSiteOptions,
      maintenaceOptions,
      getMaintenaceOptions,
      flowOptions,
      getFlowOptions,
      userListOptions,
      getUserListOptions,
      customerOptions,
      getCustomerOptions,
    } = useOption()

    // * 查询
    const handleSearch = () => {
      emit('search', formData)
    }

    // * 重置
    const handleReset = () => {
      formRef.value.reset()
      handleSearch()
    }

    // * 推演
    const handleDeduce = async () => {
      // 全屏loading
      const loading = ElementPlus.ElLoading.service({
        lock: true,
        text: '推演中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      // 1. 先判断是否可以进行推演
      const isCanDeduceParams = {
        ac: 'de_getdecutionstatus',
      }
      const { data } = await post(isCanDeduceParams)
      if (data.code === 'success') {
        const params = {
          ac: 'de_calculate',
        }
        const { data: res } = await post(params)
        if (res.code === 'success') {
          ElementPlus.ElMessage.success('推演成功')
          loading.close()
          handleSearch()
        } else {
          ElementPlus.ElMessage.error(res.text)
          loading.close()
        }
      } else {
        ElementPlus.ElMessage.error(data.text)
        loading.close()
      }
    }

    // * 折叠
    const handleCollapse = (status) => {
      const { collapse } = status
      collapseStatus.value = collapse
      emit('collapse')
    }

    onMounted(() => {
      getSiteOptions()
      getMaintenaceOptions()
      getFlowOptions()
      getUserListOptions()
      getCustomerOptions()
    })

    return {
      collapseStatus,
      formRef,
      formData,
      siteOptions,
      maintenaceOptions,
      flowOptions,
      userListOptions,
      customerOptions,
      handleSearch,
      handleReset,
      handleDeduce,
      handleCollapse,
    }
  },
  template: /*html*/ `
    <vxe-form
      ref="formRef"
      v-model:collapseStatus="collapseStatus"
      :data="formData"
      prevent-submit
      custom-layout
      size="small"
      title-align="right"
      title-width="120px"
      @collapse="handleCollapse"
    >
      <vxe-form-item title="ESN:" field="str_esn" :item-render="{}" span="6">
        <template #default="{ data }">
          <vxe-input v-model.trim="data.str_esn" clearable placeholder="请输入ESN" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="WO:" field="str_wo" :item-render="{}" folding span="6">
        <template #default="{ data }">
          <vxe-input v-model.trim="data.str_wo" clearable placeholder="请输入WO" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="SM:" field="str_sm" :item-render="{}" folding span="6">
        <template #default="{ data }">
          <vxe-input v-model.trim="data.str_sm" clearable placeholder="请输入SM" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="PN:" field="str_pn" :item-render="{}" folding span="6">
        <template #default="{ data }">
          <vxe-input v-model.trim="data.str_pn" clearable placeholder="请输入PN" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Pn Name:" field="str_part_name" :item-render="{}" folding span="6">
        <template #default="{ data }">
          <vxe-input v-model.trim="data.str_part_name" clearable placeholder="请输入零件名称" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Customer:" field="str_client" :item-render="{}" folding span="6">
        <template #default="{ data }">
          <vxe-select v-model="data.str_client" clearable placeholder="请选择Customer" filterable multiple>
            <vxe-option
              v-for="item in customerOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="LABEL:" field="is_result_main" :item-render="{}" folding span="6">
        <template #default="{ data }">
          <vxe-select v-model="data.is_result_main" clearable placeholder="请选择LABEL">
            <vxe-option label="黄" value="${LABEL_ENUM.YELLOW}"></vxe-option>
            <vxe-option label="白" value="${LABEL_ENUM.WHITE}"></vxe-option>
            <vxe-option label="无" value="${LABEL_ENUM.NONE}"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Engine Type:" field="id_engine_type" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.id_engine_type" clearable placeholder="请选择Engine Type">
            <vxe-option label="CFM56" value="CFM56"></vxe-option>
            <vxe-option label="LEAP" value="LEAP"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="F4-1 Begin Time:" field="dt_f41_date" :item-render="{}" folding span="6">
        <template #default="{data}">
          <el-date-picker
            v-model="data.dt_f41_date"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
            clearable
            style="width: 100%"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Realease End:" field="dt_release_date" :item-render="{}" folding span="6">
        <template #default="{data}">
          <el-date-picker
            v-model="data.dt_release_date"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
            clearable
            style="width: 100%"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </template>
      </vxe-form-item>
      <vxe-form-item title="B1/B2/B3" field="id_b123" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.id_b123" clearable placeholder="请选择B1/B2/B3">
            <vxe-option label="B1" value="1"></vxe-option>
            <vxe-option label="B2" value="2"></vxe-option>
            <vxe-option label="B3" value="3"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Site:" field="int_site" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.int_site" clearable multiple placeholder="请选择site">
            <vxe-option
              v-for="item in siteOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Owner:" field="id_owner" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.id_owner" clearable placeholder="请选择Owner" filterable>
            <vxe-option
              v-for="item in userListOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Maintenance Type:" field="int_maintenance_type" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.int_maintenance_type" clearable placeholder="请选择Maintenace Type">
            <vxe-option
              v-for="item in maintenaceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Flow:" field="str_flow" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.str_flow" clearable placeholder="请选择Flow">
            <vxe-option
              v-for="item in flowOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="F2/3 Closed Time:" field="dt_f23_date" :item-render="{}" folding span="6">
        <template #default="{data}">
          <el-date-picker
            v-model="data.dt_f23_date"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
            clearable
            style="width: 100%"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Group:" field="str_group" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.str_group" clearable placeholder="请选择Group">
            <vxe-option label="CORE" value="CORE"></vxe-option>
            <vxe-option label="FAN" value="FAN"></vxe-option>
            <vxe-option label="LPT" value="LPT"></vxe-option>
            <vxe-option label="B1" value="B1"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" collapse-node>
        <vxe-button status="primary" content="查询" @click="handleSearch"></vxe-button>
        <vxe-button status="primary" content="重置" @click="handleReset"></vxe-button>
        <vxe-button status="primary" content="推演" @click="handleDeduce"></vxe-button>
      </vxe-form-item>
    </vxe-form>
  `,
}

export default PartSearchForm
