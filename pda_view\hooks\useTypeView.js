import {
  DM_VIEW,
  EDD_UPDATE_TASK_PDA,
  PDA_VIEW_AD,
  PDA_VIEW_P,
  WAYBILL_VIEW,
  WAYBILL_VIEW_ADMIN,
} from '../../config/tabPaneKey.js';

const { ref } = Vue;

export function useTypeView(isPView = true) {
  const currentTypeView = ref(isPView ? PDA_VIEW_P : PDA_VIEW_AD);
  const updateCurrentTypeView = (type) => {
    currentTypeView.value = type;
  };
  const activeName = ref('0');
  const handleTabChange = (name) => {
    switch (name) {
      case '0':
        isPView ? updateCurrentTypeView(PDA_VIEW_P) : updateCurrentTypeView(PDA_VIEW_AD);
        break;
      case '1':
        updateCurrentTypeView(DM_VIEW);
        break;
      case '2':
        updateCurrentTypeView(EDD_UPDATE_TASK_PDA);
        break;
      case '3':
        updateCurrentTypeView('EDD_TASK_LIST');
        break;
      case '4':
        updateCurrentTypeView(WAYBILL_VIEW);
        break;
      case '5':
        updateCurrentTypeView(WAYBILL_VIEW_ADMIN);
        break;
    }
  };

  return {
    currentTypeView,
    updateCurrentTypeView,
    activeName,
    handleTabChange,
  };
}
