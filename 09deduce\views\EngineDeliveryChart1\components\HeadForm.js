import { useForm } from '../composables/useForm.js'
import { useApi } from '../../../hooks/useApi.js'
const { ref, onMounted } = Vue
const HeadForm = {
  emits: ['search'],
  setup(props, { emit }) {
    const { formSearch, handleRest } = useForm()

    const { getCommonOptionApi } = useApi()

    const handleSearch = () => {
      emit('search', formSearch)
    }


    const engineTypes = ref([
      { label: 'CFM56', value: 'CFM56' },
      { label: 'LEAP', value: 'LEAP' },
    ])
    const repairTypes = ref([])

    /**
     * 获取维修类型
     */
    const getRepairTypes = async () => {
      repairTypes.value = await getCommonOptionApi('Maintenace')
    }

    onMounted(() => { 
      getRepairTypes()
    })

    return {
      formSearch,
      handleSearch,
      handleRest,
      engineTypes,
      repairTypes,
    }
  },
  template: /*html*/ `
    <el-form :model="formSearch" :inline="true" class="engine-form">
      <el-form-item label="F4-1 Begin Time:" label-class="text-white">
        <el-date-picker
          v-model="formSearch.f4_1BeginTime"
          type="daterange"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          format="YYYY-MM-DD"
          class="w-64"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="Realease Time:">
        <el-date-picker
          v-model="formSearch.realeaseTime"
          type="daterange"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="F2/3 Closed Time:">
        <el-date-picker
          v-model="formSearch.f2_3ClosedTime"
          type="daterange"
          placeholder="选择日期"
          value-format="YYYY-MM-DD"
          format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="机型:">
        <el-select v-model="formSearch.engineType" placeholder="请选择机型" clearable class="!w-[220px]">
          <el-option v-for="item in engineTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维修类型:">
        <el-select v-model="formSearch.repairType" placeholder="请选择维修类型" clearable class="!w-[220px]" multiple>
          <el-option v-for="item in repairTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label=" ">
        <el-button type="primary" @click="handleSearch" class="bg-blue-500 hover:bg-blue-600">
          查询
        </el-button>
        <el-button type="info" class="bg-gray-500 hover:bg-gray-600" @click="handleRest"> 重置 </el-button>
      </el-form-item>
    </el-form>
  `,
}

export default HeadForm
