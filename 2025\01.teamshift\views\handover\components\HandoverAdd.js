const { useVModel } = VueUse
import { saveHandover, getTaskList, getEnumList } from '../api/index.js'
import { useDropdownOptions } from '../composables/useDropdownOptions.js'
import { useUploader } from '../composables/useUploader.js'
import { useFileHandler } from '../composables/useFileHandler.js'
import FileViewerComponent from './FileViewer.js'
import FileUploadButtonComponent from './FileUploadButton.js'
import FileTagsComponent from './FileTags.js'
import { useAddHandover } from '../composables/useAddHandover.js'

export default {
  name: 'AddHandoverComponent',
  components: {
    FileViewerComponent,
    FileUploadButtonComponent,
    FileTagsComponent,
  },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    flow: {
      flow: String,
      required: true,
    },
    type: {
      type: Number,
      required: false,
    },
    isEdit: {
      type: Boolean,
      required: false,
    },
    currentEditRow: {
      type: Object,
      required: false,
    },
    businessType: {
      type: [Number, String],
      required: true,
    },
  },
  emits: ['update:visible', 'refresh'],
  setup(props, { emit }) {
    const { toRef, ref, watch, onMounted } = Vue
    const { ElMessage } = ElementPlus

    const dialogVisible = useVModel(props, 'visible', emit)

    const {
      tableData,
      queryParams,
      loading,
      headerTips,
      handleQuery,
      handleReset,
      mainData,
      handleAddRow,
      editFirstMainData,
    } = useAddHandover(props)

    // 初始化文件处理器
    const fileHandler = useFileHandler()

    // 初始化文件上传器
    const uploader = useUploader({
      esn: toRef(queryParams, 'esn'),
      sm: toRef(queryParams, 'sm'),
    })

    // 删除行数据
    const handleDeleteRow = (row) => {
      const index = tableData.value.findIndex((item) => item === row)
      if (index !== -1) {
        tableData.value.splice(index, 1)
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      // 如果ESN和SM为空，则提示
      if (queryParams.type === 102) {
        if (!queryParams.shift || !queryParams.str_task_type) {
          ElMessage.error('班次和工序不能为空')
          return
        }
      }
      if (queryParams.type === 101) {
        if (!queryParams.esn || !queryParams.shift || !queryParams.str_task_type) {
          ElMessage.error('ESN、班次和工序不能为空')
          return
        }
      }
      if (queryParams.type != 101 && queryParams.type != 102) {
        if (!queryParams.esn || !queryParams.shift || !queryParams.sm || !queryParams.task) {
          ElMessage.error('esn、单元体、班次、任务不能为空')
          return
        }
      }
      let str_task = ''
      if (queryParams.task) {
        str_task = taskOptions.value.find((task) => task.value === queryParams.task)?.label || ''
      }

      // 构建主表数据
      const main = {
        id: mainData.value?.id || editFirstMainData.value?.id || '',
        id_wo: queryParams.esn || '',
        str_wo: queryParams.esnName || '',
        id_team: queryParams.teamId || '',
        dt_pt: moment().format('YYYY-MM-DD'),
        id_shift: queryParams.shift || '',
        str_shift: queryParams.shiftName || '',
        id_model: queryParams.sm || '',
        id_task: queryParams.task || '',
        str_task: queryParams.str_task || '',
        str_handover_type: props.type,
        id_by_to_receive: queryParams.id_by_to_receive || '',
        is_pending: queryParams.is_pending || 0,
        is_completed:queryParams.is_completed || 0,
        str_task_type: queryParams.str_task_type || '',
        int_handover_status: 1,
        
      }
      const handoverData = tableData.value.map((item) => ({
        pTHandover: {
          id: item.id || '', // 交接单ID
          id_model: queryParams.sm, // SM ID
          str_sm: item.sm, // SM 名称
          int_type: item.code, // 使用code作为类型
          str_category: item.legend, // 类别
          str_help: item.tip, // 提示信息
          str_content: item.desc || '', // 描述内容
          int_status: item.isTransferred ? 1 : 0, // 是否交接状态
        },
        files: (item.attachment || []).map((file) => ({
          id: file.id || '',
          str_path: file.str_path,
          str_file_name: file.str_file_name || file.name,
        })),
      }))
      const params = []
      params.push({
        pTHandoverMain: main,
        pTHandovers: handoverData,
      })
      await saveHandover(params)
      ElMessage.success('提交成功')
      dialogVisible.value = false
      emit('refresh')
    }

    const dropdownOptions = useDropdownOptions(toRef(queryParams, 'esn'), props.businessType)

    const handleEsnChange = (value) => {
      const esn = dropdownOptions.esnOptions.value.find((item) => item.id_wo === value)
      queryParams.esnName = esn?.str_wo ?? ''
    }

    const handleShiftChange = (value) => {
      const shift = dropdownOptions.shiftOptions.value.find((item) => item.value === value)
      queryParams.shiftName = shift?.label ?? ''
    }

    const handleTaskChange = (value) => {
      queryParams.task = value
      handleQuery(true)
    }

    // 计算每个Legend的行数，用于合并单元格
    const getSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1) {
        // Code和Legend列
        const rows = tableData.value || []
        if (!rows.length) return { rowspan: 1, colspan: 1 }

        const currentCode = row.code
        if (!currentCode) return { rowspan: 1, colspan: 1 }

        // 向上找到第一个具有相同code的行的索引
        let startIndex = rowIndex
        while (startIndex > 0 && rows[startIndex - 1] && rows[startIndex - 1].code === currentCode) {
          startIndex--
        }

        // 如果是该分组的第一行
        if (rowIndex === startIndex) {
          // 计算具有相同code的行数
          let spanCount = 1
          let nextIndex = rowIndex + 1
          while (nextIndex < rows.length && rows[nextIndex] && rows[nextIndex].code === currentCode) {
            spanCount++
            nextIndex++
          }
          return {
            rowspan: spanCount,
            colspan: 1,
          }
        } else {
          // 不是第一行则隐藏
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
      return { rowspan: 1, colspan: 1 }
    }

    const taskOptions = ref([])
    const getTaskOptions = async (smName) => {
      const params = {
        id_wo: queryParams.esn,
        str_sm: smName,
        str_flow: queryParams.flow,
      }
      const res = await getTaskList(params)
      taskOptions.value = res.map((item) => ({
        value: item.id,
        label: item.str_task + '( shift #' + item.int_sort + ')',
      }))
    }

    const handleSmChange = (value) => {
      const sm = dropdownOptions.smOptions.value.find((item) => item.id === value)
      getTaskOptions(sm?.str_name)
    }
    const handBeforeClose = () => {
      dialogVisible.value = false
    }

    onMounted(async () => {
      const flowMap = {
        101: 'F1-2',
        102: 'F2',
        103: 'F1-1',
        104: 'F1-1',
        105: 'F4-1',
        106: 'F4-1',
        107: 'F1-1',
        108: 'F1-1',
        109: 'F4-1',
        110: 'F4-1',
      }
      if (props.currentEditRow && props.isEdit) {
        const params = {
          id_wo: props.currentEditRow.id_wo,
          str_sm: props.currentEditRow.str_sm,
          str_flow: flowMap[props.currentEditRow.str_handover_type] ?? '',
        }
        queryParams.flow = flowMap[props.currentEditRow.str_handover_type] ?? ''
        if (props.currentEditRow.str_sm) {
          const res = await getTaskList(params)
          taskOptions.value = res.map((item) => ({
            value: item.id,
            label: item.str_task + '( shift #' + item.int_sort + ')',
          }))
        }
      }
      queryParams.flow = flowMap[props.businessType] ?? ''
    })

    return {
      dialogVisible,
      headerTips,
      queryParams,
      tableData,
      loading,
      taskOptions,
      handleQuery,
      handleTaskChange,
      handleReset,
      handleSubmit,
      handleAddRow,
      handleDeleteRow,
      handleEsnChange,
      handleShiftChange,
      getSpanMethod,
      handleSmChange,
      // 使用组合式函数提供的功能
      ...fileHandler, // 提供 drawerVisible, currentFiles, handleViewFiles, handleDownload 等
      ...uploader, // 提供 handleUpload 等
      ...dropdownOptions,
      handBeforeClose,
    }
  },
  template: /*html*/ `
    <el-drawer
      class="common-drawer"
      v-model="dialogVisible"
      title="新增交接班"
      size="80%"
      append-to-body
    >
      <div class="mb-4 rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4 shadow-sm">
        <div class="flex items-center">
          <div class="flex w-full cursor-help items-center">
            <el-icon class="mr-2 flex-shrink-0" :size="20"><InfoFilled /></el-icon>
            <div class="min-w-0 flex-1">
              <div class="mb-1 text-lg font-semibold text-red-700">交接提示</div>
              <div class="whitespace-pre-line text-red-600">{{ headerTips }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flow和Team信息卡片 -->
      <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2">
        <div class="rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 p-2 shadow-sm">
          <div class="flex items-center space-x-3">
            <div class="flex items-center justify-center rounded-full bg-blue-100">
              <el-icon class="text-blue-600" :size="20"><Promotion /></el-icon>
            </div>
            <div>
              <div class="text-sm text-gray-500">当前Flow</div>
              <div class="text-lg font-semibold">{{ queryParams.flow }}</div>
            </div>
          </div>
        </div>

        <div class="rounded-lg bg-gradient-to-r from-purple-50 to-pink-50 p-2 shadow-sm">
          <div class="flex items-center space-x-3">
            <div class="flex items-center justify-center rounded-full bg-purple-100">
              <el-icon class="text-purple-600" :size="20"><User /></el-icon>
            </div>
            <div>
              <div class="text-sm text-gray-500">当前Team</div>
              <div class="text-lg font-semibold">{{ queryParams.teamName }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 查询表单 -->
      <el-form inline :model="queryParams" class="mb-4">
        <el-form-item label="ESN" v-if="businessType != '102'">
          <el-select
            class="!w-48"
            filterable
            v-model="queryParams.esn"
            placeholder="请选择ESN"
            clearable
            @change="handleEsnChange"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in esnOptions"
              :key="item.id_wo"
              :label="item.str_esn"
              :value="item.id_wo"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="SM" v-if="businessType != '101' && businessType != '102'">
          <el-select
            class="!w-48"
            filterable
            v-model="queryParams.sm"
            placeholder="请选择SM"
            clearable
            @change="handleSmChange"
            :disabled="isEdit"
          >
            <el-option v-for="item in smOptions" :key="item.id" :label="item.str_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班次">
          <el-select
            class="!w-48"
            v-model="queryParams.shift"
            placeholder="请选择班次"
            clearable
            @change="handleShiftChange"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in shiftOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务" v-if="businessType != '101' && businessType != '102'">
          <el-select
            class="!w-48"
            v-model="queryParams.task"
            placeholder="请选择任务"
            clearable filterable
            @change="handleTaskChange"
          >
            <el-option
              v-for="item in taskOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工序" v-if="businessType == '101' || businessType == '102'">
          <el-select class="!w-48" v-model="queryParams.str_task_type" placeholder="请选择工序" clearable filterable>
            <el-option
              v-for="item in taskTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="接收人">
          <el-select
            class="!w-48"
            v-model="queryParams.id_by_to_receive"
            filterable
            clearable
            placeholder="请选择接收人"
          >
            <el-option
              v-for="item in staffOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

     
       <el-form-item label="PENDING">
          <el-select class="!w-48" v-model="queryParams.is_pending" clearable>
            <el-option :key="1" label="是" value="1"></el-option>
            <el-option :key="0" label="否" value="0"></el-option>
          </el-select>
        </el-form-item>

           <el-form-item label="">
            <el-checkbox border  v-model="queryParams.is_completed" label="完成(最后一个任务不需要接收)"  :true-label="1" :false-label="0"  />
          <!--<el-select class="!w-48" v-model="queryParams.is_completed">
            <el-option :key="1" label="是" value="1"></el-option>
            <el-option :key="0" label="否" value="0"></el-option>
          </el-select>-->
        </el-form-item>
        <el-form-item v-if="!isEdit">
          <el-button type="primary" @click="handleQuery" :loading="loading">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <div>
        <!-- 表格数据 -->
        <el-table
          v-if="tableData.length > 0"
          :data="tableData"
          border
          style="width: 100%"
          v-loading="loading"
          :span-method="getSpanMethod"
        >
          <el-table-column prop="code" label="Code" width="80" align="center" />
          <el-table-column prop="legend" label="Legend" min-width="60">
            <template #default="{ row }">
              <div class="flex items-center">
                <div class="min-w-0 flex-1">{{ row.legend }}</div>
                <el-tooltip v-if="row.tip" effect="dark" placement="top" :content="row.tip">
                  <el-icon class="ml-1 text-gray-400" :size="16"><InfoFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="sm" label="SM" width="100" />
          <el-table-column prop="desc" label="Desc" min-width="300">
            <template #default="{ row }">
              <el-input type="textarea" v-model="row.desc" :rows="2" placeholder="请输入内容" resize="none" />
            </template>
          </el-table-column>
          <el-table-column label="Attachment" min-width="100" align="center">
            <template #default="{ row }">
              <div class="flex items-center">
                <FileUploadButtonComponent
                  :row="row"
                  :hasFiles="row.attachment?.length > 0"
                  @upload="handleUpload"
                  @view="handleViewFiles"
                />
                <FileTagsComponent
                  v-if="row.attachment?.length > 0"
                  :files="row.attachment"
                  @view="() => handleViewFiles(row)"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column label="Operate" width="120" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center space-x-2">
                <el-button v-if="!row.isOriginal" type="danger" link @click="handleDeleteRow(row)">删除</el-button>
                <el-button v-if="row.isOriginal" type="primary" link @click="handleAddRow(row)">新增</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="无交接" width="80" align="center">
            <template #default="{ row }">
              <!-- 使用html的选择框 -->
              <input type="checkbox" class="border-blue-500" v-model="row.isTransferred" />
            </template>
          </el-table-column>
        </el-table>
        <div v-else-if="loading" class="flex h-32 items-center justify-center text-gray-500">加载中...</div>
        <div v-else class="flex h-32 items-center justify-center text-gray-500">暂无数据，请先查询</div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handBeforeClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </span>
      </template>

      <!-- 文件查看抽屉 -->
      <FileViewerComponent
        v-model:visible="drawerVisible"
        :files="currentFiles"
        @download="handleDownload"
        @delete="handleDelete"
      />

    </el-drawer>
  `,
}
