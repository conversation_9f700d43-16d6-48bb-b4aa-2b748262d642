import { queryUserRole } from '../api/index.js'

export default {
  async mounted(el, binding) {
    const allowedRoles = binding.value
    // 获取当前用户的角色
    try {
      const { data } = await queryUserRole()
      const currentUserRole = data.data.map((item) => item.str_code)
      const hasAccess = allowedRoles.some((role) => currentUserRole.includes(role))
      if (!hasAccess) {
        el.style.display = 'none' // 隐藏元素
      }
    } catch (e) {
      console.error('Failed to get user role', e)
      el.style.display = 'none' // 隐藏元素
    }
  },
}
