import '../../components/VxeTable/renderer/index.js';
import { post } from '../../config/axios/httpReuest.js';
import { columnStrategy } from '../hooks/nodeTableColumn.js';
import SearchForm from './SearchForm.js';
import RateTable from './RateTable.js';
import HtDrawer from '../../components/ht.drawer.js';
import HtVxeTable from '../../components/VxeTable/HtVxeTable.js';

const { reactive, ref, onMounted } = Vue;
const ComplianceRate = {
  components: {
    SearchForm,
    RateTable,
    HtDrawer,
    HtVxeTable,
  },
  setup() {
    const formData = reactive({
      assessmentDate: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
    });

    // 过滤条件转化
    const filterData = (data) => {
      // 字段转化
      const map = {
        esn: 'str_esn',
        wo: 'str_wo',
        sm: 'str_sm',
        pn: 'str_pn',
        engineType: 'id_engine_type',
        wip: 'is_wip',
        assessmentDate: 'dt_assessment',
        releaseDate: 'dt_release_date',
      };
      const list = [];
      for (const key in data) {
        const result = {};
        if (map[key] && data[key]) {
          result['str_key'] = map[key];
          result['str_value'] = data[key];
          list.push(result);
        }
      }
      return list;
    };
    // 数据转化
    const transformData = (data) => {
      const result = {};
      // 将其他字段放入result
      for (const key in data) {
        result[key] = data[key];
      }
      return result;
    };

    // 搜索方法
    const searchMethod = () => {
      const data = transformData(formData);
      const list = filterData(data);
      getTableData(list);
    };

    // 表格数据
    const tableData = ref([]);
    // 获取表格数据
    const getTableData = async (list = []) => {
      const params = {
        ac: 'pda_ComplianceRate',
        filter_fields: list,
      };
      const { data } = await post(params);
      if (data.code === 'success') {
        tableData.value = data.data;
      } else {
        ElementPlus.ElMessage.error(data.text);
      }
    };
    const clickState = reactive({
      visible: false,
      title: '',
    });
    const tableState = reactive({
      TableData: null,
      TableColumns: [],
      totalNum: 0,
    });
    // 初始化抽屉表格数据
    const initDrawerTableState = () => {
      tableState.TableData = null;
      tableState.TableColumns = [];
      tableState.totalNum = 0;
    };
    /**
     *
     * @param {object} row
     * @param {string} type
     *        提前进入:rate_in_ahead
     *        提前离开:rate_out_ahead
     *        滞后进入:rate_in_late
     *        滞后离开:rate_out_late
     *        合格进入:rate_out
     *        总数:rate_total
     */
    const cellClick = (row, type) => {
      initDrawerTableState();
      clickState.visible = true;
      tableState.TableColumns = columnStrategy[row.int_site];
      const data = transformData(formData);
      const list = filterData(data);
      getDrawerTableData(type, row.int_site, list).then((data) => {
        tableState.TableData = data;
        tableState.totalNum = data.length;
      });
    };

    const htTableRef = ref(null);

    // 导出数据
    const exportDataEvent = () => {
      htTableRef.value.exportData({ type: 'csv' });
    };

    /**
     * 获取抽屉表格数据
     * @param {string} type
     * @param {string} node  节点
     * @param {Array} list 过滤字段
     * @return {Promise<null|*>}
     */
    const getDrawerTableData = async (type, node, list = []) => {
      const params = {
        ac: 'pda_get_pda_list_pn',
        str_ywtype: type,
        str_type: node,
        filter_fields: list,
      };
      const { data } = await post(params);
      if (data.code === 'success') {
        return data.data;
      } else {
        ElementPlus.ElMessage.error(data.text);
        return null;
      }
    };

    onMounted(() => {
      const data = transformData(formData);
      const list = filterData(data);
      getTableData(list);
    });

    return {
      formData,
      searchMethod,
      exportDataEvent,
      htTableRef,
      tableData,
      clickState,
      tableState,
      cellClick,
    };
  },
  /*html*/
  template: `
    <div class="m-2">
      <search-form v-model:form="formData" @search="searchMethod"></search-form>
    </div>
    <div class="border-b-2 border-gray-200"></div>
    <div class="m-2">
      <div class="bg-gray-200 py-2">
        <span class="text-black font-bold">站点达标率统计</span>
      </div>
      <div>
        <rate-table v-model:data="tableData" @cellClick="cellClick"></rate-table>
      </div>
    </div>

    <!--    提前进入抽屉-->
    <ht-drawer v-model:visible="clickState.visible" title="零件清单" :is-show-save="false">
      <div class="flex justify-between">
        <el-button type="primary" circle @click="exportDataEvent">
          <el-icon>
            <Download></Download>
          </el-icon>
        </el-button>
        <div>
          <span class="text-xl">Total: {{ tableState.totalNum ?? 0 }}</span>
        </div>
      </div>
      <div class="border-b-2 my-2"></div>
      <div style="height: calc(100vh - 200px)">
        <ht-vxe-table ref="htTableRef" :table-data="tableState.TableData"
                      :table-columns="tableState.TableColumns"></ht-vxe-table>
      </div>
    </ht-drawer>
  `,
};

export default ComplianceRate;
