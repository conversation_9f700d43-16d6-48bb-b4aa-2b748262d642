const { ref } = Vue

export function useTableColumns(businessType) {
  const tableColumns = ref([])

  // 公共列配置
  const commonColumns = [
    {
      field: 'duty',
      title: '交班人',
      minWidth: 100,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
    },
    {
      field: 'shift_name',
      title: '班次',
      minWidth: 100,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
    },
    {
      field: 'handover_date',
      title: '交班日期',
      minWidth: 100,
      filterRender: { name: 'FilterCalendar' },
      filters: [{ data: '' }],
    },
    {
      field: 'str_handover_type',
      title: '交接类型',
      minWidth: 100,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
      formatter: ({ cellValue }) => {
        const typeMap = {
          101: 'F1-2交接',
          102: 'F2交接',
          103: 'F1 B1交接',
          104: 'F1 B23交接',
          105: 'F4 B1交接',
          106: 'F4 B23交接',
          107: 'F1 B1检验',
          108: 'F1 B23检验',
          109: 'F4 B1检验',
          110: 'F4 B23检验',
        }
        return typeMap[cellValue] || ''
      },
    },
    {
      field: 'dt_receive',
      title: '接班日期',
      minWidth: 100,
      filterRender: { name: 'FilterCalendar' },
      filters: [{ data: '' }],
    },
    {
      field: 'receive_name',
      title: '接班人',
      minWidth: 80,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
    },
    {
      type: 'html',
      field: 'int_handover_status',
      title: '已交',
      minWidth: 80,
      filters: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
      filterMultiple: false,
      formatter: ({ cellValue }) => {
        return `<span class="${cellValue === 1 ? 'text-green-500' : ''}">${cellValue === 1 ? '是' : '否'}</span>`
      },
    },
    {
      type: 'html',
      field: 'int_status',
      title: '已接',
      minWidth: 80,
      filters: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
      filterMultiple: false,
      formatter: ({ cellValue }) => {
        return `<span class="${cellValue === 1 ? 'text-green-500' : ''}">${cellValue === 1 ? '是' : '否'}</span>`
      },
    },
    {
      type: 'html',
      field: 'is_pending',
      title: 'PENDING',
      minWidth: 80,
      filters: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
      filterMultiple: false,
      formatter: ({ cellValue }) => {
        return `<span class="${cellValue == 1 ? 'text-green-500' : ''}">${cellValue == 1 ? '是' : '否'}</span>`
      },
    },
    
    {
      type: 'html',
      field: 'is_completed',
      title: '完成',
      minWidth: 80,
      filters: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
      filterMultiple: false,
      formatter: ({ cellValue }) => {
        return `<span class="${cellValue == 1 ? 'text-green-500' : ''}">${cellValue == 1 ? '是' : '否'}</span>`
      },
    },
  ]

  // 业务类型特定列
  const businessColumns = {
    102: [
      {
        field: 'str_task_type',
        title: '工序',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
    ],
    101: [
      {
        field: 'str_wo',
        title: 'WO',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_esn',
        title: 'ESN',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_type',
        title: 'Type',
        minWidth: 80,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_task_type',
        title: '工序',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
    ],
    default: [
      {
        field: 'str_wo',
        title: 'WO',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_esn',
        title: 'ESN',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_type',
        title: 'Type',
        minWidth: 80,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_sm',
        title: 'SM',
        minWidth: 50,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'task_name',
        title: '交接任务',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
    ],
  }

  // 根据业务类型添加特定列
  if (businessColumns[businessType]) {
    tableColumns.value.push(...businessColumns[businessType])
  } else if (businessType !== 102 && businessType !== 101) {
    tableColumns.value.push(...businessColumns.default)
  }

  // 添加公共列
  tableColumns.value.push(...commonColumns)

  return {
    tableColumns,
  }
}
