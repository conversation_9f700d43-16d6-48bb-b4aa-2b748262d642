import { useSelection } from '../../hooks/useSelection.js'

import { delProject } from '../../api/plan.manage.js'

const { toRefs, defineAsyncComponent, onMounted } = Vue
const PlanCalculation = {
  components: {
    HtVxeTable: defineAsyncComponent(() => import('../../../components/VxeTable/HtVxeTable.js')),
    PagePager: defineAsyncComponent(() => import('../../components/PagePager.js')),
    HtDialog: defineAsyncComponent(() => import('../../../components/ht.dialog.js')),
    AddProject: defineAsyncComponent(() => import('./addProject.js')),
    HtDrawer: defineAsyncComponent(() => import('../../../components/ht.drawer.js')),
    PlanGantt: defineAsyncComponent(() => import('./planGantt.js')),
  },
  setup() {
    const { state, handleFilterChange, getTableDataByFrontPage, pagePagerState, handlePageChange } = useTable()
    const { hasSelectedData, hasSelectedOneData } = useSelection()
    const addProjectRef = Vue.ref(null)
    const tableRef = Vue.ref(null)
    const currentState = Vue.reactive({
      id: '',
    })
    // 新增Project
    const dialogVisible = Vue.ref(false)
    const openAddProjectDialog = () => {
      dialogVisible.value = true
    }
    // 编辑Project
    const openEditProjectDialog = () => {
      const selectedData = hasSelectedData(tableRef, 'getSelectedData')
      const oneSelectedData = hasSelectedOneData(selectedData)
      if (!oneSelectedData) {
        return
      }
      dialogVisible.value = true
      currentState.id = oneSelectedData.id
    }
    // 删除Project
    const removeProject = async () => {
      const selectedData = hasSelectedData(tableRef, 'getSelectedData')
      const ids = selectedData.map((item) => item.id)
      const res = await ElementPlus.ElMessageBox.confirm('是否删除该项目', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      if (res === 'confirm') {
        // 删除操作
        const boolean = await delProject(ids)
        if (boolean) {
          await getTableDataByFrontPage()
        }
      }
    }

    const planGanttVisible = Vue.ref(false)
    // 打开计划甘特图
    const openPlanGantt = () => {
      const selectedData = hasSelectedData(tableRef, 'getSelectedData')
      const oneSelectedData = hasSelectedOneData(selectedData)
      if (!oneSelectedData) {
        return
      }
      currentState.id = oneSelectedData.id
      planGanttVisible.value = true
    }
    const planGanttRef = Vue.ref(null)
    // 保存计划甘特图
    const savePlanGantt = () => {
      planGanttRef.value.saveGantt()
    }

    const saveProjectDialog = async () => {
      const res = await addProjectRef.value.addAndEditProject()
      if (res) {
        dialogVisible.value = false
        await getTableDataByFrontPage()
      }
    }

    onMounted(() => {
      getTableDataByFrontPage()
    })

    return {
      ...toRefs(state),
      ...toRefs(pagePagerState),
      tableRef,
      handleFilterChange,
      handlePageChange,
      openAddProjectDialog,
      openEditProjectDialog,
      removeProject,
      dialogVisible,
      openPlanGantt,
      planGanttVisible,
      currentState,
      planGanttRef,
      savePlanGantt,
      addProjectRef,
      saveProjectDialog,
    }
  },
  /*html*/
  template: `
      <!--<div class="flex items-center gap-2 m-2">
     
        <el-button type="primary">站位甘特图</el-button>
       
      </div>
      <div class="border-b-2 mb-2"></div>
      <div class="mx-2" style="height: calc(100vh - 140px);">
        <HtVxeTable
            ref="tableRef"
            :tableData
            :tableColumns
            :remote="true"
            @filter-change="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="80"></vxe-column>
          </template>
        </HtVxeTable>
        <div class="border-b-2 my-2"></div>
        <PagePager
            :currentPage
            :pageSize
            :total
            @pageChange="handlePageChange"
        ></PagePager>
      </div>-->


      <!--    计划甘特图-->
      <HtDrawer v-model:visible="planGanttVisible" title="计划甘特图" @save="savePlanGantt">
        <PlanGantt ref="planGanttRef" :id="currentState.id"></PlanGantt>
      </HtDrawer>
    `,
}

export default PlanCalculation
