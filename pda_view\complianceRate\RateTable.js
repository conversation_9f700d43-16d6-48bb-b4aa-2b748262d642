const { useVModel } = VueUse;
const RateTable = {
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  emits: ['cellClick'],
  setup(props, { emit }) {
    const tableData = useVModel(props, 'data', emit);

    // 提前进入点击事件
    const inAheadClick = (row) => {
      emit('cellClick', row, 'rate_in_ahead');
    };
    // 提前离开点击事件
    const outAheadClick = (row) => {
      emit('cellClick', row, 'rate_out_ahead');
    };
    // 滞后进入点击事件
    const inLateClick = (row) => {
      emit('cellClick', row, 'rate_in_late');
    };
    // 滞后离开点击事件
    const outLateClick = (row) => {
      emit('cellClick', row, 'rate_out_late');
    };
    // 合格进入点击事件
    const outClick = (row) => {
      emit('cellClick', row, 'rate_out');
    };
    // 总数点击事件
    const totalClick = (row) => {
      emit('cellClick', row, 'rate_total');
    };

    return {
      tableData,
      inAheadClick,
      outAheadClick,
      inLateClick,
      outLateClick,
      outClick,
      totalClick,
    }
  },
  // language=HTML
  template: `
    <el-table :data="tableData" border stripe>
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column prop="str_site" label="站点名称"></el-table-column>
      <el-table-column prop="int_in_ahead" label="提前进入">
        <template #default="{ row }">
          <span class="underline cursor-pointer text-green-500" @click="inAheadClick(row)">{{ row.int_in_ahead }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="int_out_ahead" label="提前离开">
        <template #default="{ row }">
          <span class="underline cursor-pointer text-green-500" @click="outAheadClick(row)">{{ row.int_out_ahead }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="int_in_late" label="滞后进入">
        <template #default="{ row }">
          <span class="underline cursor-pointer text-red-500" @click="inLateClick(row)">{{ row.int_in_late }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="int_out_late" label="滞后离开">
        <template #default="{ row }">
          <span class="underline cursor-pointer text-red-500" @click="outLateClick(row)">{{ row.int_out_late }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="int_out" label="合格离开">
        <template #default="{ row }">
          <span class="underline cursor-pointer" @click="outClick(row)">{{ row.int_out }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="int_total" label="总数">
        <template #default="{ row }">
          <span class="underline cursor-pointer" @click="totalClick(row)">{{ row.int_total }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="str_out_percent" label="合格完工占比" sortable></el-table-column>
      <el-table-column prop="str_out_ahead_percent" label="提前完工占比" sortable></el-table-column>
      <el-table-column prop="str_out_late_percent" label="滞后完工占比" sortable></el-table-column>
    </el-table>
  `,
};
export default RateTable;
