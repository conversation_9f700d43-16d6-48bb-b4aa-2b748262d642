import {
  ADMINISTRATIVE_RECEIVE,
  CONFIGURATION_PART,
  CSM_CONFIRM_PART,
  EXPORT_TRANSPORT_NODE,
  F1_2_NODE,
  F2_NODE,
  IMPORT_TRANSPORT_NODE,
  INCOMING_INSPECTION_NODE,
  LIGHT_UP_NODE,
  LOCK_HIGH_AVAILABLE,
  LOCK_LOW_AVAILABLE_NEW,
  LOCK_BACKUP_BOARD,
  PURCHASE_FACTORY_DELIVERY,
  PURCHASE_HIGH_AVAILABLE,
  PURCHASE_LOW_AVAILABLE_NEW,
  SEND_MATERIAL_NODE,
  START_SHIPMENT_NODE,
  SUBCONTRACT_NODE,
  SUBCONTRACT_PO,
  WAREHOUSING_NODE,
} from '../../config/nodeKey.js'

export function useTableColumn() {
  const defaultTableColumnByOther = [
    {
      showColumn: true,
      prop: 'int_day',
      label: 'P/#',
      minWidth: 90,
      fixed: 'left',
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_esn',
      label: '原台ESN',
      minWidth: 100,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_wo',
      label: '原台WO',
      minWidth: 100,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_engine_type',
      label: 'Engine Type',
      minWidth: 150,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_flow',
      label: 'Flow',
      minWidth: 100,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_sm',
      label: 'SM',
      minWidth: 90,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_pn',
      label: 'PN',
      minWidth: 140,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_part_name',
      label: 'Pn Name',
      minWidth: 220,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_num',
      label: 'QTY',
      minWidth: 100,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'dt_plan',
      label: '站点要求开工时间',
      minWidth: 200,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'dt_ttd',
      label: '站点要求完工时间',
      minWidth: 200,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'is_out',
      label: '输出',
      minWidth: 100,
      field: [
        { label: 'YES', value: 1 },
        { label: 'NO', value: 0 },
      ],
      is_select: 'select',
      formatter: (row) => {
        return row.is_out === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'is_aog',
      label: 'AOG',
      minWidth: 100,
      field: [
        { label: 'YES', value: 1 },
        { label: 'NO', value: 0 },
      ],
      is_select: 'select',
      formatter: (row) => {
        return row.is_aog === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'dt_input',
      label: '进入站点时间',
      minWidth: 150,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_actat',
      label: '实际TAT',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_tatdays',
      label: '标准TAT',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'is_abnormal',
      label: '数据状态',
      minWidth: 150,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'id_main',
      label: '业务id',
      minWidth: 140,
      fixed: 'right',
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
  ]
  const defaultTableColumnByOther1 = [
    {
      showColumn: true,
      prop: 'int_day',
      label: 'P/#',
      minWidth: 90,
      fixed: 'left',
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_wo_sp',
      label: '目标WO',
      minWidth: 120,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_esn_sp',
      label: '目标ESN',
      minWidth: 120,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_esn',
      label: '原台ESN',
      minWidth: 120,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_wo',
      label: '原台WO',
      minWidth: 120,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'is_sp',
      label: '串件',
      minWidth: 90,
      field: [
        { label: 'YES', value: 1 },
        { label: 'NO', value: 0 },
      ],
      is_select: 'select',
      formatter: (row) => {
        return row.is_sp === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'str_engine_type',
      label: 'Engine Type',
      minWidth: 150,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_flow',
      label: 'Flow',
      minWidth: 100,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_sm',
      label: 'SM',
      minWidth: 90,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_pn',
      label: 'PN',
      minWidth: 140,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_part_name',
      label: 'Pn Name',
      minWidth: 220,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_num',
      label: 'QTY',
      minWidth: 100,
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'dt_plan',
      label: '站点要求开工时间',
      minWidth: 200,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'dt_ttd',
      label: '站点要求完工时间',
      minWidth: 200,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'is_out',
      label: '输出',
      minWidth: 100,
      field: [
        { label: 'YES', value: 1 },
        { label: 'NO', value: 0 },
      ],
      is_select: 'select',
      formatter: (row) => {
        return row.is_out === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'is_aog',
      label: 'AOG',
      minWidth: 100,
      field: [
        { label: 'YES', value: 1 },
        { label: 'NO', value: 0 },
      ],
      is_select: 'select',
      formatter: (row) => {
        return row.is_aog === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'dt_input',
      label: '进入站点时间',
      minWidth: 150,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_actat',
      label: '实际TAT',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_tatdays',
      label: '标准TAT',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'is_abnormal',
      label: '数据状态',
      minWidth: 150,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'id_main',
      label: '业务id',
      minWidth: 140,
      fixed: 'right',
      field: [{ value: '', resetValue: '' }],
      is_select: 'input',
    },
  ]
  const defaultTableColumn = [
    {
      showColumn: true,
      prop: 'int_day',
      label: 'P/#',
      minWidth: 90,
      fixed: 'left',
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_wo_sp',
      label: '目标WO',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_esn_sp',
      label: '目标ESN',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_esn',
      label: '原台ESN',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_wo',
      label: '原台WO',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'is_sp',
      label: '串件',
      minWidth: 90,
      field: [
        { label: 'YES', value: 1 },
        { label: 'NO', value: 0 },
      ],
      is_select: 'select',
      formatter: (row) => {
        return row.is_sp === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'str_engine_type',
      label: 'Engine Type',
      minWidth: 150,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_flow',
      label: 'Flow',
      minWidth: 100,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_sm',
      label: 'SM',
      minWidth: 90,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_pn',
      label: 'PN',
      minWidth: 140,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_part_name',
      label: 'Pn Name',
      minWidth: 220,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_num',
      label: 'QTY',
      minWidth: 90,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'is_out',
      label: '输出',
      minWidth: 90,
      field: [
        { label: 'YES', value: 1 },
        { label: 'NO', value: 0 },
      ],
      is_select: 'select',
      formatter: (row) => {
        return row.is_out === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'is_aog',
      label: 'AOG',
      minWidth: 100,
      field: [
        { label: 'YES', value: 1 },
        { label: 'NO', value: 0 },
      ],
      is_select: 'select',
      formatter: (row) => {
        return row.is_aog === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'dt_input',
      label: '进入站点时间',
      minWidth: 150,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_tatdays',
      label: '标准TAT',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'id_main',
      label: '业务id',
      minWidth: 140,
      fixed: 'right',
      field: [{ value: '' }],
      is_select: 'input',
    },
  ]
  const getTableColumnByCurrentNode = (currentNode) => {
    const columns = []
    switch (currentNode) {
      case SUBCONTRACT_NODE:
        columns.push(...defaultTableColumnByOther1)
        columns.push({
          showColumn: true,
          prop: 'str_ycode',
          label: '运单号',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_sn_out',
          label: '送修序号',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_billcode',
          label: '定单号',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_shipper',
          label: '转包商',
          minWidth: 190,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_edd',
          label: 'EDD',
          minWidth: 190,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_repairship',
          label: '修理厂',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case F2_NODE:
        columns.push(...defaultTableColumnByOther1)
        columns.push({
          showColumn: true,
          prop: 'str_bcode',
          label: '零件条码',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_tcolocation',
          label: '已处理位置',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_pendinglocation',
          label: '待处理位置',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'is_state_dispname',
          label: '零件状态',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case START_SHIPMENT_NODE:
        columns.push(...defaultTableColumnByOther)
        columns.push({
          showColumn: true,
          prop: 'str_shipper',
          label: '转包商',
          minWidth: 160,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_billcode',
          label: '定单号',
          minWidth: 160,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_po',
          label: '定单日期',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_notes',
          label: '订单明细备注',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case EXPORT_TRANSPORT_NODE:
        columns.push(...defaultTableColumnByOther)

        columns.push({
          showColumn: true,
          prop: 'str_billcode',
          label: '定单号',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_abnormal',
          label: '状态备注',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_forwarder',
          label: '运输代理',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_shipper',
          label: '转包商',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_repairship',
          label: '修理厂',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_begin',
          label: '提货日期',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_begin_air',
          label: '机场出发',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_end_air',
          label: '到目的机场',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_notes',
          label: '订单明细备注',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case IMPORT_TRANSPORT_NODE:
        columns.push(...defaultTableColumnByOther1)
        columns.push({
          showColumn: true,
          prop: 'str_ycode',
          label: '运单号',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_forwarder',
          label: '运输代理',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_billcode',
          label: '定单号',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_shipper',
          label: '发货人',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_end_air',
          label: '预到机场',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_end_true',
          label: '提单时间',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_ship_receive',
          label: '运代收货时间',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_notes',
          label: '订单明细备注',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case PURCHASE_FACTORY_DELIVERY:
        columns.push(...defaultTableColumnByOther1)
        columns.push({
          showColumn: true,
          prop: 'str_billcode',
          label: '定单号',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_edd',
          label: 'L/T Date',
          minWidth: 120,
          field: [
            { label: 'YES', value: 1 },
            { label: 'NO', value: 0 },
          ],
          is_select: 'select',
        })
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_client',
          label: '客户',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_supplier',
          label: '供应商',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_delivery',
          label: '单元体装配时间',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case ADMINISTRATIVE_RECEIVE:
        columns.push(...defaultTableColumn)
        columns.push({
          showColumn: true,
          prop: 'dt_f3_end',
          label: 'F3绩效时间',
          minWidth: 140,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'int_actat',
          label: '实际TAT',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_plan',
          label: '站点要求开工时间',
          minWidth: 200,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_schedule',
          label: '排产时间',
          minWidth: 130,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_planend',
          label: '站点要求完工时间',
          minWidth: 200,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_ycode',
          label: '运单号',
          minWidth: 140,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_billcode',
          label: '定单号',
          minWidth: 160,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_ssamc',
          label: '到SSAMC时间',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'is_scrap_dispname',
          label: '回厂状态',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'is_state_dispname',
          label: '行政接收',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case INCOMING_INSPECTION_NODE:
        columns.push(...defaultTableColumn)
        columns.push({
          showColumn: true,
          prop: 'is_abnormal',
          label: '数据状态',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_abnormal',
          label: '状态备注',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_f3_end',
          label: 'F3绩效时间',
          minWidth: 140,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'int_actat',
          label: '实际TAT',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_plan',
          label: '站点要求开工时间',
          minWidth: 200,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_schedule',
          label: '排产时间',
          minWidth: 130,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_planend',
          label: '站点要求完工时间',
          minWidth: 200,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_ycode',
          label: '运单号',
          minWidth: 140,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_billcode',
          label: '定单号',
          minWidth: 160,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_ssamc',
          label: '到SSAMC时间',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'is_check_dispname',
          label: '检验状态',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_sn_in',
          label: '回厂序号',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_shipper',
          label: '转包商/采购商',
          minWidth: 200,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_from',
          label: '来源',
          minWidth: 130,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case WAREHOUSING_NODE:
        columns.push(...defaultTableColumn)
        columns.push({
          showColumn: true,
          prop: 'is_abnormal',
          label: '数据状态',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_abnormal',
          label: '状态备注',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_f3_end',
          label: 'F3绩效时间',
          minWidth: 140,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'int_actat',
          label: '实际TAT',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_plan',
          label: '站点要求开工时间',
          minWidth: 200,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_schedule',
          label: '排产时间',
          minWidth: 130,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_planend',
          label: '站点要求完工时间',
          minWidth: 200,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_ycode',
          label: '运单号',
          minWidth: 140,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_billcode',
          label: '定单号',
          minWidth: 160,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_ssamc',
          label: '到SSAMC时间',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case SEND_MATERIAL_NODE:
        columns.push(...defaultTableColumn)
        columns.push({
          showColumn: true,
          prop: 'is_abnormal',
          label: '数据状态',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_abnormal',
          label: '状态备注',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_f3_end',
          label: 'F3绩效时间',
          minWidth: 140,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_schedule',
          label: '排产时间',
          minWidth: 130,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_ttd',
          label: '站点要求完工时间',
          minWidth: 200,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case LIGHT_UP_NODE:
        columns.push(...defaultTableColumn)
        columns.push({
          showColumn: true,
          prop: 'is_abnormal',
          label: '数据状态',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_abnormal',
          label: '状态备注',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_schedule',
          label: '排产时间',
          minWidth: 130,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'dt_planend',
          label: '站点要求完工时间',
          minWidth: 200,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'is_kit_dispname',
          label: '集件状态',
          minWidth: 120,
          field: [{ value: '' }],
          is_select: 'input',
        })
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case F1_2_NODE:
        columns.push(...defaultTableColumnByOther)
        columns.push({
          showColumn: true,
          prop: 'str_abnormal',
          label: '状态备注',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case SUBCONTRACT_PO:
      case PURCHASE_HIGH_AVAILABLE:
      case PURCHASE_LOW_AVAILABLE_NEW:
        columns.push(...defaultTableColumnByOther)
        columns.push({
          showColumn: true,
          prop: 'str_staff',
          label: '责任人',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case LOCK_HIGH_AVAILABLE:
      case LOCK_LOW_AVAILABLE_NEW:
      case LOCK_BACKUP_BOARD:
        columns.push(...defaultTableColumnByOther)
        columns.push({
          showColumn: true,
          prop: 'str_ch_name',
          label: '存货分类',
          minWidth: 170,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      case CSM_CONFIRM_PART:
        columns.push(...defaultTableColumnByOther)
        break
      case CONFIGURATION_PART:
        columns.push(...defaultTableColumnByOther)
        columns.push({
          showColumn: true,
          prop: 'str_type',
          label: '数据来源',
          minWidth: 150,
          field: [{ value: '' }],
          is_select: 'input',
        })
        break
      default:
        columns.push(...defaultTableColumn)
        break
    }
    return columns
  }
  /**
   * 获取PDA表格列
   * @param {string} currentNode 当前节点
   */
  const getTableColumn = (currentNode) => {
    return getTableColumnByCurrentNode(currentNode)
  }

  let is_select = [
    { label: 'YES', value: 1 },
    { label: 'NO', value: 0 },
  ]
  const defaultDMTableColumn = [
    {
      showColumn: true,
      prop: 'int_m',
      label: 'M',
      minWidth: 80,
      fixed: 'left',
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_d',
      label: 'D',
      minWidth: 80,
      fixed: 'left',
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_esn',
      label: '目标ESN',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_wo',
      label: '目标WO',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_wo_ori',
      label: '原台WO',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_sn_ori',
      label: '原台ESN',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'is_sp',
      label: '串件',
      minWidth: 100,
      field: [...new Set(is_select)],
      is_select: 'select',
      formatter: (row, column) => {
        return row.is_sp === 0 ? '否' : '是'
      },
    },
    {
      showColumn: true,
      prop: 'str_engine_type',
      label: 'Engine Type',
      minWidth: 150,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_flow',
      label: 'Flow',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_sm',
      label: 'SM',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_pn',
      label: 'PN',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'str_part_name',
      label: 'PN name',
      minWidth: 150,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_num',
      label: 'Qty',
      minWidth: 100,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'dt_input',
      label: '进入站点时间',
      minWidth: 190,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'dt_plan',
      label: '站点要求开工时间',
      minWidth: 190,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'dt_start',
      label: '实际开工时间',
      minWidth: 190,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'dt_ttd',
      label: '站点要求完工时间',
      minWidth: 190,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'int_tatdays',
      label: '站点标准TAT',
      minWidth: 150,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'is_out',
      label: '输出',
      minWidth: 100,
      field: [...new Set(is_select)],
      is_select: 'select',
      formatter: (row) => {
        return row.is_out === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'is_aog',
      label: 'AOG',
      minWidth: 100,
      field: [...new Set(is_select)],
      is_select: 'select',
      formatter: (row) => {
        return row.is_aog === 1 ? '是' : '否'
      },
    },
    {
      showColumn: true,
      prop: 'str_staff',
      label: '责任人',
      minWidth: 120,
      field: [{ value: '' }],
      is_select: 'input',
    },
    {
      showColumn: true,
      prop: 'id_barcode',
      label: '业务ID',
      minWidth: 120,
      fixed: 'right',
      field: [{ value: '' }],
      is_select: 'input',
    },
  ]
  /**
   * 获取DM表格列表
   * @param {string} currentNode 当前节点
   */
  const getDMTableColumn = (currentNode) => {
    const columns = []
    columns.push(...defaultDMTableColumn)

    if (currentNode === '8') {
      // 进口运输
      columns.push({
        showColumn: true,
        prop: 'str_ycode',
        label: '运单号',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_forwarder',
        label: '运输代理',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_billcode',
        label: '定单号',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_shipper',
        label: '发货人',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_end_air',
        label: '预到机场',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_end_true',
        label: '提单时间',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_ship_receive',
        label: '运代收货时间',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_staff',
        label: '责任人',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
    } else if (currentNode === '12') {
      // 出口运输
      columns.push({
        showColumn: true,
        prop: 'str_billcode',
        label: '定单号',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_forwarder',
        label: '运输代理',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_shipper',
        label: '转包商',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_repairship',
        label: '修理厂',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_begin',
        label: '提货日期',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_begin_air',
        label: '机场出发',
        minWidth: 120,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_end_air',
        label: '到目的机场',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_staff',
        label: '责任人',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
    } else if (currentNode === '15') {
      // f2
      columns.push({
        showColumn: true,
        prop: 'is_state_dispname',
        label: '零件状态',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'int_actat',
        label: '站点实际TAT',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_bcode',
        label: '零件条码',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_tcolocation',
        label: '已处理位置',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_pendinglocation',
        label: '待处理位置',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_notes',
        label: '备注',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
    } else if (currentNode === '13') {
 // 厂家交付
      columns.push({
        showColumn: true,
        prop: 'int_items',
        label: '项数',
        minWidth: 95,
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_billcode',
        label: '定单号',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_edd',
        label: 'L/T Date',
        minWidth: 120,
        field: [
          { label: 'YES', value: 1 },
          { label: 'NO', value: 0 },
        ],
        is_select: 'select',
      })
      // columns.push({
      //   showColumn: true,
      //   prop: 'str_staff',
      //   label: '责任人',
      //   minWidth: 170,
      //   field: [{ value: '' }],
      //   is_select: 'input',
      // })
      columns.push({
        showColumn: true,
        prop: 'str_client',
        label: '客户',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_supplier',
        label: '供应商',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_delivery',
        label: '单元体装配时间',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })

      columns.push({
        showColumn: true,
        prop: 'dt_f3_finish',
        label: 'F3结束时间',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_release_finish',
        label: '放行时间',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_client_show',
        label: '显示客户',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
    } else if (currentNode === '10') {
      // 转包
      columns.push({
        showColumn: true,
        prop: 'int_items',
        label: '项数',
        minWidth: 95,
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      })

      columns.push({
        showColumn: true,
        prop: 'str_sn_out',
        label: '送修序号',
        minWidth: 95,
        field: [{ value: '' }],
        is_select: 'input',
      })
      
      columns.push({
        showColumn: true,
        prop: 'str_ycode',
        label: '运单号',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_billcode',
        label: '定单号',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_shipper',
        label: '转包商',
        minWidth: 190,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_edd',
        label: 'EDD',
        minWidth: 190,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_repairship',
        label: '修理厂',
        minWidth: 170,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_f3_finish',
        label: 'F3结束时间',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'dt_release_finish',
        label: '放行时间',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
      columns.push({
        showColumn: true,
        prop: 'str_client_show',
        label: '显示客户',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      })
    }

    return columns
  }

  return {
    getTableColumn,
    getDMTableColumn,
  }
}
