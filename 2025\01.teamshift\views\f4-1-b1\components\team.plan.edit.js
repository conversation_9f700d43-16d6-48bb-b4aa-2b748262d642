import {
  queryAssembltTaskList,
  querySiteList,
  queryTeamList,
  queryTeamPlanEditList,
  queryTeamSecList,
  saveTeamPlan,
} from '../../../api/teams/index.js'
export default {
  name: 'TeamPlanEdit',
  props: {
    planItem: Object,
    visible: <PERSON><PERSON>an,
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const { reactive, onMounted, ref } = Vue
    const { useVModel } = VueUse
    const teamPlanEditVisible = useVModel(props, 'visible')

    // 团队计划数据
    const planData = reactive({
      name: props.planItem?.name || '',
      scheduleAdjust: '',
      isWholeMove: false,
      planItems: [],
    })

    // 计算计划项数量
    const planItemsCount = ref(0)

    // 保存计划
    const handleSave = async () => {
      const params = []
      for (const item of planData.planItems) {
        let staffs = []
        for (const staff of item.pt_team_plan.id_team_staffs) {
          staffs.push({ id_staff: staff, int_staff_type: 1 })
        }
        for (const staff of item.pt_team_plan.id_team_sec_staffs) {
          staffs.push({ id_staff: staff, int_staff_type: 2 })
        }
        params.push({
          pt_team_plan: {
            id: item.pt_team_plan.id,
            id_main: item.pt_team_plan.id_main,
            id_site: item.pt_team_plan.id_site,
            id_assembly_task: item.pt_team_plan.id_assembly_task,
          },
          pt_team_plan_staffs: staffs,
        })
      }
      await saveTeamPlan(params)
      teamPlanEditVisible.value = false
      emit('refresh')
    }

    const assemblyTaskOptions = ref([])
    // 获取Assembly Task 下拉选择
    const getAssemblyTaskOptions = async (model) => {
      if (!model) return []
      const res = await queryAssembltTaskList(model)
      return res ?? []
    }

    const siteOptions = ref([])
    // 获取Site 下拉选择
    const getSiteOptions = async () => {
      const res = await querySiteList()
      siteOptions.value = res ?? []
    }

    const planMainItem = ref(null)

    // 获取编辑页的数据
    const getTeamPlanEditList = async () => {
      try {
        const res = await queryTeamPlanEditList(props.planItem.planId)
        if (!res || !res.pt_team_plan_dto) return
        
        planMainItem.value = res.pt_team_plan_dto.pt_main
        
        // 处理团队计划数据
        const planItems = await Promise.all((res.pt_team_plan_dto.pt_team_plans || []).map(async (item) => {
          // 构建基本数据结构
          const planItem = {
            pt_team_plan: {
              ...item.pt_team_plan,
              id_team_staffs: item.pt_team_plan_staffs
                .filter(staff => staff.int_staff_type === '1')
                .map(staff => staff.id_staff),
              id_team_sec_staffs: item.pt_team_plan_staffs
                .filter(staff => staff.int_staff_type === '2')
                .map(staff => staff.id_staff),
            },
            shift_times: [item.pt_team_plan.dt_shift_start, item.pt_team_plan.dt_shift_end],
            assemblyTaskOptions: [],
            thirdTasks: item.thirdTasks,
          }
          
          // 获取Assembly Task选项
          if (item.pt_team_plan.str_sm) {
            planItem.assemblyTaskOptions = await getAssemblyTaskOptions(item.pt_team_plan.str_sm)
          }
          
          return planItem
        }))
        
        planData.planItems = planItems
        planItemsCount.value = planItems.length
      } catch (error) {
        console.error('获取团队计划编辑数据失败:', error)
      }
    }

    const teamOptions = ref([])
    // 获取团队选项
    const getTeamOptions = async () => {
      const params = {
        dt_date: planMainItem.value.pt_dt,
        id_main: props.planItem.planId,
        id_team: planMainItem.value.id_team,
        str_flow: planMainItem.value.str_flow,
      }
      const res = await queryTeamList(params)
      teamOptions.value = res ?? []
    }

    // 获取Team Sec 选项
    const teamSecOptions = ref([])
    const getTeamSecOptions = async () => {
      const params = {
        id_main: props.planItem.planId,
        id_team: planMainItem.value.id_team,
        pt_dt:props.planItem.plan_date
      }
      const res = await queryTeamSecList(params)
      teamSecOptions.value = res ?? []
    }
    const handleChangeTeamSec = async (teamId) => {
      for (const staff of teamId.pt_team_plan.id_team_sec_staffs) {
        var hour = teamSecOptions.value.filter(item => item.id === staff)[0].dbl_hour
        if(hour >=120){        
          ElementPlus.ElMessage.error("员工时间银行已超120小时,不能排班")
          teamId.pt_team_plan.id_team_sec_staffs = teamId.pt_team_plan.id_team_sec_staffs.filter(id => id !== staff)          
        }
        if(hour >=84){        
          ElementPlus.ElMessage.warning("员工时间银行已超84小时,请注意") 
        }
      }
      
    }
    const handleChangeTeam = async (teamId) => {
      for (const staff of teamId.pt_team_plan.id_team_staffs) {
        var hour = teamOptions.value.filter(item => item.id === staff)[0].dbl_hour
        if(hour >=120){
          ElementPlus.ElMessage.error("员工时间银行已超120,不能排班")
          teamId.pt_team_plan.id_team_staffs = teamId.pt_team_plan.id_team_staffs.filter(id => id !== staff)
        }
        if(hour >=84){        
          ElementPlus.ElMessage.warning("员工时间银行已超84小时,请注意")  
        }
      }
     
      
    }

    onMounted(async () => {
      await getTeamPlanEditList()
      await getSiteOptions()
      await getTeamOptions()
      await getTeamSecOptions()
    })

    return {
      teamPlanEditVisible,
      planData,
      planItemsCount,
      handleSave,
      getAssemblyTaskOptions,
      assemblyTaskOptions,
      siteOptions,
      teamOptions,
      teamSecOptions,
      handleChangeTeamSec,
      handleChangeTeam,
    }
  },
  template: /*html*/ `
    <el-dialog
      v-model="teamPlanEditVisible"
      title="Team Plan"
      width="80%"
      class="common-dialog"
      :fullscreen="false"
      :append-to-body="true"
    >
      <div class="pr-2">
        <el-form :model="planData" label-width="150px">
          <div class="my-2">
            <el-badge :value="planItemsCount" class="item" type="info">
              <el-button>Num</el-button>
            </el-badge>
          </div>

          <div
            v-for="(item, index) in planData.planItems"
            :key="item.id"
            class="my-2 flex flex-col rounded-md border p-4"
          >
            <div class="grid grid-cols-1 gap-4 pt-2 md:grid-cols-3">
              <el-form-item label="Task" required>
                <el-input v-model="item.pt_team_plan.str_task" placeholder="请输入任务" class="w-full" disabled />
              </el-form-item>

              <el-form-item label="Task description">
                <el-input v-model="item.pt_team_plan.str_task_name" placeholder="请输入任务描述" />
              </el-form-item>

              <el-form-item label="Model">
                <el-input v-model="item.pt_team_plan.str_sm" placeholder="请输入型号" class="w-full" disabled />
              </el-form-item>
            </div>

            <div class="grid grid-cols-1 gap-4 pt-2 md:grid-cols-3">
              <el-form-item label="Assembly task">
                <el-select
                  v-model="item.pt_team_plan.id_assembly_task"
                  placeholder="请选择装配任务"
                >
                  <el-option
                    v-for="item in item.assemblyTaskOptions"
                    :key="item.id"
                    :label="item.str_name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="Site">
                <el-select v-model="item.pt_team_plan.id_site" placeholder="请选择站点" class="w-full">
                  <el-option v-for="item in siteOptions" :key="item.id" :label="item.str_name" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="Team">
                <el-select
                  v-model="item.pt_team_plan.id_team_staffs"
                  multiple
                  placeholder="请选择团队"
                  @change="handleChangeTeam(item)"
                >
                  <el-option v-for="item in teamOptions" :key="item.id" :label="item.str_name+'('+item.dbl_hour+'H)'" :value="item.id" :style="{ color: item.dbl_hour >= 120 ? 'red' :item.dbl_hour > 84 ? '#DAA520' : 'black'}"/>
                </el-select>
              </el-form-item>
            </div>

            <div class="grid grid-cols-1 gap-4 pt-2 md:grid-cols-3">
              <el-form-item label="Team Sec">
                <el-select
                  v-model="item.pt_team_plan.id_team_sec_staffs"
                  multiple
                  placeholder="请选择团队负责人"
                  @change="handleChangeTeamSec(item)"
                >
                  <el-option v-for="item in teamSecOptions" :key="item.id" :label="item.str_name+'('+item.dbl_hour+'H)'" :value="item.id"  :style="{ color: item.dbl_hour >= 120 ? 'red' : item.dbl_hour > 84 ? '#DAA520' : 'black' }"/>
                </el-select>
              </el-form-item>

              <el-form-item label="Shift" required>
                <el-input v-model="item.pt_team_plan.shift_name" placeholder="请输入班次" class="w-full" disabled />
              </el-form-item>
            </div>

            <div class="grid grid-cols-1 gap-4 pt-2 md:grid-cols-1">
              <el-form-item label="Shift time">
                <el-time-picker
                  v-model="item.shift_times"
                  is-range
                  range-separator="To"
                  start-placeholder="Start time"
                  end-placeholder="End time"
                  value-format="HH:mm"
                  format="HH:mm"
                  disabled
                />
              </el-form-item>
            </div>
            <!-- <el-form-item label="Third Task" class="pt-2">
              <el-transfer
                v-model="item.completedThirdTasks"
                :data="item.thirdTasks"
                :titles="['可选任务', '已完成任务']"
                filterable
                :props="{
                  key: 'id',
                  label: 'str_task',
                }"
                filter-placeholder="搜索任务"
              />
            </el-form-item> -->
            <el-form-item label="Remark" class="pt-2">
              <el-input v-model="item.pt_team_plan.str_remark" type="textarea" :rows="3" placeholder="请输入备注" />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="flex justify-end">
          <el-button @click="teamPlanEditVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>
  `,
}
