import { post } from '../../../utils/request.js'
/**
 * @typedef {Object} FilterParams
 * @property {string} apply_user 申请人
 * @property {string} dt_up 申请时间
 * @property {string} dt_mes_start 开始时间 格式: 2024-01-01
 * @property {string} dt_mes_end 结束时间 格式: 2024-01-01
 * @property {string} str_apply_reason 申请事由
 * @property {string} str_remark 备注
 * @property {string} int_substatus 审批状态
 * @property {string} str_audit_manage 审批人
 * @property {string} str_mes_manage MES审批人
 * @property {string} staff_name MES人员
 */

/**
 * 获取MES申请列表
 * @param {FilterParams} params
 */
export function getMesApplyList(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_mes_apply_list',
    Filter: params,
  })
}

/**
 * 获取单个申请详情
 * @param {string} id 申请ID
 */
export function getMesApplyById(id) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_mes_apply_by_id',
    id: id,
  })
}

/**
 * 删除MES申请
 * @param {string} id 申请ID
 */
export function deleteMesApply(id) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_delete_mes_apply',
    id: id,
  })
}

/**
 * 提交申请审批
 * @param {string} id 申请ID
 */
export function submitMesApplyForApproval(id) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_commit_mes_apply',
    id: id,
  })
}

/**
 * 确认MES申请
 * @param {string} id 申请ID
 */
export function confirmMesApply(id) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_confirm_mes_apply',
    id: id,
  })
}

/**
 * @typedef {object} mesApply
 * @property {string} id
 * @property {string} id_staff
 * @property {string} staff_name
 * @property {string} dt_mes_start
 * @property {string} dt_mes_end
 * @property {string} int_mes_day
 * @property {string} str_apply_reason
 * @property {string} str_remark
 * @property {string} str_audit_manage
 * @property {string} str_mes_manage
 * @property {number} int_substatus
 */

/**
 * @typedef {object} mesApplySubs
 * @property {string} dt_date
 * @property {string} dt_start_time
 * @property {string} dt_end_time
 * @property {string} int_apply_hours
 */

/**
 * @typedef {object} saveParams
 * @property {mesApply} mesApply
 * @property {mesApplySubs[]} mesApplySubs
 */
/**
 * 创建和更新MES申请
 * @param {saveParams} data
 */
export function createAndUpdateMesApply(data) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_mes_apply',
    postdata: data,
  })
}

/**
 * 获取人员下拉列表
 * @returns
 */
export const getStaffList = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_section_user_list',
  })
}

/**
 * 获取审批人列表
 * @returns
 */
export const getApproverList = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_approver_list',
  })
}

/**
 * 撤回MES申请
 * @param {string} id 申请ID
 */
export function withdrawMesApply(id) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_withdraw_mes_apply',
    id: id,
  })
}

/**
 * 审批通过MES申请
 * @param {string} id 申请ID
 */
export function approveMesApply(id) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_audit_mes_apply',
    id: id,
    status: 1,
    str_content: '',
  })
}

/**
 * 审批驳回MES申请
 * @param {string} id 申请ID
 * @param {string} rejectReason 驳回原因
 */
export function rejectMesApply(id, rejectReason) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_audit_mes_apply',
    id: id,
    status: -1,
    str_content: rejectReason,
  })
}

/**
 * 确认保存MES申请（包含确认时间和确认工时）
 * @param {Array} data 确认数据
 */
export function confirmSaveMesApply(data) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_confirm_mes_apply',
    postdata: data,
  })
}
