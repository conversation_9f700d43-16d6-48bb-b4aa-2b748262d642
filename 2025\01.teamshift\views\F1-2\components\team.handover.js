import { saveHandover, uploadFile, queryHandover } from '../../../api/handover/index.js'
const { onMounted } = Vue
export default {
  name: 'TeamHandover',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    teamId:String,
    row: {
      type: Object,
      default: () => {},
    },
    planItem: {
      type: Object,
      default: () => {},
    },
  },
  emits: ['update:visible'],
  setup(props) {
    const { ref, useModel } = Vue
    const { ElMessage, ElLoading } = ElementPlus
    const pThandoverMainid = ref('')
    // const baseInfo = ref({
    //   esn: props.row.esn,
    //   wo: props.row.wo,
    //   type: props.row.str_task_type,
    //   team: props.row.team,
    //   leader: props.row.leader,
    //   shift: props.row.shift,
    // })

    const baseInfo = ref([]);
    // 抽屉可见性控制
    const drawerVisible = ref(false)
    // 当前选中的文件列表
    const currentFiles = ref([])

    // 计算每个Legend的行数，用于合并单元格
    const getSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1) {
        // Code和Legend列
        const rows = tableMap.value[row.id_model] || []
        if (!rows.length) return { rowspan: 1, colspan: 1 }
        
        const currentCode = row.code

        // 向上找到第一个具有相同code的行的索引
        let startIndex = rowIndex
        while (startIndex > 0 && rows[startIndex - 1].code === currentCode) {
          startIndex--
        }

        // 如果是该分组的第一行
        if (rowIndex === startIndex) {
          // 计算具有相同code的行数
          let spanCount = 1
          let nextIndex = rowIndex + 1
          while (nextIndex < rows.length && rows[nextIndex].code === currentCode) {
            spanCount++
            nextIndex++
          }
          return {
            rowspan: spanCount,
            colspan: 1,
          }
        } else {
          // 不是第一行则隐藏
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
      return { rowspan: 1, colspan: 1 }
    }

    // 获取交接单
    const getHandover = async () => {
      try {

        const wos = Array.from(new Set(props.planItem.task.map((item) => item.id_wo))).join(',');
        //const sms = Array.from(new Set(props.planItem.task.map((item) => item.modelId))).join(',')
        const param = {
          id_wo: wos,
          id_team: props.teamId,
          dt_pt: props.planItem.dt_shift
        }

        baseInfo.value = props.planItem.task.map((item) => ({
          esn: item.str_esn,
          wo: item.str_wo,
          type: item.str_type,
          id:item.id_f2_target,
           team: item.str_esn,
           leader: item.id_staff,
          shift: item.dt_shift
        }));

        
        const res = await queryHandover(param)
        
        // 保存主表ID
        pThandoverMainid.value = res.pTHandoverMain?.id || ''
        
        // 1. 首先构建SM列表
        const smMap = new Map()
        props.planItem.task.forEach(task => {
          if (!smMap.has(task.id_wo)) {
            smMap.set(task.id_wo, {
              id: task.id_wo,
              name: task.str_wo || task.str_esn  || '未命名wo'
            })
          }
        })
        smTabList.value = Array.from(smMap.values())

        // 2. 初始化tableMap
        const newTableMap = {}
        smTabList.value.forEach(sm => {
          newTableMap[sm.id] = []
        })

        // 3. 处理交接数据
        if (res.pTHandovers && Array.isArray(res.pTHandovers)) {
          // 先构建一个SM名称映射
          const smNameMap = new Map()
          res.pTHandovers.forEach(handover => {
            if (handover.pTHandover.id_model && handover.pTHandover.str_sm) {
              smNameMap.set(handover.pTHandover.id_model, handover.pTHandover.str_sm)
            }
          })

          // 更新SM列表的名称
          smTabList.value = smTabList.value.map(sm => ({
            ...sm,
            name: smNameMap.get(sm.id) || sm.name
          }))

          res.pTHandovers.forEach(handover => {
            const item = {
              id: handover.pTHandover.id || '',
              code: handover.pTHandover.int_type || 0,
              legend: handover.pTHandover.str_category || '',
              tip: handover.pTHandover.str_help || '暂无信息',
              desc: handover.pTHandover.str_content || '',
              isTransferred: handover.pTHandover.int_status === 1,
              isOriginal: false,
              attachment: handover.files || [],
              id_model: handover.pTHandover.id_model,
              str_sm: handover.pTHandover.str_sm || ''
            }

            // 将数据添加到对应的SM分组中
            if (newTableMap[item.id_model]) {
              newTableMap[item.id_model].push(item)
            }
          })

          // 4. 处理每个SM分组的数据
          Object.keys(newTableMap).forEach(smId => {
            // 按code排序
            newTableMap[smId].sort((a, b) => a.code - b.code)

            // 标记每个code组中的第一条记录为原始记录
            const codeGroups = new Map()
            newTableMap[smId].forEach(item => {
              if (!codeGroups.has(item.code)) {
                codeGroups.set(item.code, [])
              }
              codeGroups.get(item.code).push(item)
            })

            codeGroups.forEach(group => {
              if (group.length > 0) {
                // 将第一条记录标记为原始行
                group[0].isOriginal = true
              }
            })
          })
        }

        // 5. 更新tableMap
        tableMap.value = newTableMap

        // 6. 设置表头提示信息
        headerTips.value = res.str_title || ''

      } catch (error) {
        ElMessage.error('获取交接单数据失败：' + (error.message || '未知错误'))
      }
    }

    // 删除行
    const handleDeleteRow = (row) => {
      const currentSmData = tableMap.value[row.id_model]
      if (!currentSmData) {
        ElMessage.warning('当前 SM 数据无效')
        return
      }

      const index = currentSmData.findIndex((item) => item === row)
      if (index > -1) {
        // 创建当前 SM 数据的副本
        const updatedSmData = [...currentSmData]

        // 如果是原始行，需要检查是否有其他相同code的行
        if (row.isOriginal) {
          const currentCode = row.code
          // 删除当前行
          updatedSmData.splice(index, 1)

          // 查找相同code的所有行
          const sameCodeRows = updatedSmData.filter((item) => item.code === currentCode)
          if (sameCodeRows.length > 0) {
            // 将第一行设置为原始行
            sameCodeRows[0].isOriginal = true
          }
        } else {
          // 不是原始行直接删除
          updatedSmData.splice(index, 1)
        }

        // 更新 tableMap
        tableMap.value = {
          ...tableMap.value,
          [row.id_model]: updatedSmData
        }

        ElMessage.success('删除成功')
      }
    }

    // 新增行
    const handleAddRow = (row) => {
      if (!row?.code || !row?.legend) {
        ElMessage.warning('无效的行数据')
        return
      }

      const currentSmData = tableMap.value[row.id_model]
      if (!currentSmData) {
        ElMessage.warning('当前 SM 数据无效')
        return
      }

      // 创建新行，使用解构赋值确保数据结构一致性
      const newRow = {
        ...row,
        id: '', // 清空ID，因为这是新行
        desc: '',
        attachment: [],
        isExpanded: false,
        isOriginal: false,
        isTransferred: false,
      }

      // 找到相同code的所有行
      const sameCodeRows = currentSmData.filter((item) => item.code === row.code)

      // 计算新行应该插入的位置
      const insertIndex = currentSmData.findIndex((item) => item.code > row.code)

      // 更新当前 SM 的数据
      const updatedSmData = [...currentSmData]
      if (insertIndex === -1) {
        // 如果没有找到更大的code，则添加到末尾
        updatedSmData.push(newRow)
      } else {
        // 在正确的位置插入新行
        updatedSmData.splice(insertIndex + sameCodeRows.length, 0, newRow)
      }

      // 更新 tableMap
      tableMap.value = {
        ...tableMap.value,
        [row.id_model]: updatedSmData
      }

      ElMessage.success('新增行成功')
    }

    // 定义允许的文件类型和大小限制
    const ALLOWED_FILE_TYPES = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'text/plain',
      'application/zip',
      'application/x-rar-compressed',
    ]
    const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

    // 检查文件安全性
    const checkFileSecurity = (file) => {
      // 检查文件类型
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        ElMessage.error(`不支持的文件类型: ${file.name}`)
        return false
      }

      // 检查文件大小
      if (file.size > MAX_FILE_SIZE) {
        ElMessage.error(
          `文件过大 (${(file.size / 1024 / 1024).toFixed(2)}MB), 最大限制 ${MAX_FILE_SIZE / 1024 / 1024}MB: ${file.name}`,
        )
        return false
      }

      // 检查文件名安全性
      const dangerousChars = /[<>:"/\\|?*\x00-\x1F]/g // 禁止包含的特殊字符
      const consecutiveDots = /\.{2,}/g // 禁止连续的点
      const reservedNames = /^(con|prn|aux|nul|com[0-9]|lpt[0-9])$/i // Windows保留文件名

      if (dangerousChars.test(file.name)) {
        ElMessage.error(`文件名包含非法字符: ${file.name}`)
        return false
      }

      if (consecutiveDots.test(file.name)) {
        ElMessage.error(`文件名不能包含连续的点: ${file.name}`)
        return false
      }

      const nameWithoutExt = file.name.split('.')[0].toLowerCase()
      if (reservedNames.test(nameWithoutExt)) {
        ElMessage.error(`文件名不能使用系统保留名: ${file.name}`)
        return false
      }

      return true
    }

    // 上传文件
    const handleUpload = async (row) => {
      try {
        const input = document.createElement('input')
        input.type = 'file'
        input.multiple = true
        input.accept = ALLOWED_FILE_TYPES.join(',')

        input.onchange = async (e) => {
          const files = Array.from(e.target.files)
          if (files.length) {
            // 过滤安全的文件
            const safeFiles = files.filter((file) => checkFileSecurity(file))

            if (safeFiles.length === 0) {
              ElMessage.warning('没有符合要求的文件可以上传')
              return
            }

            if (safeFiles.length !== files.length) {
              ElMessage.warning('部分文件未通过安全检查，已被过滤')
            }

            try {
              // 显示上传加载状态
              const loading = ElLoading.service({
                lock: true,
                text: '文件上传中...',
                background: 'rgba(0, 0, 0, 0.7)',
              })

              // 并行上传所有文件
              const uploadPromises = safeFiles.map(async (file) => {
                const formData = new FormData()
                formData.append('file', file)
                formData.append('type', 'handover') // 添加上传类型
                formData.append('esn', props.row.esn || '') // 添加ESN
                formData.append('wo', props.row.wo || '') // 添加工单号

                try {
                  const res = await uploadFile(formData)
                  return {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    uploadTime: new Date().toLocaleString(),
                    str_path: res.path || '', // 保存返回的文件路径
                    str_file_name: file.name, // res.data.filename || file.name // 保存返回的文件名
                  }
                } catch (error) {
                  ElMessage.error(`文件 ${file.name} 上传失败: ${error.message || '未知错误'}`)
                  return null
                }
              })

              // 等待所有文件上传完成
              const results = await Promise.all(uploadPromises)

              // 过滤掉上传失败的文件
              const successFiles = results.filter((result) => result !== null)

              // 更新行数据
              successFiles.forEach((file) => {
                row.attachment?.push(file)
              })

              loading.close()

              if (successFiles.length > 0) {
                ElMessage.success(`成功上传 ${successFiles.length} 个文件`)
              } else {
                ElMessage.error('所有文件上传失败')
              }
            } catch (error) {
              ElMessage.error('文件上传过程中发生错误: ' + (error.message || '未知错误'))
            }
          }
        }
        input.click()
      } catch (error) {
        ElMessage.error('文件上传失败: ' + (error.message || '未知错误'))
      }
    }

    // 查看文件
    const handleViewFiles = (row) => {
      currentFiles.value = row.attachment || []
      drawerVisible.value = true
    }

    /**保存交接 */
    const handleSaveHandover = async () => {
      try {
        // 获取所有 SM 的数据
        const allData = Object.values(tableMap.value).flat()
        if (!allData || allData.length === 0) {
          ElMessage.error('没有可提交的数据')
          return
        }

        // 验证数据
        const invalidRows = allData.filter((item) => {
          // 如果勾选了无交接，则跳过验证
          if (item.isTransferred) {
            return false
          }

          // 检查描述内容和文件
          const hasContent = item.desc && item.desc.trim() !== ''
          const hasFiles = item.attachment && item.attachment.length > 0

          return !hasContent && !hasFiles
        })

        if (invalidRows.length > 0) {
          // 简化错误消息，只显示需要完善的行号
          const errorCodes = invalidRows.map((row) => `${row.code}`).join('、')
          ElMessage.error(`请完善 Code ${errorCodes} 的内容或附件`)
          return
        }

        // 构建主表数据
        const main = {
          id: pThandoverMainid.value,
          id_wo: props.row.id_wo || '',
          str_wo: props.row.wo || '',
          id_team: props.row.id_team || '',
          dt_pt: props.planItem.plan_date,
        }

        // 转换表格数据为后端需要的格式
        const handovers = allData.map((item) => ({
          pTHandover: {
            id: item.id || '', // 交接单ID
            id_model: item.id_model, // SM ID
            str_sm: item.str_sm, // SM 名称
            int_type: item.code, // 使用code作为类型
            str_category: item.legend, // 类别
            str_help: item.tip, // 提示信息
            str_content: item.desc || '', // 描述内容
            int_status: item.isTransferred ? 1 : 0, // 是否交接状态
          },
          files: (item.attachment || []).map((file) => ({
            id: file.id || '',
            str_path: file.str_path,
            str_file_name: file.str_file_name || file.name,
          })),
        }))

        const postData = {
          pTHandoverMain: main,
          pTHandovers: handovers,
        }

        await saveHandover(postData)
        ElMessage.success('交接单保存成功')
        dialogVisible.value = false // 保存成功后关闭弹窗
      } catch (error) {
        ElMessage.error('保存失败：' + (error.message || '未知错误'))
      }
    }

    // 弹窗可见性控制
    const dialogVisible = useModel(props, 'visible')

    const handleCloseDialog = () => {
      dialogVisible.value = false
    }

    // 表头提示信息
    const headerTips = ref('')

    // 表格的Map
    const tableMap = ref({})

    // 下载文件
    const handleDownload = (row) => {
      const loading = ElLoading.service({
        lock: true,
        text: '正在下载...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      try {
        // 不用window.open，而是用a标签下载
        const a = document.createElement('a')
        a.href = row.str_path
        a.download = row.str_file_name
        a.click()
        a.remove()
      } catch (error) {
        ElMessage.error('下载失败：' + (error.message || '未知错误'))
      } finally {
        loading.close()
      }
    }

    // 定义SM的TabList
    const smTabList = ref([])
    // 当前选中的SM
    const activeTab = ref(0)
    // 切换SM
    const handleTabChange = (tab) => {
      activeTab.value = tab
    }

    onMounted(() => {
      getHandover()
    })

    return {
      baseInfo,
      dialogVisible,
      drawerVisible,
      currentFiles,
      headerTips,
      smTabList,
      tableMap,
      handleAddRow,
      handleUpload,
      handleViewFiles,
      handleDeleteRow,
      getSpanMethod,
      handleSaveHandover,
      handleCloseDialog,
      handleDownload,
    }
  },
  template: /*html*/ `
    <el-drawer
      v-model="dialogVisible"
      title="交接记录表"
      size="80%"
      class="common-drawer"
      :show-close="false"
      :append-to-body="true"
    >
      <!-- 表头提示信息 -->
      <div class="mb-4 rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4 shadow-sm">
        <div class="flex items-center">
          <div class="flex w-full cursor-help items-center">
            <el-icon class="mr-2 flex-shrink-0" :size="20"><InfoFilled /></el-icon>
            <div class="min-w-0 flex-1">
              <div class="mb-1 text-lg font-semibold text-red-700">交接提示</div>
              <div class="whitespace-pre-line text-red-600">{{ headerTips }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 基本信息 -->
      <template v-for="sm in smTabList" :key="sm.id">
      <div class="mb-6">
        <el-descriptions :column="5" border>
          <el-descriptions-item label="ESN">{{ wo.esn }}</el-descriptions-item>
          <el-descriptions-item label="WO">{{ wo.wo }}</el-descriptions-item>
          <el-descriptions-item label="Type">{{ wo.type }}</el-descriptions-item>
          <el-descriptions-item label="组长">{{ wo.leader }}</el-descriptions-item>
          <el-descriptions-item label="班次">{{ wo.shift }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 分区域展示各个SM的数据 -->
      <div class="flex flex-col gap-6 overflow-auto">
          <div class="rounded-lg border border-gray-200 bg-white shadow">
            <!-- SM内容区域 -->
            <div class="p-4">
              <el-table 
                :data="tableMap[wo.id]" 
                border 
                :span-method="getSpanMethod"
                size="small"
                class="w-full"
                v-if="tableMap[wo.id] && tableMap[wo.id].length > 0"
              >
                <el-table-column prop="code" label="Code" width="80" align="center" />
                <el-table-column prop="legend" label="Legend" width="120">
                  <template #default="{ row }">
                    <div class="flex items-center">
                      <div class="min-w-0 flex-1">{{ row.legend }}</div>
                      <el-tooltip effect="dark" placement="top" :content="row.tip">
                        <el-icon class="ml-1 text-gray-400" :size="16"><InfoFilled /></el-icon>
                      </el-tooltip>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="desc" label="Desc" min-width="300">
                  <template #default="{ row }">
                    <el-input 
                      type="textarea" 
                      v-model="row.desc" 
                      :rows="2" 
                      placeholder="请输入内容"
                      resize="none"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="Attachment" width="100" align="center">
                  <template #default="{ row }">
                    <div class="flex items-center justify-center space-x-2">
                      <el-button type="primary" link @click="handleUpload(row)">
                        <el-icon><Upload /></el-icon>
                      </el-button>
                      <el-button
                        v-if="row.attachment?.length > 0"
                        type="primary"
                        link
                        @click="handleViewFiles(row)"
                      >
                        <el-icon><View /></el-icon>
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="Operate" width="120" align="center">
                  <template #default="{ row }">
                    <div class="flex items-center justify-center space-x-2">
                      <el-button 
                        v-if="!row.isOriginal" 
                        type="danger" 
                        link 
                        @click="handleDeleteRow(row)"
                      >
                        删除
                      </el-button>
                      <el-button 
                        v-if="row.isOriginal" 
                        type="primary" 
                        link 
                        @click="handleAddRow(row)"
                      >
                        新增
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="无交接" width="80" align="center">
                  <template #default="{ row }">
                    <el-checkbox v-model="row.isTransferred" />
                  </template>
                </el-table-column>
              </el-table>
              <div v-else class="flex h-32 items-center justify-center text-gray-500">
                暂无数据
              </div>
            </div>
          </div>
        
      </div>
      </template>
      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button type="danger" @click="handleCloseDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveHandover">提交</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 文件查看抽屉 -->
    <el-drawer v-model="drawerVisible" title="文件列表" size="50%" direction="rtl">
      <el-table :data="currentFiles" border size="small">
        <el-table-column prop="str_file_name" label="文件名">
          <template #default="{ row }">{{ row.str_file_name.split('.')[0] }}</template>
        </el-table-column>
        <el-table-column prop="str_type" label="类型" width="100">
          <template #default="{ row }">{{ row.str_file_name.split('.')[1] }}</template>
        </el-table-column>
        <el-table-column prop="dt_up" label="上传时间" width="180" />
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleDownload(row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
  `,
}
