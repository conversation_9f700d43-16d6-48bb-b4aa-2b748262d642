const { ref, onMounted, onUnmounted, nextTick, watch, computed } = Vue
const { ElMessage } = ElementPlus
import {
  getTeamShiftCalendarPage,
  get_pt_get_shift_people,
  pt_save_staffshift,
  get_pt_get_shift,
} from '../../../api/shift.calendar.team/index.js'
import { getCommonEnumList } from '../../../api/comm/index.js'
import { getTeams } from '../../../api/borrow/index.js'

// 定义节假日数据
const HOLIDAY_DATA = {}

// 获取节假日数据
const getHolidays = (year) => {
  const holidays = {}
  Object.entries(HOLIDAY_DATA).forEach(([date, info]) => {
    if (date.startsWith(year)) {
      holidays[date] = info
    }
  })
  return holidays
}

export default {
  name: 'F2ShiftCalendar',
  props: {
    dept: {
      type: String,
      default: '',
      required: true,
    },
    isBatchScheduling: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    // 加载状态
    const loading = ref(true)

    // 节假日数据
    const holidays = ref({})

    // 当前选择的月份
    const currentMonth = ref(moment().format('YYYY-MM'))

    // 搜索表单
    const searchForm = ref({
      month: currentMonth.value,
      str_name: '',
    })

    // 分页相关状态
    const pagination = ref({
      currentPage: 1,
      pageSize: 10,
      total: 0,
    })

    // 表格高度
    const tableHeight = ref('400px')
    const tableContainerRef = ref(null)

    // 添加批量排班相关状态
    const batchDialogVisible = ref(false)
    const selectedTeamId = ref('')
    const selectedShiftTypeForBatch = ref('')
    const batchSelectedDate = ref(moment().format('YYYY-MM-DD'))
    const teamOptions = ref([])
    const teamStaffList = ref([])

    // 计算表格高度
    const calculateTableHeight = () => {
      nextTick(() => {
        // 获取窗口高度
        let windowHeight = window.innerHeight

        // 检查是否在iframe中
        const isInIframe = window !== window.parent

        if (isInIframe) {
          try {
            // 尝试获取iframe的高度
            const iframe = window.frameElement
            if (iframe) {
              windowHeight = iframe.clientHeight
            }
          } catch (e) {
            console.error('获取iframe高度失败:', e)
          }
        }

        // 获取表格容器元素
        const tableContainer = tableContainerRef.value
        if (!tableContainer) return

        // 获取表格容器到视口顶部的距离
        const containerTop = tableContainer.getBoundingClientRect().top

        // 获取分页器高度
        const paginationHeight = 60

        // 计算表格可用高度 (窗口高度 - 表格顶部位置 - 分页器高度 - 底部边距)
        const availableHeight = windowHeight - containerTop - paginationHeight - 20

        // 设置最小高度
        const minHeight = 300

        // 更新表格高度
        tableHeight.value = `${Math.max(availableHeight, minHeight)}px`
      })
    }
    // 添加弹框相关的状态
    const dialogVisible = ref(false)
    const selectedShift = ref(null)
    const selectedDate = ref(null)
    const selectedTeam = ref(null)
    const selectedShiftType = ref('')
    const selectedStaffStatus = ref('0')
    /**是否重排 */
    const isReschedule = ref(false)
    const shiftTypeOptions = ref([])
    const isShiftDisabled = ref(false)

    const fetchShiftTypeOptions = async () => {
      const response = await get_pt_get_shift()
      shiftTypeOptions.value = response.data
    }

    const staffStatusOptions = ref([])
    // 获取人员状态选项
    const fetchStaffStatusOptions = async () => {
      const response = await getCommonEnumList('pt_staff_status')
      staffStatusOptions.value = response.map((item) => ({
        str_name: item.str_name,
        id: item.str_code,
      }))
    }

    onMounted(async () => {
      await fetchShiftTypeOptions()
      await fetchStaffStatusOptions()
    })

    // 获取节假日数据

    // 班次类型定义
    const shiftTypes = {
      早班: { class: 'bg-green-500 text-white', text: '早' },
      晚班: { class: 'bg-blue-500 text-white', text: '晚' },
      白班: { class: 'bg-white text-black', text: '日' },
      休息: { class: 'bg-red-500 text-white', text: '休' },
    }

    // 团队成员数据
    const teamMembers = ref([])

    // 获取月份的起始日期和结束日期
    const getMonthRange = (dateStr) => {
      const momentObj = moment(dateStr, 'YYYY-MM')
      return {
        start: momentObj.clone().startOf('month'),
        end: momentObj.clone().endOf('month'),
        year: momentObj.year(),
        month: momentObj.month() + 1,
      }
    }

    // 更新节假日数据
    const updateHolidays = (year) => {
      holidays.value = getHolidays(year)
    }

    // 计算当前月的日期数组
    const handleDateRange = () => {
      const monthRange = getMonthRange(currentMonth.value)
      const dates = []
      const current = monthRange.start.clone()
      const end = monthRange.end.clone()

      while (current.isSameOrBefore(end, 'day')) {
        const dateStr = current.format('YYYY-MM-DD')
        const holiday = holidays.value[dateStr]
        dates.push({
          date: dateStr,
          day: current.date(),
          weekday: '日一二三四五六'.charAt(current.day()),
          isWeekend: current.day() === 0 || current.day() === 6,
          isHoliday: holiday?.type === 'holiday',
          isWorkday: holiday?.type === 'workday',
          holidayName: holiday?.name,
          isToday: current.isSame(moment(), 'day'),
          weekNumber: current.week(),
        })
        current.add(1, 'day')
      }
      return dates
    }

    // 计算表头数据
    const handleHeaderData = () => {
      const weeks = []
      const dates = handleDateRange()
      let currentWeek = null
      let weekSpan = 0
      let weekDates = []

      dates.forEach((date, index) => {
        const momentDate = moment(date.date)
        const weekNumber = momentDate.week()

        if (currentWeek === null) {
          currentWeek = weekNumber
        }

        if (currentWeek !== weekNumber) {
          weeks.push({
            weekNumber: currentWeek,
            span: weekSpan,
            dates: [...weekDates],
          })
          weekSpan = 0
          weekDates = []
          currentWeek = weekNumber
        }

        weekSpan++
        weekDates.push(date)

        // 处理最后一周
        if (index === dates.length - 1) {
          weeks.push({
            weekNumber: currentWeek,
            span: weekSpan,
            dates: [...weekDates],
          })
        }
      })

      return {
        weeks,
        dates,
      }
    }

    // 表格数据
    const tableData = ref([])
    const headerData = ref({})

    // 获取表格数据
    const getTableData = async () => {
      headerData.value = handleHeaderData()
      loading.value = true
      try {
        const params = {
          str_staff_code: searchForm.value.str_staff_code,
          str_dept: props.dept,
          str_name: searchForm.value.str_name,
          currentPage: pagination.value.currentPage,
          pageSize: pagination.value.pageSize,
          str_team: searchForm.value.str_team,
          str_orderby: orderBy.value,
          str_dept_name: searchForm.value.str_dept_name,
          str_year: moment(currentMonth.value).year(),
          str_month: moment(currentMonth.value).month() + 1,
        }

        const response = await getTeamShiftCalendarPage(params)

        if (response && response.items) {
          tableData.value = response.items.map((item) => ({
            id: item.id_staff,
            str_team: item.str_team,
            str_staff: item.str_staff,
            shifts: item.shifts,
            str_dept_name: item.str_dept_name,
            weeks: item.weeks || [], // 添加后端返回的周统计数据
            dbl_left: item.dbl_left,
            str_staff_code: item.str_staff_code,
          }))
          pagination.value.total = response.totalCount
        } else {
          throw new Error('获取数据失败')
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        ElMessage.error(error.message || '数据加载失败')
      } finally {
        loading.value = false
      }
    }
    const orderBy = ref('asc')

    // 部门排序
    const handleSortChange = ({ column, prop, order }) => {
      if (order === 'ascending') {
        orderBy.value = prop + ' asc'
      } else if (order === 'descending') {
        orderBy.value = prop + ' desc'
      }
      getTableData()
    }

    // 处理页码变化
    const handleCurrentChange = (page) => {
      pagination.value.currentPage = page
      getTableData()
    }

    // 处理每页条数变化
    const handleSizeChange = (size) => {
      pagination.value.pageSize = size
      pagination.value.currentPage = 1
      getTableData()
    }

    // 获取单元格样式
    const getCellClass = (shift, date) => {
      const classes = ['shift-cell']

      if (!shift) {
        if (moment(date).isSame(moment(), 'day')) {
          classes.push('today')
        }
        return classes
      }

      // 适配新的数据结构，同时兼容原有结构
      const shiftName = typeof shift === 'object' ? shift.name : shift

      if (shiftName === '早班') {
        classes.push('status-day')
      } else if (shiftName === '晚班') {
        classes.push('status-night')
      } else if (shiftName === '行政班') {
        classes.push('status-rest')
      }

      if (moment(date).isSame(moment(), 'day')) {
        classes.push('today')
      }

      return classes
    }

    // 获取表头样式
    const getHeaderClass = (date) => {
      if (date.isHoliday) {
        return 'holiday-header'
      }
      if (date.isWorkday) {
        return 'workday-header'
      }
      if (date.isWeekend && !date.isWorkday) {
        return 'weekend-header'
      }
      return 'table-header'
    }

    // 处理单元格点击事件
    const handleCellClick = (shift, date, team) => {
      let staffStatus = shift?.int_staff_status || '0'
      let shiftType = shift?.id_shift || ''
      if (shiftType === '0' && props.dept !== 'hr') {
        const name = shift.str_body_s
        ElMessage.warning(`${name}已经在当天排了灵活班,需先删除灵活班才可以进行修改`)
        return
      }
      // 判断date是否为周末
      if (date.isWeekend && !shiftType) {
        // staffStatus = '1'
        // 获取班次为行政班的id
        shiftType = shiftTypeOptions.value.find((item) => item.str_name === '行政班')?.id
      }
      selectedShiftType.value = shiftType
      selectedStaffStatus.value = staffStatus
      selectedShift.value = shift
      selectedDate.value = date
      selectedTeam.value = team

      const staffStatusName = staffStatusOptions.value.find((item) => item.id === staffStatus)?.str_name
      // 判断是否为特殊状态
      if (specialStatusName.value.includes(staffStatusName)) {
        isShiftDisabled.value = true
      } else {
        isShiftDisabled.value = false
      }
      dialogVisible.value = true
    }

    // 获取班次人员
    const handleShiftPeople = async (id_staff, dt_leave) => {
      const response = await get_pt_get_shift_people(id_staff, dt_leave)
      teamMembers.value = response.data
    }

    // 获取班次信息
    const handleShiftsInfo = (shifts, date) => {
      if (shifts && shifts.length > 0) {
        let shift = shifts.find((shift) => shift.dt_plan === date)
        if (shift) {
          const staffName = staffStatusOptions.value.find((item) => item.id === shift.int_staff_status)?.str_name
          let shiftName = ''

          if (!staffName) {
            shiftName = shift.str_shift
          } else if (shift.int_staff_status === '0') {
            shiftName = shift.str_shift
          } else if (shift.int_staff_status === '1') {
            shiftName = '休'
          } else {
            shiftName = staffName
          }

          return {
            name: shiftName,
            hours: shift.dbl_hour && shift.int_staff_status === '0' ? shift.dbl_hour : null,
          }
        } else {
          return '-'
        }
      } else {
        return '-'
      }
    }

    // 保存班次信息
    const handleSave = async () => {
      submitLoading.value = true
      dialogVisible.value = false
      const params = {
        id: selectedShift.value?.id,
        str_body_s: selectedTeam.value.str_staff,
        id_body_s: selectedTeam.value.id,
        int_type: selectedShiftType.value,
        dt_plan: selectedDate.value.date,
        int_staff_status: selectedStaffStatus.value,
        is_reschedule: isReschedule.value ? 1 : 0, // 是否重排
      }
      isReschedule.value = false
      try {
        await pt_save_staffshift(params, props.dept)
        await getTableData()
      } catch (error) {
        console.error('保存班次信息失败:', error)
      } finally {
        submitLoading.value = false
      }
    }

    const specialStatusName = ref(['MES', '培训', '出差'])
    const handleStaffStatusChange = (value) => {
      // 获取对应的人员状态
      const staffStatus = staffStatusOptions.value.find((item) => item.id === value)
      if (staffStatus) {
        // 判断是否为特殊状态
        if (specialStatusName.value.includes(staffStatus.str_name)) {
          // 设置班次禁用状态
          isShiftDisabled.value = true
          // 获取行政班的id
          selectedShiftType.value = shiftTypeOptions.value.find((item) => item.str_name === '行政班')?.id
        } else {
          isShiftDisabled.value = false
        }
      }
    }

    let resizeObserver = null

    // 获取团队列表
    const fetchTeamOptions = async () => {
      try {
        const response = await getTeams()
        if (response && response.data) {
          teamOptions.value = response.data
        }
      } catch (error) {
        console.error('获取团队列表失败:', error)
        ElMessage.error('获取团队列表失败')
      }
    }

    // 选择团队后获取团队成员
    const handleTeamChange = async () => {
      if (!selectedTeamId.value) {
        teamStaffList.value = []
        return
      }

      try {
        const id_staff = selectedTeamId.value
        const dt_leave = batchSelectedDate.value
        const response = await get_pt_get_shift_people(id_staff, dt_leave)

        if (response && response.data) {
          teamStaffList.value = response.data.map((item) => ({
            id: item.id_staff,
            str_staff: item.str_name,
            selectedShiftType: selectedShiftTypeForBatch.value,
            selectedStaffStatus: '0',
            isShiftDisabled: false,
          }))
        } else {
          teamStaffList.value = []
        }
      } catch (error) {
        console.error('获取团队成员失败:', error)
        ElMessage.error('获取团队成员失败')
        teamStaffList.value = []
      }
    }

    // 打开批量排班弹框
    const openBatchDialog = () => {
      batchSelectedDate.value = moment().format('YYYY-MM-DD')
      selectedTeamId.value = ''
      selectedShiftTypeForBatch.value = ''
      teamStaffList.value = []
      batchDialogVisible.value = true
    }

    // 处理人员状态变化
    const handleBatchStaffStatusChange = (row, value) => {
      const staffStatus = staffStatusOptions.value.find((item) => item.id === value)
      if (staffStatus && specialStatusName.value.includes(staffStatus.str_name)) {
        row.isShiftDisabled = true
        row.selectedShiftType = shiftTypeOptions.value.find((item) => item.str_name === '行政班')?.id
      } else {
        row.isShiftDisabled = false
      }
    }

    const submitLoading = ref(false)
    // 批量保存班次信息
    const handleBatchSave = async () => {
      submitLoading.value = true
      if (!batchSelectedDate.value) {
        ElMessage.warning('请选择日期')
        return
      }

      try {
        loading.value = true

        // 收集所有员工的班次信息
        const savePromises = teamStaffList.value.map((staff) => {
          const params = {
            str_body_s: staff.str_staff,
            id_body_s: staff.id,
            int_type: staff.selectedShiftType,
            dt_plan: batchSelectedDate.value,
            int_staff_status: staff.selectedStaffStatus,
          }

          return pt_save_staffshift(params, props.dept)
        })

        await Promise.all(savePromises)
        ElMessage.success('批量保存成功')
        batchDialogVisible.value = false
        await getTableData()
      } catch (error) {
        console.error('批量保存失败:', error)
        ElMessage.error('批量保存失败')
      } finally {
        loading.value = false
        submitLoading.value = false
      }
    }

    // 修改默认日期为当前日期
    watch(selectedShiftTypeForBatch, (newVal) => {
      if (newVal && teamStaffList.value.length > 0) {
        teamStaffList.value.forEach((staff) => {
          if (!staff.isShiftDisabled) {
            staff.selectedShiftType = newVal
          }
        })
      }
    })

    // 组件挂载时初始化数据
    onMounted(async () => {
      const year = moment().year()
      updateHolidays(year)
      await getTableData()
      await fetchTeamOptions()

      // 初始计算表格高度
      calculateTableHeight()

      // 监听窗口大小变化
      resizeObserver = new ResizeObserver(calculateTableHeight)

      // 监听窗口大小变化
      window.addEventListener('resize', calculateTableHeight)

      // 如果在iframe中，尝试监听iframe大小变化
      if (window !== window.parent) {
        try {
          const iframe = window.frameElement
          if (iframe && resizeObserver) {
            resizeObserver.observe(iframe)
          }
        } catch (e) {
          console.error('监听iframe大小变化失败:', e)
        }
      }

      // 监听表格容器大小变化
      if (tableContainerRef.value && resizeObserver) {
        resizeObserver.observe(tableContainerRef.value)
      }
    })
    // 组件卸载时清理监听器
    onUnmounted(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', calculateTableHeight)

      // 断开ResizeObserver连接
      if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
      }
    })

    const getWeekSummary = (row, week) => {
      // 计算该周的工时总计
      const shifts = row.shifts || []
      const weekDates = week.dates || []
      let totalHours = 0

      weekDates.forEach((date) => {
        const shift = shifts.find((s) => s.dt_plan === date.date)
        if (shift && shift.dbl_hour) {
          totalHours += shift.dbl_hour
        }
      })

      // 优先使用后端提供的周统计数据，如果有的话
      if (row.weeks && row.weeks.length > 0) {
        const weekData = row.weeks.find((w) => w.int_week === week.weekNumber)
        if (weekData && weekData.dbl_left) {
          return `${weekData.dbl_left}`
        }
      }

      // 兜底：使用前端计算的工时总计
      return totalHours > 0 ? `${totalHours}` : ''
    }

    const processedTableData = computed(() => {
      if (!tableData.value || !headerData.value.weeks) {
        return []
      }

      const result = []
      tableData.value.forEach((row) => {
        result.push({ ...row, rowType: 'data' })

        const weekSummaries = {}
        headerData.value.weeks.forEach((week) => {
          weekSummaries[week.weekNumber] = getWeekSummary(row, week)
        })

        result.push({
          id: `${row.id}-summary`,
          str_team: row.str_team,
          str_staff: row.str_staff,
          str_dept_name: row.str_dept_name,
          dbl_left: row.dbl_left,
          rowType: 'summary',
          weekSummaries,
          shifts: [], // Add empty shifts to prevent errors
          weeks: row.weeks || [], // 传递周统计数据
        })
      })
      return result
    })

    const tableSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      const fixedCols = 1 + (props.dept === 'manger_mn' || props.dept === 'hr' ? 1 : 0) + 1 + 1 + 1

      // 处理基础列的合并
      if (columnIndex < fixedCols) {
        if (row.rowType === 'data') {
          // 数据行：基础列占2行（数据行+汇总行）
          return { rowspan: 2, colspan: 1 }
        } else if (row.rowType === 'summary') {
          // 汇总行：基础列隐藏（已被上一行占用）
          return { rowspan: 0, colspan: 0 }
        }
      } else {
        // 处理日期列的合并（仅汇总行需要合并）
        if (row.rowType === 'summary') {
          let currentColumnIndex = fixedCols
          if (headerData.value.weeks) {
            for (const week of headerData.value.weeks) {
              const weekDateCount = week.dates.length
              if (columnIndex >= currentColumnIndex && columnIndex < currentColumnIndex + weekDateCount) {
                if (columnIndex === currentColumnIndex) {
                  return { rowspan: 1, colspan: weekDateCount }
                } else {
                  return { rowspan: 0, colspan: 0 }
                }
              }
              currentColumnIndex += weekDateCount
            }
          }
        }
      }
    }

    const tableRowClassName = ({ row }) => {
      if (row.rowType === 'summary') {
        return 'summary-row'
      }
      return ''
    }

    const getWorkTimePoolClass = (dbl_left) => {
      if (dbl_left > 150 || dbl_left < -150) {
        return 'text-red-600' // 红色：禁止排班/强制排班
      } else if (dbl_left > 84) {
        return 'text-yellow-600' // 黄色：警示排班
      } else {
        return 'text-green-600' // 绿色：正常范围
      }
    }

    const getWeekSummaryClass = (row, week) => {
      const summaryNumber = row.weekSummaries[week.weekNumber]
      if (summaryNumber > 40) {
        return 'text-red-500'
      }
    }

    // 是否显示重排
    const isShowReschedule = computed(() => {
      // 班次是行政班不显示或者dept为manger_mn
      const shiftType = shiftTypeOptions.value.find((item) => item.id === selectedShiftType.value)
      if ((shiftType && shiftType.str_name === '行政班') || props.dept === 'manger_mn') {
        return false
      }
      return true
    })

    return {
      tableContainerRef,
      tableHeight,
      loading,
      searchForm,
      tableData,
      headerData,
      currentMonth,
      pagination,
      dialogVisible,
      selectedShift,
      selectedDate,
      selectedTeam,
      selectedShiftType,
      teamMembers,
      shiftTypeOptions,
      shiftTypes,
      staffStatusOptions,
      selectedStaffStatus,
      isShiftDisabled,
      batchDialogVisible,
      selectedTeamId,
      selectedShiftTypeForBatch,
      batchSelectedDate,
      teamOptions,
      teamStaffList,
      isReschedule,
      processedTableData,
      isShowReschedule,
      submitLoading,
      getCellClass,
      getHeaderClass,
      handleCurrentChange,
      handleSizeChange,
      handleCellClick,
      handleShiftPeople,
      handleShiftsInfo,
      handleSave,
      handleStaffStatusChange,
      handleSortChange,
      openBatchDialog,
      handleTeamChange,
      handleBatchStaffStatusChange,
      handleBatchSave,
      tableSpanMethod,
      tableRowClassName,
      handleSearch: getTableData, // 添加搜索方法
      getWorkTimePoolClass,
      getWeekSummaryClass,
    }
  },
  template: /*html*/ `
    <div class="shift-calendar-container">
      <!-- 加载遮罩 -->
      <div v-if="loading" class="loading-mask">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span class="loading-text">加载中...</span>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="月份">
            <el-date-picker
              v-model="currentMonth"
              type="month"
              format="YYYY-MM"
              value-format="YYYY-MM"
              :clearable="false"
              :editable="false"
              placeholder="选择月份"
            />
          </el-form-item>
          <el-form-item label="工号">
            <el-input v-model="searchForm.str_staff_code" placeholder="请输入工号" clearable class="!w-52" />
          </el-form-item>
          <el-form-item label="Team">
            <el-input v-model="searchForm.str_team" placeholder="请输入Team" clearable class="!w-52" />
          </el-form-item>
          <el-form-item label="Name">
            <el-input v-model="searchForm.str_name" placeholder="请输入Name" clearable class="!w-52" />
          </el-form-item>
          <el-form-item v-if="dept === 'manger_mn' || dept === 'hr'" label="部门">
            <el-input v-model="searchForm.str_dept_name" placeholder="请输入部门" clearable class="!w-52" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button v-if="isBatchScheduling" type="primary" @click="openBatchDialog">批量班组排班</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div ref="tableContainerRef" class="table-container">
        <!-- 日历表格 -->
        <el-table
          :data="processedTableData"
          border
          class="w-full"
          :height="tableHeight"
          @sort-change="handleSortChange"
          :span-method="tableSpanMethod"
          :row-class-name="tableRowClassName"
        >
          <!-- 基本信息列 -->
          <el-table-column
            prop="str_staff_code"
            label="工号"
            width="100"
            class-name="table-header"
            fixed="left"
            align="center"
            sortable="custom"
          >
            <template #default="scope">{{ scope.row.str_staff_code }}</template>
          </el-table-column>
          <el-table-column
            prop="str_team"
            label="Team"
            width="100"
            class-name="table-header"
            fixed="left"
            align="center"
            sortable="custom"
          >
            <template #default="scope">{{ scope.row.str_team }}</template>
          </el-table-column>

          <el-table-column
            v-if="dept === 'manger_mn' || dept === 'hr'"
            prop="str_dept_name"
            label="部门"
            width="100"
            class-name="table-header"
            fixed="left"
            sortable="custom"
            align="center"
          />

          <el-table-column
            prop="str_staff"
            label="Name"
            width="100"
            class-name="table-header"
            fixed="left"
            align="center"
            sortable="custom"
          >
            <template #default="scope">{{ scope.row.str_staff }}</template>
          </el-table-column>
          <el-table-column
            prop="dbl_left"
            label="工时池"
            width="100"
            class-name="table-header"
            fixed="left"
            align="center"
            sortable="custom"
          >
            <template #default="scope">
              <div :class="getWorkTimePoolClass(scope.row.dbl_left)">{{ scope.row.dbl_left }}</div>
            </template>
          </el-table-column>

          <template v-for="week in headerData.weeks" :key="'week-' + week.weekNumber">
            <!-- 周信息作为父级表头 -->
            <el-table-column :label="'第' + week.weekNumber + '周'" align="center">
              <!-- 日期列作为子级表头 -->
              <el-table-column
                v-for="date in week.dates"
                :key="date.date"
                align="center"
                min-width="80"
                :class-name="getHeaderClass(date)"
              >
                <!-- 自定义日期表头 -->
                <template #header>
                  <div class="p-1 text-xs md:text-sm">
                    <div>{{ date.day }} {{ date.weekday }}</div>
                    <div v-if="date.isHoliday" class="holiday-text text-xs">{{ date.holidayName }}</div>
                  </div>
                </template>

                <!-- 单元格内容 -->
                <template #default="scope">
                  <template v-if="scope.row.rowType === 'data'">
                    <div
                      class="flex h-full w-full flex-col items-center justify-center"
                      :class="getCellClass(handleShiftsInfo(scope.row.shifts, date.date), date.date)"
                      @click="handleCellClick(scope.row.shifts.find(s => s.dt_plan === date.date), date, scope.row)"
                    >
                      <template v-if="typeof handleShiftsInfo(scope.row.shifts, date.date) === 'object'">
                        <div class="shift-type text-xs whitespace-nowrap md:text-sm">
                          {{ handleShiftsInfo(scope.row.shifts, date.date).name }}
                        </div>
                        <div
                          v-if="handleShiftsInfo(scope.row.shifts, date.date).hours"
                          class="shift-hours text-xs text-gray-600"
                        >
                          ({{ handleShiftsInfo(scope.row.shifts, date.date).hours }}h)
                        </div>
                      </template>
                      <template v-else>
                        <div class="shift-type text-xs whitespace-nowrap md:text-sm">
                          {{ handleShiftsInfo(scope.row.shifts, date.date) }}
                        </div>
                      </template>
                    </div>
                  </template>
                  <template v-else>
                    <div
                      class="p-1 text-xs font-bold text-gray-500 md:text-sm"
                      :class="getWeekSummaryClass(scope.row, week)"
                    >
                      {{ scope.row.weekSummaries[week.weekNumber] }}h
                    </div>
                  </template>
                </template>
              </el-table-column>
            </el-table-column>
          </template>
        </el-table>
      </div>

      <!-- 分页器 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 弹框组件 -->
      <el-dialog
        v-model="dialogVisible"
        title="班次编辑"
        width="50%"
        :close-on-click-modal="false"
        class="common-dialog shift-dialog"
        :destroy-on-close="true"
      >
        <div class="p-4">
          <!-- 小组信息 -->
          <div class="section-title mb-4 text-lg font-bold">小组信息</div>
          <el-form label-width="80px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="日期">
                  <div class="form-content">{{ selectedDate?.date || '' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Team">
                  <div class="form-content">{{ selectedTeam?.str_team || '' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Name">
                  <div class="form-content">{{ selectedTeam?.str_staff || '' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="班次">
                  <el-select v-model="selectedShiftType" class="w-full" clearable :disabled="isShiftDisabled">
                    <el-option
                      v-for="item in shiftTypeOptions"
                      :key="item.id"
                      :label="item.str_name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="人员状态">
                  <el-select v-model="selectedStaffStatus" class="w-full" @change="handleStaffStatusChange">
                    <el-option
                      v-for="item in staffStatusOptions"
                      :key="item.id"
                      :label="item.str_name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <!-- <el-col v-if="isShowReschedule" :span="24"> -->
              <el-col :span="24">
                <el-form-item label="是否重排">
                  <el-switch v-model="isReschedule" /> <span style="color:red">重排后会覆盖后续所有班次，请谨慎选择</span>
                </el-form-item>
              </el-col>
               
            </el-row>
          </el-form>
        </div>

        <template #footer>
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSave" :loading="submitLoading">保存</el-button>

        </template>
      </el-dialog>

      <!-- 批量排班弹框 -->
      <el-dialog
        v-model="batchDialogVisible"
        title="批量班组排班"
        width="70%"
        :close-on-click-modal="false"
        class="common-dialog shift-dialog"
        :destroy-on-close="true"
      >
        <div class="p-4">
          <!-- 选择区域 -->
          <div class="section-title mb-4 text-lg font-bold">排班信息</div>
          <el-form label-width="100px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="日期">
                  <el-date-picker
                    v-model="batchSelectedDate"
                    type="date"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :clearable="false"
                    placeholder="选择日期"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Team">
                  <el-select
                    v-model="selectedTeamId"
                    class="w-full"
                    @change="handleTeamChange"
                    placeholder="请选择Team"
                  >
                    <el-option v-for="item in teamOptions" :key="item.id" :label="item.str_name" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="班次">
                  <el-select v-model="selectedShiftTypeForBatch" class="w-full" placeholder="请选择班次">
                    <el-option
                      v-for="item in shiftTypeOptions"
                      :key="item.id"
                      :label="item.str_name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <!-- 人员表格 -->
          <div class="mt-6">
            <div class="section-title mb-4 text-lg font-bold">人员列表</div>
            <el-table :data="teamStaffList" max-height="400px" border>
              <el-table-column prop="str_staff" label="Name" width="120" align="center" />
              <el-table-column label="班次" align="center">
                <template #default="{ row }">
                  <el-select v-model="row.selectedShiftType" class="w-full" :disabled="row.isShiftDisabled">
                    <el-option
                      v-for="item in shiftTypeOptions"
                      :key="item.id"
                      :label="item.str_name"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="人员状态" align="center">
                <template #default="{ row }">
                  <el-select
                    v-model="row.selectedStaffStatus"
                    class="w-full"
                    @change="(val) => handleBatchStaffStatusChange(row, val)"
                  >
                    <el-option
                      v-for="item in staffStatusOptions"
                      :key="item.id"
                      :label="item.str_name"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <template #footer>
          <el-button @click="batchDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleBatchSave" :loading="submitLoading">保存</el-button>
        </template>
      </el-dialog>
    </div>
  `,
}
