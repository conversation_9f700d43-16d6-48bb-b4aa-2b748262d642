<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- css部分 -->
    <link rel="stylesheet" href="../../../styles/tailwind.css" />
    <link rel="stylesheet" href="../../../assets/element-plus@2.9.4/dist/index.css" />
    <link rel="stylesheet" href="../../../assets/vxe-pc-ui@4.3.80/lib/style.min.css" />
    <link rel="stylesheet" href="../../../assets/vxe-table@4.10.6/lib/style.min.css" />
    <link rel="stylesheet" href="../../../styles/common.dialog.css" />
    <link rel="stylesheet" href="../../../styles/shift.calendar.team.css" />
    <!-- CDN js部分 -->
    <script src="../../../assets/vue@3.5.13/vue.global.js"></script>
    <script src="../../../assets/element-plus@2.9.4/dist/index.full.js"></script>
    <script src="../../../assets/sortablejs@latest/Sortable.min.js"></script>
    <script src="../../../assets/element-plus@2.9.4/icons-vue/index.full.js"></script>
    <script src="../../../assets/moment/moment.min.js"></script>
    <script src="../../../assets/lodash@4.17.21/lodash.min.js"></script>
    <script src="../../../assets/xe-utils@3.7.0/dist/xe-utils.umd.min.js"></script>
    <script src="../../../assets/vxe-pc-ui@4.3.80/lib/index.umd.min.js"></script>
    <script src="../../../assets/vxe-table@4.10.6/lib/index.umd.min.js"></script>
    <script src="../../../assets/element-plus@2.9.4/dist/locale/zh-cn.js"></script>
    <!-- api部分 -->
    <script src="../../../assets/axios@1.6.7/axios.min.js"></script>
    <title>F2 检验人员班次</title>
  </head>
  <body>
    <div id="app">
      <f2-shift-calendar dept="hr" :is-batch-scheduling="true"></f2-shift-calendar>
    </div>
  </body>
  <script type="module">
    import F2ShiftCalendar from './f2.shift.page.component.js'
    const { createApp } = Vue
    const app = createApp({
      components: {
        F2ShiftCalendar,
      },
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    moment.updateLocale('en', {
      week: {
        dow: 1,
      },
    })
    app.use(ElementPlus, {
      locale: ElementPlusLocaleZhCn,
    })
    app.use(VxeUI)
    app.use(VXETable)
    app.mount('#app')
  </script>
</html>
