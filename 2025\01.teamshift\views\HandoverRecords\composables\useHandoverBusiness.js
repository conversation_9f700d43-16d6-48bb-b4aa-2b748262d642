// 交接业务逻辑 composable
const { ref, computed } = Vue

export function useHandoverBusiness() {
  // 业务状态
  const selectedRows = ref([])
  const filterKeyword = ref('')
  const dateRange = ref([])

  // 业务计算属性
  const filteredData = computed(() => {
    // 根据关键词和日期范围过滤数据的逻辑
    return []
  })

  // 业务方法
  const validateHandoverData = (data) => {
    const errors = []
    
    if (!data.date) {
      errors.push('日期不能为空')
    }
    
    if (data.handoverRate < 0 || data.handoverRate > 1) {
      errors.push('交接率必须在0-1之间')
    }
    
    if (data.handoverCount < 0) {
      errors.push('交接次数不能为负数')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  const calculateHandoverRate = (handoverUser, handoverTotal) => {
    if (handoverTotal === 0) return 0
    return (handoverUser / handoverTotal).toFixed(2)
  }

  const generateReport = (data) => {
    return {
      totalRecords: data.length,
      averageRate: data.reduce((sum, item) => sum + item.handoverRate, 0) / data.length,
      highPerformance: data.filter(item => item.handoverRate > 0.8).length,
      lowPerformance: data.filter(item => item.handoverRate < 0.5).length
    }
  }

  const batchUpdate = (rows, updates) => {
    return rows.map(row => ({
      ...row,
      ...updates,
      // 重新计算交接率
      handoverRate: calculateHandoverRate(
        updates.handoverUser || row.handoverUser,
        updates.handoverTotal || row.handoverTotal
      )
    }))
  }

  return {
    // 状态
    selectedRows,
    filterKeyword,
    dateRange,
    filteredData,
    // 方法
    validateHandoverData,
    calculateHandoverRate,
    generateReport,
    batchUpdate
  }
}

// 数据导入导出逻辑
export function useDataImportExport() {
  const { ref } = Vue
  
  const isExporting = ref(false)
  const isImporting = ref(false)

  const exportToExcel = async (data, filename = 'handover-records') => {
    try {
      isExporting.value = true
      // 导出到Excel的逻辑
      console.log('导出数据到Excel:', data)
      // 实际的导出实现
    } catch (error) {
      console.error('导出失败:', error)
      throw error
    } finally {
      isExporting.value = false
    }
  }

  const importFromExcel = async (file) => {
    try {
      isImporting.value = true
      // 从Excel导入的逻辑
      console.log('从Excel导入数据:', file)
      // 返回解析后的数据
      return []
    } catch (error) {
      console.error('导入失败:', error)
      throw error
    } finally {
      isImporting.value = false
    }
  }

  const exportToPDF = async (data, options = {}) => {
    try {
      isExporting.value = true
      // 导出到PDF的逻辑
      console.log('导出数据到PDF:', data, options)
    } catch (error) {
      console.error('PDF导出失败:', error)
      throw error
    } finally {
      isExporting.value = false
    }
  }

  return {
    isExporting,
    isImporting,
    exportToExcel,
    importFromExcel,
    exportToPDF
  }
} 