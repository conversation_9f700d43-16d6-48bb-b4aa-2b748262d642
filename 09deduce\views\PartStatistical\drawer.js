/**
 * @description 未进入供应链抽屉组件
 * @author: rik_tang
 */
import { post } from '../../../config/axios/httpReuest.js'
// 引入公共表格组件
import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'
import ReasonRemarkDialog from '../components/ReasonRemarkDialog.js'
import { useReasonRemark } from '../hooks/useReasonRemark.js'
const { reactive, toRefs, ref, onMounted } = Vue
const { useVModel } = VueUse
import { commonEnum } from '../../api/index.js'
const EngineDrawer = {
  components: {
    HtVxeTable,
    ReasonRemarkDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: Number,
      default: '',
    },
    idWo: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
    is_delivery: 0,
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)
    const tableRef = ref(null)
    const jump_config_api = ref([]) // 跳转配置
    const jump_barcode_columns_api = ref([])
    const labelMap = {
      101: 'EKD计算中',
      102: '待转包',
      103: 'CSM确认(进厂缺件)',
      104: '构型确认',
      105: '待锁库',
      106: '消耗件',
      107: 'Keep Missing',
      108: 'F4异常领料',
      109: '转包无EDD',
      110: '背板',
      111: '待采购',
      112: '试车借件',
      113: '未离开gate1',
      114: '进入供应链后消失',
      115: '修理领料',
      116: '待CSM确认(锁库)',
      117: '消耗件(未发料)',
      118: '需求关闭',
      119: '无PN号',
      120: '串件中',
      121: '待客户提供无LTDate',
      122: '未离开gate1',
      123: 'F2 EDD过期',
      124: '修理领料',
      125: '未离开gate1',
      999: '总量',
    }
    // 表格数据
    const tableState = reactive({
      data: null,
      columns: [
        {
          visible: props.type === 999,
          title: '类型 Type',
          field: 'strcategory',
          minWidth: 100,
          fixed: 'left',
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          fixed: 'left',
          title: ' JC Flow',
          field: 'str_jc_flow',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
          visible: props.type === 113 || props.type === 122 || props.type === 125,
          sortable: true,
        },
        {
          fixed: 'left',
          title: 'Flow',
          field: 'str_wo_flow',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
          visible: props.type === 113 || props.type === 122,
          sortable: true,
        },
        {
          fixed: 'left',
          title: '原因备注',
          field: 'str_reason',
          minWidth: 200,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          fixed: 'left',
          title: '是否工卡退回',
          field: 'is_back',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
          visible: props.type === 113 || props.type === 122,
          filters: [
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ],
          formatter: ({ cellValue }) => {
            return cellValue === 1 ? '是' : '否'
          },
        },
        {
          visible: props.type === 120,
          title: '审批步骤',
          field: 'str_audit',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          visible: props.type === 120,
          title: '审批状态',
          field: 'str_real_audit',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: props.type === 114 ? '消失站点' : 'Kitting完成/站点',
          field: 'str_nodename',
          minWidth: 120,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '备注',
          field: 'str_remark',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '目标WO',
          field: 'str_code',
          minWidth: 120,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '目标ESN',
          field: 'str_esn',
          minWidth: 120,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台',
          field: 'str_wo_code_ori',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台 ESN',
          field: 'str_wo_esn_ori',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PN',
          field: 'str_pn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件标签',
          field: 'str_label',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件名称',
          field: 'str_part_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          visible: props.type === 107 || props.type === 112,
          title: '市场决定',
          field: 'str_csm_dispname',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'SM',
          field: 'str_sm',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '客户',
          field: 'str_client',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '转包供应商/采购供应商',
          field: 'str_subcontract',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件来源',
          field: 'str_class',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件类别',
          field: 'str_item_type',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'Marshalling集件关闭',
          field: 'is_close',
          minWidth: 150,
          filters: [
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ],
          formatter: ({ cellValue }) => {
            return cellValue === 1 ? '是' : '否'
          },
        },
        {
          title: 'Marshalling完成日期',
          field: 'dt_close',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PKP',
          field: 'id_pkp',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '机型',
          field: 'str_engine_type',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '数量',
          field: 'int_qty',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'EKD',
          field: 'dt_ekd',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          visible: props.type === 999,
          title: '零件来源',
          field: 'str_source_type',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          visible: props.type === 999,
          title: '原台WO',
          field: 'str_wo_sp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          visible: props.type === 999,
          title: '原台ESN',
          field: 'str_esn_sp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          visible: props.type === 999,
          title: '原台EKD',
          field: 'dt_ekd_sp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          visible: true, // props.type === 101 || props.type === 114,
          title: '无EKD场景',
          field: 'int_no_ekd_type',
          minWidth: 200,
          formatter: ({ cellValue }) => {
            return noEkdTypeEnums.value.find((item) => +item.str_code === cellValue)?.str_name
          },
        },
        {
          title: '保障交付时间',
          field: 'dt_delivery',
          minWidth: 150,
        },
      ],
      total: 0,
    })

    const apiMap = {
      [props.type]: 'de_pendingpnlist',
      0: 'de_getpnlist_bygroup',
      888: 'de_pendingpnlist',
    }
    // * 获取表格数据
    const getTableData = async () => {
      const { idWo, type } = props
      const ac = apiMap[type]
      const params = {
        ac,
        id_wo: idWo,
        int_point_type: type,
        is_delivery: props.is_delivery ? 1 : 0, // 是否保证交付
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        if (type === 0) {
          tableState.data = data.data
        } else {
          tableState.data = data.data.map((item) => {
            item.strcategory = item.strcategory
              .split(',')
              .map((item) => labelMap[item])
              .join(',')
            return item
          })
        }
        tableState.total = data.data.length
        if (type === 113 || type === 122) {
          jump_config_api.value
            .filter((x) => x.key == 'int' + type)
            .map((item) => {
              item.index_key_num = data.data.filter((x) => x.str_jc_flow == item.index_key).length //
            })
        }
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    const { reasonRemark, openReasonRemark, saveReasonRemark, closeReasonRemark, disabledDate } = useReasonRemark(
      tableRef,
      getTableData,
    )
    // * 表格筛选
    const handleFilterChange = (data) => {
      tableState.total = tableRef.value.getCurrentLength()
    }
    // * 导出表格数据
    const exportTableData = () => {
      tableRef.value.exportData()
    }

    const noEkdTypeEnums = ref([])
    // 获取字段类型枚举
    const getColumnTypeEnum = async () => {
      const { data } = await commonEnum('pda_ekd_no_ekd')
      if (data.code === 'success') {
        noEkdTypeEnums.value = data.data
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    /* ---------------------- 跳转 ---------------------- */
    const jumpMap = {
      a: '集件零件管理',
      b: '用料申请',
      c: '零件报表',
      d: '转包零件跟踪',
      e: '锁库处理',
      g: '零件历程',
    }
    const jumpMapId = {
      a: '1435766996880461825',
      b: '1453234319950618624',
      c: '1435766981386702849',
      d: '1435766992040235009',
      e: '1453282700483895296',
      f: '1697500556140101633',
      g: '1826524987646808064',
      h: '1435766977628606465',
    }
    const idKeyMap = {
      a: 'id_pkp',
      c: 'id_offlog',
      b: 'id_apply',
      d: 'id_po_sub_sc',
      e: 'id_apply',
      f: 'id_se_audit',
      g: 'id_pkp',
      h: 'id_po_sub',
    }
    const handleJump = (command) => {
      // 没有选中数据
      if (!tableRef.value.getSelectedData().length) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }
      const idList =
        tableRef.value
          .getSelectedData()
          .map((item) => item[idKeyMap[command]])
          .filter((item) => item !== null)
          .join(',') ?? ''

      if (idList === '') {
        ElementPlus.ElMessage.warning(`无${jumpMap[command]}信息`)
        return
      }
      const getUrl = (command, idList) => {
        const baseUrl = `/Page/?moduleid=${jumpMapId[command]}`
        return command === 'e' ? `${baseUrl}&qrc_id_main=${idList}` : `${baseUrl}&qrc_id=${idList}`
      }
      com.refreshTab(jumpMap[command], getUrl(command, idList))
    }
    /**获取跳转发动机基础信息 */
    const getJumpBarcodeParams = async () => {
      const params = {
        ac: 'de_get_jump_params',
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        jump_config_api.value = data.data
        jump_barcode_columns_api.value = data.data.map((item) => {
          return item.key
        })
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    /**跳转barcode 操作界面 */
    const jumpBarcode = (index_key) => {
      const jumpConfig = jump_config_api.value.find(
        (x) => x.key.indexOf(props.type) > -1 && (x.index_key || 't') == ((index_key && index_key.index_key) || 't'),
      )
      if (!jumpConfig) return ElementPlus.ElMessage.error('跳转模块未配置')
      let url = `/Page/?moduleid=${jumpConfig.id}&param_value={id_wo:'${props.idWo}'${jumpConfig.params_value}}` // &qri_id_wo='${idList}'
      if (jumpConfig.cus) url += `&cus=${jumpConfig.cus}`
      const winparam = {
        url,
        title: '*' + jumpConfig.name,
        grid: null,
        width: '1200px',
        height: '400px',
        region: null,
      }
      com.openwin(this, '', winparam, false, false, false)
    }
    /**
     * 单元格样式
     * @param {Object} row - 当前行数据
     * @param {Object} column - 当前列配置
     * @param {Number} rowIndex - 当前行索引
     * @param {Number} columnIndex - 当前列索引
     */
    const cellClassName = ({ row, column, rowIndex, columnIndex }) => {
      const { field } = column
      if (field === 'str_jc_flow') {
        if (row.str_jc_flow === 'F1-2') {
          return 'bg-sky-400'
        }
        if (row.str_jc_flow === 'F2-1') {
          return 'bg-purple-500'
        }
      } else if (field === 'is_back') {
        if (row.is_back === 1) {
          return 'bg-red-500'
        }
      }
    }
    // 获取 gate 各Flow数量
    const getTypeNum = (flow) => {}
    const saveAog = async () => {
      const selectedData = tableRef.value.getSelectedData().map((item) => item.id_pkp)

      if (selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }
      if (selectedData.length > 1) {
        ElementPlus.ElMessage.warning('只能选择一条数据')
        return
      }

      ElementPlus.ElMessageBox.confirm('是否保证交付?', 'Warning', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning',
      })
        .then(() => {
          const params = {
            ac: 'de_pndeliverysave',
            int_type: '1',
            id_pkp: selectedData,
          }
          post(params).then((res) => {
            if (res.data.code === 'success') {
              ElementPlus.ElMessage({
                type: 'success',
                message: '保证交付成功',
              })
              // tableRef.value.clearSelection()
              // tableRef.value.reload()
            } else {
              ElementPlus.ElMessage({
                type: 'error',
                message: '保证交付失败',
              })
            }
          })
        })
        .catch(() => {
          const params = {
            ac: 'de_pndeliverysave',
            int_type: '0',
            id_pkp: selectedData,
          }
          post(params).then((res) => {
            if (res.data.code === 'success') {
              ElementPlus.ElMessage({
                type: 'success',
                message: '取消保证交付成功',
              })
              // tableRef.value.clearSelection()
              // tableRef.value.reload()
            } else {
              ElementPlus.ElMessage({
                type: 'error',
                message: '取消保证交付失败',
              })
            }
          })
        })
    }

    /**
     * 保证交付
     */
    const handleEnsureDelivery = () => {
      const selectedData = tableRef.value.getSelectedData().map((item) => item.id_pkp)
      if (selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }
      if (selectedData.length > 1) {
        ElementPlus.ElMessage.warning('只能选择一条数据')
        return
      }
      const params = {
        ac: 'de_save_guarantee_delivery',
        id_pkp: selectedData[0],
      }
      post(params).then((res) => {
        if (res.data.code === 'success') {
          ElementPlus.ElMessage.success('保证交付成功')
        } else {
          ElementPlus.ElMessage.error('保证交付失败')
        }
      })
    }
    // -------- 资源方案 --------
    const resourcePlanRef = ref(null)
    const resourcePlan = reactive({
      visible: false,
      tableData: null,
      tableColumns: [
        {
          title: '零件来源',
          field: 'str_source_type',
          fixed: 'left',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'EKD',
          minWidth: 120,
          field: 'dt_ekd',
          fixed: 'left',
          filters: [{ data: '' }],
          fiterRender: { name: 'FilterInput' },
        },
        {
          title: '零件标签',
          field: 'str_label',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台客户',
          field: 'str_client_sp',
          minWidth: 160,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台发动机排序',
          field: 'int_sort',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'EKD是否满足模拟',
          field: 'is_ekd_meet',
          minWidth: 160,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '是否同客户',
          field: 'is_same_client',
          minWidth: 160,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '推演占用',
          field: 'str_wo_de',
          minWidth: 160,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'Kitting完成/站点',
          field: 'str_nodename',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        // {
        //   title: '目标WO',
        //   field: 'str_wo',
        //   minWidth: 100,
        //   filters: [{ data: '' }],
        //   filterRender: { name: 'FilterInput' },
        // },
        // {
        //   title: '目标ESN',
        //   field: 'str_esn',
        //   minWidth: 100,
        //   filters: [{ data: '' }],
        //   filterRender: { name: 'FilterInput' },
        // },
        {
          title: '供体计划开始时间',
          field: 'dt_project_start',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '供体计划结束时间',
          field: 'dt_project_end',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '供体ESN',
          field: 'str_esn_sp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '供体WO',
          field: 'str_wo_sp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '初始ESN',
          field: 'str_esn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '初始WO',
          field: 'str_wo',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PN',
          field: 'str_pn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'SM',
          field: 'str_sm',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件名称',
          field: 'str_part_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件条码',
          field: 'str_bcode',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '仓库',
          field: 'str_wh_name',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '货位名称',
          field: 'str_product_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '批次',
          field: 'str_batch',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PKP',
          field: 'id_pkp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '要求证书',
          field: 'str_certificate_copy',
          minWidth: 130,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '供体零件证书',
          field: 'str_certificate',
          minWidth: 130,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '数量',
          minWidth: 150,
          field: 'int_num',
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
      ],
      total: 0,
    })
    // * 查看资源方案
    const viewResourcePlan = () => {
      // 获取当前选中的数据
      const selectedData = tableRef.value.getSelectedData()
      // 必须选中一条数据
      if (selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请选择数据')
        return
      } else if (selectedData.length > 1) {
        ElementPlus.ElMessage.warning('只能选择一条数据')
        return
      }
      const { str_pn } = selectedData[0]
      resourcePlan.visible = true
      getResourcePlanData(str_pn)
    }
    // * 获取资源方案数据
    const getResourcePlanData = async (str_pn) => {
      const { idWo } = props
      const params = {
        ac: 'de_query_resource_plan',
        id_wo: idWo,
        str_pn,
        is_all: 1,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        resourcePlan.tableData = data.data
        resourcePlan.total = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    // * 导出资源方案表格
    const exportResourcePlan = () => {
      resourcePlanRef.value.exportData()
    }
    onMounted(() => {
      // if (props.type === 101 || props.type === 114) {
      getColumnTypeEnum()
      // }
      getTableData()
      getJumpBarcodeParams()
    })

    return {
      visible,
      tableRef,
      ...toRefs(tableState),
      getTableData,
      handleFilterChange,
      exportTableData,
      handleJump,
      jumpBarcode,
      getJumpBarcodeParams,
      jump_config_api,
      jump_barcode_columns_api,
      cellClassName,
      reasonRemark,
      openReasonRemark,
      saveReasonRemark,
      closeReasonRemark,
      getTypeNum,
      saveAog,
      handleEnsureDelivery,
      disabledDate,
      viewResourcePlan,
      exportResourcePlan,
      resourcePlan,
      resourcePlanRef,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="85%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex items-center justify-between">
          <div class="text-white">{{ title }}零件清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-dropdown @command="handleJump">
              <el-button type="primary">
                跳转
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="a">集件零件管理（On-log）</el-dropdown-item>
                  <el-dropdown-item command="b">用料申请</el-dropdown-item>
                  <el-dropdown-item command="c">零件报表（Off-log）</el-dropdown-item>
                  <el-dropdown-item command="d">转包跟踪</el-dropdown-item>
                  <el-dropdown-item command="e">锁库处理</el-dropdown-item>
                  <el-dropdown-item command="g">零件历程</el-dropdown-item>
                  <el-dropdown-item v-if="type === 104" command="f">现场串件</el-dropdown-item>
                  <el-dropdown-item command="h">采购_厂家交付</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <div class="ml-2 flex">
              <div v-if="type === 113 || type === 122">
                <el-button type="primary" @click="handleJump('c')">零件报表（Off-log）</el-button>
              </div>
              <div v-for="item in jump_config_api" class="ml-2">
                <el-button
                  size="small"
                  :key="item.index"
                  type="primary"
                  v-if="item.key.split('int')[1]==type"
                  @click="jumpBarcode({index_key: item.index_key})"
                >
                  跳转 {{item.name}} | {{item.index_key_num}}
                </el-button>
              </div>
            </div>
            <el-button class="ml-2" type="primary" @click="openReasonRemark(0)">原因备注</el-button>
            <el-button class="ml-2" type="warning" @click="openReasonRemark(1,100)">异常屏蔽</el-button>
            <el-button class="ml-2" type="primary" @click="openReasonRemark(-999,60)">保证交付</el-button>
            <el-button class="ml-2" type="primary" @click="viewResourcePlan">查看资源方案</el-button>
          </div>

          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable
          ref="tableRef"
          :tableData="data"
          :tableColumns="columns"
          :isShowHeaderCheckbox="true"
          :cellClassName="cellClassName"
          @filterChange="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
    <!-- 原因备注 -->
    <ReasonRemarkDialog
      v-model="reasonRemark.visible"
      :form="reasonRemark.form"
      :businessType="reasonRemark.businessType"
      @saveReasonRemark="saveReasonRemark"
      @closeReasonRemark="closeReasonRemark"
      :title="reasonRemark.title"
      :disabled-date="disabledDate"
    />
<!-- 资源方案-->
    <el-drawer class="my_drawer" v-model="resourcePlan.visible" size="80%" :show-close="false" destroy-on-close>
    <template #title>
      <div class="flex items-center justify-between">
        <div class="text-white">资源方案</div>
        <el-button type="danger" @click="resourcePlan.visible = false">关闭</el-button>
      </div>
    </template>
    <div class="flex items-center justify-between">
      <el-button type="primary" @click="exportResourcePlan">导出</el-button>
      <div class="text-black">共计：{{ resourcePlan.total || 0 }} 条</div>
    </div>
    <div style="height: calc(100% - 50px)">
      <HtVxeTable
        ref="resourcePlanRef"
        :tableData="resourcePlan.tableData"
        :tableColumns="resourcePlan.tableColumns"
      ></HtVxeTable>
    </div>
  </el-drawer>
  `,
}
export default EngineDrawer
