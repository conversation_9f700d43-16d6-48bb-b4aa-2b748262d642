<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>异常转换监控 - 测试页面</title>
    <!--    引入element-plus的样式-->
    <link href="../../../assets/element-plus@2.5.5/index.css" rel="stylesheet" />
    <!--    引入vxe-table的样式-->
    <link href="../../../assets/vxe-table/style.css" rel="stylesheet" />
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow: hidden;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            height: 100%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .modern-table {
            height: 100%;
        }
        
        .modern-table .vxe-table--body-wrapper {
            overflow: hidden !important;
        }
        
        .modern-table .vxe-table--body-wrapper::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <div class="header">
                <h1>RUPK异常件与白转黄监控表 - 测试版</h1>
                <p>当前时间: <span id="current-time"></span></p>
            </div>
            
            <div class="content">
                <div class="table-container">
                    <h3>异常集件缺件零件</h3>
                    <div id="table-wrapper" style="height: calc(100% - 50px);">
                        <vxe-table
                            ref="testTable"
                            :data="tableData"
                            border
                            stripe
                            height="100%"
                            :row-config="{ isHover: true }"
                            :scroll-y="{ enabled: false }"
                            :scroll-x="{ enabled: false }"
                            class="modern-table"
                        >
                            <vxe-column type="seq" width="60" fixed="left" title="#"></vxe-column>
                            <vxe-column field="sequence" title="序号" width="80">
                                <template #default="{ row }">
                                    <el-tag :type="row.sequence === '是' ? 'success' : 'info'" size="small">
                                        {{ row.sequence }}
                                    </el-tag>
                                </template>
                            </vxe-column>
                            <vxe-column field="partNumber" title="集件已发" width="120"></vxe-column>
                            <vxe-column field="workOrder" title="工作指令" width="120"></vxe-column>
                            <vxe-column field="engineNumber" title="发动机号" width="120"></vxe-column>
                            <vxe-column field="applicationNumber" title="申请件号" width="150"></vxe-column>
                            <vxe-column field="partName" title="零件名称" width="200"></vxe-column>
                            <vxe-column field="unitCode" title="单元体" width="100"></vxe-column>
                            <vxe-column field="exceptionCode" title="异常编码" width="120"></vxe-column>
                        </vxe-table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--    引入VUE-->
    <script src="../../../assets/vue@3.4.15/vue.global.prod.js"></script>
    <!--    引入vxe-table组件-->
    <script src="../../../assets/vxe-table/xe-utils.js"></script>
    <script src="../../../assets/vxe-table/vxe-table.js"></script>
    <!--    引入element-plus-->
    <script src="../../../assets/element-plus@2.5.5/index.js"></script>
    <!--  引入element-plus-icon-->
    <script src="../../../assets/icons-vue@2.3.1/index.iife.min.js"></script>
    <!-- 引入element-plus-ch -->
    <script src="../../../assets/element-plus@2.5.5/lang/zh-cn.js"></script>
    <!--    引入moment-->
    <script src="../../../assets/moment/moment.min.js"></script>

    <script>
        const { createApp, ref } = Vue
        
        const app = createApp({
            setup() {
                const tableData = ref([
                    {
                        id: 1,
                        sequence: '否',
                        partNumber: '消件件',
                        workOrder: 'E20241121',
                        engineNumber: '569919',
                        applicationNumber: '649-784-529-0',
                        partName: 'NUT CAPTIVE WASHER',
                        unitCode: 'QEC',
                        exceptionCode: 'SM30'
                    },
                    {
                        id: 2,
                        sequence: '否',
                        partNumber: 'Hardware',
                        workOrder: 'E20241018',
                        engineNumber: '598327',
                        applicationNumber: '2463M30P01',
                        partName: 'RING EXTERNAL RETAINING',
                        unitCode: 'SM30',
                        exceptionCode: 'SM30'
                    },
                    {
                        id: 3,
                        sequence: '是',
                        partNumber: '消件件',
                        workOrder: 'E20241018',
                        engineNumber: '598327',
                        applicationNumber: 'J1493P05A',
                        partName: 'BOLT',
                        unitCode: 'SM30',
                        exceptionCode: 'SM30'
                    },
                    {
                        id: 4,
                        sequence: '是',
                        partNumber: '消件件',
                        workOrder: 'E20241018',
                        engineNumber: '598327',
                        applicationNumber: 'J1494P05A',
                        partName: 'BOLT, MACHINE',
                        unitCode: 'SM30',
                        exceptionCode: 'SM30'
                    },
                    {
                        id: 5,
                        sequence: '是',
                        partNumber: '消件件',
                        workOrder: 'E20250308',
                        engineNumber: '802636',
                        applicationNumber: '1523M73P01',
                        partName: 'Forward heat shield pins and nuts',
                        unitCode: 'SM53',
                        exceptionCode: 'SM53'
                    }
                ])
                
                return {
                    tableData
                }
            }
        })
        
        // 注册Element Plus图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component)
        }
        
        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn,
        })
        app.use(VXETable)
        app.mount('#app')
        
        // 更新时间
        function updateTime() {
            document.getElementById('current-time').textContent = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        
        updateTime()
        setInterval(updateTime, 1000)
        
        console.log('测试页面已加载')
    </script>
</body>
</html> 