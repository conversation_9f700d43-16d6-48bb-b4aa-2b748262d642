import {
  getDutyManagerList,
  getDutyManagerSchedule,
  saveDutyManagerSchedule,
  getCurrentMonthDays,
} from '../../api/dutyManagerSchedule/index.js'
import DutyNote from './components/duty.note.js'
export default {
  name: 'DutyManagerSchedule',
  components: {
    DutyNote,
  },
  setup() {
    const { ref, onMounted } = Vue
    const { ElMessage } = ElementPlus

    // 当前选择的月份
    const currentMonth = ref(moment().format('YYYY-MM'))

    // 值班经理列表
    const managerOptions = ref([])
    const fetchManagerOptions = async () => {
      const res = await getDutyManagerList()
      managerOptions.value = res.map((item) => ({
        value: item.id_manager,
        label: item.str_name,
      }))
      managerOptions.value.unshift({
        value: -1,
        label: '请选择',
      })
    }

    onMounted(() => {
      fetchManagerOptions()
    })

    // 表格数据结构
    const tableConfig = ref({
      staticList: [],
      selectList: [],
    })
    // 获取表格数据结构
    const fetchTableConfig = async () => {
      const params = {
        int_year: currentMonth.value.split('-')[0],
        int_month: currentMonth.value.split('-')[1],
      }
      const res = await getDutyManagerSchedule(params)
      tableConfig.value.staticList = res.depts.map((item) => ({
        name: item.str_dept,
        date: item.dt_plan,
        value: item.int_num,
      }))
      tableConfig.value.selectList = res.managers.map((item) => ({
        id: item.id,
        name: 'On-duty Mg.',
        date: item.dt_plan,
        value: item.id_manager,
      }))
      if (tableConfig.value.selectList.length === 0) {
        tableConfig.value.selectList.push({
          name: 'On-duty Mg.',
          date: '',
          value: null,
        })
      }
      initTableData()
    }
    // 表格数据
    const tableData = ref([])

    // 初始化表格数据
    const initTableData = () => {
      getMonthDays()

      // 处理静态列表，按name分组
      const staticGroups = tableConfig.value.staticList.reduce((acc, item) => {
        if (!acc[item.name]) {
          acc[item.name] = { name: item.name }
        }
        acc[item.name][item.date] = item.value
        return acc
      }, {})

      // 处理可选择列表，按name分组
      const selectGroups = tableConfig.value.selectList.reduce((acc, item) => {
        if (!acc[item.name]) {
          acc[item.name] = { name: item.name, isSelect: true }
        }
        acc[item.name][item.date] = item.value
        return acc
      }, {})

      // 合并数据
      tableData.value = [...Object.values(selectGroups), ...Object.values(staticGroups)]
    }

    const monthDays = ref([])
    // 计算当前月份的日期数据
    const getMonthDays = async () => {
      const parms = {
        starttime: moment(currentMonth.value).startOf('month').format('YYYY-MM-DD'),
        endtime: moment(currentMonth.value).endOf('month').format('YYYY-MM-DD'),
      }
      const res = await getCurrentMonthDays(parms)
      monthDays.value = res.map((item) => ({
        date: item.dt_day,
        dayName: moment(item.dt_day).format('ddd'),
        fullDate: item.dt_day,
        holiday: item.str_name,
        isHoliday: item.is_holiday === 2,
        formattedDate: moment(item.dt_day).format('DD'),
      }))
      // const currentMoment = moment(currentMonth.value)
      // const daysInMonth = currentMoment.daysInMonth()
      // const year = currentMoment.year()
      // const month = currentMoment.format('MM')

      // // 生成日期数组
      // const days = []
      // for (let i = 1; i <= daysInMonth; i++) {
      //   const date = moment(`${year}-${month}-${String(i).padStart(2, '0')}`)
      //   const dayOfWeek = date.day() // 0是周日，1-6是周一到周六
      //   const dayName = date.format('ddd')

      //   // 只保留周六和周日
      //   if (dayOfWeek === 0 || dayOfWeek === 6) {
      //     days.push({
      //       date: i,
      //       dayName,
      //       dayOfWeek,
      //       fullDate: date.format('YYYY-MM-DD'),
      //       formattedDate: date.format('DD'),
      //     })
      //   }
      // }
      // console.log(days)

      // monthDays.value = test1
    }

    // 值班经理数据
    const dutyManagers = ref({})

    // 处理查询按钮点击
    const handleQuery = async () => {
      await fetchManagerOptions()
      await fetchTableConfig()
    }

    // 处理值班经理选择变化
    const handleManagerChange = async (value, date, row) => {
      const id = tableConfig.value.selectList.find((item) => item.date === date)?.id || null
      const params = {
        id,
        id_manager: value,
        dt_plan: date,
      }
      await saveDutyManagerSchedule(params)
      ElMessage({
        message: '保存成功',
        type: 'success',
      })
      handleQuery()
    }

    const openDutyNoteDialog = ref(false)
    const dutyNoteDialogData = ref({})
    // 处理值班经理备注点击
    const handleDutyNote = (row, date) => {
      dutyNoteDialogData.value = {
        row,
        date,
      }
      openDutyNoteDialog.value = true
    }

    // 初始化数据
    onMounted(async () => {
      await fetchTableConfig()
    })

    return {
      currentMonth,
      tableData,
      managerOptions,
      dutyManagers,
      monthDays,
      openDutyNoteDialog,
      dutyNoteDialogData,
      handleQuery,
      handleManagerChange,
      handleDutyNote,
    }
  },
  template: /*html*/ `
    <div class="duty-manager-schedule p-4">
      <h1 class="mb-4 text-xl font-bold">值班经理排班</h1>

      <div class="mb-4 flex items-center space-x-4">
        <el-date-picker
          v-model="currentMonth"
          type="month"
          placeholder="选择月份"
          format="YYYY-MM"
          value-format="YYYY-MM"
        />
        <el-button type="primary" @click="handleQuery">查询</el-button>
      </div>

      <!-- 表格部分 -->
      <el-table :data="tableData" border style="width: 100%">
        <!-- 第一列：指标名称 -->
        <el-table-column prop="name" label="Date" width="150" fixed="left">
          <template #default="scope">
            <span :class="{ 'font-bold': scope.row.type === 'odd' }">{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <!-- 动态生成日期列 -->
        <template v-for="day in monthDays" :key="day.fullDate">
          <el-table-column :label="day.dayName" align="center">
            <template #header>
              <span :class="{ 'font-bold': day.isHoliday }">
                {{ day.dayName }}
                <span v-if="day.isHoliday" class="text-red-500">({{ day.holiday }})</span>
              </span>
            </template>
            <el-table-column :label="day.formattedDate" align="center">
              <template #default="scope">
                <template v-if="scope.row.isSelect">
                  <el-select
                    v-model="scope.row[day.fullDate]"
                    placeholder="请选择值班经理"
                    @change="(value) => handleManagerChange(value, day.fullDate, scope.row)"
                    class="w-full"
                  >
                    <el-option
                      v-for="manager in managerOptions"
                      :key="manager.value"
                      :label="manager.label"
                      :value="manager.value"
                    />
                  </el-select>
                </template>
                <template v-else>
                  <span
                    class="cursor-pointer underline hover:text-blue-500"
                    :class="{ 'font-bold': scope.row.type === 'odd' }"
                    @click="handleDutyNote(scope.row, day.fullDate)"
                  >
                    {{ scope.row[day.fullDate] }}
                  </span>
                </template>
              </template>
            </el-table-column>
          </el-table-column>
        </template>
      </el-table>
      <DutyNote v-if="openDutyNoteDialog" v-model:isOpen="openDutyNoteDialog" :data="dutyNoteDialogData" />
    </div>
  `,
}
