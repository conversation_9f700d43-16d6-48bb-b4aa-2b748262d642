const { ref, reactive, onMounted, onBeforeUnmount } = Vue
const { useIntervalFn } = VueUse
// import { exceptionMonitorApi } from '../api/index.js'

export function useExceptionMonitor() {
  const isLoading = ref(false)
  const lastUpdateTime = ref(new Date())
  
  // 异常数据状态
  const exceptionState = reactive({
    totalExceptions: 0,
    pendingCount: 0,
    convertedCount: 0,
    scanningCount: 0
  })

  // 自动刷新数据
  const { pause, resume } = useIntervalFn(() => {
    refreshData()
  }, 5 * 60 * 1000) // 5分钟刷新一次

  // 更新时间显示
  const { pause: pauseTime, resume: resumeTime } = useIntervalFn(() => {
    lastUpdateTime.value = new Date()
  }, 1000)

  // 获取异常数据
  const fetchExceptionData = async () => {
    try {
      isLoading.value = true
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据更新
      exceptionState.totalExceptions = Math.floor(Math.random() * 50) + 20
      exceptionState.pendingCount = Math.floor(Math.random() * 15) + 5
      exceptionState.convertedCount = Math.floor(Math.random() * 15) + 5
      exceptionState.scanningCount = Math.floor(Math.random() * 15) + 5
      
      lastUpdateTime.value = new Date()
      
    } catch (error) {
      console.error('获取异常数据失败:', error)
      ElementPlus.ElMessage.error('数据获取失败，请稍后重试')
    } finally {
      isLoading.value = false
    }
  }

  // 刷新数据
  const refreshData = async () => {
    await fetchExceptionData()
  }

  // 手动刷新
  const manualRefresh = async () => {
    ElementPlus.ElMessage.info('正在刷新数据...')
    await refreshData()
    ElementPlus.ElMessage.success('数据刷新完成')
  }

  // 格式化时间
  const formatTime = (date) => {
    return moment(date).format('YYYY-MM-DD HH:mm:ss')
  }

  // 获取相对时间
  const getRelativeTime = (date) => {
    return moment(date).fromNow()
  }

  onMounted(() => {
    fetchExceptionData()
    resume() // 开始自动刷新
    resumeTime() // 开始时间更新
  })

  onBeforeUnmount(() => {
    pause()
    pauseTime()
  })

  return {
    isLoading,
    lastUpdateTime,
    exceptionState,
    refreshData,
    manualRefresh,
    formatTime,
    getRelativeTime
  }
} 