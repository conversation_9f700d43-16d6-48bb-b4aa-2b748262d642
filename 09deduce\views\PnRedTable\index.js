import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'
import EngineDrawer from './drawer.js'
import TotalDrawer from './totalDrawer.js'
import { post } from '../../../config/axios/httpReuest.js'
// 引入点位颜色抽屉组件
// import PointColotDrawer from '../../../09deduce/views/EngineSimulationPlan/components/pointColorDrawer.js'
const { onMounted, ref, reactive, toRefs } = Vue
// const { ElDatePicker } = ElementPlus

// 表格列配置
// 定义了表格的所有列,包括标题、字段名、过滤器等信息
const TABLE_COLUMNS = [
  {
    title: 'WO', // 工单号
    field: 'str_wo',
    minWidth: 35,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' }, // 使用输入框作为过滤器
  },
  {
    title: 'ESN',
    field: 'str_esn',
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: 'Flow',
    field: 'str_flow',
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '放行日期',
    field: 'dt_release',
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
    sortable: true,
  },
  {
    title: 'F3关闭日期',
    field: 'dt_f3_close',
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
    sortable: true,
  },
  {
    title: '机型',
    field: 'str_engine_type',
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' }, // 使用输入框作为过滤器
    // filters: [
    //   { label: 'LEAP', value: 'LEAP' },
    //   { label: 'CFM56', value: 'CFM56' },
    // ],
  },
  {
    title: 'SV级别',
    field: 'str_level',
    filters: [
      { label: 'OH', value: 'OH' },
      { label: 'SV', value: 'SV' },
      { label: 'NSV', value: 'NSV' },
      { label: 'MODULE', value: 'MODULE' },
    ],
  },
  {
    title: '客户',
    field: 'str_client',
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },

  {
    type: 'html',
    title: 'F2',
    field: 'node_15',
  },
  {
    type: 'html',
    title: '转包PO',
    field: 'node_111',
  },
  {
    type: 'html',
    title: '起运准备',
    field: 'node_11',
  },
  {
    type: 'html',
    title: '出口运输',
    field: 'node_12',
  },
  {
    type: 'html',
    title: '进口运输',
    field: 'node_8',
  },
  {
    type: 'html',
    title: '锁库-csm确认',
    field: 'node_411',
  },
  {
    type: 'html',
    title: '构型确认',
    field: 'node_412',
  },
  {
    type: 'html',
    title: '锁库',
    field: 'node_4',
  },
  {
    type: 'html',
    title: '行政接收',
    field: 'node_1',
  },
  {
    type: 'html',
    title: '进厂检验',
    field: 'node_2',
  },
  {
    type: 'html',
    title: '入库',
    field: 'node_3',
  },

  {
    type: 'html',
    title: '发料',
    field: 'node_5',
  },
  // {
  //   type: 'html',
  //   title: '点灯',
  //   field: 'node_6',
  // },

  {
    type: 'html',
    title: '转包',
    field: 'node_10',
  },

  {
    type: 'html',
    title: '采购-厂家交付',
    field: 'node_13',
  },
  {
    type: 'html',
    title: '待采购',
    field: 'node_14',
  },

  // {
  //   type: 'html',
  //   title: '已集件',
  //   field: 'node_999',
  // },
]

// 类型配置映射表
// 用于将字段名映射到对应的类型和标题
// type: 用于后端API识别的类型码
// title: 显示在抽屉中的标题
const TYPE_CONFIG = {
  node_15: { type: 15, title: 'F2' },
  node_111: { type: 111, title: '转包PO' },
  node_11: { type: 11, title: '起运准备' },
  node_12: { type: 12, title: '出口运输' },
  node_8: { type: 8, title: '进口运输' },
  node_412: { type: 412, title: '构型确认' },
  node_4: { type: 4, title: '锁库' },
  node_1: { type: 1, title: '行政接收' },
  node_2: { type: 2, title: '进厂检验' },
  node_3: { type: 3, title: '入库' },
  node_5: { type: 5, title: '发料' },
  node_10: { type: 10, title: '转包' },
  node_13: { type: 13, title: '采购-厂家交付' },
  node_14: { type: 14, title: '待采购' },
}

// 不可点击列的配置
// 这些列不会触发抽屉打开事件
const DISABLED_COLUMNS = [
  'ekdpercent',
  'str_wo',
  'str_esn',
  'str_flow',
  'dt_release',
  'dt_f3_close',
  'str_engine_type',
  'str_level',
  'str_client',
]

/**
 * 推演计划零件处置统计表组件
 */
const PartStatistical = {
  // 注册需要使用的子组件
  components: {
    HtVxeTable,
    EngineDrawer,
    TotalDrawer,
    // PointColotDrawer,
    // 'el-date-picker': ElDatePicker,
  },

  setup() {
    // 表格引用,用于调用表格实例方法
    const tableRef = ref(null)

    // 表格状态管理
    const tableState = reactive({
      data: null, // 表格数据
      columns: TABLE_COLUMNS.map((column) => ({
        ...column,
        // 为html类型的列添加统一的格式化函数,实现悬浮效果
        ...(column.type === 'html' && {
          formatter: ({ cellValue }) => {
            if (cellValue) {
              return `<span class="text-red-500 hover:cursor-pointer hover:text-blue-500 hover:underline">${cellValue}</span>`
            } else {
            }
          },
        }),
      })),
      footerData: [], // 表格底部数据
    })

    // 普通抽屉状态管理
    const drawerState = reactive({
      visible: false, // 控制抽屉显示/隐藏
      type: '', // 当前选中的类型
      title: '', // 抽屉标题
      idWo: '', // 工单ID
    })

    // 总量抽屉状态管理
    const totalDrawerState = reactive({
      visible: false,
      type: '',
      title: '',
      idWo: '',
    })

    /**
     * 获取表格数据
     * 通过API获取数据并更新到表格状态中
     */
    const getTableData = async () => {
      try {
        const param = {
          ac: 'de_red_back_table',
          //filter_fields: Object.assign(queryLists, ), // 默认查询待确认的
        }
        const { data } = await post(param)
        const { data: res, code, text } = data
        if (code !== 'success') {
          ElementPlus.ElMessage.error(text)
          return
        }
        tableState.data = res
      } catch (error) {
        ElementPlus.ElMessage.error('获取数据失败')
        console.error(error)
      }
    }

    /**
     * 导出表格数据
     * 调用表格实例的导出方法
     */
    const exportTableData = () => {
      tableRef.value?.exportData()
    }

    const openPointColorDrawer = reactive({
      pointColorType: '',
      idWo: 0,
      pointColotDrawerVisible: false,
      title: '',
      int_node_type: '',
      filterFields: [],
      simulationType: '1',
      pointColorType: '110',
    })

    /**
     * 打开抽屉处理函数
     * @param {Object} row - 当前行数据
     * @param {Object} column - 当前列配置
     */
    const openDrawer = ({ row, column }) => {
      const { field } = column
      // 如果是不可点击列,直接返回
      if (DISABLED_COLUMNS.includes(field)) return
      openPointColorDrawer.idWo = row.id_wo
      openPointColorDrawer.title = column.title
      openPointColorDrawer.int_node_type = field.split('_')[1]
      openPointColorDrawer.filterFields = [{ str_key: 'int_site', str_value: [field.split('_')[1]] }]
      openPointColorDrawer.pointColotDrawerVisible = true
    }
    const onSortChangeEvent = (sort) => {
      // 排序事件处理函数
      // 更新表格数据
      getTableData(sort)
    }
    const footerMethod = () => {
      tableState.footerData = []
      // Check if there is data before proceeding
      if (!tableState.data || !tableState.data.length) {
        return [[]] // Return empty 2D array when no data
      }

      // Create footer array matching TABLE_COLUMNS order
      let footerRow = Array(TABLE_COLUMNS.length).fill('') // Initialize with empty strings

      // Set "Total" in first column
      footerRow.unshift('Total')

      // Calculate sums for numeric columns in correct order
      TABLE_COLUMNS.forEach((column, index) => {
        if (column.type === 'html' && column.field !== 'int_total') {
          // Only sum numeric (html) columns
          const sum = tableState.data.reduce((acc, curr) => acc + (curr[column.field] || 0), 0)
          footerRow[index + 1] = sum
        }
      })

      let footerObject = {}
      // Convert footerRow array to array of objects
      const footerRowObject = footerRow.map((value, index) => {
        // Get the field name from TABLE_COLUMNS, accounting for the "Total" offset
        const field = index === 0 ? 'seq' : TABLE_COLUMNS[index - 1]?.field || `column${index}`
        Object.assign(footerObject, {
          [field]: value,
        })
        return footerObject
      })
      tableState.footerData = [footerRowObject[0]]
      return [footerRow]
    }
    /**统计行点击事件 */
    const onFooterCellClick = ({ row, column }) => {
      const { field } = column
      // 获取类型配置
      const config = TYPE_CONFIG[field]
      if (!config) return
      let dataTableIdWo = tableState.data
        .filter((item) => item[field] > 0)
        .map((item) => item.id_wo)
        .join(',') // 多个ID_wo用逗号分隔

      openPointColorDrawer.idWo = dataTableIdWo || '0'
      openPointColorDrawer.title = config.title
      openPointColorDrawer.int_node_type = field.split('_')[1]
      openPointColorDrawer.filterFields = [{ str_key: 'int_site', str_value: [field.split('_')[1]] }]
      openPointColorDrawer.pointColotDrawerVisible = true
      // // 打开普通抽屉
      // Object.assign(drawerState, {
      //   visible: true,
      //   type: config.type,
      //   title: config.title,
      //   idWo: dataTableIdWo,
      //   int_type: field,

      // })
    }
    const jumpMap = {
      a: '集件零件管理',
      b: '用料申请',
      c: '零件报表',
      d: '转包零件跟踪',
      e: '锁库处理',
      f: '现场串件',
      g: '零件历程',
      h: '采购_厂家交付',
    }
    const jumpMapId = {
      a: '1435766996880461825',
      b: '1453234319950618624',
      c: '1435766981386702849',
      d: '1435766992040235009',
      e: '1453282700483895296',
      f: '1697500556140101633',
      g: '1826524987646808064',
      h: '1435766977628606465',
    }
    const idKeyMap = {
      a: 'id_pkp',
      c: 'id_offlog',
      b: 'id_apply',
      d: 'id_po_sub_sc',
      e: 'id_apply',
      f: 'id_se_audit',
      g: 'id_pkp',
      h: 'id_po_sub',
    }
    const handleJump = (command) => {
      // 没有选中数据
      if (!tableRef.value.getSelectedData().length) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }
      const idList =
        tableRef.value
          .getSelectedData()
          .map((item) => item[idKeyMap[command]])
          .filter((item) => item !== null)
          .join(',') ?? ''

      if (idList === '') {
        ElementPlus.ElMessage.warning(`无${jumpMap[command]}信息`)
        return
      }
      const getUrl = (command, idList) => {
        const baseUrl = `/Page/?moduleid=${jumpMapId[command]}`
        return command === 'e' ? `${baseUrl}&qrc_id_main=${idList}` : `${baseUrl}&qrc_id=${idList}`
      }
      com.refreshTab('*' + jumpMap[command], getUrl(command, idList))
    }
    // 组件挂载时获取表格数据
    onMounted(getTableData)
    // 返回模板需要使用的属性和方法
    return {
      ...toRefs(tableState),
      tableRef,
      exportTableData,
      openDrawer,
      drawerState,
      totalDrawerState,
      onSortChangeEvent, // 暴露排序事件处理函数
      openPointColorDrawer,
      onFooterCellClick,
      footerMethod,
      handleJump,
    }
  },

  // 组件模板
  template: /*html*/ `
    <!-- 导出按钮 -->
    <div class="mr-4 mt-4 flex items-center justify-end space-x-2">
      <el-dropdown @command="handleJump">
        <el-button type="primary">
          跳转
          <el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="a">集件零件管理（On-log）</el-dropdown-item>
            <el-dropdown-item command="b">用料申请</el-dropdown-item>
            <el-dropdown-item command="c">零件报表（Off-log）</el-dropdown-item>
            <el-dropdown-item command="d">转包跟踪</el-dropdown-item>
            <el-dropdown-item command="e">锁库处理</el-dropdown-item>
            <el-dropdown-item command="g">零件历程</el-dropdown-item>
            <template v-if="type === 104">
              <el-dropdown-item command="f">现场串件</el-dropdown-item>
            </template>
            <el-dropdown-item command="h">采购_厂家交付</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button type="primary" size="mini" @click="exportTableData">导出</el-button>
    </div>

    <!-- 表格容器 -->
    <div class="ml-4 mr-4 mt-2" style="height: calc(100vh - 60px)">
      <HtVxeTable
        ref="tableRef"
        :tableData="data"
        :tableColumns="columns"
        :isShowHeaderCheckbox="true"
        :showFooter="true"
        @cell-click="openDrawer"
        @sort-change="onSortChangeEvent"
        :footerData="footerData"
        :footerMethod="footerMethod"
        @footer-cell-click="onFooterCellClick"
      />
    </div>

    <EngineDrawer
      v-if="openPointColorDrawer.pointColotDrawerVisible"
      v-model:visible="openPointColorDrawer.pointColotDrawerVisible"
      :type="openPointColorDrawer.pointColorType"
      :simulationType="openPointColorDrawer.simulationType"
      :filterFields="openPointColorDrawer.filterFields"
      :id="pointId"
      :idWo="openPointColorDrawer.idWo"
      :title="openPointColorDrawer.title"
      :isShowBtnInput="false"
    ></EngineDrawer>
  `,
}

export default PartStatistical
