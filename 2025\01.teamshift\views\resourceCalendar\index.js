// 确保在 HTML 中已引入 Vue, Element Plus (JS/CSS), Element Plus Icons, moment.js, 和 Tailwind CSS
import { getResourceCalendar } from '../../api/resourceCalendar/index.js'
import { calculateTableHeight } from '../../utils/common.js'
import { isWeekend, getWeekday, checkIsWeekend } from '../../utils/tools.js'
// 引入 Vue Composition API 函数
// 假设在 HTML 中 Vue, moment 等已全局可用, 且 Element Plus 图标已注册

export default {
  name: 'ResourceCalendar',
  // 使用 setup 函数
  setup() {
    // --- 状态管理 ---
    // 注意: 在 setup 中使用 ref 和 computed 需要从 Vue 中导入
    // 如果 Vue 是全局引入的，可以直接使用 Vue.ref, Vue.computed
    const { ref, computed, onMounted } = Vue // 假设 Vue 是全局变量

    const { ElMessage } = ElementPlus
    const { Search } = ElementPlusIconsVue

    // 使用 moment.js 替代 dayjs (需求要求)
    const defaultEndDate = moment().add(30, 'days').toDate()
    const dateRange = ref([new Date(), defaultEndDate])
    const searchQuery = ref({})
    const tableDataRaw = ref([]) // 初始化为空数组

    // --- 计算属性 ---
    const filteredTableData = computed(() => {
      return tableDataRaw.value
    })

    // 根据日期范围生成日期列定义
    const dateColumns = computed(() => {
      if (!dateRange.value || dateRange.value.length !== 2) {
        return []
      }

      // 使用 moment.js 处理日期计算
      const start = moment(dateRange.value[0])
      const end = moment(dateRange.value[1])
      const dates = []

      // 创建当前日期的副本用于迭代
      let current = start.clone()

      // 使用 moment.js 的日期比较和迭代
      while (current.isSameOrBefore(end, 'day')) {
        dates.push(current.format('YYYY-MM-DD'))
        current.add(1, 'days') // moment 修改了原对象，不需要重新赋值
      }

      return dates
    })

    // --- 方法 ---
    const handleSearch = async () => {
      // 过滤在 computed 中完成
      await getTableData()
    }

    const handleDateChange = () => {
      getTableData() // 日期变化时重新获取数据
    }

    // 格式化日期列的表头
    const formatDateHeader = (dateString) => {
      return moment(dateString).format('MM/DD')
    }

    // 获取任务完成率卡片的样式
    const getTaskCardStyle = (current, total) => {
      if (!total || total === 0) return 'bg-gray-100 text-gray-500' // 处理 total 为 0 或无效的情况
      const ratio = current / total
      if (ratio < 0.5) return 'bg-green-100 text-green-800'
      else if (ratio < 0.8) return 'bg-yellow-100 text-yellow-800'
      else if (ratio >= 1)
        return 'bg-red-100 text-red-800' // 明确大于等于1为红色
      else return 'bg-orange-100 text-orange-800' // 0.8 到 1 之间用橙色
    }

    const getTableData = async () => {
      try {
        const params = {
          dt_start: moment(dateRange.value[0]).format('YYYY-MM-DD'),
          dt_end: moment(dateRange.value[1]).format('YYYY-MM-DD'),
          str_sm: searchQuery.value.str_sm,
          str_flow: searchQuery.value.str_flow,
          str_engine_type: searchQuery.value.str_engine_type,
          str_team: searchQuery.value.str_team,
          str_staff: searchQuery.value.str_staff,
          id_dept: searchQuery.value.id_dept,
        }
        const res = await getResourceCalendar(params)

        if (Array.isArray(res)) {
          const processedData = res.map((teamData, teamIndex) => {
            const subTeams = teamData.resource || []
            const totalCapacity = subTeams.length
            const unitCell = subTeams.map((subTeam) => ({
              id: subTeam.id || `sub-${teamIndex}-${subTeam.str_name}`,
              col1: subTeam.str_name || '-',
              col2: subTeam.str_skill || '无技能信息',
              col3: subTeam.is_auth?'有授权':'未授权' ,
            }))

            const dailyTasks = {}
            const dailyLoad = {}
            const dailyShift = {}
            // 初始化所有日期的dailyLoad
            dateColumns.value.forEach((date) => {
              dailyLoad[date] = { current: 0, total: totalCapacity || 0 } // 确保total至少为0
              let shift = teamData.shift_list.find(x => x.dt_plan === date)
              if (!shift) {
                shift = { str_shift: '未知班次' }
              }
              dailyShift[date] = {str_shift:shift.str_shift,str_leave_type:shift.str_leave_type}
            })

            if (Array.isArray(teamData.plan_task)) {
              teamData.plan_task.forEach((task, taskIndex) => {
                if (!task.pt_dt) return

                const dateStr = moment(task.pt_dt).format('YYYY-MM-DD')

                // 初始化当天的任务记录
                if (!dailyTasks[dateStr]) {
                  dailyTasks[dateStr] = []
                }

                // 添加任务详情
                const taskText = `${task.str_esn || '-'}/${task.str_sm || '-'}/${task.str_shift || '-'}/${task.str_task || '-'}`
                dailyTasks[dateStr].push({
                  id: task.id || `task-${teamIndex}-${dateStr}-${taskIndex}`,
                  text: taskText,
                  str_shift: task.str_shift
                })

                // 累加当天负载
                const subValue = parseFloat(task.int_sub)
                if (!isNaN(subValue) && dailyLoad[dateStr]) {
                  dailyLoad[dateStr].current += subValue
                }
              })
            }

            return {
              id: teamData.id || `team-${teamIndex}`,
              team: teamData.str_team || '未知团队',
              unitCell: unitCell,
              dailyTasks: dailyTasks,
              dailyLoad: dailyLoad,
              dailyShift: dailyShift,
              totalCapacity: totalCapacity || 0, // 确保totalCapacity至少为0
            }
          })
          tableDataRaw.value = processedData
        } else {
          console.error('获取资源日历数据格式错误:', res)
          tableDataRaw.value = []
          ElMessage.error('加载日历数据失败: 数据格式错误')
        }
      } catch (error) {
        console.error('请求资源日历数据时出错:', error)
        tableDataRaw.value = []
        ElMessage.error('加载日历数据时发生网络错误')
      }
    }

    const handleHeaderCellClassName = ({ column }) => {
      // 只处理展开状态下的周末列
      if (column && column.label) {
        if (checkIsWeekend(column.property)) {
          return 'bg-weekend'
        }
      }
      return ''
    }
    onMounted(() => {
      getTableData()
    })

    // --- 返回需要在模板中使用的数据和方法 ---
    return {
      dateRange,
      defaultEndDate, // 需要返回给模板的 :default-value
      searchQuery,
      tableDataRaw, // 如果模板直接用了原始数据也需要返回
      filteredTableData,
      dateColumns,
      Search,
      handleSearch,
      handleDateChange,
      formatDateHeader,
      getTaskCardStyle,
      handleHeaderCellClassName,
    }
  },

  // --- 模板 (保持不变) ---
  template: /*html*/ `
    <div class="flex flex-col p-4">
      <!-- 头部搜索区域 -->
      <el-card shadow="hover" class="mb-2 border-0">
        <div class="flex flex-wrap items-center gap-6">
          <div class="flex items-center gap-3">
            <label class="text-sm font-medium text-gray-600">日期范围:</label>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateChange"
              :default-value="[new Date(), defaultEndDate]"
              class="w-64"
            />
          </div>
          <div class="flex items-center gap-3">
            <label class="w-16 text-sm font-medium text-gray-600">单元体:</label>
            <el-input v-model="searchQuery.str_sm" placeholder="输入单元体" clearable class="!w-52"></el-input>
          </div>
          <div class="flex items-center gap-3">
            <label class="w-12 text-sm font-medium text-gray-600">FLOW:</label>
            <el-select v-model="searchQuery.str_flow" class="!w-52" clearable>
              <el-option label="F1-1" value="F1-1"></el-option>
              <el-option label="F4-1" value="F4-1"></el-option>
            </el-select>
          </div>
           <div class="flex items-center gap-3">
            <label class="w-12 text-sm font-medium text-gray-600">Sec:</label>
            <el-select v-model="searchQuery.id_dept" class="!w-52" clearable>
              <!--<el-option label="RUPI" value="1435274186582528001"></el-option>-->
              <el-option label="RUPB2/3" value="1435274186590916609"></el-option>
               <el-option label="RUPB1" value="1435274186599305217"></el-option>
            </el-select>
          </div>
          <div class="flex items-center gap-3">
            <label class="w-12 text-sm font-medium text-gray-600">机型:</label>
            <el-input v-model="searchQuery.str_engine_type" placeholder="输入机型" clearable class="!w-52"></el-input>
          </div>
          <div class="flex items-center gap-3">
            <label class="w-12 text-sm font-medium text-gray-600">Team:</label>
            <el-input v-model="searchQuery.str_team"  clearable class="!w-52"></el-input>
          </div>
          <div class="flex items-center gap-3">
            <label class="w-12 text-sm font-medium text-gray-600">Staff:</label>
            <el-input v-model="searchQuery.str_staff" placeholder="输入员工名称" clearable class="!w-52"></el-input>
          </div>
          <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
        </div>
      </el-card>

      <!-- 主题表格区域 -->
      <el-card shadow="hover" class="resource-calendar-card flex-1 border-0">
        <el-table
          ref="resourceCalendarTable"
          :data="filteredTableData"
          border
          stripe
          table-layout="fixed"
          :header-cell-style="{ backgroundColor: '#f5f7fa' }"
          class="resource-calendar-table"
          height="calc(100vh - 180px)"
          :header-cell-class-name="handleHeaderCellClassName"
        >
          <el-table-column type="index" label="序号" width="80" fixed="left" />
          <!-- 固定列 - Team -->
          <el-table-column prop="team" label="Team" width="150" fixed="left" />

          <!-- 固定列 - 单元体 (复杂渲染) -->
          <el-table-column label="资源" width="250" fixed="left">
            <template #default="{ row }">
              <div class="space-y-1 p-1">
                <div v-for="item in row.unitCell" :key="item.id" class="grid  gap-1 text-xs">
                  <el-tooltip :content="item.col1" placement="top" :disabled="!item.col1 || item.col1.length < 15">
                    <div class="truncate font-medium " style="font-size: 14px;font-weight: 800;color: #101010;">{{ item.col1 }} {{item.col3}}</div>
                  </el-tooltip>
                 <!-- <el-tooltip :content="item.col2" placement="top" :disabled="!item.col2 || item.col2.length < 15">
                    
                  </el-tooltip>-->
                  <div class=" text-gray-500">{{ item.col2 }}</div>
                </div>
                <div v-if="!row.unitCell || row.unitCell.length === 0" class="p-1 text-xs text-gray-400">
                  无单元体信息
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 动态日期列 -->
          <el-table-column
            
            v-for="date in dateColumns"
            :key="date"
            :prop="date"
            :label="formatDateHeader(date)"
            width="180"
            align="center"
            class-name="date-column"
          >
            <template #header="{ column }">
              <div class="text-xs font-medium">{{ formatDateHeader(date) }}</div>
            </template>
            <template #default="{ row }">
              <div class="relative flex min-h-[50px] flex-col items-center justify-start p-1 text-xs">
                <!-- 任务容量卡片 (使用 dailyLoad) -->
                <div
                  v-if="row.dailyLoad && row.dailyLoad[date]"
                  class="task-badge absolute right-0 top-0 rounded-bl-md px-1 py-0.5 text-[10px]"
                  :class="getTaskCardStyle(row.dailyLoad[date].current, row.dailyLoad[date].total)"
                >
                  {{ Math.floor(row.dailyLoad[date].current) }}/{{ row.dailyLoad[date].total }} {{row.dailyShift[date].str_shift}} {{row.dailyShift[date].str_leave_type}}
                </div>
                <div
                  v-else
                  class="absolute right-0 top-0 rounded-bl-md bg-gray-100 px-1 py-0.5 text-[10px] text-gray-400"
                >
                  0/{{ row.totalCapacity || 0 }}
                </div>
              
                <!-- 任务列表 -->
                <div class="task-content mt-3 w-full space-y-0.5">
                  <template v-if="row.dailyTasks && row.dailyTasks[date] && row.dailyTasks[date].length > 0">
                    <div v-for="task in row.dailyTasks[date]" :key="task.id" class="task-item w-full">
                      <el-tooltip :content="task.text" placement="top" :disabled="!task.text || task.text.length < 5">
                        <div class="truncate text-left">{{ task.text }}</div>
                      </el-tooltip>
                    </div>
                  </template>
                  <template v-else>
                    <span class="text-xs text-gray-400">-</span>
                  </template>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  `,
}
