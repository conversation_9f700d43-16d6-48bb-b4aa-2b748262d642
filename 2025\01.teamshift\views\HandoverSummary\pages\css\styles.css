/* 交接汇总自定义样式 - 基于 Tailwind CSS */

/* 自定义渐变背景 */
.gradient-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 自定义阴影 */
.shadow-header {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.shadow-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.shadow-card-hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* 业务类型标题装饰条 */
.title-decorator::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  display: inline-block;
  margin-right: 0.5rem;
}

/* 统计卡片顶部装饰条 */
.card-top-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}



/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
} 