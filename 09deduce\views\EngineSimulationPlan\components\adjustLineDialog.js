const { useVModel } = VueUse
const LineDialog = {
  props: {
    visible: Boolean,
    currentData: Object,
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)
    // 提交
    const lineDialogSumbit = () => {
      emit('submit', props.currentData)
    }
    // 禁用日期
    const disabledDate = (time) => {
      if (!props.currentData.disabledDate) return false
      return moment(time).isSameOrAfter(props.currentData.disabledDate)
    }
    return {
      visible,
      lineDialogSumbit,
      disabledDate,
    }
  },
  template: /*html*/ `
    <el-dialog class="my-dialog" v-model="visible" :show-close="false" width="30%">
      <template #header>
        <div class="flex justify-between">
          <div class="text-sm text-white">线条调整</div>
          <el-button size="small" type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <!-- 主体 -->
      <div class="mt-4">
        <el-form :model="currentData" label-width="100px">
          <el-form-item label="当前日期">
            <el-date-picker
              v-model="currentData.currentCloseDate"
              type="date"
              placeholder="选择日期"
              clearable
              value-format="YYYY-MM-DD"
              disabled
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="目标日期">
            <el-date-picker
              v-model="currentData.targetCloseDate"
              type="date"
              placeholder="选择日期"
              clearable
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <!-- 底部按钮 -->
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="lineDialogSumbit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  `,
}

export default LineDialog
