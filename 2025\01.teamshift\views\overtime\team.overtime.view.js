import { calculateTableHeight } from '../../utils/common.js'
import HtVxeTable from '../../components/VxeTable/HtVxeTable.js'
import { getDelayApplyList, submitApprove } from '../../api/timeover/index.js'
export default {
  components: {
    HtVxeTable,
  },
  name: 'TeamOvertimeView',
  setup() {
    const { nextTick, ref, onMounted, onUnmounted } = Vue
    const { ElMessage } = ElementPlus
    const statusColor = (value) => {
      if (value === 0) return 'text-green-500'
      if (value === 301) return 'text-blue-500'
      if (value === 1) return 'text-green-500'
      if (value === -1) return 'text-red-500'
      return ''
    }
    const statusText = (value) => {
      if (value === 0) return '草稿'
      if (value === 301) return '待审批'
      if (value === 1) return '审批通过'
      if (value === -1) return '审批失败'
      if (value === -99) return '撤回'
      return ''
    }

    // 容器
    const tableContainerRef = ref(null)
    // 表格高度
    const tableHeight = ref('400px')
    // 表格数据
    const tableData = ref([])

    const statusFormatter = ({ cellValue }) => {
      if (cellValue === 0) return '<span class="text-green-500">草稿</span>'
      if (cellValue === 301) return '<span class="text-blue-500">待审批</span>'
      if (cellValue === 1) return '<span class="text-green-500">审批通过</span>'
      if (cellValue === -1) return '<span class="text-red-500">审批失败</span>'
      if (cellValue === -99) return '<span class="text-red-500">撤回</span>'
      return cellValue
    }
    // 表格列
    const tableColumns = ref([
      {
        field: 'dt_delay',
        title: '日期',
        minWidth: 120,
        filterRender: { name: 'FilterCalendar' },
        filters: [{ data: '' }],
      },
      {
        field: 'shift_name',
        title: '班次',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'shift_time',
        title: '班次时间',
        minWidth: 150,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'int_delay',
        title: '延时时长',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'delay_time',
        title: '延时范围',
        minWidth: 150,
      },
      {
        field: 'team_name',
        title: 'Team',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'staff_name',
        title: '人员',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'int_substatus', title: '状态', minWidth: 120,
        formatter: statusFormatter,
        type: 'html',
        filters: [
          { label: '草稿', value: 0 },
          { label: '待审批', value: 301 },
          { label: '审批通过', value: 1 },
          { label: '审批不通过', value: -1 },
          { label: '撤回', value: -99 }
        ],
        filterMultiple: false,

      },
      {
        field: 'str_sec_manage',
        title: '审批人',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
    ])
    // 表格Dom
    const xTableRef = ref(null)
    const filterParams = ref({})
    const handleFilterChange = (params) => {
      pagination.currentPage = 1
       let field = params.column.field;
      if(params.column.field=='int_substatus'){field='status'};
      filterParams.value[field] = params.datas?.[0] ||  params.filters.find(x=>x.field==params.column.field).values[0]
      getTableData()
    }
    // 获取表格数据
    const getTableData = async () => {
      const res = await getDelayApplyList({
        CurrentPage: pagination.value.currentPage,
        PageSize: pagination.value.pageSize,
        // status: 1,
        ...filterParams.value,
      })
      tableData.value = res.items
      pagination.value.total = res.totalCount || 0
    }

    // 审批按钮是否禁用(默认禁用)
    const approveButtonDisabled = ref(true)

    // 复选框改变
    const handleCheckboxChange = ({ checked, row }) => {
      if (checked) {
        // 启用审批按钮
        approveButtonDisabled.value = false
      } else {
        // 禁用审批按钮
        approveButtonDisabled.value = true
      }
    }

    // 审批
    const handleApprove = () => {
      // 获取选中的行
      const selectedRows = xTableRef.value.getSelectedData()
      if (selectedRows.length === 0) {
        ElMessage.warning('请选择要审批的行')
        return
      }
      approveDialogVisible.value = true
      approveForm.value = selectedRows[0]
    }

    onMounted(() => {
      getTableData()
    })

    // 分页
    const pagination = ref({
      currentPage: 1,
      pageSize: 10,
      total: 0,
    })
    // 处理分页
    const handleCurrentChange = (currentPage) => {
      pagination.value.currentPage = currentPage.currentPage
      getTableData()
    }
    const handleSizeChange = (pageSize) => {
      pagination.value.pageSize = pageSize
      getTableData()
    }


    // 审批弹窗
    const approveDialogVisible = ref(false)
    // 审批表单
    const approveForm = ref({
      // 备注
      remark: '',
    })

    // 审批失败/通过
    const handleConfirm = async (type) => {
      const params = {
        id: approveForm.value.id,
        status: type === 'fail' ? -1 : 1,
        str_content: approveForm.value.remark,
      }
      if (type === 'fail') {
        // 审批失败
        // 必须填写备注
        if (!approveForm.value.remark) {
          ElMessage.warning('请填写备注')
          return
        }
      }
      // 提交审批
      await submitApprove(params)
      ElMessage.success('审批成功')
      approveDialogVisible.value = false
      getTableData()
    }

    // 窗口大小变化监听器
    let resizeObserver = null

    const handleCalculateTableHeight = () => {
      nextTick(() => {
        tableHeight.value = calculateTableHeight(tableContainerRef)
      })
    }

    onMounted(() => {
      handleCalculateTableHeight()

      // 监听窗口大小变化
      resizeObserver = new ResizeObserver(handleCalculateTableHeight)
      window.addEventListener('resize', handleCalculateTableHeight)

      // 如果在iframe中，尝试监听iframe大小变化
      if (window !== window.parent) {
        try {
          const iframe = window.frameElement
          if (iframe && resizeObserver) {
            resizeObserver.observe(iframe)
          }
        } catch (e) {
          console.error('监听iframe大小变化失败:', e)
        }
      }

      // 监听表格容器大小变化
      if (tableContainerRef.value && resizeObserver) {
        resizeObserver.observe(tableContainerRef.value)
      }
    })

    onUnmounted(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', handleCalculateTableHeight)

      // 断开ResizeObserver连接
      if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
      }
    })

    return {
      tableContainerRef,
      approveDialogVisible,
      approveForm,
      tableHeight,
      tableData,
      tableColumns,
      pagination,
      xTableRef,
      approveButtonDisabled,
      statusColor,
      statusText,
      handleCurrentChange,
      handleSizeChange,
      handleApprove,
      handleCheckboxChange,
      handleConfirm,
      handleFilterChange,
    }
  },
  template: /*html*/ `
    <div class="h-screen p-2" ref="tableContainerRef">
      <!-- 操作按钮 -->
      
      <!-- 表格 -->
      <HtVxeTable
        ref="xTableRef"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :height="tableHeight"
        @checkbox-change="handleCheckboxChange"
        @filterChange="handleFilterChange"
      >
        <template #checkbox>
          <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
        </template>
      </HtVxeTable>

      <!-- 分页组件 -->
      <div class="mt-4 flex justify-end">
        <vxe-pager
          background
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :layouts="['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']"
          @page-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></vxe-pager>
      </div>
    </div>
    <!-- 审批弹窗 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="审批"
      width="50%"
      destroy-on-close
      :append-to-body="true"
      class="common-dialog"
    >
      <el-descriptions v-if="approveForm" :column="1" border>
        <el-descriptions-item label="日期">{{ approveForm.dt_delay }}</el-descriptions-item>
        <el-descriptions-item label="班次">{{ approveForm.shift_name }}</el-descriptions-item>
        <el-descriptions-item label="班次时间">{{ approveForm.shift_time }}</el-descriptions-item>
        <el-descriptions-item label="延时时长">{{ approveForm.int_delay }}</el-descriptions-item>
        <el-descriptions-item label="延时范围">{{ approveForm.delay_time }}</el-descriptions-item>
        <el-descriptions-item label="Team">{{ approveForm.team_name }}</el-descriptions-item>
        <el-descriptions-item label="人员">{{ approveForm.staff_name }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <span :class="statusColor(approveForm.int_substatus)">{{ statusText(approveForm.int_substatus) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="审批人">{{ approveForm.str_sec_manage }}</el-descriptions-item>
      </el-descriptions>
      <!-- 备注 -->
      <label class="el-form-item__label">备注</label>
      <el-input v-model="approveForm.remark" type="textarea" :rows="4" placeholder="请输入备注" />
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button type="danger" @click="handleConfirm('fail')">审批失败</el-button>
          <el-button type="primary" @click="handleConfirm('pass')">审批通过</el-button>
          <el-button @click="approveDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  `,
}
