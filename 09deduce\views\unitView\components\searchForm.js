/**
 * @description 头部搜索
 * @date 2024-07-20
 * <AUTHOR>
 */
import { useOption } from '../../../hooks/useOption.js'

const { ref, reactive, onMounted } = Vue
const SearchForm = {
  emits: ['search'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const collapseStatus = ref(true)
    const formData = reactive({
      str_esn: '',
      str_wo: '',
      str_sm: '',
      str_pn: '',
      id_engine_type: '',
      dt_f41_date: '',
      dt_release_date: '',
      id_b123: '',
      str_main_sm: '',
      int_site: '',
      id_dept: '',
      int_maintenance_type: '',
      str_flow: '',
      dt_f23_date: '',
    })
    const {
      siteOptions,
      getSiteOptions,
      deptOptions,
      getDeptOptions,
      maintenaceOptions,
      getMaintenaceOptions,
      flowOptions,
      getFlowOptions,
    } = useOption()
    // * 查询
    const handleSearch = () => {
      emit('search', formData)
    }
    // * 重置
    const handleReset = () => {
      formRef.value.reset()
    }

    onMounted(() => {
      getSiteOptions()
      getDeptOptions()
      getMaintenaceOptions()
      getFlowOptions()
    })

    return {
      formRef,
      collapseStatus,
      formData,
      handleSearch,
      handleReset,
      siteOptions,
      deptOptions,
      maintenaceOptions,
      flowOptions,
    }
  },
  template: /*html*/ `
    <vxe-form
      ref="formRef"
      v-model:collapseStatus="collapseStatus"
      :data="formData"
      prevent-submit
      custom-layout
      size="small"
      title-align="right"
      title-width="120px"
    >
      <vxe-form-item title="ESN:" field="str_esn" :item-render="{}" span="6">
        <template #default="{ data }">
          <vxe-input v-model="data.str_esn" clearable placeholder="请输入ESN" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="WO:" field="str_wo" :item-render="{}" folding span="6">
        <template #default="{ data }">
          <vxe-input v-model="data.str_wo" clearable placeholder="请输入WO" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="SM:" field="str_sm" :item-render="{}" folding span="6">
        <template #default="{ data }">
          <vxe-input v-model="data.str_sm" clearable placeholder="请输入SM" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="PN:" field="str_pn" :item-render="{}" folding span="6">
        <template #default="{ data }">
          <vxe-input v-model="data.str_pn" clearable placeholder="请输入PN" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Engine Type:" field="id_engine_type" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.id_engine_type" clearable placeholder="请选择Engine Type">
            <vxe-option label="CFM56" value="CFM56"></vxe-option>
            <vxe-option label="LEAP" value="LEAP"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="F4-1 Begin Time:" field="dt_f41_date" :item-render="{}" folding span="6">
        <template #default="{data}">
          <el-date-picker
            v-model="data.dt_f41_date"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
            clearable
          ></el-date-picker>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Realease Time:" field="dt_release_date" :item-render="{}" folding span="6">
        <template #default="{data}">
          <el-date-picker
            v-model="data.dt_release_date"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
            clearable
          ></el-date-picker>
        </template>
      </vxe-form-item>
      <vxe-form-item title="B1/B2/B3" field="id_b123" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.id_b123" clearable placeholder="请选择B1/B2/B3">
            <vxe-option label="B1" value="1"></vxe-option>
            <vxe-option label="B2" value="2"></vxe-option>
            <vxe-option label="B3" value="3"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Main SM:" field="str_main_sm" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.str_main_sm" clearable placeholder="请选择Main SM">
            <vxe-option label="CORE" value="CORE"></vxe-option>
            <vxe-option label="FAN" value="FAN"></vxe-option>
            <vxe-option label="LPT" value="LPT"></vxe-option>
            <vxe-option label="B1" value="B1"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Site:" field="int_site" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.int_site" clearable placeholder="请选择site">
            <vxe-option
              v-for="item in siteOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Dept:" field="id_dept" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.id_dept" clearable placeholder="请选择Dept">
            <vxe-option
              v-for="item in deptOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Maintenace Type:" field="int_maintenance_type" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.int_maintenance_type" clearable placeholder="请选择Maintenace Type">
            <vxe-option
              v-for="item in maintenaceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Flow:" field="str_flow" :item-render="{}" folding span="6">
        <template #default="{data}">
          <vxe-select v-model="data.str_flow" clearable placeholder="请选择Flow">
            <vxe-option
              v-for="item in flowOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="F2/3 Closed Time:" field="dt_f23_date" :item-render="{}" folding span="6">
        <template #default="{data}">
          <el-date-picker
            v-model="data.dt_f23_date"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
            clearable
            style="width: 100%"
          ></el-date-picker>
       </template>
      </vxe-form-item>
      <vxe-form-item align="center" collapse-node>
        <vxe-button status="primary" content="查询" @click="handleSearch"></vxe-button>
        <vxe-button status="primary" content="重置" @click="handleReset"></vxe-button>
      </vxe-form-item>
    </vxe-form>
  `,
}
export default SearchForm
