// import { useSelection } from '../../hooks/useSelection.js';
import { useTable } from './useTable.js'
import { queryworkclothesById } from '../../api/workcloth.js'

const { toRefs, defineAsyncComponent, onMounted, ref, reactive } = Vue
const WorkClothes = {
  components: {
    HtVxeTable: defineAsyncComponent(() => import('../../../components/VxeTable/HtVxeTable.js')),
    PagePager: defineAsyncComponent(() => import('../../components/PagePager.js')),
    HtDialog: defineAsyncComponent(() => import('../../../components/ht.dialog.js')),
    HtDrawer: defineAsyncComponent(() => import('../../../components/ht.drawer.js')),
  },
  setup() {
    const { state, handleFilterChange, getTableDataByFrontPage, pagePagerState, handlePageChange } = useTable()
    // const { hasSelectedData, hasSelectedOneData } = useSelection();
    const addProjectRef = ref(null)
    const tableRef = Vue.ref(null)
    const dialogRef = reactive({
      title: '占用详情',
      visible: false,
    })

    const currentState = Vue.reactive({
      id: '',
    })
    const tableDataDetails = ref([])
    const cellClick = async (row) => {
      // if (column.field == "int_num") {
      //   dialogRef.title = column.title;
      //   dialogRef.visible = true;
      //   getTypeTableData(row.id,row.int_id);
      // } else if (column.field == "int_residue") {
      //   dialogRef.title = column.title;
      //   dialogRef.visible = true;

      // } else {
      // }
      tableDataDetails.value = await queryworkclothesById(row.row.id)

      dialogRef.visible = true
    }
    onMounted(() => {
      getTableDataByFrontPage()
    })
    return {
      ...toRefs(state),
      ...toRefs(pagePagerState),
      tableRef,
      currentState,
      handleFilterChange,
      handlePageChange,
      cellClick,
      dialogRef,
      tableDataDetails,
    }
  },
  template: /*html*/ `
      <!--<div class="flex items-center gap-2 m-2">
      
       <el-button type="info" @click="cellClick">查看</el-button>
      </div>-->
      <div class="border-b-2 mb-2"></div>
      <div class="mx-2" style="height: calc(100vh - 140px);">
        <HtVxeTable
            ref="tableRef"
            :tableData
            :tableColumns
            :remote="true"
            @filter-change="handleFilterChange"
            @cell-click="cellClick"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="80" fixed="left"></vxe-column>
          </template>
          <template #operation>
          <vxe-column title="操作" width="100">
            <template #default="{row}">
            <vxe-button mode="text" status="primary" content="Details"  @click="cellClick(row)"></vxe-button>
            
            </template>
          </vxe-column>
        </template>
        </HtVxeTable>
        <div class="border-b-2 my-2"></div>
        <PagePager
            :currentPage
            :pageSize
            :total
            @pageChange="handlePageChange"
        ></PagePager>
      </div>
      <HtDrawer v-model:visible="dialogRef.visible" title="工装占用详情" :is-show-save="false">
      <el-table :data="tableDataDetails" style="width: 100%">
      <el-table-column prop="str_wo" label="WO" width="500" />
      <el-table-column prop="int_num" label="QTY" width="180" />
      <el-table-column prop="dt_start" label="占用日期" />
      <el-table-column prop="dt_end" label="释放日期" />
      
    </el-table-column>
    </el-table>
      </HtDrawer>
    `,
}

export default WorkClothes
