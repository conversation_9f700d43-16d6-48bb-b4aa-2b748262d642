const { h, defineComponent } = Vue
const { ElIcon } = ElementPlus
const { InfoFilled } = ElementPlusIconsVue

const HeaderTips = defineComponent({
  name: 'HeaderTips',
  props: {
    tips: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    // 渲染图标
    const renderIcon = () => {
      return h(
        ElIcon,
        {
          class: 'mr-2 flex-shrink-0',
          size: 20,
        },
        () => [h(InfoFilled)],
      )
    }

    // 渲染标题
    const renderTitle = () => {
      return h(
        'div',
        {
          class: 'mb-1 text-lg font-semibold text-red-700',
        },
        '交接提示',
      )
    }

    // 渲染提示内容
    const renderContent = () => {
      return h(
        'div',
        {
          class: 'whitespace-pre-line text-red-600',
        },
        props.tips,
      )
    }

    // 渲染文本区域（标题 + 内容）
    const renderTextArea = () => {
      return h(
        'div',
        {
          class: 'min-w-0 flex-1',
        },
        [renderTitle(), renderContent()],
      )
    }

    // 渲染内部容器（图标 + 文本区域）
    const renderInnerContainer = () => {
      return h(
        'div',
        {
          class: 'flex w-full cursor-help items-center',
        },
        [renderIcon(), renderTextArea()],
      )
    }

    // 渲染中间容器
    const renderMiddleContainer = () => {
      return h(
        'div',
        {
          class: 'flex items-center',
        },
        [renderInnerContainer()],
      )
    }

    // 渲染最外层容器
    const renderOuterContainer = () => {
      return h(
        'div',
        {
          class: 'mb-4 rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4 shadow-sm',
        },
        [renderMiddleContainer()],
      )
    }

    return renderOuterContainer
  },
})

export default HeaderTips
