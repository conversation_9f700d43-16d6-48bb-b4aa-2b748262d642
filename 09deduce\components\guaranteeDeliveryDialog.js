const { useVModel } = VueUse

const GuaranteeDeliveryDialog = {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
      default: () => ({ int_type: 1, dt_delivery: null }), // Initialize form structure
    },
    disabledDate: {
      type: String,
      default: null,
    },
  },
  emits: ['update:visible', 'update:form', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const dialogVisible = useVModel(props, 'visible', emit)
    const currentForm = useVModel(props, 'form', emit)

    const handleConfirm = () => {
      emit('confirm', props.form) // Emit the form data on confirm
    }

    const handleCancel = () => {
      emit('cancel')
      closeDialog()
    }

    const closeDialog = () => {
      dialogVisible.value = false
    }

    const isDisabledDate = (date) => {
      return moment(date).isAfter(props.disabledDate)
    }

    return {
      dialogVisible,
      handleConfirm,
      handleCancel,
      closeDialog,
      // Expose props.form directly to the template for v-model binding
      currentForm,
      isDisabledDate,
    }
  },
  template: /*html*/ `
    <el-dialog
      class="my-dialog"
      v-model="dialogVisible"
      title="保证交付"
      width="400px" // Adjusted width for better form display
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
    >
      <el-form :model="currentForm" label-width="100px">
        <el-form-item label="保交付" prop="int_type">
          <el-radio-group v-model="currentForm.int_type">
            <el-radio :label="1">保交付</el-radio>
            <el-radio :label="0">取消保障</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="保交付时间" prop="dt_delivery">
          <el-date-picker
            v-model="currentForm.dt_delivery"
            type="date"
            placeholder="请选择交付时间"
            value-format="YYYY-MM-DD"
            class="w-full"
            :disabled-date="isDisabledDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="loading">
            保存
          </el-button>
      </template>
    </el-dialog>
  `,
}

export default GuaranteeDeliveryDialog
