// 团队看板主要JavaScript代码
// 团队看板主要逻辑

// API URL和基础参数
const API_URL = 'http://**************/api/Do/DoAPI';
const API_BASE_PARAMS = {
    "au": "ssamc",
    "ap": "api2018",
    "ak": ""
};

// 存储所有团队信息
let allTeams = [];
let currentTeamIndex = 0;
let teamRotationTimer = null;
let teamRotationTimeLeft = 30 * 60; // 30分钟，以秒为单位

// 团队看板初始化
function initDashboard(siteCode) {
    // 储存站点代码
    window.DASHBOARD_CONFIG = {
        siteCode: siteCode || 'CC(CombustorChamber)' // 默认使用CC站点
    };
    
    // 初始化页面
    updateDate();
    fetchTeamInfo();
    fetchChartData();
    fetchTasksData();

    // 设置定时切换团队（每30分钟）
    setInterval(rotateTeam, 30 * 60 * 1000);
    
    // 启动团队切换计时器
    startTeamRotationTimer();
}

// 更新日期显示
function updateDate() {
    const refreshTime = formatDateTime();
    document.getElementById('currentDate').textContent = refreshTime.split(' ')[0];
}

// 格式化日期时间
function formatDateTime() {
    var now = new Date();
    var year = now.getFullYear();
    var month = String(now.getMonth() + 1).padStart(2, '0');
    var day = String(now.getDate()).padStart(2, '0');
    var hours = String(now.getHours()).padStart(2, '0');
    var minutes = String(now.getMinutes()).padStart(2, '0');
    
    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
}

// 启动团队切换计时器
function startTeamRotationTimer() {
    // 清除之前的计时器
    if (teamRotationTimer) {
        clearInterval(teamRotationTimer);
    }
    
    // 重置计时器
    teamRotationTimeLeft = 30 * 60;
    updateTeamRotationDisplay();
    
    // 设置新的计时器，每秒更新一次
    teamRotationTimer = setInterval(function() {
        teamRotationTimeLeft--;
        updateTeamRotationDisplay();
        
        // 计时器归零时，不需要做任何事，因为rotateTeam函数已经通过setInterval单独设置
        if (teamRotationTimeLeft <= 0) {
            teamRotationTimeLeft = 30 * 60;
        }
    }, 1000);
}

// 更新团队切换计时器显示
function updateTeamRotationDisplay() {
    // 计算分钟和秒
    var minutes = Math.floor(teamRotationTimeLeft / 60);
    var seconds = teamRotationTimeLeft % 60;
    
    // 格式化显示
    var displayText = (minutes < 10 ? '0' : '') + minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
    
    // 更新显示
    var rotationElement = document.getElementById('teamRotation');
    if (rotationElement) {
        if (allTeams.length > 1) {
            rotationElement.textContent = '(自动切换: ' + displayText + ')';
            rotationElement.style.display = 'inline-block';
        } else {
            rotationElement.style.display = 'none';
        }
    }
}

// 获取团队信息
function fetchTeamInfo() {
    var xhr = new XMLHttpRequest();
    xhr.open('POST', API_URL, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    const params = JSON.stringify(Object.assign({}, API_BASE_PARAMS, {
        "ac": "pt_query_team_by_site",
        "siteCode": window.DASHBOARD_CONFIG.siteCode
    }));
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    
                    if (response.code === "success" && response.data && response.data.length > 0) {
                        // 保存所有团队信息
                        allTeams = response.data;
                        
                        // 显示第一个团队信息
                        currentTeamIndex = 0;
                        updateTeamInfo(allTeams[currentTeamIndex]);
                    } else {
                        console.error('获取团队信息失败: 服务器返回异常', response);
                    }
                } catch (e) {
                    console.error('解析团队信息失败:', e);
                }
            } else {
                console.error('获取团队信息失败: 网络错误', xhr.status);
            }
        }
    };
    
    xhr.send(params);
}

// 切换到下一个团队
function rotateTeam() {
    if (allTeams.length <= 1) return; // 只有一个团队，不需要切换
    
    // 切换到下一个团队
    currentTeamIndex = (currentTeamIndex + 1) % allTeams.length;
    updateTeamInfo(allTeams[currentTeamIndex]);
    
    // 刷新图表和任务数据
    fetchChartData();
    fetchTasksData();
    
    // 重置团队切换计时器
    startTeamRotationTimer();
}

// 更新团队信息显示
function updateTeamInfo(teamInfo) {
    if (teamInfo.str_site_code) {
        document.getElementById('stationInfo').textContent = 'Work Station: ' + teamInfo.str_site_code;
    }
    
    if (teamInfo.str_team) {
        document.getElementById('teamNumber').textContent = 'Team: ' + teamInfo.str_team;
    }
    
    if (teamInfo.str_team_leader) {
        document.getElementById('teamLeader').textContent = 'Team Leader: ' + teamInfo.str_team_leader;
    }
    
    // 更新刷新时间
    var refreshTime = formatDateTime();
    document.querySelectorAll('.refresh-time').forEach(function(el) {
        el.textContent = '更新: ' + refreshTime;
    });
}

// 从API获取图表数据
function fetchChartData() {
    // 显示加载状态
    document.querySelectorAll('.chart-container').forEach(function(container) {
        container.innerHTML = '<div class="loading">加载数据中...</div>';
    });
    
    var xhr = new XMLHttpRequest();
    var url = API_URL;
    var params = JSON.stringify(Object.assign({}, API_BASE_PARAMS, {
        "ac": "pt_f2_bar_chart",
        "siteCode": window.DASHBOARD_CONFIG.siteCode,
        "teamId": allTeams.length > 0 ? allTeams[currentTeamIndex].str_team_id : undefined
    }));

    xhr.open('POST', url, true);
    xhr.setRequestHeader('Content-Type', 'application/json');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.code === "success" && response.data) {
                        // 处理月度图表数据
                        if (response.data.monthChart) {
                            // 预处理数据，按照str_wo分组
                            var monthChartData = groupChartData(response.data.monthChart);
                            renderChart('monthChart', monthChartData);
                        } else {
                            handleApiError('monthChart', new Error('月度数据不存在'));
                        }

                        // 处理周度图表数据
                        if (response.data.weekChart) {
                            // 预处理数据，按照str_wo分组
                            var weekChartData = groupChartData(response.data.weekChart);
                            renderChart('weekChart', weekChartData);
                        } else {
                            handleApiError('weekChart', new Error('周度数据不存在'));
                        }
                    } else {
                        var err = new Error(response.text || '服务器返回错误');
                        handleApiError('monthChart', err);
                        handleApiError('weekChart', err);
                    }
                } catch (e) {
                    console.error('解析图表数据失败:', e);
                    handleApiError('monthChart', e);
                    handleApiError('weekChart', e);
                }
            } else {
                var err = new Error('网络响应异常: ' + xhr.status);
                console.error('获取图表数据失败:', err);
                handleApiError('monthChart', err);
                handleApiError('weekChart', err);
            }
        }
    };

    xhr.send(params);
}

// 将API返回的图表数据按工单号(str_wo)分组
function groupChartData(apiData) {
    if (!apiData || !apiData.length) return [];
    
    // 按工单号分组
    var groupedByWo = {};
    
    apiData.forEach(function(item) {
        // 安全地访问属性，确保不会因为缺少属性而报错
        if (!item) return; // 跳过无效数据项
        
        // 确保工单号(str_wo)存在
        var woKey = item.str_wo || '未知工单';
        var esnId = item.str_esn || '未知ESN';
        
        // 初始化工单组
        if (!groupedByWo[woKey]) {
            groupedByWo[woKey] = {
                id: esnId + '(' + woKey + ')',
                data: {
                    percent: item.is_finish === 1 ? "100%" : "进行中"
                }
            };
        }
        
        try {
            // 确保str_type存在且为字符串
            var typeStr = (item.str_type || 'unknown').toString();
            // 获取类型小写形式（如 b1, b2, b3, b4 等）
            var typeKey = typeStr.toLowerCase();
            
            // 确保TAT值存在且为数字
            var tatValue = parseFloat(item.dec_tat || 0);
            var overdueTatValue = parseFloat(item.dec_overdue_tat || 0);
            
            // 处理所有类型
            if (typeKey && typeKey !== 'unknown') {
                // 初始化类型数据
                if (!groupedByWo[woKey].data[typeKey]) {
                    groupedByWo[woKey].data[typeKey] = {
                        segments: []
                    };
                }
                
                // 如果有TAT值，添加为常规TAT段
                if (tatValue > 0) {
                    groupedByWo[woKey].data[typeKey].segments.push({
                        value: Math.round(tatValue),
                        isOverdue: false // 标记为非超期
                    });
                }
                
                // 如果有超期时间，添加为超期段
                if (overdueTatValue > 0) {
                    groupedByWo[woKey].data[typeKey].segments.push({
                        value: Math.round(overdueTatValue),
                        isOverdue: true // 标记为超期
                    });
                }
            }
            
            // 更新完成状态（只要有一个完成，整个工单就显示完成）
            if (item.is_finish === 1) {
                groupedByWo[woKey].data.percent = "100%";
            }
        } catch (err) {
            console.error('处理图表数据项时出错:', err, item);
            // 错误处理继续下一项，不中断整个循环
        }
    });
    
    // 将对象转换回数组
    return Object.values(groupedByWo);
}

// 从API获取任务数据（今日和昨日）
function fetchTasksData() {
    // 显示加载状态
    document.getElementById('todayTasks').innerHTML = '<tr><td colspan="8" class="loading">加载数据中...</td></tr>';
    document.getElementById('yesterdayTasks').innerHTML = '<tr><td colspan="8" class="loading">加载数据中...</td></tr>';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', API_URL, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    const params = JSON.stringify(Object.assign({}, API_BASE_PARAMS, {
        "ac": "pt_f2_task_list",
        "siteCode": window.DASHBOARD_CONFIG.siteCode,
        "teamId": allTeams.length > 0 ? allTeams[currentTeamIndex].str_team_id : undefined
    }));
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    
                    if (response.code === "success" && response.data) {
                        // 处理今日任务数据
                        if (response.data.nowDatas) {
                            var todayTasks = formatTasks(response.data.nowDatas);
                            renderTasks('todayTasks', todayTasks);
                        } else {
                            handleApiError('todayTasks', new Error('今日任务数据不存在'));
                        }
                        
                        // 处理昨日任务数据
                        if (response.data.yesterdayDatas) {
                            var yesterdayTasks = formatTasks(response.data.yesterdayDatas);
                            renderTasks('yesterdayTasks', yesterdayTasks);
                        } else {
                            handleApiError('yesterdayTasks', new Error('昨日任务数据不存在'));
                        }
                    } else {
                        var err = new Error(response.text || '服务器返回错误');
                        handleApiError('todayTasks', err);
                        handleApiError('yesterdayTasks', err);
                    }
                } catch (e) {
                    console.error('解析任务数据失败:', e);
                    handleApiError('todayTasks', e);
                    handleApiError('yesterdayTasks', e);
                }
            } else {
                var err = new Error('网络响应异常: ' + xhr.status);
                console.error('获取任务数据失败:', err);
                handleApiError('todayTasks', err);
                handleApiError('yesterdayTasks', err);
            }
        }
    };
    
    xhr.send(params);
}

// 格式化任务数据
function formatTasks(apiData) {
    return apiData.map(function(item, index) {
        return {
            id: index + 1,
            esn: item.str_esn || '',
            type: item.str_type || '',
            taskType: item.str_task_type || '',
            task: item.str_task || '',
            date: item.dt_range || '',
            people: item.str_staff || '',
            complete: item.str_complete || ''
        };
    });
}

// 处理API错误并显示测试数据
function handleApiError(containerId, error) {
    var container = document.getElementById(containerId);
    container.innerHTML = '<tr><td colspan="8" class="error-message">加载数据失败: ' + (error.message || '未知错误') + '</td></tr>';
    
    // 根据容器ID加载相应的演示数据
    if (containerId === 'todayTasks') {
        renderTasks('todayTasks', demoData.tasks.today);
    } else if (containerId === 'yesterdayTasks') {
        renderTasks('yesterdayTasks', demoData.tasks.yesterday);
    }
}

// 渲染图表
function renderChart(containerId, dataArray) {
    try {
        var container = document.getElementById(containerId);
        container.innerHTML = '';

        // 如果数据为空，显示无数据
        if (!dataArray || dataArray.length === 0) {
            container.innerHTML = '<div style="text-align:center;">无数据</div>';
            return;
        }

        // 找出所有数据中的最大值
        var maxValue = 0;
        dataArray.forEach(function(group) {
            if (!group || !group.data) return;
            
            // 遍历所有类型数据（b1, b2, b3, b4等）
            Object.keys(group.data).forEach(function(key) {
                if (key === 'percent') return; // 跳过百分比字段
                
                var data = group.data[key];
                if (data && data.segments && Array.isArray(data.segments)) {
                    // 遍历所有数据段
                    data.segments.forEach(function(segment) {
                        if (!isNaN(segment.value) && segment.value > maxValue) {
                            maxValue = segment.value;
                        }
                    });
                }
            });
        });

        // 为了美观，将最大值向上取整到100的倍数
        maxValue = Math.ceil(maxValue / 100) * 100;
        if (maxValue < 100) maxValue = 100;

        // 创建X轴刻度
        createXAxis(container, maxValue);

        // 直接使用传入的dataArray（已经是分组好的数据）
        const chartData = dataArray;
    
        // 定义颜色
        const TAT_COLOR = '#66BB6A';    // 绿色：常规TAT
        const OVERDUE_COLOR = '#EF5350';  // 红色：超期TAT

        chartData.forEach(function(group) {
            if (!group || !group.data) return; // 跳过无效数据
            
            var chartGroup = document.createElement('div');
            chartGroup.className = 'chart-group';

            // 创建组标题（ESN和百分比）
            var groupHeader = document.createElement('div');
            groupHeader.className = 'group-header';

            var esnLabel = document.createElement('div');
            esnLabel.className = 'esn-code';
            esnLabel.textContent = group.id || '未知ESN';
            groupHeader.appendChild(esnLabel);

            if (group.data.percent) {
                var percentLabel = document.createElement('div');
                percentLabel.className = 'percentage';
                percentLabel.textContent = group.data.percent;
                groupHeader.appendChild(percentLabel);
            }

            chartGroup.appendChild(groupHeader);

            // 遍历组内所有数据类型（排除percent属性）
            Object.keys(group.data).forEach(function(typeKey) {
                if (typeKey === 'percent') return; // 跳过percent属性
                
                const typeData = group.data[typeKey];
                const hasData = typeData !== undefined && 
                    typeData.segments && 
                    typeData.segments.length > 0 && 
                    typeData.segments.some(segment => !isNaN(segment.value) && segment.value > 0);

                if (hasData) {
                    var chartItem = document.createElement('div');
                    chartItem.className = 'chart-item';

                    var barLabel = document.createElement('div');
                    barLabel.className = 'bar-label';
                    // 将typeKey转换为大写显示（如 'b1' -> 'B1'）
                    barLabel.textContent = typeKey.toUpperCase();
                    chartItem.appendChild(barLabel);

                    var barWrapper = document.createElement('div');
                    barWrapper.className = 'bar-wrapper';

                    if (typeData.segments && typeData.segments.length > 0) {
                        // 遍历所有数据段
                        typeData.segments.forEach(segment => {
                        if (!isNaN(segment.value) && segment.value > 0) {
                            var segmentWidth = (segment.value / maxValue * 100) + '%';

                            var barSegment = document.createElement('div');
                            barSegment.className = 'bar-segment';
                            barSegment.style.width = segmentWidth;
                            
                            // 计算实际百分比
                            var percentValue = (segment.value / maxValue) * 100;
                            
                            // 根据是否为超期设置颜色，短段使用更深的颜色
                            if (segment.isOverdue) {
                                // 超期时间段 - 红色系
                                barSegment.style.backgroundColor = percentValue < 5 ? '#d32f2f' : OVERDUE_COLOR;
                            } else {
                                // 标准时间段 - 绿色系
                                barSegment.style.backgroundColor = percentValue < 5 ? '#2e7d32' : TAT_COLOR;
                            }

                            // 所有段都在内部显示数值
                            var barValue = document.createElement('div');
                            barValue.className = 'bar-value';
                            barValue.textContent = segment.value;
                            
                            // 对于非常短的段，调整字体大小以确保数值可见
                            if (percentValue < 8) {
                                barValue.style.fontSize = '10px';
                                // 为超短段添加最小宽度，确保数字至少部分可见
                                if (percentValue < 4) {
                                    barSegment.style.minWidth = '30px';
                                }
                            }
                            
                            barSegment.appendChild(barValue);

                            barWrapper.appendChild(barSegment);
                        }
                    });
                }

                    chartItem.appendChild(barWrapper);
                    chartGroup.appendChild(chartItem);
                }
            });

            container.appendChild(chartGroup);
        });
    } catch (error) {
        console.error('渲染图表时出错:', error);
        var container = document.getElementById(containerId);
        container.innerHTML = '<div class="error-message">图表渲染失败: ' + error.message + '</div>';
        return;
    }
}

// 渲染任务表格
function renderTasks(containerId, tasks) {
    var container = document.getElementById(containerId);
    var html = '';

    if (tasks.length === 0) {
        html = '<tr><td colspan="8" style="text-align:center;">无数据</td></tr>';
    } else {
        tasks.forEach(function(task) {
            // 根据完成状态设置样式类名
            var statusClass = task.complete === '完成' ? 'status-complete' :
                            task.complete === '进行中' ? 'status-pending' : '';

            html += '<tr class="task-row">' +
                '<td>' + (task.id || '') + '</td>' +
                '<td class="esn-code-small">' + (task.esn || '') + '</td>' +
                '<td>' + (task.type || '') + '</td>' +
                '<td>' + (task.taskType || '') + '</td>' +
                '<td>' + (task.task || '') + '</td>' +
                '<td>' + (task.date || '') + '</td>' +
                '<td>' + (task.people || '') + '</td>' +
                '<td class="' + statusClass + '">' + (task.complete || '') + '</td>' +
            '</tr>';
        });
    }

    container.innerHTML = html;
}

// 创建X轴刻度和网格线
function createXAxis(container, maxValue) {
    // 清除现有的X轴和网格线
    const existingAxis = container.querySelector('.x-axis');
    if (existingAxis) {
        existingAxis.remove();
    }
    
    const existingGridLines = container.querySelector('.x-grid-lines');
    if (existingGridLines) {
        existingGridLines.remove();
    }

    // 创建X轴容器
    const xAxis = document.createElement('div');
    xAxis.className = 'x-axis';
    
    // 创建网格线容器
    const gridLines = document.createElement('div');
    gridLines.className = 'x-grid-lines';

    // 刻度数量
    const tickCount = 6;

    // 创建刻度、标签和网格线
    for (let i = 0; i <= tickCount; i++) {
        // 计算刻度值，向上取整到10的倍数，使数值更整齐
        const value = Math.ceil(maxValue * i / tickCount / 10) * 10;
        
        // 计算位置百分比
        const position = (value / maxValue * 100) + '%';

        // 创建刻度标记
        const tick = document.createElement('div');
        tick.className = 'x-axis-mark';
        
        // 刻度位置根据值设置
        tick.style.position = 'absolute';
        tick.style.left = position;
        
        if (i > 0 && i < tickCount) {
            // 创建网格线
            const gridLine = document.createElement('div');
            gridLine.className = 'x-grid-line';
            gridLine.style.left = position;
            gridLines.appendChild(gridLine);
        }

        // 创建刻度标签
        const label = document.createElement('div');
        label.className = 'x-axis-label';
        label.textContent = value;

        tick.appendChild(label);
        xAxis.appendChild(tick);
    }

    container.appendChild(gridLines);
    container.appendChild(xAxis);
}

// 示例数据
var demoData = {
    tasks: {
        yesterday: [
            {
                id: 1,
                esn: "654321(*********)",
                type: "B1",
                taskType: "repair",
                task: "任务描述",
                date: "2025-04-08~2025-04-09",
                people: "张三",
                complete: "完成"
            },
            {
                id: 2,
                esn: "654321(*********)",
                type: "B1",
                taskType: "inspection",
                task: "任务描述",
                date: "2025-04-08~2025-04-09",
                people: "张三",
                complete: "完成"
            }
        ],
        today: [
            {
                id: 1,
                esn: "654321(*********)",
                type: "B1",
                taskType: "repair",
                task: "任务描述",
                date: "2025-04-08~2025-04-09",
                people: "张三",
                complete: "完成"
            },
            {
                id: 2,
                esn: "654321(*********)",
                type: "B1",
                taskType: "inspection",
                task: "任务描述",
                date: "2025-04-08~2025-04-09",
                people: "张三",
                complete: "完成"
            }
        ]
    }
}; 