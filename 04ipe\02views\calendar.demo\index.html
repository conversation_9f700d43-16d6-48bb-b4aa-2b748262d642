<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!--    全部样式-->
    <link href="../../../assets/css/index.css" rel="stylesheet" />
    <!--    引入element-plus的样式-->
    <link href="../../../assets/element-plus@2.5.5/index.css" rel="stylesheet" />
    <!--    引入VUE-->
    <script src="../../../assets/vue@3.4.15/vue.global.prod.js"></script>
    <!--    引入element-plus-->
    <script src="../../../assets/element-plus@2.5.5/index.js"></script>
    <script src="../../../assets/element-plus@2.5.5/lang/zh-cn.js"></script>
    <title>日历Demo</title>
  </head>
  <body>
    <div id="app">
      <calendar-demo></calendar-demo>
    </div>
  </body>
  <script type="module">
    import CalendarDemo from './index.js';
    const app = Vue.createApp({
      components: {
        CalendarDemo,
      },
      setup() {},
    });
    app.use(ElementPlus);
    app.mount('#app');
  </script>
</html>
