const { useVModel } = VueUse
const orderDialog = {
  props: {
    visible: Boolean,
    currentData: Object,
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)
    const list = [1,2,3,4,5]
    // 将数组中的3移动到第一个位置
    // 提交
    const orderDialogSumbit = () => {
      emit('submit', props.currentData)
    }
    return {
      visible,
      orderDialogSumbit,
    }
  },
  template: /*html*/ `
    <el-dialog
      class="my-dialog"
      title="顺序调整"
      v-model="visible"
      :show-close="false"
      width="30%"
    >
      <template #header>
        <div class="flex justify-between">
          <div class="text-sm text-white">顺序调整</div>
          <el-button size="small" type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <!-- 主体 -->
      <div class="mt-4">
        <el-form :model="currentData" label-width="100px">
          <el-form-item label="当前顺序">
            <el-input-number v-model="currentData.currentOrder" :min="1" disabled></el-input-number>
          </el-form-item>
          <el-form-item label="目标顺序">
            <el-input-number v-model="currentData.targetOrder" :min="1"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
      <!-- 底部按钮 -->
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="orderDialogSumbit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  `,
}

export default orderDialog
