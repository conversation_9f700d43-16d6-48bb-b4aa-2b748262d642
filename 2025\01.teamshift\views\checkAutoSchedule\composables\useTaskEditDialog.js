import { updateTask } from '../api/index.js'
const { ref } = Vue
const { ElMessage } = ElementPlus // Assuming ElMessage is globally available or passed as a prop

export function useTaskEditDialog(getTableData) {
  const editTaskDialogVisible = ref(false)
  const editTaskForm = ref({})

  const handleEditTask = (task, date, row) => {
    editTaskForm.value = {
      ...task,
    }
    editTaskDialogVisible.value = true
  }


  const handleEditTaskSave = async (formData) => {
    try {
      console.log(formData)
      await updateTask(formData) // 假设有一个更新任务的API
      ElMessage.success('任务更新成功')
      editTaskDialogVisible.value = false
      if (typeof getTableData === 'function') {
        await getTableData() // 刷新表格数据
      }
    } catch (error) {
      ElMessage.error('任务更新失败')
    }
    editTaskDialogVisible.value = false
  }

  const handleEditTaskCancel = () => {
    editTaskDialogVisible.value = false
  }

  return {
    editTaskDialogVisible,
    editTaskForm,
    handleEditTask,
    handleEditTaskSave,
    handleEditTaskCancel,
  }
}
