import { post } from '../../config/axios/httpReuest.js'

export function useDownloadAndUpload() {
  /**
   *
   * @param {Objectj} templateData 模板数据
   * @param {String} templateName 模板名称
   */
  const downloadTemplate = (templateData, templateName) => {
    const ws = XLSX.utils.aoa_to_sheet(templateData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
    XLSX.writeFile(wb, templateName)
  }

  /**
   *
   * @param {Object} upload  上传的ref
   * @param {Array} fileList  上传的文件列表
   * @param {Object} map 映射关系
   * @param {String} ac 接口名称
   */
  const uploadXlsx = (upload, fileList, map, ac) => {
    const file = fileList.value[0]
    const reader = new FileReader()
    // 映射关系中的key转大写
    const newMap = {}
    for (const key in map) {
      newMap[key.toLocaleUpperCase()] = map[key]
    }
    reader.onload = (e) => {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      const sheet = workbook.Sheets[workbook.SheetNames[0]]
      const json = XLSX.utils.sheet_to_json(sheet)
      const result = json.map((item) => {
        const obj = {}
        for (const key in item) {
          obj[newMap[key.toLocaleUpperCase()]] = item[key]
        }
        return obj
      })

      const params = {
        ac,
        importDatas: result,
      }
      post(params).then((res) => {
        if (res.data.code === 'success') {
          ElementPlus.ElMessage.success('导入成功')
        } else {
          ElementPlus.ElMessage.error(res.data.text)
        }
        // 清空文件
        // upload.value?.clearFiles()
        // fileList.value = []
      })
    }
    reader.readAsArrayBuffer(file.raw)
  }

  return {
    downloadTemplate,
    uploadXlsx,
  }
}
