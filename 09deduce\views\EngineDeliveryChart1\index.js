const { reactive, ref, shallowRef, onMounted, onUnmounted } = Vue
import { post } from '../../../config/axios/httpReuest.js'
import HeadForm from './components/HeadForm.js'
import { useButtons } from './composables/useButtons.js'
import { useForm } from './composables/useForm.js'
import { useLine } from './composables/useLine.js'
import { resolveSearchParam } from './utils/index.js'
/**
 * 发动机交付预测趋势图
 */
const EngineDeliveryChart = {
  components: {
    HeadForm,
  },
  setup() {
    const { formSearch } = useForm()
    const timeRangeType = ref('0')
    
    const { chartOption, initChart, myChart, chartRef } = useLine()

    // 获取x轴数据
    const getXAxisData = (data) => {
      chartOption.xAxis.data = data.map((item) => item.str_key)
    }
    // 获取series数据
    const getSeriesData = (data) => { 
      chartOption.series[0].data = data.map((item) => item.str_value)
    }
    // 获取数据
    const getChartData = async (search) => {
      const filter_fields = resolveSearchParam(search)
      const params = {
        ac: 'de_p80_view',
        filter_fields,
        int_particle: timeRangeType.value,
      }
      const { data } = await post(params)
      const { code, data: res, text } = data
      if(code ==='success') {
        getXAxisData(res)
        getSeriesData(res)
        myChart.value.setOption(chartOption)
      } else {
        ElementPlus.ElMessage.error(text)
      }
    }

    onMounted(() => {
      initChart()
      getChartData(formSearch)
      window.addEventListener('resize', handleResize)
    })
    const handleResize = () => {
      myChart.value.resize()
    }

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
    })

    const { handleSearch, handleChangeTimeRangeType } = useButtons(timeRangeType, formSearch, getChartData)

    return {
      chartRef,
      timeRangeType,
      handleSearch,
      handleChangeTimeRangeType
    }
  },
  template: /*html*/ `
    <div class="flex h-screen flex-col bg-slate-800/95 p-4">
      <div class="rounded-lg backdrop-blur-md bg-white/5 p-4 shadow-lg border border-white/10">
        <HeadForm @search="handleSearch"></HeadForm>
      </div>
      <div class="h-30 pt-4">
        <div class="flex justify-between items-center">
          <div class="bg-gradient-to-r from-white to-blue-500 bg-clip-text text-transparent text-xl m-4">
            发动机交付周期 P80趋势图
          </div>
          <div class="mr-4">
            <el-radio-group v-model="timeRangeType" @change="handleChangeTimeRangeType">
              <el-radio-button label="0">周</el-radio-button>
              <el-radio-button label="1">月</el-radio-button>
              <el-radio-button label="2">季度</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="corner-border m-4 flex-auto">
        <div ref="chartRef" class="h-full w-full"></div>
      </div>
    </div>
  `,
}

export default EngineDeliveryChart
