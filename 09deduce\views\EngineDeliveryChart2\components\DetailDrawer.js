import { post } from '../../../../config/axios/httpReuest.js'
import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'

const { ref, onMounted } = Vue
const DetailDrawer = {
  components: {
    HtVxeTable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    detailData: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const handleClose = () => {
      emit('update:visible', false)
    }

    const tableData = ref([])
    const columns = ref([
      { field: 'str_esn', title: 'ESN', minWidth: 120, filters: [{ data: '' }], filterRender: { name: 'FilterInput' } },
      { field: 'str_wo', title: 'WO', minWidth: 120, filters: [{ data: '' }], filterRender: { name: 'FilterInput' } },
      {
        field: 'str_flow',
        title: 'Flow',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'str_engine_type',
        title: 'Engine Type',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'str_maintenance_type',
        title: 'Maintenance Type',
        minWidth: 160,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'str_client',
        title: 'Customer',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'dt_f41_begin',
        title: 'F4-1 B2/B3 Start',
        minWidth: 140,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'dt_release_begin',
        title: 'Release Start',
        minWidth: 140,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'dt_f3_end',
        title: 'F2/3 End',
        minWidth: 140,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'dt_project_end',
        title: '主单元体计划结束时间',
        minWidth: 140,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
    ])

    /**
     * 获取表格数据
     */
    const getTableData = async () => {
      const params = {
        ac: 'de_sm_delivery_forecast_report_list',
        dt_date: props.detailData.date,
        str_group: props.detailData.seriesName,
      }
      const { data } = await post(params)
      tableData.value = data.data
    }

    onMounted(() => {
      getTableData()
    })

    return {
      handleClose,
      tableData,
      columns,
    }
  },
  template: /*html*/ `
    <el-drawer
      class="my_drawer"
      v-model="visible"
      size="80%"
      :destroy-on-close="true"
      :show-close="false"
    >
      <template #title>
        <div class="flex items-center justify-between">
          <span class="text-white">详细信息</span>
          <el-button type="danger" @click="handleClose">关闭</el-button>
        </div>
      </template>
      <HtVxeTable :tableData="tableData" :tableColumns="columns" :showOverflow="true" />
    </el-drawer>
  `,
}

export default DetailDrawer
