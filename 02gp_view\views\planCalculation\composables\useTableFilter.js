const { ref, reactive } = Vue

export function useTableFilter() {
  const parentFilterList = ref([])
  
  // 头部搜索条件的字段名列表
  const headerSearchFields = ['dt_release_start', 'dt_release_end']

  // 过滤器变更处理
  const handleFilterChange = (panel) => {
    // 保留头部搜索条件，只清除表格列头过滤器
    const headerFilters = parentFilterList.value.filter(item => 
      headerSearchFields.includes(item.str_key)
    )
    
    // 重置过滤器列表，保留头部搜索条件
    parentFilterList.value = [...headerFilters]
    
    const { filterList } = panel
    
    const queryLists = filterList.map((item) => {
      // 特殊处理 Flow 字段的多选过滤器
      if (item.field === 'str_flow') {
        return {
          str_key: item.field,
          str_value: item.values ?? item.datas,
        }
      }
      
      // 处理其他字段的过滤器
      return {
        str_key: item.field,
        str_value: item.values[0] ?? item.datas[0],
      }
    })
    
    parentFilterList.value.push(...queryLists)
    return queryLists
  }

  // 添加自定义过滤器
  const addCustomFilter = (key, value) => {
    // 移除已存在的同名过滤器
    removeFilter(key)
    
    if (value) {
      parentFilterList.value.push({
        str_key: key,
        str_value: value,
      })
    }
  }

  // 移除指定过滤器
  const removeFilter = (key) => {
    parentFilterList.value = parentFilterList.value.filter(
      (item) => item.str_key !== key
    )
  }

  // 清空所有过滤器
  const clearFilters = () => {
    parentFilterList.value = []
  }

  // 清空表格列头过滤器，保留头部搜索条件
  const clearTableFilters = () => {
    parentFilterList.value = parentFilterList.value.filter(item => 
      headerSearchFields.includes(item.str_key)
    )
  }

  // 清空头部搜索条件，保留表格列头过滤器
  const clearHeaderFilters = () => {
    parentFilterList.value = parentFilterList.value.filter(item => 
      !headerSearchFields.includes(item.str_key)
    )
  }

  // 获取当前过滤器列表
  const getFilterList = () => {
    return [...parentFilterList.value]
  }

  // 获取头部搜索条件
  const getHeaderFilters = () => {
    return parentFilterList.value.filter(item => 
      headerSearchFields.includes(item.str_key)
    )
  }

  // 获取表格列头过滤器
  const getTableFilters = () => {
    return parentFilterList.value.filter(item => 
      !headerSearchFields.includes(item.str_key)
    )
  }

  // 合并过滤器列表
  const mergeFilters = (additionalFilters = []) => {
    return [...parentFilterList.value, ...additionalFilters]
  }

  return {
    parentFilterList,
    handleFilterChange,
    addCustomFilter,
    removeFilter,
    clearFilters,
    clearTableFilters,
    clearHeaderFilters,
    getFilterList,
    getHeaderFilters,
    getTableFilters,
    mergeFilters
  }
} 