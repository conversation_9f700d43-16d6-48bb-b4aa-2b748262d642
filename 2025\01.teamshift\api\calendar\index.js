import { post } from '../../utils/request.js'
/**
 * 获取表格数据
 */
export function queryTableData(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_task_view',
    Filter: params,
  })
}

// 调整任务
export function adjustTask(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_adjust_task',
    adjustData: params,
  })
}

// 获取team
export function getTeamByType(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_by_type',
    type: params.type,
    dt_pt: params.dt_pt,
    type: params.type,
  })
}
//获取班次
export function getShiftList(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_shift',
  })
}

/**
 * 获取SM
 * @param {string} id_wo
 * @returns
 */
export function getSmList(id_wo, str_group, str_flow) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_sm_task',
    id_wo: id_wo,
    str_group: str_group,
    str_flow: str_flow,
  })
}


// 任务取消
export function cancelTask(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_self_task_shift',
    postdata: params,
  })
}
// 整个单元体任务完成
export function updateEngineTaskStatus(id_wo, str_flow, str_sm_s, int_status, int_sort) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_update_engine_task_status',
    id_wo: id_wo,
    str_flow: str_flow,
    str_sm_s: str_sm_s,
    int_status: int_status,
    int_sort: int_sort,
  })
}

// 获取添加的SM列表
export function getAddSmList(id_wo, str_type, str_flow) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_engine_task_model',
    id_wo: id_wo,
    str_type: str_type,
    str_flow: str_flow,
  })
}

// 获取任务列表
export function getTaskListByTaskId(taskId) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_shift_task_by_engine_model',
    id_task: taskId,
  })
}

// 添加任务
export function addTask(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_task_view_result',
    postData: params,
  })
}
// 锁定
export function lockTaskView(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_lock_task_view',
    id_wo: params.id_wo,
    str_type: params.str_type,
    str_flow: params.str_flow,
    dt_pt: params.dt_pt,
    str_engine_type: params.str_engine_type,
    is_lock: params.is_lock,
    is_all: params.is_all,
    dt_start: params.dt_start,
    dt_end: params.dt_end,
    id_main_engine: params.id_main_engine,
  })
}

// 查看是否有警告
export function getWarning(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_sub_team_select',
    id_sub_team: params.id_sub_team,
    dt_pt: params.dt_pt,
    id_team: params.id_team,
  })
}

/*
 * 获取当前分部的人员
 * @param {} params 
 * @returns 
 */
export function getDeptStaff(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_current_dept_staff'
  })
}

/*
 * 获取当前team的人员
 * @param {} params 
 * @returns 
 */
export function getTeamStaff(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_staff_list',
    id_team: params.id_team,
    dt_date: params.dt_date,
    str_dept: params.str_dept,

  })
}

/*
 * 获取当前team的借调人员
 * @param {} params 
 * @returns 
 */
export function getTeamSecStaff(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_teamsection_staff_list',
    id_team: params.id_team,
    pt_dt: params.dt_date,
  })
}
// 人员重排
export function saveStaff(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_reassign_staff',
    data: params,
  })
}