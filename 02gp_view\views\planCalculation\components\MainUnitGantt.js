import { getMainUnitGantt, saveMainUnitGantt } from '../../../api/index.js'
import { activeProjectApi, freezeAndUnfreezeApi } from '../../../api/calculation.js'
import { useButtons } from '../composables/useButtons.js'
import UnitPlanDrawer from './unitPlanDrawer.js'
import { post } from '../../../../config/axios/httpReuest.js'
import DynamicColumnConfigurator from '../../../components/DynamicColumnConfigurator.js'
import { useDynamicColumn } from '../../../composables/useDynamicColumn.js'
const { ref, reactive, onMounted, nextTick } = Vue

const MainUnitGantt = {
  components: {
    UnitPlanDrawer,
    DynamicColumnConfigurator,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
    idWo: {
      type: String,
      required: true,
    },
  },
  emits: ['toSubUnitGantt'],
  setup(props, { emit }) {
    const formSearch = reactive({
      str_node: '',
    })

    const GANTT_COLUMNS = [
      { name: 'text', label: '任务名称', tree: true, width: '*', resize: true },
      { name: 'start_date', label: '开始时间', align: 'center', width: 100 },
      { name: 'dt_end', label: '结束时间', align: 'center', width: 100 },
      { name: 'duration', label: '持续时间', align: 'center', width: 100 },
      { name: 'dt_f2_3_end', label: 'F2/3结束时间', align: 'center', width: 100 },
    ]

    const search = () => {
      getGanttData()
      expandGantt()
    }

    const gantt = Gantt.getGanttInstance()

    const ganttParse = ref({
      data: [],
      links: [],
    })
    // gantt插件
    gantt.plugins({
      // 自动调整大小
      auto_scheduling: true,
      // 临界路径
      critical_path: true,
      // 撤销
      undo: true,
    })
    /* gantt 配置 */
    // 日期格式
    gantt.config.date_format = '%Y-%m-%d'
    // 表格日期格式
    gantt.date_grid = '%Y-%m-%d'
    // 禁止拖拽链接
    gantt.config.drag_links = false
    // grid的宽度
    gantt.config.grid_width = 600
    // 自动展开
    gantt.config.open_tree_initially = true
    // 禁止project的拖拽
    gantt.config.drag_project = false
    // 禁止progress的拖拽
    gantt.config.drag_progress = false
    gantt.config.auto_scheduling = true // 自动调度
    gantt.config.auto_scheduling_compatibility = true // 自动调度
    // 设置自动适应任务时间范围
    gantt.config.fit_tasks = true

    // 设置列
    gantt.config.columns = GANTT_COLUMNS
    // 不同层级的颜色
    gantt.templates.task_class = (start, end, task) => {
      return `main-level-${task.$level}`
    }

    gantt.config.lightbox_sections = [
      { name: 'description', height: 38, map_to: 'text', type: 'textarea', focus: true, readonly: true },
      { name: 'time', type: 'duration', map_to: 'auto', time_format: ['%d', '%m', '%Y'] },
    ]
    gantt.locale.labels.section_description = '名称'
    gantt.locale.labels.section_time = '时间'

    gantt.config.scale_height = 100
    // 头部增加周
    gantt.config.scales = [
      // 周
      { unit: 'week', step: 1, format: 'W%w' },
      // 星期
      { unit: 'day', step: 1, format: '%D' },
      // 天
      { unit: 'day', step: 1, format: '%m-%d' },
    ]

    const dynamicColumn = useDynamicColumn(gantt, GANTT_COLUMNS)

    // 获取甘特图数据中最小的开始时间和最大的结束时间
    const getGanttDataMinMaxTime = () => {
      const tasks = gantt.getTaskByTime()
      if (!tasks || tasks.length === 0) {
        return {
          preDayOfMonth: moment().subtract(7, 'days').format('YYYY-MM-DD'),
          lastDayOfYear: moment().add(30, 'days').format('YYYY-MM-DD'),
        }
      }

      const minTime = _.minBy(tasks, 'start_date')?.start_date
      // 获取当前月份的前一天
      const preDayOfMonth = moment(minTime).subtract(1, 'days').format('YYYY-MM-DD')
      const maxTime = _.maxBy(tasks, 'end_date')?.end_date
      // 获取最大日期的年
      const maxYear = moment(maxTime).year()
      // 获取当前年的下一年的第一天
      const lastDayOfYear = moment(`${maxYear + 1}-01-01`).format('YYYY-MM-DD')
      return { preDayOfMonth, lastDayOfYear }
    }

    // 更新甘特图的时间范围
    const updateGanttTimeRange = () => {
      const { preDayOfMonth, lastDayOfYear } = getGanttDataMinMaxTime()
      // 设置起始时间
      gantt.config.start_date = preDayOfMonth
      // 设置结束时间
      gantt.config.end_date = lastDayOfYear
      gantt.render()
    }

    gantt.attachEvent('onAfterTaskDrag', (id) => {
      // 获取拖动后的任务
      const task = gantt.getTask(id)
      task.dt_end = moment(task.start_date).add(task.duration, 'days').subtract(1, 'days').format('YYYY-MM-DD')
      gantt.updateTask(id, task)
      // 更新甘特图的时间范围
      updateGanttTimeRange()
    })

    // 禁用默认的双击事件，改为使用自定义的处理
    gantt.attachEvent('onTaskDblClick', (id, e) => {
      // 自定义lightbox行为，只显示时间部分
      gantt.resetLightbox()

      // 创建一个只有时间部分的lightbox
      gantt.config.lightbox.sections = [
        { name: 'time', height: 72, type: 'duration', map_to: 'auto', time_format: ['%d', '%m', '%Y'] },
      ]

      // 打开lightbox
      gantt.showLightbox(id)

      // 防止默认的双击事件
      return false
    })

    // 监听lightbox保存事件
    gantt.attachEvent('onLightboxSave', (id, task, isNew) => {
      // 更新结束日期
      task.dt_end = moment(task.start_date).add(task.duration, 'days').subtract(1, 'days').format('YYYY-MM-DD')
      // 更新甘特图的时间范围
      updateGanttTimeRange()
      return true // 返回true允许保存操作继续
    })

    const deleteTask = async (id) => {
      const params = {
        ac: 'gp_delete_sm_gantt',
        id,
      }
      await post(params)
      // 更新甘特图的时间范围
      updateGanttTimeRange()
    }

    // 监听lightbox删除事件
    gantt.attachEvent('onLightboxDelete', (id) => {
      // 删除任务
      deleteTask(id)
      return true
    })

    // 监听任务更新事件
    gantt.attachEvent('onAfterTaskUpdate', (id, task) => {
      // 更新甘特图的时间范围
      updateGanttTimeRange()
    })

    // 监听甘特图渲染事件，确保每次渲染时调整时间范围
    gantt.attachEvent('onGanttRender', () => {
      // 如果有任务数据，则更新时间范围
      if (gantt.getTaskByTime().length > 0) {
        // 只更新配置，不调用render()避免循环调用
        const { preDayOfMonth, lastDayOfYear } = getGanttDataMinMaxTime()
        gantt.config.start_date = preDayOfMonth
        gantt.config.end_date = lastDayOfYear
      }
    })

    // 转换数据
    const transformData = (data) => {
      return _.map(data, (item) => {
        // 查看当前id下是否有子节点
        const hasChild = data.some((it) => it.id_root === item.id)
        const unscheduled = !(item.dt_start && item.dt_end) && !hasChild
        return {
          id: item.id,
          text: item.str_node,
          start_date: item.dt_start,
          dt_end: item.dt_end,
          duration: item.duration,
          progress: item.dec_precent,
          parent: item.id_root,
          unscheduled,
          is_lock: item.is_lock,
          type: hasChild ? 'project' : 'task',
        }
      })
    }

    const getGanttData = async () => {
      const data = await getMainUnitGantt(props.id, formSearch.str_node)
      ganttParse.value.data = transformData(data.subPlans)
      ganttParse.value.links = data.links
      // 初始化gantt
      nextTick(() => {
        // 清除
        gantt.clearAll()
        gantt.init('mainUnitGantt')
        gantt.parse(ganttParse.value)
        // 加载完成后更新时间范围
        updateGanttTimeRange()
        gantt.render()
      })
    }

    const isCollapse = ref(false)
    // 折叠甘特图
    const collapseGantt = () => {
      isCollapse.value = true
      // 使用批量操作优化性能
      gantt.batchUpdate(() => {
        // 只折叠第一层，保持数据结构
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.close(taskId)
          }
        })
      })
    }
    // 展开甘特图
    const expandGantt = () => {
      isCollapse.value = false
      // 使用批量操作来优化性能
      gantt.batchUpdate(() => {
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.open(taskId)
            // 展开第二层
            const children = gantt.getChildren(taskId)
            children.forEach((childId) => gantt.open(childId))
          }
        })
      })
    }

    const isCollapseGrid = ref(false)
    // 收起grid
    const collapseGrid = () => {
      isCollapseGrid.value = !isCollapseGrid.value
      gantt.config.show_grid = false
      gantt.render()
    }
    // 展开grid
    const expandGrid = () => {
      isCollapseGrid.value = false
      gantt.config.show_grid = true
      gantt.render()
    }

    // 转到子单元体甘特图
    const toSubUnitGantt = () => {
      emit('toSubUnitGantt')
    }
    // 保存
    const saveGantt = async () => {
      const data = gantt.serialize()?.data
      return await saveMainUnitGantt(data)
    }
    /**正式应用 */
    const activeProject = async () => {
      const res = await ElementPlus.ElMessageBox.confirm('是否正式应用该计划', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      if (res === 'confirm') {
        await activeProjectApi([props.id])
      }
    }
    // 添加容器高度响应式变量
    const containerHeight = ref('100%')

    // 检查是否在 iframe 中
    const isInIframe = () => {
      try {
        return window !== window.top
      } catch (e) {
        return true
      }
    }
    // 计算并设置容器高度
    const updateContainerHeight = () => {
      try {
        if (isInIframe()) {
          // iframe 环境下的高度计算
          // 获取iframe的可视区域高度
          const iframeHeight = window.innerHeight || document.documentElement.clientHeight
          // 减去顶部搜索区域的高度(48px)和内边距(32px)
          containerHeight.value = `${iframeHeight - 220}px`
        } else {
          // 非 iframe 环境下的高度计算
          const viewportHeight = window.innerHeight
          containerHeight.value = `${viewportHeight - 220}px`
        }
      } catch (error) {
        console.error('更新容器高度失败:', error)
        containerHeight.value = '100%'
      }
    }
    // 添加防抖处理
    const debounce = (fn, delay) => {
      let timer = null
      return function (...args) {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          fn.apply(this, args)
        }, delay)
      }
    }
    // 使用防抖处理的updateContainerHeight
    const debouncedUpdateHeight = debounce(updateContainerHeight, 200)

    onMounted(() => {
      getGanttData()
      updateContainerHeight() // 初始化时执行一次
      window.addEventListener('resize', debouncedUpdateHeight) // 使用防抖处理的函数
    })

    const { handleToDeduction, smChartDrawerState } = useButtons(props.idWo)

    return {
      formSearch,
      search,
      isCollapse,
      collapseGantt,
      expandGantt,
      toSubUnitGantt,
      saveGantt,
      isCollapseGrid,
      collapseGrid,
      expandGrid,
      activeProject,
      containerHeight,
      handleToDeduction,
      smChartDrawerState,
      ...dynamicColumn,
    }
  },
  template: /*html*/ `
    <div>
      <el-form :model="formSearch" inline size="default">
        <el-form-item label="">
          <el-input style="width:220px" v-model="formSearch.str_node" placeholder="请输入任务名称" clearable></el-input>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="search">搜索</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button v-if="!isCollapse" type="primary" @click="collapseGantt">折叠甘特图</el-button>
          <el-button v-else type="primary" @click="expandGantt">展开甘特图</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button v-if="!isCollapseGrid" type="primary" @click="collapseGrid">收起grid</el-button>
          <el-button v-else type="primary" @click="expandGrid">展开grid</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="toSubUnitGantt">子单元体甘特图</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="handleToDeduction">跳转到推演</el-button>
          <!-- <el-button type="primary" @click="activeProject">正式应用</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <div class="flex justify-end gap-2 mb-2">
      <DynamicColumnConfigurator
        v-model="visibleColumns"
        :all-columns="columnConfig"
        button-text="列配置"
        button-icon="Grid"
      />
    </div>
    <div class="relative" :style="{ height: containerHeight }">
      <div id="mainUnitGantt" class="h-full w-full"></div>
    </div>
    <!-- 单元体抽屉 -->
    <UnitPlanDrawer
      v-if="smChartDrawerState.visible"
      v-model:visible="smChartDrawerState.visible"
      :id="smChartDrawerState.id"
    ></UnitPlanDrawer>
  `,
}

export default MainUnitGantt
