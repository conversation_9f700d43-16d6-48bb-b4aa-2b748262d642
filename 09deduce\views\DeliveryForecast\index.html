<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!--    全部样式-->
    <link href="../../../assets/css/index.css" rel="stylesheet" />
    <!--    引入element-plus的样式-->
    <link href="../../../assets/element-plus@2.5.5/index.css" rel="stylesheet" />
    <!--    引入VUE-->
    <script src="../../../assets/vue@3.4.15/vue.global.prod.js"></script>
    <!--    引入element-plus-->
    <script src="../../../assets/element-plus@2.5.5/index.js"></script>
    <!--    引入axios-->
    <script src="../../../assets/axios@1.6.7/axios.min.js"></script>
    <!--    引入XLSX-->
    <script src="../../../assets/xlsx@0.16.8/xlsx.full.min.js"></script>
    <title>交付预测模拟表导入</title>
  </head>
  <body>
    <div id="app">
      <performance-view></performance-view>
    </div>
  </body>
  <script type="module">
    import PerformanceView from './index.js'
    const { createApp } = Vue

    const app = createApp({
      components: {
        PerformanceView,
      },
      setup() {
        return {}
      },
    })
    app.use(ElementPlus)
    app.mount('#app')
  </script>
</html>
