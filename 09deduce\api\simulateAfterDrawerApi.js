import { post } from '../../config/axios/httpReuest.js'

/**
 * @description 重选资源方案接口
 * @param {String} id  发动机id
 * @param {Array} list  重选资源方案数据
 */
export async function reSelectResourcePlanApi(id, list) {
  const datas = list.map((item) => {
    return {
      id_source: item.id_source,
      int_source_type: item.int_source_type,
      int_num: item.int_num,
      dt_ekd: item.dt_ekd,
    }
  })
  const params = {
    ac: 'de_save_resource_plan',
    id,
    datas,
  }
  const { data } = await post(params)
  if (data.code === 'success') {
    return data ?? true
  } else {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
}
