export function useTableColumn() {

  // 获取模板管理表格列
  const getProjectTemplateColumns = () => {
    return [
      {
        field: 'str_engine_type',
        title: '机型',
        minWidth: 100,
        filters: [{ label: 'CFM56', value: 'CFM56' }, { label: 'LEAP', value: 'LEAP' }],
        filterMultiple: false,
        // filterRender: { name: 'FilterInput' },
        formatter: ({ cellValue }) => {
          return cellValue.toUpperCase();
        },
      },
      {
        title: '模板名称',
        field: 'str_template_name',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '操作时间',
        field: 'dt_up',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '操作人',
        field: 'str_by',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        type: 'html',
        title: '状态',
        field: 'is_state',
        minWidth: 100,
        filters: [{ label: '启用', value: 1 }, { label: '停用', value: 0 }],
        filterMultiple: false,
        // filterRender: { name: 'FilterInput' },
        formatter: ({ cellValue }) => {
          return `<span style="color: ${cellValue === 1 ? 'green' : 'red'}">${cellValue === 1 ? '启用' : '停用'}</span>`;
        },
      },
    ];
  };

  // 获取任务基础数据表格列
  const getTaskBaseDataColumns = () => {
    return [
      {
        title: '任务名称',
        field: 'taskName',
        minWidth: 200,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        field: 'model',
        title: '机型',
        minWidth: 60,
        filters: [{ label: 'CFM56', value: 'CFM56' }, { label: 'LEAP', value: 'LEAP' }],
        filterMultiple: false,
        // filterRender: { name: 'FilterInput' },
      },
      {
        title: 'Flow',
        field: 'flow',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'Type',
        field: 'type',
        minWidth: 60,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'SM',
        field: 'sm',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '站位',
        field: 'str_occupy_site',
        minWidth: 80,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' }
      },
      {
        title: '工期(天)',
        field: 'duration',
        minWidth: 60,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '操作时间',
        field: 'createTime',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '操作人',
        field: 'createBy',
        minWidth: 80,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },  {
        type: 'html',
        title: '状态',
        field: 'int_state',
        minWidth: 40,
        filters: [{ label: '启用', value: 1 }, { label: '停用', value: 0 }],
        filterMultiple: false,
        // filterRender: { name: 'FilterInput' },
        formatter: ({ cellValue }) => {
          return `<span style="color: ${cellValue === 1 ? 'green' : 'red'}">${cellValue === 1 ? '启用' : '停用'}</span>`;
        },
      },
    ];
  };

  // 获取每日产能配置表格列
  const getDailyCapacityColumns = () => {
    return [
      {
        title: '日期',
        field: 'date',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '日产能',
        field: 'dailyCapacity',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '操作时间',
        field: 'createTime',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '操作人',
        field: 'createBy',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
    ];
  };

  return {
    getProjectTemplateColumns,
    getTaskBaseDataColumns,
    getDailyCapacityColumns,
  };
}
