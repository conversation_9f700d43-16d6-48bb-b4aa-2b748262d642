<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- css部分 -->
    <link rel="stylesheet" href="../../styles/tailwind.css" />
    <link rel="stylesheet" href="../../styles/common.dialog.css" />
    <!-- CDN CSS -->
    <link rel="stylesheet" href="../../assets/element-plus@2.9.4/dist/index.css" />
    <!-- CDN js部分 -->
    <script src="../../assets/vue@3.5.13/vue.global.js"></script>
    <script src="../../assets/element-plus@2.9.4/dist/index.full.js"></script>
    <script src="../../assets/sortablejs@latest/Sortable.min.js"></script>
    <script src="../../assets/element-plus@2.9.4/icons-vue/index.full.js"></script>
    <script src="../../assets/moment/moment.min.js"></script>
    <script src="../../assets/lodash@4.17.21/lodash.min.js"></script>
    <!-- api部分 -->
    <script src="../../assets/axios@1.6.7/axios.min.js"></script>
    <title>提交交接文件</title>
  </head>
  <body>
    <div id="app">
      <commit-handover></commit-handover>
    </div>
  </body>
  <script type="module">
    import CommitHandover from './commit.handover.page.js'
    const { createApp } = Vue
    const app = createApp({
      components: {
        CommitHandover,
      },
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    app.use(ElementPlus)
    app.mount('#app')
  </script>
</html>