import { post } from '../../utils/request.js'

/**
 * 上传文件
 * @param {FormData} formData - 包含文件和其他信息的FormData对象
 * @returns {Promise} 上传结果
 */
/**
 * 延时申请列表 
 */
export function getBorrowApplyList(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_borrow_list',
    Filter: postData,
  })
}

/**
 * 保存延时申请
 * @param {*} params
 * @returns
 */
export function saveBorrowApply(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_borrow_apply',
    postdata: params,
  })
}

/**
 * 获取延时申请明细
 * @param {} params
 * @returns
 */
export function getBorrowApplyId(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_borrow_by_id',
    id: params,
  })
}

/**
 * 删除延时申请
 * @param {} params
 * @returns
 */
export function deleteBorrowApply(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_delete_borrow',
    ids: params,
  })
}

/**
 * 获取team
 * @param {} params
 * @returns
 */
export function getTeams() {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_teams',
  })
}

/**
 * 获取staff
 * @param {} params
 * @returns
 */
export function getCurrentDeptStaff() {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_current_dept_staff',
  })
}

/**
 * 提交审批
 * @param {} params
 * @returns
 */
export function submitApprove(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_autid_borrow_apply',
    id: params.id,
    status: params.status,
    str_content: params.str_content,
  })
}

/**
 * 获取班次
 * @param {} params
 * @returns
 */
export function getShiftList() {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_shift',
  })
}
/**
 * 撤回
 * @param {} params
 * @returns
 */
export function withdrawBorrowApply(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_autid_borrow_apply',
    id: params.id,
    status: params.status,
  })
}

/**
 * 根据人员查询能力
 * @param {} params
 * @returns
 */
export function getSKillbyStaff(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_skills_by_staff',
    id_staff: params,
  })
}