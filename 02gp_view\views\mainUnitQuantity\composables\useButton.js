const { computed, ref, nextTick } = Vue
export function useButton(GANTT_COLUMNS) {
  // 是否收起甘特图
  const isCollapseGrid = ref(false)

  // 计算属性优化按钮文本显示
  const gridButtonText = computed(() => (isCollapseGrid.value ? '展开左侧' : '收起左侧'))

  // 切换甘特图的列
  const toggleGrid = () => {
    isCollapseGrid.value = !isCollapseGrid.value
    // 重新配置列,并且改变列的宽度
    gantt.config.columns = isCollapseGrid.value
      ? [{ name: 'text', label: '任务名称', tree: true, width: '*', resize: true }]
      : GANTT_COLUMNS
    gantt.config.grid_width = isCollapseGrid.value ? 200 : 600
    // 重新渲染 使用nextTick 是因为gantt.render() 需要等待gantt.init() 完成 优化性能 不然会强制回流
    nextTick(() => {
      gantt.render()
    })
  }

  /**
   * 展开或者收起甘特图
   * @param {Boolean} expand 是否展开
   */
  const toggleGantt = (expand) => {
    gantt.batchUpdate(() => {
      gantt.eachTask((task) => {
        if (expand) {
          gantt.open(task.id)
        } else {
          gantt.close(task.id)
        }
      })
    })
  }

  return {
    isCollapseGrid,
    gridButtonText,
    toggleGrid,
    toggleGantt
  }
}
