import { post } from '../../../config/axios/httpReuest.js'
import OrderDialog from './components/orderDialog.js'
import ScatterChart from './components/scatterChart.js'
import UnitPlanDrawer from './components/unitPlanDrawer.js'
// 导入推演串件结果抽屉
import DeductionDrawer from './components/deductionDrawer.js'
// 导入颜色统计组件
import PointColorStatistics from './components/pointColorStatistics.js'
// 导入头部右边组件
import EngineTopRight from './components/engineTopRight.js'
// 导入头部中间组件
import EngineTopMiddle from './components/engineTopMiddle.js'
import { useFilter } from '../../hooks/useFilter.js'
import { useAdjustLine } from './hooks/useAdjustLine.js'
import LineDialog from './components/adjustLineDialog.js'
// 头部查询组件
import PartSearchForm from './components/searchForm.js'
// 模拟后清单
import SimulateAfterDrawer from './components/simulateAfterDrawer.js'
import ChangeListDrawer from './components/changeListDrawer.js' // 串件后清单
import { useOption } from '../../hooks/useOption.js'
import { EKD_TYPE_ENUM } from '../../enum/index.js'
import { useButton } from './composables/useButton.js'
import WhiteToYellowConverter from './components/WhiteToYellowConverter.js'
const { ref, reactive, onMounted, h, nextTick } = Vue
const { ElDatePicker, ElForm, ElFormItem, ElSelect, ElOption, ElCheckbox } = ElementPlus
const EngineSimulationPlan = {
  components: {
    OrderDialog,
    ScatterChart,
    UnitPlanDrawer,
    DeductionDrawer,
    PointColorStatistics,
    EngineTopRight,
    EngineTopMiddle,
    LineDialog,
    PartSearchForm,
    SimulateAfterDrawer,
    ChangeListDrawer,
    WhiteToYellowConverter,
  },
  props: ['input_str_esn'], // 目前给MP调整使用
  setup(props) {
    const isFirstEnter = ref(true)
    const { recombineFilter } = useFilter()
    const parentFilterFields = ref([])
    const parentIsBeforeSimulation = ref(true)
    const parentIsSimulation = ref(false)

    // 排序相关状态
    const sortState = reactive({
      releaseSort: 'asc', // Release排序 升序 asc 降序 desc
      f3closeSort: '', // F3Close排序 升序 asc 降序 desc
      gpSort: '', // GP排序 升序 asc 降序 desc
      str_order_feild: '',
      str_order_acs: '',
    })

    // 处理排序变化
    const handleSortChange = (type, value) => {
      // 清除其他排序选项
      if (type !== 'releaseSort') sortState.releaseSort = ''
      if (type !== 'f3closeSort') sortState.f3closeSort = ''
      if (type !== 'gpSort') sortState.gpSort = ''

      // 设置当前排序
      sortState[type] = value
      // 设置排序字段
      if (type === 'releaseSort') {
        sortState.str_order_feild = 'dt_release'
      } else if (type === 'f3closeSort') {
        sortState.str_order_feild = 'dt_f3_close'
      } else if (type === 'gpSort') {
        sortState.str_order_feild = 'dt_project_start'
      }
      // 设置排序方式
      sortState.str_order_acs = value === 'asc' ? 'asc' : 'desc'

      getBaseInfo()
    }

    // * 查询
    const handleSearch = async (formData) => {
      isFirstEnter.value = true
      const filterFields = recombineFilter(formData)
      parentFilterFields.value = filterFields
      // 使用requestAnimationFrame避免阻塞渲染
      requestAnimationFrame(async () => {
        // 重置所有组件的isLoaded标志
        Object.values(topMiddleRef.value).forEach((ref) => {
          if (ref) ref.isLoaded = false
        })
        Object.values(topRightRef.value).forEach((ref) => {
          if (ref) ref.isLoaded = false
        })
        Object.values(pointColorRef.value).forEach((ref) => {
          if (ref) ref.isLoaded = false
        })
        Object.values(scatterChartRef.value).forEach((ref) => {
          if (ref) ref.isLoaded = false
        })

        await getBaseInfo()
      })
    }
    // * 折叠
    const handleCollapse = () => {
      isFirstEnter.value = false
    }
    // 显示middle的页面内容
    const showMiddleContent = (splitArr, target) => {
      const id = splitArr[1]
      if (topMiddleRef.value[id] && !topMiddleRef.value[id].isLoaded) {
        topMiddleRef.value[id].getState()
        topMiddleRef.value[id].isLoaded = true
        // 停止观察当前元素，即只触发一次
        io.unobserve(target)
      }
    }
    // 显示right的页面内容
    const showRightContent = (splitArr, target) => {
      const id = splitArr[1]
      if (topRightRef.value[id] && !topRightRef.value[id].isLoaded) {
        topRightRef.value[id].getState()
        topRightRef.value[id].isLoaded = true
        // 停止观察当前元素，即只触发一次
        io.unobserve(target)
      }
    }
    // 显示点位颜色的页面内容
    const showPointContent = (splitArr, target) => {
      const id = splitArr[1]
      if (pointColorRef.value[id] && !pointColorRef.value[id].isLoaded) {
        pointColorRef.value[id].getPointColorStatistics(id, parentFilterFields.value)
        pointColorRef.value[id].isLoaded = true
        // 停止观察当前元素，即只触发一次
        io.unobserve(target)
      }
    }
    // 显示scatter的页面内容
    const showScatterContent = (splitArr, target) => {
      const id = splitArr[1]
      if (scatterChartRef.value[id] && !scatterChartRef.value[id].isLoaded) {
        scatterChartRef.value[id].getChartList(parentIsBeforeSimulation.value, parentFilterFields.value)
        scatterChartRef.value[id].isLoaded = true
        // 停止观察当前元素，即只触发一次
        io.unobserve(target)
      }
    }
    // 使用多态的思想，根据不同的情况来处理
    const handleIntersection = (target) => {
      // 根据-进行分割
      const splitArr = target.id.split('-')
      const contentHandles = {
        middle: showMiddleContent,
        right: showRightContent,
        point: showPointContent,
        scatter: showScatterContent,
      }
      Object.keys(contentHandles).forEach((key) => {
        if (splitArr.includes(key)) {
          contentHandles[key](splitArr, target)
        }
      })
    }
    // 创建一个IntersectionObserver实例
    const io = new IntersectionObserver((entries) => {
      // entries是被观察的元素集合
      entries.forEach((entry) => {
        // 根据不同的情况来处理
        if (entry.isIntersecting) {
          // 1. target为散点图
          handleIntersection(entry.target)
        }
      })
    })

    // * 打开SM图表抽屉
    const smChartDrawerState = reactive({
      visible: false,
      currentData: {},
    })
    const handleOpenSmChartDrawer = (item) => {
      isFirstEnter.value = false
      smChartDrawerState.currentData = item
      smChartDrawerState.visible = true
    }

    // 基础信息数据
    const baseInfoList = ref([])
    // 获取基础信息
    const getBaseInfo = async () => {
      const params = {
        ac: 'de_get_wo_list',
        filter_fields: parentFilterFields.value,
        str_order_feild: sortState.str_order_feild || 'dt_release',
        str_order_acs: sortState.str_order_acs || 'asc',
      }

      // 使用Promise包装请求
      return new Promise((resolve) => {
        requestAnimationFrame(async () => {
          try {
            const { data } = await post(params)
            if (data.code === 'success') {
              baseInfoList.value = data.data.map((item, index) => {
                return {
                  ...item,
                  tempSort: index + 1,
                  key: Math.random(),
                  // 默认进来推演后
                  // activeButton: 'handleAfterSimulation',
                  // fix 推演前
                  activeButton: 'handleBeforeSimulation',
                }
              })
              resolve(true)
            } else {
              ElementPlus.ElMessage.error(data.text)
              resolve(false)
            }
          } catch (err) {
            ElementPlus.ElMessage.error('请求失败')
            resolve(false)
          }
        })
      })
    }

    const topMiddleRef = ref({})
    const setTopMiddleRef = (el, id) => {
      if (el) {
        topMiddleRef.value[id] = el
        if (isFirstEnter.value) {
          // 监听元素
          io.observe(el.$el)
        }
      }
    }

    // 设置发动机右边ref
    const topRightRef = ref({})
    const setTopRightRef = (el, id) => {
      if (el) {
        topRightRef.value[id] = el
        if (isFirstEnter.value) {
          // 监听元素
          io.observe(el.$el)
        }
      }
    }

    // 设置散点图ref
    const scatterChartRef = ref({})
    const setScatterChartRef = (el, id) => {
      if (el) {
        scatterChartRef.value[id] = el
        if (isFirstEnter.value) {
          // 监听元素
          io.observe(el.$el)
        }
      }
    }

    // * 散点图点击事件
    const handleDotClick = (data) => {
      const { id, id_wo, int_point_type } = data
      // 获取点的ref
      const pointColor = pointColorRef.value[id_wo]
      // 打开点位颜色抽屉
      pointColor.handleOpenDrawer(int_point_type, id)
    }

    // 设置点位颜色ref
    const pointColorRef = ref({})
    const setPointColorRef = (el, id) => {
      if (el) {
        pointColorRef.value[id] = el
        // 监听元素
        if (isFirstEnter.value) {
          io.observe(el.$el)
        }
      }
    }

    // * 点位颜色计提交
    const handlePointColorStatisticsSubmit = (id) => {
      // 刷新头部右边数据
      topRightRef.value[id].getState()
      // 刷新散点图
      scatterChartRef.value[id].getChartList(
        parentIsBeforeSimulation.value,
        parentFilterFields.value,
        parentIsSimulation.value,
      )
    }
    // * 散点图提交
    const handleScatterChartSubmit = (idWo) => {
      getBaseInfo()
      // 刷新头部中间数据
      topMiddleRef.value[idWo].getState()
    }

    // * 顶部右边提交
    const handleEngineTopRightSubmit = (id) => {
      // 刷新点位颜色
      changePointColor(id)
      // 刷新散点图
      changeScatterChart(id)
    }

    const { adjustLineDialog, handleAdjustLine, handleAdjustLineSubmit } = useAdjustLine()

    const handleLineSubmit = async (data) => {
      await handleAdjustLineSubmit(data, getBaseInfo)
      ElementPlus.ElMessage.success('调整成功')
      // 刷新图表
      changeScatterChart(data.id_wo)
    }

    const handleCommand = (command, item) => {
      isFirstEnter.value = false
      if (command === 'a') {
        handleOrder(item)
      } else if (command === 'b') {
        handleAdjustLine(item)
      }
    }
    // * 顺序弹框
    const orderDialogState = reactive({
      visible: false,
      currentData: {
        currentOrder: Infinity,
        targetOrder: Infinity,
        id_wo: null,
      },
    })
    // * 顺序调整
    const handleOrder = (item) => {
      orderDialogState.currentData.currentOrder = parseInt(item.int_sort)
      orderDialogState.currentData.targetOrder = parseInt(item.int_sort)
      orderDialogState.currentData.id_wo = item.id_wo
      orderDialogState.visible = true
    }

    const move = (arr, oldIndex, newIndex) => {
      // 如果新位置大于数组长度,则补充undefined
      if (newIndex >= arr.length) {
        // 补充的个数
        let k = newIndex - arr.length + 1
        // 补充undefined
        while (k--) {
          arr.push(undefined)
        }
      }
      // 删除旧位置的元素,并在新位置插入
      arr.splice(newIndex, 0, arr.splice(oldIndex, 1)[0])
      return arr
    }
    // * 顺序调整提交
    const handleOrderDialogSubmit = async (data, idWo) => {
      const { currentOrder, targetOrder } = data
      const params = {
        ac: 'de_save_sort',
        id_wo: orderDialogState.currentData.id_wo ?? idWo,
        int_sort: targetOrder.toString(),
      }
      const { data: res } = await post(params)
      if (res.code === 'success') {
        // 创建映射
        const intSortMap = new Map(res.data.map((item) => [item.id_wo, item.int_sort]))
        // 移动数组
        const oldIndex = baseInfoList.value.findIndex((item) => item.int_sort === currentOrder)
        const newIndex = baseInfoList.value.findIndex((item) => item.int_sort === targetOrder)
        baseInfoList.value = move(baseInfoList.value, oldIndex, newIndex)
        // 使用映射更新baseInfoList.value中的int_sort
        baseInfoList.value.forEach((item, index) => {
          // 如果映射中有当前id_wo,则更新int_sort
          if (intSortMap.has(item.id_wo)) {
            item.int_sort = intSortMap.get(item.id_wo)
          }
          item.tempSort = index + 1
        })
        orderDialogState.visible = false
        // 滚动条跳转
        handleScrollTo(targetOrder)
      } else {
        ElementPlus.ElMessage.error(res.text)
      }
    }
    // * 上移
    const movePre = (item) => {
      // 当为第一个的时候提示当前为第一项不可上移
      if (item.int_sort === 1) {
        ElementPlus.ElMessage.warning('当前为第一项不可上移')
        return
      }
      // 当前项的序号
      const currentSort = item.int_sort
      // 上一项的序号
      const preSort = currentSort - 1
      const data = {
        currentOrder: currentSort,
        targetOrder: preSort,
      }
      handleOrderDialogSubmit(data, item.id_wo)
    }
    // * 下移
    const moveNext = (item) => {
      // 当为最后一个的时候提示当前为最后一项不可下移
      if (item.int_sort === baseInfoList.value.length) {
        ElementPlus.ElMessage.warning('当前为最后一项不可下移')
        return
      }
      // 当前项的序号
      const currentSort = item.int_sort
      // 下一项的序号
      const nextSort = currentSort + 1
      const data = {
        currentOrder: currentSort,
        targetOrder: nextSort,
      }
      handleOrderDialogSubmit(data, item.id_wo)
    }

    /**
     * 改变点位颜色
     * @param {String} idWo  id_wo
     * @param {String} ekdType int_ekd_type
     */
    const changePointColor = (idWo, ekdType) => {
      const pointColor = pointColorRef.value[idWo]
      pointColor.getPointColorStatistics(idWo, parentFilterFields.value, ekdType)
    }

    /**
     * 改变散点图
     * @param {String} idWo id_wo
     * @param {Number} exception 1 or undefine
     */
    const changeScatterChart = (idWo, exception) => {
      const myChart = scatterChartRef.value[idWo]
      myChart.getChartList(
        parentIsBeforeSimulation.value,
        parentFilterFields.value,
        parentIsSimulation.value,
        exception,
      )
    }
    // * 推演前
    const handleBeforeSimulation = (item, buttonName, event) => {
      item.activeButton = buttonName
      isFirstEnter.value = false
      parentIsBeforeSimulation.value = true
      parentIsSimulation.value = false
      // 改变点位颜色
      changePointColor(item.id_wo, EKD_TYPE_ENUM.BEFORE_SIMULATION)
      // 获取散点图
      changeScatterChart(item.id_wo)
    }
    // * 推演后
    const handleAfterSimulation = (item, buttonName) => {
      item.activeButton = buttonName
      isFirstEnter.value = false
      parentIsBeforeSimulation.value = false
      parentIsSimulation.value = false
      // 改变点位颜色
      changePointColor(item.id_wo, EKD_TYPE_ENUM.AFTER_SIMULATION)
      // 获取散点图
      changeScatterChart(item.id_wo)
    }

    const simulateAfterState = reactive({
      visible: false,
      currentData: {},
    })
    // * 模拟后清单
    const openSimulationDrawer = (item) => {
      if (!item.dt_simulation) {
        ElementPlus.ElMessage.warning('当前无模拟结果,请模拟后查看')
        return
      }
      isFirstEnter.value = false
      simulateAfterState.currentData = item
      simulateAfterState.visible = true
    }
    // 推演串件结果抽屉
    const deductionDrawerState = reactive({
      visible: false,
      currentData: {},
    })
    // * 推演串件结果
    const handleOpenDeductionDrawer = (item) => {
      isFirstEnter.value = false
      deductionDrawerState.currentData = item
      deductionDrawerState.visible = true
    }

    // * GM接手
    const handleGmTakeover = (item) => {
      isFirstEnter.value = false
      ElementPlus.ElMessageBox.confirm('确定GM接手吗?,接手后不可撤回！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const params = {
          ac: 'de_gm_takeover',
          id_wo: item.id_wo,
        }
        const { data } = await post(params)
        if (data.code === 'success') {
          item.is_gm = true
        } else {
          ElementPlus.ElMessage.error(data.text)
        }
      })
    }
    // * 模拟API
    const simulateApi = async (item, buttonName, currentData) => {
      const idWo = item.id_wo
      ElementPlus.ElMessageBox({
        title: '提示',
        showCancelButton: true,
        message: () => messageRender(currentData),
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(async () => {
          if (currentData.mode === '1') {
            parentIsSimulation.value = true
            const params = {
              ac: 'de_calculate_one',
              id_wo: idWo,
              dt_gp_simulation_start: currentData.dt_gp_simulation_start,
              str_customer: currentData.str_customer,
              int_yellow: currentData.int_yellow,
            }
            // 添加loading
            const loading = ElementPlus.ElLoading.service({
              target: scatterChartRef.value[idWo].$el,
              text: '模拟中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            const { data } = await post(params)
            if (data.code === 'success') {
              item.activeButton = 'simulateAfter'
              // 更新模拟时间
              item.dt_simulation = data.data.dt_simulation ?? moment().format('YYYY-MM-DD HH:mm:ss')
              // 更新当前行数据
              item.dt_gp_simulation_start = currentData.dt_gp_simulation_start
              // 调用模拟后接口
              // 改变点位颜色
              changePointColor(idWo, EKD_TYPE_ENUM.AFTER_ENGINE_SIMULATION)
              // 获取散点图
              changeScatterChart(idWo)
              ElementPlus.ElMessage.success(data.text)
            } else {
              ElementPlus.ElMessage.error(data.text)
            }
            loading.close()
          } else {
            ElementPlus.ElMessage.success('待开发')
          }
        })
        .catch(() => {
          // 刷新散点图
          // changeScatterChart(idWo)
        })
    }
    const { customerOptions, getCustomerOptions } = useOption()
    // * 红蓝模拟消息的渲染函数
    const messageRender = (currentData) => {
      return h('div', null, [
        // form表单
        h(
          ElForm,
          {
            modelValue: currentData,
            labelWidth: '115px',
            'onUpdate:modelValue': (val) => {
              currentData = val
            },
          },
          [
            h(ElFormItem, { label: '操作' }, [
              h(
                ElSelect,
                {
                  modelValue: currentData.mode,
                  disabled: currentData.isRedAndBlue,
                  'onUpdate:modelValue': (val) => {
                    currentData.mode = val
                  },
                },
                [h(ElOption, { label: '设置模拟', value: '1' })], //, h(ElOption, { label: '调整计划', value: '2' })
              ),
            ]),
            h(ElFormItem, { label: '开始日期' }, [
              h(ElDatePicker, {
                modelValue: currentData.dt_gp_simulation_start,
                // 禁用小于今天的日期
                disabledDate: (date) => {
                  return date.getTime() < Date.now()
                },
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD',
                'onUpdate:modelValue': (val) => {
                  currentData.dt_gp_simulation_start = val
                },
              }),
            ]),
            // 当选择调整计划时，显示客户和限定已开黄标签
            currentData.mode === '1' && [
              h(ElFormItem, { label: '客户', prop: 'str_customer' }, [
                h(
                  ElSelect,
                  {
                    modelValue: currentData.str_customer,
                    // 多选
                    multiple: true,
                    // collapseTags: true, // 是否不显示完整信息
                    // 可清空
                    clearable: true,
                    // 可搜索
                    filterable: true,

                    'onUpdate:modelValue': (val) => {
                      currentData.str_customer = val
                    },
                  },
                  h(ElCheckbox, {
                    label: '全选',
                    modelValue: currentData.str_customer.length === customerOptions.value.length,
                    'onUpdate:modelValue': (val) => {
                      if (val) {
                        currentData.str_customer = customerOptions.value.map((item) => item.value)
                      } else {
                        currentData.str_customer = []
                      }
                    },
                  }),
                  [
                    // customerOptions.value.length > 0 && h(ElOption,
                    //   {
                    //     label: `Select All (${customerOptions.value.length})`, value: 'all',
                    //     onclick: () => {
                    //       currentData.str_customer = customerOptions.value.map((item) => item.value)
                    //     }
                    //   }

                    // ),
                    // customerOptions.value.length > 0 && h(ElOption,
                    //   {
                    //     label: `Cancel All (${customerOptions.value.length})`, value: 'cancel_all',
                    //     onclick: () => {
                    //       currentData.str_customer = []
                    //     }
                    //   }

                    // ),
                    customerOptions.value.map((item) => {
                      return h(ElOption, { key: item.value, label: item.label, value: item.value })
                    }),
                  ],
                ),
              ]),
              // 限定已开黄标签
              h(ElFormItem, { label: '限定已开黄标签' }, [
                h(
                  ElSelect,
                  {
                    modelValue: currentData.int_yellow,
                    clearable: true,
                    'onUpdate:modelValue': (val) => {
                      currentData.int_yellow = val
                    },
                  },
                  [h(ElOption, { label: '是', value: 1 }), h(ElOption, { label: '否', value: 0 })],
                ),
              ]),
            ],
          ],
        ),
      ])
    }
    // 点击未扫描按钮
    const handleUnscanButton = (item, buttonName) => {
      item.activeButton = buttonName
      isFirstEnter.value = false
      changeScatterChart(item.id_wo, 130)
    }
    // 点击未集件按钮
    const handleUncollectButton = (item, buttonName) => {
      item.activeButton = buttonName
      isFirstEnter.value = false
      changeScatterChart(item.id_wo, 140)
    }
    // 点击有风险按钮
    const handleRiskButton = (item, buttonName) => {
      item.activeButton = buttonName
      isFirstEnter.value = false
      changeScatterChart(item.id_wo, 150)
    }
    // 点击全部按钮
    const handleAllButton = (item, buttonName) => {
      item.activeButton = buttonName
      isFirstEnter.value = false
      changeScatterChart(item.id_wo)
    }
    // * 红蓝模拟
    const simulate = (item, buttonname) => {
      isFirstEnter.value = false
      parentIsBeforeSimulation.value = false
      parentIsSimulation.value = true
      const params = {
        startDate: item.dt_gp_simulation_start || item.dt_project_start || item.dt_adjust_start,
      }
      const currentData = reactive({
        isRedAndBlue: true,
        mode: '1',
        id_wo: item.id_wo,
        dt_gp_simulation_start: isBeforeSimulation(params) ? moment().format('YYYY-MM-DD') : params.startDate,
        str_customer: [],
        int_yellow: 1,
      })
      simulateApi(item, buttonname, currentData)
    }
    const isOutMinOrMax = (params) => {
      isFirstEnter.value = false
      const { startDate, endData, minDate, maxDate, idWo } = params
      const isOutMinOrMax = moment(startDate).isBefore(minDate) || moment(endData).isAfter(maxDate)
      if (isOutMinOrMax) {
        ElementPlus.ElMessage({
          message: '超出坐轴范围',
          type: 'error',
          duration: 3000,
        })
        // 刷新散点图
        const myChart = scatterChartRef.value[idWo]
        myChart.getChartList(parentIsBeforeSimulation.value, parentFilterFields.value, parentIsSimulation.value)
        return true
      }
      return false
    }
    const isBeforeNow = (params) => {
      const { startDate, idWo } = params
      const isBeforeNow = moment(startDate).isBefore(moment())
      if (isBeforeNow) {
        ElementPlus.ElMessage({
          message: '开始时间不能小于当前时间',
          type: 'error',
          duration: 3000,
        })
        // 刷新散点图
        const myChart = scatterChartRef.value[idWo]
        myChart.getChartList(parentIsBeforeSimulation.value, parentFilterFields.value, parentIsSimulation.value)
        return true
      }
      return false
    }
    const isBeforeSimulation = (params) => {
      const { startDate } = params
      return moment(startDate).isBefore(moment())
    }
    // * 通过拖动红框之后模拟
    const handleSimulationByDrag = (params) => {
      isFirstEnter.value = false
      const { idWo, startDate } = params
      // 1. 判断拖动的红框是否超出坐标轴范围
      if (isOutMinOrMax(params)) return
      // 2. 开始时间不能小于当前时间
      if (isBeforeNow(params)) return
      // 据当前idWo获取当前行
      const item = baseInfoList.value.find((item) => item.id_wo === idWo)
      const buttonName = ''
      // 3.模拟时间小于今天时间则取今天时间
      const dtGpSimulationStart = isBeforeSimulation(params) ? moment().format('YYYY-MM-DD') : startDate
      const currentData = reactive({
        isRedAndBlue: false,
        mode: '1',
        id_wo: idWo,
        dt_gp_simulation_start: dtGpSimulationStart,
        str_customer: [],
        int_yellow: 1,
      })
      simulateApi(item, buttonName, currentData)
    }
    /**模拟前零件数据渲染 */
    const simulateAgo = (item, buttonName) => {
      isFirstEnter.value = false
      parentIsBeforeSimulation.value = true
      parentIsSimulation.value = false
      ElementPlus.ElMessageBox.confirm('将显示【模拟前】零件分布情况', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        item.activeButton = buttonName
        // 改变点位颜色
        changePointColor(item.id_wo, EKD_TYPE_ENUM.BEFORE_ENGINE_SIMULATION)
        // 获取散点图
        changeScatterChart(item.id_wo)
      })
    }
    /**模拟后零件数据渲染 */
    const simulateAfter = (item, buttonName) => {
      isFirstEnter.value = false
      if (!item.dt_simulation) {
        ElementPlus.ElMessage.warning('当前无模拟结果,请模拟后查看')
        return
      }
      parentIsBeforeSimulation.value = false
      parentIsSimulation.value = true
      ElementPlus.ElMessageBox.confirm('将显示【模拟后】零件分布情况', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        item.activeButton = buttonName
        // 改变点位颜色
        changePointColor(item.id_wo, EKD_TYPE_ENUM.AFTER_ENGINE_SIMULATION)
        // 获取散点图
        changeScatterChart(item.id_wo)
      })
    }

    const changeListState = reactive({
      visible: false,
      currentData: {},
    })
    // * 已串件清单
    const openChangeListDrawer = (item) => {
      isFirstEnter.value = false
      changeListState.currentData = item
      changeListState.visible = true
    }

    // * 点击按钮
    const handleClickButton = (buttonName, item, event) => {
      // 调用响应的方法
      const buttonActions = {
        simulate: simulate,
        simulateAgo: simulateAgo,
        simulateAfter: simulateAfter,
        openSimulationDrawer: openSimulationDrawer,
        handleBeforeSimulation: handleBeforeSimulation,
        handleAfterSimulation: handleAfterSimulation,
        handleOpenDeductionDrawer: handleOpenDeductionDrawer,
        openChangeListDrawer: openChangeListDrawer,
        risk: handleRiskButton,
        all: handleAllButton,
        unscan: handleUnscanButton,
        uncollect: handleUncollectButton,
      }

      if (buttonActions.hasOwnProperty(buttonName)) {
        buttonActions[buttonName](item, buttonName, event)
      }
    }

    // * 滚动条
    const scrollRef = ref(null)

    // * 跳转到指定位置
    const handleScrollTo = (targetOrder) => {
      // 折叠form表单
      handleCollapse()
      // 获取form表单的高度
      const formHeight = document.getElementById('header_form').scrollHeight
      // 获取tag的高度
      const tagHeight = document.getElementById('header_tag').scrollHeight
      // 获取图表的高度
      const chartHeight = document.getElementById('header_chart').scrollHeight
      const totalHeight = formHeight + tagHeight + chartHeight * (targetOrder - 1)
      const top = totalHeight
      nextTick(() => {
        scrollRef.value.setScrollTop(top)
      })
    }

    /* ----------------------按钮颜色---------------------- */
    const getButtonClass = (item) => {
      if (item.int_needconfirm === 0) {
        return 'bg-indigo-600 hover:bg-indigo-700'
      } else if (item.int_needconfirm > 0 && item.int_needconfirm === item.int_agree) {
        return 'bg-green-600 hover:bg-green-700'
      } else if (item.int_needconfirm > 0 && item.int_reject > 0) {
        return 'bg-red-600 hover:bg-red-700'
      } else {
        return 'bg-indigo-600 hover:bg-indigo-700'
      }
    }
    /* ----------------------同步---------------------- */
    const handleSync = async (item) => {
      const { id_wo } = item
      const dom = document.getElementById(id_wo)
      const loading = ElementPlus.ElLoading.service({
        target: dom,
        text: '同步中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      return new Promise((resolve) => {
        requestAnimationFrame(async () => {
          try {
            await post({ ac: 'pda_ekd_node_by_id_wo', id_wo })
            const params = {
              ac: 'de_wo_sync',
              id_wo,
            }
            const { data } = await post(params)
            if (data.code === 'success') {
              ElementPlus.ElMessage.success(data.text)
              // 使用requestAnimationFrame刷新图表
              requestAnimationFrame(() => {
                topMiddleRef.value[id_wo].getState()
                topRightRef.value[id_wo].getState()
                changeScatterChart(id_wo)
              })
              resolve(true)
            } else {
              ElementPlus.ElMessage.error(data.text)
              resolve(false)
            }
          } catch (err) {
            ElementPlus.ElMessage.error('同步失败')
            resolve(false)
          } finally {
            loading.close()
          }
        })
      })
    }
    // * 页面加载
    onMounted(() => {
      if (inputesn) {
        parentFilterFields.value = [{ str_key: 'str_esn', str_value: inputesn }]
      }
      getBaseInfo()
      getCustomerOptions()
    })

    /* ----------------------GM接手 GP接手 ---------------------- */
    const { handleGmLockOrUnlock, handleGpLockOrUnlock,handleRulLockOrUnlock } = useButton(
      scatterChartRef,
      parentIsBeforeSimulation,
      parentFilterFields,
      parentIsSimulation,
    )

    // 返回数据
    return {
      scrollRef,
      handleClickButton,
      parentFilterFields,
      handleSearch,
      handleCollapse,
      baseInfoList,
      setTopRightRef,
      setScatterChartRef,
      setPointColorRef,
      orderDialogState,
      handleOrder,
      handleOrderDialogSubmit,
      movePre,
      moveNext,
      smChartDrawerState,
      handleOpenSmChartDrawer,
      deductionDrawerState,
      handlePointColorStatisticsSubmit,
      handleEngineTopRightSubmit,
      handleScatterChartSubmit,
      handleCommand,
      adjustLineDialog,
      handleAdjustLine,
      handleLineSubmit,
      simulateAfterState,
      changeListState,
      handleSimulationByDrag,
      setTopMiddleRef,
      handleDotClick,
      getButtonClass,
      handleSync,
      handleGmLockOrUnlock,
      handleGpLockOrUnlock,
      handleRulLockOrUnlock,
      // 排序相关
      sortState,
      handleSortChange,
    }
  },
  template: /*html*/ `
    <el-scrollbar class="scroll" height="calc(100vh)" ref="scrollRef">
      <!-- 头部搜索 -->
      <div class="m-4 border px-4" id="header_form">
        <PartSearchForm @search="handleSearch" @collapse="handleCollapse"></PartSearchForm>
      </div>
      <!-- 标识 -->
      <div class="m-2 px-4 py-3 bg-white shadow-sm rounded-md border border-gray-200" id="header_tag">
        <div class="flex flex-wrap items-center justify-between">
          <!-- 排序选项 -->
          <div class="flex flex-wrap gap-6">
            <div class="flex flex-col border-l pl-6 border-gray-200">
              <div class="text-sm font-medium text-gray-700 mb-1.5 flex items-center">
                <svg class="w-4 h-4 mr-1 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Release排序
              </div>
              <el-radio-group 
                v-model="sortState.releaseSort" 
                size="small" 
                class="shadow-sm" 
                @change="(val) => handleSortChange('releaseSort', val)"
              >
                <el-radio-button class="!rounded-l-md hover:!opacity-90" label="asc">升序</el-radio-button>
                <el-radio-button class="!rounded-r-md hover:!opacity-90" label="desc">降序</el-radio-button>
              </el-radio-group>
            </div>
            <div class="flex flex-col border-l pl-6 border-gray-200">
              <div class="text-sm font-medium text-gray-700 mb-1.5 flex items-center">
                <svg class="w-4 h-4 mr-1 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                F3Close排序
              </div>
              <el-radio-group 
                v-model="sortState.f3closeSort" 
                size="small" 
                class="shadow-sm" 
                @change="(val) => handleSortChange('f3closeSort', val)"
              >
                <el-radio-button class="!rounded-l-md hover:!opacity-90" label="asc">升序</el-radio-button>
                <el-radio-button class="!rounded-r-md hover:!opacity-90" label="desc">降序</el-radio-button>
              </el-radio-group>
            </div>
            <div class="flex flex-col border-l pl-6 border-gray-200">
              <div class="text-sm font-medium text-gray-700 mb-1.5 flex items-center">
                <svg class="w-4 h-4 mr-1 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                GP排序
              </div>
              <el-radio-group 
                v-model="sortState.gpSort" 
                size="small" 
                class="shadow-sm" 
                @change="(val) => handleSortChange('gpSort', val)"
              >
                <el-radio-button class="!rounded-l-md hover:!opacity-90" label="asc">升序</el-radio-button>
                <el-radio-button class="!rounded-r-md hover:!opacity-90" label="desc">降序</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          
          <!-- 颜色标识说明 -->
          <div class="flex flex-wrap gap-4 mt-4 md:mt-0 bg-gray-50 p-2 rounded-md border border-gray-100">
            <div class="flex items-center">
              <div class="mr-2 h-4 w-4 rounded-full bg-green-500 shadow-sm border border-gray-300"></div>
              <span class="text-sm text-gray-700">可串件</span>
            </div>
            <div class="flex items-center">
              <div class="mr-2 h-4 w-4 rounded-full bg-blue-500 shadow-sm border border-gray-300"></div>
              <span class="text-sm text-gray-700">有条件串件</span>
            </div>
            <div class="flex items-center">
              <div class="mr-2 h-4 w-4 rounded-full bg-red-500 shadow-sm border border-gray-300"></div>
              <span class="text-sm text-gray-700">不能串</span>
            </div>
            <div class="flex items-center">
              <div class="mr-2 h-4 w-4 rounded-full bg-purple-500 shadow-sm border border-gray-300"></div>
              <span class="text-sm text-gray-700">不具备串件条件</span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-for="item in baseInfoList" :key="item.key" id="header_chart">
        <div :id="item.id_wo">
          <div class="flex items-center">
            <!-- 序号 -->
            <div class="flex items-center justify-center">
              <div class="pl-4 text-xl font-bold">{{ item.tempSort }}.</div>
            </div>
            <!-- 头部信息 -->
            <div class="pl-4 font-bold">发动机信息</div>
            <!-- 序号 是个圆 -->
            <div
              class="my-2 ml-2 flex h-8 w-8 items-center justify-center rounded-full text-black font-bold"
              :class="{ 'bg-gray-300': item.is_lock === '1', 'bg-blue-500 text-white': item.is_lock === '0' }"
            >
              {{ item.int_sort }}
            </div>
            <!-- 同步按钮 -->
            <div class="ml-4 flex items-center">
              <el-tooltip content="同步" placement="top">
                <el-button circle type="primary" @click="handleSync(item)">
                  <el-icon><Refresh></Refresh></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
          <!-- 详细信息和图表 -->
          <div class="mx-4 flex gap-2 border">
            <div class="md:w-1/4 w-full flex-none">
              <!-- 详细信息 -->
              <el-descriptions class="my-descriptions" :column="1" border>
                <el-descriptions-item label="ESN:" >
                  <div
                    class="text-xl font-bold hover:cursor-pointer hover:text-blue-300 pl-2"
                    @click="handleOpenSmChartDrawer(item)"
                  >
                    {{ item.str_esn }}({{ item.str_code }}) {{item.str_level}}
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="Flow:" >
                  <div class="pl-2">{{ item.str_flow }}</div>
                </el-descriptions-item>
                <el-descriptions-item label="客户:">
                  <div class="pl-2">{{ item.str_client }}</div>
                </el-descriptions-item>
                <el-descriptions-item label="Project F4开始时间:" >
                  <div class="pl-2">{{ item.dt_project_start }}</div>
                </el-descriptions-item>
                <el-descriptions-item label="绿框时间:" >
                  <div class="pl-2">{{ item.dt_adjust_start }}</div>
                </el-descriptions-item>
                <el-descriptions-item label="借件办理日期:" >
                  <div class="pl-2">{{ item.dt_borrow }}</div>
                </el-descriptions-item>
                <el-descriptions-item label="放行结束时间:" >
                  <div class="pl-2">{{ item.dt_release }}</div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="flex-1">
              <!-- 不可串件信息 -->
              <EngineTopMiddle :ref="(el) => setTopMiddleRef(el, item.id_wo)" :id="item.id_wo"></EngineTopMiddle>
            </div>
            <div class="w-1/5 flex-none">
              <!-- 可串件信息 -->
              <EngineTopRight
                :ref="(el) => setTopRightRef(el, item.id_wo)"
                :id="item.id_wo"
                @submit="handleEngineTopRightSubmit"
              ></EngineTopRight>
            </div>
          </div>
          <div class="mx-8 my-2 flex items-center justify-between">
            <PointColorStatistics
              :ref="(el) => setPointColorRef(el, item.id_wo)"
              :id-wo="item.id_wo"
              @submit="handlePointColorStatisticsSubmit"
            ></PointColorStatistics>
            <div class="mx-6 my-2 flex items-center justify-end">最近推演：{{item.dt_last_deduction}}</div>
            <div class="mx-6 my-2 flex items-center justify-end">最近模拟：{{item.dt_simulation}}</div>
             <div class="mx-6 my-2 flex items-center justify-end">
              RUL接手：
              <span v-if="item.is_rul==='1'">{{item.dt_rul}}</span>

              <el-button size="mini" type="primary" v-if="item.is_rul==='0'" @click="handleRulLockOrUnlock(item)">
                接手
              </el-button>
              <el-button size="mini" type="primary" v-else @click="handleRulLockOrUnlock(item)">解锁</el-button>
            </div>
            <div class="mx-6 my-2 flex items-center justify-end">
              GM接手：
              <span v-if="item.is_lock==='1'">{{item.dt_gm}}</span>

              <el-button size="mini" type="primary" v-if="item.is_lock==='0'" @click="handleGmLockOrUnlock(item)">
                接手/锁定
              </el-button>
              <el-button size="mini" type="primary" v-else @click="handleGmLockOrUnlock(item)">解锁</el-button>
            </div>
            <div class="mx-6 my-2 flex items-center justify-end">
              GP接手：
              <span v-if="item.is_gp=== '1'">{{item.dt_gp}}</span>

              <el-button size="mini" type="primary" v-if="item.is_gp==='0'" @click="handleGpLockOrUnlock(item)">
                接手/锁定
              </el-button>
              <el-button size="mini" type="primary" v-else @click="handleGpLockOrUnlock(item)">解锁</el-button>
            </div>
            <!-- 上下调顺序 -->
            <el-dropdown trigger="click" @command="handleCommand($event, item)">
              <el-icon size="28" class="hover:cursor-pointer hover:text-blue-300">
                <Setting></Setting>
              </el-icon>
              <template #dropdown>
                <el-dropdown-item command="a">调整顺序</el-dropdown-item>
                <el-dropdown-item command="b">调整G2</el-dropdown-item>
              </template>
            </el-dropdown>
          </div>
          <!-- 图表 -->
          <div class="mx-4 flex h-[50vh] border">
            <div class="h-full flex-auto">
              <ScatterChart
                :ref="(el) => setScatterChartRef(el, item.id_wo)"
                :id="item.id_wo"
                :isLock="item.is_lock"
                :is-lock-gp="item.is_gp"
                :coordRange="[item.dt_project_start, item.dt_project_end]"
                :markAreaRange="[item.dt_adjust_start, item.dt_adjust_end]"
                :markLineDate="item.dt_close"
                :dtGpSimulationStart="item.dt_gp_simulation_start"
                :standard-mark-line-date="item.dt_close_std"
                @submit="handleScatterChartSubmit"
                @simulate="handleSimulationByDrag"
                @dot-click="handleDotClick"
              ></ScatterChart>
            </div>
            <div class="flex h-full w-12 flex-col items-center justify-between bg-gray-100">
              <!-- 上下分布 -->
              <!-- 上箭头 -->
              <div
                class="mt-4 flex items-center justify-center rounded-full bg-blue-200 p-2 hover:cursor-pointer"
                title="上移"
                @click="movePre(item)"
              >
                <svg
                  class="h-6 w-6 text-gray-700"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                </svg>
              </div>
              <!-- 下箭头 -->
              <div
                class="mb-4 flex items-center justify-center rounded-full bg-blue-200 p-2 hover:cursor-pointer"
                title="下移"
                @click="moveNext(item)"
              >
                <svg
                  class="h-6 w-6 text-gray-700"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>
            <div class="min-w-1/8 grid h-full grid-cols-2 gap-1 bg-gray-100 py-2">
              <button
                class="col-span-2 bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                :class="{ 'bg-yellow-700': item.activeButton === 'unscan' }"
                @click="(event) => handleClickButton('unscan', item, event)"
              >
                未扫描零件
              </button>
              <button
                class="col-span-2 bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                :class="{ 'bg-yellow-700': item.activeButton === 'uncollect' }"
                @click="(event) => handleClickButton('uncollect', item, event)"
              >
                未集件零件
              </button>
              <button
                class="col-span-2 bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                :class="{ 'bg-yellow-700': item.activeButton === 'risk' }"
                @click="(event) => handleClickButton('risk', item, event)"
              >
                未关闭零件
              </button>
              <button
                class="col-span-2 bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                :class="{ 'bg-yellow-700': item.activeButton === 'all' }"
                @click="(event) => handleClickButton('all', item, event)"
              >
                全部零件
              </button>
              <button
                class="col-span-2 bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                :class="{ 'bg-yellow-700': item.activeButton === 'simulate' }"
                @click="(event) => handleClickButton('simulate', item, event)"
              >
                红蓝模拟
              </button>
              <button
                class="bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                :class="{ 'bg-yellow-700': item.activeButton === 'simulateAgo' }"
                @click="(event) => handleClickButton('simulateAgo', item, event)"
              >
                模拟前
              </button>
              <button
                class="bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                :class="{ 'bg-yellow-700': item.activeButton === 'simulateAfter' }"
                @click="(event) => handleClickButton('simulateAfter', item, event)"
              >
                模拟后
              </button>
              <button
                class="col-span-2 text-xl text-white shadow disabled:bg-indigo-300"
                :class="getButtonClass(item)"
                @click="(event) => handleClickButton('openSimulationDrawer', item, event)"
              >
                模拟后清单
                <div>
                  {{item.int_needconfirm}}(
                  <span title="同意">{{item.int_agree}}</span>
                  |
                  <span title="不同意">{{item.int_reject}}</span>
                  )
                </div>
              </button>

              <button
                class="bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                :class="{ 'bg-yellow-700': item.activeButton === 'handleBeforeSimulation' }"
                @click="(event) => handleClickButton('handleBeforeSimulation', item, event)"
              >
                推演前
              </button>
              <button
                class="bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                :class="{ 'bg-yellow-700': item.activeButton === 'handleAfterSimulation' }"
                @click="(event) => handleClickButton('handleAfterSimulation', item, event)"
              >
                推演后
              </button>
              <button
                class="col-span-2 bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                @click="(event) => handleClickButton('handleOpenDeductionDrawer', item,event)"
              >
                推演结果
                <div>{{item.int_send}}</div>
              </button>

              <button
                class="col-span-2 bg-indigo-600 text-xl text-white shadow hover:cursor-pointer hover:bg-indigo-700 disabled:bg-indigo-300"
                @click="(event) => handleClickButton('openChangeListDrawer', item, event)"
              >
                已串件清单
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 回到顶部 -->
      <el-backtop target=".scroll .el-scrollbar__wrap" :bottom="80"></el-backtop>
    </el-scrollbar>
    <!-- 顺序弹框 -->
    <OrderDialog
      v-if="orderDialogState.visible"
      v-model:visible="orderDialogState.visible"
      :currentData="orderDialogState.currentData"
      @submit="handleOrderDialogSubmit"
    ></OrderDialog>
    <!-- 线条弹框 -->
    <LineDialog
      v-if="adjustLineDialog.visible"
      v-model:visible="adjustLineDialog.visible"
      :currentData="adjustLineDialog.currentData"
      @submit="handleLineSubmit"
    ></LineDialog>

    <!-- 单元体抽屉 -->
    <UnitPlanDrawer
      v-if="smChartDrawerState.visible"
      v-model:visible="smChartDrawerState.visible"
      :id="smChartDrawerState.currentData.id_wo"
    ></UnitPlanDrawer>

    <!-- 模拟后清单 -->
    <SimulateAfterDrawer
      v-if="simulateAfterState.visible"
      v-model:visible="simulateAfterState.visible"
      :id-wo="simulateAfterState.currentData.id_wo"
    ></SimulateAfterDrawer>

    <!-- 推演串件结果 -->
    <DeductionDrawer
      v-if="deductionDrawerState.visible"
      v-model:visible="deductionDrawerState.visible"
      :id="deductionDrawerState.currentData.id_wo"
      :filterFields="parentFilterFields"
    ></DeductionDrawer>

    <!-- 模拟后清单 -->
    <ChangeListDrawer
      v-if="changeListState.visible"
      v-model:visible="changeListState.visible"
      :id-wo="changeListState.currentData.id_wo"
    ></ChangeListDrawer>
  `,
}
export default EngineSimulationPlan
