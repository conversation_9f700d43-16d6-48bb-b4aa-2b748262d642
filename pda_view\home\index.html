<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta content="width=640, user-scalable=no" name="viewport" />
    <meta content="ie=edge" http-equiv="X-UA-Compatible" />
    <meta content="telephone=no" name="format-detection" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <!-- 引入tailwindcss -->
    <link href="../../assets/css/tailwind.css" rel="stylesheet" />
    <!--    全部样式-->
    <link href="../../assets/css/index.css" rel="stylesheet" />
    <!--    引入element-plus的样式-->
    <link href="../../assets/element-plus@2.5.5/index.css" rel="stylesheet" />
    <!--    引入drawer的样式-->
    <link href="../../assets/css/drawer.css" rel="stylesheet" />
    <!--    引入vxe-table的样式-->
    <link href="../../assets/vxe-table/style.css" rel="stylesheet" />
    <link href="../../assets/css/home.css" rel="stylesheet" />
    <!--    引入VUE-->
    <script src="../../assets/vue@3.4.15/vue.global.prod.js"></script>
    <!--    引入vxe-table组件-->
    <script src="../../assets/vxe-table/xe-utils.js"></script>
    <script src="../../assets/vxe-table/vxe-table.js"></script>
    <!--    引入element-plus-->
    <script src="../../assets/element-plus@2.5.5/index.js"></script>
    <!--  引入element-plus-icon-->
    <script src="../../assets/icons-vue@2.3.1/index.iife.min.js"></script>
    <!--    引入axios-->
    <script src="../../assets/axios@1.6.7/axios.min.js"></script>
    <!--    引入@vueuse/core-->
    <script src="../../assets/@vueuse/shared@10.7.2/index.iife.min.js"></script>
    <script src="../../assets/@vueuse/core@10.7.2/index.iife.min.js"></script>
    <!--    引入moment-->
    <script src="../../assets/moment/moment.min.js"></script>
    <!--    引入echarts-->
    <script src="../../assets/echarts@5.5.0/echarts.min.js"></script>
    <title>Home</title>
  </head>

  <body>
    <div id="app">
      <pda-home></pda-home>
    </div>
  </body>
  <script type="module">
    import PdaHome from './index.js';

    const { createApp } = Vue;

    const app = createApp({
      components: {
        // 组件
        PdaHome,
      },
    });
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component);
    }
    app.use(ElementPlus);
    app.use(VXETable);
    app.use(XEUtils);
    app.mount('#app');
  </script>
  <style lang="css">
    @keyframes scroll {
      0% {
        transform: translateX(0%);
      }
      50% {
        transform: translateX(-50%);
      }
      50.1% {
        transform: translateX(100%);
      }
      100% {
        transform: translateX(0%);
      }
    }
    .scroll-content {
      display: inline-block;
      animation: scroll 30s linear infinite;
    }
  </style>
</html>
