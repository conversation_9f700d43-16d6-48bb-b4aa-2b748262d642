/**
 * @description: 未进入供应链组件
 * @author: rik_tang
 */
import { post } from '../../../../config/axios/httpReuest.js'
const { ref, reactive, onMounted, toRefs } = Vue
import EngineTopMiddleDrawer from './engineTopMiddleDrawer.js'
import WhiteToYellowDrawer from './WhiteToYellowDrawer.js'
import DefaultSerialReturn from '../../components/DefaultSerialReturn.js'
const { InfoFilled } = ElementPlusIconsVue
const EngineTopMiddle = {
  components: {
    EngineTopMiddleDrawer,
    InfoFilled,
    WhiteToYellowDrawer,
    DefaultSerialReturn,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      // IT监控
      partsNotEnteredSupplyChain: 0,
      // 带转包
      withTransferPackage: 0,
      // 待锁库
      toBeLocked: 0,
      // CSM确认
      csmConfirm: 0,
      // 工程确认
      engineeringConfirmation: 0,
      // 消耗件
      consumables: 0,
      // HardWare
      hardWare: 0,
      // F4异常领料
      f4ExceptionMaterialRequisition: 0,
      // 转包无EDD
      transferPackageNoEDD: 0,
      // 采购无EDD
      purchaseNoEDD: 0,
      // 备板
      backupBoard: 0,
      // 采购无LTdate
      laDate: 0,
      // 试车借件
      testLoan: 0,
      noLeavebeforeF3: 0, //未离开Gate 1-F1-1 F1-2
      noLeaveF12: 0, //未离开Gate 1-F2-1之前
      noLeaveF3After: 0, //未离开Gate 1-F3之后
      noSupply: 0, // 进入供应链后无数据
      repair: 0, //修理领料
      beforeIssued: 0, //待发料以前
      csmOthers: 0, //CSM其他
      notIssued: 0, // 未发料
      lockLibraryDemandClose: 0, // 需求关闭
      noPNNumber: 0, // 无PN号
      serialPartsException: 0, // 串件中
      total: 0, // 总量
      // 待处置总量
      pendingTotal: 0,
      // 待客户提供无LTDate
      noLTDate: 0,
      // f2 edd过期
      f2EddExpired: 0,
      testNo: 0, //试车借件未办理
      pendingPnr: 0, //待转PNR
      convertedPnr: 0, //已转PNR
      pendingScan: 0, //待点灯扫描
      // 默认串回
      defaultSerialReturn: 0,
    })
    const loading = ref(false)
    const map = {
      // IT监控
      101: 'partsNotEnteredSupplyChain',
      // 带转包
      102: 'withTransferPackage',
      // CSM确认
      103: 'csmConfirm',
      // 工程确认
      104: 'engineeringConfirmation',
      // 待锁库
      105: 'toBeLocked',
      // 消耗件
      106: 'consumables',
      // Keep Missing
      107: 'hardWare',
      // F4异常领料
      108: 'f4ExceptionMaterialRequisition',
      // 转包无EDD
      109: 'transferPackageNoEDD',
      // 采购无EDD
      126: 'purchaseNoEDD',
      // 备板
      110: 'backupBoard',
      // 待采购
      111: 'laDate',
      // 试车借件
      112: 'testLoan',
      // 未离开Gate 1 F2-1
      113: 'noLeaveF12',
      // 未离开Gate 2 F3 f4
      122: 'noLeaveF3After',
      // 未离开Gate F1-1和F1-2
      125: 'noLeavebeforeF3',
      // 进入供应链后消失
      114: 'noSupply',
      // 修理领料总数
      115: 'repair',
      // 待发料以前
      124: 'beforeIssued',
      // CSM其他
      116: 'csmOthers',
      // 未发料
      117: 'notIssued',
      // 需求关闭
      118: 'lockLibraryDemandClose',
      // 无PN号
      119: 'noPNNumber',
      // 串件中
      120: 'serialPartsException',
      // 总量
      999: 'total',
      // 待处置总量
      130: 'pendingTotal',
      // 待客户提供无LTDate
      121: 'noLTDate',
      // F2 EDD过期
      123: 'f2EddExpired',
      127: 'testNo',
      200: 'pendingPnr',
      300: 'convertedPnr',
      400: 'pendingScan',
      500: 'defaultSerialReturn',
    }
    const labelMap = {
      101: 'EKD计算中',
      102: '待转包',
      103: 'CSM确认',
      104: '工程确认',
      105: '待锁库',
      106: '消耗件',
      107: 'Keep Missing',
      108: 'F4异常领料',
      109: '转包无EDD',
      126: '采购无EDD',
      110: '备板',
      111: '待采购',
      112: '试车借件',
      113: '未离开Gate 1-F2-1',
      122: '未离开Gate 1-F3之后',
      125: '未离开Gate 1-F1-1&F1-2',
      114: '进入供应链后消失',
      115: '修理领料总数',
      124: '待发料以前',
      116: 'CSM其他',
      117: '未发料',
      118: '需求关闭',
      119: '无PN号',
      120: '串件中',
      999: '总量',
      130: '待处置总量',
      121: '待客户提供无LTDate',
      123: 'F2 EDD过期',
      127: '试车借件未办结',
      200: '待转PNR',
      300: '已转PNR',
      400: '待点灯扫描',
      500: '默认串回',
    }
    // 获取数据
    const getState = async () => {
      loading.value = true
      const params = {
        ac: 'de_pendingpnsummary',
        id_wo: props.id,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        const { pnCounts } = data.data
        pnCounts.forEach((item) => {
          state[map[item.int_point_type]] = item.int_num
        })
        loading.value = false
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    const drawerState = reactive({
      visible: false,
      type: '',
    })
    const whiteToYellowDrawerState = reactive({
      visible: false,
      type: '',
    })
    const defaultSerialReturnState = reactive({
      visible: false,
      type: '',
    })

    // * 打开抽屉
    const handleOpenDrawer = (type) => {
      if (type === 200 || type === 300 || type === 400) {
        whiteToYellowDrawerState.visible = true
        whiteToYellowDrawerState.type = type
      } else if (type === 500) {
        defaultSerialReturnState.visible = true
        defaultSerialReturnState.type = type
      } else {
        drawerState.visible = true
        drawerState.type = type
      }
    }

    onMounted(() => {
      // getState()
    })

    return {
      ...toRefs(state),
      loading,
      getState,
      drawerState,
      whiteToYellowDrawerState,
      handleOpenDrawer,
      labelMap,
      defaultSerialReturnState,
    }
  },
  template: /*html*/ `
    <div :id="'middle-'+id">
      <el-skeleton :loading="loading" animated>
        <template #template>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
        </template>
        <template #default>
          <el-descriptions class="my-descriptions" :column="3" border>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex flex-col">
                  <div class="flex items-center justify-center gap-2">
                    <span>未离开Gate 1:</span>
                    <el-tooltip placement="top" effect="dark">
                      <template #content>
                        <span>
                          零件报表主状态:TBD、工程待定；主状态：转包但无工卡或工卡被退回;主状态：不可用，无用料需求；F1-2管理
                          | F2-1管理 | F3和F4管理
                        </span>
                      </template>
                      <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                    </el-tooltip>
                  </div>
                </div>
              </template>
              <div class="flex justify-around divide-x-2">
                <div
                  :title="labelMap[125]"
                  class="pr-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(125)"
                >
                  {{ noLeavebeforeF3 }}
                </div>
                <div
                  :title="labelMap[113]"
                  class="pr-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(113)"
                >
                  {{ noLeaveF12 }}
                </div>
                <div
                  :title="labelMap[122]"
                  class="pl-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(122)"
                >
                  {{ noLeaveF3After }}
                </div>
              </div>
            </el-descriptions-item>

            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>锁库/需求关闭:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>
                        模块"锁库处理"中"锁库确认"为"已关闭"或"无需处理"；模块"用料申请"中的"处理状态"为"关闭"。
                      </span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(118)">
                {{ lockLibraryDemandClose }}
              </div>
            </el-descriptions-item>

            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>待转包:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>开出转包工卡后，未能生成转包订单；在PDA的"转包PO"站点，必定能查到对应的待转包工卡。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(102)">
                {{ withTransferPackage }}
              </div>
            </el-descriptions-item>

            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>修理领料:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>模块"用料申请"的需求类别为"修理领料"的零件。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="flex justify-center divide-x-2">
                <div
                  class="pr-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(124)"
                  :title="labelMap[124]"
                >
                  {{ beforeIssued}}
                </div>
                <div
                  class="pl-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(115)"
                  :title="labelMap[115]"
                >
                  {{ repair}}
                </div>
              </div>
            </el-descriptions-item>

            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>CSM确认(进厂缺件):</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>在模块"进厂缺件客户确认"未给出市场决定。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(103)">
                {{ csmConfirm }}
              </div>
            </el-descriptions-item>

            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>转包|采购无EDD:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>
                        Subcontracting|Purchase without EDD 开出转包订单后，未能填写EDD或EDD小于当前日期（EDD过期）。
                      </span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="flex justify-center divide-x-2">
                <div class="pr-2 hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(109)">
                  <span :title="labelMap[109]">{{ transferPackageNoEDD }}</span>
                </div>
                <div class="pl-2 hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(126)">
                  <span :title="labelMap[126]">{{ purchaseNoEDD }}</span>
                </div>
              </div>
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>进入供应链后消失:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>进入供应链后无数据。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(114)">{{ noSupply}}</div>
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>待锁库:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>在PDA中"锁库高价值"和"锁库低价值&新件"两个站点待锁库的零件。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(105)">
                {{ toBeLocked }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>待客户提供无LTDate:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>未填写客户虚拟po的LTdate，或LTdate过期。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(121)">{{ noLTDate }}</div>
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>消耗件:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>模块"用料申请"的"存货分类"为指定的13种存货分类。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="flex justify-center divide-x-2">
                <div
                  :title="labelMap[117]"
                  class="pr-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(117)"
                >
                  {{ notIssued }}
                </div>
                <div
                  :title="labelMap[106]"
                  class="pl-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(106)"
                >
                  {{ consumables }}
                </div>
              </div>
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>EKD计算中:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>处于正在计算站点和零件EKD的过程中的零件，最长不超过30分钟。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <!-- 可点击的数字,并带下划线 -->
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(101)">
                {{ partsNotEnteredSupplyChain }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span>待采购:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>
                        发起用料申请后，转入模块"待采购管理"准备发起采购订单，在PDA中的"采购高价值"和"采购低价值"两个站点能查到对应的零件。
                      </span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(111)">{{ laDate}}</div>
            </el-descriptions-item>
            <el-descriptions-item label="背板:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(110)">
                {{ backupBoard }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="F2 EDD过期:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(123)">
                {{ f2EddExpired }}
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="Keep Missing:" align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span class="cursor-not-allowed text-stone-400">Keep Missing:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>模块"进厂缺件客户确认"给出"市场决定"为"Keep Missing"。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(107)">{{ hardWare }}</div>
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span class="cursor-not-allowed text-stone-400">待CSM确认(锁库):</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>用料需求发起后，转入csm确认的待处理零件，在PDA的"CSM确认"站点能查到对应待处理零件。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(116)">{{ csmOthers}}</div>
            </el-descriptions-item>

            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span class="cursor-not-allowed text-stone-400">无PN号:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>模块"集件零件管理"中"集件件号"为空白的零件。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(119)">
                {{ noPNNumber }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span class="cursor-not-allowed text-stone-400">试车借件:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>模块"进厂缺件客户确认"给出"市场决定"为"Test Only by SSAMC provide"。</span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="flex justify-center divide-x-2">
                <div
                  :title="labelMap[127]"
                  class="pr-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(127)"
                >
                  {{ testNo }}
                </div>
                <div
                  :title="labelMap[112]"
                  class="pl-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(112)"
                >
                  {{ testLoan }}
                </div>
              </div>
              <!-- <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(112)">{{ testLoan}} </div>-->
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span class="cursor-not-allowed text-stone-400">待构型确认:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>
                        来自模块"进场缺件影响试车确认"、"进厂缺件工程确认"、"构型管理"和"现场串件"中的"审批步骤"为"工程审核"。
                      </span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(104)">
                {{ engineeringConfirmation }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item align="center">
              <template #label>
                <div class="flex items-center justify-center gap-2">
                  <span class="cursor-not-allowed text-stone-400">串件中:</span>
                  <el-tooltip placement="top" effect="dark">
                    <template #content>
                      <span>
                        模块"集件零件管理"中发生串件的零件，在跟踪供体零件的供应链过程中发生中断，或该零件属于虚拟零件。
                      </span>
                    </template>
                    <el-icon :size="16" color="#999"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(120)">
                {{ serialPartsException }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="默认串回:">
              <div class="hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(500)">
                {{ defaultSerialReturn }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="白转黄:">
              <div class="flex justify-around divide-x-2">
                <div
                  :title="labelMap[200]"
                  class="pr-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(200)"
                >
                  {{ pendingPnr }}
                </div>
                <div
                  :title="labelMap[300]"
                  class="px-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(300)"
                >
                  {{ convertedPnr }}
                </div>
                <div
                  :title="labelMap[400]"
                  class="pl-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(400)"
                >
                  {{ pendingScan }}
                </div>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="总量:" align="center" :span="3">
              <div class="flex justify-center divide-x-2">
                <div class="pr-2 hover:cursor-pointer hover:text-blue-300" @click="handleOpenDrawer(999)">
                  {{ total }}
                </div>
                <div
                  class="pl-2 hover:cursor-pointer hover:text-blue-300"
                  @click="handleOpenDrawer(130)"
                  title="待处置总量"
                >
                  {{ pendingTotal }}
                </div>
              </div>
            </el-descriptions-item>

            <el-descriptions-item align="center"></el-descriptions-item>
            <el-descriptions-item align="center"></el-descriptions-item>
          </el-descriptions>
        </template>
      </el-skeleton>

      <!-- 抽屉 -->
      <EngineTopMiddleDrawer
        v-if="drawerState.visible"
        v-model:visible="drawerState.visible"
        :id-wo="id"
        :type="drawerState.type"
        :title="labelMap[drawerState.type]"
      ></EngineTopMiddleDrawer>

      <WhiteToYellowDrawer
        v-if="whiteToYellowDrawerState.visible"
        v-model:visible="whiteToYellowDrawerState.visible"
        :id-wo="id"
        :type="whiteToYellowDrawerState.type"
      ></WhiteToYellowDrawer>

      <DefaultSerialReturn
        v-if="defaultSerialReturnState.visible"
        v-model:visible="defaultSerialReturnState.visible"
        :id-wo="id"
        :type="defaultSerialReturnState.type"
      ></DefaultSerialReturn>
    </div>
  `,
}
export default EngineTopMiddle
