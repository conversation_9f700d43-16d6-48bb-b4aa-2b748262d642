import { post } from '../../../../config/axios/httpReuest.js'
// 引入公共表格组件
import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
import { useApi } from '../../../hooks/useApi.js'
import ReasonRemarkDialog from '../../components/ReasonRemarkDialog.js'
import { useReasonRemark } from '../../hooks/useReasonRemark.js'
import { useCertificateInfo } from '../hooks/pointColor.js'
import GuaranteeDeliveryDialog from '../../../components/guaranteeDeliveryDialog.js'
import { useGuaranteeDelivery } from '../../../composables/useGuaranteeDelivery.js'

const { reactive, toRefs, ref, onMounted, computed, h, watch } = Vue
const { ElSwitch } = ElementPlus
const { useVModel } = VueUse
const PointColotDrawer = {
  components: {
    HtVxeTable,
    ReasonRemarkDialog,
    GuaranteeDeliveryDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    // 模拟前or模拟后
    simulationType: {
      type: String,
      default: '0',
    },
    id: {
      type: String,
      default: '',
    },
    idWo: {
      type: String,
      required: true,
    },
    filterFields: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
    isShowBtnInput: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)
    const tableRef = ref(null)
    /** 零件分类 */
    const labelMap = {
      101: 'EKD计算中',
      102: '待转包',
      103: 'CSM确认(进厂缺件)',
      104: '构型确认',
      105: '待锁库',
      106: '消耗件',
      107: 'Keep Missing',
      108: 'F4异常领料',
      109: '转包无EDD',
      110: '背板',
      111: '待采购',
      112: '试车借件',
      113: '未离开gate1',
      114: '进入供应链后消失',
      115: '修理领料',
      116: '待CSM确认(锁库)',
      117: '消耗件(未发料)',
      118: '需求关闭',
      119: '无PN号',
      120: '串件中',
      121: '待客户提供无LTDate',
      122: '未离开gate1',
      123: 'F2 EDD过期',
      124: '修理领料',
      125: '未离开gate1',
      9999: '泳道',
    }
    const defaultColumns = [
      {
        title: '原因备注',
        field: 'str_reason',
        minWidth: 200,
      },
      {
        title: 'M值',
        field: 'int_m',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'Kitting完成/站点',
        field: 'str_nodename',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        sortable: true, // 开启排序
      },
       {
        title: '检验状态',
        field: 'is_check_displayname',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        sortable: true, // 开启排序
        visible: props.type === '0',
      },
      {
        title: '分类',
        field: 'strcategory',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        visible: props.type === '0',
      },
      {
        title: '保障交付时间',
        field: 'dt_delivery',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        visible: props.type != '120',
      },
      {
        title: '目标WO',
        field: 'str_code',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标ESN',
        field: 'str_esn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台',
        field: 'str_wo_code_ori',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台 ESN',
        field: 'str_wo_esn_ori',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PN',
        field: 'str_pn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件标签',
        field: 'str_label',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件名称',
        field: 'str_part_name',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'SM',
        field: 'str_sm',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        sortable: true, // 添加排序功能
      },
      {
        title: '客户',
        field: 'str_client',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '转包供应商/采购供应商',
        field: 'str_subcontract',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件来源',
        field: 'str_class',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件类别',
        field: 'str_item_type',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'Marshalling集件关闭',
        field: 'is_close',
        minWidth: 150,
        filters: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        formatter: ({ cellValue }) => {
          return cellValue === 1 ? '是' : '否'
        },
      },
      {
        title: 'Marshalling完成日期',
        field: 'dt_close',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        sortable: true,
      },
      {
        title: 'PKP',
        field: 'id_pkp',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '机型',
        field: 'str_engine_type',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '数量',
        field: 'int_qty',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        visible: props.type == 0,
        title: '客户拿走数',
        field: 'int_missing',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },

      {
        title: 'EKD',
        field: 'dt_ekd',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        sortable: true,
      },

      {
        visible: props.type === '0',
        title: '定单号',
        field: 'str_po',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        visible: props.type === '0',
        title: '批次号',
        field: 'str_batch',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        visible: props.type === '0',
        title: '库房',
        field: 'str_stock',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        visible: props.type === '0',
        title: '是否虚拟零件',
        field: 'is_virtual',
        minWidth: 150,
        filters: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        formatter: ({ cellValue }) => {
          return cellValue === 1 ? '是' : '否'
        },
      },
      {
        visible: props.type === '0',
        title: '串件标识',
        field: 'str_se_type',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'AOG',
        minWidth: 100,
        field: 'is_aog',
        filters: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        formatter: ({ cellValue }) => {
          return cellValue === 1 ? '是' : '否'
        },
      },
    ]
    const diffColumns = [
      {
        title: 'ID',
        field: 'id',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'WO',
        field: 'str_wo',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'ESN',
        field: 'str_esn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PN',
        field: 'str_pn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件名称',
        field: 'str_name',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件条码',
        field: 'str_bcode',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件类别',
        field: 'str_item_type',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'STR-FLOW',
        field: 'str_flow',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件报表数量',
        field: 'int_offlog',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '集件零件数量',
        field: 'int_onlog',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原因备注',
        field: 'str_reason',
        minWidth: 200,
      },
    ]
    // 表格数据
    const tableState = reactive({
      data: null,
      columns: [],
      total: 0,
    })
    const isShowBtn = computed(() => {
      if (props.type === '120' || !props.isShowBtnInput) {
        return false
      } else {
        return true
      }
      // return props.type !== '120'
    })
    // * 获取表格数据
    const getTableData = async () => {
      const { id, idWo, simulationType, type, filterFields } = props
      const params = {
        ac: 'de_getpnlist_bygroup',
        id: id,
        id_wo: idWo,
        int_ekd_type: simulationType,
        int_point_type: type,
        filter_fields: filterFields,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        tableState.columns = isShowBtn.value || !props.isShowBtnInput ? [...defaultColumns] : [...diffColumns]
        tableState.data = data.data.map((item) => {
          if (props.type === '0' && item.strcategory) {
            item.strcategory = item.strcategory
              .split(',')
              .map((item) => labelMap[item])
              .join(',')
          }

          return item
        })
        if (isShowNotClose.value && isNotClose.value) {
          // 过滤掉int_no_ekd_typed的值为301，302，103的数据
          const filteredData = tableState.data.filter((item) => ![301, 302, 103].includes(item.int_no_ekd_type))
          tableState.data = filteredData
          tableState.total = filteredData.length
        } else {
          tableState.total = data.data.length
        }
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    // * 表格筛选
    const handleFilterChange = (data) => {
      tableState.total = tableRef.value.getCurrentLength()
    }
    const { markPartApi } = useApi()
    /**
     * @description 缺件出厂和F5等待的操作
     * @param {*} type
     * @param {*} int_type_sub
     * @returns
     */
    const promptBorrowPartForTest = (type, int_type_sub, selectedData) => {
      const { idWo } = props
      const idList = selectedData.map((item) => item.id_pkp)
      const checked = ref(false)
      ElementPlus.ElMessageBox({
        title: '提示',
        message: () =>
          h('div', [
            h('div', '是否确认提交？'),
            h('div', { class: 'flex items-center justify-center' }, [
              h('span', '是否借件试车？'),
              h(ElSwitch, {
                style: {
                  '--el-switch-on-color': '#13ce66',
                  '--el-switch-off-color': '#ff4949',
                },
                modelValue: checked.value,
                'onUpdate:modelValue': (val) => {
                  checked.value = val
                },
                inlinePrompt: true,
                activeText: '是',
                inactiveText: '否',
              }),
            ]),
          ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const str_test = checked.value ? '1' : '0'
        // 提交
        markPartApi(idWo, type, idList, 0, int_type_sub, str_test).then(() => {
          emit('submit', idWo)
        })
      })
    }

    // * 可缺件
    const handleCanLack = (type, int_type_sub) => {
      const selectedData = tableRef.value.getSelectedData()
      // 提醒
      if (selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请选择数据')
        return
      }
      if (type === '20' || type === '30' || type === '40') {
        promptBorrowPartForTest(type, int_type_sub, selectedData)
        return
      }
      // 是否确定提交
      ElementPlus.ElMessageBox.confirm('是否确定提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 提交
        const { idWo } = props
        const idList = selectedData.map((item) => item.id_pkp)
        markPartApi(idWo, type, idList, 0, int_type_sub).then(() => {
          emit('submit', idWo)
        })
      })
    }
    // -------- 资源方案 --------
    const resourcePlanRef = ref(null)
    const resourcePlan = reactive({
      visible: false,
      tableData: null,
      tableColumns: [
        {
          title: '零件来源',
          field: 'str_source_type',
          fixed: 'left',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'EKD',
          minWidth: 120,
          field: 'dt_ekd',
          fixed: 'left',
          filters: [{ data: '' }],
          fiterRender: { name: 'FilterInput' },
        },
        {
          title: '零件标签',
          field: 'str_label',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台客户',
          field: 'str_client_sp',
          minWidth: 160,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台发动机排序',
          field: 'int_sort',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'EKD是否满足模拟',
          field: 'is_ekd_meet',
          minWidth: 160,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '是否同客户',
          field: 'is_same_client',
          minWidth: 160,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '推演占用',
          field: 'str_wo_de',
          minWidth: 160,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'Kitting完成/站点',
          field: 'str_nodename',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        // {
        //   title: '目标WO',
        //   field: 'str_wo',
        //   minWidth: 100,
        //   filters: [{ data: '' }],
        //   filterRender: { name: 'FilterInput' },
        // },
        // {
        //   title: '目标ESN',
        //   field: 'str_esn',
        //   minWidth: 100,
        //   filters: [{ data: '' }],
        //   filterRender: { name: 'FilterInput' },
        // },
        {
          title: '供体计划开始时间',
          field: 'dt_project_start',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '供体计划结束时间',
          field: 'dt_project_end',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '供体ESN',
          field: 'str_esn_sp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '供体WO',
          field: 'str_wo_sp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '初始ESN',
          field: 'str_esn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '初始WO',
          field: 'str_wo',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PN',
          field: 'str_pn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'SM',
          field: 'str_sm',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件名称',
          field: 'str_part_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件条码',
          field: 'str_bcode',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '仓库',
          field: 'str_wh_name',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '货位名称',
          field: 'str_product_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '批次',
          field: 'str_batch',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PKP',
          field: 'id_pkp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '要求证书',
          field: 'str_certificate_copy',
          minWidth: 130,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '供体零件证书',
          field: 'str_certificate',
          minWidth: 130,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '数量',
          minWidth: 150,
          field: 'int_num',
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
      ],
      total: 0,
    })
    // * 查看资源方案
    const viewResourcePlan = () => {
      // 获取当前选中的数据
      const selectedData = tableRef.value.getSelectedData()
      // 必须选中一条数据
      if (selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请选择数据')
        return
      } else if (selectedData.length > 1) {
        ElementPlus.ElMessage.warning('只能选择一条数据')
        return
      }
      const { str_pn } = selectedData[0]
      resourcePlan.visible = true
      getResourcePlanData(str_pn)
    }
    // * 获取资源方案数据
    const getResourcePlanData = async (str_pn) => {
      const { idWo } = props
      const params = {
        ac: 'de_query_resource_plan',
        id_wo: idWo,
        str_pn,
        is_all: 1,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        resourcePlan.tableData = data.data
        resourcePlan.total = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    // * 导出资源方案表格
    const exportResourcePlan = () => {
      resourcePlanRef.value.exportData()
    }

    // -------- 导出表格数据 --------
    // * 导出表格数据
    const exportTableData = () => {
      tableRef.value.exportData()
    }

    /* ---------------------- 跳转 ---------------------- */
    const jumpMap = {
      a: '集件零件管理',
      b: '用料申请',
      c: '零件报表',
      d: '转包零件跟踪',
      e: '锁库处理',
      g: '零件历程',
      h: '采购零件跟踪',
    }
    const jumpMapId = {
      a: '1435766996880461825',
      b: '1453234319950618624',
      c: '1435766981386702849',
      d: '1435766992040235009',
      e: '1453282700483895296',
      g: '1826524987646808064',
      h: '1435766977628606465',
    }
    const idKeyMap = {
      a: 'id_pkp',
      c: 'id_offlog',
      b: 'id_apply',
      d: 'id_po_sub_sc',
      e: 'id_apply',
      g: 'id_pkp',
      h: 'id_po_sub',
    }
    const handleJump = (command) => {
      // 没有选中数据
      if (!tableRef.value.getSelectedData().length) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }
      const idList =
        tableRef.value
          .getSelectedData()
          .map((item) => item[idKeyMap[command]])
          .filter((item) => item !== null)
          .join(',') ?? ''
      if (idList === '') {
        ElementPlus.ElMessage.warning(`无${jumpMap[command]}信息`)
        return
      }
      const getUrl = (command, idList) => {
        const baseUrl = `/Page/?moduleid=${jumpMapId[command]}`
        return command === 'e' ? `${baseUrl}&qrc_id_main=${idList}` : `${baseUrl}&qrc_id=${idList}`
      }

      com.refreshTab('*' + jumpMap[command], getUrl(command, idList))
    }

    const { reasonRemark, openReasonRemark, saveReasonRemark, closeReasonRemark } = useReasonRemark(tableRef)
    const {
      certificateInfoVisible,
      openCertificateInfo,
      closeCertificateInfo,
      certificateTableData,
      certificateLoading,
      getCertificateTableData,
    } = useCertificateInfo(tableRef)

    const saveAog = async () => {
      const selectedData = tableRef.value.getSelectedData().map((item) => item.id_pkp)

      // if (selectedData.length === 0) {
      //   ElementPlus.ElMessage.warning('请先选择数据')
      //   return
      // }
      // if (selectedData.length > 1) {
      //   ElementPlus.ElMessage.warning('只能选择一条数据')
      //   return
      // }

      ElementPlus.ElMessageBox.confirm('标记或取消AOG?', 'Warning', {
        confirmButtonText: '标记AOG',
        cancelButtonText: '取消AOG',
        type: 'warning',
      })
        .then(() => {
          const params = {
            ac: 'de_saveaog',
            is_aog: 1,
            id_pkp: selectedData,
          }
          post(params).then((res) => {
            if (res.data.code === 'success') {
              ElementPlus.ElMessage({
                type: 'success',
                message: '标记AOG成功',
              })
              tableRef.value.getSelectedData().map((item) => (item.is_aog = 1))
              tableRef.value.clearCheckboxRow()
            } else {
              ElementPlus.ElMessage({
                type: 'error',
                message: '标记AOG失败',
              })
            }
          })
        })
        .catch(() => {
          const params = {
            ac: 'de_saveaog',
            is_aog: 0,
            id_pkp: selectedData,
          }
          post(params).then((res) => {
            if (res.data.code === 'success') {
              ElementPlus.ElMessage({
                type: 'success',
                message: '取消标记AOG成功',
              })
              tableRef.value.getSelectedData().map((item) => (item.is_aog = 0))
              tableRef.value.clearCheckboxRow()
            } else {
              ElementPlus.ElMessage({
                type: 'error',
                message: '标记AOG失败',
              })
            }
          })
        })
    }

    const { guaranteeDelivery, handleEnsureDelivery, closeGuaranteeDelivery } = useGuaranteeDelivery(props, emit)

    const disabledDate = ref(null)
    const handleDelivery = () => {
      const selectedSingleData = tableRef.value.getSelectedData()[0]
      const selectedData = tableRef.value.getSelectedData().map((item) => item.id_pkp)

      disabledDate.value = moment(selectedSingleData.dt_delivery_limit).format('YYYY-MM-DD')
      if (selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }
      if (selectedData.length > 1) {
        ElementPlus.ElMessage.warning('只能选择一条数据')
        return
      }
      guaranteeDelivery.visible = true
      guaranteeDelivery.form.id_pkp = selectedData
    }

    const isShowNotClose = ref(true)
    // 是否未关闭
    const isNotClose = computed(() => {
      return props.type === '150'
    })

    const handleShowNotClose = () => {
      loading.value = true
      getTableData().finally(() => {
        loading.value = false
      })
    }

    const loading = ref(false)

    onMounted(() => {
      getTableData()
    })
    return {
      visible,
      tableRef,
      loading,
      ...toRefs(tableState),
      handleFilterChange,
      handleCanLack,
      exportTableData,
      resourcePlanRef,
      resourcePlan,
      viewResourcePlan,
      exportResourcePlan,
      handleJump,
      isShowBtn,
      reasonRemark,
      openReasonRemark,
      saveReasonRemark,
      closeReasonRemark,
      certificateInfoVisible,
      openCertificateInfo,
      closeCertificateInfo,
      certificateTableData,
      certificateLoading,
      getCertificateTableData,
      saveAog,
      handleDelivery,
      guaranteeDelivery,
      handleEnsureDelivery,
      closeGuaranteeDelivery,
      disabledDate,
      isNotClose,
      isShowNotClose,
      handleShowNotClose,
    }
  },
  template: /*html*/ `
    <el-drawer
      class="my_drawer"
      v-model="visible"
      size="80%"
      :show-close="false"
      destroy-on-close
      :modal="false"
      :z-index="1"
    >
      <template #title>
        <div class="flex items-center justify-between">
          <div class="text-white">{{ title }}零件清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <div class="flex items-center justify-between">
          <div class="flex flex-wrap items-center">
            <!-- <el-button v-if="isShowBtn" color="#fed047" @click="handleCanLack('10')">缺件出厂</el-button> -->
            <el-button v-if="isShowBtn" color="#16a34a">同意串</el-button>
            <el-button v-if="isShowBtn" color="#d8b4fe" @click="handleCanLack('20')">F5等待</el-button>
            <el-button v-if="isShowBtn" color="#fca5a5" @click="handleCanLack('30')">退客户</el-button>
            <el-button v-if="isShowBtn" color="#86efac" @click="handleCanLack('40','401')">退客户库</el-button>
            <el-button v-if="isShowBtn" color="#86efac" @click="handleCanLack('40','402')">退黄标签库</el-button>
            <el-button v-if="isShowBtn" color="#86efac" @click="handleCanLack('40','403')">退SSAMC库</el-button>
            <el-button v-if="isShowBtn" color="#86efac" @click="handleCanLack('50')">待串件</el-button>
            <el-button v-if="isShowBtn" color="#86efac" @click="viewResourcePlan">查看资源方案</el-button>
            <el-button v-if="isShowBtn" type="primary" @click="exportTableData">导出</el-button>

            <el-dropdown v-if="isShowBtn" class="ml-4" @command="handleJump">
              <el-button type="primary">
                跳转
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="a">集件零件管理（On-log）</el-dropdown-item>
                  <el-dropdown-item command="b">用料申请</el-dropdown-item>
                  <el-dropdown-item command="c">零件报表（Off-log）</el-dropdown-item>
                  <el-dropdown-item command="d">转包跟踪</el-dropdown-item>
                  <el-dropdown-item command="e">锁库处理</el-dropdown-item>
                  <el-dropdown-item command="g">零件历程</el-dropdown-item>
                  <el-dropdown-item command="h">采购_厂家交付</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button class="ml-4" type="primary" @click="openReasonRemark(0)">原因备注</el-button>
            <el-button class="ml-4" type="primary" @click="openCertificateInfo">证书信息</el-button>
            <el-button class="ml-4" type="primary" @click="saveAog">AOG</el-button>
            <el-button class="ml-4" type="primary" @click="handleDelivery">保证交付</el-button>
            <!-- 开关按钮 -->
            <el-switch v-if="isNotClose" v-model="isShowNotClose" @change="handleShowNotClose" />
          </div>
          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable
          v-loading="loading"
          ref="tableRef"
          :tableData="data"
          :tableColumns="columns"
          :isShowHeaderCheckbox="true"
          @filterChange="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column v-if="isShowBtn" type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
    <el-drawer class="my_drawer" v-model="resourcePlan.visible" size="80%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex items-center justify-between">
          <div class="text-white">资源方案</div>
          <el-button type="danger" @click="resourcePlan.visible = false">关闭</el-button>
        </div>
      </template>
      <div class="flex items-center justify-between">
        <el-button type="primary" @click="exportResourcePlan">导出</el-button>
        <div class="text-black">共计：{{ resourcePlan.total || 0 }} 条</div>
      </div>
      <div style="height: calc(100% - 50px)">
        <HtVxeTable
          ref="resourcePlanRef"
          :tableData="resourcePlan.tableData"
          :tableColumns="resourcePlan.tableColumns"
        ></HtVxeTable>
      </div>
    </el-drawer>
    <ReasonRemarkDialog
      v-model="reasonRemark.visible"
      :form="reasonRemark.form"
      @saveReasonRemark="saveReasonRemark"
      @closeReasonRemark="closeReasonRemark"
    />
    <!-- 证书信息 -->
    <el-dialog class="my-dialog" v-model="certificateInfoVisible" title="证书信息" width="60%" :show-close="false">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="text-white">证书信息</div>
          <el-button size="small" type="danger" @click="closeCertificateInfo">关闭</el-button>
        </div>
      </template>
      <div class="m-4">
        <el-table :data="certificateTableData" border height="500" v-loading="certificateLoading">
          <el-table-column label="属性信息" prop="property">
            <template #header>
              <div class="font-bold text-black">属性信息</div>
            </template>
            <template #default="scope">
              <div class="text-black">{{ scope.row.property }}</div>
            </template>
          </el-table-column>
          <el-table-column label="目标发动机" prop="value1">
            <template #header>
              <div class="font-bold text-black">目标发动机</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    {{ disabledDate }}
    <GuaranteeDeliveryDialog
      v-model:visible="guaranteeDelivery.visible"
      v-model:form="guaranteeDelivery.form"
      :disabled-date="disabledDate"
      @confirm="handleEnsureDelivery"
      @cancel="closeGuaranteeDelivery"
    />
  `,
}

export default PointColotDrawer
