/**
 * 交接接收统计模块 - 负责展示和处理交接接收统计图表 (Composition API)
 */
const ReceivingChartModule = {
  /**
   * 使用Composition API创建图表逻辑
   * @returns {Object} 图表相关的状态和方法
   */
  setup() {
    // 图表实例
    const chartInstance = Vue.ref(null)
    const chartContainer = Vue.ref(null)

    // 加载状态
    const loading = Vue.ref(false)

    // 图表数据
    const chartData = Vue.reactive({
      received: 0,
      unreceived: 0,
      total: 0,
      details: {},
    })

    // 视窗调整处理函数
    const resizeHandler = Vue.ref(null)

    /**
     * 初始化图表
     */
    const initChart = () => {
      // 确保Vue已经挂载DOM元素
      if (!chartContainer.value) {
        console.error('Chart container not found')
        return null
      }

      // 初始化ECharts实例
      chartInstance.value = echarts.init(chartContainer.value)

      // 设置图表响应式
      resizeHandler.value = () => {
        chartInstance.value && chartInstance.value.resize()
      }

      window.addEventListener('resize', resizeHandler.value)

      return chartInstance.value
    }

    /**
     * 清理事件监听
     */
    const cleanup = () => {
      if (resizeHandler.value) {
        window.removeEventListener('resize', resizeHandler.value)
      }
      if (chartInstance.value) {
        chartInstance.value.dispose()
        chartInstance.value = null
      }
    }

    /**
     * 更新图表数据
     * @param {Function} clickCallback 点击回调
     */
    const updateChart = (clickCallback) => {
      if (!chartInstance.value) {
        console.error('Chart not initialized')
        return
      }

      // 确保容器可见再设置选项
      if (chartContainer.value.offsetHeight === 0 || chartContainer.value.offsetWidth === 0) {
        setTimeout(() => updateChart(clickCallback), 100)
        return
      }

      // 准备图表配置
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'horizontal',
          bottom: '0',
          data: ['已接收', '未接收'],
        },
        color: ['#67C23A', '#F56C6C'],
        series: [
          {
            name: '交接接收统计',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 6,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c}项 ({d}%)',
              fontSize: 14,
              fontWeight: 'bold',
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 10,
              smooth: true,
            },
            data: [
              { value: chartData.received || 0, name: '已接收' },
              { value: chartData.unreceived || 0, name: '未接收' },
            ],
            // 在饼图中间显示总数
            emphasis: {
              label: {
                show: false,
              },
            },
          },
          {
            name: '总计',
            type: 'pie',
            radius: '50%',
            z: 1,
            itemStyle: {
              color: 'rgba(250, 250, 250, 0.3)',
              borderColor: '#f5f5f5',
              borderWidth: 2,
            },
            label: {
              position: 'center',
              formatter: () => {
                return ['{total|' + chartData.total + '}', '{label|总项目数}'].join('\n')
              },
              rich: {
                total: {
                  fontSize: 28,
                  fontWeight: 'bold',
                  color: '#333',
                  padding: [5, 0],
                },
                label: {
                  fontSize: 14,
                  color: '#999',
                  padding: [5, 0],
                },
              },
            },
            tooltip: {
              show: false,
            },
            data: [{ value: 1, name: '总计' }],
          },
        ],
      }

      // 应用配置
      chartInstance.value.setOption(option)

      // 强制重新调整大小以适应容器
      chartInstance.value.resize()

      // 添加点击事件
      if (typeof clickCallback === 'function') {
        chartInstance.value.off('click')
        chartInstance.value.on('click', (params) => {
          // 仅在点击外环时触发
          if (params.seriesIndex === 0) {
            console.log('Receiving chart clicked:', params.name) // 调试日志
            clickCallback(params.name)
          }
        })
      }
    }

    /**
     * 获取接收统计数据
     * @param {Object} filters 筛选条件
     * @returns {Promise<Object>} 统计数据
     */
    const fetchData = async (filters = {}) => {
      const { getHandoverStatistics } = await import('../../api/index.js')
      loading.value = true

      try {
        const res = await getHandoverStatistics({
          dtRange: filters.dateRange,
          charType: '交接接收',
          strType: filters.businessTypes,
        })
        // res 是一个数组，数组中包含两个对象，每个对象中包含两个属性，分别是 name 和 count
        // 需要将 res 转换为两个对象，分别是 submitted 和 unsubmitted
        const received = res.find((item) => item.name === '已接收')
        const unreceived = res.find((item) => item.name === '未接收')
        const total = res.find((item) => item.name === '总数')

        // 模拟的响应数据
        const mockData = {
          received: received.count,
          unreceived: unreceived.count,
          total: total.count,
        }

        // 更新图表数据
        Object.assign(chartData, mockData)

        return mockData
      } catch (error) {
        console.error('Failed to fetch submission data:', error)
        throw error
      } finally {
        loading.value = false
      }
    }
    // 返回组合式API的结果
    return {
      chartInstance,
      chartContainer,
      chartData,
      loading,
      initChart,
      updateChart,
      fetchData,
      cleanup,
    }
  },

  /**
   * 创建接收统计图表组件
   * @param {Object} props 组件属性
   * @returns {Object} 组件配置
   */
  createChartComponent(props = {}) {
    return {
      name: 'ReceivingChartComponent',
      props: {
        filters: {
          type: Object,
          default: () => ({}),
        },
        onChartClick: {
          type: Function,
          default: () => {},
        },
        onDataUpdate: {
          type: Function,
          default: () => {},
        },
        ...props,
      },
      setup(props) {
        const { chartInstance, chartContainer, chartData, loading, initChart, updateChart, fetchData, cleanup } =
          ReceivingChartModule.setup()

        // 图表容器元素引用
        const chartWrapperRef = Vue.ref(null)

        // 创建图表后初始化
        Vue.onMounted(() => {
          // 初始化图表
          setTimeout(() => {
            initChart()
            // 加载初始数据
            loadData()
          }, 100) // 稍微延迟以确保DOM已渲染
        })

        // 组件卸载时清理资源
        Vue.onUnmounted(() => {
          cleanup()
        })

        // 使用ResizeObserver监听容器大小变化
        Vue.onMounted(() => {
          if (window.ResizeObserver && chartContainer.value) {
            const resizeObserver = new ResizeObserver(() => {
              // 使用作用域内的chartInstance变量
              if (chartInstance.value) {
                chartInstance.value.resize()
              }
            })
            resizeObserver.observe(chartContainer.value)

            // 清理观察器
            Vue.onUnmounted(() => {
              resizeObserver.disconnect()
            })
          }
        })

        /**
         * 获取点击的表格数据
         * @param {string} dataType 数据类型
         */
        const getTableData = async (dataType) => {
          const { getHandoverDetail } = await import('../../api/index.js')
          const params = {
            dtRange: props.filters.dateRange,
            charType: '交接接收',
            itemType: dataType,
            strType: props.filters.businessTypes,
          }
          return await getHandoverDetail(params)
        }

        // 点击事件处理函数
        const handleClick = async (dataType) => {
          if (props.onChartClick) {
            try {
              const res = await getTableData(dataType)
              props.onChartClick({
                type: 'receiving',
                dataType,
                data: res,
              })
            } catch (error) {
              console.error('Failed to get table data:', error)
            }
          }
        }

        // 加载数据方法
        const loadData = async () => {
          try {
            await fetchData(props.filters)
            updateChart(handleClick)

            // 通知主应用数据更新
            if (props.onDataUpdate) {
              props.onDataUpdate({
                type: 'receiving',
                data: {
                  received: chartData.received,
                  unreceived: chartData.unreceived,
                  total: chartData.total,
                },
              })
            }
          } catch (error) {
            console.error('Failed to load data:', error)
          }
        }

        return {
          chartContainer,
          chartWrapperRef,
          chartData: Vue.readonly(chartData),
          loading,
          loadData,
        }
      },
      template: /*html*/ `
        <div ref="chartWrapperRef">
          <h2 class="mb-4 text-xl font-semibold text-gray-800">交接接收统计</h2>
          <div
            ref="chartContainer"
            class="echarts-container"
            v-loading="loading"
            element-loading-text="加载中..."
            element-loading-background="rgba(255, 255, 255, 0.8)"
          ></div>
        </div>
      `,
    }
  },
}
