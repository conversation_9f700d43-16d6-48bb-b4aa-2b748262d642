<!-- StringBuilder strhtml = new StringBuilder(); strhtml.Append(@' -->
<!DOCTYPE html>
<html lang='en'>

<head>
    <meta charset='UTF-8'>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title> F4-1B23汇总编辑列表 组件</title>
    <script src='../../../assets/jquery/jquery.js'></script>
    <script src='../../../assets/vue/vue.js'></script>
    <script src='../../../assets/element/index.js'></script>
    <script src='../../../assets/axios/axios.min.js'></script>
    <script src='../../../03team_new/comm/api_environment.js'></script>
    <script src='../../../assets/tools/helper.js'></script>
    <script src='../../../assets/moment/moment.min.js'></script>
    <script src='../../../assets/echarts/echarts.min.js'></script>
    <!-- 引入样式 -->
    <link rel='stylesheet' href='../../../assets/vxe-table-v3/style.css'>
    <!-- 引入脚本 -->
    <script src='../../../assets/vxe-table-v3/xe-utils.js'></script>
    <script src='../../../assets/vxe-table-v3/<EMAIL>'></script>

    <link rel='stylesheet' href='../../../assets/element/index.css'>
    <link rel='stylesheet' href='../../../assets/css/el.dialog.css'>
    <link rel='stylesheet' href='../../../assets/css/comm.self.css'>
    <link rel='stylesheet' href='../../../assets/css/shift.page.component.css'>
    <!--组件-->
    <script src='../../04summary/components/f4_1.shift.page.summary.edit.js'></script>
    <script src='../../03_team_plan/components/vg.hsg.sm.js'></script>
    <script src='../../03_team_plan/components/common.team.plan.js'></script>
    <script src='../../03_team_plan/components/common.team.plan.see.js'></script>


</head>

<body>
    <div id='app'>
        <!-- 1：任务管理 2：责任人处理 3：sms处理 4：审批管理 -->
        <y-f4-1-shift-page-summary-edit input_str_flow='F4-1'  :is_edit_page='false' input_group_type='B2/3' ></y-f4-1-shift-page-summary-edit>
    </div>

    <script>
        var vue1 = new Vue({
            el: '#app',
            data: function () {
                return {
                    id_main: ''
                }
            },
            methods: {
                backCall() {

                }
            }
        })
    </script>
</body>


</html>
<!-- '); arg.redata = strhtml.ToString(); return arg.redata; -->