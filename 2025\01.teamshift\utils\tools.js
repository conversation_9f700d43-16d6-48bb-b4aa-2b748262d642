/**
 * 日期格式化
 * @param {Date} date 日期
 * @param {string} format 格式
 * @returns {string} 格式化后的日期
 */
export const formatDate = (date, format) => {
  return moment(date).format(format)
}

/**
 * 数组转为字符串
 * @param {Array} arr 数组
 * @param {string} separator 分隔符
 * @returns {string} 字符串
 */
export const arrayToString = (arr, separator = ',') => {
  return arr.join(separator)
}

/**
 * 字符串转为数组
 * @param {string} str 字符串
 * @param {string} separator 分隔符
 */
export const stringToArray = (str, separator = ',') => {
  return str.split(separator)
}

/**
 * 获取当前日期的星期
 * @param {string} date 日期
 * @returns {string} 星期几
 */
export const getWeekday = (date) => {
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const weekday = moment(date).day()
  return weekdays[weekday]
}

/**
 * 是否为周末
 * @param {string} weekday 星期几
 * @returns {boolean} 是否为周末
 */
export const isWeekend = (weekday) => {
  return weekday === '星期六' || weekday === '星期日'
}

/**
 * 是否为周末
 * @param {string} date 日期
 * @returns {boolean} 是否为周末
 */
export const checkIsWeekend = (date) => {
  return moment(date).day() === 0 || moment(date).day() === 6
}
