import { post } from '../../../config/axios/httpReuest.js'

/**
 * 异常转换监控API服务
 */
export const exceptionMonitorApi = {
  /**
   * 获取异常集件缺件零件数据
   */
  async getExceptionParts() {
    try {
      const params = {
        ac: 'exception_parts_monitor',
        type: 'missing_parts'
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        return data.data
      } else {
        throw new Error(data.text || '获取异常零件数据失败')
      }
    } catch (error) {
      console.error('获取异常零件数据失败:', error)
      throw error
    }
  },

  /**
   * 获取白转黄监控数据
   * @param {string} type - 类型: pending(待转), converted(已转), scanning(扫描)
   */
  async getWhiteToYellowData(type) {
    try {
      const params = {
        ac: 'white_to_yellow_monitor',
        monitor_type: type
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        return data.data
      } else {
        throw new Error(data.text || '获取白转黄数据失败')
      }
    } catch (error) {
      console.error(`获取白转黄数据失败(${type}):`, error)
      throw error
    }
  },

  /**
   * 获取监控统计数据
   */
  async getMonitorStatistics() {
    try {
      const params = {
        ac: 'monitor_statistics',
        date: moment().format('YYYY-MM-DD')
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        return data.data
      } else {
        throw new Error(data.text || '获取统计数据失败')
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      throw error
    }
  },

  /**
   * 刷新所有监控数据
   */
  async refreshAllData() {
    try {
      const [exceptionParts, pendingData, convertedData, scanningData, statistics] = await Promise.all([
        this.getExceptionParts(),
        this.getWhiteToYellowData('pending'),
        this.getWhiteToYellowData('converted'),
        this.getWhiteToYellowData('scanning'),
        this.getMonitorStatistics()
      ])

      return {
        exceptionParts,
        whiteToYellow: {
          pending: pendingData,
          converted: convertedData,
          scanning: scanningData
        },
        statistics
      }
    } catch (error) {
      console.error('刷新数据失败:', error)
      throw error
    }
  },

  /**
   * 标记异常处理状态
   * @param {string} id - 记录ID
   * @param {string} status - 处理状态
   */
  async markExceptionStatus(id, status) {
    try {
      const params = {
        ac: 'mark_exception_status',
        id,
        status
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        return data.data
      } else {
        throw new Error(data.text || '标记状态失败')
      }
    } catch (error) {
      console.error('标记状态失败:', error)
      throw error
    }
  }
} 