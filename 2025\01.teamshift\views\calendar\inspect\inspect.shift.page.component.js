const { ref, onMounted, computed } = Vue
const { ElMessage } = ElementPlus
import {
  getTeamShiftCalendarPage,
  get_pt_get_shift,
  get_pt_get_shift_people,
  pt_save_team_shift_peolpe,
} from '../../../api/shift.calendar.team/index.js'

// 设置 moment 语言为中文
moment.locale('zh-cn')

// 定义节假日数据
const HOLIDAY_DATA = {}

// 获取节假日数据
const getHolidays = (year) => {
  const holidays = {}
  Object.entries(HOLIDAY_DATA).forEach(([date, info]) => {
    if (date.startsWith(year)) {
      holidays[date] = info
    }
  })
  return holidays
}

export default {
  name: 'InspectShiftCalendar',
  setup() {
    // 加载状态
    const loading = ref(true)

    // 节假日数据
    const holidays = ref({})

    // 当前选择的月份 - 修改初始化方式
    const currentMonth = ref(moment().format('YYYY-MM'))

    // 搜索表单
    const searchForm = ref({
      month: currentMonth.value,
      team: '',
      type: '',
    })

    // 分页相关状态
    const pagination = ref({
      currentPage: 1,
      pageSize: 10,
      total: 0,
    })

    // 添加弹框相关的状态
    const dialogVisible = ref(false)
    const selectedShift = ref(null)
    const selectedDate = ref(null)
    const selectedTeam = ref(null)
    const selectedShiftType = ref('')
    const shiftTypeOptions = ref([])

    // 模拟Team数据
    const teamMembers = ref([])

    const teamSecMembers = ref([{ id: 'SM31', name: '张三', ability: 'SM31' }])

    // 获取月份的起始日期和结束日期
    const getMonthRange = (dateStr) => {
      const momentObj = moment(dateStr, 'YYYY-MM')
      return {
        start: momentObj.clone().startOf('month'),
        end: momentObj.clone().endOf('month'),
        year: momentObj.year(),
        month: momentObj.month() + 1,
      }
    }

    // 更新节假日数据
    const updateHolidays = (year) => {
      holidays.value = getHolidays(year)
    }

    // 计算当前月的日期数组
    const dateRange = computed(() => {
      const monthRange = getMonthRange(currentMonth.value)
      const dates = []
      const current = monthRange.start.clone()
      const end = monthRange.end.clone()

      while (current.isSameOrBefore(end, 'day')) {
        const dateStr = current.format('YYYY-MM-DD')
        const holiday = holidays.value[dateStr]
        dates.push({
          date: dateStr,
          day: current.date(),
          weekday: '日一二三四五六'.charAt(current.day()),
          isWeekend: current.day() === 0 || current.day() === 6,
          isHoliday: holiday?.type === 'holiday',
          isWorkday: holiday?.type === 'workday',
          holidayName: holiday?.name,
          isToday: current.isSame(moment(), 'day'),
          weekNumber: current.week(),
        })
        current.add(1, 'day')
      }
      return dates
    })

    // 计算表头数据
    const headerData = computed(() => {
      const weeks = []
      const dates = dateRange.value
      let currentWeek = null
      let weekSpan = 0
      let weekDates = []

      dates.forEach((date, index) => {
        const momentDate = moment(date.date)
        const weekNumber = momentDate.week()

        if (currentWeek === null) {
          currentWeek = weekNumber
        }

        if (currentWeek !== weekNumber) {
          weeks.push({
            weekNumber: currentWeek,
            span: weekSpan,
            dates: [...weekDates],
          })
          weekSpan = 0
          weekDates = []
          currentWeek = weekNumber
        }

        weekSpan++
        weekDates.push(date)

        // 处理最后一周
        if (index === dates.length - 1) {
          weeks.push({
            weekNumber: currentWeek,
            span: weekSpan,
            dates: [...weekDates],
          })
        }
      })

      return {
        weeks,
        dates,
      }
    })

    // 表格数据
    const tableData = ref([])

    // 获取表格数据
    const getTableData = async () => {
      loading.value = true
      try {
        const params = {
          str_year: currentMonth.value.split('-')[0],
          str_month: currentMonth.value.split('-')[1],
          team: searchForm.value.team,
          type: searchForm.value.type,
          CurrentPage: pagination.value.currentPage,
          PageSize: pagination.value.pageSize,
        }

        // 使用模拟数据
        const response = await getTeamShiftCalendarPage(params)

        if (response.code === 200) {
          tableData.value = response.data.items.map((item, index) => ({
            id: item.id_team + item.id_sub_team,
            ...item.team,
            shifts: item.shifts,
            team: item.team,
          }))
          pagination.value.total = response.data.totalCount
          // ElMessage.success('数据加载成功')
        } else {
          throw new Error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        ElMessage.error(error.message || '数据加载失败')
      } finally {
        loading.value = false
      }
    }

    // 处理页码变化
    const handleCurrentChange = (page) => {
      pagination.value.currentPage = page
      getTableData()
    }

    // 处理每页条数变化
    const handleSizeChange = (size) => {
      pagination.value.pageSize = size
      pagination.value.currentPage = 1
      getTableData()
    }

    // 处理月份变化
    const handleMonthChange = async (date) => {
      if (!date) {
        currentMonth.value = moment().format('YYYY-MM')
      } else {
        currentMonth.value = date
      }

      const year = moment(currentMonth.value).year()
      updateHolidays(year)
      pagination.value.currentPage = 1 // 切换月份时重置页码
      await getTableData()
    }

    // 获取单元格样式
    const getCellClass = (shift, date) => {
      const classes = ['shift-cell']

      if (!shift) {
        if (moment(date).isSame(moment(), 'day')) {
          classes.push('today')
        }
        return classes
      }

      if (shift.str_shift_team.includes('早')) {
        classes.push('status-day')
      } else if (shift.str_shift_team.includes('晚')) {
        classes.push('status-night')
      } else if (shift.str_shift_team.includes('行政')) {
        classes.push('status-rest')
      }

      if (moment(date).isSame(moment(), 'day')) {
        classes.push('today')
      }

      return classes
    }

    // 获取表头样式
    const getHeaderClass = (date) => {
      if (date.isHoliday) {
        return 'holiday-header'
      }
      if (date.isWorkday) {
        return 'workday-header'
      }
      if (date.isWeekend && !date.isWorkday) {
        return 'weekend-header'
      }
      return 'table-header'
    }

    // 处理单元格点击事件
    const handleCellClick = (shift, date, team) => {
      handleshiftPeople(team.id_team, date.date)
      selectedShiftType.value = shift?.id_shift
      selectedShift.value = shift
      selectedDate.value = date
      selectedTeam.value = team
      dialogVisible.value = true
    }
    const handleShiftTypeOptions = async () => {
      const response = await get_pt_get_shift()
      shiftTypeOptions.value = response.data
    }
    const handleshiftPeople = async (id_team, dt_leave) => {
      const response = await get_pt_get_shift_people(id_team, dt_leave)
      teamMembers.value = response.data
    }
    const handShiftsInfo = (shifts, date) => {
      if (shifts && shifts.length > 0) {
        let shift = shifts.find((shift) => shift.dt_pt === date)
        if (shift) {
          return shift.str_shift_team
        } else {
          return '休'
        }
      } else {
        return '休'
      }
    }
    const handSave = async (shifts, date) => {
      dialogVisible.value = false
      const params = {
        team_shift_calendar: {
          id_team: selectedTeam.value.id_team,
          dt_pt: selectedDate.value.date,
          id_shift: selectedShiftType.value,
          str_year: selectedDate.value.date.split('-')[0],
          str_month: selectedDate.value.date.split('-')[1],
        },
        pt_leaves: teamMembers.value,
      }
      const response = await pt_save_team_shift_peolpe(params)
      if (response.code === 200) {
        ElMessage.success('保存成功')
        await getTableData()
      }
    }
    // 组件挂载时初始化数据
    onMounted(async () => {
      const year = moment().year()
      updateHolidays(year)
      await getTableData()
      await handleShiftTypeOptions()
    })

    return {
      loading,
      searchForm,
      tableData,
      headerData,
      currentMonth,
      pagination,
      getCellClass,
      getHeaderClass,
      handleMonthChange,
      handleCurrentChange,
      handleSizeChange,
      dialogVisible,
      selectedShift,
      selectedDate,
      selectedTeam,
      handleCellClick,
      selectedShiftType,
      teamMembers,
      teamSecMembers,
      shiftTypeOptions,
      handleShiftTypeOptions,
      handleshiftPeople,
      handShiftsInfo,
      handSave,
    }
  },
  template: /*html*/ `
    <div class="shift-calendar-container">
      <!-- 加载遮罩 -->
      <div v-if="loading" class="loading-mask">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span class="loading-text">加载中...</span>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="月份">
            <el-date-picker
              v-model="currentMonth"
              type="month"
              format="YYYY-MM"
              value-format="YYYY-MM"
              :clearable="false"
              :editable="false"
              placeholder="选择月份"
              @change="handleMonthChange"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 日历表格 -->
      <el-table 
        :data="tableData" 
        border 
        class="w-full"
      >
        <!-- 基本信息列 -->
        <el-table-column prop="str_team" label="Team" width="100" class-name="table-header" fixed="left" />
      
        <el-table-column prop="type" label="Type" width="100" class-name="table-header" fixed="left" />
        
        <!-- 日期列 -->
        <el-table-column align="center">
          <!-- 周表头 -->
          <template #header>
            <div class="flex flex-col w-full">
              <!-- 周信息行 -->
              <div class="flex w-full border-b border-gray-200">
                <div 
                  v-for="week in headerData.weeks" 
                  :key="'week-' + week.weekNumber"
                  class="week-header border-r border-gray-200"
                  :style="{ flex: week.span }"
                >
                  第{{ week.weekNumber }}周
                </div>
              </div>
              <!-- 日期行 -->
              <div class="flex w-full">
                <div 
                  v-for="date in headerData.dates" 
                  :key="date.date"
                  class="date-header border-r border-gray-200"
                  :class="getHeaderClass(date)"
                  style="flex: 1"
                >
                  <div>{{ date.day }} {{ date.weekday }}</div>
                  <div v-if="date.isHoliday" class="holiday-text">
                    {{ date.holidayName }}
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 数据单元格 -->
          <template #default="scope">
            <div class="flex w-full h-full">
              <div 
                v-for="date in headerData.dates" 
                :key="date.date"
                class="flex-1 px-0.5"
              >
                <div 
                  :class="getCellClass(handShiftsInfo(scope.row.shifts,date.date), date.date)"
                  @click="handleCellClick(handShiftsInfo(scope.row.shifts,date.date), date, scope.row)"
                >
                  <div class="shift-type">
                    {{ handShiftsInfo(scope.row.shifts,date.date) }}
                  </div>
                  <div class="staff-count" v-if=" handShiftsInfo(scope.row.shifts,date.date)?.str_shift_team !== '休息'">
                  
                   
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 弹框组件 -->
      <el-dialog
        v-model="dialogVisible"
        title="资源编辑"
        width="600px"
        :close-on-click-modal="false"
        class="common-dialog shift-dialog"
        :destroy-on-close="true"
      >
        <div class="p-4">
          <!-- 小组信息 -->
          <div class="section-title">小组信息</div>
          <el-form label-width="80px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="日期">
                  <div class="form-content">{{ selectedDate?.date || '' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Team">
                  <div class="form-content">{{ selectedTeam?.str_team || '' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Type">
                  <div class="form-content">{{ selectedTeam?.type || '' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="班次">
                  <el-select v-model="selectedShiftType" class="w-full">
                    <el-option  v-for="item in shiftTypeOptions"  :label="item.str_name" :value="item.id" />
                   
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <!-- Team表格 -->
          <div class="section-title">Team</div>
          <div>
            <el-table :data="teamMembers" border>
              <el-table-column prop="str_code" label="工号" width="120" />
              <el-table-column prop="str_name" label="姓名" width="100" />
              <el-table-column prop="operation" label="操作">
                <template #default="scope">
                  <el-select v-model="scope.row.str_type" class="w-full">
                    <el-option label="正常" value="0" />
                    <el-option label="请假" value="1" />
                    <el-option label="MES" value="2" />
                    <el-option label="休息" value="3" />
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </div>

        </div>
        
        <template #footer>
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handSave">保存</el-button>
        </template>
      </el-dialog>
    </div>
  `,
}
