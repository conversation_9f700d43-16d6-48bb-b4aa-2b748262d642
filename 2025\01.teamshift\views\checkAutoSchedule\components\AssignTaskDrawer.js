import { useAssignTaskDrawer } from '../composables/useAssignTaskDrawer.js'
import { getShiftList, savePersonnelOptions } from '../api/index.js'

const { ref, computed, watch, onMounted } = Vue
const { useVModel } = VueUse
const { ElMessage } = ElementPlus
const { Loading, DocumentRemove } = ElementPlusIconsVue

export default {
  name: 'AssignTaskDrawer',
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
    flow: {
      type: String,
      required: true,
    },
    enginetype: {
      type: String,
      required: true,
    },
    searchForm: {
      type: Object,
      required: true,
    },
  },
  emits: ['update:visible', 'update:searchForm', 'refresh'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)
    const searchForm = useVModel(props, 'searchForm', emit)

    const { allTasks, getMemberOptions, memberOptionsCache } = useAssignTaskDrawer(props)

    // 使用 moment 进行日期过滤和分组
    const groupedTasks = computed(() => {
      if (!searchForm.value.date || searchForm.value.date.length !== 2) {
        return []
      }
      // el-date-picker v-model 绑定的是 Date 对象，需要转换成 moment 对象进行比较
      const start = moment(searchForm.value.date[0]).startOf('day') // 确保从一天的开始比较
      const end = moment(searchForm.value.date[1]).endOf('day') // 确保到一天的结束比较

      if (!start.isValid() || !end.isValid()) {
        console.error('Invalid date range:', searchForm.value.date)
        return []
      }

      const filtered = allTasks.value.filter((task) => {
        const taskDate = moment(task.date)
        // 使用 moment().isBetween()，第三个参数 'day' 表示比较精度为天，第四个参数 '[]' 表示包含边界
        return taskDate.isValid() && taskDate.isBetween(start, end, 'day', '[]')
      })

      const groups = {}
      filtered.forEach((task) => {
        const taskDateStr = moment(task.date).format('YYYY-MM-DD') // 使用标准日期格式作为 key
        if (!groups[taskDateStr]) {
          groups[taskDateStr] = []
        }
        groups[taskDateStr].push({ ...task })
      })

      // 使用 moment 进行日期排序
      return Object.entries(groups).sort(([dateA], [dateB]) => moment(dateA).diff(moment(dateB)))
    })

    const handleSave = async () => {
      const dataToSave = groupedTasks.value.flatMap(([date, tasks]) => tasks).filter((task) => task.assignedMember)
      const params = dataToSave.map((task) => {
        return {
          id: task.id,
          id_task: task.taskId,
          id_shift: task.shiftId,
          id_skill: task.skill,
          str_shift: task.shift,
          str_skill: task.skill,
          str_task: task.task,
          str_wo: task.wo,
          staffs: task.assignedMember,
          dec_tat: task.decTat,
          str_flow: props.flow,
          str_sm: task.sm,
          int_check_type: task.checkType,
          dt_shift: task.date,
          id_wo: task.woId,
          str_group: task.str_group,
        }
      })
      await savePersonnelOptions(params)
      ElMessage.success('保存成功')
      visible.value = false
      emit('refresh')
    }

    const handleClose = () => {
      visible.value = false
    }

    // 存储每个任务行的人员选项
    const taskMemberOptions = ref({})

    // 获取特定任务的人员选项
    const getTaskMemberOptions = async (task) => {
      if (!task || !task.date || !task.skill) {
        return []
      }

      const taskId = `${task.date}_${task.skill}_${task.shiftId}`

      if (!taskMemberOptions.value[taskId]) {
        const options = await getMemberOptions(task.date, task.skill, task.shiftId)
        taskMemberOptions.value[taskId] = options
      }

      return taskMemberOptions.value[taskId] || []
    }

    const handleAssignMemberChange = (value, task) => {
      // 检查是否有cl_status为1的人员被选择
      if (value && value.length > 0) {
        const warningStaffs = value.filter((staff) => staff.cl_status === 1 || staff.cl_status === 2)
        if (warningStaffs.length > 0) {
          ElMessage({
            message: `${warningStaffs.map((staff) => staff.str_name).join('、')} 已经上班${warningStaffs.map((staff) => staff.dbl_hour).join('、')}H`,
            type: 'warning',
            zIndex: 99999,
          })
        }
      }
    }

    /**
     * 是否禁用日期
     * @param {Date} date
     */
    const isDisabledDate = (date) => {
      return moment(date).isBefore(moment(), 'day')
    }

    const shiftOptions = ref([])
    /**
     * 获取班次选项
     */
    const fetchShiftOptions = async () => {
      const { data } = await getShiftList()
      shiftOptions.value = data
    }

    /**
     * @param {Date} val
     * @param {object} row
     */
    const handleDateChange = (val, row) => {
      // 清空人员选择
      row.assignedMember = []
    }
    /**
     *
     * @param {*} val
     * @param {object} row
     */
    const handleShiftChange = (val, row) => {
      // 清空人员选项
      row.assignedMember = []
    }

    onMounted(() => {
      fetchShiftOptions()
    })

    // 监听 groupedTasks 的变化，为已分配人员的任务预加载选项
    watch(
      groupedTasks,
      (newGroupedTasks) => {
        if (newGroupedTasks && newGroupedTasks.length > 0) {
          for (const [_date, tasksInDate] of newGroupedTasks) {
            for (const task of tasksInDate) {
              // 检查任务是否已分配人员 (assignedMember 是一个 ID 数组)
              if (task.assignedMember && task.assignedMember.length > 0) {
                // 调用 getTaskMemberOptions 来加载或从缓存获取选项
                // getTaskMemberOptions 内部会处理 taskMemberOptions 的赋值
                // 无需 await，让选项在后台加载
                getTaskMemberOptions(task)
              }
            }
          }
        }
      },
      { immediate: true },
    ) // immediate: true 确保在组件初始化时也执行一次

    return {
      visible,
      searchForm,
      groupedTasks,
      memberOptionsCache,
      getTaskMemberOptions,
      taskMemberOptions,
      handleSave,
      handleClose,
      isDisabledDate,
      handleAssignMemberChange,
      shiftOptions,
      handleShiftChange,
      handleDateChange,
    }
  },
  template: /*html */ `
    <el-drawer
      v-model="visible"
      title="手动分配任务"
      size="80%"
      direction="rtl"
      :show-close="true"
      class="common-drawer flex flex-col"
      style="height: 100vh;"
    >
      <!-- 使用 flex 布局 -->
      <div class="flex flex-grow flex-col overflow-hidden p-5">
        <!-- 过滤区域 -->
        <div class="filter-section mb-5 flex-shrink-0">
          <el-form :inline="true" label-width="80px">
            <el-form-item label="*日期范围">
              <el-date-picker
                v-model="searchForm.date"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="w-[250px]"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 任务分组区域 - 允许滚动 -->
        <div v-if="groupedTasks.length === 0" class="no-data flex flex-grow items-center justify-center text-gray-500">
          暂无符合条件的数据
        </div>
        <div v-else class="task-groups flex-grow overflow-y-auto pr-2">
          <div
            v-for="([date, tasksInDate]) in groupedTasks"
            :key="date"
            class="date-group mb-6 rounded border border-gray-200 bg-gray-50 p-4"
          >
            <h3 class="date-header mb-4 mt-0 border-b border-gray-300 pb-2 text-lg font-semibold text-gray-800">
              日期: {{ date }}
            </h3>
            <el-table :data="tasksInDate" border stripe style="width: 100%">
              <el-table-column prop="wo" label="WO" width="180"></el-table-column>
              <el-table-column prop="sm" label="sm" width="180"></el-table-column>
              <el-table-column prop="task" label="任务"></el-table-column>
              <el-table-column prop="date" label="日期">
                <template #default="{row}">
                  <span v-if="row.checkType === 0">{{ row.date }}</span>
                  <el-date-picker
                    v-else
                    v-model="row.date"
                    value-format="YYYY-MM-DD"
                    :disabled-date="isDisabledDate"
                    @change="handleDateChange(val, row)"
                  ></el-date-picker>
                </template>
              </el-table-column>
              <el-table-column prop="shiftId" label="班次">
                <template #default="{ row }">
                  <span v-if="row.checkType === 0">{{ row.shift }}</span>
                  <el-select v-else v-model="row.shiftId" placeholder="请选择班次" clearable filterable class="w-full" @change="handleShiftChange(val, row)">
                    <el-option v-for="item in shiftOptions" :key="item.id" :label="item.str_name" :value="item.id" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="类型" width="120"></el-table-column>
              <el-table-column label="人员" width="250">
                <template #default="{ row }">
                  <el-select
                    v-model="row.assignedMember"
                    placeholder="请选择人员"
                    clearable
                    filterable
                    multiple
                    class="w-full"
                    value-key="id_staff"
                    @focus="getTaskMemberOptions(row)"
                    @change="(val) => handleAssignMemberChange(val, row)"
                  >
                    <el-option
                      v-for="item in taskMemberOptions[row.date + '_' + row.skill + '_' + row.shiftId] || []"
                      :key="item.value.id_staff"
                      :label="item.label"
                      :value="item.value"
                    >
                      <span
                        :class="{'text-yellow-500': item.value.cl_status === 1, 'text-red-500': item.value.cl_status === 2}"
                      >
                        {{ item.value.str_name }}({{ item.value.dbl_hour }}H)
                        <!-- <span v-if="item.value.cl_status === 1" class="text-xs ml-1">(已上班120H)</span> -->
                        <!-- <span v-if="item.value.cl_status === 2" class="text-xs ml-1">(不可选)</span> -->
                      </span>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <template #footer>
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-drawer>
  `,
}
