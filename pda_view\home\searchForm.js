const { ref, reactive } = Vue;
export default {
  name: 'SearchForm',
  emits: ['search'],
  setup(props, { attrs, emit }) {
    const collapseStatus = ref(true);
    const searchForm = reactive({
      // is_wip: '1',
      // id_engine_type: 'cfm56',
      is_positive: 0
    });
    const handleSearchClick = () => {
      emit('search', searchForm);
    };

    return {
      collapseStatus,
      searchForm,
      handleSearchClick,
    };
  },
  // language=HTML
  template: `
    <vxe-form 
      v-model:collapseStatus="collapseStatus" 
      :data="searchForm" 
      custom-layout
      title-align="right"
      title-width="90px"
    >
      <vxe-form-item title="WIP:">
        <template #default="{data}">
          <el-select v-model.trim="data.is_wip" clearable style="width: 210px;">
            <el-option label="All" value="all"></el-option>
            <el-option label="Yes" value="1"></el-option>
            <el-option label="No" value="0"></el-option>
          </el-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Engine Type:">
        <template #default="{data}">
          <el-select v-model.trim="data.id_engine_type" clearable style="width: 210px;">
            <el-option label="All" value="all"></el-option>
            <el-option label="CFM56" value="cfm56"></el-option>
            <el-option label="LEAP" value="leap"></el-option>
          </el-select>
        </template>
      </vxe-form-item>

      <vxe-form-item title="ESN:" folding>
        <template #default="{data}">
          <el-input v-model.trim="data.str_esn" clearable style="width: 210px;"></el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="WO:" folding>
        <template #default="{data}">
          <el-input v-model.trim="data.str_wo" clearable style="width: 210px;"></el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="SM:" folding>
        <template #default="{data}">
          <el-input v-model.trim="data.str_sm" clearable style="width: 210px;"></el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="PN:" folding>
        <template #default="{data}">
          <el-input v-model.trim="data.str_pn" clearable style="width: 210px;"></el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="F1-2 Closed:" folding>
        <template #default="{data}">
          <el-select v-model.trim="data.int_f12close" clearable style="width: 210px;">
            <el-option label="All" value="all"></el-option>
            <el-option label="Yes" value="1"></el-option>
            <el-option label="No" value="0"></el-option>
          </el-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Status:" folding>
        <template #default="{data}">
          <el-select v-model.trim="data.is_abnormal" clearable style="width: 210px;">
            <el-option label="All" value="all"></el-option>
            <el-option label="正常" value="0"></el-option>
            <el-option label="异常" value="1"></el-option>
          </el-select>
        </template>
      </vxe-form-item>

      <vxe-form-item title="AOG:">
      <template #default="{data}">
        <el-select v-model.trim="data.is_aog" clearable style="width: 210px;">
          <el-option label="Yes" value="1"></el-option>
          <el-option label="No" value="0"></el-option>
        </el-select>
      </template>
    </vxe-form-item>

      <vxe-form-item title="Release Plan Date:" folding title-width="150px">
        <template #default="{data}">
          <el-date-picker
            v-model.trim="data.dt_date"
            type="daterange"
            range-separator="to"
            start-placeholder="Start date"
            end-placeholder="End date"
            value-format="YYYY-MM-DD"
            clearable
          ></el-date-picker>
        </template>
      </vxe-form-item>

      <vxe-form-item title="P&M(<):" folding>
      <template #default="{data}">
        <el-input-number v-model.trim="data.is_positive" clearable :controls="false" style="width: 210px;"></el-input-number>
      </template>
    </vxe-form-item>
    
      <vxe-form-item align="center" collapse-node>
        <el-button type="primary" circle @click="handleSearchClick">
          <template #icon>
            <el-icon>
              <Search></Search>
            </el-icon>
          </template>
        </el-button>
        <el-button type="info" circle @click="handleSearchClick">
          <template #icon>
            <el-icon>
              <Refresh></Refresh>
            </el-icon>
          </template>
        </el-button>
      </vxe-form-item>
    </vxe-form>
  `,
};
