/* 资源日历卡片全局样式 */
.resource-calendar-card {
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  overflow: hidden;
}

/* 表格整体样式 */
.resource-calendar-card .resource-calendar-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* 表格单元格基础样式 */
.resource-calendar-card .resource-calendar-table .el-table__cell {
  padding: 8px;
  transition: all 0.2s ease;
}

/* 表格单元格悬停效果 */
.resource-calendar-card .resource-calendar-table .el-table__row:hover .el-table__cell {
  background-color: #f9fafb;
}

/* 表头样式 */
.resource-calendar-card .resource-calendar-table th.el-table__cell {
  background-color: #f5f7fa;
  height: 45px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #ebeef5;
}

/* 日期列内容 */
.resource-calendar-card .resource-calendar-table .el-table__cell .cell {
  position: relative;
  padding: 0;
  overflow: visible;
}

/* 单元格内容容器 */
.resource-calendar-card .resource-calendar-table .el-table__cell .relative {
  padding-top: 16px;
  min-height: 60px;
}

/* 任务容量卡片 - 固定在右上角 */
.task-badge {
  position: absolute;
  top: 0px;
  right: 0;
  font-size: 0.7rem;
  padding: 1px 4px;
  border-radius: 0 0 0 6px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 2; /* 降低z-index，让悬浮提示可以显示在上面 */
  margin: 0;
  min-width: 24px;
  text-align: center;
  border-width: 0 0 1px 1px;
  line-height: 1.2;
}

/* 任务项样式 */
.task-content {
  padding: 5px 6px;
  position: relative;
  z-index: 1;
}

.task-item {
  margin-bottom: 4px;
  padding: 4px 6px;
  border-radius: 4px;
  background-color: rgba(249, 250, 251, 0.95);
  border-left: 3px solid #e5e7eb;
  font-size: 0.75rem;
  line-height: 1.2;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative; /* 确保位置正确 */
  z-index: 1;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 基于任务类型设置不同的边框颜色 */
.task-item:nth-child(3n+1) {
  border-left-color: #3b82f6; /* 蓝色 */
}

.task-item:nth-child(3n+2) {
  border-left-color: #10b981; /* 绿色 */
}

.task-item:nth-child(3n+3) {
  border-left-color: #f59e0b; /* 橙色 */
}

.task-item:last-child {
  margin-bottom: 0;
}

.task-item:hover {
  background-color: #f3f4f6;
  transform: translateX(2px);
}

/* 单元体样式优化 */
.resource-calendar-card .grid-cols-2 {
  grid-template-columns: 1fr 1.2fr;
  background-color: #fcfcfc;
  padding: 3px 5px;
  border-radius: 4px;
  margin-bottom: 3px;
}

.resource-calendar-card .grid-cols-2:hover {
  background-color: #f5f7fa;
}


/* 响应式调整 */
@media (max-width: 768px) {
  .resource-calendar-card .resource-calendar-table .el-table__cell {
    padding: 5px;
  }
  
  .resource-calendar-card .resource-calendar-table .el-table__cell .relative {
    padding-top: 14px;
    min-height: 50px;
  }
  
  .task-badge {
    top: 0;
    right: 0;
    padding: 1px 3px;
    font-size: 0.65rem;
    min-width: 20px;
    border-radius: 0 0 0 5px;
  }
  
  .task-content {
    padding: 3px 4px;
  }
  
  .task-item {
    padding: 3px 4px;
    margin-bottom: 2px;
  }
}
