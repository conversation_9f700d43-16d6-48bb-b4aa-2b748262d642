import { post } from '../../utils/request.js'
/**获取小组班次日历 */
export function getTeamShiftCalendarPage(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_getstaff_calendarpage',
    postData: postData
  })
}

/**获取Team信息 */
export function queryTeam(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_shift_calendar_page',
    postData
  })
}
/**获取班次信息 */
export function get_pt_get_shift() {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_shift',

  })

 
}

export function get_pt_get_shift_people(id_team, dt_leave) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_team_peolpe',
    id_team: id_team,
    dt_leave: dt_leave

  })
}
/**保存team 版次日历及人员状态信息 */
export function pt_save_team_shift_peolpe(postdata) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_team_shift_peolpe',
    postdata: postdata

  })
}
/**人员班次状态信息 */
export function pt_save_staffshift(postdata,str_dept) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_staffshift',
    postData: postdata,
    str_dept: str_dept

  })
}
