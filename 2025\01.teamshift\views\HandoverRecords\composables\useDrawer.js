import { getHandoverDetailList } from '../api/index.js'
const { ref } = Vue
// 抽屉管理
export function useDrawer(searchForm) {
  const drawerVisible = ref(false)

  const drawerTableData = ref([])

  const drawerTableColumns = ref([
    {
      title: 'FLOW',
      field: 'str_flow',
      width: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: '班次',
      field: 'str_shift',
      width: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: 'WO',
      field: 'str_wo',
      width: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: 'ESN',
      field: 'str_esn',
      width: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: '机型',
      field: 'str_engine_type',
      width: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: 'TYPE',
      field: 'str_type',
      width: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: '任务',
      field: 'str_task',
      width: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: '交接人',
      field: 'str_receiver',
      width: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      type: 'html',
      title: '是否交',
      field: 'is_submitted',
      width: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
      formatter: ({ row }) => {
        return row.is_submitted === '是'
          ? `<span class="text-green-500">${row.is_submitted}</span>`
          : `<span class="text-red-500">${row.is_submitted}</span>`
      },
    },
    {
      type: 'html',
      title: '是否接',
      field: 'is_received',
      width: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
      formatter: ({ row }) => {
        return row.is_received === '是'
          ? `<span class="text-green-500">${row.is_received}</span>`
          : `<span class="text-red-500">${row.is_received}</span>`
      },
    },
  ])

  const getDrawerTableData = async (queryParams) => {
    const res = await getHandoverDetailList(queryParams)
    drawerTableData.value = res
  }

  // 打开抽屉
  const openDrawer = (data, isGroup = false) => {
    const { row, column } = data
    const queryParams = {
      dt_date: row.dt_date,
      str_flow: row.str_flow,
      str_category: column.title,
      str_engine_type: isGroup ? column.str_engine_type : '',
      str_shift: isGroup ? column.str_shift : '',
      params: {
        str_engine_type: searchForm.value.str_engine_type,
        str_type: searchForm.value.str_type,
        dt_range: searchForm.value.dt_range,
      },
    }
    drawerVisible.value = true
    getDrawerTableData(queryParams)
  }

  // 关闭抽屉
  const closeDrawer = () => {
    drawerVisible.value = false
    drawerTableData.value = []
  }

  // 保存抽屉
  const saveDrawer = () => {
    console.log('保存抽屉数据:', drawerTableData.value)
    // 这里可以添加保存逻辑
    closeDrawer()
  }

  return {
    drawerVisible,
    drawerTableData,
    drawerTableColumns,
    openDrawer,
    closeDrawer,
    saveDrawer,
  }
}
