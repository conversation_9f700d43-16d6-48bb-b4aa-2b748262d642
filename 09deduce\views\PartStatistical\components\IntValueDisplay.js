const { defineComponent } = Vue
export default defineComponent({
  name: 'IntValueDisplay',
  props: {
    row: {
      type: Object,
      required: true,
    },
    getCustomColor: {
      type: Function,
      required: true,
    },
    getCustomSecondColor: {
      type: Function,
      required: true,
    },
    onInt115Click: {
      type: Function,
      required: true,
    },
    fieldFirst: {
      type: String,
    },
    fieldSecond: {
      type: String,
    },
  },
  emits: ['curstomColor', 'curstomSecondColor', 'onIntClick'],
  setup(props, { emit }) {
    const curstomColor = (row, field) => {
      return props.getCustomColor(row, field)
    }
    const curstomSecondColor = (row, field) => {
      return props.getCustomSecondColor(row, field)
    }

    const onIntClick = (row, field) => {
      emit('onIntClick', row, field)
    }
    return {
      curstomColor,
      onIntClick,
      curstomSecondColor,
    }
  },
  template: /*html*/ `
    <div class="flex items-center justify-between">
      <div v-if="row[fieldFirst] && row[fieldSecond]">
        <span
          class="hover:cursor-pointer hover:text-blue-500 hover:underline"
          :class="curstomColor(row, fieldFirst)"
          @click.stop="onIntClick(row, fieldFirst)"
        >
          {{ row[fieldFirst] }}
        </span>
        <span class="mx-1 font-medium text-black">|</span>
        <span
          class="hover:cursor-pointer hover:text-blue-500 hover:underline"
          :class="curstomColor(row, fieldSecond)"
          @click.stop="onIntClick(row, fieldSecond)"
        >
          {{ row[fieldSecond] }}
        </span>
      </div>
      <div v-else-if="row[fieldFirst]">
        <span
          class="hover:cursor-pointer hover:text-blue-500 hover:underline"
          :class="curstomColor(row, fieldFirst)"
          @click.stop="onIntClick(row, fieldFirst)"
        >
          {{ row[fieldFirst] }}
        </span>
        <span class="mx-1 font-medium text-black">|</span>
      </div>
      <div v-else-if="row[fieldSecond]">
        <span class="mx-1 font-medium text-black">|</span>
        <span
          class="hover:cursor-pointer hover:text-blue-500 hover:underline"
          :class="curstomSecondColor(row, fieldSecond)"
          @click.stop="onIntClick(row, fieldSecond)"
        >
          {{ row[fieldSecond] }}
        </span>
      </div>
    </div>
  `,
})
