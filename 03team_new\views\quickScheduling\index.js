import PagePager from '../../../components/VxePager/PagePager.js'
import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'
import CreateAndEdit from './createAndEdit.js'
const { ref, reactive, onMounted, toRefs } = Vue
const QuickScheduling = {
  components: {
    CreateAndEdit,
    HtVxeTable,
    PagePager,
  },
  setup() {
    const tableState = reactive({
      tableData: [],
      tableColumns: [
        {
          field: 'str_name',
          title: '模板名称',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          field: 'str_flow',
          title: 'Flow',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          field: 'str_type',
          title: '模板类型',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          field: 'dt_up',
          title: '创建时间',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          field: 'create_user',
          title: '创建人',
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
      ],
    })
    // 分页数据
    const pangination = reactive({
      page: 1,
      limit: 10,
      total: 0,
    })
    
    // 获取表格数据
    const getTableData = async () => {
      // 使用axios获取数据
      axios.post(globalApiUrl, {
        au: "ssamc",
        ap: "api2018",
        ak: "",
        ac: "pt_get_quick_schedul_list",
        filter:{CurrentPage: pangination.page,Pageize: pangination.limit}
    }).then((res) => {
        tableState.tableData = res.data.data.page.items
        pangination.total = res.data.data.page.totalCount
      })
    }
    // 分页
    const handlePageChange = ({ currentPage, pageSize }) => {
      pangination.page = currentPage
      pangination.limit = pageSize
      getTableData()
    }

    const tableRef = ref(null)

    // 定义创建或者编辑抽屉
    const createOrEditDrawer = reactive({
      isShow: false,
      title: '',
      currentRow: {},
    })

    // 创建
    const handleCreate = () => {
      createOrEditDrawer.isShow = true
      createOrEditDrawer.title = '创建'
      createOrEditDrawer.currentRow={}
      tableRef.value.clearAll()
    }

    // 编辑
    const handleEdit = () => {
      const selection = tableRef.value.getSelectedData()
      if (selection.length === 0) {
        ElementPlus.ElMessage.warning('请选择要编辑的数据')
        return
      }
      createOrEditDrawer.isShow = true
      createOrEditDrawer.title = '编辑'
      createOrEditDrawer.currentRow = selection[0]
    }

    // 删除
    const handleDelete = () => {
      const selection = tableRef.value.getSelectedData();
      if(selection.length === 0) {
        ElementPlus.ElMessage.warning('请选择要删除的数据')
        return
      }
      // 提示
      ElementPlus.ElMessageBox.confirm('此操作将永久删除选中的数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 删除操作
        const selection = tableRef.value.getSelectedData();
        const ids = selection.map((item) => item.id);
        // 删除选中的数据
        // tableState.tableData = tableState.tableData.filter((item) => !selection.includes(item))
        axios.post(globalApiUrl, {
          au: "ssamc",
          ap: "api2018",
          ak: "",
          ac: "pt_del_quick_schedul_template",
          ids:ids.join(",")
        }).then((res) => {
          ElementPlus.ElMessage.success('删除成功')
          getTableData()
        })
        // 提示
       
        // 重新获取数据
       
      })
    }

    onMounted(() => {
      getTableData()
    })
    return {
      tableRef,
      ...toRefs(tableState),
      pangination,
      handlePageChange,
      createOrEditDrawer,
      handleCreate,
      handleEdit,
      handleDelete,
      getTableData,
    }
  },
  template: /*html*/ `
    <!-- 操作列 -->
    <div class="flex m-2">
      <el-button type="primary" class="flex-none" @click="handleCreate">Create</el-button>
      <el-button type="success" class="flex-none ml-4" @click="handleEdit">Edit</el-button>
      <el-button type="danger" class="flex-none ml-4" @click="handleDelete">Delete</el-button>
    </div>
    <!-- 模板表格 -->
     <div style="height: calc(100vh - 170px)">
      <ht-vxe-table ref="tableRef" :tableData :tableColumns >
        <template #checkbox>
          <vxe-column fixed="left" type="checkbox" width="80"></vxe-column>
        </template>
      </ht-vxe-table>
    </div>
    <page-pager
      :currentPage="pangination.page"
      :pageSize="pangination.limit"
      :total="pangination.total"
      @pageChange="handlePageChange"
    ></page-pager>

    <!-- 创建或者编辑抽屉组件 -->
    <create-and-edit 
    v-if="createOrEditDrawer.isShow" 
    v-model:isShowDrawer="createOrEditDrawer.isShow" 
    :title="createOrEditDrawer.title" 
    :rowData="createOrEditDrawer.currentRow"
    @update="getTableData"
    />
  `,
}

export default QuickScheduling
