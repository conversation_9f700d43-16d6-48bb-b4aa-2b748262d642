const { ref, computed, watch, onMounted } = Vue
const { ElMessage } = ElementPlus
import {
  getDepartmentsOrBranches,
  queryScheduleData,
  saveScheduleData,
  getScheduleStatusApi,
} from '../../api/schedule/index.js'
import { getCurrentUserInfo, getCommonEnumList } from '../../api/comm/index.js'
export default {
  name: 'RegularSchedule',
  setup() {
    // 日历引用和当前日期
    const calendarRef = ref(null)
    const currentDate = ref(new Date())

    // 班次数据
    const shifts = ref([])

    // 部门数据
    const departments = ref([])

    // 数据加载状态
    const loading = ref(false)

    // 是否有数据标志
    const hasData = ref(false)

    // 人员状态ENUM
    const staffStatusEnum = ref([])

    const fetchStaffStatusEnum = async () => {
      const response = await getCommonEnumList('pt_staff_status')
      // 过滤掉str_code为'0'的
      staffStatusEnum.value = response.filter((item) => item.str_code !== '0')
    }

    onMounted(async () => {
      await fetchStaffStatusEnum()
      initStaffStatus()
    })

    // 人员状态区域数据
    const staffStatus = ref([])

    // 初始化人员状态区域数据
    const initStaffStatus = () => {
      // 定义状态类型与样式的映射
      const statusStyleMap = {
        4: { type: 'warning', color: 'var(--el-color-warning-light-9)' }, // 体检
        1: { type: 'info', color: 'var(--el-color-info-light-9)' }, // 休息
        2: { type: 'success', color: 'var(--el-color-success-light-9)' }, // 培训
        5: { type: 'primary', color: 'var(--el-color-primary-light-9)' }, // 出差
        3: { type: 'danger', color: 'var(--el-color-danger-light-9)' }, // MES
      }

      staffStatus.value = staffStatusEnum.value.map((item) => {
        // 获取对应的样式，如果没有则使用默认样式
        const style = statusStyleMap[item.str_code] || { type: 'info', color: 'var(--el-color-info-light-9)' }

        return {
          int_type: item.str_code,
          str_type: item.str_name,
          type: style.type,
          color: style.color,
          staffs: [], // 使用staffs替代list
        }
      })
    }

    // 获取部门数据
    const getDepartments = async () => {
      try {
        const response = await getDepartmentsOrBranches({ isBranch: 'false' })
        departments.value = response.data

        // 获取当前登录用户信息
        await getCurrentUserDepartment()
      } catch (error) {
        ElMessage.error('获取部门数据失败')
      }
    }

    const isBranchDisabled = ref(false)
    // 获取当前登录用户部门
    const getCurrentUserDepartment = async () => {
      try {
        loading.value = true
        const response = await getCurrentUserInfo()
        if (response) {
          // 设置当前用户的部门ID
          selectedDepartment.value = response.id_dept_new
          selectedBranch.value = response.id_sec_new
          // 获取该部门下的分部
          await handleDepartmentChange(response.id_dept_new)
          // 设置分部是否禁用
          isBranchDisabled.value = response.id_sec_new ? true : false
          // 自动获取排班数据
          await getScheduleData()
        }
      } catch (error) {
        ElMessage.error('获取当前用户部门信息失败')
      } finally {
        loading.value = false
      }
    }

    const scheduleStatus = ref([])
    /**
     * 获取排班状态
     */
    const getScheduleStatus = async () => {
      try {
        const int_year = moment(selectDate.value).year()
        const int_month = moment(selectDate.value).month() + 1
        const response = await getScheduleStatusApi({
          id_dept: selectedDepartment.value,
          id_sec: selectedBranch.value,
          int_year: int_year,
          int_month: int_month,
        })
        scheduleStatus.value = response || []
      } catch (error) {
        console.error('获取排班状态失败:', error)
        scheduleStatus.value = []
      }
    }

    onMounted(() => {
      getDepartments()
    })

    // 当前选中的部门和分部
    const selectedDepartment = ref(null)
    const selectedBranch = ref(null)

    // 当前可选的分部列表
    const availableBranches = ref([])

    // 监听分部变化，重置数据状态
    watch(selectedBranch, () => {
      hasData.value = false
    })

    // 处理部门变化
    const handleDepartmentChange = async (value) => {
      if (!value) return // 如果没有值，早期返回
      try {
        loading.value = true
        const response = await getDepartmentsOrBranches({ isbranch: 'true', id_top: value })
        availableBranches.value = response.data
        getScheduleStatus()
      } catch (error) {
        ElMessage.error('获取分部数据失败')
      } finally {
        loading.value = false
      }
    }

    // 处理分部变化
    const handleBranchChange = async (value) => {
      console.log('选择的分部ID:', value)
      if (value && selectedDepartment.value) {
        await getScheduleStatus()
      }
    }

    // 待排班人员
    const waitingStaff = ref([])

    // 拖拽开始处理
    const handleDragStart = (event, item, source) => {
      event.dataTransfer.setData(
        'text/plain',
        JSON.stringify({
          item,
          source,
        }),
      )
    }

    // 拖拽放置处理
    const handleDrop = async (event, target) => {
      event.preventDefault()
      const data = JSON.parse(event.dataTransfer.getData('text/plain'))
      const { item, source } = data

      if (target === 'waiting') {
        await handleWaitingDrop(item, source)
      } else if (typeof target === 'string' && target.startsWith('status_')) {
        // 处理人员状态区域的拖拽
        const statusType = target.split('_')[1]
        await handleStatusDrop(item, source, statusType)
      } else {
        await handleShiftDrop(item, source, target)
      }
    }

    // 处理待排班区域的拖拽
    const handleWaitingDrop = async (item, source) => {
      if (typeof source === 'number') {
        const sourceShift = shifts.value[source]

        // 如果从行政班拖动到待排班区域，先检查是否在非体检的人员状态区域中
        if (sourceShift.int_type === 1) {
          // 检查人员是否存在于除体检外的任何状态区域中
          const existingInAnyStatus = staffStatus.value.find(
            (status) => status.int_type !== '4' && status.staffs.some((staff) => staff.id_body_s === item.id_body_s),
          )

          if (existingInAnyStatus) {
            ElMessage.warning(`${item.str_body_s || item.name} 已在${existingInAnyStatus.str_type}状态中，无法调动`)
            return
          }
        }

        const index = sourceShift.shiftList.findIndex((staff) => staff.id_body_s === item.id_body_s)
        if (index > -1) {
          sourceShift.shiftList.splice(index, 1)
          waitingStaff.value.push(item)
          ElMessage.success(`已将 ${item.str_body_s || item.name} 从 ${sourceShift.str_type} 调动至待排班区域`)
        }
      }
    }

    // 处理班次之间的拖拽
    const handleShiftDrop = async (item, source, target) => {
      const targetShift = shifts.value[target]
      if (source === 'waiting') {
        const index = waitingStaff.value.findIndex((staff) => staff.id_body_s === item.id_body_s)
        if (index > -1) {
          waitingStaff.value.splice(index, 1)
          targetShift.shiftList.push(item)
          ElMessage.success(`已将 ${item.str_body_s || item.name} 从待排班区域调动至 ${targetShift.str_type}`)
        }
      } else if (typeof source === 'number') {
        const sourceShift = shifts.value[source]

        // 如果从行政班拖动到早班或晚班，先检查是否在非体检的人员状态区域中
        if (sourceShift.int_type === 1 && (targetShift.int_type === 2 || targetShift.int_type === 3)) {
          // 检查人员是否存在于除体检外的任何状态区域中
          const existingInAnyStatus = staffStatus.value.find(
            (status) => status.int_type !== '4' && status.staffs.some((staff) => staff.id_body_s === item.id_body_s),
          )

          if (existingInAnyStatus) {
            ElMessage.warning(`${item.str_body_s || item.name} 已在${existingInAnyStatus.str_type}状态中，无法调动`)
            return
          }
        }

        const index = sourceShift.shiftList.findIndex((staff) => staff.id_body_s === item.id_body_s)
        if (index > -1) {
          sourceShift.shiftList.splice(index, 1)
          targetShift.shiftList.push(item)
          ElMessage.success(
            `已将 ${item.str_body_s || item.name} 从 ${sourceShift.str_type} 调动至 ${targetShift.str_type}`,
          )
        }
      }
    }

    // 处理人员状态区域的拖拽
    const handleStatusDrop = async (item, source, statusType) => {
      const statusItem = staffStatus.value.find((status) => status.int_type === statusType)
      if (!statusItem) return

      const adminShift = shifts.value.find((shift) => shift.int_type === 1) // 行政班

      // 首先检查人员是否已存在于任何状态区域中
      const existingInAnyStatus = staffStatus.value.find((status) =>
        status.staffs.some((staff) => staff.id_body_s === item.id_body_s),
      )

      if (existingInAnyStatus) {
        ElMessage.warning(`${item.str_body_s || item.name} 已在${existingInAnyStatus.str_type}状态中`)
        return
      }

      if (source === 'waiting') {
        // 从待排班区域拖到状态区域
        statusItem.staffs.push(item)

        // 新需求：除了体检(4)之外的任何区域，从待排班删除并且添加到行政班
        if (statusItem.int_type !== '4') {
          // 从待排班人员中删除
          const waitingIndex = waitingStaff.value.findIndex((staff) => staff.id_body_s === item.id_body_s)
          if (waitingIndex > -1) {
            waitingStaff.value.splice(waitingIndex, 1)
          }

          // 添加到行政班
          if (adminShift) {
            const adminIndex = adminShift.shiftList.findIndex((staff) => staff.id_body_s === item.id_body_s)
            if (adminIndex === -1) {
              adminShift.shiftList.push(item)
            }
          }

          ElMessage.success(
            `已将 ${item.str_body_s || item.name} 从待排班区域调动至${statusItem.str_type}状态并添加到行政班`,
          )
        } else {
          ElMessage.success(`已将 ${item.str_body_s || item.name} 添加到${statusItem.str_type}状态`)
        }
      } else if (typeof source === 'number') {
        const sourceShift = shifts.value[source]

        if (sourceShift.int_type === 1) {
          // 行政班
          // 从行政班拖到状态区域 - 不删除源，只添加到状态区域
          statusItem.staffs.push(item)
          ElMessage.success(`已将 ${item.str_body_s || item.name} 添加到${statusItem.str_type}状态`)
        } else {
          // 早班或晚班
          // 从早班或晚班拖到状态区域 - 删除源，添加到状态区域和行政班
          const index = sourceShift.shiftList.findIndex((staff) => staff.id_body_s === item.id_body_s)
          if (index > -1) {
            sourceShift.shiftList.splice(index, 1)
            statusItem.staffs.push(item)

            // 检查是否已存在于行政班
            if (adminShift) {
              const adminIndex = adminShift.shiftList.findIndex((staff) => staff.id_body_s === item.id_body_s)
              if (adminIndex === -1) {
                adminShift.shiftList.push(item)
              }
            }

            ElMessage.success(
              `已将 ${item.str_body_s || item.name} 从 ${sourceShift.str_type} 调动至${statusItem.str_type}状态`,
            )
          }
        }
      }
    }

    // 从状态区域删除人员
    const removeFromStatus = (statusType, item) => {
      const statusItem = staffStatus.value.find((status) => status.int_type === statusType)
      if (!statusItem) return

      const index = statusItem.staffs.findIndex((staff) => staff.id_body_s === item.id_body_s)
      if (index > -1) {
        statusItem.staffs.splice(index, 1)
        ElMessage.success(`已将 ${item.str_body_s || item.name} 从${statusItem.str_type}状态中移除`)
      }
    }

    // 拖拽悬停处理
    const handleDragOver = (event) => {
      event.preventDefault()
    }

    // 计算选中的日期
    const selectDate = computed(() => {
      return moment(currentDate.value).format('YYYY-MM-DD')
    })

    const mainShift = ref({})

    // 获取选中日期下排班数据
    const getScheduleData = async () => {
      shifts.value = []
      if (!selectedDepartment.value) {
        ElMessage.warning('请先选择部门')
        return
      }

      try {
        loading.value = true
        const params = {
          dt_plan: selectDate.value,
          id_dept: selectedDepartment.value,
          id_sec: selectedBranch.value,
        }
        const response = await queryScheduleData(params)

        // 处理返回的数据
        if (response && response.main) {
          mainShift.value = response.main
          waitingStaff.value = response.schedulingShift || []
          shifts.value = response.ptShift || []
          // 重置人员状态区域数据
          staffStatus.value.forEach((status) => {
            status.staffs = response.ptStaffStatuses.find((s) => s.int_type === status.int_type)?.staffs || []
          })

          hasData.value = true
          // ElMessage.success('排班数据查询成功')
        } else {
          ElMessage.warning('未找到排班数据')
          hasData.value = false
        }
      } catch (error) {
        ElMessage.error('排班数据查询失败')
        hasData.value = false
      } finally {
        loading.value = false
      }
    }

    // 查询按钮点击处理
    const handleQuery = () => {
      getScheduleData()
    }

    // 监听日期变化
    watch(currentDate, (newVal) => {
      // 先重置数据状态
      hasData.value = false

      // 如果已选择部门，则自动重新获取排班数据
      if (selectedDepartment.value) {
        usageSelectDate.value = selectDate.value
        // 添加短暂延迟，确保日期计算已完成
        setTimeout(async () => {
          await getScheduleData()
          // 同时获取排班状态
          if (selectedBranch.value) {
            await getScheduleStatus()
          }
        }, 100)
      }
    })

    const usageSelectDate = ref(selectDate.value)

    const submitLoading = ref(false)
    // 保存按钮点击处理
    const handleSave = async () => {
      submitLoading.value = true
      mainShift.value.dt_plan = selectDate.value
      mainShift.value.id_dept = selectedDepartment.value
      mainShift.value.id_sec = selectedBranch.value
      mainShift.value.int_type = '0'
      mainShift.value.dt_continue = usageSelectDate.value

      // 准备保存的数据
      const saveData = {
        main: mainShift.value,
        ptShift: shifts.value.map((shift) => ({
          ...shift,
          shiftList: shift.shiftList || [],
        })),
        schedulingShift: waitingStaff.value,
        ptStaffStatuses: staffStatus.value.map((status) => ({
          int_type: status.int_type,
          staffs: status.staffs.map((staff) => ({
            id_body_s: staff.id_body_s,
            str_body_s: staff.str_body_s,
          })),
        })),
      }

      try {
        await saveScheduleData(saveData)
        ElMessage.success('保存排班数据成功')
      } catch (error) {
        ElMessage.error('保存排班数据失败')
      } finally {
        submitLoading.value = false
      }
    }

    // 获取班次类型对应的样式
    const getShiftTypeStyle = (type) => {
      switch (type) {
        case 1: // 行政班
          return { type: 'danger', color: 'var(--el-color-danger-light-9)' }
        case 2: // 晚班
          return { type: 'warning', color: 'var(--el-color-warning-light-9)' }
        case 3: // 早班
          return { type: 'primary', color: 'var(--el-color-primary-light-9)' }
        default:
          return { type: 'info', color: 'var(--el-color-info-light-9)' }
      }
    }

    // 检查指定日期是否已排班
    const isDatePlanned = (date) => {
      if (!scheduleStatus.value || scheduleStatus.value.length === 0) {
        return false
      }
      const dateStr = moment(date).format('YYYY-MM-DD')
      const statusItem = scheduleStatus.value.find((item) => item.dt_plan === dateStr)
      return statusItem && statusItem.isPlaned === 1
    }

    const handleDisabledDate = (date) => moment(date).isBefore(moment(selectDate.value))

    return {
      calendarRef,
      currentDate,
      shifts,
      waitingStaff,
      staffStatus,
      selectDate,
      departments,
      availableBranches,
      selectedDepartment,
      selectedBranch,
      isBranchDisabled,
      loading,
      hasData,
      usageSelectDate,
      submitLoading,
      handleDisabledDate,
      handleDragStart,
      handleDrop,
      handleDragOver,
      handleDepartmentChange,
      handleBranchChange,
      handleQuery,
      handleSave,
      getShiftTypeStyle,
      removeFromStatus,
      isDatePlanned,
    }
  },
  template: /*html*/ `
    <div class="h-screen bg-gray-50">
      <!-- 顶部导航栏 -->
      <div class="bg-white p-4 shadow-md">
        <div class="container mx-auto flex flex-wrap items-center gap-4">
          <div class="text-xl font-bold text-gray-800">排班管理系统</div>
          <div class="flex-1"></div>
          <div class="flex items-center gap-4">
            <div class="flex items-center">
              <span class="mr-2 whitespace-nowrap text-gray-600">部门:</span>
              <el-select
                filterable
                v-model="selectedDepartment"
                placeholder="请选择部门"
                class="!w-40"
                disabled
                @change="handleDepartmentChange"
              >
                <el-option v-for="dept in departments" :key="dept.id" :label="dept.str_name" :value="dept.id" />
              </el-select>
            </div>
            <div class="flex items-center">
              <span class="mr-2 whitespace-nowrap text-gray-600">分部:</span>
              <el-select
                filterable
                v-model="selectedBranch"
                placeholder="请选择分部"
                class="!w-40"
                :disabled="isBranchDisabled"
                @change="handleBranchChange"
              >
                <el-option
                  v-for="branch in availableBranches"
                  :key="branch.id"
                  :label="branch.str_name"
                  :value="branch.id"
                />
              </el-select>
            </div>
            <el-button type="primary" @click="handleQuery" :loading="loading" :disabled="!selectedDepartment">
              查询排班
            </el-button>
            <el-button type="success" @click="handleSave" :disabled="!selectedDepartment" :loading="submitLoading">
              保存
            </el-button>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 - 四列布局 -->
      <div class="flex gap-4 p-4">
        <!-- 左侧日历面板 - 固定宽度 -->
        <div class="w-[350px] shrink-0">
          <el-card class="calendar-card" shadow="hover">
            <template #header>
              <div class="flex items-center justify-between border-b border-gray-100 pb-2">
                <span class="text-sm font-medium text-gray-700">排班日历</span>
                <span class="text-xs text-gray-500">{{ selectDate }}</span>
              </div>
            </template>
            <el-calendar ref="calendarRef" v-model="currentDate" class="compact-calendar !h-[calc(100vh-280px)]">
              <template #date-cell="{ data }">
                <div class="calendar-cell relative">
                  <span
                    :class="{
                    'calendar-day': true,
                    'current-month': data.isCurrentMonth,
                    'other-month': !data.isCurrentMonth,
                    'today': data.isToday,
                  }"
                  >
                    {{ data.day.split('-')[2] }}
                  </span>
                  <div v-if="isDatePlanned(data.day)" class="absolute top-0 right-0 text-xs font-bold text-green-500">
                    ✓
                  </div>
                </div>
              </template>
            </el-calendar>
          </el-card>
        </div>

        <!-- 中间排班安排区域 - 自适应宽度 -->
        <div class="min-w-0 flex-1">
          <el-card class="h-full" shadow="hover" v-loading="loading">
            <template #header>
              <div class="flex items-center justify-between">
                <div class="text-lg font-bold text-gray-800">排班安排</div>
                <div class="flex items-center gap-2">
                  <label for="usageSelectDate" class="text-gray-600">沿用至:</label>
                  <el-date-picker
                    id="usageSelectDate"
                    v-model="usageSelectDate"
                    placeholder="请选择日期"
                    class="!w-40"
                    :disabled-date="handleDisabledDate"
                  ></el-date-picker>
                </div>
                <div class="text-base text-gray-600">{{ selectDate }}</div>
              </div>
            </template>

            <!-- 暂无数据提示 -->
            <div v-if="!hasData" class="flex h-[calc(100vh-240px)] items-center justify-center">
              <el-empty description="暂无排班数据，请选择部门和分部后点击查询"></el-empty>
            </div>

            <!-- 班次列表 - 垂直排列 -->
            <div v-else class="flex h-[calc(100vh-240px)] flex-col gap-4">
              <div
                v-for="(shift, shiftIndex) in shifts"
                :key="shift.id"
                class="max-h-full flex-1 overflow-y-auto rounded-lg p-4 transition-all duration-300"
                :style="{ backgroundColor: getShiftTypeStyle(shift.int_type).color }"
              >
                <div class="mb-2 flex items-center">
                  <el-tag :type="getShiftTypeStyle(shift.int_type).type" size="large" class="mr-3">
                    {{ shift.str_type }}
                  </el-tag>
                  <div class="flex-1 border-b-2 border-gray-200"></div>
                </div>

                <div
                  class="bg-opacity-50 min-h-[150px] rounded-lg p-3"
                  @dragover.prevent
                  @drop="(event) => handleDrop(event, shiftIndex)"
                >
                  <div class="flex flex-wrap gap-2">
                    <el-tag
                      v-for="person in shift.shiftList"
                      :key="person.id_body_s"
                      :type="getShiftTypeStyle(shift.int_type).type"
                      class="cursor-move transition-all duration-200 hover:shadow-md"
                      draggable="true"
                      @dragstart="(event) => handleDragStart(event, person, shiftIndex)"
                    >
                      {{ person.str_body_s }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 人员状态区域 - 自适应宽度 -->
        <div class="min-w-0 flex-1">
          <el-card class="h-full" shadow="hover" v-loading="loading">
            <template #header>
              <div class="text-center text-lg font-bold text-gray-800">人员状态</div>
            </template>

            <!-- 暂无数据提示 -->
            <div v-if="!hasData" class="flex h-[calc(100vh-240px)] items-center justify-center">
              <el-empty description="暂无人员状态数据" :image-size="60"></el-empty>
            </div>

            <div v-else class="h-[calc(100vh-240px)] overflow-y-auto">
              <!-- 状态区域块 -->
              <div
                v-for="status in staffStatus"
                :key="status.int_type"
                class="mb-4 max-h-full flex-1 overflow-y-auto rounded-lg p-4 transition-all duration-300"
                :style="{ backgroundColor: status.color }"
              >
                <div class="mb-2 flex items-center">
                  <el-tag :type="status.type" size="large" class="mr-3">{{ status.str_type }}</el-tag>
                  <div class="flex-1 border-b-2 border-gray-200"></div>
                </div>

                <div
                  class="bg-opacity-50 min-h-[50px] rounded-lg p-3"
                  @dragover.prevent
                  @drop="(event) => handleDrop(event, 'status_' + status.int_type)"
                >
                  <div class="flex flex-wrap gap-2">
                    <el-tag
                      v-for="person in status.staffs"
                      :key="person.id_body_s"
                      :type="status.type"
                      closable
                      @close="removeFromStatus(status.int_type, person)"
                      class="transition-all duration-200 hover:shadow-md"
                    >
                      {{ person.str_body_s }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 右侧待排班人员 - 固定宽度 -->
        <div class="w-[140px] shrink-0">
          <el-card class="h-full" shadow="hover" v-loading="loading">
            <template #header>
              <div class="text-center text-lg font-bold text-gray-800">待排班人员</div>
            </template>

            <!-- 暂无数据提示 -->
            <div v-if="!hasData" class="flex h-[calc(100vh-240px)] items-center justify-center">
              <el-empty description="暂无待排班人员" :image-size="60"></el-empty>
            </div>

            <div
              v-else
              class="h-[calc(100vh-240px)] overflow-y-auto rounded-lg bg-gray-50 p-3"
              @dragover.prevent
              @drop="(event) => handleDrop(event, 'waiting')"
            >
              <div class="flex flex-col gap-2">
                <el-tag
                  v-for="staff in waitingStaff"
                  :key="staff.id_body_s"
                  class="cursor-move transition-all duration-200 hover:shadow-md"
                  effect="plain"
                  draggable="true"
                  @dragstart="(event) => handleDragStart(event, staff, 'waiting')"
                >
                  {{ staff.str_body_s }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  `,
}
