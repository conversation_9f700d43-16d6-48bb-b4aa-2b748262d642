import { fetchPersonnelOptions } from '../api/index.js'
import { getShiftList } from '../../../api/calendar/index.js'

const { defineComponent, computed, ref, watch, onMounted } = Vue

export default defineComponent({
  name: 'CheckAutoScheduleTaskEditDialog',
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: '编辑检验任务',
    },
    form: {
      type: Object,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
  },
  emits: ['update:visible', 'cancel', 'save', 'delete'], // 添加 delete 事件
  setup(props, { emit }) {
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value),
    })

    // 使用 ref 来创建 form 的本地副本，以便在对话框内部进行修改而不直接影响父组件传递的 props
    // 同时通过 watch 来同步外部 form 的变化
    const localForm = ref({})
    watch(
      () => props.form,
      (newForm) => {
        localForm.value = { ...newForm }
      },
      { deep: true, immediate: true },
    )

    const staffOptions = ref([])
    const getStaffOptions = async () => {
      const params = {
        id_skill: localForm.value.id_skill,
        dt_shift: localForm.value.dt_shift,
        id_shift: localForm.value.id_shift,
      }
      const res = await fetchPersonnelOptions(params)
      staffOptions.value = res.map((item) => {
        return {
          value: item,
          label: item.str_name,
        }
      })
    }
    /**
     * 选择日期
     */
    const handleDateChange = (val) => {
      localForm.value.staffs = []
      if (val) {
        getStaffOptions()
      }
    }

    /**
     * 班次
     */
    const shiftOptions = ref([])
    const getShiftOptions = async () => {
      const res = await getShiftList()
      shiftOptions.value = res.data.filter((item) => item.int_type === 3 || item.int_type === 2)
    }
    /**
     * 选择班次
     */
    const handleShiftChange = () => {
      localForm.value.staffs = []
      getStaffOptions()
    }

    onMounted(async () => {
      await getStaffOptions()
      await getShiftOptions()
    })

    const handleCancel = () => {
      emit('cancel')
    }

    const formRef = ref(null)
    const handleSave = () => {
      formRef.value.validate((valid) => {
        if (valid) {
          emit('save', localForm.value)
        }
      })
    }

    const handleDelete = () => {
      emit('delete', localForm.value)
    }

    const formRules = {
      dt_shift: [{ required: true, message: '请选择日期', trigger: 'change' }],
      staffs: [{ required: true, message: '请选择人员', trigger: 'change' }],
      id_shift: [{ required: true, message: '请选择班次', trigger: 'change' }],
    }

    return {
      dialogVisible,
      localForm,
      staffOptions,
      shiftOptions,
      formRules,
      formRef,
      handleCancel,
      handleSave,
      handleDelete,
      handleDateChange,
      handleShiftChange,
    }
  },
  template: /*html */ `
    <el-dialog
      v-model="dialogVisible"
      class="common-dialog"
      :title="title"
      width="550px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="localForm" label-width="100px" :rules="formRules">
        <el-form-item label="任务名称">
          <el-input v-model="localForm.str_task" disabled />
        </el-form-item>
        <el-form-item label="日期" prop="dt_shift">
          <el-date-picker
            :disabled="localForm.int_check_type === 0"
            v-model="localForm.dt_shift"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            @change="handleDateChange"
          />
        </el-form-item>
         <el-form-item label="班次"  prop="int_check_type" >
          <el-input v-if="localForm.int_check_type === 0" v-model="localForm.str_shift" disabled />
          <el-select
            v-else
            v-model="localForm.id_shift"
            placeholder="请选择班次"
            style="width: 100%"
            @change="handleShiftChange"
          >
            <el-option v-for="item in shiftOptions" :key="item.id" :label="item.str_name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="人员"  prop="staffs" >
          <el-select
            v-model="localForm.staffs"
            multiple
            filterable
            placeholder="请选择人员"
            style="width: 100%"
            value-key="id_staff"
            clearable
          >
            <el-option
              v-for="item in staffOptions"
              :key="item.value.id_staff"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
       
      </el-form>
      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>
  `,
})