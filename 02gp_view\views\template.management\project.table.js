
const { computed } = Vue;
export default {
  name: 'ProjectTemplateTable',
  inheritAttrs: false,
  setup(props, { attrs }) {
    const isLoading = computed(() => {
      return !attrs.tableData;
    });

    // 设置表头样式
    const headerRowClassName = ({ row, rowIndex }) => {
      return 'table-header';
    };

    return {
      isLoading,
      headerRowClassName,
    };
  },
  // language=HTML
  template: `
    <vxe-table
      :loading="isloading"
      :columns-config="{resizable: true}"
      :row-config="{isHover: true, isCurrent: true}"
      :data="$attrs.tableData"
      border
      stripe
      :show-header="true"
      :scroll-y="{enabled: true, mode: 'wheel'}"
      :header-row-class-name="headerRowClassName"
      height="100%"
      @filter-change="$attrs.handleFilterChange"
    >
      <vxe-column type="checkbox" width="80" align="center"></vxe-column>
      <vxe-column
        v-for="column in tableColumns"
        :key="column.field"
        :field="column.field"
        :title="column.title"
        :min-width="column.minWidth"
        show-overflow="title"
        :filters="column.filters"
        :filter-render="column.filterRender"
      ></vxe-column>
    </vxe-table>
  `,
};
