import { post } from '../../../config/axios/httpReuest.js';

const { reactive } = Vue;

export function useTemplateForm() {
  // define form rules
  const formRules = reactive({
    str_template_name: [
      { required: true, message: '请输入模板名称', trigger: 'blur' },
      { max: 20, message: '长度在 20 个字符以内', trigger: 'blur' },
    ],
    str_engine_type: [
      { required: true, message: '请选择机型', trigger: 'change' },
    ],
    str_remark: [
      { max: 500, message: '长度在 500 个字符以内', trigger: 'blur' },
    ],
  });

  // 根据Id获取模板详情
  const requestFormDataById = async (id) => {
    let obj = {};
    const param = {
      ac: 'gp_template_search_by_id',
      id,
    };
    const { data } = await post(param);
    if (data.code === 'error') {
      ElementPlus.ElMessage.error(data.text);
      return obj;
    }
    obj = Object.assign({}, data.data);
    return obj;
  };

  return {
    formRules,
    requestFormDataById,
  };
}
