export const tableConfig = {
  TABLE_COLUMNS: [
    {
      title: 'WO', // 工单号
      field: 'str_wo',
      minWidth: 35,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' }, // 使用输入框作为过滤器
      slots: {
        default: 'wo_default',
      },
    },
    {
      title: 'ESN',
      field: 'str_esn',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: 'Flow',
      field: 'str_flow',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      title: 'Project F4 Recommend', // 建议绿框开始时间
      field: 'dt_adjust_start',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' }, // 使用输入作为过滤器
      sortable: true,
      visible: false,
    },
    {
      title: 'Project F4 begin', // Project 开始时间
      field: 'dt_project_start',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' }, // 使用输入框作为过滤器
      sortable: true,
      visible: false,
    },
    {
      title: 'Release date',
      field: 'dt_release',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
      sortable: true,
    },
    {
      title: 'F3 close date',
      field: 'dt_f3_close',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
      sortable: true,
      visible: false,
    },
    {
      title: 'Engine Type',
      field: 'str_engine_type',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
      visible: false,
    },
    {
      title: 'Repair level',
      field: 'str_level',
      filters: [
        { label: 'OH', value: 'OH' },
        { label: 'SV', value: 'SV' },
        { label: 'NSV', value: 'NSV' },
        { label: 'MODULE', value: 'MODULE' },
      ],
      sortable: true,
      visible: false,
    },
    {
      title: 'Customer',
      field: 'str_client',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
      visible: false,
    },
    {
      title: '零件来源',
      field: 'str_class',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      type: 'html',
      title: 'On-log qty', // 总量(不包含Keep Missing)
      field: 'int_total',
      visible: false,
    },
    {
      type: 'html',
      title: 'CSM确认(进厂缺件) / Missing parts confirm by CSM',
      field: 'int103',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '在模块"进厂缺件客户确认"未给出市场决定。',
      },
    },
    {
      type: 'html',
      title: '未离开 Gate 1  / Not leaving Gate 1',
      field: 'int113',
      minWidth: 50,
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content:
          '零件报表主状态:TBD、工程待定；主状态：转包但无工卡或工卡被退回;主状态：不可用，无用料需求；F1-2管理 | F2-1管理 | F3和F4管理',
      },
      slots: {
        default: 'int113_default', // 自定义默认内容
        footer: 'int113_footer', // 自定义表尾内容
      },
    },
    {
      type: 'html',
      title: '待转包 / To be subcontract',
      field: 'int102',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '开出转包工卡后，未能生成转包订单；在PDA的"转包PO"站点，必定能查到对应的待转包工卡。',
      },
    },
    {
      type: 'html',
      title: '转包|采购无EDD / Subcontract|Purchase Parts without EDD',
      field: 'int109',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: 'Subcontracting without EDD 开出转包订单后，未能填写EDD或EDD小于当前日期（EDD过期）。',
      },
      slots: {
        default: 'int109_default',
        footer: 'int109_footer',
      },
    },
    {
      type: 'html',
      title: '待锁库 / To be lock Stock',
      field: 'int105',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '在PDA中"锁库高价值"和"锁库低价值&新件"两个站点待锁库的零件。',
      },
    },
    {
      type: 'html',
      title: '待采购 / To be purchased',
      field: 'int111',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content:
          '发起用料申请后，转入模块"待采购管理"准备发起采购订单，在PDA中的"采购高价值"和"采购低价值"两个站点能查到对应的零件。',
      },
    },
    {
      type: 'html',
      title: '待客户提供无LTDate / provided by customer without LTDate',
      field: 'int121',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '已下客户提供件的虚拟PO未能给出LT/Date，或LT/Date过期。',
      },
    },
    {
      type: 'html',
      title: '进入供应链消失 / Disappear in the supply chain',
      field: 'int114',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '进入PDA供应链后在点灯前消失。',
      },
    },
    {
      type: 'html',
      title: 'EKD计算中 / EKD calculation in progress',
      field: 'int101',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '处于正在计算站点和零件EKD的过程中的零件，最长不超过30分钟。',
      },
    },
    {
      type: 'html',
      title: '修理领料 / Material requisition for repair',
      field: 'int115',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '模块"用料申请"的需求类别为"修理领料"的零件。',
      },
      slots: {
        default: 'int115_default', // 自定义默认内容
        footer: 'int115_footer', // 自定义表尾内容
      },
    },
    {
      type: 'html',
      title: '消耗件 / Consumables parts',
      field: 'int106',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '模块"用料申请"的"存货分类"为指定的13种存货分类。',
      },
    },
    {
      type: 'html',
      title: '背板 / Backboard',
      field: 'int110',
    },
    {
      type: 'html',
      title: 'F2 EDD过期',
      field: 'int123',
    },
    {
      type: 'html',
      title: '待CSM确认(锁库) / To be confirmed by CSM  (lock stock)',
      field: 'int116',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '用料需求发起后，转入csm确认的待处理零件，在PDA的"CSM确认"站点能查到对应待处理零件。',
      },
    },
    {
      type: 'html',
      title: '待构型确认  / To be  confirmed by Engineering  configuration',
      field: 'int104',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content:
          '来自模块"进场缺件影响试车确认"、"进厂缺件工程确认"、"构型管理"和"现场串件"中的"审批步骤"为"工程审核"。',
      },
    },
    {
      type: 'html',
      title: 'Keep Missing',
      field: 'int107',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '模块"进厂缺件客户确认"给出"市场决定"为"Keep Missing"',
      },
    },
    {
      type: 'html',
      title: '试车借件 / For Test only',
      field: 'int112',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '模块"进厂缺件客户确认"给出"市场决定"为"Test Only by SSAMC provide"',
      },
    },
    {
      type: 'html',
      title: '无PN号 / Without PN',
      field: 'int119',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '模块"集件零件管理"中"集件件号"为空白的零件。',
      },
    },
    {
      type: 'html',
      title: '串件中 / Exchange Parts in progress',
      field: 'int120',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '模块"集件零件管理"中发生串件的零件，在跟踪供体零件的供应链过程中发生中断，或该零件属于虚拟零件。',
      },
    },
    {
      type: 'html',
      title: '锁库(需求关闭) / Lock Stock or Material requirement closed',
      field: 'int118',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '模块"锁库处理"中"锁库确认"为"已关闭"或"无需处理"；模块"用料申请"中的"处理状态"为"关闭"。',
      },
    },
    {
      type: 'html',
      title: '待串件项 / Waiting for swich',
      field: 'int50',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '需要串件的零件。',
      },
    },
    {
      type: 'html',
      title: '白转黄/From CTEM TO ESM',
      field: 'int200',
      minWidth: 30,
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: '待转PNR | 已转PNR | 待点灯扫描',
      },
      slots: {
        default: 'int200_default',
        footer: 'int200_footer',
      },
    },
    {
      type: 'html',
      title: 'core,fan,lpt红框后零件',
      field: 'int0110',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: 'core,fan,lpt红框后零件',
      },
      slots: {
        default: 'int0110_default',
        footer: 'int0110_footer',
      },
    },
    {
      type: 'html',
      title: 'b1红框后零件',
      field: 'int0111',
      titlePrefix: {
        icon: 'vxe-icon-question-circle-fill',
        content: 'core,fan,lpt蓝框后零件',
      },
      slots: {
        default: 'int0111_default',
        footer: 'int0111_footer',
      },
    },
  ],
  DISABLED_COLUMNS: [
    'ekdpercent',
    'str_wo',
    'str_esn',
    'str_flow',
    'dt_release',
    'dt_f3_close',
    'str_engine_type',
    'str_level',
    'str_client',
  ],
}
