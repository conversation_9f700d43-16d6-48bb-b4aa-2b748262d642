import HtDrawer from '../../../components/ht.drawer.js'
import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'

// 渲染抽屉
export function useDrawerRenderer(drawerVisible, drawerTableData, drawerTableColumns, closeDrawer, saveDrawer) {
  const { h } = Vue

  const renderDrawerContent = () => {
    return h(HtVxeTable, {
      tableData: drawerTableData.value,
      tableColumns: drawerTableColumns.value,
    })
  }
  const renderDrawer = () => {
    // 只在抽屉可见时才渲染抽屉组件
    if (!drawerVisible.value) return null

    return h(
      HtDrawer,
      {
        visible: drawerVisible.value,
        title: '单元格详情',
        size: '80%',
        isShowSave: false,
        onClear: closeDrawer,
      },
      {
        default: () => renderDrawerContent(),
      },
    )
  }

  return {
    renderDrawer,
  }
}
