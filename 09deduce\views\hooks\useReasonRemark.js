import { post } from '../../../config/axios/httpReuest.js'
const { reactive, nextTick, ref } = Vue
export function useReasonRemark(tableRef, func) {
  const reasonRemark = reactive({
    visible: false,
    id: null,
    id_pkp: null,
    form: {
      remark: '',
      int_exception: 0, // 1 标识异常标记
      int_type: 1, // 1 标识保交付 0标识取消保障
    },
    title: '原因备注',
    businessType: '',
  })

  const disabledDate = ref(null)
  /**
   * 打开原因备注
   * @param {number} int_exception 1 标识异常标记  reasonRemark.title
   * @param {number} businessType
   */
  const openReasonRemark = (int_exception = 0, businessType = 0) => {
    const selectedData = tableRef.value.getSelectedData()
    if (selectedData.length === 0) {
      ElementPlus.ElMessage.warning('请先选择数据')
      return
    }
    if (selectedData.length > 1) {
      ElementPlus.ElMessage.warning('请选择一条数据')
      return
    }

    if (businessType == 100) {
      handleException(selectedData)
      return
    }
    const { id, id_pkp } = selectedData[0]
    reasonRemark.id = id
    reasonRemark.id_pkp = id_pkp
    reasonRemark.visible = true
    reasonRemark.title = int_exception == 1 ? '异常标记' : '原因备注'
    reasonRemark.businessType = businessType
    reasonRemark.form.int_exception = int_exception
    if (businessType == 60) {
      reasonRemark.title = '保交付'
      disabledDate.value = moment(selectedData[0].dt_delivery_limit).format('YYYY-MM-DD')
    }
  }
  // 异常屏蔽处理
  const handleException = (selectedData) => {
    // 增加提示语
    ElementPlus.ElMessageBox.confirm(
      `<span style="font-size: 20px;">零件名称为<span style="color: red;">${selectedData[0].str_part_name}</span>，屏蔽后不再作为发动机<span style="color: red;">${selectedData[0].str_esn}</span>的集件项，请向工程部和市场部进行严格确认后再进行屏蔽操作，是否还是坚持屏蔽!</span>`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        // 使用html标签
        dangerouslyUseHTMLString: true,
        customStyle: {
          width: '50vw',
          maxWidth: '100vw',
        },
        type: 'warning',
      },
    )
      .then(() => {
        // 用户点击确定后，显示弹出框
        const { id, id_pkp } = selectedData[0]
        reasonRemark.id = id
        reasonRemark.id_pkp = id_pkp
        reasonRemark.title = '异常标记'
        reasonRemark.businessType = 0
        reasonRemark.form.int_exception = 1
        reasonRemark.visible = true
      })
      .catch(() => {
        // 用户点击取消，不做任何操作
        console.log('用户取消异常屏蔽')
      })
  }
  const saveReasonRemark = async () => {
    let params = {}
    if (reasonRemark.businessType == 60) {
      params = {
        ac: 'de_pndeliverysave',
        int_type: reasonRemark.form.int_type,
        id_pkp: [reasonRemark.id],
        dt_delivery: reasonRemark.form.dt_delivery,
      }
    } else {
      params = {
        ac: 'de_save_pn_remark',
        id: reasonRemark.id,
        id_pkp: reasonRemark.id_pkp,
        remark: reasonRemark.form.remark,
        int_exception: reasonRemark.form.int_exception,
      }
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      ElementPlus.ElMessage.success('保存成功')
      reasonRemark.visible = false
      func()
    } else {
      ElementPlus.ElMessage.error(data.text)
    }
  }
  const closeReasonRemark = () => {
    reasonRemark.visible = false
    reasonRemark.form = {
      remark: '',
      int_type: null,
    }
  }
  return { reasonRemark, openReasonRemark, saveReasonRemark, closeReasonRemark, disabledDate }
}
