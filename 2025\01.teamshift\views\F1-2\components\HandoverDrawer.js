import { getHandover, uploadFile, saveHandover } from '../api/index.js'

const { useVModel } = VueUse
const { ref, onMounted } = Vue

export default {
  name: 'HandoverDrawer',
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    row: {
      type: Object,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    title: {
      type: String,
      default: '检验',
    },
    handoverType: {
      type: String,
      required: true,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const handoverVisible = useVModel(props, 'visible', emit)
    const handleClose = () => {
      handoverVisible.value = false
    }
    // 是否完成
    const is_completed_ref = ref(0)
    // 交接单提示
    const handoverTip = ref('')
    // 交接单列表
    const handoverList = ref([])
    // 交接单基础信息
    const baseInfo = ref({})
    // 交接单表格数据
    const tableData = ref([])
    // 交接单主表ID
    const pThandoverMainid = ref('')
    // 文件查看器抽屉可见性
    const fileDrawerVisible = ref(false)
    // 当前查看的文件列表
    const currentFiles = ref([])

    // 获取交接单数据
    const getHandoverList = async () => {
      try {
        // 当前日期下的任务
        const currentTask = props.row.plan.find((item) => moment(item.plan_date).isSame(props.column.day, 'day'))
        // 当前日期下的任务ID
        const taskId = currentTask.task?.map((item) => item.taskId).join(',')
        // 当前日期下的任务SMID
        const smId = currentTask.task?.map((item) => item.id_sm).join(',')
        // 当前日期下的任务班次ID
        const shiftId = props.row.id_shift

        const params = {
          id_wo: props.row.id_wo,
          id_sms: smId,
          id_tasks: taskId,
          id_shift: shiftId,
          str_handover_type: props.handoverType,
          dt_pt: props.column.day,
        }

        const res = await getHandover(params)
        handoverList.value = res.pTHandovers
        handoverTip.value = res.str_title
        baseInfo.value = {
          esn: props.row.esn,
          wo: props.row.wo,
          type: props.row.str_task_type,
          leader: props.row.team_Leader,
          shift: props.row.str_shift,
        }

        // 保存主表ID
        pThandoverMainid.value = res.pTHandoverMain?.id || ''
        is_completed_ref.value = +( res.pTHandoverMain?.is_completed || 0)
        // 初始化表格数据
        initializeTableData(res.pTHandovers)
      } catch (error) {
        ElementPlus.ElMessage.error('获取交接单数据失败：' + (error.message || '未知错误'))
      }
    }

    // 初始化表格数据
    const initializeTableData = (handovers) => {
      if (!handovers || !Array.isArray(handovers)) return

      // 处理交接数据
      const processedData = handovers.map((handover) => ({
        id: handover.pTHandover.id || '',
        code: handover.pTHandover.int_type || 0,
        legend: handover.pTHandover.str_category || '',
        tip: handover.pTHandover.str_help || '暂无信息',
        desc: handover.pTHandover.str_content || '',
        isTransferred: handover.pTHandover.int_status === 1,
        isOriginal: true, // 先标记所有数据为原始数据
        attachment: handover.files || [],
        id_model: handover.pTHandover.id_model,
        str_sm: handover.pTHandover.str_sm || '',

      }))

      // 按code排序
      processedData.sort((a, b) => a.code - b.code)

      // 为每个code组找出原始行
      const codeGroups = new Map()
      processedData.forEach((item) => {
        if (!codeGroups.has(item.code)) {
          codeGroups.set(item.code, [])
        }
        codeGroups.get(item.code).push(item)
      })

      // 只有第一行是原始行，其他行不是
      codeGroups.forEach((group) => {
        if (group.length > 0) {
          for (let i = 1; i < group.length; i++) {
            group[i].isOriginal = false
          }
        }
      })

      // 更新表格数据
      tableData.value = processedData
    }

    // 计算表格单元格合并方法
    const getSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1) {
        // Code和Legend列需要合并
        const rows = tableData.value
        if (!rows.length) return { rowspan: 1, colspan: 1 }

        const currentCode = row.code

        // 向上查找同一code的第一行
        let startIndex = rowIndex
        while (startIndex > 0 && rows[startIndex - 1].code === currentCode) {
          startIndex--
        }

        // 如果是该分组的第一行
        if (rowIndex === startIndex) {
          // 计算相同code的行数
          let spanCount = 1
          let nextIndex = rowIndex + 1
          while (nextIndex < rows.length && rows[nextIndex].code === currentCode) {
            spanCount++
            nextIndex++
          }
          return {
            rowspan: spanCount,
            colspan: 1,
          }
        } else {
          // 不是第一行则隐藏
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
      return { rowspan: 1, colspan: 1 }
    }

    // 删除行
    const handleDeleteRow = (row) => {
      const index = tableData.value.findIndex((item) => item === row)
      if (index > -1) {
        // 如果是原始行，需要检查是否有其他相同code的行
        if (row.isOriginal) {
          const currentCode = row.code
          // 删除当前行
          tableData.value.splice(index, 1)

          // 查找相同code的所有行
          const sameCodeRows = tableData.value.filter((item) => item.code === currentCode)
          if (sameCodeRows.length > 0) {
            // 将第一行设置为原始行
            sameCodeRows[0].isOriginal = true
          }
        } else {
          // 不是原始行直接删除
          tableData.value.splice(index, 1)
        }

        ElementPlus.ElMessage.success('删除成功')
      }
    }

    // 新增行
    const handleAddRow = (row) => {
      if (!row?.code || !row?.legend) {
        ElementPlus.ElMessage.warning('无效的行数据')
        return
      }

      // 创建新行，使用解构赋值确保数据结构一致性
      const newRow = {
        ...row,
        id: '', // 清空ID，因为这是新行
        desc: '',
        attachment: [],
        isOriginal: false,
        isTransferred: false,
      }

      // 查找相同code的行，确定插入位置
      const sameCodeRows = tableData.value.filter((item) => item.code === row.code)
      const lastSameCodeRowIndex = tableData.value.lastIndexOf(sameCodeRows[sameCodeRows.length - 1])

      // 插入新行
      tableData.value.splice(lastSameCodeRowIndex + 1, 0, newRow)

      ElementPlus.ElMessage.success('新增行成功')
    }

    // 定义允许的文件类型和大小限制
    const ALLOWED_FILE_TYPES = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'application/zip',
      'application/x-rar-compressed',
    ]
    const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

    // 检查文件安全性
    const checkFileSecurity = (file) => {
      // 检查文件类型
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        ElementPlus.ElMessage.error(`不支持的文件类型: ${file.name}`)
        return false
      }

      // 检查文件大小
      if (file.size > MAX_FILE_SIZE) {
        ElementPlus.ElMessage.error(
          `文件过大 (${(file.size / 1024 / 1024).toFixed(2)}MB), 最大限制 ${MAX_FILE_SIZE / 1024 / 1024}MB: ${file.name}`,
        )
        return false
      }

      // 检查文件名安全性
      const dangerousChars = /[<>:"/\\|?*\x00-\x1F]/g
      const consecutiveDots = /\.{2,}/g
      const reservedNames = /^(con|prn|aux|nul|com[0-9]|lpt[0-9])$/i

      if (dangerousChars.test(file.name)) {
        ElementPlus.ElMessage.error(`文件名包含非法字符: ${file.name}`)
        return false
      }

      if (consecutiveDots.test(file.name)) {
        ElementPlus.ElMessage.error(`文件名不能包含连续的点: ${file.name}`)
        return false
      }

      const nameWithoutExt = file.name.split('.')[0].toLowerCase()
      if (reservedNames.test(nameWithoutExt)) {
        ElementPlus.ElMessage.error(`文件名不能使用系统保留名: ${file.name}`)
        return false
      }

      return true
    }

    // 上传文件
    const handleUpload = (row) => {
      try {
        const input = document.createElement('input')
        input.type = 'file'
        input.multiple = true
        input.accept = ALLOWED_FILE_TYPES.join(',')

        input.onchange = async (e) => {
          const files = Array.from(e.target.files)
          if (files.length) {
            // 过滤安全的文件
            const safeFiles = files.filter((file) => checkFileSecurity(file))

            if (safeFiles.length === 0) {
              ElementPlus.ElMessage.warning('没有符合要求的文件可以上传')
              return
            }

            if (safeFiles.length !== files.length) {
              ElementPlus.ElMessage.warning('部分文件未通过安全检查，已被过滤')
            }

            await uploadFiles(safeFiles, row)
          }
        }
        input.click()
      } catch (error) {
        ElementPlus.ElMessage.error('文件上传失败: ' + (error.message || '未知错误'))
      }
    }

    // 执行文件上传
    const uploadFiles = async (files, row) => {
      try {
        // 显示上传加载状态
        const loading = ElementPlus.ElLoading.service({
          lock: true,
          text: '文件上传中...',
          background: 'rgba(0, 0, 0, 0.7)',
        })

        // 并行上传所有文件
        const uploadPromises = files.map(async (file) => {
          const formData = new FormData()
          formData.append('file', file)
          formData.append('type', 'handover')
          formData.append('esn', props.row.esn || '')
          formData.append('wo', props.row.wo || '')

          try {
            const res = await uploadFile(formData)
            return {
              name: file.name,
              size: file.size,
              type: file.type,
              uploadTime: new Date().toLocaleString(),
              str_path: res.path || '',
              str_file_name: file.name,
            }
          } catch (error) {
            ElementPlus.ElMessage.error(`文件 ${file.name} 上传失败: ${error.message || '未知错误'}`)
            return null
          }
        })

        // 等待所有文件上传完成
        const results = await Promise.all(uploadPromises)

        // 过滤掉上传失败的文件
        const successFiles = results.filter((result) => result !== null)

        // 更新行数据
        successFiles.forEach((file) => {
          row.attachment?.push(file)
        })

        loading.close()

        if (successFiles.length > 0) {
          ElementPlus.ElMessage.success(`成功上传 ${successFiles.length} 个文件`)
        } else {
          ElementPlus.ElMessage.error('所有文件上传失败')
        }
      } catch (error) {
        ElementPlus.ElMessage.error('文件上传过程中发生错误: ' + (error.message || '未知错误'))
      }
    }

    // 查看文件
    const handleViewFiles = (row) => {
      currentFiles.value = row.attachment || []
      fileDrawerVisible.value = true
    }

    // 下载文件
    const handleDownload = (file) => {
      try {
        const a = document.createElement('a')
        a.href = file.str_path
        a.download = file.str_file_name
        a.click()
        a.remove()
      } catch (error) {
        ElementPlus.ElMessage.error('下载失败：' + (error.message || '未知错误'))
      }
    }

    // 保存交接单
    const handleSaveHandover = async () => {
      try {
        // 获取所有交接单的数据
        const allData = tableData.value
        if (!allData || allData.length === 0) {
          ElementPlus.ElMessage.error('没有可提交的数据')
          return
        }

        // 验证数据有效性
        const invalidRows = validateHandoverData(allData)
        if (invalidRows.length > 0) {
          const errorCodes = invalidRows.map((row) => `${row.code}`).join('、')
          ElementPlus.ElMessage.error(`请完善 Code ${errorCodes} 的内容或附件`)
          return
        }

        // 保存交接单
        await saveHandoverData(allData)

        ElementPlus.ElMessage.success('交接单保存成功')
        handoverVisible.value = false
      } catch (error) {
        ElementPlus.ElMessage.error('保存失败：' + (error.message || '未知错误'))
      }
    }

    // 保存交接单数据
    const saveHandoverData = async (allData) => {
      // 获取当前日期下的任务
      const currentTask = props.row.plan.find((item) => moment(item.plan_date).isSame(props.column.day, 'day'))

      // 按SM分组数据用于保存
      const smGroups = new Map()
      allData.forEach((item) => {
        if (!smGroups.has(item.id_model)) {
          smGroups.set(item.id_model, {
            id: item.id_model,
            name: item.str_sm,
            data: [],
          })
        }
        smGroups.get(item.id_model).data.push(item)
      })

      const postData = []
      smGroups.forEach((sm) => {
        const main = {
          id: pThandoverMainid.value,
          id_wo: props.row.id_wo || '',
          str_wo: props.row.wo || '',
          id_team: props.row.id_team || '',
          dt_pt: props.column.day,
          id_model: sm.id,
          id_task: currentTask.task.map((item) => item.taskId).join(','),
          id_shift: props.row.id_shift,
          str_shift: props.row.shift_name,
          str_handover_type: props.handoverType,
          str_task_type: props.row.str_task_type,
          is_completed: is_completed_ref.value,
        }

        // 转换表格数据为后端需要的格式
        const handovers = sm.data.map((item) => ({
          pTHandover: {
            id: item.id || '',
            id_model: item.id_model,
            str_sm: item.str_sm,
            int_type: item.code,
            str_category: item.legend,
            str_help: item.tip,
            str_content: item.desc || '',
            int_status: item.isTransferred ? 1 : 0,
          },
          files: (item.attachment || []).map((file) => ({
            id: file.id || '',
            str_path: file.str_path,
            str_file_name: file.str_file_name || file.name,
          })),
        }))

        postData.push({
          pTHandoverMain: main,
          pTHandovers: handovers,
        })
      })

      // 构建主表数据
      await saveHandover(postData)
    }

    // 验证交接单数据
    const validateHandoverData = (allData) => {
      return allData.filter((item) => {
        // 如果勾选了无交接，则跳过验证
        if (item.isTransferred) {
          return false
        }

        // 检查描述内容和文件
        const hasContent = item.desc && item.desc.trim() !== ''
        const hasFiles = item.attachment && item.attachment.length > 0

        return !hasContent && !hasFiles
      })
    }

    onMounted(() => {
      getHandoverList()
    })

    return {
      handoverVisible,
      handoverTip,
      baseInfo,
      handoverList,
      tableData,
      pThandoverMainid,
      fileDrawerVisible,
      currentFiles,
      handleClose,
      getSpanMethod,
      handleDeleteRow,
      handleAddRow,
      handleUpload,
      handleViewFiles,
      handleDownload,
      handleSaveHandover,
      is_completed_ref,
    }
  },
  template: /*html*/ `
    <el-drawer
      class="common-drawer"
      v-model="handoverVisible"
      :title="title + '交接'"
      size="80%"
      :append-to-body="true"
      @close="handleClose"
    >
      <div class="mb-4 rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4 shadow-sm">
        <div class="flex items-center">
          <div class="flex w-full cursor-help items-center">
            <el-icon class="mr-2 flex-shrink-0" :size="20"><InfoFilled /></el-icon>
            <div class="min-w-0 flex-1">
              <div class="mb-1 text-lg font-semibold text-red-700">交接提示</div>
              <div class="whitespace-pre-line text-red-600">{{ handoverTip }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="mb-6">
        <el-descriptions :column="5" border>
          <el-descriptions-item label="ESN">{{ baseInfo.esn }}</el-descriptions-item>
          <el-descriptions-item label="WO">{{ baseInfo.wo }}</el-descriptions-item>
          <el-descriptions-item label="Type">{{ baseInfo.type }}</el-descriptions-item>
          <el-descriptions-item label="组长">{{ baseInfo.leader }}</el-descriptions-item>
          <el-descriptions-item label="班次">{{ baseInfo.shift }}</el-descriptions-item>
        </el-descriptions>
      </div>
    
      <div class="flex flex-row">
      <div class="basis-64">
       <el-checkbox v-model="is_completed_ref" label="完成(最后一个任务不需要接收)"  :true-label="1" :false-label="0" size="large" />
      </div>
    </div>
      <div class="flex flex-col gap-6 overflow-auto">
        <el-table
          :data="tableData"
          border
          :span-method="getSpanMethod"
          size="small"
          class="w-full"
        >
          <el-table-column prop="code" label="Code" width="80" align="center" />
          <el-table-column prop="legend" label="Legend" width="120">
            <template #default="{ row }">
              <div class="flex items-center">
                <div class="min-w-0 flex-1">{{ row.legend }}</div>
                <el-tooltip effect="dark" placement="top" :content="row.tip">
                  <el-icon class="ml-1 text-gray-400" :size="16"><InfoFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="desc" label="Desc" min-width="300">
            <template #default="{ row }">
              <el-input type="textarea" v-model="row.desc" :rows="2" placeholder="请输入内容" resize="none" />
            </template>
          </el-table-column>
          <el-table-column label="Attachment" width="100" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center space-x-2">
                <el-button type="primary" link @click="handleUpload(row)">
                  <el-icon><Upload /></el-icon>
                </el-button>
                <el-button v-if="row.attachment?.length > 0" type="primary" link @click="handleViewFiles(row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="Operate" width="120" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center space-x-2">
                <el-button v-if="!row.isOriginal" type="danger" link @click="handleDeleteRow(row)">
                  删除
                </el-button>
                <el-button v-if="row.isOriginal" type="primary" link @click="handleAddRow(row)">新增</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="无交接" width="80" align="center">
            <template #default="{ row }">
              <el-checkbox v-model="row.isTransferred" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button type="danger" @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSaveHandover">提交</el-button>
        </div>
      </template>
    </el-drawer>
    
    <!-- 文件查看抽屉 -->
    <el-drawer v-model="fileDrawerVisible" title="文件列表" size="50%" direction="rtl">
      <el-table :data="currentFiles" border size="small">
        <el-table-column prop="str_file_name" label="文件名">
          <template #default="{ row }">{{ row.str_file_name.split('.')[0] }}</template>
        </el-table-column>
        <el-table-column prop="str_type" label="类型" width="100">
          <template #default="{ row }">{{ row.str_file_name.split('.')[1] }}</template>
        </el-table-column>
        <el-table-column prop="dt_up" label="上传时间" width="180" />
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleDownload(row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
  `,
}
