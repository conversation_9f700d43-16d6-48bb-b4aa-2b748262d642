/**
 * 初始化甘特图
 * @param {string} domId - 甘特图容器的DOM元素id
 */
const { nextTick } = Vue

export const useInitialize = (domId, columns) => {
  const gantt = Gantt.getGanttInstance()
  const GANTT_CONFIG = {
    // 日期格式
    date_format: '%Y-%m-%d',
    // 表格日期格式
    date_grid: '%Y-%m-%d',
    // 禁止拖拽链接
    drag_links: false,
    // grid的宽度
    grid_width: 600,
    // 是否显示网格
    show_grid: true,
    // 自动展开
    open_tree_initially: true,
    // 禁止project的拖拽
    drag_project: false,
    // 禁止progress的拖拽
    drag_progress: false,
    // 自动调度
    auto_scheduling: true,
    // 自动调度兼容
    auto_scheduling_compatibility: true,
  }
  const GANTT_PLUGINS = {
    // 自动调整大小
    auto_scheduling: true,
    // 临界路径
    critical_path: true,
  }
  const initGantt = () => {
    // 确保DOM元素存在

    gantt.clearAll()
    // 设置配置
    Object.keys(GANTT_CONFIG).forEach((key) => {
      gantt.config[key] = GANTT_CONFIG[key]
    })
    // 设置列
    gantt.config.columns = columns
    // 设置插件
    Object.keys(GANTT_PLUGINS).forEach((key) => {
      gantt.plugins({
        [key]: GANTT_PLUGINS[key],
      })
    })

    try {
      nextTick(() => {
        const container = document.getElementById(domId)
        if (!container) {
          console.error(`找不到ID为 ${domId} 的DOM元素`)
          return
        }
        gantt.init(domId) // 直接传入DOM元素而不是ID字符串
      })
    } catch (error) {
      console.error('甘特图初始化失败:', error)
    }
  }
  return {
    initGantt,
    gantt,
  }
}
