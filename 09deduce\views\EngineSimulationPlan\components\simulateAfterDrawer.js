import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
import { useSimulateAfter, useCertificateInfo } from '../hooks/useSimulateAfter.js'
import ReSelectResourceDrawer from './reSelectResourceDrawer.js'
const { toRefs, onMounted } = Vue
const { useVModel } = VueUse
/**
 * @description 模拟后抽屉
 * <AUTHOR>
 */
// 引入公共表格组件
const SimulateAfterDrawer = {
  components: {
    HtVxeTable,
    ReSelectResourceDrawer,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    idWo: {
      type: String,
      required: true,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)

    const {
      tableRef,
      tableState,
      getTableData,
      handleFilterChange,
      exportTableData,
      iSCsmResult,
      handleRunSimulation,
      reSelectResource,
      handleReSelect,
    } = useSimulateAfter()

    const handleRunSimulationClick = async () => {
      await handleRunSimulation(props.idWo)
      visible.value = false
    }

    const {
      openCertificateInfo,
      closeCertificateInfo,
      certificateLoading,
      certificateTableData,
      getCertificateTableData,
      certificateInfoVisible,
    } = useCertificateInfo(tableRef)
    onMounted(() => {
      const { idWo } = props
      getTableData(idWo)
    })
    return {
      visible,
      tableRef,
      ...toRefs(tableState),
      handleFilterChange,
      exportTableData,
      iSCsmResult,
      handleRunSimulationClick,
      reSelectResource,
      handleReSelect,
      openCertificateInfo,
      closeCertificateInfo,
      certificateInfoVisible,
      certificateLoading,
      certificateTableData,
      getCertificateTableData,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="80%" :show-close="false" destroy-on-close :modal="false" :z-index="1">
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-white">模拟后清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <el-button type="primary" @click="handleRunSimulationClick">执行模拟结果</el-button>
            <el-button type="primary" @click="iSCsmResult({int_csm_result: 301}, idWo)">发送CSM</el-button>
            <el-button type="danger" @click="handleReSelect">重选资源方案</el-button>
            <el-button type="success" @click="iSCsmResult({is_lock: 1},idWo)">锁定方案</el-button>
            <el-button type="success" @click="iSCsmResult({is_lock: 0},idWo)">解锁方案</el-button>
            <el-button type="primary" @click="exportTableData">导出</el-button>
            <el-button type="primary" @click="openCertificateInfo">证书信息</el-button>
          </div>
          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable
          ref="tableRef"
          :tableData="data"
          :tableColumns="columns"
          :isShowHeaderCheckbox="true"
          @filterChange="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>

    <ReSelectResourceDrawer
      v-if="reSelectResource.visible"
      v-model:visible="reSelectResource.visible"
      :params="reSelectResource"
    ></ReSelectResourceDrawer>

    <el-dialog class="my-dialog" v-model="certificateInfoVisible" title="证书信息" width="60%" :show-close="false">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="text-white">证书信息</div>
          <el-button size="small" type="danger" @click="closeCertificateInfo">关闭</el-button>
        </div>
      </template>
      <div class="m-4">
        <el-table :data="certificateTableData" border height="500" v-loading="certificateLoading">
          <el-table-column label="属性信息" prop="property">
            <template #header>
              <div class="text-black font-bold">属性信息</div>
            </template>
            <template #default="scope">
              <div class="text-black">{{ scope.row.property }}</div>
            </template>
          </el-table-column>
          <el-table-column label="目标发动机" prop="value1">
            <template #header>
              <div class="text-black font-bold">目标发动机</div>
            </template>
          </el-table-column>
          <el-table-column label="原发动机" prop="value2">
            <template #header>
              <div class="text-black font-bold">原发动机</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  `,
}

export default SimulateAfterDrawer
