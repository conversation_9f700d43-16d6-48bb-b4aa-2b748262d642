

const {
    reactive,
    onMounted,
    ref
} = Vue;
const Global = {
    components: {

    },
    setup() {
        const dragSet = reactive({
            drag: false
        })
        const data = ref([{
            id: 1,
            str_pn: 'PN0001',
            dt_date: '2024-06-15',
            int_type: 1
        }, {
            id: 1,
            str_pn: 'PN0001',
            dt_date: '2024-06-20',
            int_type: 1
        }, {
            id: 1,
            str_pn: 'PN0001',
            dt_date: '2024-07-05',
            int_type: 1
        }]);
        const chartRef = ref(null)
        const option = {
            title: {
                // text: 'Male and female height and weight distribution',
                // subtext: 'Data from: Heinz 2003'
            },
            grid: {
                left: '3%',
                right: '7%',
                bottom: '7%',
                containLabel: true
            },
            tooltip: {
                // trigger: 'axis',
                showDelay: 0,
                formatter: function (params) {
                    if (params.value.length > 1) {
                        return (
                            params.seriesName +
                            ' :<br/>' +
                            params.value[0] +
                            'cm ' +
                            params.value[1] +
                            'kg '
                        );
                    } else {
                        return (
                            params.seriesName +
                            ' :<br/>' +
                            params.name +
                            ' : ' +
                            params.value +
                            'kg '
                        );
                    }
                },
                axisPointer: {
                    show: true,
                    type: 'cross',
                    lineStyle: {
                        type: 'dashed',
                        width: 1
                    }
                }
            },
            toolbox: {
                // 操作工具
                // feature: {
                //     dataZoom: {},
                //     brush: {
                //         type: ['rect', 'polygon', 'clear']
                //     }
                // }
            },
            // brush: {},
            legend: {
                data: ['Female', 'Male'],
                left: 'center',
                bottom: 10
            },
            xAxis: [
                {
                    type: 'value',
                    scale: true,
                    axisLabel: {
                        formatter: '{value} cm'
                    },
                    splitLine: {
                        show: false
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    scale: true,
                    axisLabel: {
                        formatter: '{value} kg'
                    },
                    splitLine: {
                        show: false
                    }
                }
            ],
            graphic: [
                {
                    type: 'group',
                    // rotation: Math.PI / 4,
                    bounding: 'all',
                    // left: 165,
                    // bottom: 75,
                    x: 1000,
                    y: 400,
                    z: 100,
                    draggable: true,
                    children: [
                        {
                            type: 'rect',
                            left: 'center',
                            top: 'center',
                            z: 100,
                            shape: {
                                x: 300,
                                y: 200,
                                width: 400,
                                height: 300
                            },
                            style: {
                                fill: 'rgba(0,0,0,0.3)'
                            }
                        },
                        // {
                        //     type: 'text',
                        //     left: 'center',
                        //     top: 'center',
                        //     z: 100,
                        //     style: {
                        //         fill: '#fff',
                        //         text: 'ECHARTS LINE CHART',
                        //         font: 'bold 26px sans-serif'
                        //     }
                        // }
                    ]
                }
            ],
            series: [
                {
                    name: 'Female',
                    type: 'scatter',
                    emphasis: {
                        focus: 'series'
                    },
                    // prettier-ignore
                    data: [
                        // [174.0, 65.6], [184.0, 65.68], 
                    ],
                    // markArea: {
                    //     silent: true,
                    //     itemStyle: {
                    //         color: 'transparent',
                    //         borderWidth: 1,
                    //         borderType: 'dashed'
                    //     },
                    //     data: [
                    //         [
                    //             {
                    //                 name: 'Female Data Range',
                    //                 xAxis: 'min',
                    //                 yAxis: 'min'
                    //             },
                    //             {
                    //                 xAxis: 'max',
                    //                 yAxis: 'max'
                    //             }
                    //         ]
                    //     ]
                    // },
                    // markPoint: {
                    //     data: [
                    //         { type: 'max', name: 'Max' },
                    //         { type: 'min', name: 'Min' }
                    //     ]
                    // },
                    // markLine: {
                    //     lineStyle: {
                    //         type: 'solid'
                    //     },
                    //     data: [{ type: 'average', name: 'AVG' }, { xAxis: 160 }]
                    // }
                },
                {
                    name: 'Male',
                    type: 'scatter',
                    emphasis: {
                        focus: 'series'
                    },
                    // prettier-ignore
                    data: [[174.0, 65.6], [174.0, 65.6],


                    ],
                    itemStyle: {
                        normal: {
                            opacity: 0.8
                        }
                    },
                    silent: true,
                    animation: false,
                    seriesOptions: {
                        type: 'scatter',
                        overlap: 'jitter'

                    }
                    // markArea: {
                    //     silent: true,
                    //     itemStyle: {
                    //         color: 'transparent',
                    //         borderWidth: 1,
                    //         borderType: 'dashed'
                    //     },
                    //     data: [
                    //         [
                    //             {
                    //                 name: 'Male Data Range',
                    //                 xAxis: 'min',
                    //                 yAxis: 'min'
                    //             },
                    //             {
                    //                 xAxis: 'max',
                    //                 yAxis: 'max'
                    //             }
                    //         ]
                    //     ]
                    // },
                    // markPoint: {
                    //     data: [
                    //         { type: 'max', name: 'Max' },
                    //         { type: 'min', name: 'Min' }
                    //     ]
                    // },
                    // markLine: {
                    //     lineStyle: {
                    //         type: 'solid'
                    //     },
                    //     data: [{ type: 'average', name: 'Average' }, { xAxis: 170 }]
                    // }
                },
                // {
                //     type: 'custom',
                //     renderItem: function (params, api) {
                //         var yValue = api.value(2);
                //         var start = api.coord([api.value(0), yValue]);
                //         var size = api.size([api.value(1) - api.value(0), yValue]);
                //         var style = api.style();
                //         return {
                //             type: 'rect',
                //             shape: {
                //                 x: start[0],
                //                 y: start[1],
                //                 width: size[0],
                //                 height: size[1]
                //             },
                //             style: style
                //         };
                //     },
                //     label: {
                //         show: true,
                //         position: 'top'
                //     },
                //     dimensions: ['from', 'to', 'profit'],
                //     encode: {
                //         x: [0, 1],
                //         y: 2,
                //         tooltip: [0, 1, 2],
                //         itemName: 3
                //     },
                //     data: data,
                //     cursor: 'move',
                //     ondrag: function (params) {
                //         console(params)
                //     }
                // }
            ]
        };
        onMounted(async () => {
            // const rectangles = [{ x: 300, y: 400, height: 100, width: 100, name: "卷长", itemStyle: { borderColor: "red", normal: { color: "red" } } },
            // { x: 400, y: 800, height: 100, width: 100, name: "卷宽", itemStyle: { borderColor: "red", normal: { color: "blue" } } },]
            // let data = rectangles.map(item => {
            //     return {
            //         name: item.name,
            //         value: [item.x, item.y, item.width, item.height],
            //         itemStyle: item.itemStyle
            //     }
            // })
            // option.series[0].data = rectangles
            // option.series[0].data = rectangles
            const myChart = echarts.init(chartRef.value);
            myChart.setOption(option);

            //监听鼠标按下事件
            myChart.on('mousedown', function (params) {
                if (params.componentType === 'graphic') {
                    dragSet.dragStart = params.event.offsetX;
                }
                // 记录开始拖拽的坐标

            });
            // 监听鼠标移动事件
            myChart.on('mousemove', function (params) {
                if (dragSet.dragStart !== null) {
                    // 执行拖拽操作
                    // 可以根据实际需求进行处理，例如更新图表数据等
                    let site = myChart.convertFromPixel({ seriesIndex: 0 }, [params.event.offsetX, params.event.offsetY]); // 系列索引和屏幕坐标
                    console.log('正在拖动，当前位置：', site);

                    // 更新图表显示（根据实际需求进行调整）
                    // myChart.setOption(...);
                }
            });
            // 监听鼠标抬起事件
            myChart.on('mouseup', function (params) {
                dragSet.dragEnd = params.event.offsetX;
                // 拖拽操作结束，重置开始拖拽的坐标
                dragSet.dragStart = null;
            });
            // 监听brush事件来处理选中的数据
            myChart.on('brush', function (params) {
                // 屏幕坐标转数据坐标
                let data = myChart.convertFromPixel({ seriesIndex: 0 }, [params.areas[0].range[0][0], params.areas[0].range[0][1]]); // 系列索引和屏幕坐标
                console.log(data); // 输出数据坐标，例如 [2, 3]
                console.log("--------------------");
                let data1 = myChart.convertFromPixel({ seriesIndex: 1 }, [params.areas[0].range[0][0], params.areas[0].range[0][1]]); // 系列索引和屏幕坐标
                console.log("y:" + data1); // 输出数据坐标，例如 [2, 3]

            })
        });

        return {
            option,
            chartRef,
            dragSet
        };
    },
    /*HTML*/
    template: `
    <div ref="chartRef" style="height: 400px;"></div>
  `

    ,
};
export default Global;
