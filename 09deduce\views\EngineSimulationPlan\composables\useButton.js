import { post } from '../../../../config/axios/httpReuest.js'
export function useButton(scatterChartRef, parentIsBeforeSimulation, parentFilterFields, parentIsSimulation) {
  const LOCKED = '1' // 锁定状态
  const UNLOCKED = '0' // 解锁状态

  /**
   * 处理锁定/解锁
   * @param {Object} item - 当前行数据
   * @param {String} action - 动作类型 'lock' 或 'unlock'
   * @param {Object} params - 请求参数
   * @returns {Promise<Object>} - 返回请求结果
   */
  const handleLockOrUnlock = async (item, action, params) => {
    const loading = ElementPlus.ElLoading.service({
      text: `${action === 'lock' ? '接手/锁定' : '解锁'}中...`,
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const { data } = await post(params)
    if (data.code === 'success') {
      if (params.ac === 'de_gm_takeover') {
        item.is_lock = action === 'lock' ? LOCKED : UNLOCKED // 更新锁定状态
      } else if (params.ac === 'de_gp_takeover') {
        item.is_gp = action === 'lock' ? LOCKED : UNLOCKED // 更新锁定状态
      }else if (params.ac === 'de_rul_takeover') {
        item.is_rul = action === 'lock' ? LOCKED : UNLOCKED // 更新锁定状态
      }
    } else {
      ElementPlus.ElMessage.error(data.text)
    }
    loading.close()
    return data
  }

  const handleGmLock = async (item) => {
    const params = {
      ac: 'de_gm_takeover',
      id_wo: item.id_wo,
      istake: LOCKED,
    }
    const data = await handleLockOrUnlock(item, 'lock', params)
    // 更新GM接手时间
    item.dt_gm = data.data
  }
  const handleGmUnlock = async (item) => {
    const params = {
      ac: 'de_gm_takeover',
      id_wo: item.id_wo,
      istake: UNLOCKED,
    }
    await handleLockOrUnlock(item, 'unlock', params)
  }

  const handleGpLock = async (item, scatterChartRef) => {
    const params = {
      ac: 'de_gp_takeover',
      id_wo: item.id_wo,
      istake: LOCKED,
    }
    const data = await handleLockOrUnlock(item, 'lock', params)
    // 更新GP接手时间
    item.dt_gp = data.data
    // 刷新散点图
    const myChart = scatterChartRef.value[item.id_wo]
    myChart.getChartList(parentIsBeforeSimulation.value, parentFilterFields.value, parentIsSimulation.value)
  }

  const handleGpUnlock = async (item) => {
    const params = {
      ac: 'de_gp_takeover',
      id_wo: item.id_wo,
      istake: UNLOCKED,
    }
    await handleLockOrUnlock(item, 'unlock', params)
  }

  const handleRulLock = async (item, scatterChartRef) => {
    const params = {
      ac: 'de_rul_takeover',
      id_wo: item.id_wo,
      istake: LOCKED,
    }
     const data = await handleLockOrUnlock(item, 'lock', params)
    // 更新RUL接手时间
    item.dt_rul = data.data
    
  }

  const handleRulUnlock = async (item) => {
    const params = {
      ac: 'de_rul_takeover',
      id_wo: item.id_wo,
      istake: UNLOCKED,
    }
    const data = await handleLockOrUnlock(item, 'unlock', params)
  }

  /**
   * GM接手 解锁/锁定
   * @param {Object} item - 当前行数据
   */
  const handleGmLockOrUnlock = (item) => {
    const message = item.is_lock === UNLOCKED ? '确定GM接手吗?接手后将锁定！！！' : '确定要解锁吗?'
    ElementPlus.ElMessageBox.confirm(message, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      if (item.is_lock === UNLOCKED) {
        await handleGmLock(item)
      } else {
        await handleGmUnlock(item)
      }
    })
  }

  /**
   * GP接手 解锁/锁定
   * @param {Object} item - 当前行数据
   */
  const handleGpLockOrUnlock = (item) => {
    const message = item.is_gp === UNLOCKED ? '确定GP接手吗?接手后将保存红框位置，红框将不再随绿框显示！！！' : '确定要解锁吗?'
    ElementPlus.ElMessageBox.confirm(message, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      if (item.is_gp === UNLOCKED) {
        await handleGpLock(item, scatterChartRef)
      } else {
        await handleGpUnlock(item)
      }
    })
  }

    /**
   * GP接手 解锁/锁定
   * @param {Object} item - 当前行数据
   */
  const handleRulLockOrUnlock = (item) => {
    const message = item.is_rul === UNLOCKED ? '确定RUL接手吗?': '确定要解锁吗?'
    ElementPlus.ElMessageBox.confirm(message, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      if (item.is_rul === UNLOCKED) {
        await handleRulLock(item, scatterChartRef)
      } else {
        await handleRulUnlock(item)
      }
    })
  }

  return {
    handleGmLockOrUnlock,
    handleGpLockOrUnlock,
    handleRulLockOrUnlock
  }
}
