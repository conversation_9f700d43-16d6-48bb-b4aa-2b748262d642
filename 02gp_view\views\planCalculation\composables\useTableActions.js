/**
 * 表格操作 composable
 * 提供通用的表格操作方法，如批量操作、确认对话框等
 */

const { ref } = Vue

export function useTableActions(refreshCallback) {

  /**
   * 执行批量操作
   * @param {Object} tableRef - 表格引用
   * @param {Function} apiCall - API 调用函数
   * @param {Object} options - 配置选项
   * @param {string} options.confirmMessage - 确认消息
   * @param {string} options.successMessage - 成功消息
   * @param {string} options.errorMessage - 错误消息
   * @param {Function} options.dataTransform - 数据转换函数
   * @param {boolean} options.needConfirm - 是否需要确认
   */
  const executeBatchAction = async (tableRef, apiCall, options = {}) => {
    const {
      confirmMessage = '确定要执行此操作吗？',
      successMessage = '操作成功',
      errorMessage = '操作失败',
      dataTransform = (data) => data.map(item => item.id),
      needConfirm = true
    } = options

    let loading = null
    try {
      // 创建全局loading遮罩
      loading = ElementPlus.ElLoading.service({
        fullscreen: true,
        lock: true,
        text: '同步中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      // 获取选中数据
      const selectedData = tableRef?.getSelectedData?.()
      if (!selectedData || selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请选择要操作的数据')
        return false
      }

      // 确认对话框
      if (needConfirm) {
        const result = await ElementPlus.ElMessageBox.confirm(
          confirmMessage,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        if (result !== 'confirm') {
          return false
        }
      }

      // 数据转换
      const transformedData = dataTransform(selectedData)

      // 执行 API 调用
      const success = await apiCall(transformedData)
      
      if (success) {
        if (successMessage) {
          ElementPlus.ElMessage.success(successMessage)
        }
        // 刷新数据
        if (refreshCallback) {
          await refreshCallback()
        }
        return true
      } else {
        if (errorMessage) {
          ElementPlus.ElMessage.error(errorMessage)
        }
        return false
      }
    } catch (error) {
      console.error('批量操作失败:', error)
      ElementPlus.ElMessage.error(errorMessage)
      return false
    } finally {
      // 关闭loading遮罩
      if (loading) {
        loading.close()
      }
    }
  }

  /**
   * 检查是否有选中数据
   * @param {Object} tableRef - 表格引用
   * @param {string} method - 获取数据的方法名
   * @returns {Array} 选中的数据
   */
  const getSelectedData = (tableRef, method = 'getSelectedData') => {
    const selectedData = tableRef.value?.[method]?.()
    if (!selectedData || selectedData.length === 0) {
      ElementPlus.ElMessage.warning('请选择要操作的数据')
      return []
    }
    return selectedData
  }

  /**
   * 检查是否选中了一条数据
   * @param {Object} tableRef - 表格引用
   * @param {string} method - 获取数据的方法名
   * @returns {Object|null} 选中的单条数据
   */
  const getSelectedOneData = (tableRef, method = 'getSelectedData') => {
    const selectedData = getSelectedData(tableRef, method)
    if (selectedData.length === 0) {
      return null
    }
    if (selectedData.length > 1) {
      ElementPlus.ElMessage.warning('只能选择一条数据')
      return null
    }
    return selectedData[0]
  }

  /**
   * 创建批量操作函数
   * @param {Function} apiCall - API 调用函数
   * @param {Object} options - 配置选项
   * @returns {Function} 批量操作函数
   */
  const createBatchAction = (apiCall, options = {}) => {
    return (tableRef) => executeBatchAction(tableRef, apiCall, options)
  }

  return {
    executeBatchAction,
    getSelectedData,
    getSelectedOneData,
    createBatchAction
  }
} 