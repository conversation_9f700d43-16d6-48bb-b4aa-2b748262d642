import {
  activeProjectApi,
  activeWipToProjectApi,
  freezeAndUnfreezeApi,
  knowF3ChangeApi,
} from '../../api/calculation.js'
import { useTable } from './composables/useTable.js'
import { useTableActions } from './composables/useTableActions.js'

const { toRefs, defineAsyncComponent, onMounted } = Vue
const PlanCalculation = {
  components: {
    HtVxeTable: defineAsyncComponent(() => import('../../../components/VxeTable/HtVxeTable.js')),
    PagePager: defineAsyncComponent(() => import('../../components/PagePager.js')),
    HtDrawer: defineAsyncComponent(() => import('../../../components/ht.drawer.js')),
    PlanGantt: defineAsyncComponent(() => import('./components/PlanGantt.js')),
    MainUnitGantt: defineAsyncComponent(() => import('./components/MainUnitGantt.js')),
  },
  setup() {
    const {
      state,
      handleFilterChange,
      getTableDataByFrontPage,
      pagePagerState,
      handlePageChange,
      addCustomFilter,
      removeFilter,
      handleSearch: handleTableSearch,
      handleReset: handleTableReset,
      refreshData,
    } = useTable()
    const { getSelectedOneData, createBatchAction } = useTableActions(refreshData)
    const tableRef = Vue.ref(null)
    const currentState = Vue.reactive({
      id: '',
      idWo: '',
      engineType: '',
    })
    // 正式应用Project
    const activeProject = createBatchAction(activeProjectApi, {
      confirmMessage: '是否正式应用该项目',
      successMessage: '项目应用成功',
      errorMessage: '项目应用失败',
    })

    const planGanttVisible = Vue.ref(false)
    // 打开计划甘特图
    const openPlanGantt = () => {
      const oneSelectedData = getSelectedOneData(tableRef)
      if (!oneSelectedData) {
        return
      }
      currentState.id = oneSelectedData.id
      currentState.idWo = oneSelectedData.id_wo
      currentState.engineType = oneSelectedData.str_engine_type
      planGanttVisible.value = true
    }
    const planGanttRef = Vue.ref(null)
    // 保存计划甘特图
    const savePlanGantt = async () => {
      let isTrue = false
      if (!isShowSubUnitGantt.value) {
        isTrue = await saveSubUnitGantt()
      } else {
        isTrue = await saveMainUnitGantt()
      }
      if (isTrue) {
        planGanttVisible.value = false
        // 重新请求表格数据
        await refreshData()
      }
    }

    // 保存子单元体
    const saveSubUnitGantt = () => {
      return planGanttRef.value.saveGantt()
    }

    const mainUnitGanttRef = Vue.ref(null)
    // 保存主单元体
    const saveMainUnitGantt = () => {
      return mainUnitGanttRef.value.saveGantt()
    }

    /* -----------------子单元体甘特图----------------- */
    const isShowSubUnitGantt = Vue.ref(true)

    // 转到子单元体甘特图
    const toSubUnitGantt = (id) => {
      isShowSubUnitGantt.value = !isShowSubUnitGantt.value
    }

    onMounted(() => {
      getTableDataByFrontPage()
    })

    const closePlanGantt = () => {
      planGanttVisible.value = false
      isShowSubUnitGantt.value = true
    }

    const search = Vue.reactive({
      // release开始时间
      dt_release_start: '',
      // release结束时间
      dt_release_end: '',
    })

    const handleSearch = async () => {
      // 先清除之前的搜索条件
      removeFilter('dt_release_start')
      removeFilter('dt_release_end')

      // 添加新的搜索条件
      if (search.dt_release_start) {
        addCustomFilter('dt_release_start', search.dt_release_start)
      }
      if (search.dt_release_end) {
        addCustomFilter('dt_release_end', search.dt_release_end)
      }

      // 重新获取数据
      await handleTableSearch([])
    }

    // 处理release开始时间（不立即过滤，等待用户点击搜索）
    const handleReleaseStartChange = (value) => {
      // 只更新搜索状态，不立即过滤
      search.dt_release_start = value
    }

    // 处理release结束时间（不立即过滤，等待用户点击搜索）
    const handleReleaseEndChange = (value) => {
      // 只更新搜索状态，不立即过滤
      search.dt_release_end = value
    }

    const handleReset = () => {
      // 清除搜索条件
      search.dt_release_start = ''
      search.dt_release_end = ''

      // 清除过滤器
      removeFilter('dt_release_start')
      removeFilter('dt_release_end')

      // 清除表格过滤器
      tableRef.value.clearAll()
      // 重新获取数据
      handleTableReset()
    }
    // 从wip 重新同步
    const activeWipToProject = createBatchAction(activeWipToProjectApi, {
      confirmMessage: '是否从Wip Summary同步数据覆盖当前计划？',
      successMessage: null,
      errorMessage: null,
      dataTransform: (data) => data.map((item) => item.id),
    })

    /* 冻结 */
    const handleFreeze = createBatchAction((ids) => freezeAndUnfreezeApi('1', ids), {
      confirmMessage: '确定要冻结选中的项目吗？',
      successMessage: null,
      errorMessage: null,
    })

    /* 解冻 */
    const handleUnfreeze = createBatchAction((ids) => freezeAndUnfreezeApi('0', ids), {
      confirmMessage: '确定要解冻选中的项目吗？',
      successMessage: null,
      errorMessage: null,
    })

    const handleKnowF3Change = createBatchAction(knowF3ChangeApi, {
      confirmMessage: '确定已知晓F3变化吗？',
      successMessage: null,
      errorMessage: null,
    })

    const handleJumpToMainUnit = () => {
      const oneSelectedData = getSelectedOneData(tableRef)
      if (!oneSelectedData) {
        return
      }
      const idWo = oneSelectedData.id_wo
      com.refreshTab('本体RID', `/Page/?moduleid=1435766989511069696&qrc_str_class=rid&qrc_id_wo=${idWo}`)
    }

    return {
      ...toRefs(state),
      ...toRefs(pagePagerState),
      tableRef,
      handleFilterChange,
      handlePageChange,
      openPlanGantt,
      planGanttVisible,
      currentState,
      planGanttRef,
      savePlanGantt,
      activeProject,
      isShowSubUnitGantt,
      toSubUnitGantt,
      mainUnitGanttRef,
      closePlanGantt,
      search,
      handleSearch,
      handleReleaseStartChange,
      handleReleaseEndChange,
      handleReset,
      activeWipToProject,
      handleFreeze,
      handleUnfreeze,
      handleKnowF3Change,
      handleJumpToMainUnit,
    }
  },
  template: /*html*/ `
    <!-- 搜索 -->
    <div class="m-2 flex items-center gap-2">
      <label class="el-form-item__label text-right">release开始时间:</label>
      <el-date-picker
        v-model="search.dt_release_start"
        type="date"
        placeholder="请选择release开始时间"
        value-format="YYYY-MM-DD"
        @change="handleReleaseStartChange"
      />
      <label class="el-form-item__label text-right">release结束时间:</label>
      <el-date-picker
        v-model="search.dt_release_end"
        type="date"
        placeholder="请选择release结束时间"
        value-format="YYYY-MM-DD"
        @change="handleReleaseEndChange"
      />
      <el-button type="success" @click="handleSearch">搜索</el-button>
      <el-button type="success" @click="handleReset">重置</el-button>

      <el-button type="success" @click="openPlanGantt">计划甘特图</el-button>
      <el-button type="success" @click="() => activeProject(tableRef)">正式应用</el-button>
      <el-button type="success" @click="() => activeWipToProject(tableRef)">重新同步WIP</el-button>
      <el-button type="success" @click="() => handleFreeze(tableRef)">冻结</el-button>
      <el-button type="success" @click="() => handleUnfreeze(tableRef)">解冻</el-button>
      <el-button type="success" @click="() => handleKnowF3Change(tableRef)">已知晓F3变化</el-button>
      <el-button type="success" @click="() => handleJumpToMainUnit(tableRef)">跳转本体RID</el-button>
    </div>
    <div class="mb-2 border-b-2"></div>
    <div class="mx-2" style="height: calc(100vh - 140px);">
      <HtVxeTable
        ref="tableRef"
        :is-show-header-checkbox="true"
        :tableData
        :tableColumns
        :remote="true"
        @filter-change="handleFilterChange"
      >
        <template #checkbox>
          <vxe-column type="checkbox" width="80" fixed="left"></vxe-column>
        </template>
      </HtVxeTable>
      <div class="my-2 border-b-2"></div>
      <PagePager :currentPage :pageSize :total @pageChange="handlePageChange"></PagePager>
    </div>

    <!--    计划甘特图-->
    <HtDrawer
      v-model:visible="planGanttVisible"
      title="计划甘特图"
      @save="savePlanGantt"
      @clear="closePlanGantt"
      :is-show-save="isShowSubUnitGantt"
      btnname="Save & Issued"
    >
      <PlanGantt
        v-if="!isShowSubUnitGantt"
        ref="planGanttRef"
        :id="currentState.id"
        :id-wo="currentState.idWo"
        :engine-type="currentState.engineType"
        @toMainUnitGantt="isShowSubUnitGantt = !isShowSubUnitGantt"
      ></PlanGantt>
      <MainUnitGantt
        v-else
        ref="mainUnitGanttRef"
        :id="currentState.id"
        :id-wo="currentState.idWo"
        @toSubUnitGantt="toSubUnitGantt"
      ></MainUnitGantt>
    </HtDrawer>
  `,
}

export default PlanCalculation
