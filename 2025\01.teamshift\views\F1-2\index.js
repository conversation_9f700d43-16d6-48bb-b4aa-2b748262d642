const { ref, reactive, onMounted, toRefs, defineComponent, nextTick, onUnmounted } = Vue
import { queryHeadData } from '../../api/teams/index.js'
import { getF12Data } from './api/index.js'
// 导入拆分的组件
import SearchForm from './components/SearchForm.js'
import EsnCell from './components/EsnCell.js'
import StartDateCell from './components/StartDateCell.js'
import EndDateCell from './components/EndDateCell.js'
import TaskItem from './components/TaskItem.js'
import VgHeader from './components/VgHeader.js'
import TeamPlan from './components/team.plan.js'
// 主组件
export default defineComponent({
  name: 'F12Page',
  components: {
    SearchForm,
    TaskItem,
    EsnCell,
    StartDateCell,
    EndDateCell,
    VgHeader,
    TeamPlan,
  },
  props: {
    strFlow: String,
    inputIdWo: String,
    inputPlanDate: String,
    inputIdMain: String,
    inputStrFlow: String,
    inputStrEsnType: String,
    inspectType: String,
    inputGroupType: String,
    handoverType: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const loading = ref(false)
    const state = reactive({
      columnList: [],
      showHeader: true,
      loadingData: false,
      tableMaxHeight: 850,
      tableData: [],
    })

    // 判断是否是周末
    const isWeekend = (weekday) => {
      return weekday === '星期六' || weekday === '星期日'
    }

    const xTable = ref(null)
    const searchFormRef = ref(null)
    const teamInfoRef = ref(null)

    // 计算表格高度
    const calculateTableHeight = () => {
      // 获取视口高度
      const viewportHeight = window.innerHeight
      // 获取搜索表单高度
      const searchFormHeight = searchFormRef.value?.$el?.offsetHeight || 0
      // 获取团队信息高度
      const teamInfoHeight = teamInfoRef.value?.$el?.offsetHeight || 0

      // 计算表格可用高度
      let availableHeight = viewportHeight - searchFormHeight - teamInfoHeight - 32 // 32是padding的总高度(上下各16px)

      // 如果在iframe中，需要考虑iframe的padding和margin
      if (window.self !== window.top) {
        availableHeight -= 40 // iframe的padding和margin的总高度
      }

      // 设置最小高度
      state.tableMaxHeight = Math.max(availableHeight, 400)
    }

    // 监听窗口大小变化
    const handleResize = () => {
      calculateTableHeight()
    }

    const loadList = async () => {
      loading.value = true
      await initColumns()
      const data = await fetchF12Data()

      if (xTable.value) {
        await xTable.value.reloadData(data)
        loading.value = false
        // 数据加载完成后重新计算高度
        nextTick(() => {
          calculateTableHeight()
        })
      }
    }

    const initColumns = async () => {
      const searchParams = searchFormRef.value?.searchParams
      const params = {
        str_flow: props.inputStrFlow,
        start_date: (searchParams.dt_date && searchParams.dt_date[0]) || moment().format('YYYY-MM-DD'),
        end_date: (searchParams.dt_date && searchParams.dt_date[1]) || moment().add(14, 'days').format('YYYY-MM-DD'),
      }
      try {
        const response = await queryHeadData(params)
        state.columnList = response
      } catch (error) {
        console.error(error)
      }
    }

    const fetchF12Data = async () => {
      state.loadingData = true
      const searchParams = searchFormRef.value?.searchParams
      try {
        const params = {
          start_date: (searchParams.dt_date && searchParams.dt_date[0]) || moment().format('YYYY-MM-DD'),
          end_date: (searchParams.dt_date && searchParams.dt_date[1]) || moment().add(14, 'days').format('YYYY-MM-DD'),
          str_esn: searchParams.str_esn,
          id_teams: searchParams.id_teams,
          id_shifts: searchParams.id_shifts,
          str_types: searchParams.str_types,
        }
        const response = await getF12Data(params)
        state.tableData = response || []
        state.loadingData = false
      } catch (error) {
        console.error(error)
        state.loadingData = false
      }
    }

    const handleHeaderCellClassName = ({ column }) => {
      // 只处理展开状态下的周末列
      if (column && column.title) {
        if (column.title === '星期六' || column.title === '星期日') {
          return 'bg-weekend'
        }
      }
      return ''
    }

    const handleSearch = (searchParams) => {
      console.log(searchParams)
      state.searchParams = searchParams
      loadList()
    }

    const isExistData = (row, column) => {
      const planList = row.plan
      const planDay = column.day
      const taskIndex = planList.findIndex((item) => item.plan_date === planDay)
      return taskIndex !== -1
    }

    const isShowAddButton = (row, column) => {
      // 判断是否在项目开始和结束日期之间并且包含
      return moment(column.day).isBetween(row.project_start, row.project_end, undefined, '[]')
    }

    onMounted(() => {
      calculateTableHeight()
      loadList()
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', handleResize)
    })

    const handleRefresh = () => {
      handleSearch(state.searchParams)
    }

    const teamPlanVisible = ref(false)
    const teamPlanRef = ref(null)
    const currentRow = ref({})
    const currentColumn = ref({})

    const handleAddTask = (row, column) => {
      teamPlanVisible.value = true
      currentRow.value = row
      currentColumn.value = column
    }

    return {
      ...toRefs(state),
      loading,
      searchFormRef,
      teamInfoRef,
      teamPlanVisible,
      teamPlanRef,
      currentRow,
      currentColumn,
      // 方法
      handleSearch,
      handleHeaderCellClassName,
      isExistData,
      handleRefresh,
      isWeekend,
      isShowAddButton,
      handleAddTask,
    }
  },
  template: /*html*/ `
    <div class="p-4">
      <SearchForm ref="searchFormRef" @search="handleSearch" />

      <vxe-table
        :data="tableData"
        ref="xTable"
        border
        :header-cell-class-name="handleHeaderCellClassName"
        :header-cell-style="{color:'#fff',border:'0.01rem solid #fff'}"
        :column-config="{resizable: true,minWidth:170}"
        :loading="loadingData"
        :height="tableMaxHeight"
        :show-header="true"
      >
        <template>
          <!-- 固定列 -->
          <vxe-column type="seq" title="序号" width="60" align="center" fixed="left"></vxe-column>
          <vxe-column title="ESN" field="esn" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <EsnCell :row="row" :handover-type="handoverType" />
            </template>
          </vxe-column>
          <vxe-column title="Team(Name)" field="team_name" width="120" align="center" fixed="left">
            <template #default="{ row }">
              <div class="truncate" :title="row.team_name">
                {{ row.team_name }}({{ row.team_Leader }})
              </div>
            </template>
          </vxe-column>
          <vxe-column title="Shift" field="str_shift" width="60" align="center" fixed="left"></vxe-column>
          <vxe-column title="Type" field="str_task_type" width="70" align="center" fixed="left"></vxe-column>
          <vxe-column title="Start Date" field="start_date" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <StartDateCell :row="row" />
            </template>
          </vxe-column>
          <vxe-column title="End Date" field="end_date" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <EndDateCell :row="row" />
            </template>
          </vxe-column>
          <vxe-column title="TAT" field="dec_tat" width="60" align="center" fixed="left"></vxe-column>

          <vxe-colgroup v-for="(column, index) in columnList" :key="index" :title="column.str_week">
            <!-- 星期 -->
            <vxe-column :title="column.day">
              <template #default="{ row }">
                <!-- 是否有数据 -->
                <div v-if="isExistData(row, column)" class="h-full w-full">
                  <TaskItem
                    :row="row"
                    :column="column"
                    @refresh="handleRefresh"
                    :handover-type="handoverType"
                    :str-flow="inputStrFlow"
                    :str-group="inputGroupType"
                  />
                </div>
                <!-- 任务为空 -->
                <div v-else class="h-full w-full">
                  <div v-if="isShowAddButton(row, column)" class="flex h-full items-center justify-center">
                    <el-button type="primary" @click="handleAddTask(row, column)">Add</el-button>
                  </div>
                </div>
              </template>
            </vxe-column>
          </vxe-colgroup>
        </template>
      </vxe-table>

      <TeamPlan
        v-if="teamPlanVisible"
        ref="teamPlanRef"
        v-model:visible="teamPlanVisible"
        mode="add"
        :current-row="currentRow"
        :current-column="currentColumn"
        @refresh="handleRefresh"
      />
    </div>
  `,
})
