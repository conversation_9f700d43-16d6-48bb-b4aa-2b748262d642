import { post } from '../../../utils/request.js'
/**
 * 获取交接列表
 */
export const getHandoverTableList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_handover_list',
    params,
  })
}

/**
 * 获取交接表头
 */
export const getHandoverTableColumns = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_handover_list_head',
  })
}

/**
 * 获取交接详情表数据
 */
export const getHandoverDetailList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_handover_list_detail',
    ...params,
  })
}
