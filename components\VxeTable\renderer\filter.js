import FilterComplex from './components/FilterComplex.js'
import FilterInput from './components/FilterInput.js'
import FilterCalendar from './components/FilterCalendar.js'

const { h } = Vue

VXETable.renderer.add('FilterInput', {
  // 筛选模板
  renderFilter(renderOpts, params) {
    return h(FilterInput, {
      params,
    })
  },
  // 重置数据方法
  filterResetMethod(params) {
    const { options } = params
    options.forEach((option) => {
      option.data = ''
    })
  },
  // 重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
  filterRecoverMethod({ option }) {
    option.data = ''
  },
  // 筛选方法
  filterMethod(params) {
    const { option, row, column } = params
    const { data } = option
    const cellValue = row[column.field]
    if (cellValue) {
      // 不区分大小写
      return String(cellValue).toLowerCase().indexOf(data.toLowerCase()) > -1
    }
    return false
  },
})

// 创建一个条件的渲染器
VXETable.renderer.add('FilterComplex', {
  // 不显示底部按钮，使用自定义按钮
  showFilterFooter: false,
  // 筛选模板
  renderFilter(renderOpts, params) {
    return h(FilterComplex, {
      params,
    })
  },
  // 重置数据方法
  filterResetMethod(params) {
    const { options } = params
    options.forEach((option) => {
      option.data = { type: 'less', name: '' }
    })
  },
  // 筛选数据方法
  filterMethod(params) {
    const { option, row, column } = params
    const { data } = option
    const cellValue = row[column.field]
    const { type, name } = data
    // 判断输入的是否是数字
    const isNumber = /^\d+$/.test(name)
    if (cellValue && isNumber) {
      if (type === 'less') {
        return cellValue < name
      } else if (type === 'eq') {
        return Number(cellValue) === Number(name)
      } else if (type === 'greater') {
        return cellValue > name
      }
    }
    return false
  },
})

// 创建日期筛选渲染器
VXETable.renderer.add('FilterCalendar', {
  // 显示底部按钮
  showFilterFooter: true,
  // 筛选模板
  renderFilter(renderOpts, params) {
    const { column } = params
    // 确保列有初始的过滤器选项
    if (!column.filters || !column.filters.length) {
      column.filters = [{ data: null }]
    }

    // 为每个列创建独立的过滤器实例
    return h(
      'div',
      {
        class: 'filter-wrapper',
      },
      [
        h(FilterCalendar, {
          key: column.field,
          params,
        }),
      ],
    )
  },
  // 重置数据方法
  filterResetMethod(params) {
    const { options } = params
    if (options) {
      options.forEach((option) => {
        option.data = null
      })
    }
  },
  // 重置筛选复原方法
  filterRecoverMethod({ option }) {
    if (option) {
      option.data = null
    }
  },
  // 筛选方法
  filterMethod({ option, row, column }) {
    const cellValue = row[column.field]
    if (!cellValue || !option?.data) {
      return false
    }

    try {
      // 使用 moment 进行日期比较
      const filterDate = moment(option.data)
      const cellDate = moment(cellValue)

      // 使用 isSame 方法比较日期是否相同（忽略时分秒）
      return filterDate.isSame(cellDate, 'day')
    } catch (e) {
      console.error('日期比较出错：', e)
      return false
    }
  },
})
