// 作用: 基础信息页面的添加和编辑
import { post } from "../../../config/axios/httpReuest.js";
import { useSelectList } from "../../hooks/useSelectList.js";
import { useConversion } from "./useConversion.js";
import { useRelation } from "./useRelation.js";
import { useWorkWear } from "./useWorkWear.js";

const { reactive, ref, onMounted, toRefs, watch } = Vue;
const BasePageAddAndEdit = {
  setup() {
    // 基础信息表单
    const basePageAndEditForm = ref({
      model: "",
      flow: "",
      type: "",
      smObject: null,
      occupySite: "",
      duration: "",
      taskName: "",
      remark: "",
      int_state: 1,
    });
    // 基础信息表单校验规则
    const formRules = reactive({
      model: [{ required: true, message: "请选择机型", trigger: "change" }],
      flow: [{ required: true, message: "请选择Flow", trigger: "change" }],
      // type: [{required: true, message: '请选择Type', trigger: 'change'}],
      // smObject: [{required: true, message: '请选择SM', trigger: 'change'}],
      duration: [
        { required: true, message: "请输入工期", trigger: "change" },
        { pattern: /^\d+$/, message: "请输入大于等于0的整数", trigger: "change" },
      ],
      taskName: [
        { required: true, message: "请输入任务名称", trigger: "change" },
        { max: 50, message: "长度限制50个字符", trigger: "change" },
      ],
      remark: [{ max: 50, message: "长度限制50个字符", trigger: "change" }],
    });

    // 工装信息
    const {
      workWearForm,
      initWorkWearForm,
      workWearNameForm,
      visibleChangeWorkWearName,
      changeWorkWearName,
    } = useWorkWear();

    // 关联信息
    const {
      relationForm,
      relationTaskList,
      getRelationTaskList,
      initRelationForm,
    } = useRelation();

    // 机型变化
    const changeModel = (model) => {
      initWorkWearForm();
      initRelationForm();
      basePageAndEditForm.model = model;
    };
    // flow变化
    const changeFlow = (flow) => {
      initWorkWearForm();
      //initRelationForm();
      basePageAndEditForm.flow = flow;
    };

    // 监听机型和flow变化
    watch(
      () => [
        basePageAndEditForm.value.flow,
        basePageAndEditForm.value.model,
        basePageAndEditForm.value.id,
      ],
      ([flow, model, id]) => {
        getRelationTaskList(flow, model, id);
      }
    );

    // form表单是否可编辑
    const isDisabled = ref(false);
    // 获取编辑的数据
    const getEditForm = (title, id) => {
      if (title === "修改") {
        isDisabled.value = false;
        getFormDataById(id);
      } else {
        isDisabled.value = true;
      }
    };

    const selectList = reactive({
      flowList: [],
      smList: [],
      occupyList: [],
    });

    const { getFlowList, getSmList, getOccupyList } = useSelectList();

    onMounted(async () => {
      // 获取Flow列表
      selectList.flowList = await getFlowList();
      // 获取SM列表
      selectList.smList = await getSmList();
      //获取站位列表
      selectList.occupyList = await getOccupyList();

    });

    const { apiToForm, formToApi } = useConversion();

    const basePageAndEditFormRef = ref(null);
    // 校验
    const validate = async () => {
      return await basePageAndEditFormRef.value.validate();
    };

    const saveAndUpdateApi = async (form, type = "add") => {
      const rest = Object.assign({}, form);
      rest.relateTasks = relationForm.list
        .map((item) => {
          return {
            id_relate_task: item.task,
            int_task_relate_type: item.relation,
          };
        })
        .filter(
          (item) =>
            item.id_relate_task !== "" && item.int_task_relate_type !== ""
        );
      rest.workClothes = workWearForm.list
        .map((item) => {
          return {
            id_workclothes: item.name,
            int_num: item.quantity,
            int_worktype: item.int_worktype,
          };
        })
        .filter((item) => item.id_workclothes !== "" && item.int_num !== "" && item.int_worktype !== "");
      const param = {
        ac: type === "add" ? "gp_task_info_add" : "gp_task_info_edit",
        data: rest,
      };
      const { data } = await post(param);
      if (data.code === "error") {
        ElementPlus.ElMessage.error(data.text);
        return false;
      }
      ElementPlus.ElMessage.success(data.text);
      return true;
    };

    /**
     * @description 保存或更新
     * @param title
     * @returns {Promise<boolean>}
     */
    const saveAndUpdate = async (title) => {
      const isValidate = await validate();
      if (!isValidate) return false;
      const form = formToApi(basePageAndEditForm.value);
      if (title === "新增") {
        return await saveAndUpdateApi(form);
      } else {
        return await saveAndUpdateApi(form, "edit");
      }
    };
    /**
     * @description 根据id获取表单数据
     * @param {string} id - 表单id
     */
    const getFormDataById = async (id) => {
      const param = {
        ac: "gp_task_info_search_by_id",
        id,
      };
      const { data } = await post(param);
      if (data.code === "error") {
        ElementPlus.ElMessage.error(data.text);
        return;
      }
      const { relateTasks, workClothes, ...res } = data.data;
      basePageAndEditForm.value = apiToForm(res);
      // 工装信息
      workWearForm.list = workClothes?.map((item) => {
        return {
          name: item.id_workclothes,
          quantity: item.int_num,
        };
      }) || [{ name: "", number: "" }];
      // 关联信息
      relationForm.list = relateTasks?.map((item) => {
        return {
          task: item.id_relate_task,
          relation: item.int_task_relate_type,
        };
      }) || [{ task: "", relation: "" }];
    };

    return {
      basePageAndEditFormRef,
      basePageAndEditForm,
      changeModel,
      formRules,
      getEditForm,
      isDisabled,
      ...toRefs(selectList),
      saveAndUpdate,
      workWearForm,
      workWearNameForm,
      visibleChangeWorkWearName,
      changeWorkWearName,
      relationForm,
      relationTaskList,
    };
  },
  /*html*/
  template: `
        <div class="border-l-4 border-l-emerald-700 text-emerald-700">
            <span class="text-xl pl-4">基础信息</span>
        </div>
        <div class="border-b-2 my-2"></div>
        <el-form
                ref="basePageAndEditFormRef"
                :model="basePageAndEditForm"
                label-width="90px"
                :rules="formRules"
                :disabled="isDisabled"
        >
            <div class="grid grid-cols-3 gap-4">
                <el-form-item label="机型:" prop="model">
                    <el-select v-model="basePageAndEditForm.model" placeholder="请选择" clearable filterable @change="changeModel">
                        <el-option label="CFM56" value="CFM56"></el-option>
                        <el-option label="LEAP" value="LEAP"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Flow:" prop="flow">
                    <el-select v-model="basePageAndEditForm.flow" placeholder="请选择" clearable filterable @change="changeFlow">
                        <el-option v-for="item in flowList" :key="item.value" :label="item.label"
                                   :value="item.label"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Type:" prop="type">
                    <el-select v-model="basePageAndEditForm.type" placeholder="请选择" clearable filterable>
                        <el-option label="Core" value="Core"></el-option>
                        <el-option label="Fan" value="Fan"></el-option>
                        <el-option label="LPT" value="LPT"></el-option>
                        <el-option label="B1" value="B1"></el-option>
                    </el-select>
                </el-form-item>
            </div>
            <div class="grid grid-cols-3 gap-4">
                <el-form-item label="SM:" prop="smObject">
                    <el-select v-model="basePageAndEditForm.smObject" placeholder="请选择" clearable filterable
                               value-key="str_value">
                        <el-option v-for="item in smList" :key="item.str_value" :label="item.str_key"
                                   :value="item"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="站位:" prop="occupySite">
                    <el-select v-model="basePageAndEditForm.occupySite" placeholder="请选择" clearable filterable value-key="id">
                    <el-option v-for="item in occupyList" :key="item.id" :label="item.str_code"
                    :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="工期(天):" prop="duration">
                    <el-input v-model="basePageAndEditForm.duration" placeholder="请输入数字" clearable></el-input>
                </el-form-item>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <el-form-item label="任务名称:" prop="taskName">
                    <el-input v-model="basePageAndEditForm.taskName" placeholder="请输入内容, 长度限制50个字符"
                              clearable></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="int_state" class="items-center">
                <el-switch v-model="basePageAndEditForm.int_state" inline-prompt :active-value="1" active-text="启用"
                     inactive-text="禁用" :inactive-value="0"
                     inactive-color="red" active-color="green"></el-switch>
                </el-form-item>
            </div>
            <div class="grid grid-cols-1 gap-4">
                <el-form-item label="备注:" prop="remark">
                    <el-input v-model="basePageAndEditForm.remark" type="textarea"
                              placeholder="请输入内容, 长度限制50个字符" clearable></el-input>
                </el-form-item>
            </div>
        </el-form>
        <div class="border-l-4 border-l-emerald-700 text-emerald-700">
            <span class="text-xl pl-4">工装信息</span>
            <div style="float: right;"><el-button type="primary" @click="workWearForm.list.push({ name: '', int_worktype: '' })">新增</el-button></div>
            
        </div>
        <div class="border-b-2 my-2"></div>
        <div v-for="(item, index) in workWearForm.list" :key="index">
            <el-form :model="item" label-width="90px" inline class="grid grid-cols-4 gap-4">
                <el-form-item label="名称:" prop="name">
                    <el-select v-model="item.name" placeholder="请选择" clearable filterable
                               @visible-change="(visible) => visibleChangeWorkWearName(visible,index)"
                               @change="(value) => changeWorkWearName(value,index)"
                    >
                        <el-option v-for="(item,index1) in workWearNameForm.nameList[index]" :key="index1"
                                   :label="item.label"
                                   :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="操作:" prop="int_worktype">
                <el-select v-model="item.int_worktype" placeholder="请选择" clearable filterable>
                <el-option label="占用工装" value="1"></el-option>
                <el-option label="释放工装" value="2"></el-option>
                </el-select>
                </el-form-item>
                <el-form-item label="数量:" prop="quantity">
                    <el-input v-model="item.quantity" placeholder="请输入数字" clearable></el-input>
                </el-form-item>
                <el-form-item>
                   
                    <el-button  type="danger" @click="workWearForm.list.splice(index, 1)">删除
                    </el-button>
                </el-form-item>
            </el-form>
        </div>

        <div class="border-l-4 border-l-emerald-700 text-emerald-700">
            <span class="text-xl pl-4">关联信息</span>
            <div style="float: right;">  <el-button type="primary" @click="relationForm.list.push({ task: '', relation: '' })">新增
            </el-button></div>
        </div>
        <div class="border-b-2 my-2"></div>
        <div v-for="(item, index) in relationForm.list" :key="index">
            <el-form :model="item" label-width="90px" inline class="grid grid-cols-4 gap-4">
                <el-form-item label="关联任务:" prop="task">
                    <el-select v-model="item.task" placeholder="请选择" clearable filterable>
                        <el-option v-for="item in relationTaskList" :key="item.id" :label="item.str_task_name"
                                   :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="关联关系:" prop="relation">
                    <el-select v-model="item.relation" placeholder="请选择" clearable>
                        <el-option label="S-S" value="1"></el-option>
                        <el-option label="S-F" value="3"></el-option>
                        <el-option label="F-F" value="2"></el-option>
                        <el-option label="F-S" value="0"></el-option>
                    </el-select>
                </el-form-item>
              <el-form-item>
                  
                    <el-button v-if="index !== 0" type="danger" @click="relationForm.list.splice(index, 1)">删除
                    </el-button>
                </el-form-item>
            </el-form>
        </div>
    `,
};
export default BasePageAddAndEdit;
