const { reactive } = Vue
export default {
  name: 'FilterInput',
  props: {
    params: Object,
  },
  setup(props) {
    const inputState = reactive({
      option: null,
    })

    const load = () => {
      const { params } = props
      if (params) {
        const { column } = params
        const option = column.filters[0]
        inputState.option = option
      }
    }

    const changeOptionEvent = () => {
      const { params } = props
      const { option } = inputState
      if (params && option) {
        const { $panel } = params
        const checked = !!option.data
        $panel.changeOption({}, checked, option)
      }
    }

    const keyupEvent = ({ $event }) => {
      const { params } = props
      if (params) {
        const { $panel } = params
        if ($event.keyCode === 13) {
          $panel.confirmFilter($event)
        }
      }
    }

    load()

    return {
      inputState,
      changeOptionEvent,
      keyupEvent,
    }
  },
  template: /*html*/ `
    <vxe-input
      type="text"
      v-model.trim="inputState.option.data"
      placeholder="支持回车搜索"
      @keyup="keyupEvent"
      @change="changeOptionEvent"
    ></vxe-input>
  `,
}
