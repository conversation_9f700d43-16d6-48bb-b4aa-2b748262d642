const { reactive, ref, shallowRef, onMounted, onUnmounted } = Vue
import { useChartConfig } from './hooks/useChartConfig.js'
import { useTableConfig } from './hooks/useTableConfig.js'
import { useFormConfig } from './hooks/useFormConfig.js'
import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'
import { useApi } from '../../hooks/useApi.js'
/**
 * 发动机交付预测趋势图
 */
const EngineDeliveryChart = {
  setup() {
    const filterFields = ref([])
    // 引入配置
    const { form, resetForm } = useFormConfig()
    const {
      chartRef,
      myChart,
      timeRangeType,
      engineDeliveryData,
      getChartOption,
      handleResize,
      changeTimeRangeType,
      generateDailyData,
    } = useChartConfig()
    const { columns, tableData, drawerVisible, getDetailData } = useTableConfig(filterFields)

    const engineTypes = ref([
      { label: 'CFM56', value: 'CFM56' },
      { label: 'LEAP', value: 'LEAP' },
    ])

    const { getCommonOptionApi } = useApi()
    const repairTypes = ref([])

    /**
     * 获取维修类型
     */
    const getRepairTypes = async () => {
      repairTypes.value = await getCommonOptionApi('Maintenace')
    }

    // 查询图表数据
    const getEngineDeliveryChart = async () => {
      const filter_fields = Object.entries(form).reduce((acc, [key, value]) => {
        if (value && value.length > 0) {
          const fieldMap = {
            f4_1BeginTime: 'dt_f41_date',
            realeaseTime: 'dt_release_date',
            f2_3ClosedTime: 'dt_f23_date',
            engineType: 'str_engine_type',
            repairType: 'str_maintenance_type',
          }
          acc.push({
            str_key: fieldMap[key],
            str_value: value,
          })
        }
        return acc
      }, [])
      filterFields.value = filter_fields

      await generateDailyData(filterFields.value)
      myChart.value?.setOption(getChartOption(timeRangeType.value))
    }

    // 生命周期钩子
    onMounted(async () => {
      await getRepairTypes()
      await getEngineDeliveryChart()
      myChart.value = echarts.init(chartRef.value)
      myChart.value.setOption(getChartOption(timeRangeType.value))

      // 将 timeRangeType 添加到 window 对象，以便在其他地方访问
      window.timeRangeType = timeRangeType

      // 添加点击事件监听
      myChart.value.on('click', async (params) => {
        if (params.componentType === 'series') {
          await getDetailData(params.name, params.seriesName)
        }
      })

      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      delete window.timeRangeType
      myChart.value?.dispose()
    })

    return {
      engineTypes,
      repairTypes,
      form,
      getEngineDeliveryChart,
      resetForm,
      chartRef,
      timeRangeType,
      changeTimeRangeType,
      engineDeliveryData,
      columns,
      tableData,
      drawerVisible,
    }
  },
  template: /*html*/ `
    <div class="flex h-screen flex-col bg-slate-800">
      <div class="h-30 pt-4">
        <el-form :model="form" :inline="true" class="engine-form mx-5">
          <el-form-item label="F4-1 Begin Time:">
            <el-date-picker
              v-model="form.f4_1BeginTime"
              type="daterange"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="Realease Time:">
            <el-date-picker
              v-model="form.realeaseTime"
              type="daterange"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="F2/3 Closed Time:">
            <el-date-picker
              v-model="form.f2_3ClosedTime"
              type="daterange"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="机型:">
            <el-select v-model="form.engineType" placeholder="请选择机型" clearable class="!w-[220px]">
              <el-option
                v-for="item in engineTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="维修类型:">
            <el-select v-model="form.repairType" placeholder="请选择维修类型" clearable class="!w-[220px]" multiple>
              <el-option
                v-for="item in repairTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getEngineDeliveryChart">查询</el-button>
            <el-button type="info" @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="flex items-center justify-between">
          <div class="ml-4 grid grid-cols-1 gap-4 md:grid-cols-3">
            <div
              v-for="(value, key) in engineDeliveryData"
              :key="key"
              class="flex flex-row justify-center space-x-1 rounded-lg bg-white bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-2 shadow-md"
            >
              <div class="flex space-x-1">
                <div v-for="(name, index) in key" :key="index" class="border border-cyan-500 p-2 text-white">
                  {{ name }}
                </div>
              </div>
              <div class="flex space-x-1">
                <div v-for="(digit, index) in value" :key="index" class="border border-cyan-500 p-2 text-white">
                  {{ digit }}
                </div>
              </div>
            </div>
          </div>
          <div class="mr-4">
            <el-radio-group v-model="timeRangeType" @change="changeTimeRangeType">
              <el-radio-button label="0">天</el-radio-button>
              <el-radio-button label="1">月</el-radio-button>
              <el-radio-button label="2">季度</el-radio-button>
              <el-radio-button label="3">年</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="corner-border m-4 flex-auto overflow-hidden">
        <div ref="chartRef" class="h-full w-full overflow-x-auto"></div>
      </div>
      <!-- 抽屉 -->
      <el-drawer v-model="drawerVisible" size="85%" :destroy-on-close="true" class="my_drawer" :show-close="false">
        <template #title>
          <div class="flex items-center justify-between">
            <div class="text-white">零件清单</div>
            <el-button type="danger" @click="drawerVisible = false">关闭</el-button>
          </div>
        </template>
        <div class="flex items-center justify-end">
          <div class="text-black">总计: {{ tableData.length || 0 }} 条</div>
        </div>
        <div style="height: calc(100% - 50px)">
          <HtVxeTable :tableData="tableData" :tableColumns="columns" :showOverflow="true" />
        </div>
      </el-drawer>
    </div>
  `,
  components: {
    HtVxeTable,
  },
}

export default EngineDeliveryChart
