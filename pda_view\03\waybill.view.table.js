import ErrorComponent from '../../components/error.component.js';
import LoadingComponent from '../../components/loading.component.js';
import { post } from '../../config/axios/httpReuest.js';
import { currentNodeKey } from '../../config/keys.js';
import { useTableColumn } from './useTableColumn.js';

const { defineAsyncComponent, inject, ref, onMounted } = Vue;

export default {
  name: 'WaybillViewTable',
  components: {
    HtVxeTable: defineAsyncComponent({
      loader: () => import('../components/ht.vxe.table.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup() {
    const { getTableColumn } = useTableColumn();
    const tableColumn = getTableColumn();

    // get current node
    const currentNode = inject(currentNodeKey);
    const tableData = ref([]);
    const totalNum = ref(0);
    // get table data by current node
    const getTableDataByCurrentNode = async () => {
      const params = {
        ac: 'pda_waybill',
        str_type: currentNode,
      };
      const { data } = await post(params);
      if (data.code === 'success') {
        tableData.value = data.data;
        totalNum.value = data.data.length;
      } else {
        tableData.value = [];
        totalNum.value = 0;
        ElementPlus.ElMessage.error(data.text);
      }
    };

    onMounted(() => {
      getTableDataByCurrentNode();
    });
    return {
      tableColumn,
      tableData,
      totalNum,
    };
  },
  // language=HTML
  template: `
    <HtVxeTable :tableData :totalNum :tableColumn></HtVxeTable>
  `,
};
