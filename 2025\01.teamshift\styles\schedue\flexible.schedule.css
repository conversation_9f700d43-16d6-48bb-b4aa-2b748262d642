.custom-calendar {
  --el-calendar-cell-width: 14.28%;
}

.calendar-cell {
  padding: 8px;
  min-height: 120px;
  transition: all 0.2s ease;
}

.calendar-cell:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* 日历今天的样式 */
.el-calendar-table .el-calendar-day:not(.is-selected):hover {
  background-color: #dbeafe;
}

.el-calendar-table .is-today {
  /* background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; */
  color: black;
  font-weight: 600;
}

.el-calendar-table .is-today .calendar-cell {
  color: white;
}

/* 表单项样式优化 */
.el-form-item__label {
  font-weight: 500;
  color: #374151;
}

.el-select,
.el-time-picker {
  --el-select-border-color-hover: #3b82f6;
  --el-select-border-color-focus: #3b82f6;
}

/* 标签样式 */
.el-tag {
  border-radius: 6px;
  font-weight: 500;
}

.el-tag--warning {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  border: none;
}

/* 卡片阴影效果 */
.shadow-sm {
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .schedule-dialog {
    width: 95% !important;
    margin: 5vh auto;
  }

  .calendar-cell {
    min-height: 80px;
    padding: 4px;
  }

  .grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.schedule-dialog .el-dialog {
  animation: fadeInUp 0.3s ease;
}

/* 渐变边框效果 */
.border-l-3 {
  border-left-width: 3px;
}

/* 自定义滚动条 */
.space-y-4::-webkit-scrollbar {
  width: 6px;
}

.space-y-4::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.space-y-4::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border-radius: 3px;
}

.space-y-4::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
}
