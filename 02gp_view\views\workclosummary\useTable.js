
import { queryWorkClothesList ,queryworkclothesById} from '../../api/workcloth.js';

const { reactive, onMounted, ref } = Vue;

export function useTable() {
  const state = reactive({
    tableData: [],
    tableColumns: [],
  });
  const pagePagerState = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取表格列
  const getTableColumns = () => {
    state.tableColumns = [{
      field: 'str_name',
      title: '工装名称',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    }, {
      field: 'str_code',
      title: 'P/N',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    }, {
      field: 'int_total',
      title: '工装总量',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'int_num',
      title: '目前占用量',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'int_residue',
      title: '剩余数量',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'str_name',
      title: '工装名称',
      minWidth: 100,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    ];
  };
 
  onMounted(() => {
    getTableColumns();
  });

  // 过滤
  const handleFilterChange = () => {

  };
  /**
   * @description 获取表格数据通过前端分页
   * @param {number} currentPage - 当前页
   * @param {number} pageSize - 每页条数
   * @param {array} queryLists - 查询条件
   * @return {Promise<void>}
   */
  const getTableDataByFrontPage = async (currentPage = 1, pageSize = 10, queryLists = []) => {
    const tableData = await queryWorkClothesList(queryLists);
    const start = (currentPage - 1) * pageSize;
    const end = currentPage * pageSize;
    state.tableData = tableData.slice(start, end);
    pagePagerState.total = tableData.length;
  };
  const handlePageChange = async (currentPage) => {
    pagePagerState.currentPage = currentPage.currentPage;
    pagePagerState.pageSize = currentPage.pageSize;
    await getTableDataByFrontPage(currentPage.currentPage, currentPage.pageSize);
  };

  return {
    state,
    pagePagerState,
    handleFilterChange,
    getTableDataByFrontPage,
    handlePageChange,

    
  };
}
