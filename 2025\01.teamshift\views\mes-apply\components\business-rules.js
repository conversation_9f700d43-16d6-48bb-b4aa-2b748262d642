import { MES_CONFIG } from './config.js'

// 引入moment.js进行时间处理
// 注意：需要在页面中引入moment.js库
// <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>

// ==================== 业务规则工具类 ====================
export const MesBusinessRules = {
  // 状态检查
  canPerformAction(status, action) {
    const permissions = MES_CONFIG.permissions
    switch (action) {
      case 'edit':
        return permissions.canEdit.includes(status)
      case 'submit':
        return permissions.canSubmit.includes(status)
      case 'confirm':
        return permissions.canConfirm.includes(status)
      default:
        return false
    }
  },
  
  // 获取状态样式
  getStatusStyle(status) {
    return MES_CONFIG.status.styles[status] || { type: 'info', text: '未知' }
  },
  
  // 生成工时详情 - 使用moment.js
  generateWorkTimeDetails(startDate, endDate, existingDetails = []) {
    console.log('🔧 generateWorkTimeDetails 被调用:', { startDate, endDate, existingCount: existingDetails.length })
    
    if (!startDate || !endDate) {
      console.log('❌ 日期为空，返回空数组')
      return []
    }
    
    // 使用moment.js处理日期
    const start = moment(startDate, 'YYYY-MM-DD')
    const end = moment(endDate, 'YYYY-MM-DD')
    
    if (!start.isValid() || !end.isValid()) {
      console.log('❌ 日期格式无效')
      return []
    }
    
    const details = []
    const defaultWork = MES_CONFIG.form.defaultWorkTime
    
    console.log('📅 日期范围:', { start: start.format('YYYY-MM-DD'), end: end.format('YYYY-MM-DD') })
    
    // 使用moment.js遍历日期范围
    const current = start.clone()
    while (current.isSameOrBefore(end)) {
      const dateString = current.format('YYYY-MM-DD')
      const existingDetail = existingDetails.find(d => d.date === dateString)
      
      if (existingDetail) {
        details.push(existingDetail)
      } else {
        const newDetail = {
          id: `${Date.now()}_${current.valueOf()}`,
          date: dateString,
          startTime: defaultWork.startTime,
          endTime: defaultWork.endTime,
          appliedHours: defaultWork.appliedHours
        }
        details.push(newDetail)
      }
      
      current.add(1, 'day')
    }
    
    console.log('✅ 最终生成的详情数量:', details.length)
    return details
  },
  
  // 计算工时 - 使用moment.js
  calculateHours(startTime, endTime) {
    if (!startTime || !endTime) return 0
    
    // 使用moment.js解析时间
    const start = moment(`2024-01-01 ${startTime}`, 'YYYY-MM-DD HH:mm')
    const end = moment(`2024-01-01 ${endTime}`, 'YYYY-MM-DD HH:mm')
    
    if (!start.isValid() || !end.isValid()) return 0
    
    // 计算时间差（小时）
    const diff = end.diff(start, 'hours', true)
    return diff > 0 ? Math.round(diff * 10) / 10 : 0 // 保留一位小数
  },
  
  // 表单验证 - 使用moment.js
  validateForm(formData) {
    const errors = []
    
    if (!formData.person) errors.push('请选择人员')
    if (!formData.startDate) errors.push('请选择开始日期')
    if (!formData.endDate) errors.push('请选择结束日期')
    
    // 使用moment.js验证日期范围
    if (formData.startDate && formData.endDate) {
      const startDate = moment(formData.startDate, 'YYYY-MM-DD')
      const endDate = moment(formData.endDate, 'YYYY-MM-DD')
      
      if (!startDate.isValid()) errors.push('开始日期格式无效')
      if (!endDate.isValid()) errors.push('结束日期格式无效')
      
      if (startDate.isValid() && endDate.isValid() && !endDate.isAfter(startDate)) {
        errors.push('结束日期必须晚于开始日期')
      }
    }
    
    if (!formData.reason?.trim()) errors.push('请填写申请事由')
    
    // 验证工时详情 - 使用moment.js
    if (formData.details && formData.details.length > 0) {
      formData.details.forEach((detail, index) => {
        if (!detail.startTime) errors.push(`${detail.date}：请选择开始时间`)
        if (!detail.endTime) errors.push(`${detail.date}：请选择结束时间`)
        
        if (detail.startTime && detail.endTime) {
          const startTime = moment(`2024-01-01 ${detail.startTime}`, 'YYYY-MM-DD HH:mm')
          const endTime = moment(`2024-01-01 ${detail.endTime}`, 'YYYY-MM-DD HH:mm')
          
          if (!startTime.isValid()) errors.push(`${detail.date}：开始时间格式无效`)
          if (!endTime.isValid()) errors.push(`${detail.date}：结束时间格式无效`)
          
          if (startTime.isValid() && endTime.isValid() && !endTime.isAfter(startTime)) {
            errors.push(`${detail.date}：结束时间必须晚于开始时间`)
          }
        }
      })
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
} 