!(function (e, t) {
  'function' == typeof define && define.amd
    ? define('@vxe-ui/plugin-export-xlsx', ['exports', 'xe-utils'], t)
    : 'undefined' != typeof exports
      ? t(exports, require('xe-utils'))
      : (t((t = {}), e.XEUtils), (e.VxeUIPluginExportXLSX = t))
})(
  'undefined' != typeof globalThis
    ? globalThis
    : 'undefined' != typeof self
      ? self
      : this,
  function (e, N) {
    var R, A
    Object.defineProperty(e, '__esModule', { value: !0 }),
      (e.default = e.VxeUIPluginExportXLSX = void 0),
      (N = (l = N) && l.__esModule ? l : { default: l })
    var D = 'f8f8f9',
      T = '606266',
      t = 'thin',
      o = 'e8eaec'
    function X(e, t) {
      if (t) {
        if ('seq' === e.type) return N.default.toValueString(t)
        switch (e.cellType) {
          case 'string':
            return N.default.toValueString(t)
          case 'number':
            if (isNaN(t)) break
            return Number(t)
          default:
            if (t.length < 12 && !isNaN(t)) return Number(t)
        }
      }
      return t
    }
    function $(e, t) {
      t && (e.height = N.default.floor(0.75 * t, 12))
    }
    function j(e, t) {
      ;(e.protection = { locked: !1 }),
        (e.alignment = { vertical: 'middle', horizontal: t || 'left' })
    }
    function L() {
      return {
        top: { style: t, color: { argb: o } },
        left: { style: t, color: { argb: o } },
        bottom: { style: t, color: { argb: o } },
        right: { style: t, color: { argb: o } },
      }
    }
    function n(e) {
      function t() {
        var e = new (A || window.ExcelJS).Workbook(),
          n = e.addWorksheet(b)
        ;(e.creator = 'vxe-table'),
          (n.columns = _),
          v &&
            n.addRows(M).forEach(function (e) {
              C && $(e, m),
                e.eachCell(function (e) {
                  var t = n.getColumn(e.col),
                    t = u.getColumnById(t.key),
                    o = t.headerAlign,
                    t = t.align
                  j(e, o || t || g || h),
                    C &&
                      Object.assign(e, {
                        font: { bold: !0, color: { argb: T } },
                        fill: {
                          type: 'pattern',
                          pattern: 'solid',
                          fgColor: { argb: D },
                        },
                        border: L(),
                      })
                })
            }),
          n.addRows(O).forEach(function (e) {
            C && $(e, m),
              e.eachCell(function (e) {
                var t = n.getColumn(e.col),
                  t = u.getColumnById(t.key)
                t &&
                  (j(e, t.align || h), C) &&
                  Object.assign(e, {
                    font: { color: { argb: T } },
                    border: L(),
                  })
              })
          }),
          w &&
            n.addRows(V).forEach(function (e) {
              C && $(e, m),
                e.eachCell(function (e) {
                  var t,
                    o = n.getColumn(e.col),
                    o = u.getColumnById(o.key)
                  o &&
                    ((t = o.footerAlign),
                    (o = o.align),
                    j(e, t || o || x || h),
                    C) &&
                    Object.assign(e, {
                      font: { color: { argb: T } },
                      border: L(),
                    })
                })
            }),
          k &&
            k({
              options: f,
              workbook: e,
              worksheet: n,
              columns: d,
              colgroups: a,
              datas: i,
              $table: u,
            }),
          U.forEach(function (e) {
            var t = e.s,
              e = e.e
            n.mergeCells(t.r + 1, t.c + 1, e.r + 1, e.c + 1)
          }),
          e.xlsx.writeBuffer().then(function (e) {
            var t,
              e = new Blob([e], { type: 'application/octet-stream' }),
              o = f,
              n = (r = R).modal,
              r = r.t,
              a = o.message,
              i = o.filename,
              o = o.type,
              a = !1 !== a
            window.Blob
              ? navigator.msSaveBlob
                ? navigator.msSaveBlob(e, ''.concat(i, '.').concat(o))
                : (((t = document.createElement('a')).target = '_blank'),
                  (t.download = ''.concat(i, '.').concat(o)),
                  (t.href = URL.createObjectURL(e)),
                  document.body.appendChild(t),
                  t.click(),
                  document.body.removeChild(t))
              : a &&
                n &&
                n.alert({ content: r('vxe.error.notExp'), status: 'error' }),
              I &&
                s &&
                (s.close(l),
                s.message({
                  content: c('vxe.table.expSuccess'),
                  status: 'success',
                }))
          })
      }
      var r,
        o,
        l = 'xlsx',
        n = R,
        s = n.modal,
        c = n.getI18n,
        u = e.$table,
        f = e.options,
        d = e.columns,
        a = e.colgroups,
        i = e.datas,
        n = u.props,
        e = u.reactData,
        p = u.getComputeMaps().computeColumnOpts,
        g = n.headerAlign,
        h = n.align,
        x = n.footerAlign,
        m = e.rowHeight,
        n = f.message,
        b = f.sheetName,
        v = f.isHeader,
        w = f.isFooter,
        e = f.isMerge,
        y = f.isColgroup,
        E = f.original,
        C = f.useStyle,
        k = f.sheetMethod,
        S = p.value,
        I = !1 !== n,
        p = u.getMergeCells(),
        M = [],
        V = [],
        _ = [],
        U = [],
        B = 0,
        O =
          (d.forEach(function (e) {
            var t = e.id,
              e = e.renderWidth
            _.push({ key: t, width: N.default.ceil(e / 8, 1) })
          }),
          v &&
            (y && a
              ? a.forEach(function (e, i) {
                  var l = {}
                  d.forEach(function (e) {
                    l[e.id] = null
                  }),
                    e.forEach(function (e) {
                      var t = e._colSpan,
                        o = e._rowSpan,
                        n = (function e(t) {
                          var o = t.childNodes
                          return o && o.length ? e(o[0]) : t
                        })(e),
                        r = d.indexOf(n),
                        a = e.headerExportMethod || S.headerExportMethod
                      ;(l[n.id] = a
                        ? a({ column: e, options: f, $table: u })
                        : E
                          ? n.field
                          : e.getTitle()),
                        (1 < t || 1 < o) &&
                          U.push({
                            s: { r: i, c: r },
                            e: { r: i + o - 1, c: r + t - 1 },
                          })
                    }),
                    M.push(l)
                })
              : ((r = {}),
                d.forEach(function (e) {
                  var t = e.id,
                    o = e.field,
                    n = e.headerExportMethod || S.headerExportMethod
                  r[t] = n
                    ? n({ column: e, options: f, $table: u })
                    : E
                      ? o
                      : e.getTitle()
                }),
                M.push(r)),
            (B += M.length)),
          e &&
            p.forEach(function (e) {
              var t = e.row,
                o = e.rowspan,
                n = e.col,
                e = e.colspan
              U.push({
                s: { r: t + B, c: n },
                e: { r: t + B + o - 1, c: n + e - 1 },
              })
            }),
          i.map(function (t) {
            var o = {}
            return (
              d.forEach(function (e) {
                o[e.id] = X(e, t[e.id])
              }),
              o
            )
          }))
      ;(B += O.length),
        w &&
          ((n = u.getTableData().footerData),
          (y = n),
          (p = (o = f.footerFilterMethod)
            ? y.filter(function (e, t) {
                return o({ items: e, $rowIndex: t })
              })
            : y),
          (n = u.getMergeFooterItems()),
          e &&
            n.forEach(function (e) {
              var t = e.row,
                o = e.rowspan,
                n = e.col,
                e = e.colspan
              U.push({
                s: { r: t + B, c: n },
                e: { r: t + B + o - 1, c: n + e - 1 },
              })
            }),
          p.forEach(function (t) {
            var o = {}
            d.forEach(function (e) {
              o[e.id] = X(e, t[u.getVMColumnIndex(e)])
            }),
              V.push(o)
          }))
      I && s
        ? (s.message({
            id: l,
            content: c('vxe.table.expLoading'),
            status: 'loading',
            duration: -1,
          }),
          setTimeout(t, 1500))
        : t()
    }
    function d(e) {
      var t = R,
        o = t.modal,
        t = t.t,
        n = e.$table,
        e = e.options,
        n = n.internalData._importReject
      !1 !== e.message &&
        o &&
        o.message({ content: t('vxe.error.impFields'), status: 'error' }),
        n && n({ status: !1 })
    }
    function r(a) {
      var e = R,
        i = e.modal,
        l = e.getI18n,
        s = a.$table,
        o = a.columns,
        c = a.options,
        e = a.file,
        u = s.internalData._importResolve,
        f = !1 !== c.message,
        t = new FileReader()
      ;(t.onerror = function () {
        d(a)
      }),
        (t.onload = function (e) {
          var r = [],
            t =
              (o.forEach(function (e) {
                e = e.field
                e && r.push(e)
              }),
              new (A || window.ExcelJS).Workbook()),
            e = e.target
          e
            ? t.xlsx.load(e.result).then(function (e) {
                var t,
                  n,
                  o,
                  e = e.worksheets[0]
                e &&
                ((e = e.getSheetValues()),
                (t = N.default.findIndexOf(e, function (e) {
                  return e && 0 < e.length
                })),
                (n = e[t]),
                (o = r),
                n.some(function (e) {
                  return -1 < o.indexOf(e)
                }))
                  ? ((e = e.slice(t).map(function (e) {
                      var o = {},
                        t =
                          (e.forEach(function (e, t) {
                            o[n[t]] = e
                          }),
                          {})
                      return (
                        r.forEach(function (e) {
                          t[e] = N.default.isUndefined(o[e]) ? null : o[e]
                        }),
                        t
                      )
                    })),
                    s.createData(e).then(function (e) {
                      e =
                        'insert' === c.mode
                          ? s.insertAt(e, -1)
                          : s.reloadData(e)
                      return e.then(function () {
                        u && u({ status: !0 })
                      })
                    }),
                    f &&
                      i &&
                      i.message({
                        content: l('vxe.table.impSuccess', [e.length]),
                        status: 'success',
                      }))
                  : d(a)
              })
            : d(a)
        }),
        t.readAsArrayBuffer(e)
    }
    function a(e) {
      if ('xlsx' === e.options.type) return r(e), !1
    }
    function i(e) {
      if ('xlsx' === e.options.type) return n(e), !1
    }
    var l = (e.VxeUIPluginExportXLSX = {
      install: function (e, t) {
        ;(R = e),
          /^(4)\./.test(R.uiVersion) ||
            console.error('[plugin-export-xlsx 4.x] Version 4.x is required'),
          (A = t ? t.ExcelJS : null),
          R.setConfig({
            table: {
              importConfig: { _typeMaps: { xlsx: 1 } },
              exportConfig: { _typeMaps: { xlsx: 1 } },
            },
          }),
          R.interceptor.mixin({ 'event.import': a, 'event.export': i })
      },
    })
    'undefined' != typeof window &&
      window.VxeUI &&
      window.VxeUI.use &&
      window.VxeUI.use(l),
      (e.default = l)
  },
)
