<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- css部分 -->
    <link rel="stylesheet" href="../../styles/tailwind.css" />
    <link rel="stylesheet" href="../../assets/element-plus@2.9.4/dist/index.css" />
    <link rel="stylesheet" href="../../assets/vxe-pc-ui@4.3.80/lib/style.min.css" />
    <link rel="stylesheet" href="../../assets/vxe-table@4.10.6/lib/style.min.css" />
    <link rel="stylesheet" href="../../styles/common.dialog.css" />
    <link rel="stylesheet" href="../../styles/f41.b1.shift.page.component.css" />
    <!-- CDN js部分 -->
    <script src="../../assets/vue@3.5.13/vue.global.js"></script>
    <script src="../../assets/element-plus@2.9.4/dist/index.full.js"></script>
    <script src="../../assets/sortablejs@latest/Sortable.min.js"></script>
    <script src="../../assets/element-plus@2.9.4/icons-vue/index.full.js"></script>
    <script src="../../assets/moment/moment.min.js"></script>
    <script src="../../assets/lodash@4.17.21/lodash.min.js"></script>
    <script src="../../assets/xe-utils@3.7.0/dist/xe-utils.umd.min.js"></script>
    <script src="../../assets/vxe-pc-ui@4.3.80/lib/index.umd.min.js"></script>
    <script src="../../assets/vxe-table@4.10.6/lib/index.umd.min.js"></script>
    <script src="../../assets/@vueuse/shared@12.7.0/index.iife.min.js"></script>
    <script src="../../assets/@vueuse/core@12.7.0/index.iife.min.js"></script>
    <!-- api部分 -->
    <script src="../../assets/axios@1.6.7/axios.min.js"></script>
    <title>F2 Site列表组件  废弃</title>
  </head>
  <body>
    <div id="app">
      <f2-site-page></f2-site-page>
    </div>
  </body>
  <script type="module">
    import F2SitePage from './f2.site.page.component.js'
    const { createApp } = Vue
    const app = createApp({
      components: {
        F2SitePage,
      },
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    app.use(ElementPlus)
    app.use(VxeUI)
    app.use(VXETable)
    app.mount('#app')
  </script>
  <style scoped>
    .container {
      display: grid; 
      padding: 20px;
      max-width: 100%;
      height:100%;
      margin: 0 auto;
      grid-template-columns: 1fr 1fr; /* 两列等宽 */
      gap: 20px;
    }  

    .position-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px; 
      width: 100%;     /* 满宽度 */ 
    }
    
    .position-area {
      border: 2px solid #ccc;
      border-radius: 8px;
      min-height: 200px;
      padding: 10px;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .position-area:hover {
      transform: translateY(-5px);
    }
    
    .team-info {
      margin-top: 10px;
      
    }
    
    .team-list table {
      width: 100%;
      border-collapse: collapse;
    }
    
    .team-list th,
    .team-list td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    
    .dialog {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .dialog-content {
      background: white;
      padding: 20px;
      border-radius: 8px;
      min-width: 400px;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
    }
    
    .form-group input,
    .form-group select {
      width: 100%;
      padding: 8px;
    }
    
    .form-actions {
      margin-top: 20px;
      text-align: right;
    }
    
    button {
      padding: 8px 15px;
      margin: 0 5px;
      cursor: pointer;
    }
    </style>
</html>