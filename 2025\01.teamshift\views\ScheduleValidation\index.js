const { ref, reactive, onMounted, toRefs, defineComponent, computed, nextTick, onUnmounted, watch } = Vue
import {
  queryTeamLeaderAndStaff,
  queryTeamPlan,
  queryHeadData,
  submitFeedback,
  saveFeedBack,
} from '../../api/teams/index.js'
import { getScheduleValidation } from './api/index.js'
import TeamPlanSee from './components/team.plan.see.js'
import TeamPlanEdit from './components/team.plan.edit.js'
import TeamFeedback from './components/team.feedback.js'
import PendingHandover from '../handover/pending.handover.js'
import HandoverDrawer from './components/HandoverDrawer.js'
// 搜索表单组件
const SearchForm = defineComponent({
  name: 'SearchForm',
  emits: ['search'],
  setup(props, { emit }) {
    const searchFormRef = ref(null)
    const searchParams = ref({
      dt_date: [moment().format('YYYY-MM-DD'), moment().add(14, 'days').format('YYYY-MM-DD')],
      str_esn: '',
    })

    return {
      searchFormRef,
      searchParams,
    }
  },
  template: /*html*/ `
    <el-form ref="searchFormRef" :model="searchParams" label-width="auto" inline label-position="right">
      <el-form-item label="Date:">
        <el-date-picker
          style="width: 100%"
          clearable
          type="daterange"
          value-format="YYYY-MM-DD"
          v-model="searchParams.dt_date"
          start-placeholder="起始日期"
          end-placeholder="截止日期"
        />
      </el-form-item>
      <el-form-item label="ESN:">
        <el-input v-model="searchParams.str_esn" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="$emit('search', searchParams)" icon="Search" />
      </el-form-item>
   </el-form>
  `,
})

// ESN下的单元格内容组件
const EsnCell = defineComponent({
  name: 'EsnCell',
  components: {
    PendingHandover,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
    handoverType: {
      type: String,
      default: '',
    },
  },
  setup() {
    // 查看支持检验
    const handleOpenIsShowInspectSupport = (row) => {
      console.log(row)
    }

    // 确认排班等调整完毕
    const handleChangeVg = (row) => {
      console.log(row)
    }

    const openStartWorkDialog = ref(false)
    const startWorkIdWo = ref('')
    const isStart = ref(false)
    // 开工
    const handleStartWork = (row) => {
      startWorkIdWo.value = row.id_wo
      isStart.value = true
      openStartWorkDialog.value = true
    }

    const handleCloseStartWorkDialog = () => {
      openStartWorkDialog.value = false
      isStart.value = false
    }

    return {
      handleOpenIsShowInspectSupport,
      handleChangeVg,
      handleStartWork,
      handleCloseStartWorkDialog,
      openStartWorkDialog,
      startWorkIdWo,
      isStart,
    }
  },
  template: /*html*/ `
    <div class="flex h-full flex-col">
      <!-- ESN 标题 -->
      <div class="m-2 h-full text-center">
        <div class="rounded px-2">
          <span class="text-[14px] font-semibold">{{ row.esn }}</span>
        </div>
      </div>

      <!-- 操作按钮区域 - 固定在底部 -->
      <div class="absolute bottom-0 flex w-full items-center justify-center">
        <el-button text type="primary" @click="handleStartWork(row)">开工</el-button>
      </div>
    </div>
    <el-drawer
      class="common-drawer"
      v-model="openStartWorkDialog"
      title="开工"
      size="80%"
      :append-to-body="true"
      @close="handleCloseStartWorkDialog"
    >
      <pending-handover
        v-if="openStartWorkDialog"
        :id-wo="startWorkIdWo"
        :is-start="isStart"
        :start-work-row="row"
        :handover-type="handoverType"
        :is-show-handover-type="false"
      />
    </el-drawer>
  `,
})

// Start Date 单元格内容组件
const StartDateCell = defineComponent({
  name: 'StartDateCell',
  props: {
    row: {
      type: Object,
      required: true,
    },
  },
  template: /*html*/ `
    <div class="h-full flex flex-col divide-y divide-gray-300">
      <div class="font-semibold p-2">{{ row.project_start }}</div>
    </div>
  `,
})

// End Date 单元格内容组件
const EndDateCell = defineComponent({
  name: 'EndDateCell',
  props: {
    row: {
      type: Object,
      required: true,
    },
  },
  template: /*html*/ `
    <div class="h-full flex flex-col divide-y divide-gray-300">
      <div class="font-semibold p-2">{{ row.project_end }}</div>
    </div>
  `,
})

// 任务项组件
const TaskItem = defineComponent({
  name: 'TaskItem',
  components: {
    TeamPlanSee,
    TeamPlanEdit,
    TeamFeedback,
    HandoverDrawer,
  },
  props: {
    row: {
      type: Object,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    handoverType: {
      type: String,
      required: true,
    },
    strFlow: {
      type: String,
      required: true,
    },
    strGroup: {
      type: String,
      required: true,
    },
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const { ElMessage } = ElementPlus
    const planItem = computed(() => {
      return props.row.plan.find((item) => item.plan_date === props.column.day)
    })

    // 获取team staff 中的staff_name 并拼接成字符串
    const getTeamStaffName = (teamstaff) => {
      return teamstaff?.map((item) => item.staff_name).join(',')
    }

    const handoverVisible = ref(false)

    // 打开交接弹窗
    const handleOpenHandover = (task) => {
      handoverVisible.value = true
    }

    const teamPlanSeeRef = ref(null)
    const teamPlanSeeVisible = ref(false)

    // 打开班组计划查看弹窗
    const handleOpenTeamPlanSee = (task) => {
      teamPlanSeeVisible.value = true
    }

    const teamPlanEditRef = ref(null)
    const teamPlanEditVisible = ref(false)

    // 打开班组计划编辑弹窗
    const handleOpenTeamPlanEdit = (task) => {
      teamPlanEditVisible.value = true
    }

    // 处理任务详情操作
    const handleOpenTaskDetail = (task) => {
      console.log('打开任务详情', task)
    }

    const teamFeedbackVisible = ref(false)
    const feedbackTask = ref(null)
    // 处理颜色选择
    const handleColorCommand = (command) => {
      // if (planItem.value.int_handover_status === 1) {
      if (command === '-2' || command === '-1') {
        teamFeedbackVisible.value = true
        feedbackTask.value = planItem.value
        feedbackTask.value.status = command
        feedbackTask.value.id_wo = props.row.id_wo
        feedbackTask.value.id_shift = props.row.id_shift
        feedbackTask.value.str_shift = props.row.shift_name
        feedbackTask.value.str_flow = props.strFlow
        feedbackTask.value.str_group = props.strGroup == 'B23' ? 'B2/3' : props.strGroup
        feedbackTask.value.handover_type = props.handoverType
      }
      if (command === '1') {
        const param = planItem.value.task.map((item) => ({
          id_wo: props.row.id_wo,
          id_shift: props.row.id_shift,
          str_shift: props.row.shift_name,
          id_task: item.taskId,
          id_sm: item.id_sm,
          dt_feed: planItem.value.plan_date,
          int_status: command,
          str_content: '',
          str_flow: props.strFlow,
          str_group: props.strGroup == 'B23' ? 'B2/3' : props.strGroup,
          str_feed_back_type: props.handoverType,
        }))
        saveFeedBack(param).then(() => {
          handleRefresh()
        })
      }
      // } else {
      //   ElMessage.warning('任务未交接，无法反馈')
      // }
    }

    const statusClass = computed(() => {
      const statusMap = {
        1: 'bg-green-500 text-white',
        '-1': 'bg-yellow-500 text-white',
        '-2': 'bg-red-500 text-white',
      }
      return statusMap[planItem.value.fedbackstatus] || ''
    })

    const handleRefresh = () => {
      emit('refresh')
    }

    return {
      planItem,
      getTeamStaffName,
      handleOpenHandover,
      handoverVisible,
      teamPlanSeeRef,
      teamPlanSeeVisible,
      handleOpenTeamPlanSee,
      teamPlanEditRef,
      teamPlanEditVisible,
      handleOpenTeamPlanEdit,
      handleOpenTaskDetail,
      handleColorCommand,
      handleRefresh,
      teamFeedbackVisible,
      feedbackTask,
      statusClass,
    }
  },
  template: /*html*/ `
    <div
      v-if="planItem.task.length > 0"
      class="h-full"
      :class="statusClass"
    >
      <!-- 任务内容区域 -->
      <div class="p-2 pb-10">
        <div
          class="flex flex-col rounded-md border border-gray-300 my-2"
          v-for="task in planItem.task"
          :key="task.id"
        >
          <div class="flex justify-between gap-2">
            <!-- 任务名称 太长 显示省略号 -->
            <div class="truncate font-semibold pl-2" :title="task.taskname">{{ task.taskname }}</div>
            <!-- 任务人员 -->
            <div class="truncate pr-2" :title="getTeamStaffName(task.teamstaff)">
              {{ getTeamStaffName(task.teamstaff) }}
            </div>
          </div>

          <!-- 底部按钮 - 使用boxBottom类 -->
          <div class="boxBottom">
            <div class="span" @click="handleOpenTeamPlanSee(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <View />
              </el-icon>
            </div>
            <div class="span" @click="handleOpenTeamPlanEdit(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <Edit />
              </el-icon>
            </div>
            <div class="span" @click="handleOpenHandover(task)">
              <el-icon :size="18" color="#409EFF" class="cursor-pointer hover:text-blue-600">
                <Folder />
              </el-icon>
            </div>
            <div class="span">
              <el-dropdown trigger="click" @command="(command) => handleColorCommand(command, task)">
                <el-icon class="cursor-pointer hover:text-blue-600" :size="18" color="#409EFF">
                  <More />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="-2" class="!text-red-500">红色</el-dropdown-item>
                    <el-dropdown-item command="-1" class="!text-yellow-500">黄色</el-dropdown-item>
                    <el-dropdown-item command="1" class="!text-green-500">绿色</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
    <HandoverDrawer
      v-if="handoverVisible"
      v-model:visible="handoverVisible"
      ref="handoverRef"
      :row="row"
      :column="column"
      :handover-type="handoverType"
    />
    <TeamPlanSee
      v-if="teamPlanSeeVisible"
      ref="teamPlanSeeRef"
      v-model:visible="teamPlanSeeVisible"
      :planItem="planItem"
    />
    <TeamPlanEdit
      v-if="teamPlanEditVisible"
      ref="teamPlanEditRef"
      v-model:visible="teamPlanEditVisible"
      :planItem="planItem"
      @refresh="handleRefresh"
    />
    <TeamFeedback
      v-if="teamFeedbackVisible"
      v-model:visible="teamFeedbackVisible"
      :task="feedbackTask"
      @refresh="handleRefresh"
    />
  `,
})

// 磨削（VG）头部内容组件
const VgHeader = defineComponent({
  name: 'VgHeader',
  props: {
    column: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const vgData = computed(() => {
      return props.column.grind.gingdata.map((item) => {
        return {
          ...item,
          sm: item.sm.filter((item) => item.str_type === 'VG'),
        }
      })
    })
    // 是否显示编辑按钮
    const isShowEdit = computed(() => {
      return props.column.grind.is_vg === '0'
    })

    // 获取VG下的str_esn和str_team
    const getVgStrEsnAndStrTeam = (vgItem) => {
      if (vgItem.sm.length > 0) {
        return `${vgItem.str_esn}${vgItem.str_team}:`
      }
      return ''
    }

    // 获取vgItem中sm的str_sm 拼接成字符串
    const getSmStrSm = (vgItem) => {
      return vgItem.sm.map((item) => item.str_sm).join(',')
    }

    // 判断是否闪烁
    const isBlink = computed(() => {
      return props.column.grind.vgfullflag === '1'
    })

    return {
      vgData,
      isShowEdit,
      getSmStrSm,
      isBlink,
      getVgStrEsnAndStrTeam,
    }
  },
  template: /*html*/ `
    <div class="h-full flex flex-col" :class="{ 'vg-blink-bg': isBlink }">
      <div v-if="isShowEdit">
        <el-icon :size="18" color="#FFF" class="cursor-pointer hover:text-blue-600">
          <Edit />
        </el-icon>
      </div>
      <div v-if="vgData && vgData.length > 0">
        <div v-for="vgItem in vgData" :key="vgItem.id_wo" class="flex items-center">
          <el-icon v-if="vgItem.sm.length > 0 && vgItem.is_hide_edit === '0'" :size="18" color="#FFF" class="cursor-pointer hover:text-blue-600">
            <Edit />
          </el-icon>
          <!-- 超出显示省略号 -->
          <div class="truncate"> 
            <span class="font-semibold">{{ getVgStrEsnAndStrTeam(vgItem) }}</span>
            <span class="font-semibold" :title="getSmStrSm(vgItem)">{{ getSmStrSm(vgItem) }}</span>
          </div>
        </div>
      </div>
    </div>
  `,
})
// 主组件
export default defineComponent({
  name: 'F41B1ShiftPage',
  components: {
    SearchForm,
    TaskItem,
    EsnCell,
    StartDateCell,
    EndDateCell,
    VgHeader,
  },
  props: {
    strFlow: String,
    inputIdWo: String,
    inputPlanDate: String,
    inputIdMain: String,
    inputStrFlow: String,
    inputStrEsnType: String,
    inspectType: String,
    inputGroupType: String,
    handoverType: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const loading = ref(false)
    const state = reactive({
      columnList: [],
      showHeader: true,
      loadingData: false,
      tableMaxHeight: 850,
      tableData: [],
    })

    // 判断是否是周末
    const isWeekend = (weekday) => {
      return weekday === '星期六' || weekday === '星期日'
    }

    const xTable = ref(null)
    const searchFormRef = ref(null)
    const teamInfoRef = ref(null)

    // 计算表格高度
    const calculateTableHeight = () => {
      // 获取视口高度
      const viewportHeight = window.innerHeight
      // 获取搜索表单高度
      const searchFormHeight = searchFormRef.value?.$el?.offsetHeight || 0
      // 获取团队信息高度
      const teamInfoHeight = teamInfoRef.value?.$el?.offsetHeight || 0

      // 计算表格可用高度
      let availableHeight = viewportHeight - searchFormHeight - teamInfoHeight - 32 // 32是padding的总高度(上下各16px)

      // 如果在iframe中，需要考虑iframe的padding和margin
      if (window.self !== window.top) {
        availableHeight -= 40 // iframe的padding和margin的总高度
      }

      // 设置最小高度
      state.tableMaxHeight = Math.max(availableHeight, 400)
    }

    // 监听窗口大小变化
    const handleResize = () => {
      calculateTableHeight()
    }

    const loadList = async () => {
      loading.value = true
      await initColumns()
      const data = await mockList()

      if (xTable.value) {
        await xTable.value.reloadData(data)
        loading.value = false
        // 数据加载完成后重新计算高度
        nextTick(() => {
          calculateTableHeight()
        })
      }
    }

    const initColumns = async () => {
      const searchParams = searchFormRef.value?.searchParams
      const params = {
        str_flow: props.inputStrFlow,
        start_date: (searchParams.dt_date && searchParams.dt_date[0]) || moment().format('YYYY-MM-DD'),
        end_date: (searchParams.dt_date && searchParams.dt_date[1]) || moment().add(14, 'days').format('YYYY-MM-DD'),
      }
      try {
        const response = await queryHeadData(params)
        state.columnList = response
      } catch (error) {
        console.error(error)
      }
    }

    const mockList = async () => {
      state.loadingData = true
      const searchParams = searchFormRef.value?.searchParams
      try {
        const params = {
          str_flow: props.inputStrFlow,
          str_type: props.inspectType,
          start_date: (searchParams.dt_date && searchParams.dt_date[0]) || moment().format('YYYY-MM-DD'),
          end_date: (searchParams.dt_date && searchParams.dt_date[1]) || moment().add(14, 'days').format('YYYY-MM-DD'),
          str_esn: searchParams.str_esn,
          str_esn_type: props.inputStrEsnType,
          str_group_type: props.inputGroupType,
        }
        const response = await getScheduleValidation(params)
        state.tableData = response || []
        state.loadingData = false
      } catch (error) {
        console.error(error)
        state.loadingData = false
      }
    }

    const handleHeaderCellClassName = ({ column }) => {
      // 只处理展开状态下的周末列
      if (column && column.title) {
        if (column.title === '星期六' || column.title === '星期日') {
          return 'bg-weekend'
        }
      }
      return ''
    }

    const handleSearch = (searchParams) => {
      state.searchParams = searchParams
      loadList()
    }

    const isExistData = (row, column) => {
      const planList = row.plan
      const planDay = column.day
      const taskIndex = planList.findIndex((item) => item.plan_date === planDay)
      return taskIndex !== -1
    }

    onMounted(() => {
      calculateTableHeight()
      loadList()
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', handleResize)
    })

    const handleRefresh = () => {
      handleSearch(state.searchParams)
    }

    return {
      ...toRefs(state),
      loading,
      handleSearch,
      handleHeaderCellClassName,
      isExistData,
      searchFormRef,
      teamInfoRef,
      handleRefresh,
      isWeekend,
    }
  },
  template: /*html*/ `
    <div class="p-4">
      <SearchForm ref="searchFormRef" @search="handleSearch" />

      <vxe-table
        :data="tableData"
        ref="xTable"
        border
        :header-cell-class-name="handleHeaderCellClassName"
        :header-cell-style="{color:'#fff',border:'0.01rem solid #fff'}"
        :column-config="{resizable: true,width:170}"
        :loading="loadingData"
        :height="tableMaxHeight"
        :show-header="true"
        :virtual-y-config="{enabled: true}"
        :virtual-x-config="{enabled: true}"
      >

        <template>
          <!-- 固定列 -->
          <vxe-column type="seq" title="序号" width="60" align="center" fixed="left"></vxe-column>
          <vxe-column title="ESN" field="esn" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <EsnCell :row="row" :handover-type="handoverType" />
            </template>
          </vxe-column>
          <vxe-column title="Team" field="team_name" width="100" align="center" fixed="left"></vxe-column>
          <vxe-column title="Name" field="staff_name" width="100" align="center" fixed="left"></vxe-column>
          <vxe-column title="Type" field="str_task_type" width="100" align="center" fixed="left"></vxe-column>
          <vxe-column title="Start Date" field="start_date" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <StartDateCell :row="row" />
            </template>
          </vxe-column>
          <vxe-column title="End Date" field="end_date" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <EndDateCell :row="row" />
            </template>
          </vxe-column>
          <vxe-column title="TAT" field="tat" width="60" align="center" fixed="left"></vxe-column>

          <vxe-colgroup v-for="(column, index) in columnList" :key="index" :title="column.str_week">
            <!-- 星期 -->
            <vxe-column :title="column.day">
              <template #default="{ row }">
                <!-- 是否有数据 -->
                <div v-if="isExistData(row, column)" class="h-full w-full">
                  <TaskItem :row="row" :column="column" @refresh="handleRefresh" :handover-type="handoverType" :str-flow = "inputStrFlow" :str-group = "inputGroupType"/>
                </div>
              </template>
            </vxe-column>
          </vxe-colgroup>
        </template>
      </vxe-table>
    </div>
  `,
})
