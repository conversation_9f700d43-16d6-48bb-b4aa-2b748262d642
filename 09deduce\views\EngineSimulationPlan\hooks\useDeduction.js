import { post } from '../../../../config/axios/httpReuest.js'
import { getCertificateInfo } from '../../../api/index.js'

const { ref, reactive } = Vue
/**
 * @description  推演串件结果抽屉组件函数
 */
export function useDeduction() {
  const pointColorMapForHtml = {
    1: '<div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>',
    2: '<div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>',
    3: '<div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>',
    4: '<div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>',
  }
  const tableRef = ref(null)
  const tableState = reactive({
    columns: [
      {
        title: '目标站点',
        field: 'str_nodename',
        fixed: 'left',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台站点',
        field: 'str_nodename_sp',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件来源',
        field: 'str_source_type',
        fixed: 'left',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '点位类型',
        field: 'int_point_type',
        minWidth: 80,
        type: 'html',
        fixed: 'left',
        formatter: ({ cellValue }) => {
          return pointColorMapForHtml[cellValue]
        },
      },
      {
        title: '串件状态',
        field: 'str_exchange',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标WO',
        field: 'str_wo',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标ESN',
        field: 'str_esn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台WO',
        field: 'str_wo_sp',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台ESN',
        field: 'str_esn_sp',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PN',
        field: 'str_pn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件名称',
        field: 'str_part_name',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'SM',
        field: 'str_sm',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件标签',
        field: 'str_label',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件条码',
        field: 'str_bcode',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '仓库',
        field: 'str_wh_name',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台客户',
        field: 'str_client_sp',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标客户',
        field: 'str_client',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '货位名称',
        field: 'str_product_name',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '批次',
        field: 'str_batch',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PKP',
        field: 'id_pkp',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标数量',
        minWidth: 100,
        field: 'int_num',
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台数量',
        minWidth: 100,
        field: 'int_num_inuse',
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '推演前 EKD',
        field: 'dt_ekd_before',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '推演后 EKD',
        field: 'dt_ekd',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
    ],
    data: null,
    total: 0,
  })
  const propsFilterFields = ref([])
  // * 根据id_wo获取推演串件结果
  const getDeductionData = async (id, filterFields) => {
    propsFilterFields.value = filterFields
    const params = {
      ac: 'de_getdecuctionresult',
      id_wo: id,
      filter_fields: propsFilterFields.value,
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      tableState.data = data.data
      tableState.total = data.data.length
    } else {
      ElementPlus.ElMessage.error(data.text)
    }
  }
  const sourceMap = {
    wip: 1,
    虚拟发动机: 1,
    库房件: 0,
  }
  /**
   * @description 根据id_pkp进行分组
   * @param {Array} data
   * @returns {Array}
   */
  const recombineArray = (data) => {
    const result = Object.values(
      data.reduce((acc, item) => {
        if (!acc[item.id_pkp]) {
          acc[item.id_pkp] = []
        }
        acc[item.id_pkp].push(item)
        return acc
      }, {}),
    )
    return result.map((item) => {
      return {
        id_se_pkp_rec: item[0].id_pkp,
        dbl_num_need: item[0].int_num,
        matcher_se_main_donors: item.map((i) => {
          return {
            id_se_pkp: sourceMap[i.str_source_type] === 1 ? i.id_pkp_sp : null,
            id_fee_po_sub: null,
            id_stock_list_site: sourceMap[i.str_source_type] === 0 ? i.id_pkp_sp : null,
            dbl_num: i.int_num_inuse,
            int_source_type: sourceMap[i.str_source_type],
          }
        }),
      }
    })
  }
  // * 发送串件
  const handleSend = async (idWo) => {
    // ElementPlus.ElMessage.warning('暂未开发')
    const loading = ElementPlus.ElLoading.service({
      text: '处理中...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const selectedData = tableRef.value.getSelectedData()
    const request = {
      id_wo: idWo,
      matcher_se_main_receptors: recombineArray(selectedData),
    }

    const params = {
      ac: 'se_pda_to_plan',
      request,
    }
    //return;
    const { data } = await post(params)
    loading.close()
    if (data.code === 'success') {
      // ElementPlus.ElMessage.success(data.text)
      changeStatusBySelectedData(idWo, selectedData)
    } else {
      ElementPlus.ElMessage.error(data.text)
    }
  }

  /**
   * @description  发送串件后标记状态
   * @param {String} idWo
   * @param {Array} selectedData
   */
  const changeStatusBySelectedData = async (idWo, selectedData) => {
    const pkps = selectedData.map((item) => item.id_pkp)
    const params = {
      ac: 'de_markexchange',
      id_wo: idWo,
      idlist: pkps,
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      // 更新表格数据
      await getDeductionData(idWo, propsFilterFields.value)
      ElementPlus.ElMessage.success(data.text)
    } else {
      ElementPlus.ElMessage.error(data.text)
    }
  }
  // 勾选改变
  const changeCheckbox = ({ checked, row }) => {
    // 将与当前行的id_pkp相同的行全部选中
    const data = tableState.data
    const idPkp = row.id_pkp
    data.forEach((item) => {
      if (item.id_pkp === idPkp) {
        tableRef.value.xTable.setCheckboxRow(item, checked)
      }
    })
  }
  // * 表格筛选
  const handleFilterChange = (data) => {
    tableState.total = tableRef.value.getCurrentLength()
  }
  // * 导出表格数据
  const exportTableData = () => {
    tableRef.value.exportData()
  }

  return {
    tableRef,
    tableState,
    getDeductionData,
    handleSend,
    exportTableData,
    handleFilterChange,
    changeCheckbox,
  }
}

/**
 * @description 获取证书信息
 * @param {Object} tableRef
 * @returns {Object}
 */
export const useCertificateInfo = (tableRef) => {
  const certificateInfoVisible = ref(false)
  const openCertificateInfo = () => {
    getCertificateTableData()
    certificateInfoVisible.value = true
  }
  const closeCertificateInfo = () => {
    certificateInfoVisible.value = false
  }

  const certificateLoading = ref(false)
  const certificateTableData = ref([])
  const getCertificateTableData = async () => {
    const selectedData = tableRef.value.getSelectedData()
    if (selectedData.length === 0) {
      ElementPlus.ElMessage.warning('请先选择数据')
      return
    }
    if (selectedData.length > 1) {
      ElementPlus.ElMessage.warning('只能选择一条数据')
      return
    }
    const { id_pkp } = selectedData[0]
    const queryParams = {
      id_pkp,
      int_page: 1,
    }
    certificateLoading.value = true
    const { data } = await getCertificateInfo(queryParams)
    certificateTableData.value = data.data
    certificateLoading.value = false
  }
  return {
    certificateInfoVisible,
    openCertificateInfo,
    closeCertificateInfo,
    certificateLoading,
    certificateTableData,
    getCertificateTableData,
  }
}
