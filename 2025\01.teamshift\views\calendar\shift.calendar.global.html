<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
     <!--    引入VUE-->
     <script src="../../../01.teamshift/assets/vue@3.4.15/vue.global.prod.js"></script>
    <!--    引入element-plus的样式-->
    <link href="../../../01.teamshift/assets/element-plus@2.5.5/index.css" rel="stylesheet" />
    <!--    引入element-plus-->
    <script src="../../../01.teamshift/assets/element-plus@2.5.5/index.js"></script>
    <!--  引入element-plus-icon-->
    <script src="../../../01.teamshift/assets/icons-vue@2.3.1/index.iife.min.js"></script>
    <!-- 引入element-plus-ch -->
    <script src="../../../01.teamshift/assets/element-plus@2.5.5/lang/zh-cn.js"></script>
    <!--    引入drawer的样式-->
    <link href="../../../01.teamshift/assets/css/drawer.css" rel="stylesheet" />
   
    <!-- 引入mock -->
    <script src="../../../01.teamshift/assets/mock/mock.js"></script>
    <!--    引入axios-->
    <script src="../../../01.teamshift/assets/axios@1.6.7/axios.min.js"></script>
    <!--    引入moment-->
    <script src="../../../01.teamshift/assets/moment/moment.min.js"></script>
    <script src="../../../01.teamshift/config/axios/httpReuest.js"></script>
    <script src="../../../01.teamshift/assets/vueuse/core@10.7.2/index.iife.min.js"></script>
    <script src="../../../01.teamshift/assets/vueuse/shared@10.7.2/index.iife.min.js"></script>
    <!-- <script src='../../../01.teamshift/assets/tools/helper.js'></script> -->

    <title>整体班次日历</title>
</head>

<body>
    <div id='app'>
        <shift-global-calendar></shift-global-calendar>
    </div>
</body>
<script type="module">
    import ShiftGlobalCalendar from './shift.calendar.global.js'
    
    const { createApp } = Vue
    const app = createApp({
        components: {
            ShiftGlobalCalendar,
        },
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        app.component(key, component)
    }
    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    })
    app.mount('#app')
</script>

</html>