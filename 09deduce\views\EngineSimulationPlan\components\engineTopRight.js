/**
 * @description: 缺件出厂、F4等待、退客户、退仓库组件
 */
// 导入抽屉组件
import EngineTopRightDrawer from './engineTopRightDrawer.js'
import { post } from '../../../../config/axios/httpReuest.js'
const { ref, reactive, onMounted, toRefs } = Vue
// 缺件出厂、F4等待、退客户、退仓库组件
const EngineTopRight = {
  components: {
    EngineTopRightDrawer,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const state = reactive({
      // 缺件出厂
      lackPartOutFactory: 0,
      // F4等待
      f4Wait: 0,
      // 退客户
      returnCustomer: 0,
      // 退仓库
      returnWarehouse: 0,
      // 待串件
      waitSerial: 0,
      // 保证交付
      ensureDelivery: 0,
    })
    const loading = ref(false)
    // 定义map
    const map = {
      10: 'lackPartOutFactory',
      20: 'f4Wait',
      30: 'returnCustomer',
      40: 'returnWarehouse',
      50: 'waitSerial',
      60: 'ensureDelivery',
    }
    const labelMap = {
      10: '缺件出厂',
      20: 'F4等待',
      30: '退客户',
      40: '退仓库',
      50: '待串件',
      60: '保证交付',
    }
    // 获取数据
    const getState = async () => {
      loading.value = true
      const params = {
        ac: 'de_markedsummary',
        id_wo: props.id,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        const { pnCounts } = data.data
        pnCounts.forEach((item) => {
          state[map[item.int_point_type]] = item.int_num
        })
        loading.value = false
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    const drawerState = reactive({
      visible: false,
      type: '',
    })
    // * 打开抽屉
    const handleOpenDrawer = (type) => {
      drawerState.visible = true
      drawerState.type = type
    }

    // * 提交
    const handleSumit = async () => {
      await getState()
      emit('submit', props.id)
      drawerState.visible = false
    }

    onMounted(() => {
      // getState()
    })

    return {
      ...toRefs(state),
      loading,
      getState,
      handleOpenDrawer,
      drawerState,
      handleSumit,
      labelMap,
    }
  },
  template: /*html*/ `
    <div :id="'right-'+id">
      <el-skeleton :loading="loading" animated>
        <template #template>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
          <el-skeleton-item></el-skeleton-item>
        </template>
        <template #default>
          <el-descriptions class="my-descriptions" :column="1" border>
             <el-descriptions-item label="缺件出厂:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-500" @click="handleOpenDrawer('10')">
                {{ lackPartOutFactory }}
              </div>
            </el-descriptions-item> 
            <el-descriptions-item label="F5等待:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-500" @click="handleOpenDrawer('20')">{{ f4Wait }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="退客户:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-500" @click="handleOpenDrawer('30')">
                {{ returnCustomer }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="退仓库:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-500" @click="handleOpenDrawer('40')">
                {{ returnWarehouse }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="待串件:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-500" @click="handleOpenDrawer('50')">
                {{ waitSerial }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="保证交付:" align="center">
              <div class="hover:cursor-pointer hover:text-blue-500" @click="handleOpenDrawer('60')">
                {{ ensureDelivery }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </template>
      </el-skeleton>
      <!-- 抽屉 -->
      <EngineTopRightDrawer
        v-if="drawerState.visible"
        v-model:visible="drawerState.visible"
        :id="id"
        :type="drawerState.type"
        :title="labelMap[drawerState.type]"
        @sumbit="handleSumit"
      ></EngineTopRightDrawer>
    </div>
  `,
}
export default EngineTopRight
