/**
 * @description 调整线条
 * <AUTHOR>
 */
import { post } from '../../../../config/axios/httpReuest.js'
const { reactive } = Vue
export function useAdjustLine() {
  const adjustLineDialog = reactive({
    visible: false,
    currentData: {
      id_wo: '',
      currentCloseDate: '',
      targetCloseDate: '',
      // 日期禁用
      disableDate: '',
    },
  })

  // 点击调整线条
  const handleAdjustLine = (item) => {
    adjustLineDialog.visible = true
    adjustLineDialog.currentData.id_wo = item.id_wo
    adjustLineDialog.currentData.currentCloseDate = item.dt_close
    adjustLineDialog.currentData.targetCloseDate = ''
    adjustLineDialog.currentData.disabledDate = item.dt_close_std
  }

  // 提交调整线条
  const handleAdjustLineSubmit = async (data, func) => {
    const params = {
      ac: 'de_save_dtclose',
      id_wo: data.id_wo,
      dt_close: data.targetCloseDate,
    }
    const { data: res } = await post(params)
    if (res.code === 'success') {
      adjustLineDialog.visible = false
      await func()
    } else {
      ElementPlus.ElMessage.error(res.text)
    }
  }

  return {
    adjustLineDialog,
    handleAdjustLine,
    handleAdjustLineSubmit,
  }
}
