# 班组排班系统前端文档

## 📑 目录

| 章节 | 内容 |
|------|------|
| 1 | [项目概述](#-项目概述) |
| 2 | [技术栈](#️-技术栈) |
| 3 | [项目结构](#-项目结构) |
| 4 | [组件说明](#-组件说明) |
| 5 | [状态管理](#-状态管理) |
| 6 | [API调用](#-api调用) |
| 7 | [开发规范](#-开发规范) |
| 8 | [常见问题](#-常见问题) |

## 📋 项目概述

班组排班系统是一个用于管理工厂班组排班、任务分配和执行跟踪的前端应用。系统主要功能包括：

- 班组人员管理
- 排班计划制定与查看
- 任务分配与跟踪
- 工作反馈与状态更新
- 数据统计与报表

本文档主要面向前端开发人员，提供系统架构、组件使用和开发规范等信息。

## 🛠️ 技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| Vue.js | 3.x | 前端框架 |
| Element Plus | 2.x | UI组件库 |
| Tailwind CSS | 3.x | 样式框架 |
| Axios | 1.x | HTTP请求 |
| Vue Router | 4.x | 路由管理 |

## 📂 项目结构

``` bash
webapp/2025/01.teamshift/
├── api/                # API接口定义
│   ├── comm/           # 通用接口
│   ├── teams/          # 团队相关接口
│   ├── handover/       # 交接班相关接口
│   └── calendar/       # 日历相关接口
├── assets/             # 静态资源
├── components/         # 公共组件
├── config/             # 配置文件
├── utils/              # 工具函数
│   └── request.js      # 请求封装
├── views/              # 页面视图
│   ├── f4-1-b1/        # F4-1-B1流程相关页面
│   ├── handover/       # 交接班相关页面
│   ├── calendar/       # 日历相关页面
│   ├── overtime/       # 加班相关页面
│   └── schedue/        # 排班相关页面
└── main.js             # 入口文件
```

## 🧩 组件说明

### 1. 班组排班页面组件 (f41.b1.shift.page.component.js)

主要功能：显示班组排班信息，支持查看、编辑和状态更新。

| 组件方法 | 说明 | 参数 |
|---------|------|------|
| toggleHeader | 切换头部显示/隐藏 | value: Boolean |
| handleOpenIsShowInspectSupport | 打开检查支持对话框 | row: Object |
| handleChangeVg | 处理VG变更 | row: Object |
| handleStartWork | 处理开始工作 | row: Object |
| handleCloseStartWorkDialog | 关闭开始工作对话框 | - |
| handleOpenHandover | 打开交接班对话框 | task: Object |
| handleOpenTeamPlanSee | 打开团队计划查看 | task: Object |
| handleOpenTeamPlanEdit | 打开团队计划编辑 | task: Object |
| handleOpenTaskDetail | 打开任务详情 | task: Object |
| handleColorCommand | 处理颜色命令 | command: String |
| handleRefresh | 刷新数据 | - |

**使用示例**：

```html
<div class="flex h-full flex-col">
  <!-- ESN 标题 -->
  <div class="m-2 h-full text-center">
    <div class="rounded px-2">
      <span class="text-[14px] font-semibold">{{ row.esn }}</span>
    </div>
  </div>

  <!-- 操作按钮区域 - 固定在底部 -->
  <div class="absolute bottom-0 flex w-full items-center justify-center">
    <el-button text type="primary" @click="handleStartWork(row)">开工</el-button>
  </div>
</div>
```

### 2. 团队计划查看组件 (team.plan.see.js)

功能：查看团队计划详情，显示任务分配和执行情况。

| 属性/方法 | 类型 | 说明 |
|----------|------|------|
| planId | String | 计划ID |
| getTeamStaffName | Function | 获取团队成员名称 |
| taskList | Array | 任务列表 |

### 3. 团队反馈组件 (team.feedback.js)

功能：提交任务执行反馈，更新任务状态。

| 属性/方法 | 类型 | 说明 |
|----------|------|------|
| submitFeedback | Function | 提交反馈信息 |
| status | String | 任务状态 |

## 🔄 状态管理

本项目使用Vue 3的Composition API进行状态管理，主要通过以下方式：

1. **组件内状态**：使用`ref`和`reactive`管理组件内部状态

```javascript
// 组件内状态示例
const openStartWorkDialog = ref(false)
const startWorkIdWo = ref('')
const isStart = ref(false)
```

2. **跨组件状态**：使用`provide/inject`或props传递

```javascript
// 父组件提供状态
provide('teamData', teamData)

// 子组件注入状态
const teamData = inject('teamData')
```

## 🌐 API调用

API调用统一通过`utils/request.js`中封装的方法进行，主要接口定义在`api`目录下。

### 示例：获取团队负责人和员工信息

```javascript
import { queryTeamLeaderAndStaff } from '../../api/teams/index.js'

const getTeamLeaderAndStaff = async () => {
  try {
    const res = await queryTeamLeaderAndStaff()
    if (res && res.data) {
      // 处理返回数据
      groupLeader.value = res.data.groupLeader
      teamType.value = res.data.teamType
      baseHours.value = res.data.baseHours
      member.value = res.data.member
    }
  } catch (error) {
    console.error('获取团队信息失败', error)
  }
}
```

### 示例：提交任务反馈

```javascript
import { submitFeedback } from '../../api/teams/index.js'

const handleColorCommand = async (command) => {
  try {
    await submitFeedback({
      planid: currentTask.value.planId,
      status: command // -2: 红色, -1: 黄色, 1: 绿色
    })
    ElMessage.success('反馈提交成功')
    handleRefresh() // 刷新数据
  } catch (error) {
    ElMessage.error('反馈提交失败')
  }
}
```

## 📏 开发规范

### 命名规范

| 类型 | 规范 | 示例 |
|------|------|------|
| 组件文件 | 小写，点分隔 | `team.plan.see.js` |
| 组件名 | PascalCase | `TeamPlanSee` |
| 方法名 | camelCase，事件处理函数以handle开头 | `handleStartWork` |
| 变量名 | camelCase | `openStartWorkDialog` |
| CSS类名 | 使用Tailwind类名 | `flex h-full flex-col` |

### Vue组件结构

```javascript
export default {
  name: 'ComponentName',
  props: {
    // props定义
  },
  setup(props, { emit }) {
    // 状态定义
    const state = ref(initialValue)
    
    // 方法定义
    const handleEvent = () => {
      // 处理逻辑
    }
    
    // 生命周期钩子
    onMounted(() => {
      // 初始化逻辑
    })
    
    // 返回模板需要的内容
    return {
      state,
      handleEvent
    }
  },
  // 模板定义
  template: /*html*/ `
    <div>
      <!-- 模板内容 -->
    </div>
  `
}
```

### Element Plus使用规范

1. 按钮使用：优先使用文本按钮，减少视觉干扰

```html
<el-button text type="primary" @click="handleStartWork(row)">开工</el-button>
```

2. 对话框使用：使用Drawer代替Modal，提供更好的移动端体验

```html
<el-drawer
  class="common-drawer"
  v-model="openStartWorkDialog"
  title="开工"
  size="80%"
  :append-to-body="true">
  <!-- 内容 -->
</el-drawer>
```

### Tailwind CSS使用规范

1. 优先使用Tailwind类名，避免自定义CSS
2. 使用flex布局实现复杂排版
3. 使用m(margin)和p(padding)控制间距
4. 使用text-[size]控制文字大小

```html
<div class="flex h-full flex-col">
  <div class="m-2 h-full text-center">
    <div class="rounded px-2">
      <span class="text-[14px] font-semibold">{{ row.esn }}</span>
    </div>
  </div>
</div>
```

## ❓ 常见问题

### 1. 组件加载失败

**问题**：组件未正确注册或路径错误导致加载失败

**解决方案**：

- 检查组件导入路径是否正确
- 确认组件是否已在main.js中全局注册
- 检查组件名称是否与使用时一致

### 2. API请求失败

**问题**：接口调用失败，无法获取数据

**解决方案**：

- 检查网络连接
- 确认API路径是否正确
- 检查请求参数格式
- 查看控制台错误信息
- 确认token是否有效

### 3. 样式问题

**问题**：样式未按预期显示

**解决方案**：

- 检查Tailwind类名是否正确
- 确认Element Plus组件版本兼容性
- 使用浏览器开发工具检查样式覆盖情况
- 检查响应式断点设置

## 📝 更新记录

| 日期 | 版本 | 变更说明 | 负责人 |
|------|------|----------|--------|
| 2024-03-15 | v1.0.0 | 初始版本创建 | 开发团队 |
