/* 浏览器缩放适配样式 */

/* 基础缩放适配 */
.zoom-adapted {
  /* 确保在不同缩放级别下的可读性 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 表格缩放适配 */
.zoom-adapted .vxe-table {
  /* 在高缩放级别下调整表格密度 */
  --vxe-table-row-height: auto;
  --vxe-table-cell-padding: 8px;
}

.zoom-adapted .vxe-table .vxe-cell {
  /* 确保单元格内容不被截断 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 32px;
  display: flex;
  align-items: center;
}

/* 分页器缩放适配 */
.zoom-adapted .vxe-pager {
  /* 在高缩放级别下调整分页器间距 */
  --vxe-pager-item-margin: 2px;
}

/* 响应不同缩放级别的媒体查询 */

/* 50% - 75% 缩放 (小缩放) */
@media screen and (min-resolution: 0.5dppx) and (max-resolution: 0.75dppx) {
  .zoom-adapted {
    font-size: 16px !important;
    line-height: 1.6 !important;
  }
  
  .zoom-adapted .el-button {
    padding: 10px 16px;
    font-size: 15px;
  }
  
  .zoom-adapted .vxe-table .vxe-cell {
    padding: 10px 12px;
    font-size: 15px;
  }
  
  .zoom-adapted .vxe-pager {
    font-size: 15px;
  }
  
  .zoom-adapted .vxe-pager .vxe-pager--btn {
    padding: 8px 12px;
  }
}

/* 75% - 100% 缩放 (正常缩放) */
@media screen and (min-resolution: 0.75dppx) and (max-resolution: 1dppx) {
  .zoom-adapted {
    font-size: 14px !important;
    line-height: 1.5 !important;
  }
}

/* 100% - 125% 缩放 (标准缩放) */
@media screen and (min-resolution: 1dppx) and (max-resolution: 1.25dppx) {
  .zoom-adapted {
    font-size: 14px !important;
    line-height: 1.5 !important;
  }
}

/* 125% - 150% 缩放 (中等放大) */
@media screen and (min-resolution: 1.25dppx) and (max-resolution: 1.5dppx) {
  .zoom-adapted {
    font-size: 13px !important;
    line-height: 1.4 !important;
  }
  
  .zoom-adapted .el-button {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .zoom-adapted .vxe-table .vxe-cell {
    padding: 6px 8px;
    font-size: 13px;
  }
  
  .zoom-adapted .vxe-pager {
    font-size: 13px;
  }
  
  .zoom-adapted .vxe-pager .vxe-pager--btn {
    padding: 4px 8px;
  }
}

/* 150% - 200% 缩放 (大放大) */
@media screen and (min-resolution: 1.5dppx) and (max-resolution: 2dppx) {
  .zoom-adapted {
    font-size: 12px !important;
    line-height: 1.3 !important;
  }
  
  .zoom-adapted .el-button {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .zoom-adapted .vxe-table .vxe-cell {
    padding: 4px 6px;
    font-size: 12px;
  }
  
  .zoom-adapted .vxe-pager {
    font-size: 12px;
  }
  
  .zoom-adapted .vxe-pager .vxe-pager--btn {
    padding: 2px 6px;
  }
}

/* 200%+ 缩放 (超大放大) */
@media screen and (min-resolution: 2dppx) {
  .zoom-adapted {
    font-size: 11px !important;
    line-height: 1.2 !important;
  }
  
  .zoom-adapted .el-button {
    padding: 2px 6px;
    font-size: 11px;
  }
  
  .zoom-adapted .vxe-table .vxe-cell {
    padding: 2px 4px;
    font-size: 11px;
    min-height: 24px;
  }
  
  .zoom-adapted .vxe-pager {
    font-size: 11px;
  }
  
  .zoom-adapted .vxe-pager .vxe-pager--btn {
    padding: 1px 4px;
  }
}

/* 缩放提示样式 */
.zoom-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-family: monospace;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.zoom-indicator.visible {
  opacity: 1;
}

/* 缩放时的平滑过渡 */
.zoom-transition {
  transition: all 0.2s ease-out;
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .zoom-adapted {
    border: 2px solid;
    background: white;
    color: black;
  }
  
  .zoom-adapted .vxe-table {
    border: 2px solid black;
  }
  
  .zoom-adapted .vxe-table .vxe-cell {
    border: 1px solid black;
  }
}

/* 减少动画模式适配 */
@media (prefers-reduced-motion: reduce) {
  .zoom-adapted,
  .zoom-transition {
    transition: none !important;
    animation: none !important;
  }
}

/* 打印样式适配 */
@media print {
  .zoom-adapted {
    font-size: 12pt !important;
    line-height: 1.4 !important;
    color: black !important;
    background: white !important;
  }
  
  .zoom-indicator {
    display: none !important;
  }
}

/* 移动设备适配 */
@media (max-width: 768px) {
  .zoom-adapted {
    font-size: 16px !important;
    line-height: 1.6 !important;
  }
  
  .zoom-adapted .vxe-table .vxe-cell {
    padding: 12px 8px;
    font-size: 16px;
    min-height: 44px; /* 符合移动端触摸目标大小 */
  }
  
  .zoom-adapted .el-button {
    padding: 12px 16px;
    font-size: 16px;
    min-height: 44px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .zoom-adapted {
    background-color: #1a1a1a;
    color: #e0e0e0;
  }
  
  .zoom-indicator {
    background: rgba(255, 255, 255, 0.9);
    color: black;
  }
} 