import { post } from '../../utils/request.js'
// 值班经理排班

/**
 * 值班经理下拉框
 */
export const getDutyManagerList = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_managerlist',
  })
}

/**
 * 值班经理排班 获取排班信息
 * @param {Object} params 参数
 * @returns {Promise<Object>} 排班信息
 */
export const getDutyManagerSchedule = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_managershift',
    query: params,
  })
}

/**
 * 获取当前月份的日期数据
 * @param {Object} params 参数
 * @returns {Promise<Object>} 排班信息
 */
export const getCurrentMonthDays = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'get_hr_holiday',
    ...params,
  })
}

/**
 * 值班经理排班 保存排班信息
 * @param {Object} data 排班信息
 * @returns {Promise<Object>} 保存结果
 */
export const saveDutyManagerSchedule = (data) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_managershift',
    postData: data,
  })
}

/**
 * 值班经理排班 获取清单列表
 * @param {Object} params 参数
 * @returns {Promise<Object>} 清单列表
 */
export const getDutyNoteList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_weekendstaffs',
    query: params,
  })
}
