// 班组计划列表公共组件
Vue.component('y-shift-component-page', {
  props: ['str_flow', 'input_id_wo', 'input_plan_date', 'input_id_main', 'input_str_flow'],
  data: function () {
    return {
      str_flow_now: this.input_str_flow,// 当前进入的Flow
      str_esn: '',// 发动机编号
      loading: false, // 列表加载动画
      isteamplanshow: false, // 添加页面显示
      id_main: '',
      searchParams: { dt_date: '', str_esn: '' },// 筛选条件

      formInline: {
        WorkOrder: '',
        Engine: '',
        date: []
      },
      formPartA: {
        reason: '',
        planId: '',
        status: ''
      },
      dialogVisible: false,
      form: {},
      height: 0,
      scrollTop: 0,
      radio: 1,
      datas: [],
      savedatas: [],
      checked: false,
      open: false,
      dialogVisibledata: '',
      radio: '',
      checkboxGroup: [],
      ischeckcolumn: '',
      tabledate: [],
      tableviewData: [],
      search: {
        date: [],
        beginDate: '',
        endDate: ''
      },
      pickerOptions: {
        shortcuts: [{
          text: 'In the latest week',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last 3 months',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      title: '添加',

      columns_config: [],
      tableData: [],
      isteamplanshow: false,
      addDate: '',
      addIdwo: '',
      showtable: true,
      id_main: '',// 计划主数据ID
      input_is_edit: false,
      input_is_editsee: false,
      isteamplanshowsee: false,

      isteamplanshowvg: false,
      isteamplanshowvg1: false,
      input_is_editvg: false,
      vgday: '',
      input_vg_plan_date_end: '',
      input_vg_plan_date_start: '',
      input_vg_plan_date: '',
      team_info: null,
      reachtable: true,
      esnfilter: [],
      rulesformPartA: {
        // form1 验证
        reason: [
          {
            required: true,
            message: 'Please select  reason',
            trigger: 'blur'
          }
          //其他验证设置...
          // { validator: deptRule, trigger: 'blur' },
        ]

      },
      is_show_inspator_suport: false,// 发动机下 检验人员信息显示
      show_inspator_suport_info: {},// 发动机下 检验人员信息
      esn_data_end: ''
    }
  },

  computed: {
    is_show_f1_1() {
      let _this = this
      return _this.input_str_flow === 'F1-1'
    },
    is_show_f1_2() {
      let _this = this
      return _this.input_str_flow === 'F1-2'
    }
  },
  mounted: function () {
    this.loadList()
    let m1 = this.str_flow_now

  },
  created: function () {
    this.searchParams.dt_date = [AddDays(0), AddDays(14)]
    //获取Team人员
    this.getteamleaderandstaff()
  },
  methods: {
    /** 查看*/
    boxview(row, plan) {
      let _this = this
      // _this.addDate = date
      _this.isteamplanshowsee = true
      _this.id_main = plan.planId

    },
    /** 编辑*/
    boxedit(row, plan, date) {
      let _this = this
      _this.addDate = date
      _this.addIdwo = row.id_wo
      _this.isteamplanshow = true
      _this.id_main = plan.planId
      _this.str_esn = row.esn
      _this.esn_data_end = row.end_date
      _this.input_is_edit = 'true'
    },
    //添加
    addPlan(date, idwo, row) {
      let _this = this
      _this.addDate = date
      _this.addIdwo = idwo
      _this.isteamplanshow = true
      _this.str_esn = row.esn
      _this.esn_data_end = row.end_date

    },
    opendilog(dataitem, type) {
      let _this = this
      _this.formPartA.planId = dataitem.id
      if (type == '1') {
        let parame = {
          'ac': 'pt_plan_fedback',
          'ak': '',
          'ap': '',
          'au': 'ssamc',
          'planid': dataitem.planId,
          'status': type
        }
        $.ajax({
          url: globalApiUrl, //表示发送请求的路径
          type: 'post', //http请求方式
          accordion: 'true',
          data: parame,
          async: false, //同步请求，将锁住浏览器，用户其他操作必须等待请求完成才可以执行
          error: function () {
            alert('系统错误，请联系管理员!')
            return false
          },
          success: function (res) {
            if (res.code == 'success') {
              // if (res.data.length > 0) {
              //     that.tableColumnview.tableColumn = []
              //     that.tableColumnview.tableColumn = JSON.parse(JSON.stringify(res.data))

              //     that.$nextTick(() => {
              //         that.$refs.plxTable.reloadColumn(that.tableColumnview)
              //     })
              //     //内容
              //     that.gettableData()
              // } else {
              //     that.Isempty()
              // }
              _this.query()

            } else {
              that.$message.error(res.text)
            }
          }
        })
      } else {
        _this.formPartA.planId = dataitem.planId
        _this.formPartA.status = type
        _this.dialogVisible = true
      }

    },
    toggleEditDialog() {
      if (this.dialogVisible) {
        let _this = this
        let _is_can_save = true
        if (_this.formPartA.status == -2 || _this.formPartA.status == -1) {
          if (!_this.formPartA.reason) {
            _is_can_save = false
            _this.$message({
              message: '请备注说明',
              type: 'warning'
            })

          }

        }

        if (_is_can_save) {
          // 可以保存
          let parame = {
            'ac': 'pt_plan_fedback',
            'ak': '',
            'ap': '',
            'au': 'ssamc',
            'planid': _this.formPartA.planId,
            'status': _this.formPartA.status,
            'reason': _this.formPartA.reason
          }
          $.ajax({
            url: globalApiUrl, //表示发送请求的路径
            type: 'post', //http请求方式
            accordion: 'true',
            data: parame,
            async: false, //同步请求，将锁住浏览器，用户其他操作必须等待请求完成才可以执行
            error: function () {
              alert('系统错误，请联系管理员!')
              return false
            },
            success: function (res) {
              if (res.code == 'success') {
                _this.query()
              } else {
                that.$message.error(res.text)
              }
            }
          })
          _this.dialogVisible = false
        }


      } else {
        _this.dialogVisible = true
      }
    },
    /**查询*/
    query() {
      let _this = this
      _this.loadList()
    },
    /** 初始化表头*/
    inint_columns() {
      let _this = this
      _this.columns_config = []
      return new Promise(resolve => {
        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_query_staff_list_head',
            str_flow: _this.input_str_flow,
            start_date: _this.searchParams.dt_date && _this.searchParams.dt_date[0] || '', //getNowDay(),
            end_date: _this.searchParams.dt_date && _this.searchParams.dt_date[1] || ''
          })
          .then(function (response) {

            _this.columns_config = response.data.data && response.data.data.map((item) => {
                return {
                  title_0: item.onduty,
                  title_g_0: item.mesleave,
                  title_vg_0: item.grind,
                  title_vg_1: item.dailydata,
                  title_g: item.str_week,
                  title: item.day,
                  field: 'attr2'
                }
              }
            )


            resolve(_this.columns_config)

          })
          .catch(function (error) {
            console.log(error)
          })

      })
    },
    /** 重新加载*/
    loadList() {
      let _this = this

      this.loading = true

      _this.inint_columns().then((colums_t) => {
        _this.mockList().then(data => {
          // 使用函数式加载，阻断 vue 对大数据的监听
          const xTable = this.$refs.xTable
          const startTime = Date.now()
          if (xTable) {

            _this.esnfilter = []
            data.forEach(function (value, index) {
              _this.esnfilter.push({ 'label': value.esn, 'value': value.esn })
            })

            _this.esnfilter = JSON.stringify(_this.esnfilter)

            this.$refs.xTable.reloadData(data).then(() => {
              // console.log(_this.esnfilter )
              // VXETable.modal.message({ content: `渲染 ${size} 行，用时 ${Date.now() - startTime}毫秒`, status: 'info' })
              _this.reachtable = false
              this.loading = false
              _this.reachtable = true
            })


          }
        })
      })

    },
    /**加载数据*/
    mockList() {
      let _this = this
      return new Promise(resolve => {
        let list = []
        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_query_f42_engine_plan',
            str_flow: _this.str_flow_now,
            start_date: _this.searchParams.dt_date && _this.searchParams.dt_date[0] || '', //getNowDay(),
            end_date: _this.searchParams.dt_date && _this.searchParams.dt_date[1] || '',
            str_esn: _this.searchParams.str_esn
          })
          .then(function (response) {
            list = response.data.data && response.data.data || []
            resolve(list)

          })
          .catch(function (error) {
            console.log(error)
          })


      })
    },
    /**编辑*/
    edit_row(row, clun, outy_day) {
      let _this = this
      _this.isteamplanshow = true
      let edit_cell = row.plan?.find(x => x.outy_day && x.outy_day == outy_day) || {}
      _this.id_main = edit_cell.id_main
    },
    /** 批量添加*/
    add_batch() {
      let _this = this
      _this.isteamplanshow = true
    },
    /** 获取当天人员*/
    oudy_day_pip_peoples(row, outy_day) {
      return row.plan?.find(x => x.outy_day == outy_day)?.peolpes || []
    },

    /** 关闭弹窗*/
    close_dialog() {
      let _this = this
      _this.isteamplanshow = false
      _this.id_main = ''
      _this.query()
    },
    get_back_close_vg_dialog() {
      let _this = this
      _this.isteamplanshowvg = false
      _this.isteamplanshowvg1 = false
      _this.id_main = ''
      _this.query()
    },

    /** 处理数组成字符串 */
    exe_str(data) {
      return data.join(',')
    },

    getteamstaff(data) {
      let name = []
      for (let item of data) {
        name.push(item.staff_name)
      }
      return name.toString()
    },
    Endstaff(colday, endday) {
      // 结束日期
      let newEndday = new Date(endday).getTime()
      // 列日期
      let newColday = new Date(colday).getTime()
      // 结束后一天
      let Nextendday = new Date(endday).getTime() + 3600 * 1000 * 24 * 1


      if (newEndday >= newColday) {
        return '0'
      } else if (Nextendday == newColday) {
        return '1'
      }
    },
    changevg(coldata, nowday) {
      let _this = this
      _this.isteamplanshowvg = true
      _this.id_main = coldata

      _this.input_vg_plan_date_end = _this.searchParams.dt_date[1]
      _this.input_vg_plan_date_start = _this.searchParams.dt_date[0]
      _this.input_vg_plan_date = nowday
    },
    changeesn(coldata, nowday) {
      let _this = this
      _this.isteamplanshowvg1 = true
      _this.id_main = coldata

      _this.input_vg_plan_date_end = _this.searchParams.dt_date[1]
      _this.input_vg_plan_date_start = _this.searchParams.dt_date[0]
      _this.input_vg_plan_date = nowday
    },
    //判断 开始/结束时间变化，要标记
    changestatus({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex }) {
      if (row.changestatus == '1') {
        return 'bgPurple'
      }
    },
    Isweek({ $rowIndex, column, columnIndex, $columnIndex }) {
      if (column.title == '星期六' || column.title == '星期日') {
        return 'bgDust'
      }
        // else if( $rowIndex == 1 && this.columns_config[$columnIndex-1]!= null&& this.columns_config[$columnIndex-1].title_vg_0.fullflag == '1'){
        // 	return 'bgDust'
      // }
      else {
        return 'bghead'
      }
    },
    //判断状态
    activeclor({ row, column, rowIndex, columnIndex }) {
      let _this = this
      // 是否需要变色内容
      if (column.type == 'Isdata') {
        // 结束日期
        let endday = new Date(row.end_date).getTime()
        // 列日期
        let colday = new Date(column.title).getTime()
        // 开始日期
        let startday = new Date(row.start_date).getTime()
        // 结束后一天
        let Nextendday = new Date(row.end_date).getTime() + 3600 * 1000 * 24 * 1

        if (colday >= startday && endday >= colday) {
          for (let i = 0; i < row.plan.length; i++) {
            if (row.plan[i].plan_date == column.title) {
              if (row.plan[i].task.length > 0) {
                if (row.plan[i].fedbackstatus == '0') {
                  return 'bgblue'
                } else if (row.plan[i].fedbackstatus == '1') {
                  return 'bgSuccess'
                } else if (row.plan[i].fedbackstatus == '-1') {
                  return 'bgWarning'
                } else if (row.plan[i].fedbackstatus == '-2') {
                  return 'bgDanger'
                } else {
                  return 'bginfoblue'
                }
              } else {
                return 'bginfoblue'
              }
            }
          }
        } else if (colday < startday && endday >= colday) {
          for (let i = 0; i < row.plan.length; i++) {
            if (row.plan[i].plan_date == column.title) {
              if (row.plan[i].task.length > 0) {
                if (row.plan[i].fedbackstatus == 0) {
                  return 'bgblue'
                } else if (row.plan[i].fedbackstatus == 1) {
                  return 'bgSuccess'
                } else if (row.plan[i].fedbackstatus == -1) {
                  return 'bgWarning'
                } else if (row.plan[i].fedbackstatus == -2) {
                  return 'bgDanger'
                }
              }
            }
          }
        } else {
          if (Nextendday == colday && _this.team_info.teamType == 'B1') {
            return 'bgDangerop'
          } else {
            return 'bgInfo'
          }
        }
      } else {
        return 'bgInfo'
      }
    },
    //获取组长和组员
    getteamleaderandstaff() {
      let _this = this
      let parame = {
        'ac': 'pt_get_teamleaderandstaff',
        'ak': '',
        'ap': '',
        'au': 'ssamc'
      }
      $.ajax({
        url: globalApiUrl, //表示发送请求的路径
        type: 'post', //http请求方式
        accordion: 'true',
        data: parame,
        async: false, //同步请求，将锁住浏览器，用户其他操作必须等待请求完成才可以执行
        error: function () {
          alert('系统错误，请联系管理员!')
          return false
        },
        success: function (res) {
          if (res.code == 'success') {
            _this.team_info = res.data
          } else {
            _this.$message.error(res.text)
          }
        }
      })
    },
    /**数组转 字符串 */
    exe_str(data) {
      return data.join(',')
    },
    /**关闭编辑页 */
    close_edit_dialog() {
      let _this = this
      _this.isteamplanshow = false
      _this.id_main = ''
      _this.query()
    },
    /**确认已修改排班 */
    ISchangevg(id) {
      let _this = this
      _this.$confirm('排班、磨削等已调整完毕?', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axios
          .post(globalApiUrl, {
            au: 'ssamc',
            ap: 'api2018',
            ak: '',
            ac: 'pt_update_change_allot',
            id: id

          })
          .then(function (response) {
            list = response.data.data && response.data.data || []
            _this.query()
            _this.$message({
              type: 'success',
              message: '操作成功!'
            })

          })
          .catch(function (error) {
            console.log(error)
          })


      }).catch(() => {
        // _this.$message({
        // 	type: 'info',
        // 	message: '已取消删除'
        // });
      })
    },
    /**打开发动机下 支持人员 */
    open_is_show_inspator_suport(data) {
      let _this = this
      _this.is_show_inspator_suport = true
      if(data.inspector ==null ){
        data.inspector="";
      }
      _this.show_inspator_suport_info = data

    },
    close_is_show_inspator_suport() {
      let _this = this
      _this.is_show_inspator_suport = false
    },
    /**判断是不是当天日期 当天之前不能编辑 */
    is_now_date(row, dataitem, title) {
      let new_date = getNowDay()
      return title >= new_date

    }
  }

  ,
  template: `
	<div>
	<el-form 
      ref="searchForm" 
      :model="searchParams" 
      :inline="true" 
      label-width="auto"
			class="demo-form-inline" 
			label-position="right"
	>
			<el-form-item label="Date:" >
						<el-date-picker size="small" style="width: 100%" clearable type="daterange" value-format='yyyy-MM-dd'
							v-model="searchParams.dt_date" start-placeholder="起始日期" end-placeholder="截止日期">
						</el-date-picker>
					 </el-form-item>
										<el-form-item v-if="!is_show_f1_2" label="ESN:">
						<el-input size="small" v-model="searchParams.str_esn" clearable></el-input>
					</el-form-item>
										<el-form-item>
						<el-button type="primary" size="small" @click="query" class="el-icon-search">
						</el-button>
					</el-form-item>
		</el-form>
	<el-row style="color: #fff;
    background-color: #5d7092; font-size: 14px;">
		<el-row>
			<span>{{team_info.groupLeader}} {{team_info.teamType}}</span>&nbsp;&nbsp;&nbsp;&nbsp<span>标准工时：{{team_info.bashours}}H</span>
		</el-row>
		<el-row>
			<span>{{ exe_str(team_info.member)}}</span>
		</el-row>
	</el-row>

	<vxe-table border ref="xTable" tooltip-effect='dark'
	v-if="reachtable"
	:header-cell-class-name ='Isweek'
	:header-cell-style="{color:'#fff',border:'0.01rem solid #fff'}" max-height="590"
	:cell-class-name='activeclor'
	:row-config="{height: 150}" :column-config="{resizable: true,width:170}" 
	:loading="loading">
	<vxe-colgroup fixed="left">
		<template #header="{}">
			<el-row> <span>RUE/RUMC/RULA/RUQA值班人员 </span></el-row>
		</template>
		
			
		<vxe-colgroup title="每日人力状态 （只填休假、MES等人员状态）" style="text-align: center;">
			<vxe-column  type="seq" title="序号" width="60" align="center"></vxe-column>
			
			<vxe-column field='esn' title="ESN" width="100" :class-name="changestatus"   align="center" >
			
				<template v-slot:default='{ row,column,rowKey,columnKey }'>
					<!--<el-button v-if="row.changestatus=='1'" class='circle-edit' type='text' circle  size='mini' icon='el-icon-check' @click='ISchangevg(row.id)'></el-button>  -->

					<el-row class='headtitle newheadtitle'>
						<span style="font-size: 14px;
						font-weight: 600">{{row.esn}}</span>
					</el-row>
					<el-row class='boxBottom' >
					<!-- 发动机下 操作区域 -->
					
					<el-col :span="12" class="span_btn">
					<span  title="查看支持检验" @click='open_is_show_inspator_suport(row)' ><i class="el-icon-view self_icon_color_default" ></i></span>
					</el-col>
					
					<el-col :span="12"  class="span_btn">
					<span  v-if="row.changestatus=='1'"  title="确认排班等调整完毕" @click='ISchangevg(row.id)' ><i class="el-icon-check self_icon_color_default"></i></span>
					</el-col>
					<!--	<div>
							<span  slot="reference" style="width:40px;color:#409EFF;    line-height: 28px; display: inline-block !important;" 
							class="taskname omit_line">检验：</span>
							<el-popover placement="top-start" width="40" trigger="hover"
								:content="row.inspector">
								<span  slot="reference" style="width:35px; line-height: 28px;" class="taskname omit_line">{{ row.inspector}}</span>
							</el-popover>
						</div>							
						<div v-for="(item,index) in row.supportor" >
							<span  slot="reference" style="width:55px;color:#409EFF;    line-height: 28px; display: inline-block !important;" class="taskname omit_line">{{ item.str_type}}</span>
							<el-popover placement="top-start" width="35" trigger="hover"
								:content="item.staffnames">
								<span  slot="reference" style="width:35px; line-height: 28px;" class="taskname omit_line">{{ item.staffnames}}</span>
							</el-popover>

						</div>
						-->
					</el-row>
				</template>
			</vxe-column>
			<vxe-column v-if="!is_show_f1_2" title="Start Date" field='start_date' align="center" width="86">
			</vxe-column>
			<vxe-column v-if="!is_show_f1_2" title="End Date" field='end_date' align="center" width="80">
			</vxe-column>
			<vxe-column v-if="!is_show_f1_2" title="TAT" field='tat' align="center" width="45">
			</vxe-column>
		</vxe-colgroup>
		

	</vxe-colgroup>

	<!-- </vxe-colgroup> -->

	<!-- <vxe-colgroup title="张三请假"> -->
	<!-- <vxe-colgroup title="星期一">
		<vxe-column title="10/01" field="attr1"></vxe-column>
	</vxe-colgroup> -->

	<!-- </vxe-colgroup> -->

	<vxe-colgroup v-for="(column_config ,index) in columns_config" :key="index" >
	
	<!--<template #header="{}">
				<el-row>{{ column_config.title_0 == '[]' ? column_config.title_0 : 'N/A'}}</el-row>
			</template>	-->
		<template #header="{}">
			<el-row> 
			<div class='headtitle' v-for='(item_title_0,index) in column_config.title_0'>
						<span>{{item_title_0 }}</span>
					</div>
			<!--<span>{{ column_config.title_0}} </span>-->
			</el-row>
		</template>

		
		<vxe-colgroup>
			<template #header="{}">
				<!--人力状态-->
				<div class='headtitle' v-for='(item,index) in column_config.title_vg_1.statusdata'>
					<span>{{item.staff_type }}:</span>
					<span v-for='(items,indexs) in item.staff'>
						{{items.staff_name}},
					</span>
				</div>
				<el-button type='text' circle  size='mini' icon='el-icon-edit' @click='changeesn(column_config.title_vg_1.id_main,column_config.title)'></el-button> 
			</template>
			<vxe-colgroup :title="column_config.title_g">
				<!--<template #header="{}">
					<el-row> 222<span>{{column_config.title_g.toString() ? column_config.title_g.toString():'N/A'}} </span></el-row>
				</template>-->

				<vxe-column type='Isdata' :title='column_config.title'>
					
					<template #default="{  row  }">
						<div class='wrapbox'>
							<!-- 组件内容 -->
							<div  v-for='(dataitem,index) in row.plan' :key='dataitem.plan_date + index'>
								<!-- 绑定每日数据 有数据 -->
								<div class='thbox' v-if='dataitem.plan_date == column_config.title' key='dataitem.plan_date + index +'>
									<!-- 有数据 -->
									<div v-if='dataitem.task.length>0' :key="'A'+index">
										<!-- task 包区域 -->
										<div class='taskgroup'
											v-for='(task,index) in dataitem.task'>
											<div class='taskitem'>
												<!-- <span class='taskname'>{{task.taskname}}</span> -->

												<el-popover placement="top-start" width="70" trigger="hover"
													:content="task.taskname">
													<span  slot="reference" style="width:70px" class="taskname omit_line">{{ task.taskname}}</span>
												</el-popover>

												<el-popover placement="top-start" width="70" trigger="hover"
													:content="getteamstaff(task.teamstaff)">
													<span slot="reference" style="width:70px" class="omit_line">{{ getteamstaff(task.teamstaff)}}{{ getteamstaff(task.sectionstaff)}}</span>
												</el-popover>
													
											</div>
										</div>

										<!-- 操作区域 -->
										<div class='boxBottom'>

										<el-col :span="8"  class="span_btn">
										<span @click='boxview(row,dataitem)'>
													<i class='el-icon-view '></i>
											</span>
										</el-col>
										<el-col :span="8"  class="span_btn">
											<span  @click='boxedit(row,dataitem,column_config.title)' v-if="is_now_date(row,dataitem,column_config.title)">
													<i class='el-icon-edit '></i>
											</span>
											<span v-else> &nbsp;</span>
										</el-col>
											<el-col :span="8"  class="span_btn">
											<span >
												<el-popover placement='bottom' width='50'
													trigger='hover'>
													<ul class='popover'>
														<li @click="opendilog(dataitem,'-2')">
															<span style="color:red">红色</span>
														</li>
														<li @click="opendilog(dataitem,'-1')">
															<span style="color:#FFD700">黄色</span>
														</li>
														<li @click="opendilog(dataitem,'1')">
															<span style="color:#67C23A">绿色</span>
														</li>
													</ul>
													<template #reference>
														<i class='el-icon-more'></i>
													</template>
												</el-popover>
											</span>
											</el-col>
										</div>
									</div>
									<!-- 无数据 -->
									<div class='addbox' v-if='dataitem.task.length==0' :key="'B'+index">
										<!-- 不超过最终日期 -->
										<div v-if="Endstaff(column_config.title, row.end_date) == '0'" :key="'C'+index">
											<el-button type='primary' size='small'
												@click='addPlan(column_config.title,row.id_wo,row)'
												icon='el-icon-circle-plus-outline'>add</el-button>
										</div>
										<div style=" line-height: 0;"  v-else-if="Endstaff(column_config.title,row.end_date) == '1' && team_info.teamType=='B1'" :key="'D'+index">
											Release
										</div>
									</div>
								</div>
							</div>
						</div>


					</template>
				</vxe-column>
			</vxe-colgroup>
		</vxe-colgroup>
		
	</vxe-colgroup>

</vxe-table>
<el-row style="margin-top:5px">
<f4-1-process-sm-chart v-if="is_show_f1_2"></f4-1-process-sm-chart>
</el-row>

<el-dialog title='Reason'  :visible.sync='dialogVisible' width='35%'>
            <el-form ref='formPartA' :model='formPartA'   :rules="rulesformPartA"   label-position='right' size='small' label-width='auto'>
                <el-form-item  label="Remark" prop="reason">
                    <el-input type='textarea' controls-position='right' v-model='formPartA.reason' style='width:100%' />
                </el-form-item>
                <el-form-item>
                    <el-button type='primary' @click='toggleEditDialog' size='default'>Save</el-button>
                    <el-button size='default' @click='dialogVisible=false'>Close</el-button>
                </el-form-item>
            </el-form>
</el-dialog>

    <div v-if='isteamplanshow'>
        <y-common-team-plan-form  @get_back_close_dialog='close_edit_dialog()'   :input_plan_date='addDate' :input_id_wo='addIdwo' :input_id_main="id_main" :input_is_edit="input_is_edit" :str_flow="input_str_flow"  :input_str_esn="str_esn" :input_esn_date_end="esn_data_end"></y-common-team-plan-form> 
    </div>
    <div v-if='isteamplanshowsee'>
        <y-common-team-plan-form-see  @get_back_close_dialog='isteamplanshowsee=false'    :input_id_main="id_main" :input_is_editsee="input_is_editsee"></y-common-team-plan-form-see> 
    </div>
	
	<div v-if='isteamplanshowvg1'>
	<y-common-vg-status-edit  @get_back_close_vg_dialog='get_back_close_vg_dialog()'  :input_vg_plan_date='input_vg_plan_date'  :input_vg_plan_date_end='input_vg_plan_date_end' :input_vg_plan_date_start='input_vg_plan_date_start' :input_id_main="id_main" ></y-common-vg-status-edit> 
</div>


<!--检验支持人员信息-->
<el-dialog :visible.sync='is_show_inspator_suport' class="self_dialog" width='30%'>
	<el-card class="box-card">
		<div slot="header" class="clearfix" style="font-size: 16px;">
			<span>检验、支持人员 Inspector Supporter</span>
		</div>

		<div class="text item" style="padding: 5px 0;font-size: 14px;">
			<span style="color: #5b8ff9;">{{'检验:' +"&nbsp;&nbsp;"}}</span> {{"&nbsp;&nbsp;"+
			show_inspator_suport_info.inspector}}
		</div>

		<template v-for="o in  show_inspator_suport_info.supportor">
			<div class="text item" style="padding: 5px 0;font-size: 14px;">
				<span style="color: #5b8ff9;">{{o.str_type +"&nbsp;&nbsp;"}}</span> {{o.staffnames}}
			</div>
		</template>
	</el-card>
	<div slot="footer">
		<el-button class="topButton_right" style="margin-left:20px;" size="small" type='danger'
			@click="close_is_show_inspator_suport()">Close
		</el-button>
	</div>
</el-dialog>

</div>
    `
})
