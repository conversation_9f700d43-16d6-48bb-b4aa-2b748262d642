import HtVxeTable from '../../components/VxeTable/HtVxeTable.js'
import {
  getBorrowApplyList,
  saveBorrowApply,
  getTeams,
  getCurrentDeptStaff,
  getBorrowApplyId,
  deleteBorrowApply,
  getShiftList,
  withdrawBorrowApply,
  getSKillbyStaff,
} from '../../api/borrow/index.js'
import borrowTemplate from './borrow.template.js'

export default {
  name: 'TeamOvertime',
  components: {
    HtVxeTable,
  },
  template: borrowTemplate,
  setup() {
    const { ref, reactive, onMounted, nextTick, onUnmounted } = Vue
    const { ElMessage, ElMessageBox } = ElementPlus
    const loading = ref(true)
    const tableData = ref([])

    // 查看详情弹窗
    const detailDialogVisible = ref(false)
    const detailData = ref(null)

    const statusColor = (value) => {
      if (value === 0) return 'text-green-500'
      if (value === 301) return 'text-blue-500'
      if (value === 1) return 'text-green-500'
      if (value === -1) return 'text-red-500'
      return ''
    }
    const statusText = (value) => {
      if (value === 0) return '草稿'
      if (value === 301) return '待审批'
      if (value === 1) return '审批通过'
      if (value === -1) return '审批失败'
      if (value === -99) return '撤回'
      return ''
    }

    // 状态格式化 - 移到tableColumns定义之前
    const statusFormatter = ({ cellValue }) => {
      if (cellValue === 0) return '<span class="text-green-500">草稿</span>'
      if (cellValue === 301) return '<span class="text-blue-500">待审批</span>'
      if (cellValue === 1) return '<span class="text-green-500">审批通过</span>'
      if (cellValue === -1) return '<span class="text-red-500">审批失败</span>'
      if (cellValue === -99) return '<span class="text-red-500">撤回</span>'
      return cellValue
    }

    // 人员名称格式化函数，处理换行符
    const staffNameFormatter = ({ cellValue }) => {
      if (!cellValue) return ''
      return cellValue.replace(/\n/g, ', ')
    }

    const tableColumns = ref([
      {
        field: 'dt_borrow_start',
        title: '借调开始日期',
        minWidth: 120,
        filterRender: { name: 'FilterCalendar' },
        filters: [{ data: '' }],
      },
      {
        field: 'dt_borrow_end',
        title: '借调结束日期',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'int_borrow_day',
        title: '借调天数',
        minWidth: 120,
      },

      // {
      //   field: 'str_from_team',
      //   title: 'From Team',
      //   minWidth: 120,
      //   filterRender: { name: 'FilterInput' },
      //   filters: [{ data: '' }],
      // },
      {
        field: 'staff_name',
        title: '借调人员',
        minWidth: 120,
        formatter: staffNameFormatter,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_to_team',
        title: 'To Team',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_shift',
        title: 'Shift Name',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },

      { field: 'int_substatus', title: '状态', minWidth: 120, formatter: statusFormatter, type: 'html' },
      {
        field: 'str_sec_manage',
        title: '审批人',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
    ])
    const tableHeight = ref('400px')
    const tableContainerRef = ref(null)

    // 分页配置
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
    })

    const filterParams = ref({})
    const handleFilterChange = (params) => {
      pagination.currentPage = 1
      filterParams.value[params.column.field] = params.datas?.[0]
      loadTableData()
    }

    // 弹窗相关
    const dialogVisible = ref(false)
    const dialogTitle = ref('借调申请')
    const formData = reactive({
      dt_borrow_start: '',
      dt_borrow_end: '',
      int_borrow_day: '',
      id_from_team: '',
      id_to_team: '',
      staffs: [],
      skills: [],
      skillTypes: [],
    })

    // 加载表格数据
    const loadTableData = async () => {
      loading.value = true
      let param = {
        CurrentPage: pagination.currentPage,
        PageSize: pagination.pageSize,
        ...filterParams.value,
      }
      try {
        const res = await getBorrowApplyList(param)
        tableData.value = res.items
        pagination.total = res.totalCount || 0
      } catch (error) {
        ElMessage.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }

    // 分页处理
    const handleCurrentChange = ({ currentPage, pageSize }) => {
      pagination.currentPage = currentPage
      pagination.pageSize = pageSize
      loadTableData()
    }

    const handleSizeChange = ({ currentPage, pageSize }) => {
      pagination.currentPage = currentPage
      pagination.pageSize = pageSize
      loadTableData()
    }

    // 处理表格高度
    const updateTableHeight = () => {
      nextTick(() => {
        if (tableContainerRef.value) {
          // 获取容器元素
          const container = tableContainerRef.value

          // 获取容器的padding
          const containerStyle = window.getComputedStyle(container)
          const paddingTop = parseInt(containerStyle.paddingTop) || 0
          const paddingBottom = parseInt(containerStyle.paddingBottom) || 0

          // 计算表格可用高度
          let availableHeight

          // 检查是否在iframe中
          if (window !== window.parent) {
            // iframe情况下使用clientHeight
            availableHeight = container.clientHeight
          } else {
            // 非iframe情况下使用视窗高度
            availableHeight = window.innerHeight
          }

          // 计算最终表格高度
          const finalHeight = availableHeight - paddingTop - paddingBottom - 120 // 120px作为缓冲

          // 设置最小高度
          const minHeight = 200
          tableHeight.value = `${Math.max(finalHeight, minHeight)}px`
        }
      })
    }

    // 创建ResizeObserver实例
    const resizeObserver = new ResizeObserver(() => {
      updateTableHeight()
    })

    const shiftOptionList = ref([])
    // 打开新增弹窗
    const handleAdd = async () => {
      const shiftRes = await getShiftList()
      shiftOptionList.value = shiftRes.data
      // 重置表单数据
      Object.assign(formData, {
        id: '',
        dt_borrow_start: '',
        dt_borrow_end: '',
        int_borrow_day: '',
        id_shift: '',
        id_from_team: '',
        id_to_team: '',
        staffs: [],
        skills: [],
        skillTypes: [],
      })
      dialogTitle.value = '借调申请'
      dialogVisible.value = true
    }

    // 打开编辑弹窗
    const handleEdit = async (row) => {
      const borrowdetail = await getBorrowApplyId(row.id)
      const shiftRes = await getShiftList()
      const skillres = await getSKillbyStaff(borrowdetail.staffs.join(','))
      shiftOptionList.value = shiftRes.data
      skillOptionList.value = skillres.map((item) => ({
        label: item.str_skill,
        value: item.id_record,
      }))
      const uniqueSkillTypes = new Set();
        skillres.forEach((item) => {
          uniqueSkillTypes.add(item.str_skill_type);
        });
       skillTypeOptionList.value = Array.from(uniqueSkillTypes).map((skillType) => ({
          label: skillres.find((item) => item.str_skill_type === skillType).str_skill_type_name,
          value: skillType,
        }));    
      // 填充表单数据
      Object.assign(formData, {
        id: borrowdetail.id,
        dt_borrow_start: borrowdetail.dt_borrow_start,
        dt_borrow_end: borrowdetail.dt_borrow_start,
        int_borrow_day: borrowdetail.int_borrow_day,
        id_shift: borrowdetail.id_shift,
        id_from_team: borrowdetail.id_from_team,
        id_to_team: borrowdetail.id_to_team,
        staffs: borrowdetail.staffs,
        skills: borrowdetail.skills,
        skillTypes: borrowdetail.skillTypes,
      })
      dialogTitle.value = '修改借调申请'
      dialogVisible.value = true
    }

    const handleCommit = (row) => {
      ElMessageBox.confirm('确定要提交该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const borrowdetail = await getBorrowApplyId(row.id)
        const postData = {
          id: borrowdetail.id,
          dt_borrow_start: borrowdetail.dt_borrow_start,
          dt_borrow_end: borrowdetail.dt_borrow_end,
          int_borrow_day: borrowdetail.int_borrow_day,
          id_from_team: borrowdetail.id_from_team,
          id_to_team: borrowdetail.id_to_team,
          id_shift: borrowdetail.id_shift,
          int_substatus: 301, // 提交待审批
          staffs: borrowdetail.staffs,
          skills: borrowdetail.skills?.join(','),
        }
        try {
          await saveBorrowApply(postData)
          ElMessage.success('借调申请保存并提交成功')
          loadTableData()
        } catch (error) {
          ElMessage.error('保存失败：' + (error.message || '未知错误'))
        }
      })
    }

    // 查看详情
    const handleView = (row) => {
      detailData.value = { ...row }
      detailDialogVisible.value = true
    }

    // 删除记录
    const handleDelete = (row) => {
      ElMessageBox.confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          // 实际项目中应该调用API删除
          await deleteBorrowApply(row.id)
          ElMessage.success('删除成功')
          loadTableData()
        })
        .catch(() => {})
    }

    // 保存表单
    const handleSave = async () => {
      // 表单验证
      if (!formData.dt_borrow_start) {
        ElMessage.warning('请选择借调开始日期')
        return
      }
      if (!formData.dt_borrow_end) {
        ElMessage.warning('请选择借调结束日期')
        return
      }
      if (!formData.int_borrow_day) {
        ElMessage.warning('请输入借调时长')
        return
      }
      // if (!formData.id_from_team) {
      //   ElMessage.warning('请输选择借出班组')
      //   return
      // }
      if (!formData.id_to_team) {
        ElMessage.warning('请输选择借入班组')
        return
      }
      if (!formData.staffs || formData.staffs.length == 0) {
        ElMessage.warning('请选择借调人员')
        return
      }

      // 实际项目中应该调用API保存
      const postData = {
        id: formData.id,
        dt_borrow_start: formData.dt_borrow_start,
        dt_borrow_end: formData.dt_borrow_end,
        int_borrow_day: formData.int_borrow_day,
        id_from_team: formData.id_from_team,
        id_to_team: formData.id_to_team,
        id_shift: formData.id_shift,
        staffs: formData.staffs,
        skills: formData.skills.join(','),
        str_skill_types: formData.skillTypes.join(','),
        int_substatus: 0,
      }
      try {
        await saveBorrowApply(postData)
        ElMessage.success('借调申请保存成功')
        dialogVisible.value = false // 保存成功后关闭弹窗
        loadTableData()
      } catch (error) {
        ElMessage.error('保存失败：' + (error.message || '未知错误'))
      }
    }
    const handleSaveAndCommit = async () => {
      // 表单验证
      if (!formData.dt_borrow_start) {
        ElMessage.warning('请选择借调开始日期')
        return
      }
      if (!formData.dt_borrow_end) {
        ElMessage.warning('请选择借调结束日期')
        return
      }
      if (!formData.int_borrow_day) {
        ElMessage.warning('请输入借调天数')
        return
      }
      // if (!formData.id_from_team) {
      //   ElMessage.warning('请输选择借出班组')
      //   return
      // }
      if (!formData.id_to_team) {
        ElMessage.warning('请输选择借入班组')
        return
      }
      if (!formData.staffs || formData.staffs.length == 0) {
        ElMessage.warning('请选择借调人员')
        return
      }

      // 实际项目中应该调用API保存
      const postData = {
        id: formData.id,
        dt_borrow_start: formData.dt_borrow_start,
        dt_borrow_end: formData.dt_borrow_end,
        int_borrow_day: formData.int_borrow_day,
        // id_from_team: formData.id_from_team,
        id_to_team: formData.id_to_team,
        id_shift: formData.id_shift,
        int_substatus: 301, // 提交待审批
        staffs: formData.staffs,
        skills: formData.skills.join(','),
        str_skill_types: formData.skillTypes.join(','),
      }
      try {
        await saveBorrowApply(postData)
        ElMessage.success('借调保存并提交成功')
        dialogVisible.value = false // 保存成功后关闭弹窗
        loadTableData()
      } catch (error) {
        ElMessage.error('保存失败：' + (error.message || '未知错误'))
      }
    }
    const xTableRef = ref(null)

    const shiftEndTime = ref('')

    const handleStartDateChange = (value) => {
      formData.dt_borrow_end = moment(value)
        .add(formData.int_borrow_day - 1, 'days')
        .format('YYYY-MM-DD')
    }

    const handleBorrowDayChange = (value) => {
      formData.dt_borrow_end = moment(formData.dt_borrow_start)
        .add(value - 1, 'days')
        .format('YYYY-MM-DD')
    }

    onMounted(() => {
      loadTableData()
      updateTableHeight()

      // 监听容器大小变化
      if (tableContainerRef.value) {
        resizeObserver.observe(tableContainerRef.value)
      }

      // 监听窗口大小变化
      window.addEventListener('resize', updateTableHeight)
    })

    // 在组件卸载时清理
    onUnmounted(() => {
      // 移除ResizeObserver
      if (tableContainerRef.value) {
        resizeObserver.unobserve(tableContainerRef.value)
      }
      resizeObserver.disconnect()

      // 移除窗口事件监听
      window.removeEventListener('resize', updateTableHeight)
    })

    const teamOptionList = ref([])
    // 获取Team下拉列表
    const fetchTeamList = async () => {
      const res = await getTeams()
      teamOptionList.value = res.data.map((item) => ({
        label: item.str_name,
        value: item.id,
      }))
    }

    const staffOptionList = ref([])
    // 获取人员下拉列表
    const fetchStaffList = async () => {
      const res = await getCurrentDeptStaff()
      staffOptionList.value = res.map((item) => ({
        label: item.str_name,
        value: item.id,
        id_team: item.id_team,
        str_team: item.str_team,
      }))
    }

    // 选择Team
    const handleTeamChange = (value) => {
      formData.staffs = []
      staffOptionList.value = staffOptionList.value.filter((item) => item.id_team === value)
    }

    const skillOptionList = ref([])
    const skillTypeOptionList = ref([])
    const handleStaffChange = async (value) => {
      formData.skills = []
      skillOptionList.value = []
      skillTypeOptionList.value = []
      if (value.length > 0) {
        const ids = value.map((item) => item).join(',')
        const res = await getSKillbyStaff(ids)
        skillOptionList.value = res.map((item) => ({
          label: item.str_skill,
          value: item.id_record,
        }))
        // 往数组中第一个添加一个值
        skillOptionList.value.unshift({
          label: '全部',
          value: 'all',
        })
        const uniqueSkillTypes = new Set();
        res.forEach((item) => {
          uniqueSkillTypes.add(item.str_skill_type);
        });
       skillTypeOptionList.value = Array.from(uniqueSkillTypes).map((skillType) => ({
          label: res.find((item) => item.str_skill_type === skillType).str_skill_type_name,
          value: skillType,
        }));
      }
    }
    const handleSkillChange = (value) => {
      if (value.includes('all')) {
        formData.skills = skillOptionList.value.map((item) => item.value).filter((item) => item !== 'all')
      }
    }
    /**
     * 撤回
     * @param {Object} row 当前行数据
     */
    const handleWithdraw = (row) => {
      // 填写备注
      ElMessageBox.prompt('请输入撤销信息', '撤销', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '撤销信息不能为空',
      })
        .then(async ({ value }) => {
          try {
            await withdrawBorrowApply({
              id: row.id,
              status: -99,
              str_content: value,
            })
            ElMessage.success('撤回成功')
            loadTableData() // 刷新表格数据
          } catch (error) {
            ElMessage.error('撤回失败：' + (error.message || '未知错误'))
          }
        })
        .catch(() => {
          // 用户取消操作，不做任何处理
        })
    }
    onMounted(() => {
      fetchTeamList()
      fetchStaffList()
    })

    return {
      shiftEndTime,
      handleStartDateChange,
      handleBorrowDayChange,
      xTableRef,
      tableData,
      tableColumns,
      tableHeight,
      tableContainerRef,
      pagination,
      dialogVisible,
      dialogTitle,
      formData,
      detailDialogVisible,
      detailData,
      handleCurrentChange,
      handleSizeChange,
      handleAdd,
      handleEdit,
      handleCommit,
      handleView,
      handleDelete,
      handleSave,
      handleSaveAndCommit,
      shiftOptionList,
      statusColor,
      statusText,
      teamOptionList,
      staffOptionList,
      skillOptionList,
      skillTypeOptionList,
      handleTeamChange,
      handleStaffChange,
      handleFilterChange,
      handleWithdraw,
      handleSkillChange,
    }
  },
}
