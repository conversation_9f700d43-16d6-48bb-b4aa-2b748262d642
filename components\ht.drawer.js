const { ref } = Vue
const { useVModel } = VueUse
export default {
  name: 'HtDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: 'Drawer',
    },
    isShowSave: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String,
      default: '90%',
    },
    btnname: {
      type: String,
      default: 'Save',
    },
  },
  emits: ['save', 'clear'],
  setup(props, { emit }) {
    const isShowDrawer = useVModel(props, 'visible', emit)
    const closeDialogHandler = () => {
      emit('clear')
      isShowDrawer.value = false
    }
    // 保存按钮点击事件
    const handleSaveDialog = () => {
      emit('save')
      // closeDialogHandler()
    }
    return {
      isShowDrawer,
      closeDialogHandler,
      handleSaveDialog,
    }
  },
  template: /*html*/ `
    <el-drawer
      v-model="isShowDrawer"
      :show-close="false"
      class="my_drawer"
      :size="size"
      direction="rtl"
      destroy-on-close
      @close="closeDialogHandler"
      @keydown.enter.prevent
    >
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-xl text-white">{{ title }}</span>
          <el-button type="danger" @click="closeDialogHandler">Close</el-button>
        </div>
      </template>
      <slot></slot>
      <template #footer>
        <el-button type="danger" @click="closeDialogHandler">Quit</el-button>
        <el-button v-if="isShowSave" type="primary" @click="handleSaveDialog">{{btnname}}</el-button>
      </template>
    </el-drawer>
  `,
}
