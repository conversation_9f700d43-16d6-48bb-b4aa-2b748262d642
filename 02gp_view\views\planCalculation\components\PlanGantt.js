import { queryBaseTask } from '../../../api/baseTask.js'
import { queryGanttById, saveGantApi, activeProjectApi } from '../../../api/calculation.js'
import { getFlow, getSM } from '../../../api/index.js'
import { useTransform } from '../composables/useTransform.js'
import { useButtons } from '../composables/useButtons.js'
import UnitPlanDrawer from './unitPlanDrawer.js'
import DynamicColumnConfigurator from '../../../components/DynamicColumnConfigurator.js'
import { useDynamicColumn } from '../../../composables/useDynamicColumn.js'

const PlanGantt = {
  components: {
    UnitPlanDrawer,
    DynamicColumnConfigurator,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
    engineType: {
      type: String,
      required: true,
    },
    idWo: {
      type: String,
      required: true,
    },
  },
  emits: ['toMainUnitGantt'],
  setup(props, { emit }) {
    const gantt = Gantt.getGanttInstance()
    const { transformSubPlansToGanttData, transformGanttDataToSubPlans } = useTransform()
    const ganttData = Vue.ref({})
    // 获取甘特图数据
    const getGanttData = async () => {
      const data = await queryGanttById(props.id, formSearch.str_node)
      if (data) {
        ganttData.value.data = _.map(data.subPlans, (task) => ({
          ...transformSubPlansToGanttData(task),
          $has_child: task.children?.length > 0,
          $level: task.level || 1,
        }))
        ganttData.value.links = data.links
      }
    }
    // 获取flow下拉框数据
    const flowOptions = Vue.ref([])
    const getFlowOptions = async () => {
      flowOptions.value = (await getFlow()).map((item) => {
        return {
          key: item.str_key,
          label: item.str_value,
        }
      })
    }
    // 获取type下拉框数据
    const typeOptions = Vue.ref([])
    const getTypeOptions = () => {
      typeOptions.value = [
        { key: 'Core', label: 'Core' },
        { key: 'Fan', label: 'Fan' },
        { key: 'Lpt', label: 'Lpt' },
        { key: 'B1', label: 'B1' },
      ]
    }
    // 获取Sm下拉框数据
    const smOptions = Vue.ref([])
    const getSmOptions = async () => {
      smOptions.value = (await getSM()).map((item) => {
        return {
          key: item.str_key,
          label: item.str_key,
        }
      })
    }
    // 获取Task下拉框数据
    const taskOptions = Vue.ref([])
    const getTaskOptions = async () => {
      const queryList = [
        {
          str_key: 'str_engine_type',
          str_value: props.engineType,
        },
        {
          str_key: 'int_state',
          str_value: '1',
        },
      ]
      taskOptions.value = (await queryBaseTask(queryList)).map((item) => {
        return {
          key: item.id,
          label: item.str_task_name,
        }
      })
    }

    // 根据任务类型获取任务下拉框数据
    const getOptionsByType = (type) => {
      switch (type) {
        case 1:
          return flowOptions.value
        case 2:
          return typeOptions.value
        case 3:
          return smOptions.value
        case 4:
          return taskOptions.value
        default:
          return []
      }
    }

    const GANTT_COLUMNS = [
      {
        name: 'text',
        label: 'Task name',
        width: '*',
        tree: true,
        align: 'left',
      },
      { name: 'start_date', label: 'Start time', width: 100 },
      { name: 'dt_end', label: 'End time', width: 100 },
      { name: 'duration', label: 'Duration', width: 100 },
      { name: 'dt_f2_3_end', label: 'F2/3 end', width: 100 },
    ]

    // 定义甘特图初始化方法
    const ganttInit = () => {
      gantt.plugins({
        auto_scheduling: true, // 自动调度
      })
      // 设置gantt列
      gantt.config.columns = GANTT_COLUMNS

      // 配置动态加载
      gantt.config.branch_loading = true
      gantt.config.show_loading = true
      gantt.config.load_level = 2 // 初始只加载2层

      // 配置延迟渲染
      gantt.config.smart_rendering = true
      gantt.config.smart_scales = true
      gantt.config.grid_width = 650
      // 设置时间格式
      gantt.config.date_format = '%Y-%m-%d'
      // 禁用可连接的任务
      gantt.config.drag_links = false
      // 禁用进度条拖拽
      gantt.config.drag_progress = false
      gantt.config.autoscroll = true
      gantt.config.auto_scheduling = true // 自动调度
      // gantt.config.auto_scheduling_strict = true;// 自动调度
      gantt.config.auto_scheduling_compatibility = true // 自动调度
      // 不可拖动
      gantt.config.drag_move = false

      gantt.config.scale_height = 100
      // 头部增加周
      gantt.config.scales = [
        // 周
        { unit: 'week', step: 1, format: 'W%w' },
        // 星期
        { unit: 'day', step: 1, format: '%D' },
        // 天
        { unit: 'day', step: 1, format: '%m-%d' },
      ]

      const nameSection = {
        name: 'name',
        height: 38,
        map_to: 'id_task',
        type: 'select',
        options: [],
      }

      // 设置弹出框
      gantt.config.lightbox.sections = [
        {
          name: 'type',
          type: 'select',
          map_to: 'level',
          height: 28,
          options: [
            { key: 1, label: 'Flow' },
            { key: 2, label: 'Type' },
            { key: 3, label: 'Sm' },
            { key: 4, label: 'Task' },
          ],
          onchange: function (event) {
            console.log(event.target.value)
            // 获取任务
            const task = gantt.getTask(gantt.getState().lightbox)
            const level = parseInt(event.target.value)
            // 1. 先判断是否是新建任务
            if (task.$new) {
              // 给task添加level属性
              task.level = level
            }
            // 获取nameSelection的selection
            const nameSelection = gantt.getLightboxSection('name')
            // 获取nameSection的dom节点
            const nameSectionDom = nameSelection.node
            // 获取nameFlowSection的select控件
            const select = nameSectionDom.querySelector('select')
            // 设置select控件的options
            select.innerHTML = ''
            getOptionsByType(level).forEach((option) => {
              const optionDom = document.createElement('option')
              optionDom.value = option.key
              optionDom.text = option.label
              select.appendChild(optionDom)
            })
          },
        },
        {
          name: 'parent',
          height: 28,
          type: 'template',
          map_to: 'my_parent',
        },
        nameSection,
        {
          name: 'time',
          type: 'duration',
          map_to: 'auto',
          time_format: ['%Y', '%m', '%d'],
        },
      ]
      gantt.locale.labels.section_parent = 'Parent'
      gantt.locale.labels.section_name = 'Task Name'

      // 改变模板任务的样式
      gantt.templates.task_class = function (start, end, task) {
        return `sub-level-${task.level}`
      }

      // 禁用双击
      gantt.attachEvent('onTaskDblClick', function (id, e) {
        return false // 返回 false 阻止默认行为
      })
      // 改变弹出框头部布局
      gantt.templates.lightbox_header = function (start, end, task) {
        const currentStart = moment(start).format('YYYY-MM-DD')
        const currentEnd = moment(end).format('YYYY-MM-DD')
        return `
          <span class="font-bold">${task.text}</span>
          <span class="float-right">${currentStart} ~ ${currentEnd}</span>
        `
      }
      function createDisabledInput(parentName) {
        return /*html*/ `<input
          type="text"
          class="w-full mb-6 bg-gray-100 border border-gray-300 text-gray-900 text-sm"
          value="${parentName}"
          disabled
        />`
      }
      function setSelectOptions(select, options) {
        select.innerHTML = ''
        options.forEach((option) => {
          const optionDom = document.createElement('option')
          optionDom.value = option.key
          optionDom.text = option.label
          select.appendChild(optionDom)
        })
      }
      function handleSelectChange(select, task, level, section) {
        select.addEventListener('change', function () {
          const value = select.value
          let changeLevel = level
          if (changeLevel === 4) {
            task.id_task = value
          }
          if (task.$new) {
            changeLevel = parseInt(task.level)
            if (changeLevel === 4) {
              section.map_to = 'id_task'
            }
            task.id_task = value
          }
          if (!isNaN(changeLevel)) {
            task.text = _.find(getOptionsByType(changeLevel), { key: value }).label
          }
        })
      }
      // 打开弹框之前
      gantt.attachEvent('onBeforeLightbox', function (id) {
        const task = gantt.getTask(id)
        const level = parseInt(task.level)
        if (task.parent === '0') {
          // 提示不能编辑根任务
          gantt.message({ type: 'warning', text: '根任务不可编辑' })
          return false
        }
        // 判断父级任务的类型是不是task
        const parentTask = gantt.getTask(task.parent)
        // 获取父任务名称
        const parentName = task.parent !== '0' ? parentTask.text : 'no parent'
        // 判断是不是新增
        if (!task.$new) {
          // 不是新增,则让type下拉框禁用
          const typeSelection = gantt.getLightboxSection('type')
          const control = typeSelection.control
          control.disabled = true
        } else {
          const typeSelection = gantt.getLightboxSection('type')
          const select = typeSelection.node.querySelector('select')
          // 根据类型设置typeSelection的下拉框值
          const options = levelOptionsMap[parentTask.level]
          setSelectOptions(select, options)
        }
        // 创建一个禁用的input控件,其值为父任务名称
        task.my_parent = createDisabledInput(parentName)
        // 获取nameSelection的selection
        const nameSelection = gantt.getLightboxSection('name')
        // 获取nameSection的dom节点
        const nameSectionDom = nameSelection.node
        // 获取nameSelection的selection
        const section = nameSelection.section
        // 根据level改变map_to
        section.map_to = level !== 4 ? 'text' : 'id_task'
        // 获取nameFlowSection的select控件
        const select = nameSectionDom.querySelector('select')
        // 设置select控件的options
        setSelectOptions(select, getOptionsByType(level))
        // 设置change事件
        handleSelectChange(select, task, level, section)
        return true
      })
      // 更改弹出框之后重新渲染
      gantt.attachEvent('onAfterLightbox', function () {
        gantt.render()
      })

      const levelOptionsMap = {
        0: [
          { key: '', label: '' },
          { key: 1, label: 'Flow' },
        ],
        1: [
          { key: '', label: '' },
          { key: 2, label: 'Type' },
          { key: 3, label: 'Sm' },
          { key: 4, label: 'Task' },
        ],
        2: [
          { key: '', label: '' },
          { key: 3, label: 'Sm' },
          { key: 4, label: 'Task' },
        ],
        3: [
          { key: '', label: '' },
          { key: 4, label: 'Task' },
        ],
      }

      // 点击add
      gantt.attachEvent('onTaskClick', function (id) {
        // 每次进来都初始化lightBox
        gantt.resetLightbox()
        // 获取task
        const task = gantt.getTask(id)
        if (task.level === 4) {
          gantt.message({ type: 'warning', text: 'Task不可添加子任务' })
          return false
        }
        return true
      })
      // 删除任务之后
      gantt.attachEvent('onAfterTaskDelete', function (id, task) {
        if (task.parent === '0') {
          return
        }
        const parent = gantt.getTask(task.parent)
        if (parent) {
          const children = gantt.getChildren(parent.id)
          if (children.length === 0) {
            gantt.deleteTask(parent.id)
          }
        }
      })
    }

    const dynamicColumn = useDynamicColumn(gantt, GANTT_COLUMNS)

    // 保存
    const saveGantt = async () => {
      // 获取甘特图数据
      const data = gantt.serialize()?.data
      return await saveGantApi(_.map(data, transformGanttDataToSubPlans))
    }

    Vue.onMounted(async () => {
      gantt.clearAll()
      await getGanttData()
      await getFlowOptions()
      getTypeOptions()
      await getSmOptions()
      await getTaskOptions()
      ganttInit()
      gantt.init('gantt')
      gantt.parse(ganttData.value)
    })

    const formSearch = Vue.reactive({
      str_node: '',
    })
    const search = async () => {
      gantt.clearAll()
      await getGanttData()
      gantt.parse(ganttData.value)
      expandGantt()
      gantt.render()
    }
    const isCollapse = Vue.ref(false)
    // 折叠甘特图
    const collapseGantt = () => {
      isCollapse.value = false
      // 使用批量操作优化性能
      gantt.batchUpdate(() => {
        // 只折叠第一层，保持数据结构
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.close(taskId)
          }
        })
      })
    }
    // 展开甘特图
    const expandGantt = () => {
      isCollapse.value = true
      // 使用批量操作来优化性能
      gantt.batchUpdate(() => {
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.open(taskId)
            // 展开第二层
            const children = gantt.getChildren(taskId)
            children.forEach((childId) => gantt.open(childId))
          }
        })
      })
    }
    const isCollapseGrid = Vue.ref(false)
    const collapseGrid = () => {
      isCollapseGrid.value = !isCollapseGrid.value
      gantt.config.show_grid = false
      gantt.render()
    }
    const expandGrid = () => {
      isCollapseGrid.value = false
      gantt.config.show_grid = true
      gantt.render()
    }
    // 转到主单元体甘特图
    const toMainUnitGantt = () => {
      emit('toMainUnitGantt')
    }
    /**正式应用 */
    const activeProject = async () => {
      const res = await ElementPlus.ElMessageBox.confirm('是否正式应用该计划', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      if (res === 'confirm') {
        // 正式应用
        await activeProjectApi([props.id])
      }
    }

    const { handleToDeduction, smChartDrawerState } = useButtons(props.idWo)
    return {
      ganttData,
      saveGantt,
      ganttInit,
      formSearch,
      search,
      isCollapse,
      collapseGantt,
      expandGantt,
      toMainUnitGantt,
      isCollapseGrid,
      collapseGrid,
      expandGrid,
      activeProject,
      handleToDeduction,
      smChartDrawerState,
      ...dynamicColumn,
    }
  },
  template: /*html*/ `
    <div>
      <el-form :model="formSearch" inline size="default">
        <el-form-item label="">
          <el-input style="width:220px" v-model="formSearch.str_node" placeholder="请输入任务名称" clearable></el-input>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="search">搜索</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button v-if="isCollapse" type="primary" @click="collapseGantt">折叠甘特图</el-button>
          <el-button v-else type="primary" @click="expandGantt">展开甘特图</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button v-if="!isCollapseGrid" type="primary" @click="collapseGrid">收起grid</el-button>
          <el-button v-else type="primary" @click="expandGrid">展开grid</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="toMainUnitGantt">主单元体甘特图</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="handleToDeduction">跳转到推演</el-button>
          <el-button type="primary" @click="activeProject">正式应用</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex justify-end gap-2 mb-2">
      <DynamicColumnConfigurator
        v-model="visibleColumns"
        :all-columns="columnConfig"
        button-text="列配置"
        button-icon="Grid"
      />
    </div>
    <div id="gantt" class="h-[75vh] w-full"></div>

    <!-- 单元体抽屉 -->
    <UnitPlanDrawer
      v-if="smChartDrawerState.visible"
      v-model:visible="smChartDrawerState.visible"
      :id="smChartDrawerState.id"
    ></UnitPlanDrawer>
  `,
}
export default PlanGantt
