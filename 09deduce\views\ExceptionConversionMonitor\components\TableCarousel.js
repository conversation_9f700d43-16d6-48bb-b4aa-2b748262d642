const { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick, defineAsyncComponent } = Vue
const { useIntervalFn } = VueUse

export default {
  name: 'TableCarousel',
  components: {
    CarouselTable: defineAsyncComponent(() => import('./CarouselTable.js'))
  },
  props: {
    tables: {
      type: Array,
      required: true
    },
    autoPlay: {
      type: Boolean,
      default: true
    },
    playInterval: {
      type: Number,
      default: 8000
    }
  },
  setup(props) {
    const currentTableIndex = ref(0)
    const isTransitioning = ref(false)
    const containerRef = ref(null)
    
    // 当前显示的表格
    const currentTable = computed(() => {
      return props.tables[currentTableIndex.value] || {}
    })
    
    // 总表格数
    const totalTables = computed(() => {
      return props.tables.length
    })
    
    // 切换到下一个表格
    const nextTable = async () => {
      if (isTransitioning.value || totalTables.value <= 1) return
      
      isTransitioning.value = true
      
      // 添加淡出效果
      if (containerRef.value) {
        containerRef.value.style.opacity = '0'
        containerRef.value.style.transform = 'translateX(20px)'
      }
      
      await new Promise(resolve => setTimeout(resolve, 200))
      
      currentTableIndex.value = (currentTableIndex.value + 1) % totalTables.value
      
      await nextTick()
      
      // 添加淡入效果
      if (containerRef.value) {
        containerRef.value.style.opacity = '1'
        containerRef.value.style.transform = 'translateX(0)'
      }
      
      setTimeout(() => {
        isTransitioning.value = false
      }, 200)
    }
    
    // 切换到上一个表格
    const prevTable = async () => {
      if (isTransitioning.value || totalTables.value <= 1) return
      
      isTransitioning.value = true
      
      if (containerRef.value) {
        containerRef.value.style.opacity = '0'
        containerRef.value.style.transform = 'translateX(-20px)'
      }
      
      await new Promise(resolve => setTimeout(resolve, 200))
      
      currentTableIndex.value = currentTableIndex.value === 0 
        ? totalTables.value - 1 
        : currentTableIndex.value - 1
      
      await nextTick()
      
      if (containerRef.value) {
        containerRef.value.style.opacity = '1'
        containerRef.value.style.transform = 'translateX(0)'
      }
      
      setTimeout(() => {
        isTransitioning.value = false
      }, 200)
    }
    
    // 跳转到指定表格
    const goToTable = async (index) => {
      if (isTransitioning.value || index === currentTableIndex.value || index >= totalTables.value) return
      
      isTransitioning.value = true
      
      if (containerRef.value) {
        containerRef.value.style.opacity = '0'
        containerRef.value.style.transform = 'scale(0.98)'
      }
      
      await new Promise(resolve => setTimeout(resolve, 200))
      
      currentTableIndex.value = index
      
      await nextTick()
      
      if (containerRef.value) {
        containerRef.value.style.opacity = '1'
        containerRef.value.style.transform = 'scale(1)'
      }
      
      setTimeout(() => {
        isTransitioning.value = false
      }, 200)
    }
    
    // 自动播放
    const { pause, resume } = useIntervalFn(() => {
      if (props.autoPlay && !isTransitioning.value) {
        nextTable()
      }
    }, props.playInterval)
    
    // 表格指示器
    const tableIndicators = computed(() => {
      return props.tables.map((table, index) => ({
        index,
        title: table.title,
        active: index === currentTableIndex.value
      }))
    })
    
    // 进度条
    const progressPercentage = computed(() => {
      return ((currentTableIndex.value + 1) / totalTables.value) * 100
    })
    
    onMounted(() => {
      if (props.autoPlay) {
        resume()
      }
      
      // 初始化容器样式
      if (containerRef.value) {
        containerRef.value.style.transition = 'all 0.3s ease-in-out'
      }
    })
    
    onBeforeUnmount(() => {
      pause()
    })
    
    return {
      currentTableIndex,
      currentTable,
      totalTables,
      isTransitioning,
      containerRef,
      nextTable,
      prevTable,
      goToTable,
      tableIndicators,
      progressPercentage
    }
  },
  template: /*html*/ `
    <div class="table-carousel-wrapper h-full w-full flex flex-col">
      <!-- 轮播控制栏 -->
      <div class="carousel-controls bg-white shadow-sm p-4 border-b w-full">
        <div class="flex items-center justify-between mb-3 w-full">
          <div class="flex items-center space-x-4">
            <h2 class="text-xl font-bold text-gray-800">监控表格轮播</h2>
            <div class="text-sm text-gray-500">
              {{ currentTableIndex + 1 }} / {{ totalTables }}
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <button 
              @click="prevTable"
              :disabled="isTransitioning"
              class="control-btn bg-gray-100 hover:bg-gray-200 text-gray-600 p-2 rounded-lg transition-all disabled:opacity-50"
            >
              <el-icon><ArrowLeft /></el-icon>
            </button>
            <button 
              @click="nextTable"
              :disabled="isTransitioning"
              class="control-btn bg-gray-100 hover:bg-gray-200 text-gray-600 p-2 rounded-lg transition-all disabled:opacity-50"
            >
              <el-icon><ArrowRight /></el-icon>
            </button>
          </div>
        </div>
        
        <!-- 表格导航 -->
        <div class="flex items-center justify-center space-x-2 mb-3 w-full">
          <button
            v-for="indicator in tableIndicators"
            :key="indicator.index"
            @click="goToTable(indicator.index)"
            :class="[
              'table-indicator px-3 py-1 rounded-full text-xs font-medium transition-all duration-200',
              indicator.active 
                ? 'bg-blue-500 text-white shadow-md' 
                : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
            ]"
          >
            {{ indicator.title }}
          </button>
        </div>
        
        <!-- 进度条 -->
        <div class="progress-container w-full bg-gray-200 rounded-full h-1">
          <div 
            class="progress-bar bg-blue-500 h-1 rounded-full transition-all duration-300 ease-out"
            :style="{ width: progressPercentage + '%' }"
          ></div>
        </div>
      </div>
      
      <!-- 表格内容区域 -->
      <div class="carousel-content flex-1 bg-gray-50 p-3 overflow-hidden w-full">
        <div 
          ref="containerRef"
          class="table-container h-full w-full"
        >
          <CarouselTable
            v-if="currentTable.data"
            :table-data="currentTable.data"
            :table-columns="currentTable.columns"
            :title="currentTable.title"
            :bg-color="currentTable.bgColor"
            :auto-scroll="currentTable.autoScroll"
            :scroll-interval="currentTable.scrollInterval"
          />
        </div>
      </div>
    </div>
  `
} 