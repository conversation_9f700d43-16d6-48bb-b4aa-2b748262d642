import {
  queryF2TargetEnum,
  queryEnum,
  queryF2TaskEnum,
  queryShifts,
  saveBatchF2TeamPlan,
  queryTeamStaff,
  queryDeptStaff,
} from '../../../api/teams/index.js'
const { ElMessage } = ElementPlus
export default {
  name: 'TeamPlanAdd',
  props: {
    visible: Boolean,
    deptId: String,
    teamId: String,
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const { reactive, onMounted, ref } = Vue
    const { useVModel } = VueUse
    const teamPlanEditVisible = useVModel(props, 'visible')

    // 团队计划数据
    const planData = reactive({
      scheduleAdjust: '',
      isWholeMove: false,
      planItems: [{}],
    })

    // 计算计划项数量
    const planItemsCount = ref(0)

    // 保存计划
    const handleSave = async () => {
      try {
        // 触发表单验证
        await formRef.value.validate()

        // 添加自定义验证逻辑
        const isValid = planData.planItems.every((item) => {
          // 检查排班日期
          if (!item.dt_shift_time || item.dt_shift_time.length !== 2) {
            ElMessage.error('排班日期必须填写')
            return false
          }
          return true
        })

        if (!isValid) return

        const res = await saveBatchF2TeamPlan(planData.planItems)
        if (res.msg) {
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: res.msg.replace(/(\n|\r|\r\n|↵)/g, '<br/>') || 'Error',
            type: 'error',
          })
        }
        teamPlanEditVisible.value = false
        emit('refresh')
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    }

    const f2TaskOptions = ref([])
    // 获取Site 下拉选择
    const getF2TaskOptions = async () => {
      const res = await queryF2TaskEnum()
      f2TaskOptions.value = res ?? []
    }

    const shiftOptions = ref([])
    // 获取Site 下拉选择
    const getShiftOptions = async () => {
      const res = await queryShifts()
      shiftOptions.value = res.data ?? []
    }

    const teamStaffs = ref([])
    //获取F2交付目标
    const getTeamStaff = async () => {
      const res = await queryTeamStaff(props.teamId, props.deptId, 'RUOR')
      teamStaffs.value = res ?? []
    }

    const deptStaffs = ref([])
    //获取F2交付目标pt_team_member
    const getDeptStaff = async () => {
      const res = await queryDeptStaff(props.deptId)
      deptStaffs.value = res ?? []
    }

    const f2TargetOptions = ref([])
    //获取F2交付目标
    const getf2TargetOptions = async () => {
      const res = await queryF2TargetEnum()
      f2TargetOptions.value = res ?? []
    }

    const typeOptions = ref([])
    //获取Type下拉选择
    const getTypeOptions = async () => {
      const res = await queryEnum('pb_f2_type')
      typeOptions.value = res ?? []
    }

    const repairTypeOptions = ref([])
    //获取Repair Type下拉选择
    const getRepairTypeOptions = async () => {
      const res = await queryEnum('pb_f2_repair_type')
      repairTypeOptions.value = res ?? []
    }

    // 添加表单引用
    const formRef = ref(null)
    // 验证规则配置
    const formRules = reactive({
      int_task_type: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
      id_f2_target: [{ required: true, message: '请选择发动机', trigger: 'change' }],
      staffs: [
        {
          validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
              callback(new Error('请至少选择一个人员'))
            } else {
              callback()
            }
          },
          trigger: 'change',
        },
      ],
      tasks: [
        {
          validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
              callback(new Error('请至少选择一个任务'))
            } else {
              callback()
            }
          },
          trigger: 'change',
        },
      ],
      teamSecs: [
        {
          validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
              callback(new Error('请至少选择一个借调人员'))
            } else {
              callback()
            }
          },
          trigger: 'change',
        },
      ],
      dt_shift_time: [
        {
          validator: (rule, value, callback) => {
            if (!value || value.length !== 2) {
              callback(new Error('请选择完整的排班日期'))
            } else {
              callback()
            }
          },
          trigger: 'change',
        },
      ],
    })

    const addTaskPlan = () => {
      planData.planItems.push({
        // staffs:[props.planItem.id_staff],
        // dt_shift:props.planItem.dt_shift,
        // id_shift:props.planItem.task[0].id_shift,
        // dt_shift_time:props.planItem.task[0].dt_shift_time
      })
      planItemsCount.value = planData.planItems.length
    }

    const delTaskPlan = (task, index) => {
      planData.planItems.splice(index, 1)
      planItemsCount.value = planData.planItems.length
    }

    const handleEsnChange = (val, item, index) => {
      const target = f2TargetOptions.value.find((target) => target.id === val)
      console.log(target, 'target')
      if (target) {
        item.str_type = target.str_type
        // 如果target.dt_begin和target.dt_end为空，则不设置目标范围
        if (target.dt_begin && target.dt_end) {
          item.dt_target_range = [target.dt_begin, target.dt_end]
        }
        // 清空排班日期，因为目标范围变化了
        item.dt_shift_time = null
      }
    }

    const disabledDate = (date, item) => {
      if (item.dt_target_range && item.dt_target_range.length === 2) {
        // 禁用超出目标范围的日期（只允许在目标范围内选择）
        return !moment(date).isBetween(item.dt_target_range[0], item.dt_target_range[1], 'day', '[]')
      }
      return false
    }

    onMounted(async () => {
      await getF2TaskOptions()
      await getf2TargetOptions()
      await getTypeOptions()
      await getShiftOptions()
      await getRepairTypeOptions()
      await getDeptStaff()
      await getTeamStaff()
    })

    // 人员选择变化
    const handleStaffChange = (val) => {
      if (val && val.length > 0) {
        const warningStaffs = teamStaffs.value
          .filter((staff) => val.includes(staff.id))
          .filter((staff) => staff.cl_status === 1)
        if (warningStaffs.length > 0) {
          ElMessage.warning(
            `${warningStaffs.map((staff) => staff.str_name).join('、')} 已经上班${warningStaffs.map((staff) => staff.dbl_hour).join('、')}H`,
          )
        }
      }
    }

    const handleTeamSecChange = (val) => {
      if (val && val.length > 0) {
        const warningStaffs = deptStaffs.value
          .filter((staff) => val.includes(staff.id))
          .filter((staff) => staff.cl_status === 1)
        if (warningStaffs.length > 0) {
          ElMessage.warning(
            `${warningStaffs.map((staff) => staff.str_name).join('、')} 已经上班${warningStaffs.map((staff) => staff.dbl_hour).join('、')}H`,
          )
        }
      }
    }
    return {
      f2TaskOptions,
      formRules,
      formRef,
      f2TargetOptions,
      typeOptions,
      repairTypeOptions,
      teamPlanEditVisible,
      planData,
      planItemsCount,
      disabledDate,
      handleSave,
      shiftOptions,
      teamStaffs,
      deptStaffs,
      addTaskPlan,
      delTaskPlan,
      handleEsnChange,
      handleStaffChange,
      handleTeamSecChange,
    }
  },
  template: /*html*/ `
    <el-dialog
      v-model="teamPlanEditVisible"
      title="Team Plan"
      width="80%"
      class="common-dialog"
      :fullscreen="false"
      :append-to-body="true"
    >
      <div class="pr-2">
        <el-form :model="planData" label-width="150px" ref="formRef" :rules="formRules">
          <div class="my-2">
            <el-badge :value="planItemsCount" class="item" type="info">
              <el-button>Num</el-button>
            </el-badge>
          </div>

          <div
            v-for="(item, index) in planData.planItems"
            :key="item.id"
            class="my-2 flex flex-col rounded-md border p-4"
          >
            <div>
              <el-button style="float: right;" type="danger" size="small" @click="delTaskPlan(item,index)">
                Delete
              </el-button>
            </div>

            <div class="grid grid-cols-1 gap-4 pt-2 md:grid-cols-3">
              <el-form-item label="Name" required :prop="'planItems.' + index + '.staffs'" :rules="formRules.staffs">
                <el-select
                  v-model="item.staffs"
                  filterable
                  multiple
                  placeholder="请选择人员"
                  @change="handleStaffChange"
                >
                <!--:disabled="sub.cl_status === 2"-->
                  <el-option
                    v-for="sub in teamStaffs"
                    :key="sub.id"
                    :label="sub.str_name"
                    :value="sub.id"
                    
                  >
                    <span :class="{'text-yellow-500': sub.cl_status === 1, 'text-red-500': sub.cl_status === 2}">
                      {{ sub.str_name }}({{ sub.dbl_hour }}H)
                      <!-- <span v-if="sub.cl_status === 1" class="text-xs ml-1">(已上班120H)</span> -->
                      <!-- <span v-if="sub.cl_status === 2" class="text-xs ml-1">(不可选)</span> -->
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="ESN"
                required
                :prop="'planItems.' + index + '.id_f2_target'"
                :rules="formRules.id_f2_target"
              >
                <el-select
                  filterable
                  v-model="item.id_f2_target"
                  @change="(val) =>handleEsnChange(val,item,index)"
                  placeholder="请选择F2交付目标"
                >
                  <el-option v-for="sub in f2TargetOptions"  :key="sub.id" :label="sub.str_esn" :value="sub.id">
                    <span style="float: left">{{ sub.str_esn }}</span>
                    <span
                      style="
                      float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;
                    "
                    >
                      <!-- {{ sub.dt_begin.slice(0, 10) }}~{{sub.dt_end.slice(0, 10)}}   -->
                      {{ sub.str_type }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="Type">
                <el-select v-model="item.str_type" c placeholder="Select" disabled>
                  <el-option
                    v-for="sub in typeOptions"
                    :key="sub.str_value"
                    :label="sub.str_key"
                    :value="sub.str_value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="Task Type"
                required
                :prop="'planItems.' + index + '.int_task_type'"
                :rules="formRules.int_task_type"
              >
                <el-select v-model="item.int_task_type" placeholder="Select" >
                  <el-option
                    v-for="sub in repairTypeOptions"
                    :key="sub.str_value"
                    :label="sub.str_key"
                    :value="sub.str_value"
                    
                  />
                </el-select>
              </el-form-item>

              <el-form-item required label="Task" 
              :prop="'planItems.' + index + '.tasks'" 
              :rules="formRules.tasks" >  
                <el-select
                  v-model="item.tasks"
                  multiple
                  filterable
                  :selectedValues="item.tasks"
                  placeholder="请选择任务"
                  class="w-full"
                >
                  <el-option
                    v-for="sub in f2TaskOptions"
                    :key="sub.str_value"
                    :label="sub.str_key"
                    :value="sub.str_value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                label="Team Secs"
                :prop="'planItems.' + index + '.teamSecs'"
              >
                <el-select
                  v-model="item.teamSecs"
                  multiple
                  filterable
                  placeholder="请选择借调人员"
                  @change="handleTeamSecChange"
                >
                  <el-option
                    v-for="item in deptStaffs"
                    :key="item.id"
                    :label="item.str_name"
                    :value="item.id"
                    :disabled="item.cl_status === 2"
                  >
                    <span :class="{'text-yellow-500': item.cl_status === 1, 'text-red-500': item.cl_status === 2}">
                      {{ item.str_name }}({{ item.dbl_hour }}H)
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="目标范围">
                <el-date-picker
                  disabled
                  v-model="item.dt_target_range"
                  type="daterange"
                  range-separator="To"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                />
              </el-form-item>
              <el-form-item label="排班日期">
                <el-date-picker
                  v-model="item.dt_shift_time"
                  type="daterange"
                  range-separator="To"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  :disabled-date="(date) => disabledDate(date,item)"
                />
              </el-form-item>
            </div>
            <el-form-item label="Remark" class="pt-2">
              <el-input v-model="item.str_notes" type="textarea" :rows="3" placeholder="请输入备注" />
            </el-form-item>
          </div>
        </el-form>
      </div>
      <el-button style="margin-left:20px; float:right" size="default" type="success" @click="addTaskPlan()">
        +New Scheduling
      </el-button>

      <template #footer>
        <div class="flex justify-end">
          <el-button @click="teamPlanEditVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>
  `,
}
