import { useOption } from '../../../hooks/useOption.js'
import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
import { post } from '../../../../config/axios/httpReuest.js'
import { reSelectResourcePlanApi } from '../../../api/simulateAfterDrawerApi.js'

const { useVModel } = VueUse
const { ref, reactive, onMounted, toRefs } = Vue
/**
 * @description 重新选择资源抽屉
 * @date 2024年8月7日20:58:56
 */
const ReSelectResourceDrawer = {
  components: {
    HtVxeTable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    params: {
      type: Object,
      required: true,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)

    const { customerOptions, getCustomerOptions } = useOption()
    const currentForm = reactive({
      customer: props.params.customer,
      isYellow: null,
    })

    const tableRef = ref(null)
    // 表格数据
    const tableState = reactive({
      data: null,
      columns: [
        {
          title: '零件来源',
          field: 'str_source_type',
          fixed: 'left',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'Kitting完成/站点',
          field: 'str_nodename',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原ESN',
          field: 'str_esn_sp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原WO',
          field: 'str_wo_sp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PN',
          field: 'str_pn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'SM',
          field: 'str_sm',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件名称',
          field: 'str_part_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件标签',
          field: 'str_label',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件条码',
          field: 'str_bcode',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '仓库',
          field: 'str_wh_name',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台客户',
          field: 'str_client_sp',
          minWidth: 160,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '货位名称',
          field: 'str_product_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '批次',
          field: 'str_batch',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PKP',
          field: 'id_pkp',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '要求证书',
          field: 'str_certificate_copy',
          minWidth: 130,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '供体零件证书',
          field: 'str_certificate',
          minWidth: 130,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '数量',
          minWidth: 150,
          field: 'int_num',
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
          editRender: { name: 'input' },
        },
        {
          title: 'EKD',
          minWidth: 120,
          field: 'dt_ekd',
          filters: [{ data: '' }],
          fiterRender: { name: 'FilterInput' },
        },
      ],
      total: 0,
    })

    // * 获取表格数据
    const getTableData = async (paramsData) => {
      const { idWo, pn } = paramsData
      const params = {
        ac: 'de_query_resource_plan',
        id_wo: idWo,
        str_pn: pn,
        clientIds: currentForm.customer,
        is_yellow: currentForm.isYellow,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        tableState.data = data.data
        tableState.total = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    const editRowEvent = (row) => {
      const $table = tableRef.value.xTable
      $table.setEditRow(row, 'int_num')
    }

    // * 提交事件
    const sumbitEvent = async () => {
      const { id } = props.params
      // 获取勾选的表格数据
      const tableData = tableRef.value.xTable.getCheckboxRecords()
      //  判断数据中的int_num是否为数字
      const result = tableData.every((item) => {
        return !isNaN(item.int_num)
      })
      if (!result) {
        ElementPlus.ElMessage.error('数量必须为数字')
        return
      }
      const boolean = await reSelectResourcePlanApi(id, tableData)
      if (boolean) {
        visible.value = false
      }
    }
    onMounted(() => {
      getTableData(props.params)
      getCustomerOptions()
    })
    return {
      visible,
      currentForm,
      customerOptions,
      tableRef,
      ...toRefs(tableState),
      getTableData,
      editRowEvent,
      sumbitEvent,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="75%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-white">重新选择资源</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <el-form :model="currentForm" inline>
        <el-form-item label="客户">
          <el-select
            v-model="currentForm.customer"
            placeholder="请选择"
            multiple
            filterable
            clearable
            collapse-tags
            style="width: 220px"
          >
            <el-option
              v-for="item in customerOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="限定已开黄标签">
          <el-select v-model="currentForm.isYellow" placeholder="请选择" style="width: 220px" clearable>
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getTableData(params)">查询</el-button>
        </el-form-item>
      </el-form>
      <div style="height: calc(100% - 90px)">
        <div class="flex justify-end items-center">
          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable
          ref="tableRef"
          :tableData="data"
          :tableColumns="columns"
          :isShowHeaderCheckbox="true"
          @filterChange="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
          <template #operation>
            <vxe-column title="操作" width="100" fixed="right">
              <template #default="{ row }">
                <vxe-button @click="editRowEvent(row)">编辑</vxe-button>
              </template>
            </vxe-column>
          </template>
        </HtVxeTable>
      </div>
      <template #footer>
        <el-button type="primary" @click="sumbitEvent">确定</el-button>
        <el-button @click="visible = false">取消</el-button>
      </template>
    </el-drawer>
  `,
}

export default ReSelectResourceDrawer
