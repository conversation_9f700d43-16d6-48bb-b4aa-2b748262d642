import { post } from '../../utils/request.js'
/**
 * 排班管理
 */

// 获取部门数据
export const getDepartmentsOrBranches = async ({ isbranch = 'false', id_top = '' }) => {
  const params = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'rcpa_get_hr_dept',
    isbranch,
    id_top,
  }
  return post('/api/Do/DoAPI', params)
}

/**
 * 获取排班数据
 * @param {string} dt_plan - 日期
 * @param {string} id_dept - 部门id
 * @param {string} id_sec - 分部id
 * @param {number} int_type - 类型
 * @param {string} id - 排班id
 * @returns {Promise<Array>} 排班数据
 */
export const queryScheduleData = async ({
  dt_plan = moment().format('YYYY-MM-DD'),
  id_dept = '',
  id_sec = '',
  int_type = 0,
  id = '',
}) => {
  const params = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_ptflexiblelist',
    query: {
      dt_plan,
      id_dept,
      id_sec,
      int_type,
      id,
    },
  }
  return post('/api/Do/DoAPI', params)
}

/**
 * 保存排班之前的校验
 * @param {Object} postData - 排班数据，包含main、ptShift和schedulingShift
 * @returns {Promise<Object>} 保存结果
 */
export const validateScheduleData = async (postData) => {
  const params = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_confirm_saveptshift',
    postData,
  }
  return post('/api/Do/DoAPI', params)
}

/**
 * 保存排班数据
 * @param {Object} postData - 排班数据，包含main、ptShift和schedulingShift
 * @returns {Promise<Object>} 保存结果
 */
export const saveScheduleData = async (postData) => {
  const params = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_ptflexible',
    postData,
  }
  return post('/api/Do/DoAPI', params)
}

/**
 * 保存灵活排班数据（新接口）
 * @param {Object} postData - 排班数据，包含main和ptShift
 * @returns {Promise<Object>} 保存结果
 */
export const saveFreeShiftsData = async (postData) => {
  const params = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_freeshifts',
    postData,
  }
  return post('/api/Do/DoAPI', params)
}

// ------------------灵活排班-----------
/**
 * 获取灵活排班数据
 * @param {string} id_sec - 分部ID
 * @param {string} id_dept - 部门ID
 * @returns {Promise<Array>} 排班数据
 */
export const getFlexibleScheduleData = async (id_sec, id_dept) => {
  const params = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_getcalendarforfree',
    query: {
      id_dept,
      id_sec,
    },
  }
  return post('/api/Do/DoAPI', params)
}

/**
 * 获取排班状态
 * @param {Object} params - 参数
 * @returns {Promise<Array>} 排班状态
 */
export const getScheduleStatusApi = async (query) => {
  const params = {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_ptshift_status',
    query,
  }
  return post('/api/Do/DoAPI', params)
}
