import { post } from '../../config/axios/httpReuest.js'
import { useFilter } from './useFilter.js'
import { useTableColumn } from './useTableColumn.js'

const { ref, nextTick, shallowRef, reactive } = Vue

export function useDMView() {
  let option = {
    grid: {
      left: '3%',
      right: '7%',
      bottom: '7%',
      containLabel: true,
    },
    tooltip: {
      showDelay: 0,
    },
    visualMap: {
      show: false,
      seriesIndex: 0,
      dimension: 2,
      pieces: [
        { value: 0, color: '#0000FF' },
        { value: 1, color: '#00aa03' }, // 绿色
        { value: 2, color: '#FFFF00' }, // 黄
        { value: 3, color: '#FF0000' }, // 红l
        { value: -999, color: '#0000FF' },
      ],
    },
    // 工具栏
    toolbox: {
      show: true,
      feature: {
        // 保存为图片
        saveAsImage: {
          show: true,
          title: '保存为图片',
          name: 'DM',
        },
      },
    },
    xAxis: [
      {
        type: 'value',
        scale: true,
        axisLabel: {
          formatter: '{value}',
        },
        splitLine: {
          show: true,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#f00',
            width: 1,
            join: 'round',
          },
        },
        axisTick: {
          show: true,
          lineStyle: {
            type: 'dotted',
            color: '#000',
            width: 1,
            cap: 'square',
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        scale: true,
        axisLabel: {
          formatter: '{value}',
        },
        splitLine: {
          show: true,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#f00',
            width: 1,
            join: 'round',
          },
        },
        axisTick: {
          show: true,
          lineStyle: {
            type: 'dotted',
            color: '#000',
            width: 1,
            cap: 'square',
          },
        },
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'none',
        startValue: -50,
        endValue: 50,
        brushSelect: false,
        top: 'top',
      },
      {
        type: 'slider',
        yAxisIndex: 0,
        startValue: -50,
        endValue: 50,
        filterMode: 'none',
        brushSelect: false,
      },
    ],
    series: [
      {
        name: 'DM',
        type: 'scatter',
        emphasis: {
          focus: 'series',
        },
        label: {
          show: true,
          textStyle: {
            color: '#f8f6f6',
            fontSize: 12,
          },
        },
        data: [],
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
        animationDelay: function (idx) {
          return idx * 5
        },
      },
    ],
  }

  const { getAllFilter } = useFilter()
  const { getDMTableColumn } = useTableColumn()
  const chartRef = shallowRef(null)
  const myChart = shallowRef(null)

  // 初始化图表
  const initChart = (currentNode, searchForm) => {
    myChart.value.setOption(option)
    myChart.value.hideLoading()
    myChart.value.off('click')
    myChart.value.on('click', 'series', (params) => {
      const data = dmViewData.value[params.dataIndex]
      drawerState.isShowDrawer = true
      nextTick(() => {
        getDrawerTableData(currentNode, data.int_d, data.int_m, searchForm)
      })
    })
  }

  const dmViewData = ref([])
  const total = ref(0)

  // 获取DM数据和总数
  const getDMDataAndTotal = async (currentNode, searchForm) => {
    const params = {
      ac: 'pda_DM_view',
      str_type: currentNode,
      filter_fields: getAllFilter(searchForm),
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      dmViewData.value = data.data
      total.value = data.data.reduce((prev, cur) => {
        return prev + cur.int_num
      }, 0)
    } else {
      ElementPlus.ElMessage.error(data.text)
      myChart.value.dispose()
      return
    }
  }
  /**
   * 获取DM数据
   * @param { string } currentNode 当前节点
   * @param { object } searchForm 搜索条件
   */
  const getDMData = async (currentNode, searchForm) => {
    myChart.value = echarts.init(chartRef.value)
    myChart.value.showLoading({
      text: 'loading...',
      showSpinner: true,
      textColor: 'black',
    })
    await getDMDataAndTotal(currentNode, searchForm)
  }

  // 获取series的data
  const getSeriesData = () => {
    option.series[0].data = dmViewData.value.map((item) => {
      const list = []
      list.push(item.int_m)
      list.push(item.int_d)
      if (item.colorType === 1) {
        list.push(3)
      } else {
        list.push(item.int_level)
      }
      list.push(item.str_num.length)
      return list
    })

    option.series[0].symbolSize = function (dataItem) {
      return dataItem[3] * 15
    }
    option.series[0].label.formatter = function (param) {
      return dmViewData.value[param.dataIndex].str_num
    }
  }

  // 获取tooltip的formatter
  const getTooltipFormatter = () => {
    option.tooltip.formatter = function (params) {
      const data = dmViewData.value[params.dataIndex]
      return `D ${data.int_d} </br> M ${data.int_m}`
    }
  }

  const nodeValue = ref('')
  // 包装请求的所有方法
  const getMethods = async (currentNode, searchForm) => {
    nodeValue.value = currentNode
    await getDMData(currentNode, searchForm)
    getSeriesData()
    getTooltipFormatter()
    initChart(currentNode, searchForm)
  }

  window.addEventListener('resize', () => {
    myChart.value.resize()
  })
  const drawerState = reactive({
    isShowDrawer: false,
    tableData: null,
    tableColumn: [],
    totalNum: 0,
  })

  // 获取抽屉表格数据
  const getDrawerTableData = async (currentNode, str_d, str_m, searchForm) => {
    drawerState.tableColumn = []
    drawerState.tableData = null
    drawerState.totalNum = 0
    const params = {
      ac: 'pda_DM_Pn_list',
      str_d,
      str_m,
      str_type: currentNode,
      filter_fields: getAllFilter(searchForm),
      sort_fields: [],
    }
    const { data } = await post(params)
    if (data.code === 'error') {
      ElementPlus.ElMessage.error(data.data)
      return
    }
    drawerState.tableColumn = getDMTableColumn(currentNode)
    drawerState.tableData = data.data
    drawerState.totalNum = data.data.length
  }

  // 点击总数获取抽屉表格数据
  const handleTotalClick = (searchForm) => {
    // 过滤searchForm空数据
    const realSearchForm = Object.keys(searchForm).reduce((prev, cur) => {
      if (searchForm[cur] !== '') {
        prev[cur] = searchForm[cur]
      }
      return prev
    }, {})
    drawerState.isShowDrawer = true
    nextTick(() => {
      getDrawerTableData(nodeValue.value, '', '', realSearchForm)
    })
  }

  return {
    chartRef,
    getMethods,
    drawerState,
    total,
    handleTotalClick,
  }
}
