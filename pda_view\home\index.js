import ErrorComponent from '../../components/error.component.js'
import LoadingComponent from '../../components/loading.component.js'
import { useCommApi } from '../hooks/useCommApi.js'
import { useCharData } from './useCharData.js'
import { useTable } from './useTable.js'
// import picture from '../../assets/image/svg/好评.png';
const { defineAsyncComponent, ref, reactive, onMounted, computed, onBeforeUnmount } = Vue
const { useIntervalFn } = VueUse
export default {
  name: 'PdaHomeComponent',
  components: {
    HtDrawer: defineAsyncComponent({
      loader: () => import('../../components/ht.drawer.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HtVxeTable: defineAsyncComponent({
      loader: () => import('../components/ht.vxe.table.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    SearchForm: defineAsyncComponent({
      loader: () => import('./searchForm.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup() {
    const currentDate = ref(moment().format('YYYY/MM/DD HH:mm:ss'))
    // 每秒更新时间
    const { pause, resume, isActive } = useIntervalFn(() => {
      currentDate.value = moment().format('YYYY/MM/DD HH:mm:ss')
    }, 1000)
    onBeforeUnmount(() => {
      pause()
    })

    const searchForm = reactive({
      // is_wip: '1',
      // id_engine_type: 'cfm56',
      is_positive: 0,
    })
    // search click
    const handleSearchClick = form => {
      Object.assign(searchForm, form)
      Promise.all([getChartData(searchForm, setNodes.value), getTableData(searchForm)])
      showSetDialog.value = false // 关闭设置框
    }

    const { chartRef, getChartData, chartState, charDrawerState, handleTotalClick } = useCharData()

    const { getTableData, tableState, handleCellClick, tableDrawerState, tableDrawerState1 } = useTable()
    const setNodes = ref([10, 15, 13, 42, 141, 43, 142, 8, 11, 2, 411, 5, 111, 1, 3, 41, 12, 412, 1000])
    const optionsSet = ref([
      { label: '点灯', value: 6 },
      { label: '转包', value: 10 },
      { label: 'F2', value: 15 },
      { label: '采购-厂家交付', value: 13 },
      { label: '锁库-(低)可用件&新件', value: 42 },
      { label: '采购-(低)可用件&新件', value: 141 },
      { label: '锁库-备板', value: 43 },
      { label: '采购-(高)可用件', value: 142 },
      { label: '进口运输', value: 8 },
      { label: '进口运输-预警', value: 1000 },
      { label: '起运准备', value: 11 },
      { label: '进厂检验', value: 2 },

      { label: 'CSM确认零件', value: 411 },
      { label: '发料', value: 5 },
      { label: '转包po', value: 111 },
      { label: '行政接收', value: 1 },
      { label: '入库', value: 3 },

      { label: '锁库-(高)可用件', value: 41 },
      { label: '出口运输', value: 12 },
      { label: '构型确认', value: 412 },
    ])
    const showSetDialog = ref(false)
    const openShowSetDialog = () => {
      showSetDialog.value = true
    }
    const setChange = () => {}

    const { burialPoint } = useCommApi()
    onMounted(() => {
      burialPoint('home')
      Promise.all([getChartData(searchForm, setNodes.value), getTableData(searchForm)])
    })

    return {
      chartRef,
      chartState,
      charDrawerState,
      currentDate,
      handleCellClick,
      handleSearchClick,
      handleTotalClick,
      searchForm,
      tableState,
      tableDrawerState,
      tableDrawerState1,
      showSetDialog,
      openShowSetDialog,
      optionsSet,
      setNodes,
      setChange,
    }
  },
  template: `
    <div class="flex items-stretch flex-col space-y-1 mx-4">
      <SearchForm @search="handleSearchClick"></SearchForm>
      <div class="flex-1 w-full border border-gray-300">
        <div class="flex items-center justify-between py-2 border">
          <div class="w-64 text-lg font-bold">当日PDA之汇总</div>
          <div class="overflow-hidden w-full whitespace-nowrap">
            <div class="scroll-content w-full inline-block">
              <div class="flex mr-4 p-2 items-center">
                  <img src="/Content/assets_huatek/webapp/assets/image/svg/好评.png" class="w-8 h-8 inline-block" />
                  {{ chartState.zeroData === '' ? 0 : chartState.zeroData }} 站点已完成消负任务！
                  <img src="/Content/assets_huatek/webapp/assets/image/svg/好评.png" class="w-8 h-8 inline-block" />
              </div>
            </div>
          </div>
          <div class="flex w-1/3">
            <div class="text-gray-400 mr-4">{{ currentDate }}</div>
            <div class="hover:text-blue-500 hover:cursor-pointer mr-4" @click="handleTotalClick(searchForm,setNodes)">
              总共: <span class="underline">{{ chartState.total }}</span>条
            </div>
          </div>
          <el-button class="mr-4" type="primary" @click="openShowSetDialog()">
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
        <div ref="chartRef" class="h-[80vh]"></div>
      </div>
      <div class="flex-1 w-full border mb-2">
        <div class="flex items-center justify-between py-2 border">
          <div class="text-lg font-bold">PDA DM 象限统计</div>
          <div class="mr-4">
            总共: <span>{{ tableState.total }}</span>条
          </div>
        </div>
        <el-table
          v-loading="tableState.isLoading"
          :data="tableState.data"
          border
          stripe
          height="800px"
          :default-sort="{prop: 'int_min_p', order: 'descending'}"
          @cell-click="(row, column) => handleCellClick(row, column, searchForm)"
        >
          <el-table-column type="index" label="#" width="50"></el-table-column>
          <el-table-column label="ESN" prop="str_esn"></el-table-column>
          <el-table-column label="WO" prop="str_wo"></el-table-column>
          <el-table-column label="SM" prop="str_sm"></el-table-column>
          <el-table-column label="P 零件项数" prop="int_min_p" sortable>
            <template #default="{ row }">
              <span class="hover:text-blue-500 underline hover:cursor-pointer">{{ row.int_min_p }}</span>
            </template>
          </el-table-column>
          <el-table-column label="M- 零件项数" prop="dbl_m_negative" sortable>
            <template #default="{ row }">
              <span class="hover:text-blue-500 underline hover:cursor-pointer">{{ row.dbl_m_negative }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最大M-值" prop="int_max_m" sortable></el-table-column>
          <el-table-column label="M+ 零件项数" prop="dbl_m_positive" sortable>
            <template #default="{ row }">
              <span class="hover:text-blue-500 underline hover:cursor-pointer">{{ row.dbl_m_positive }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最小M+值" prop="int_min_m" sortable></el-table-column>
        </el-table>
      </div>
    </div>
    <!--    当日PDA负之汇总抽屉-->
    <HtDrawer v-model:visible="charDrawerState.isShowDrawer" title="零件列表" :is-show-save="false">
      <HtVxeTable
        :tableData="charDrawerState.drawerTableData"
        :tableColumn="charDrawerState.tableColumn"
        :totalNum="charDrawerState.totalNum"
      ></HtVxeTable>
    </HtDrawer>

    <!--    PDA DM 象限统计抽屉-->
    <HtDrawer v-model:visible="tableDrawerState.isShowDrawer" title="零件列表" :is-show-save="false">
      <HtVxeTable
        :tableData="tableDrawerState.drawerTableData"
        :tableColumn="tableDrawerState.tableColumn"
        :totalNum="tableDrawerState.totalNum"
      ></HtVxeTable>
    </HtDrawer>

    <!--    PDA DM 象限统计抽屉 p负-->
    <HtDrawer v-model:visible="tableDrawerState1.isShowDrawer" title="零件列表" :is-show-save="false">
      <HtVxeTable
        :tableData="tableDrawerState1.drawerTableData"
        :tableColumn="tableDrawerState1.tableColumn"
        :totalNum="tableDrawerState1.totalNum"
      ></HtVxeTable>
    </HtDrawer>

    <HtDrawer v-model:visible="showSetDialog" title="显示站点设置" :is-show-save="true"  @save="handleSearchClick" size="25%">
    <el-select
    v-model="setNodes"
    multiple
    placeholder="Select"
    style="width: 240px" 
  >
  <!-- @change="setChange"-->
 
    <el-option
      v-for="item in optionsSet"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
  </HtDrawer>
  `,
}
