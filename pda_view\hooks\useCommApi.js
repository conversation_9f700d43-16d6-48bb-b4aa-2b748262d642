//公共基础数据接口
import { post } from '../../config/axios/httpReuest.js'

const { ref } = Vue

export function useCommApi() {
  // 获取ac接口
  const getUsers = async (str_dept_code) => {
    let params = {
      ac: 'comm_user_list',
      str_dept_code: str_dept_code,
    }
    const { data } = await post(params)
    return data.data
  }

  // 埋点接口
  const burialPoint = async (str_page) => {
    let params = {
      ac: 'pda_operation_log',
      data: {
        str_page: str_page,
      },
    }
    return await post(params)
  }

  return {
    getUsers,
    burialPoint,
  }
}
