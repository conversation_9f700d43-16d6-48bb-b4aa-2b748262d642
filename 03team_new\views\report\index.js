const { ref, onBeforeUnmount, reactive, onMounted, inject, provide, watch } = Vue
// import { useApi } from '../../comm/hooks/useApi.js'
// import { BottomRightComponent } from './components/bottom-right.js'

// import { TopRightComponent } from './components/top-right.js'

const ReportComponent = {
  props: ['station'],
  // components: {
  //   'top-right': TopRightComponent,
  //   'bottom-right': BottomRightComponent,
  // },
  setup(props) {
    const woid = ref('')
    // const { requestDataApi } = useApi()
    // 获取小组列表
    // const getWoByStation = async () => {
    //   const data = await requestDataApi({
    //     ac: 'pt_get_wo_by_station',
    //     station: props.station,
    //   })
    //   let woids = ''
    //   if (data.str_id_wo) {
    //     woids = data.str_id_wo.split(',')
    //   }
    //   let currentIndex = 0
    //   const printWoid = () => {
    //     if (currentIndex < woids.length) {
    //       woid.value = woids[currentIndex]
    //       currentIndex++
    //     } else {
    //       currentIndex = 0
    //     }
    //   }
    //   printWoid()
    //   setInterval(printWoid, 30000)
    // }

    // let intervalId = null
    // provide('wo-id', woid)
    onMounted(() => {
     // getWoByStation()
      // intervalId = setInterval(
      //   () => {
      //     getWoByStation()
      //   },
      //   5 * 60 * 1000,
      // )
    })

    onBeforeUnmount(() => {
      // if (intervalId) {
      //   clearInterval(intervalId)
      // }
    })
    return {
      woid,
    }
  },
  //   <div class="h-10 flex items-center justify-center">
  //   <span class="text-2xl">{{ flow }} </span>
  //   <el-select v-model="teamValue" @change="changeTeam">
  //     <el-option v-for="item in teamOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
  //   </el-select>
  // </div>
  template: /*html*/ `
    <div class="flex h-screen flex-col">
      <div class="flex-1 bg-blue-100">
        <top-right :station="station"></top-right>
      </div>
      <div class="flex-1 relative">
        <!-- <bottom-right></bottom-right> -->
      </div>
    </div>
  `,
}

// app.component('report-component', ReportComponent)
// export { ReportComponent }
