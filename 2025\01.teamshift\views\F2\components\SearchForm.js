const { ref, onMounted, defineComponent } = Vue
import {
  queryTeamStaff,
  queryF2TaskEnum,
  queryShifts,
  queryEnum,
} from '../../../api/teams/index.js'

// 搜索表单组件
export default defineComponent({
  name: 'SearchForm',
  emits: ['search', 'toggle-header', 'add'],
  setup(props, { emit }) {
    const searchFormRef = ref(null)
    const searchParams = ref({
      dt_date: [moment().format('YYYY-MM-DD'), moment().add(14, 'days').format('YYYY-MM-DD')],
      str_esn: '',
      id_staff: '',
      id_task: '',
      id_shift: '',
      str_task_type: '',
    })

    // 从父组件接收表头展开/收起状态
    const isHeaderExpanded = ref(false)

    // 切换表头显示/隐藏状态
    const toggleHeader = () => {
      isHeaderExpanded.value = !isHeaderExpanded.value
      emit('toggle-header', isHeaderExpanded.value)
    }

    const staffList = ref([])
    const getStaffList = async () => {
      const res = await queryTeamStaff('', '', 'RUOR')
      staffList.value = res ?? []
    }

    const taskList = ref([])
    const getTaskList = async () => {
      const res = await queryF2TaskEnum()
      taskList.value = res ?? []
    }

    const shiftList = ref([])
    const getShiftList = async () => {
      const res = await queryShifts()
      shiftList.value = res.data ?? []
    }

    const taskTypeList = ref([])
    const getTaskTypeList = async () => {
      const res = await queryEnum('pb_f2_type')
      taskTypeList.value = res ?? []
    }

    onMounted(() => {
      getStaffList()
      getTaskList()
      getShiftList()
      getTaskTypeList()
    })

    return {
      searchFormRef,
      searchParams,
      isHeaderExpanded,
      staffList,
      taskList,
      shiftList,
      taskTypeList,
      toggleHeader,
    }
  },
  template: /*html*/ `
    <el-form ref="searchFormRef" :model="searchParams" inline label-position="right">
      <el-form-item label="Date:">
        <el-date-picker
          style="width: 100%"
          clearable
          type="daterange"
          value-format="YYYY-MM-DD"
          v-model="searchParams.dt_date"
          start-placeholder="起始日期"
          end-placeholder="截止日期"
        />
      </el-form-item>
      <el-form-item label="发动机:">
        <el-input v-model="searchParams.str_esn" clearable />
      </el-form-item>
      <el-form-item label="人员:">
        <el-select class="!w-48" filterable v-model="searchParams.id_staff" clearable placeholder="请选择人员">
          <el-option v-for="item in staffList" :key="item.id" :label="item.str_name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务:">
        <el-select class="!w-48" filterable v-model="searchParams.id_task" clearable placeholder="请选择任务">
          <el-option v-for="item in taskList" :key="item.str_value" :label="item.str_key" :value="item.str_value" />
        </el-select>
      </el-form-item>
      <el-form-item label="班次:">
        <el-select class="!w-48" filterable v-model="searchParams.id_shift" clearable placeholder="请选择班次">
          <el-option v-for="item in shiftList" :key="item.id" :label="item.str_name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务类型:">
        <el-select class="!w-48" filterable v-model="searchParams.str_task_type" clearable placeholder="请选择任务类型">
          <el-option v-for="item in taskTypeList" :key="item.str_value" :label="item.str_key" :value="item.str_value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="$emit('search', searchParams)" icon="Search" />
      </el-form-item>
    </el-form>

    <div class="flex justify-end mb-2">
      <el-button type="primary" @click="$emit('add')" icon="Edit">Add</el-button>
    </div>
  `,
}) 