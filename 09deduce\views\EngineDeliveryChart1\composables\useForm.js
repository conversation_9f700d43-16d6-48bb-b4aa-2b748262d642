const { reactive } = Vue
export function useForm() {
  const formSearch = reactive({
    f4_1BeginTime: '',
    // 默认当前年的开始和结束
    realeaseTime: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')],
    f2_3ClosedTime: '',
    engineType: '',
    repairType: [],
  })

  /**
   * 重置查询表单
   */
  const handleRest = () => {
    formSearch.f4_1BeginTime = ''
    formSearch.realeaseTime = [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')]
    formSearch.f2_3ClosedTime = ''
    formSearch.engineType = ''
    formSearch.repairType = []
  }

  return {
    formSearch,
    handleRest,
  }
}
