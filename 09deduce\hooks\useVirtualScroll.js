const { ref, reactive, computed, onMounted, onUnmounted, nextTick } = Vue

/**
 * 虚拟滚动组合式函数
 * @param {Object} options - 配置选项
 * @param {Array} options.items - 数据数组
 * @param {number} options.itemHeight - 预估项目高度
 * @param {number} options.containerHeight - 容器高度
 * @param {number} options.overscan - 额外渲染的项目数量（缓冲区）
 */
export function useVirtualScroll(options = {}) {
  const {
    items = ref([]),
    itemHeight = 400, // 预估每个引擎项目的高度
    containerHeight = 600,
    overscan = 2
  } = options

  // 状态管理
  const state = reactive({
    scrollTop: 0,
    containerHeight: 0,
    scrollElement: null,
    isScrolling: false,
    scrollDirection: 'down'
  })

  // 存储每个项目的位置信息
  const itemPositions = ref([])
  
  // 可见区域的起始和结束索引
  const visibleRange = computed(() => {
    const items = options.items.value || []
    if (!items.length) return { start: 0, end: 0 }

    let start = 0
    let end = 0
    
    // 查找第一个可见项的索引
    for (let i = 0; i < itemPositions.value.length; i++) {
      const position = itemPositions.value[i]
      if (position.bottom > state.scrollTop) {
        start = i
        break
      }
    }

    // 查找最后一个可见项的索引
    for (let i = start; i < itemPositions.value.length; i++) {
      const position = itemPositions.value[i]
      if (position.top > state.scrollTop + state.containerHeight) {
        end = i
        break
      }
    }

    // 如果没有找到结束索引，说明滚动到了最后
    if (end === 0) {
      end = items.length
    }

    // 添加缓冲区
    start = Math.max(0, start - overscan)
    end = Math.min(items.length, end + overscan)

    return { start, end }
  })

  // 当前需要渲染的项目
  const visibleItems = computed(() => {
    const items = options.items.value || []
    const { start, end } = visibleRange.value
    
    return items.slice(start, end).map((item, index) => ({
      ...item,
      virtualIndex: start + index,
      originalIndex: start + index
    }))
  })

  // 总高度
  const totalHeight = computed(() => {
    const positions = itemPositions.value
    if (!positions.length) return 0
    return positions[positions.length - 1].bottom
  })

  // 偏移量
  const offsetY = computed(() => {
    const { start } = visibleRange.value
    if (start === 0) return 0
    return itemPositions.value[start]?.top || 0
  })

  // 初始化项目位置信息
  const initItemPositions = () => {
    const items = options.items.value || []
    itemPositions.value = items.map((item, index) => ({
      index,
      top: index * itemHeight,
      bottom: (index + 1) * itemHeight,
      height: itemHeight
    }))
  }

  // 更新项目位置信息
  const updateItemPositions = () => {
    const items = options.items.value || []
    if (!items.length) return

    // 重新计算所有项目的位置
    let totalHeight = 0
    itemPositions.value = items.map((item, index) => {
      const position = {
        index,
        top: totalHeight,
        height: itemHeight,
        bottom: totalHeight + itemHeight
      }
      totalHeight += itemHeight
      return position
    })
  }

  // 更新单个项目的真实高度
  const updateItemHeight = (index, height) => {
    const position = itemPositions.value[index]
    if (!position || position.height === height) return

    const heightDiff = height - position.height
    
    // 更新当前项目的高度和底部位置
    position.height = height
    position.bottom = position.top + height

    // 更新后续所有项目的位置
    for (let i = index + 1; i < itemPositions.value.length; i++) {
      itemPositions.value[i].top += heightDiff
      itemPositions.value[i].bottom += heightDiff
    }
  }

  // 滚动事件处理
  const handleScroll = (event) => {
    const scrollTop = event.target.scrollTop
    const previousScrollTop = state.scrollTop
    
    state.scrollTop = scrollTop
    state.scrollDirection = scrollTop > previousScrollTop ? 'down' : 'up'
    
    // 设置滚动状态
    state.isScrolling = true
    
    // 使用 requestAnimationFrame 来优化滚动性能
    requestAnimationFrame(() => {
      state.isScrolling = false
    })
  }

  // 滚动到指定位置
  const scrollToItem = (index) => {
    if (!state.scrollElement) return
    
    const position = itemPositions.value[index]
    if (!position) return
    
    state.scrollElement.scrollTop = position.top
  }

  // 滚动到指定位置（像素）
  const scrollToOffset = (offset) => {
    if (!state.scrollElement) return
    state.scrollElement.scrollTop = offset
  }

  // 初始化
  onMounted(() => {
    initItemPositions()
    state.containerHeight = containerHeight
  })

  // 清理
  onUnmounted(() => {
    if (state.scrollElement) {
      state.scrollElement.removeEventListener('scroll', handleScroll)
    }
  })

  // 注册滚动元素
  const registerScrollElement = (element) => {
    if (state.scrollElement) {
      state.scrollElement.removeEventListener('scroll', handleScroll)
    }
    
    state.scrollElement = element
    if (element) {
      element.addEventListener('scroll', handleScroll, { passive: true })
      state.containerHeight = element.clientHeight
    }
  }

  // 当数据变化时重新计算位置
  const resetPositions = () => {
    nextTick(() => {
      initItemPositions()
    })
  }

  return {
    // 状态
    visibleItems,
    totalHeight,
    offsetY,
    state,
    
    // 方法
    registerScrollElement,
    scrollToItem,
    scrollToOffset,
    updateItemHeight,
    resetPositions,
    
    // 调试信息
    visibleRange,
    itemPositions
  }
} 