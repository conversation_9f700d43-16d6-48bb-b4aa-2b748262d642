Vue.component('y-f4-1-vg-sm_edit', {
	props: ['input_vg_plan_date_start', 'input_vg_plan_date_end', 'input_id_main', 'input_vg_plan_date','is_show_vg','input_str_flow'], //input_id_wo : 发动机  input_plan_date：计划日期 input_id_main
	data: function () {
		return {
			id_main: this.input_id_main,
			str_flow:this.input_str_flow,
			is_show: true,
			str_num_plan: 0,
			vg_over_time:0,
			hsg_over_time:0,
			currentvgovertime:0,
			currenthsgovertime:0,
			totalvgovertime:0,
			totalhsgovertime:0,
			task_list: [
				// 	{id_task:"", str_task_name
				// 	id_team_staffs:[],
				// 	id_team_sec_staffs:[],
				// 	id_shift:null,
				// 	dt_shift_start:null,
				// 	dt_shift_end:null,
				// 	id_delayed_shift:null,
				// 	dt_delayed_start:null,
				// 	dt_delayed_end:null,
				// }
			], // 任务分配 展示使用
			wo_data: [], // 所有 发动机指令
			task: {},
			model_list: [], // 单元体
			vg_model_list: [], // vg 单元体
			hsg_model_list: [], // hsg 单元体
			rulesForm1: {
				// form1 验证
				id_wo: [
					{
						required: true,
						message: "Please select  task",
						trigger: "blur",
					},
					//其他验证设置...
					// { validator: deptRule, trigger: 'blur' },
				],

				id_model: [
					{
						required: true,
						message: "Please select  Model",
						trigger: "blur",
					},
					//其他验证设置...
					// { validator: deptRule, trigger: 'blur' },
				],


			}

		}
	},

	filters: {
		/**日期管道*/
		dateTime: function (date, type) {
			if (date != null && date != '') {
				let yer, month, day, HH, mm, ss
				let time = new Date(date),
					timeDate
				yer = time.getFullYear()
				month = time.getMonth() + 1
				day = time.getDate()
				HH = time.getHours() //获取系统时，
				mm = time.getMinutes() //分
				ss = time.getSeconds() //秒

				month = month < 10 ? '0' + month : month
				day = day < 10 ? '0' + day : day
				HH = HH < 10 ? '0' + HH : HH
				mm = mm < 10 ? '0' + mm : mm
				ss = ss < 10 ? '0' + ss : ss

				switch (type) {
					case 'yyyy':
						timeDate = String(yer)
						break
					case 'yyyy-MM':
						timeDate = yer + '-' + month
						break
					case 'yyyy-MM-dd':
						timeDate = yer + '-' + month + '-' + day
						break
					case 'yyyy/MM/dd':
						timeDate = yer + '/' + month + '/' + day
						break
					case 'yyyy-MM-dd HH:mm:ss':
						timeDate =
							yer +
							'-' +
							month +
							'-' +
							day +
							'' +
							HH +
							':' +
							mm +
							':' +
							ss
						break
					case 'HH:mm:ss':
						timeDate = HH + ':' + mm + ':' + ss
						break
					case 'MM':
						timeDate = String(month)
						break
					default:
						timeDate =
							yer < 1900 ? '' : yer + '-' + month + '-' + day
						break
				}
				return timeDate
			} else {
				return ''
			}
		},

		/**获取数据状态描述*/
		getStatusStr(data) {
			if (data == 1) {
				return '审批通过'
			} else if (data == -1) {
				return '审批失败'
			} else if (data == -999) {
				return '审批撤销'
			} else if (data == 0 || data > 300) {
				return '审批中'
			} else {
				return '待提交'
			}
		},

	},
	methods: {
		/**获取 工作指令 */
		async get_wo_list() {

			let _this = this;
			await axios
				.post(globalApiUrl, {
					au: "ssamc",
					ap: "api2018",
					ak: "",
					ac: "pt_get_grinding_esn",
					start_date: _this.input_vg_plan_date_start,
					end_date: _this.input_vg_plan_date_end,
					str_flow:_this.str_flow,
				})
				.then(function (response) {
					_this.wo_data = response.data?.data || [];
				})
				.catch(function (error) {

					console.log(error);
				});


		},
		/**获取单元体 */
		async get_model_list(id) {

			let _this = this;
			await axios
				.post(globalApiUrl, {
					au: "ssamc",
					ap: "api2018",
					ak: "",
					ac: "pt_get_model_byesn"
					//id_wo: id
				})
				.then(function (response) {
					const data  = response.data?.data || []
					_this.vg_model_list = data.filter(f => f.str_type === 'VG')
					_this.hsg_model_list = data.filter(f => f.str_type === 'HSG')
					if(_this.is_show_vg){
						_this.model_list =_this.vg_model_list
					}else{
						_this.model_list =_this.hsg_model_list
					}
				})
				.catch(function (error) {

					console.log(error);
				});

		},
		changeWo(task_list){
			let _this=this
			let totalid_wo=[];
			task_list.forEach(item=>{
				if(totalid_wo.indexOf(item.id_wo)>-1){
					_this.$message("发动机不能重复选择");
					item.id_wo=""
					return		
				}else{
					totalid_wo=item.id_wo;
				}
			});
		},
			/**获取前一天的超时工时*/
			async get_over_time() {
				
				let _this = this;
				await axios
					.post(globalApiUrl, {
						au: "ssamc",
						ap: "api2018",
						ak: "",
						ac: "pt_get_before_over_time",
						pt_dt: _this.input_vg_plan_date
					})
					.then(function (response) {
						_this.vg_over_time = response.data?.data.vgoverTiem || 0;
						_this.hsg_over_time = response.data?.data.hsgoverTiem || 0;
					})
					.catch(function (error) {
	
						console.log(error);
					});
	
			},


		/**添加任务排班 */
		add_task_plan() {
			let _this = this;
			_this.task_list.push({});
			_this.str_num_plan = _this.task_list.length;
		},
		/** 删除任务 */
		delete_task(task_delete, index) {
			let _this = this;
			_this.task_list.splice(index, 1);
			_this.str_num_plan = _this.task_list.length;

		},
		/**保存磨削 */
		save_plan() {
			let _this = this;
			let pt_grinding_plans_t = [];
			let total_model_ids=[]
			_this.task_list.forEach(element => {
				let id_model_t = [];
				element.id_model.forEach(id_model => {
					id_model_t.push({ id_model: id_model });
					total_model_ids.push(id_model)
				});
				pt_grinding_plans_t.push({ pt_grinding_plan: { id: element.id, id_esn: element.id_wo }, pt_grinding_plan_models: id_model_t })
			});

			let error_msg = [];
			// 组织判断 action
			pt_grinding_plans_t && pt_grinding_plans_t.length > 0 &&
				pt_grinding_plans_t.forEach((sm, index) => {
					if (sm.pt_grinding_plan_models.length == 0) {
						error_msg.push(`No.${index + 1} please select SM`)
					}
					if (!sm.pt_grinding_plan.id_esn) {
						error_msg.push(`No.${index + 1} please select wo`)
					}

				});

			if (error_msg.length > 0 && pt_grinding_plans_t.length > 0) {
				_this.$message({
					dangerouslyUseHTMLString: true,
					message: error_msg.join("<br/>"),
					type: "warning",
				});
			} else {
				// 可以保存
				axios
					.post(globalApiUrl, {
						au: "ssamc",
						ap: "api2018",
						ak: "",
						ac: "pt_save_grinding_plan",
						total_model_ids:total_model_ids,
						pt_grinding_model_dto: { pt_grinding_main: { id: _this.id_main, pt_dt: _this.input_vg_plan_date }, pt_grinding_plans: pt_grinding_plans_t }

					})
					.then(function (response) {

						_this.$message({
							type: "success",
							message: "Save success",
						});
						_this.closeDialog();
					})
					.catch(function (error) {
						console.log(error);
					});
				// }
			}


		},
		/**获取详情  若果有ID 是编辑*/
		getInfo() {
			let _this = this;
			if (_this.id_main) {
				//编辑
				axios
					.post(globalApiUrl, {
						au: "ssamc",
						ap: "api2018",
						ak: "",
						ac: "pt_get_grinding_plan",
						id_main:_this.id_main,
						vg_type:_this.is_show_vg==true?"VG":"HSG"
					})
					.then(function (response) {
						let task_list_t = [];						
						_this.id_main = response.data.data.pt_grinding_model_dto.pt_grinding_main.id;
						response.data.data.pt_grinding_model_dto.pt_grinding_plans.length > 0 &&
							response.data.data.pt_grinding_model_dto.pt_grinding_plans.forEach(x => {
								_this.get_model_list(x.pt_grinding_plan.id_esn).then(() => {
									id_model_t = [];

									x.pt_grinding_plan_models.forEach(sm => {
										id_model_t.push(sm.id_model);
										//over_time减去回显的单元体磨削工时
										_this.model_list.forEach(item=>{
											if(item.id==sm.id_model){
												if(item.str_type=="VG"){
													_this.vg_over_time=_this.vg_over_time-item.int_hours
												}
												if(item.str_type=="HSG"){
													_this.hsg_over_time=_this.hsg_over_time-item.int_hours
												}
											}
										})
									});
									task_list_t.push({
										id_wo: x.pt_grinding_plan.id_esn,
										id: x.pt_grinding_plan.id,
										id_model: id_model_t
									});
									_this.task_list = task_list_t || [];
									_this.str_num_plan = _this.task_list.length;

								});


							})


					})
					.catch(function (error) {
						console.log(error);

					});
			}
			else if (_this.id_main && !_this.is_edit) {
				// 查看
			}


		},	/**关闭弹 */
		closeDialog() {
			let _this = this;
			_this.is_show = false;
			_this.$emit("get_back_close_vg_dialog"); //关闭详情页

		},
		/**添加单元体判断 */
		addmodel(event,task,task_list){
			let _this= this
			_this.currentvgovertime=_this.vg_over_time
			_this.currenthsgovertime=_this.hsg_over_time
			let lasttype = "";
			let vgcount=0
			let hsgcount=0	
			let flatcountvg14=0	
			let flatcounthsg14=0	
			event.forEach(id=>{
				_this.model_list.forEach(item=>{
					if(item.id==id){
						lasttype= item.str_type
						if(item.str_type=="VG"){			
							_this.currentvgovertime+=+item.int_hours
							vgcount++
							if(_this.currentvgovertime>=14&&flatcountvg14==0){
								flatcountvg14=vgcount
							}
						}
						if(item.str_type=="HSG"){						
							_this.currenthsgovertime+=+item.int_hours
							hsgcount++
							if(_this.currenthsgovertime>=14&&flatcounthsg14==0){
								flatcounthsg14=hsgcount
							}
						}
					}
					
				})
			})
			if(vgcount>=flatcountvg14+1 && _this.currentvgovertime>14){
				_this.$message("VG磨削单元体工时已超出");						
				task.id_model.splice(task.id_model.length-1,1)
			}
			if(hsgcount>=flatcounthsg14+1 &&_this.currenthsgovertime>14){
				_this.$message("HSG磨削单元体工时已超出");
				task.id_model.splice(task.id_model.length-1,1)
			}
			_this.checkallPlan(task_list);
		},

		checkallPlan(task_list){
			let _this= this			
			_this.totalvgovertime=_this.vg_over_time
			_this.totalhsgovertime=_this.hsg_over_time
			let vgcount=0
			let hsgcount=0	
			let flatcountvg14=0	
			let flatcounthsg14=0	
			task_list.forEach(item=>{
				item.id_model.forEach(modelid=>{
					_this.model_list.forEach(data=>{
						if(data.id==modelid){
							if(data.str_type=="VG"){
								_this.totalvgovertime+=+data.int_hours
								vgcount++
								if(_this.totalvgovertime>=14&&flatcountvg14==0){
									flatcountvg14=vgcount
								}
							}
							if(data.str_type=="HSG"){
								_this.totalhsgovertime+=+data.int_hours
								hsgcount++
								if(_this.totalhsgovertime>=14&&flatcounthsg14==0){
									flatcounthsg14=hsgcount
								}
							}
						}
					})
				})
				if(vgcount>=flatcountvg14+1 && _this.totalvgovertime>14){
					_this.$message("VG磨削单元体工时已超出");						
					item.id_model.splice(item.id_model.length-1,1)
				}
				if(hsgcount>=flatcounthsg14+1 && _this.totalhsgovertime>14){
					_this.$message("HSG磨削单元体工时已超出");
					item.id_model.splice(item.id_model.length-1,1)
				}
			});
			
			
		}

	},
	created: function () {
		Promise.all([this.get_wo_list(), this.get_model_list(),this.get_over_time()]).then(() => {
			this.getInfo();
		});
	},
	mounted() {
		this.add_task_plan();
	},

	template: `
	<div>
	<el-dialog title="磨削" width="35%" :visible.sync="is_show" class="self_dialog" @close="closeDialog()">
	<el-row style="margin-bottom: 3px;">
		<el-badge :value="str_num_plan" class="item" type="info">
			<el-button size="small">Num</el-button>
		</el-badge>
		<div style="margin-left: 250px;">
			<div v-if="is_show_vg">
				<span>VG:  <span style="color:red;">{{vg_over_time}}</span> H</span>
			</div>
			<div v-else>
				<span style="margin-left:20px">HSG:  <span style="color:red;">{{hsg_over_time}}</span> H</span>
			</div>
			
		</div>
	</el-row>


	<el-row style="margin-bottom: 10px;" v-for="(task,index) in task_list" v-bind:key="index">
		<el-card class="box-card">
			<div slot="header" class="clearfix">
				<span>&nbsp;&nbsp;</span>
				<el-button style="float: right;" type="danger" size="small" @click="delete_task(task,index)">
					Delete</el-button>
			</div>
			<el-form ref="form1" :model="task" :rules="rulesForm1" label-position="left" label-width="auto">
				<el-row>
					<el-col :span="12">
						<el-form-item label="ESN" prop='id_wo'>
							<el-select v-model="task.id_wo" filterable placeholder="select wo"  @change="changeWo(task_list)"
								style="width: 100%;">
								<el-option v-for="(item,index) in wo_data" :key="item.id" :label="item.str_esn"
									:value="item.id_wo"></el-option>
							</el-select>

						</el-form-item>
					</el-col>
					
					<el-col :span="12">
						<el-form-item v-if="is_show_vg" label="VG Type" prop='id_model'>
							<el-select v-model="task.id_model"  multiple filterable placeholder="select model"  @change="addmodel($event,task,task_list)" 
								style="width: 100%;">
								<el-option v-for="(item,index)  in vg_model_list" :key="item.id" :label="item.str_name"
									:value="item.id"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item v-else label="HSG Type" prop='id_model'>
							<el-select v-model="task.id_model"  multiple filterable placeholder="select model"  @change="addmodel($event,task,task_list)" 
								style="width: 100%;">
								<el-option v-for="(item,index)  in hsg_model_list" :key="item.id" :label="item.str_name"
									:value="item.id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				
		
			</el-form>
		</el-card>
	</el-row>
	<el-row>
		<el-button style="margin-left:20px; float:right" size="small" type="success" @click="add_task_plan()">
			+New 
		</el-button>
	</el-row>
	<div slot="footer">
		<el-button class="topButton_right" style="margin-left:20px;" size="small" type='danger'
			@click="is_show=false">Cancle
		</el-button>
		<el-button class="topButton_right" size="small" type='primary' @click="save_plan()">Save
		</el-button>
	</div>
</el-dialog>
</div>
    `,
})
