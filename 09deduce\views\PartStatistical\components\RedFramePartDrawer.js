import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
import { getRedFramePartData } from '../../EngineSimulationPlan/api/index.js'
const { useVModel } = VueUse
const { ref, onMounted } = Vue
const RedFramePartDrawer = {
  components: {
    HtVxeTable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    idWo: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)
    const tableRef = ref(null)
    const tableData = ref([])
    const tableColumns = ref([
      {
        title: '原因备注',
        field: 'str_reason',
        minWidth: 200,
      },
      {
        title: 'M值',
        field: 'int_m',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'Kitting完成/站点',
        field: 'str_nodename',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        sortable: true, // 开启排序
      },
      {
        title: '分类',
        field: 'strcategory',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        visible: props.type === '0',
      },
      {
        title: '保障交付时间',
        field: 'dt_delivery',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        visible: props.type != '120',
      },
      {
        title: '目标WO',
        field: 'str_code',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标ESN',
        field: 'str_esn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台',
        field: 'str_wo_code_ori',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台 ESN',
        field: 'str_wo_esn_ori',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PN',
        field: 'str_pn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件标签',
        field: 'str_label',
        minWidth: 120,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件名称',
        field: 'str_part_name',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'SM',
        field: 'str_sm',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        sortable: true, // 添加排序功能
      },
      {
        title: '客户',
        field: 'str_client',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '转包供应商/采购供应商',
        field: 'str_subcontract',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件来源',
        field: 'str_class',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件类别',
        field: 'str_item_type',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'Marshalling集件关闭',
        field: 'is_close',
        minWidth: 150,
        filters: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        formatter: ({ cellValue }) => {
          return cellValue === 1 ? '是' : '否'
        },
      },
      {
        title: 'Marshalling完成日期',
        field: 'dt_close',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        sortable: true,
      },
      {
        title: 'PKP',
        field: 'id_pkp',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '机型',
        field: 'str_engine_type',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '数量',
        field: 'int_qty',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'EKD',
        field: 'dt_ekd',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
        sortable: true,
      },
      {
        title: 'AOG',
        minWidth: 100,
        field: 'is_aog',
        filters: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        formatter: ({ cellValue }) => {
          return cellValue === 1 ? '是' : '否'
        },
      },
    ])
    const total = ref(0)

    const getTableData = async () => {
      const searchParams = {
        idWo: props.idWo,
        type: props.type,
      }
      const { data } = await getRedFramePartData(searchParams)
      if (data.code === 'success') {
        tableData.value = data.data
        total.value = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    onMounted(() => {
      getTableData()
    })

    return {
      visible,
      tableRef,
      tableData,
      tableColumns,
      total,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="80%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-white">红框后零件</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <div class="flex items-center justify-between">
          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable
          ref="tableRef"
          :tableData="tableData"
          :tableColumns="tableColumns"
        ></HtVxeTable>
      </div>
    </el-drawer>
  `,
}

export default RedFramePartDrawer
