import { post } from "../../../config/axios/httpReuest.js";


const {
    defineAsyncComponent,
    reactive,
    toRefs,
    onMounted,
    ref,
    nextTick,
    watch,
} = Vue;
const Global = {
    components: {

    },
    setup() {

        const active = ref(4)

        const next = () => {
            if (active.value++ > 4) active.value = 0
        }
        onMounted(async () => {

        });

        return {
            active,
            next
        };
    },
    /*HTML*/
    template: `
    <div class="block">
    <span class="demonstration">Month</span>
    <el-date-picker
      v-model="value2"
      type="month"
      placeholder="Pick a month"
    />
    <el-button type="primary">一键计算</el-button>
  </div>
    <el-card >
  <template #header>
    <div class="card-header">
      <span>影响因素</span>
      <el-popconfirm
    width="220"
    confirm-button-text="OK"
    cancel-button-text="No, Thanks"
    :icon="InfoFilled"
    icon-color="#626AEF"
    title="Are you sure to delete this?"
  >
    <template #reference>
      <el-button>Delete</el-button>
    </template>
  </el-popconfirm>
    </div>
  </template>
  <el-row>
    <el-col :span="3" >
      <el-result
        icon="success"
        title="特殊发动机"
        sub-title="标记是否有参与计算的特殊 发动机"
      >
        <template #extra>
          <el-button type="primary">添加</el-button>
        </template>
      </el-result>
    </el-col>
    <el-col :span="3" >
      <el-result
        icon="warning"
        title="异常/延期申请"
        sub-title="Please follow the instructions"
      >
        <template #extra>
          <el-button type="primary">添加</el-button>
        </template>
      </el-result>
    </el-col>
    <el-col :span="3" >
      <el-result
        icon="error"
        title="发动机Pending处理"
        sub-title="Please follow the instructions"
      >
        <template #extra>
          <el-button type="primary">Back</el-button>
        </template>
      </el-result>
    </el-col>
    <el-col :span="3" >
      <el-result icon="info" title="调整TAT">
        <template #sub-title>
          <p>Using slot as subtitle</p>
        </template>
        <template #extra>
          <el-button type="primary">Back</el-button>
        </template>
      </el-result>
      </el-col>
      <el-col :span="3" >
      <el-result
        icon="error"
        title="可原谅TAT"
        sub-title="Please follow the instructions"
      >
        <template #extra>
          <el-button type="primary">Back</el-button>
        </template>
      </el-result>
    
    </el-col>
  </el-row>
  <template #footer><el-button style="margin-top: 12px" @click="next">Next step</el-button></template>
  </el-card>
  <el-card >
  <template #header>
    <div class="card-header">
      <span>相关TAT统计</span>
    </div>
  </template>
  <el-row>
    <el-col :span="3" >
      <el-result
        icon="success"
        title="交付量绩效"
        sub-title="标记是否有参与计算的特殊 发动机"
      >
        <template #extra>
          <el-button type="primary">添加</el-button>
        </template>
      </el-result>
    </el-col>
    <el-col :span="3" >
      <el-result
        icon="warning"
        title="NQC绩效得分"
        sub-title="Please follow the instructions"
      >
        <template #extra>
          <el-button type="primary">添加</el-button>
        </template>
      </el-result>
    </el-col>
    <el-col :span="3" >
      <el-result
        icon="error"
        title="发动机得分统计"
        sub-title="Please follow the instructions"
      >
        <template #extra>
          <el-button type="primary">Back</el-button>
        </template>
      </el-result>
    </el-col>
   
  </el-row>
  <template #footer><el-button style="margin-top: 12px" @click="next">Next step</el-button></template>
  </el-card>
    <el-card >
    <template #header>
      <div class="card-header">
        <span>绩效TAT</span>
      </div>
    </template>
    <el-steps finish-status="success" style="max-width: 600px" :active="active" align-center>
    <el-step title="WIP TAT" description="依据WIP SUMMARY 计算各Flow的TAT" />
    <el-step title="WIP 绩效 TAT" description="" />
    <el-step title="TAT 绩效得分" description="Some description" />
    <el-step title="分部得分计算" description="Some description" />
  </el-steps>
    <template #footer><el-button style="margin-top: 12px" @click="next">Next step</el-button></template>
  </el-card>
  </el-card>
  <el-card >
  <template #header>
    <div class="card-header">
      <span>分部、员工得分</span>
    </div>
  </template>
  <el-row>
    <el-col :span="3" >
      <el-result
        icon="success"
        title="分部二级得分"
        sub-title="标记是否有参与计算的特殊 发动机"
      >
        <template #extra>
          <el-button type="primary">添加</el-button>
        </template>
      </el-result>
    </el-col>
    <el-col :span="3" >
      <el-result
        icon="warning"
        title="NQC扣分"
        sub-title="Please follow the instructions"
      >
        <template #extra>
          <el-button type="primary">添加</el-button>
        </template>
      </el-result>
    </el-col>
    <el-col :span="3" >
      <el-result
        icon="error"
        title="员工二级得分"
        sub-title="Please follow the instructions"
      >
        <template #extra>
          <el-button type="primary">Back</el-button>
        </template>
      </el-result>
    </el-col>
    <el-col :span="3" >
    <el-result
      icon="error"
      title="MES数量"
      sub-title="Please follow the instructions"
    >
      <template #extra>
        <el-button type="primary">Back</el-button>
      </template>
    </el-result>
  </el-col>
  <el-col :span="3" >
  <el-result
    icon="error"
    title="空缺人数"
    sub-title="Please follow the instructions"
  >
    <template #extra>
      <el-button type="primary">Back</el-button>
    </template>
  </el-result>
</el-col>
  </el-row>
  <template #footer><el-button style="margin-top: 12px" @click="next">Next step</el-button></template>
  </el-card>

  <el-card >
  <template #header>
    <div class="card-header">
      <span>员工绩效</span>
    </div>
  </template>
  <el-steps finish-status="success" style="max-width: 600px" :active="active" align-center>

  <el-step title="导出绩效EXCEL" description="Some description" />
  
</el-steps>
  <template #footer><el-button style="margin-top: 12px" @click="next">Next step</el-button></template>
</el-card>
  





  `,
};
export default Global;
