import { queryTableData } from '../../api/partStaistical.js'
import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'
import EngineDrawer from './drawer.js'
import TotalDrawer from './totalDrawer.js'
import WhiteToYellowDrawer from '../EngineSimulationPlan/components/WhiteToYellowDrawer.js'
import RedFramePartDrawer from './components/RedFramePartDrawer.js'
import { post } from '../../../config/axios/httpReuest.js'
import { useSearchState } from './composables/useSearchState.js'
import IntValueDisplay from './components/IntValueDisplay.js'
import { TYPE_CONFIG } from './config/typeConfig.js'
import { tableConfig } from './config/tableColumn.js'
import { usePartStatistics } from './composables/usePartStatistics.js'
const { onMounted, ref, reactive, toRefs, nextTick, onBeforeUnmount } = Vue
const { Setting } = ElementPlusIconsVue
/**
 * 推演计划零件处置统计表组件
 */
const PartStatistical = {
  // 注册需要使用的子组件
  components: {
    HtVxeTable,
    EngineDrawer,
    TotalDrawer,
    WhiteToYellowDrawer,
    Setting,
    IntValueDisplay,
    RedFramePartDrawer,
    // 'el-date-picker': ElDatePicker,
  },
  props: {
    page_type: '',
  },
  setup(props, { emit }) {
    const jump_config_api = ref([])
    const jump_barcode_columns_api = ref([])
    // 表格引用,用于调用表格实例方法
    const tableRef = ref(null)
    if (props.page_type === '2') {
      let TABLE_COLUMNST = tableConfig.TABLE_COLUMNS.filter(
        (column) =>
          column.field !== 'int101' &&
          column.field !== 'int115' &&
          column.field !== 'int106' &&
          column.field !== 'int110' &&
          column.field !== 'int107' &&
          column.field !== 'int120' &&
          column.field !== 'int118' &&
          column.field !== 'int112',
      )
      tableConfig.TABLE_COLUMNS = TABLE_COLUMNST // 隐藏不显示的列
    }
    // 表格状态管理
    const tableState = reactive({
      data: [], // 表格数据
      columns: tableConfig.TABLE_COLUMNS.map((column) => ({
        ...column,
        // 为html类列添加统一的格式化函数,实现悬浮效果
        ...(column.type === 'html' && {
          formatter: ({ cellValue, row }) => {
            if (cellValue) {
              return `<span class=" hover:cursor-pointer hover:text-blue-500 hover:underline">${cellValue}</span>`
            }
          },
        }),
        // 设置默认显示状态
        visible: column.visible === false ? false : true,
      })),
      footerData: [],
    })

    // 普通抽屉状态管理
    const drawerState = reactive({
      visible: false, // 控制抽屉显示/隐藏
      type: '', // 当前选中的类型
      title: '', // 抽屉标题
      idWo: '', // 工单ID
    })

    // 总量抽屉状态管理
    const totalDrawerState = reactive({
      visible: false,
      type: '',
      title: '',
      idWo: '',
    })

    // 白转黄抽屉状态管理
    const whiteToYellowDrawerState = reactive({
      visible: false,
      type: '',
      title: '',
      idWo: '',
    })

    const { onInt109Click, onFooterInt109Click } = usePartStatistics(drawerState, tableState)
    /**发动机 */
    const baseInfoList = ref([])
    /**获取发动机基础信息 */
    const getBaseWoInfo = async (paramList = []) => {
      const params = {
        ac: 'de_get_wo_list',
        filter_fields: paramList,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        baseInfoList.value = data.data.map((item, index) => {
          return {
            ...item,
            tempSort: index + 1,
          }
        })
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    /**
     * 获取表格数据
     * 通过API获取数据并实时更新到表格状态中
     */
    const getTableData = async () => {
      // 首先检查 baseInfoList 是否为空
      if (baseInfoList.value.length === 0) {
        // 如果为空，清空表格数据并更新表尾
        tableState.data = []
        footerMethod()
        // 可以选择性地显示提示信息
        ElementPlus.ElMessage.info('没有需要加载的工单数据')
        return // 直接返回，不执行后续加载
      }

      // 重要：清空当前表格数据，确保不会显示旧数据
      tableState.data = []

      // 确认 baseInfoList 不为空，开始逐个加载
      for (let i = 0; i < baseInfoList.value.length; i++) {
        const item = baseInfoList.value[i]

        try {
          // 获取单个工单数据
          const { data } = await queryTableData({ id_wo: item.id_wo })

          if (data.code === 'success' && Array.isArray(data.data)) {
            // 获取到新数据，立即添加到表格中
            const newItems = data.data
            if (newItems.length > 0) {
              // 将新数据直接添加到表格中，一条一条地显示
              tableState.data.push(...newItems)
              // 每次添加数据后更新表尾
              footerMethod()
            }
          } else {
            // 接口返回错误
            ElementPlus.ElMessage.warning(`工单 ${item.str_wo || item.id_wo} 数据获取失败: ${data.text || '未知错误'}`)
          }
        } catch (error) {
          // 请求异常
          console.error(`获取工单 ${item.str_wo || item.id_wo} 数据出错:`, error)
          ElementPlus.ElMessage.warning(`工单 ${item.str_wo || item.id_wo} 请求异常`)
        }
      }

      // 所有数据加载完成后，再次更新表尾（确保数据完整）
      footerMethod()

      // 获取完成后显示结果
      if (tableState.data.length > 0) {
        ElementPlus.ElMessage.success(`加载完成，共 ${tableState.data.length} 条数据`)
      } else {
        ElementPlus.ElMessage.info('未获取到有效数据')
      }
    }

    const footerMethod = () => {
      tableState.footerData = []
      // 检查是否有数据
      if (!tableState.data || !tableState.data.length) {
        return [[], []] // 无数据时返回2个空数组
      }

      // 创建 footerData 对象
      let footerObject = { seq: 'Total' }
      let footerRow = ['Total'] // 第一列为 Total

      // 创建第二行表尾数据 - 绿色值
      let footerObject2 = { seq: 'green' }
      let footerRow2 = ['保交付'] // 第一列为 Green

      // 遍历所有列计算合计和平均值
      tableState.columns.forEach((column) => {
        // 跳过第一列(seq)
        if (column.field === 'seq') return

        // 如果是 html 类型且不是 int_total 列，计算合计和平均值
        if (column.type === 'html' && column.field !== 'int_total') {
          // 切割column.field
          const number = column.field?.split('int')[1]
          // 如果column.field是113, 113要算左边的合计和右边的合计
          let sumFirst = 0
          let sumLeft = 0
          let sumRight = 0
          let sum = 0
          let sumGreenFirst = 0
          let sumGreenLeft = 0
          let sumGreenRight = 0
          let sumGreen = 0
          if (column.field === 'int113') {
            sumFirst = tableState.data.reduce((acc, curr) => acc + (curr['int125'] || 0), 0)
            sumLeft = tableState.data.reduce((acc, curr) => acc + (curr['int113'] || 0), 0)
            sumRight = tableState.data.reduce((acc, curr) => acc + (curr['int122'] || 0), 0)

            sumGreenFirst = tableState.data.reduce((acc, curr) => acc + (curr['int_color125'] || 0), 0)
            sumGreenLeft = tableState.data.reduce((acc, curr) => acc + (curr['int_color113'] || 0), 0)
            sumGreenRight = tableState.data.reduce((acc, curr) => acc + (curr['int_color122'] || 0), 0)
          } else if (column.field === 'int115') {
            sumLeft = tableState.data.reduce((acc, curr) => acc + (curr['int124'] || 0), 0)
            sumRight = tableState.data.reduce((acc, curr) => acc + (curr['int115'] || 0), 0)

            sumGreenLeft = tableState.data.reduce((acc, curr) => acc + (curr['int_color124'] || 0), 0)
            sumGreenRight = tableState.data.reduce((acc, curr) => acc + (curr['int_color115'] || 0), 0)
          } else if (column.field === 'int109') {
            sumLeft = tableState.data.reduce((acc, curr) => acc + (curr['int109'] || 0), 0)
            sumRight = tableState.data.reduce((acc, curr) => acc + (curr['int126'] || 0), 0)

            sumGreenLeft = tableState.data.reduce((acc, curr) => acc + (curr['int_color109'] || 0), 0)
            sumGreenRight = tableState.data.reduce((acc, curr) => acc + (curr['int_color126'] || 0), 0)
          } else if (column.field === 'int200') {
            sumFirst = tableState.data.reduce((acc, curr) => acc + (curr['int200'] || 0), 0)
            sumLeft = tableState.data.reduce((acc, curr) => acc + (curr['int300'] || 0), 0)
            sumRight = tableState.data.reduce((acc, curr) => acc + (curr['int400'] || 0), 0)

            sumGreenFirst = tableState.data.reduce((acc, curr) => acc + (curr['int_color200'] || 0), 0)
            sumGreenLeft = tableState.data.reduce((acc, curr) => acc + (curr['int_color300'] || 0), 0)
            sumGreenRight = tableState.data.reduce((acc, curr) => acc + (curr['int_color400'] || 0), 0)
          } else {
            sum = tableState.data.reduce((acc, curr) => acc + (curr[column.field] || 0), 0)
            sumGreen = tableState.data.reduce((acc, curr) => acc + (curr['int_color' + number] || 0), 0)
          }

          // 如果列是可见的，添加到对象和数组中
          if (column.visible) {
            if (column.field === 'int113') {
              footerObject[column.field] = `${sumFirst} | ${sumLeft} | ${sumRight}`
              footerRow.push(`${sumFirst} | ${sumLeft} | ${sumRight}`)
              footerObject2[column.field] = `${sumGreenFirst} | ${sumGreenLeft} | ${sumGreenRight}`
              footerRow2.push(`${sumGreenFirst} | ${sumGreenLeft} | ${sumGreenRight}`)
            } else if (column.field === 'int115') {
              footerObject[column.field] = `${sumLeft} | ${sumRight}`
              footerRow.push(`${sumLeft} | ${sumRight}`)

              footerObject2[column.field] = `${sumLeft} | ${sumRight}`
              footerRow2.push(`${sumGreenLeft} | ${sumGreenRight}`)
            } else if (column.field === 'int109') {
              footerObject[column.field] = `${sumLeft} | ${sumRight}`
              footerRow.push(`${sumLeft} | ${sumRight}`)

              footerObject2[column.field] = `${sumLeft} | ${sumRight}`
              footerRow2.push(`${sumGreenLeft} | ${sumGreenRight}`)
            } else if (column.field === 'int200') {
              footerObject[column.field] = `${sumFirst} | ${sumLeft} | ${sumRight}`
              footerRow.push(`${sumFirst} | ${sumLeft} | ${sumRight}`)
              footerObject2[column.field] = `${sumGreenFirst} | ${sumGreenLeft} | ${sumGreenRight}`
              footerRow2.push(`${sumGreenFirst} | ${sumGreenLeft} | ${sumGreenRight}`)
            } else {
              footerObject[column.field] = sum
              footerObject2[column.field] = sumGreen
              footerRow.push(sum)
              footerRow2.push(sumGreen)
            }
          }
        } else {
          // 非数值列，如果可见则添加空字符串
          if (column.visible) {
            footerObject[column.field] = ''
            footerRow.push('')
            footerObject2[column.field] = ''
            footerRow2.push('')
          }
        }
      })

      tableState.footerData = [footerObject, footerObject2]
      return [footerRow, footerRow2]
    }
    /**统计行点击事件 */
    const onFooterCellClick = ({ items, column }) => {
      const { field } = column
      // 获取类型配
      const config = TYPE_CONFIG[field]
      if (!config) return
      let dataTableIdWo = tableState.data
        .filter((item) => item[field] > 0)
        .map((item) => item.id_wo)
        .join(',') // 多个ID_wo用逗号分隔

      if (field === 'int113') {
        return
      }
      if (field === 'int115') {
        return
      }

      if (field === 'int109') {
        return
      }

      // 如果是白转黄相关字段，打开白转黄抽屉
      if (field === 'int200') {
        return
      }

      // 打开普通抽屉
      Object.assign(drawerState, {
        visible: true,
        type: config.type,
        title: config.title,
        idWo: dataTableIdWo,
        int_type: field,
        is_delivery: items[0] === '保交付' ? 1 : 0,
      })
    }

    const onFooterInt113Click = (items, column, field) => {
      const config = TYPE_CONFIG[field]
      if (!config) return
      let dataTableIdWo = tableState.data
        .filter((item) => item[field] > 0)
        .map((item) => item.id_wo)
        .join(',') // 多个ID_wo用逗号分隔
      Object.assign(drawerState, {
        visible: true,
        type: config.type,
        title: config.title,
        idWo: dataTableIdWo,
        int_type: field,
      })
    }
    const onFooterInt115Click = (column, field) => {
      const config = TYPE_CONFIG[field]
      if (!config) return
      let dataTableIdWo = tableState.data
        .filter((item) => item[field] > 0)
        .map((item) => item.id_wo)
        .join(',') // 多个ID_wo用逗号分隔
      Object.assign(drawerState, {
        visible: true,
        type: config.type,
        title: config.title,
        idWo: dataTableIdWo,
        int_type: field,
      })
    }

    const onFooterInt200Click = (column, field) => {
      const config = TYPE_CONFIG[field]
      if (!config) return
      let dataTableIdWo = tableState.data
        .filter((item) => item[field] > 0)
        .map((item) => item.id_wo)
        .join(',') // 多个ID_wo用逗号分隔

      // 如果是白转黄相关字段，打开白转黄抽屉
      if (field === 'int200' || field === 'int300' || field === 'int400') {
        Object.assign(whiteToYellowDrawerState, {
          visible: true,
          type: config.type,
          title: config.title,
          idWo: dataTableIdWo,
        })
      } else {
        Object.assign(drawerState, {
          visible: true,
          type: config.type,
          title: config.title,
          idWo: dataTableIdWo,
          int_type: field,
        })
      }
    }

    /**导出前处理 */
    const onBeforeExport = ({ options }) => {
      // 1. 获取数据
      const data = tableRef.value.xTable.getTableData().visibleData
      // 2.处理数据
      const newData = data
        .map((item) => {
          // 2.1 处理113, 115的值
          // 2.1.1 处理113的值
          let int113 = item.int113
          if (item.int113 && item.int122 && item.int125) {
            int113 = `${item.int125} | ${item.int113} | ${item.int122}`
          } else if (item.int113) {
            int113 = `| ${item.int113} | `
          } else if (item.int122) {
            int113 = `| | ${item.int122}`
          } else if (item.int125) {
            int113 = ` ${item.int125} | |`
          }
          // 2.1.2 处理115的值
          let int115 = item.int115
          if (item.int115 && item.int124) {
            int115 = `${item.int124} | ${item.int115}`
          } else if (item.int115) {
            int115 = `| ${item.int115}`
          } else if (item.int124) {
            int115 = `${item.int124} | `
          }

          let int109 = item.int109
          if (item.int109 && item.int126) {
            int115 = `${item.int109} | ${item.int126}`
          } else if (item.int109) {
            int115 = `| ${item.int109}`
          } else if (item.int126) {
            int115 = `${item.int126} | `
          }

          // 2.1.3 处理200的值（白转黄）
          let int200 = item.int200
          if (item.int200 && item.int300 && item.int400) {
            int200 = `${item.int200} | ${item.int300} | ${item.int400}`
          } else if (item.int200 && item.int300) {
            int200 = `${item.int200} | ${item.int300} | `
          } else if (item.int200 && item.int400) {
            int200 = `${item.int200} | | ${item.int400}`
          } else if (item.int300 && item.int400) {
            int200 = ` | ${item.int300} | ${item.int400}`
          } else if (item.int200) {
            int200 = `${item.int200} | | `
          } else if (item.int300) {
            int200 = ` | ${item.int300} | `
          } else if (item.int400) {
            int200 = ` | | ${item.int400}`
          }

          return {
            ...item,
            int113,
            int115,
            int109,
            int200,
          }
        })
        .map((item) => {
          // 2.2  处理空值
          return Object.fromEntries(
            Object.entries(item).map(([key, value]) => [key, value === null || value === 0 ? ' ' : value]),
          )
        })
      // 3. 将处理后的数据赋值给options.data
      options.data = newData
    }
    /**
     * 导出表格数据
     * 调用表格实例的导出方法
     */
    const exportTableData = () => {
      tableRef.value?.exportData('零件处置统计表', onBeforeExport)
    }

    /**
     * 刷新视图
     *
     */
    const refreshTableData = async () => {
      const loading = ElementPlus.ElLoading.service({
        text: '处理中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const params = {
        ac: 'de_refresh_view_de_pending_pn_vm',
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        getTableData()
        ElementPlus.ElMessage.success('Refresh Success')
        loading.close()
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    /**
     * 打开抽屉处理函数
     * @param {Object} row - 当前行数据
     * @param {Object} column - 当前列配置
     */
    const openDrawer = ({ row, column, $columnIndex }) => {
      const { field } = column
      // 如果是不可点击列,直接返回
      if (tableConfig.DISABLED_COLUMNS.includes(field)) return

      // 获取类型配置
      const config = TYPE_CONFIG[field]
      if (!config) return

      if (field === 'int113') {
        return
      }
      if (field === 'int115') {
        return
      }
      if (field === 'int200') {
        return
      }
      // 如果是int_total，打开总量抽屉
      else if (field === 'int_total' || field === 'green') {
        // 打开总量抽屉
        Object.assign(totalDrawerState, {
          visible: true,
          type: config.type,
          title: config.title,
          idWo: row.id_wo,
          is_delivery: field === 'green' ? 1 : 0,
        })
      } else {
        // 打开普通抽屉
        Object.assign(drawerState, {
          visible: true,
          type: config.type,
          title: config.title,
          idWo: row.id_wo,
          int_type: field,
        })
      }
    }

    const onInt113Click = (row, field) => {
      const config = TYPE_CONFIG[field]
      if (!config) return
      Object.assign(drawerState, {
        visible: true,
        type: config.type,
        title: config.title,
        idWo: row.id_wo,
        int_type: field,
      })
    }

    const onInt115Click = (row, field) => {
      const config = TYPE_CONFIG[field]
      if (!config) return
      Object.assign(drawerState, {
        visible: true,
        type: config.type,
        title: config.title,
        idWo: row.id_wo,
        int_type: field,
      })
    }

    const onInt200Click = (row, field) => {
      const config = TYPE_CONFIG[field]
      if (!config) return

      // 如果是白转黄相关字段，打开白转黄抽屉
      if (field === 'int200' || field === 'int300' || field === 'int400') {
        Object.assign(whiteToYellowDrawerState, {
          visible: true,
          type: config.type,
          title: config.title,
          idWo: row.id_wo,
        })
      } else {
        Object.assign(drawerState, {
          visible: true,
          type: config.type,
          title: config.title,
          idWo: row.id_wo,
          int_type: field,
        })
      }
    }

    /**获取发动机基础信息 */
    const getJumpBarcodeParams = async () => {
      const params = {
        ac: 'de_get_jump_params',
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        jump_config_api.value = data.data
        jump_barcode_columns_api.value = data.data.map((item) => {
          return item.key
        })
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    const onSortChangeEvent = (sort) => {
      // 排序事件处理函数
      // 更新表格数据
      // getTableData(sort)
    }

    // 组件挂载时获取表格数据
    onMounted(async () => {
      // 尝试从 localStorage 恢复用户的列设置
      const savedSettings = localStorage.getItem('tableColumnSettings')
      if (savedSettings) {
        try {
          const savedColumns = JSON.parse(savedSettings)
          tableState.columns = tableState.columns.map((col) => ({
            ...col,
            visible: savedColumns.includes(col.field),
          }))
        } catch (e) {
          console.error('Failed to parse saved column settings:', e)
          // 如果解析失败，使用默认设置
          tableState.columns = tableState.columns.map((col) => ({
            ...col,
            visible: col.visible === false ? false : true,
          }))
        }
      }

      await getBaseWoInfo()
      getTableData()
      getJumpBarcodeParams()
    })

    // 在 setup 函数内添加以下状态管理代码
    const columnSettingVisible = ref(false) // 控制列设置对话框显示
    const checkedColumns = ref([]) // 选中的列

    const openColumnSetting = () => {
      // 初始化时，将所有当前显示的列都勾选上
      checkedColumns.value = tableState.columns.filter((col) => col.visible).map((col) => col.field)
      columnSettingVisible.value = true
    }
    /**
     * 确认列设置
     */
    const confirmColumnSetting = () => {
      // 更新列显示状态
      tableState.columns = tableState.columns.map((col) => ({
        ...col,
        visible: checkedColumns.value.includes(col.field),
      }))

      columnSettingVisible.value = false

      // 刷新表格
      nextTick(() => {
        if (tableRef.value) {
          tableRef.value.xTable.refreshColumn()
        }
      })

      // 保存用户的列设置到 localStorage
      try {
        localStorage.setItem('tableColumnSettings', JSON.stringify(checkedColumns.value))
      } catch (e) {
        console.error('Failed to save column settings:', e)
      }
    }

    /**
     * 单元格样式
     * @param {Object} row - 当前行数据
     * @param {Object} column - 当前列配置
     * @param {Number} rowIndex - 当前行索引
     * @param {Number} columnIndex - 当前列索引
     */
    const cellClassName = ({ row, column, rowIndex, columnIndex }) => {
      const { field } = column
      if (field === 'dt_project_start') {
        if (row.dt_project_start < row.dt_adjust_start) {
          return 'bg-red-500'
        }
      }
      const number = field?.split('int')[1]
      if (number) {
        const color =
          row[`int_color${number}`] === row[`int${number}`] ? 'text-green-500 font-bold' : 'text-black font-medium'
        return color
      }
    }

    const getCustomColor = (row, field) => {
      const number = field?.split('int')[1]
      if (number) {
        const color =
          row[`int_color${number}`] === row[`int${number}`] ? 'text-green-500 font-bold' : 'text-black font-medium'
        return color
      }
    }

    const { searchState, engineList } = useSearchState()
    // 查询
    const handleSearch = async () => {
      const paramList = [
        {
          str_key: 'id_wos',
          str_value: searchState.engine,
        },
        {
          str_key: 'int_take_type',
          str_value: searchState.receiver,
        },
      ]

      tableState.data = []

      await getBaseWoInfo(paramList)
      getTableData()
    }

    /**
     * 获取背景颜色
     * @param {Object} row - 当前行数据
     * @returns {String} 背景颜色
     */
    const getBgColor = (row) => {
      // 1. 当is_gm为1的时候，返回黄色
      // 2. 否则判断is_gp为1的时候，返回蓝色
      // 3. 先判断is_gp,在判断is_gm
      if (row.is_gp) {
        return 'bg-blue-500'
      } else if (row.is_gm) {
        return 'bg-yellow-500'
      }else if (row.is_rul) {
        return 'bg-green-500'
      }
    }

    const redFramePartDrawerState = reactive({
      visible: false,
      type: '',
      idWo: '',
    })

    const onInt0110Click = (row, field) => {
      const config = TYPE_CONFIG[field]
      if (!config) return
      Object.assign(redFramePartDrawerState, {
        visible: true,
        type: config.type,
        idWo: row.id_wo,
      })
    }
    const onFooterInt0110Click = (column, field) => {
      const config = TYPE_CONFIG[field]
      let dataTableIdWo = tableState.data
        .filter((item) => item[field] > 0)
        .map((item) => item.id_wo)
        .join(',') // 多个ID_wo用逗号分隔
      if (!config) return
      Object.assign(redFramePartDrawerState, {
        visible: true,
        type: config.type,
        idWo: dataTableIdWo,
      })
    }
    const onInt0111Click = (row, field) => {
      const config = TYPE_CONFIG[field]
      if (!config) return
      Object.assign(redFramePartDrawerState, {
        visible: true,
        type: config.type,
        idWo: row.id_wo,
      })
    }
    const onFooterInt0111Click = (column, field) => {
      const config = TYPE_CONFIG[field]
      if (!config) return
      let dataTableIdWo = tableState.data
        .filter((item) => item[field] > 0)
        .map((item) => item.id_wo)
        .join(',') // 多个ID_wo用逗号分隔
      if (!config) return
      Object.assign(redFramePartDrawerState, {
        visible: true,
        type: config.type,
        idWo: dataTableIdWo,
      })
    }
    // 返回模板需要使用的属性和方法
    return {
      ...toRefs(tableState),
      tableRef,
      exportTableData,
      openDrawer,
      drawerState,
      totalDrawerState,
      whiteToYellowDrawerState,
      redFramePartDrawerState,
      onSortChangeEvent, // 暴露排序事件处理函数
      footerMethod,
      onFooterCellClick,
      getBaseWoInfo,
      refreshTableData,
      getJumpBarcodeParams,
      jump_config_api,
      jump_barcode_columns_api,
      // 增
      openColumnSetting,
      columnSettingVisible,
      checkedColumns,
      confirmColumnSetting,
      cellClassName,
      getCustomColor,
      onFooterInt113Click,
      onInt113Click,
      onFooterInt115Click,
      onInt115Click,
      searchState,
      handleSearch,
      engineList,
      getBgColor,
      onInt109Click,
      onFooterInt109Click,
      onInt200Click,
      onFooterInt200Click,
      onInt0110Click,
      onFooterInt0110Click,
      onInt0111Click,
      onFooterInt0111Click,
    }
  },

  // 组件模板
  template: /*html*/ `
    <!-- 按钮组 -->
    <div class="ml-4 mr-4 mt-4 flex items-center justify-between gap-2">
      <div class="flex items-center gap-2">
        <label class="w-auto flex-none" for="engine">发动机:</label>
        <el-select v-model="searchState.engine" id="engine" multiple filterable clearable class="!w-56">
          <el-option v-for="item in engineList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <label class="w-auto flex-none">接手</label>
        <el-select v-model="searchState.receiver" clearable class="!w-56">
          <el-option label="全部" value="-1" />
          <el-option label="RUL接手" value="0" />
          <el-option label="GM接手" value="1" />
          <el-option label="GP接手" value="2" />
        </el-select>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </div>
      <div class="flex items-center gap-2">
        <el-button type="primary" size="mini" @click="exportTableData">导出</el-button>
        <el-button type="primary" size="mini" @click="refreshTableData">刷新</el-button>
        <el-popover
          v-model:visible="columnSettingVisible"
          placement="bottom-end"
          :width="400"
          trigger="click"
          @show="openColumnSetting"
        >
          <template #reference>
            <el-button type="primary" size="mini">
              <el-icon><Setting /></el-icon>
              自定义列
            </el-button>
          </template>

          <div class="p-4">
            <div class="mb-4 font-bold">自定义列设置</div>
            <el-scrollbar max-height="400px">
              <el-checkbox-group v-model="checkedColumns" class="flex flex-col gap-2">
                <el-checkbox
                  v-for="col in columns"
                  :key="col.field"
                  :label="col.field"
                  :disabled="col.field === 'str_wo'"
                  class="!flex !w-full"
                >
                  <span class="truncate" :title="col.title">{{col.title}}</span>
                </el-checkbox>
              </el-checkbox-group>
            </el-scrollbar>

            <div class="mt-4 flex justify-end gap-2">
              <el-button size="small" @click="columnSettingVisible = false">取消</el-button>
              <el-button size="small" type="primary" @click="confirmColumnSetting">确定</el-button>
            </div>
          </div>
        </el-popover>
      </div>
    </div>
    <!-- 表格容器 -->
    <div class="ml-4 mr-4 mt-2" style="height: calc(100vh - 60px)">
      <HtVxeTable
        ref="tableRef"
        :tableData="data"
        :tableColumns="columns"
        :isShowHeaderCheckbox="true"
        :showFooter="true"
        :footerData="footerData"
        :footerMethod="footerMethod"
        :cellClassName="cellClassName"
        @cell-click="openDrawer"
        @sort-change="onSortChangeEvent"
        @footer-cell-click="onFooterCellClick"
      >
        <!-- 自定义单元格内容 -->
        <template #wo_default="{column, scope}">
          <!-- 使用tailwidcss生成一个圆圈 -->
          <div class="flex items-center justify-center">
            <div class="flex h-4 w-4 items-center justify-center rounded-full" :class="getBgColor(scope.row)"></div>
            <span class="ml-2">{{ scope.row.str_wo }}</span>
          </div>
        </template>
        <template #int109_default="{ column, scope }">
          <IntValueDisplay
            :row="scope.row"
            field-first="int109"
            field-second="int126"
            :getCustomColor="getCustomColor"
            :getCustomSecondColor="getCustomColor"
            @onIntClick="onInt109Click"
          />
        </template>
        <template #int109_footer="{ items, data, columnIndex }">
          <div class="flex items-center justify-between">
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt109Click(column, 'int109')">
              {{ items[columnIndex]?.split('|')[0] }}
            </span>
            <span>|</span>
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt109Click(column, 'int126')">
              {{ items[columnIndex]?.split('|')[1] }}
            </span>
          </div>
        </template>
        <template #int113_default="{ column, scope }">
          <div class="flex items-center">
            <div v-if="scope.row.int125">
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int125')"
                @click.stop="onInt113Click(scope.row, 'int125')"
              >
                {{ scope.row.int125 }}
              </span>
              <span class="mx-1 font-medium text-black">|</span>
            </div>
            <div v-else>
              <span class="mx-1 font-medium text-black">|</span>
            </div>

            <div v-if="scope.row.int113">
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int113')"
                @click.stop="onInt113Click(scope.row, 'int113')"
              >
                {{ scope.row.int113 }}
              </span>
              <span class="mx-1 font-medium text-black">|</span>
            </div>
            <div v-else>
              <span class="mx-1 font-medium text-black">|</span>
            </div>

            <div v-if="scope.row.int122">
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int122')"
                @click.stop="onInt113Click(scope.row, 'int122')"
              >
                {{ scope.row.int122 }}
              </span>
            </div>
          </div>
        </template>

        <!-- 自定义表尾内容 -->
        <template #int113_footer="{ items, column, columnIndex }">
          <div class="flex items-center justify-between">
            <!-- 取|之前的值 -->
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt113Click(column, 'int125')">
              {{ items[columnIndex]?.split('|')[0] }}
            </span>
            <span>|</span>
            <!-- 取|之前的值 -->
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt113Click(column, 'int113')">
              {{ items[columnIndex]?.split('|')[1] }}
            </span>
            <span>|</span>
            <!-- 取|之后的值 -->
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt113Click(column, 'int122')">
              {{ items[columnIndex]?.split('|')[2] }}
            </span>
          </div>
        </template>

        <template #int115_default="{ column, scope }">
          <div class="flex items-center justify-between">
            <div v-if="scope.row.int115 && scope.row.int124">
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int124')"
                @click.stop="onInt115Click(scope.row, 'int124')"
              >
                {{ scope.row.int124 }}
              </span>
              <span class="mx-1 font-medium text-black">|</span>
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int115')"
                @click.stop="onInt115Click(scope.row, 'int115')"
              >
                {{ scope.row.int115 }}
              </span>
            </div>
            <div v-else-if="scope.row.int115">
              <span class="mx-1 font-medium text-black">|</span>
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int115')"
                @click.stop="onInt115Click(scope.row, 'int115')"
              >
                {{ scope.row.int115 }}
              </span>
            </div>
            <div v-else-if="scope.row.int124">
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int124')"
                @click.stop="onInt115Click(scope.row, 'int124')"
              >
                {{ scope.row.int124 }}
              </span>
              <span class="mx-1 font-medium text-black">|</span>
            </div>
          </div>
        </template>

        <template #int115_footer="{ items, data, columnIndex }">
          <div class="flex items-center justify-between">
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt115Click(column, 'int124')">
              {{ items[columnIndex]?.split('|')[0] }}
            </span>
            <span>|</span>
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt115Click(column, 'int115')">
              {{ items[columnIndex]?.split('|')[1] }}
            </span>
          </div>
        </template>

        <template #int200_default="{ column, scope }">
          <div class="flex items-center">
            <div v-if="scope.row.int200">
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int200')"
                @click.stop="onInt200Click(scope.row, 'int200')"
              >
                {{ scope.row.int200 }}
              </span>
              <span class="mx-1 font-medium text-black">|</span>
            </div>
            <div v-else>
              <span class="mx-1 font-medium text-black">|</span>
            </div>

            <div v-if="scope.row.int300">
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int300')"
                @click.stop="onInt200Click(scope.row, 'int300')"
              >
                {{ scope.row.int300 }}
              </span>
              <span class="mx-1 font-medium text-black">|</span>
            </div>
            <div v-else>
              <span class="mx-1 font-medium text-black">|</span>
            </div>

            <div v-if="scope.row.int400">
              <span
                class="hover:cursor-pointer hover:text-blue-500 hover:underline"
                :class="getCustomColor(scope.row, 'int400')"
                @click.stop="onInt200Click(scope.row, 'int400')"
              >
                {{ scope.row.int400 }}
              </span>
            </div>
          </div>
        </template>

        <!-- 自定义表尾内容 -->
        <template #int200_footer="{ items, column, columnIndex }">
          <div class="flex items-center justify-between">
            <!-- 取|之前的值 -->
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt200Click(column, 'int200')">
              {{ items[columnIndex]?.split('|')[0] }}
            </span>
            <span>|</span>
            <!-- 取|之前的值 -->
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt200Click(column, 'int300')">
              {{ items[columnIndex]?.split('|')[1] }}
            </span>
            <span>|</span>
            <!-- 取|之后的值 -->
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt200Click(column, 'int400')">
              {{ items[columnIndex]?.split('|')[2] }}
            </span>
          </div>
        </template>

        <template #int0110_default="{ column, scope }">
          <div class="flex items-center">
            <span v-if="scope.row.int0110" class="hover:cursor-pointer hover:text-blue-500 hover:underline" @click.stop="onInt0110Click(scope.row, 'int0110')">
              {{ scope.row.int0110 }}
            </span>
          </div>
        </template>

        <template #int0110_footer="{ items, column, columnIndex }">
          <div class="flex items-center justify-between">
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt0110Click(column, 'int0110')">
              {{ items[columnIndex] }}
            </span>
          </div>
        </template>

        <template #int0111_default="{ column, scope }">
          <div class="flex items-center">
            <span v-if="scope.row.int0111" class="hover:cursor-pointer hover:text-blue-500 hover:underline" @click.stop="onInt0111Click(scope.row, 'int0111')">
              {{ scope.row.int0111 }}
            </span>
          </div>
        </template>

        <template #int0111_footer="{ items, column, columnIndex }">
          <div class="flex items-center justify-between">
            <span class="cursor-pointer hover:underline" @click.stop="onFooterInt0111Click(column, 'int0111')">
              {{ items[columnIndex] }}
            </span>
          </div>
        </template>
      </HtVxeTable>
    </div>

    <!-- 普通抽屉组件 -->
    <EngineDrawer
      v-if="drawerState.visible"
      v-model:visible="drawerState.visible"
      :type="drawerState.type"
      :title="drawerState.title"
      :id-wo="drawerState.idWo"
      :is_delivery="drawerState.is_delivery"
      
      my_table_class="my_table_class"
    />

    <!-- 总量抽屉组件 -->
    <TotalDrawer
      v-if="totalDrawerState.visible"
      v-model:visible="totalDrawerState.visible"
      :type="totalDrawerState.type"
      :title="totalDrawerState.title"
      :id-wo="totalDrawerState.idWo"
    />

    <!-- 白转黄抽屉组件 -->
    <WhiteToYellowDrawer
      v-if="whiteToYellowDrawerState.visible"
      v-model:visible="whiteToYellowDrawerState.visible"
      :type="whiteToYellowDrawerState.type"
      :id-wo="whiteToYellowDrawerState.idWo"
      
    />

    <!-- 红框后零件抽屉组件 -->
    <RedFramePartDrawer
      v-if="redFramePartDrawerState.visible"
      v-model:visible="redFramePartDrawerState.visible"
      :type="redFramePartDrawerState.type"
      :id-wo="redFramePartDrawerState.idWo"
    />
  `,
}

export default PartStatistical
