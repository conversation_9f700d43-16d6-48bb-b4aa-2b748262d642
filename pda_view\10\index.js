import { DM_VIEW, EDD_UPDATE_TASK_PDA, PDA_VIEW_AD } from '../../config/tabPaneKey.js'
import ErrorComponent from '../../components/error.component.js'
import LoadingComponent from '../../components/loading.component.js'
import { currentDateKey, currentNodeKey, currentTypeViewKey, searchDateKey, searchFormKey } from '../../config/keys.js'
import { SUBCONTRACT_NODE } from '../../config/nodeKey.js'
import { useTypeView } from '../hooks/useTypeView.js'
import { useCommApi } from '../hooks/useCommApi.js'

const { ref, reactive, provide, defineAsyncComponent, onMounted, toRefs } = Vue

export default {
  name: 'SubcontractComponent',
  components: {
    CardComponent: defineAsyncComponent({
      loader: () => import('../components/card.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    TotalViewComponent: defineAsyncComponent({
      loader: () => import('../components/total.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    PViewComponent: defineAsyncComponent({
      loader: () => import('../components/p.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    DMViewComponent: defineAsyncComponent({
      loader: () => import('../components/dm.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HeaderSearchComponent: defineAsyncComponent({
      loader: () => import('../components/HeaderSearch/index.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup() {
    const inputLowerDay = ref(-7)
    const { getUsers, burialPoint } = useCommApi()
    const userS = ref([])
    const userST = ref([])
    const startDate = ref(moment().format('YYYY-MM-DD'))
    const endDate = ref(moment().add(6, 'days').format('YYYY-MM-DD'))
    provide(searchDateKey, {
      startDate,
      endDate,
      updateSearchDate: (start, end) => {
        startDate.value = start
        endDate.value = end
      },
    })

    const currentDate = ref(moment().format('YYYY-MM-DD'))
    provide(currentDateKey, {
      currentDate,
      updateCurrentDate: date => {
        currentDate.value = date
      },
    })

    // 初始化currentDate
    const initCurrentDate = () => {
      currentDate.value = moment().format('YYYY-MM-DD')
    }

    provide(currentNodeKey, SUBCONTRACT_NODE)
    const { currentTypeView, updateCurrentTypeView, activeName } = useTypeView(false)
    const handleTabChange = name => {
      switch (name) {
        case '0':
          updateCurrentTypeView(PDA_VIEW_AD)
          initCurrentDate()
          break
        case '1':
          updateCurrentTypeView(DM_VIEW)
          initCurrentDate()
          break
        case '2':
          updateCurrentTypeView(EDD_UPDATE_TASK_PDA)
          initCurrentDate()
          break
      }
    }
    provide(currentTypeViewKey, {
      currentTypeView,
      updateCurrentTypeView,
    })
    const searchForm = reactive({
      date: [moment().format('YYYY-MM-DD'), moment().add(6, 'days').format('YYYY-MM-DD')],
      //id_engine_type: 'cfm56',
    })
    const refreshKey = ref(0)
    const updateSearchForm = form => {
      Object.assign(searchForm, form)
      refreshKey.value += 1
    }
    provide(searchFormKey, {
      searchForm,
      updateSearchForm,
    })
    // 查询
    const handleSearchClick = form => {
      updateSearchForm(form)
    }
    const userFilter = val => {
      if (val) {
        userST.value = userS.value.filter(x => x.str_name?.indexOf(val) > -1 || x.str_code?.indexOf(val) > -1)
      } else {
        userST.value = userS.value
      }
    }

    // 搜索条件
    const searchEddForm = reactive({
      str_staff: '',
    })

    function updateSearchEddForm(form) {
      Object.assign(searchEddForm, form)
    }

    provide('searchEddForm', {
      searchEddForm,
    })
    // EDD查询
    const handleSearchEddClick = () => {
      updateSearchEddForm(searchEddForm)
    }

    onMounted(async () => {
      burialPoint(SUBCONTRACT_NODE)
      userS.value = await getUsers('RULS')
      userST.value = await getUsers('RULS')
    })
    return {
      handleTabChange,
      activeName,
      handleSearchClick,
      userS,
      userST,
      userFilter,
      searchEddForm,
      handleSearchEddClick,
      refreshKey,
      inputLowerDay,
    }
  },
  template: /*html*/ `
    <el-tabs v-model="activeName" type="card" @tab-change="handleTabChange">
      <el-tab-pane label="PDA View 视图" name="0" layz>
        <div class="mx-4">
          <HeaderSearchComponent @search="handleSearchClick">
            <vxe-form-item title="Owner:" folding>
              <template #default="{data}">
                <el-select
                  v-model.trim="data.str_staff"
                  clearable
                  filterable
                  style="width: 210px;"
                  :filter-method="userFilter"
                >
                  <el-option v-for="item in userST" :key="item.id" :label="item.str_name" :value="item.str_name"
                    >{{ item.str_name }}-{{ item.str_code }}
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
          </HeaderSearchComponent>
        </div>
        <div class="mb-4 w-full border-b border-gray-200"></div>
        <!--    卡片-->
        <div class="mx-4">
          <CardComponent :key="refreshKey"></CardComponent>
        </div>
        <div class="my-4 w-full border-b border-gray-200"></div>
        <div class="mx-4">
          <TotalViewComponent :key="refreshKey"></TotalViewComponent>
        </div>

        <div class="my-4 w-full border-b border-gray-200"></div>
        <div class="mx-4">
          <PViewComponent :key="refreshKey"></PViewComponent>
        </div>
        <div class="mt-4"></div>
      </el-tab-pane>
      <el-tab-pane label="DM View 视图" name="1" lazy>
        <div class="mx-4">
          <DMViewComponent>
            <vxe-form-item title="Owner:" folding>
              <template #default="{data}">
                <el-select
                  v-model.trim="data.str_staff"
                  clearable
                  filterable
                  style="width: 210px;"
                  :filter-method="userFilter"
                >
                  <el-option v-for="item in userST" :key="item.id" :label="item.str_name" :value="item.str_name"
                    >{{ item.str_name }}-{{ item.str_code }}
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
          </DMViewComponent>
        </div>
      </el-tab-pane>
      <el-tab-pane label="EDD update task PDA/EDD 更新任务PDA" name="2" lazy>
        <div class="mx-4">
          <el-form :model="searchEddForm" label-width="100px" inline>
            <el-form-item label="Owner" prop="str_staff">
              <el-select
                v-model="searchEddForm.str_staff"
                clearable
                filterable
                style="width: 210px;"
                :filter-method="userFilter"
              >
                <el-option v-for="item in userST" :key="item.id" :label="item.str_name" :value="item.str_name"
                  >{{ item.str_name }}-{{ item.str_code }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="my-4 border-b border-gray-200"></div>
          <PViewComponent type="edd">
            <template v-slot:lower="props">
              <span class="mr-2 text-sm">下限:</span>
              <el-input-number
                size="small"
                v-model="inputLowerDay"
                :step="1"
                :max="props.max"
                @change="props.lower"
              ></el-input-number>
            </template>
            <template v-slot:refresh="props">
              <el-button type="primary" @click="props.refresh">
                <el-icon>
                  <Refresh></Refresh>
                </el-icon>
              </el-button>
            </template>
            <template v-slot:exceed="props">
              超过3次未更新EDD: <el-button type="text" @click="props.exceedClick(3)">{{ props.exceed }}</el-button>
            </template>
            <template v-slot:never="props">
              从未更新EDD: <el-button type="text" @click="props.neverClick(0)">{{ props.never }}</el-button>
            </template>
            <template v-slot:total="props">
              Total: <el-button type="text" @click="props.totalClick">{{ props.total }}</el-button>
            </template>
          </PViewComponent>
        </div>
      </el-tab-pane>
    </el-tabs>
  `,
}
