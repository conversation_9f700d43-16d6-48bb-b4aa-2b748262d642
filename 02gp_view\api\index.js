import { post } from '../../config/axios/httpReuest.js'

// get flow
export const getFlow = async () => {
  const list = []
  const { data } = await post({
    ac: 'gp_search_flow',
  })
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return []
  }
  list.push(...data.data)
  return list
}
// 查询SM
export const getSM = async (id_engine_type = '') => {
  const list = []
  const { data } = await post({
    ac: 'gp_search_engine_sm',
    id_engine_type,
  })
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return []
  }
  list.push(...data.data)
  return list
}
// 查询维修类型
export const getRepairType = async () => {
  const list = []
  const { data } = await post({
    ac: 'common_get_sys_enum_list',
    key: 'engine_level',
  })
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return []
  }
  list.push(...data.data)
  return list
}
// 查询任务列表
export const getTaskList = async (queryLists = []) => {
  const list = []
  const { data } = await post({
    ac: 'gp_task_info_search',
    queryLists,
  })
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return []
  }
  list.push(...data.data)
  return list
}
// 保存模板
export const saveTemplate = async (params) => {
  const { data } = await post({
    ac: 'gp_template_add',
    data: params,
  })
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}
// 编辑模板
export const editTemplate = async (params) => {
  const { data } = await post({
    ac: 'gp_template_edit',
    data: params,
  })
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}
// 模板启用禁用
export const enableTemplate = async (dealType, lists) => {
  const { data } = await post({
    ac: 'gp_template_change_state',
    dealType,
    lists,
  })
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}
// 删除模板
export const deleteTemplate = async (lists) => {
  const { data } = await post({
    ac: 'gp_template_del',
    lists,
  })
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return false
  }
  ElementPlus.ElMessage.success(data.text)
  return true
}
// 查询模板列表
export const queryTemplateList = async (queryLists = []) => {
  const list = []
  const { data } = await post({
    ac: 'gp_template_search',
    queryLists,
  })
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text)
    return []
  }
  list.push(...data.data)
  return list
}

/**
 * 获取主单元体计划甘特图
 */
export const getMainUnitGantt = async (id, str_node, dt_start, dt_end, str_engine_type) => {
  const params = {
    ac: 'gp_plan_sm_gantt',
    ...(id && { id }),
    ...(str_node && { str_node }),
    ...(dt_start && { dt_start }),
    ...(dt_end && { dt_end }),
    ...(str_engine_type && { str_engine_type }),
  }
  const { data } = await post(params)
  const { code, text, data: resData } = data
  if (code === 'error') {
    ElementPlus.ElMessage.error(text)
    return []
  }
  return resData
}
/**
 * 保存主单元体计划甘特图
 */
export const saveMainUnitGantt = async (data) => {
  const params = {
    ac: 'gp_save_sm_gantt',
    data,
  }
  const { data: resData } = await post(params)
  const { code, text } = resData
  if (code === 'error') {
    ElementPlus.ElMessage.error(text)
    return false
  }
  ElementPlus.ElMessage.success(text)
  return true
}
/**
 * 获取试车总量甘特图
 */
export const getF42QuantityGantt = async (str_node, dt_start, dt_end) => {
  const params = {
    ac: 'gp_f42_gantt',
    ...(str_node && { str_node }),
    ...(dt_start && { dt_start }),
    ...(dt_end && { dt_end }),
  }
  const { data } = await post(params)
  const { code, text, data: resData } = data
  if (code === 'error') {
    ElementPlus.ElMessage.error(text)
    return []
  }
  return resData
}

/**
 * 获取release总量甘特图
 */
export const getReleaseQuantityGantt = async (str_node, dt_start, dt_end) => {
  const params = {
    ac: 'gp_release_gantt',
    ...(str_node && { str_node }),
    ...(dt_start && { dt_start }),
    ...(dt_end && { dt_end }),
  }
  const { data } = await post(params)
  const { code, text, data: resData } = data
  if (code === 'error') {
    ElementPlus.ElMessage.error(text)
    return []
  }
  return resData
}

/**
 * 更新甘特图任务时间
 * @param {Array} data 甘特图数据
 */
export const updateGanttTask = async (data) => {
  const params = {
    ac: 'gp_save_f42_gantt',
    data,
  }
  const { data: resData } = await post(params)
  const { code, text } = resData
  if (code === 'error') {
    ElementPlus.ElMessage.error(text)
    return false
  }
  return true
}
