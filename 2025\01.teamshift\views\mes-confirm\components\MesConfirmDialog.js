const { ref, reactive, computed } = Vue
const { ElMessage } = ElementPlus

import { confirmSaveMesApply } from '../../mes-apply/api/index.js'
import { MesApplyFormDetails } from '../../mes-apply/components/MesApplyFormDetails.js'

export default {
  name: 'MesConfirmDialog',
  components: {
    MesApplyFormDetails,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      required: true,
    },
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const isSubmitting = ref(false)

    // 计算属性：对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value),
    })

    // 处理确认保存
    const handleConfirmSave = async () => {
      try {
        isSubmitting.value = true

        // 验证确认数据
        const hasInvalidData = props.formData.details.some((detail) => {
          return !detail.confirmStartTime || !detail.confirmEndTime || !detail.confirmHours
        })

        if (hasInvalidData) {
          ElMessage.error('请完善所有确认信息')
          return
        }

        // 格式化确认数据
        const confirmData = []
        props.formData.details.forEach((detail) => {
          confirmData.push({
            id: detail.id,
            dt_date: detail.date,
            dt_confirm_start: detail.confirmStartTime,
            dt_confirm_end: detail.confirmEndTime,
            int_confirm_hours: detail.confirmHours,
            int_confirm_status: detail.confirmStatus,
          })
        })

        await confirmSaveMesApply(confirmData)
        ElMessage.success('确认保存成功')
        emit('success')
      } catch (error) {
        console.error('确认保存失败:', error)
        ElMessage.error('确认保存失败')
      } finally {
        isSubmitting.value = false
      }
    }

    // 处理取消
    const handleCancel = () => {
      dialogVisible.value = false
    }

    // 更新详情数据
    const handleUpdateDetails = (details) => {
      // 直接更新响应式对象的details数组
      props.formData.details.splice(0, props.formData.details.length, ...details)
    }

    return {
      dialogVisible,
      isSubmitting,
      handleConfirmSave,
      handleCancel,
      handleUpdateDetails,
    }
  },

  template: /*html*/ `
    <el-dialog
      v-model="dialogVisible"
      title="工时确认"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="mes-confirm-dialog"
    >
      <div class="space-y-6">
        <!-- 申请信息概览 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-600">申请人：</span>
              <span class="font-medium">{{ formData.person }}</span>
            </div>
            <div>
              <span class="text-gray-600">开始日期：</span>
              <span class="font-medium">{{ formData.startDate }}</span>
            </div>
            <div>
              <span class="text-gray-600">结束日期：</span>
              <span class="font-medium">{{ formData.endDate }}</span>
            </div>
            <div>
              <span class="text-gray-600">申请原因：</span>
              <span class="font-medium">{{ formData.reason }}</span>
            </div>
          </div>
          <div v-if="formData.remarks" class="mt-3 pt-3 border-t border-gray-200">
            <span class="text-gray-600">备注：</span>
            <span class="font-medium">{{ formData.remarks }}</span>
          </div>
        </div>

        <!-- 工时确认详情 -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">工时确认详情</h3>
          <MesApplyFormDetails
            mode="confirm"
            :details="formData.details"
            :readonly="false"
            @update:details="handleUpdateDetails"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="handleCancel" :disabled="isSubmitting" icon="Back">
            取消
          </el-button>
          <el-button
            type="success"
            @click="handleConfirmSave"
            :loading="isSubmitting"
            :disabled="isSubmitting"
            icon="Check"
          >
            {{ isSubmitting ? '保存中...' : '确认保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  `,
} 