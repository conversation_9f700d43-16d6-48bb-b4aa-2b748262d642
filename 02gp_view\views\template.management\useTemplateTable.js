import { post } from '../../../config/axios/httpReuest.js';

const { ref } = Vue;

export function useTemplateTable() {
  const tableRef = ref(null);
  /**
   * @description 获取表格数据
   * @param queryLists
   * @return {Promise<array>}
   */
  const getTableData = async (queryLists) => {
    const result = [];
    const param = {
      ac: 'gp_template_search',
      queryLists,
    };
    const { data } = await post(param);
    if (data.code === 'error') {
      ElementPlus.ElMessage.error(data.text);
      return [];
    }
    result.push(...data.data);
    return result;
  };

  // 判断是否选择一条数据
  const isSelectOne = () => {
    const selectedRows = tableRef.value.getSelectedData();
    if (selectedRows.length !== 1) {
      ElementPlus.ElMessage.warning('请选择一条数据');
      return false;
    }
    return selectedRows[0];
  };

  // 获取所选表格的ID
  const getSelectedId = () => {
    const selectedRows = tableRef.value.getSelectedData();
    return selectedRows.map((item) => item.id);
  };

  return {
    getTableData,
    tableRef,
    isSelectOne,
    getSelectedId,
  };
}
