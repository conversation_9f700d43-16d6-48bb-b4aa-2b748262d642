const { reactive, onMounted, markRaw, ref } = Vue

export default {
  name: 'FilterSelect',
  props: {
    params: Object,
  },
  setup(props) {
    const state = reactive({
      option: { data: null },
      columnField: null,
    })

    const options = ref([])

    // 初始化数据
    const load = () => {
      const { params } = props
      if (params) {
        const { column } = params
        // 保存列标识
        state.columnField = column.field

        console.log(`FilterSelect load for ${column.field}:`, {
          filters: column.filters,
          filterOptions: column.filterOptions
        })

        // 直接使用 column.filters[0]，与 FilterInput 保持一致
        if (column.filters?.[0]) {
          state.option = column.filters[0]
          console.log(`Using column.filters[0] for ${column.field}:`, state.option)
        }

        // 获取下拉选择的选项列表
        // 优先从 column.filterOptions 获取，如果没有则从 column.filters 获取（除第一个外）
        if (column.filterOptions && Array.isArray(column.filterOptions)) {
          options.value = column.filterOptions
        } else if (column.filters && Array.isArray(column.filters) && column.filters.length > 1) {
          // 如果filters数组中有多个选项，除第一个外的都是选择选项
          options.value = column.filters
        } else {
          // 默认选项
          options.value = []
        }

        console.log(`Options for ${column.field}:`, options.value)
      }
    }

    const changeOptionEvent = () => {
      const { params } = props
      if (params) {
        const { $panel } = params
        const { option, columnField } = state

        if (params.column.field === columnField) {
          const checked = !!option.data
          console.log(`FilterSelect changeOption for ${columnField}:`, {
            optionData: option.data,
            checked: checked,
            option: option
          })
          $panel.changeOption({}, checked, option)
        }
      }
    }

    // 初始化
    onMounted(() => {
      load()
    })

    return {
      state,
      options,
      changeOptionEvent,
    }
  },
  template: /*html*/ `
    <vxe-select
      v-model="state.option.data"
      clearable
      placeholder="请选择"
      @change="changeOptionEvent"
    >
      <vxe-option
        v-for="item in options"
        :key="item.value"
        :value="item.value"
        :label="item.label"
      />
    </vxe-select>
  `,
}
