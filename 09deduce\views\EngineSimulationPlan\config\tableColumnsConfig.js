/**
 * 串件方案的表格列配置
 */
export const componentReplacementPlanColumns = [
  {
    title: '零件来源',
    field: 'str_source_type',
    fixed: 'left',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: 'EKD',
    minWidth: 120,
    field: 'dt_ekd',
    fixed: 'left',
    filters: [{ data: '' }],
    fiterRender: { name: 'FilterInput' },
  },
  {
    title: '零件标签',
    field: 'str_label',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '原台客户',
    field: 'str_client_sp',
    minWidth: 160,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '原台发动机排序',
    field: 'int_sort',
    minWidth: 150,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: 'EKD是否满足模拟',
    field: 'is_ekd_meet',
    minWidth: 160,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '是否同客户',
    field: 'is_same_client',
    minWidth: 160,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '推演占用',
    field: 'str_wo_de',
    minWidth: 160,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: 'Kitting完成/站点',
    field: 'str_nodename',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '供体计划开始时间',
    field: 'dt_project_start',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '供体计划结束时间',
    field: 'dt_project_end',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '供体ESN',
    field: 'str_esn_sp',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '供体WO',
    field: 'str_wo_sp',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '初始ESN',
    field: 'str_esn',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '初始WO',
    field: 'str_wo',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: 'PN',
    field: 'str_pn',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: 'SM',
    field: 'str_sm',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '零件名称',
    field: 'str_part_name',
    minWidth: 150,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '零件条码',
    field: 'str_bcode',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '仓库',
    field: 'str_wh_name',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '货位名称',
    field: 'str_product_name',
    minWidth: 150,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '批次',
    field: 'str_batch',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: 'PKP',
    field: 'id_pkp',
    minWidth: 100,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '要求证书',
    field: 'str_certificate_copy',
    minWidth: 130,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '供体零件证书',
    field: 'str_certificate',
    minWidth: 130,
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
  {
    title: '数量',
    minWidth: 150,
    field: 'int_num',
    filters: [{ data: '' }],
    filterRender: { name: 'FilterInput' },
  },
]
