const ExportButton = {
  props: {
    isUpdateExport: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },
  template: /*html*/ `
    <button class="flex items-center gap-3 bg-white px-5 py-4 text-sm text-black shadow-sm" type="button">
      <el-badge is-dot :hidden="!isUpdateExport">
        <svg
          t="1725327978632"
          class="icon"
          viewBox="0 0 1170 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="2535"
          width="52"
          height="52"
        >
          <path d="M12.190476 0h1024v1024H12.190476z" fill="#909090" fill-opacity="0" p-id="2536"></path>
          <path
            d="M97.52381 426.666667m42.666666 0l768 0q42.666667 0 42.666667 42.666666l0 426.666667q0 42.666667-42.666667 42.666667l-768 0q-42.666667 0-42.666666-42.666667l0-426.666667q0-42.666667 42.666666-42.666666Z"
            fill="#4A90E2"
            fill-opacity=".6"
            p-id="2537"
          ></path>
          <path
            d="M537.033143 57.344l261.077333 298.368a17.066667 17.066667 0 0 1-12.885333 28.288H263.15581a17.066667 17.066667 0 0 1-12.885334-28.288L511.390476 57.344a17.066667 17.066667 0 0 1 25.685334 0z"
            fill="#4A90E2"
            p-id="2538"
          ></path>
          <path
            d="M396.190476 384h256v341.333333H396.190476zM396.190476 768h256v42.666667H396.190476z"
            fill="#4A90E2"
            p-id="2539"
          ></path>
          <path
            d="M575.390476 648.533333m-42.666666 0a42.666667 42.666667 0 1 0 85.333333 0 42.666667 42.666667 0 1 0-85.333333 0Z"
            fill="#E02020"
            p-id="2540"
          ></path>
        </svg>
      </el-badge>
      <span class="text-2xl">Export Excel</span>
    </button>
  `,
}
const ExportClick = () => {
  if (!searchForm.month) {
    ElementPlus.ElMessage.warning('Please select a month')
    isShowEmployeeRatingChart.value = false
    isShowDistributionChart.value = false
    return
  }
  isShowEmployeeRatingChart.value = true
  isShowDistributionChart.value = true
  employeeRef.value.getChartData(searchForm.month)
  distributionRef.value.getChartData(searchForm.month)
  getExceptionStatus()
}

export default ExportButton
