const { ref, computed, reactive } = Vue

const HtVxeTable = {
  name: 'HtVxeTable',
  props: {
    tableData: {
      type: Array,
      default: () => null,
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    isShowHeaderCheckbox: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['checkbox-change'],
  setup(props, { emit }) {
    // 加载状态
    const isLoading = computed(() => {
      return !props.tableData
    })

    // 设置表头样式
    const headerRowClassName = ({ row, rowIndex }) => {
      return 'table-header'
    }

    const xTable = ref(null)

    // 下载
    const exportData = (name, func = onBeforeExport) => {
      const $table = xTable.value
      const isSupportExportXLSX = VXETable.globalConfs.exportTypes.includes('xlsx')
      if (isSupportExportXLSX) {
        $table.exportData({ type: 'xlsx', filename: name, beforeExportMethod: ({ options }) => func({ options }) })
      } else {
        $table.exportData({ type: 'csv', filename: name, beforeExportMethod: ({ options }) => func({ options }) })
      }
    }
    // 获取表格选中的数据
    const getSelectedData = () => {
      return xTable.value.getCheckboxRecords()
    }
    // 清除所有
    const clearAll = () => {
      xTable.value.clearAll()
    }
    // 复选框改变
    const changeCheckbox = ({ checked, row }) => {
      if (!props.isShowHeaderCheckbox) {
        // 1. 先清除所有选中的行
        xTable.value.clearCheckboxRow()
      }
      // 2. 选中当前行
      xTable.value.setCheckboxRow(row, checked)
      emit('checkbox-change', { checked, row })
    }
    // 获取当前数据的length
    const getCurrentLength = () => {
      const $table = xTable.value
      if ($table) {
        return $table.getTableData().visibleData.length
      }
      return 0
    }

    const defaultTitleSuffix = reactive({
      icon: 'vxe-icon-question-circle-fill',
      content: 'qq',
    })

    // 导出前处理
    const onBeforeExport = ({ options }) => {
      // 1. 获取数据
      const data = xTable.value.getTableData().visibleData
      // 2. 处理数据 将数据为null的值替换为空字符串
      const newData = data.map((item) => {
        return Object.fromEntries(Object.entries(item).map(([key, value]) => [key, value === null ? ' ' : value]))
      })
      // 3. 将处理后的数据赋值给options.data
      options.data = newData
    }

    return {
      xTable,
      defaultTitleSuffix,
      isLoading,
      headerRowClassName,
      exportData,
      getSelectedData,
      clearAll,
      changeCheckbox,
      getCurrentLength,
      onBeforeExport,
    }
  },
  template: /*html*/ `
    <vxe-table
      ref="xTable"
      :loading="isLoading"
      :column-config="{resizable: true, useKey: true}"
      :export-config="{ type: 'xlsx', sheetName: 'Sheet1'}"
      :row-config="{isHover: true, isCurrent: true, useKey: true}"
      :edit-config="{trigger: 'manual', mode: 'cell'}"
      :data="tableData"
      :filter-config="{remote: $attrs.remote}"
      border
      stripe
      :show-overflow="$attrs.showOverflow"
      :show-header="true"
      :scroll-y="{enabled: true, mode: 'wheel'}"
      :header-row-class-name="headerRowClassName"
      :cell-class-name="$attrs.cellClassName"
      :checkbox-config="{showHeader: isShowHeaderCheckbox}"
      :show-footer="$attrs.showFooter"
      :footer-data="$attrs.footerData"
      :footer-method="$attrs.footerMethod"
      height="100%"
      @filter-change="$attrs.onFilterChange"
      @checkbox-change="changeCheckbox"
      @cell-click="$attrs.onCellClick"
      @sort-change="$attrs.onSortChangeEvent"
      @footer-cell-click="$attrs.onFooterCellClick"
    >
      <slot name="checkbox"></slot>
      <vxe-column type="seq" width="60" fixed="left"></vxe-column>
      <vxe-column
        v-for="column in tableColumns"
        :visible="column.visible"
        :key="column.field"
        :type="column.type"
        :field="column.field"
        :title="column.title"
        :fixed="column.fixed"
        :min-width="column.minWidth"
        :filters="column.filters"
        :filter-render="column.filterRender"
        :formatter="column.formatter"
        :filter-multiple="column.filterMultiple"
        :edit-render="column.editRender"
        :sortable="true"
        :title-prefix="column.titlePrefix"
      >
        <template v-if="column.slots?.default" #default="scope">
          <slot :name="column.slots.default" :column="column" :scope="scope"></slot>
        </template>
        <template #header>
          <slot v-if="column.slots?.header" :name="column.slots.header" :column="column"></slot>
          <span v-else>{{ column.title }}</span>
        </template>
        
        <template v-if="column.slots?.footer" #footer="scope">
          <slot :name="column.slots.footer" :items="scope.items" :column="column" :columnIndex="scope.$columnIndex"></slot>
        </template>
      </vxe-column>
      <slot name="operation"></slot>
    </vxe-table>
  `,
}

export default HtVxeTable
