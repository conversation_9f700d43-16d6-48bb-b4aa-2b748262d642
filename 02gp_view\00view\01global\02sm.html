<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src='../../01asset/vue/vue3/unpkg.com_vue@3.3.4_dist_vue.global.js'></script>
    <!-- element-plus 引入样式 -->
    <link rel='stylesheet' href='../../01asset/element_ui/unpkg.com_element-plus@2.3.8_dist_index.css' />
    <link rel='stylesheet' href="../../01asset/css/index.css" />
    <link rel='stylesheet' href="../../02comm.config/comm.component/y-el-draw/y_el_draw.component.css" />
    <!-- element-plus 引入组件库 -->
    <script src='../../01asset/element_ui/unpkg.com_element-plus@2.3.8_dist_index.full.js'></script>
    <script src="../../01asset/datav/datav.umd.js"></script>
    <title>单元体试图</title>
</head>

<body>
    <div id='app'>
        <y-mp-sm></y-mp-sm>
    </div>
</body>

<script type='module'>
    import ySmGlobal from '../../03view/01global/02sm.compent.js'

    const { createApp, ref } = Vue
    const app = createApp({
        components: {


        },
    })

    app.component('y-mp-sm', ySmGlobal); // 第三方组件
    app.use(ElementPlus)
    app.mount('#app')
</script>
<!-- <style>
    .el-badge__content.is-fixed {
        top: -4px;
    }

    .my_red .el-badge__content.is-fixed {
        background-color: red;
    }

    .my_green .el-badge__content.is-fixed {
        background-color: green;
    }

    .my_gray .el-badge__content.is-fixed {
        background-color: #A8ABB2;
    }


    .el-table .warning-row {
        --el-table-tr-bg-color: var(--el-color-warning-light-9);
    }

    .el-table .success-row {
        --el-table-tr-bg-color: var(--el-color-success-light-9);
    }
</style> -->

</html>