export function useTableColumn() {
  // 获取当日PDA负之汇总的列表
  const getChartTableColumn = () => {
    return [
      {
        showColumn: true,
        prop: 'str_site',
        label: 'Site',
        minWidth: 120,
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_class',
        label: '标识',
        minWidth: 100,
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'int_day',
        minWidth: 100,
        label: 'P/#',
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_esn_sp',
        minWidth: 120,
        label: '目标ESN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_wo_sp',
        minWidth: 120,
        label: '目标WO',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_esn',
        minWidth: 120,
        label: '原台ESN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_wo',
        minWidth: 120,
        label: '原台WO',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'is_sp',
        minWidth: 120,
        label: '串件',
        field: [
          { label: 'YES', value: 1 },
          { label: 'NO', value: 0 },
        ],
        is_select: 'select',
      },

      {
        showColumn: true,
        prop: 'is_aog',
        minWidth: 120,
        label: 'AOG',
        field: [
          { label: 'YES', value: 1 },
          { label: 'NO', value: 0 },
        ],
        is_select: 'select',
      },
      {
        showColumn: true,
        prop: 'str_engine_type',
        minWidth: 150,
        label: 'Engine Type',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_flow',
        label: 'Flow',
        minWidth: 100,
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_sm',
        minWidth: 100,
        label: 'SM',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_pn',
        minWidth: 100,
        label: 'PN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_part_name',
        label: 'PN name',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      },
      { showColumn: true, prop: 'int_num', label: 'Qty', field: [{ value: '' }], is_select: 'input', minWidth: 100 },
      {
        showColumn: true,
        minWidth: 150,
        prop: 'dt_input',
        label: '进入站点时间',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'dt_plan',
        label: '计划开工时间',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'int_tatdays',
        label: '站点标准TAT',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        prop: 'id_main',
        minWidth: 120,
        label: '业务id',
        fixed: 'right',
        field: [{ value: '' }],
        is_select: 'input',
      },
    ];
  };

  // 获取PDA DM象限统计的列表
  const getTableDataColumn = () => {
    return [
      {
        showColumn: true,
        minWidth: 150,
        prop: 'str_type',
        label: '站点',
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'int_d',
        label: 'D/#',
        minWidth: 120,
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'int_m',
        label: 'M/#',
        minWidth: 120,
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_sn_ori',
        minWidth: 120,
        label: '目标ESN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_wo_ori',
        minWidth: 120,
        label: '目标WO',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        minWidth: 120,
        prop: 'str_esn',
        label: '原台ESN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        minWidth: 120,
        prop: 'str_wo',
        label: '原台WO',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'is_sp',
        label: '串件',
        minWidth: 120,
        field: [
          { label: 'YES', value: 1 },
          { label: 'NO', value: 0 },
        ],
        is_select: 'select',
      },
      {
        showColumn: true,
        prop: 'is_aog',
        label: 'AOG',
        minWidth: 120,
        field: [
          { label: 'YES', value: 1 },
          { label: 'NO', value: 0 },
        ],
        is_select: 'select',
      },
      {
        showColumn: true,
        prop: 'str_engine_type',
        minWidth: 150,
        label: 'Engine Type',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_flow',
        minWidth: 120,
        label: 'Flow',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_sm',
        minWidth: 120,
        label: 'SM',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_pn',
        minWidth: 120,
        label: 'PN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_part_name',
        minWidth: 150,
        label: 'PN name',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'dt_input',
        minWidth: 150,
        label: '进入站点时间',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'int_tatdays',
        label: '站点标准TAT',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'id_barcode',
        label: '业务id',
        fixed: 'right',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      },
    ];
  };

  // 获取PDA DM象限统计的列表
  const getTableDataColumnForP = () => {
    return [
      {
        showColumn: true,
        minWidth: 150,
        prop: 'str_site',
        label: '站点',
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'int_day',
        label: 'P#/M#',
        minWidth: 120,
        fixed: 'left',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_esn_sp',
        minWidth: 120,
        label: '目标ESN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_wo_sp',
        minWidth: 120,
        label: '目标WO',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        minWidth: 120,
        prop: 'str_esn',
        label: '原台ESN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        minWidth: 120,
        prop: 'str_wo',
        label: '原台WO',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'is_sp',
        label: '串件',
        minWidth: 120,
        field: [
          { label: 'YES', value: 1 },
          { label: 'NO', value: 0 },
        ],
        is_select: 'select',
      },
      {
        showColumn: true,
        prop: 'is_aog',
        label: 'AOG',
        minWidth: 120,
        field: [
          { label: 'YES', value: 1 },
          { label: 'NO', value: 0 },
        ],
        is_select: 'select',
      },
      {
        showColumn: true,
        prop: 'str_engine_type',
        minWidth: 150,
        label: 'Engine Type',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_flow',
        minWidth: 120,
        label: 'Flow',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_sm',
        minWidth: 120,
        label: 'SM',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_pn',
        minWidth: 120,
        label: 'PN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_part_name',
        minWidth: 150,
        label: 'PN name',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'dt_input',
        minWidth: 150,
        label: '进入站点时间',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'int_tatdays',
        label: '站点标准TAT',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      },

      {
        showColumn: true,
        prop: 'id_main',
        label: '业务id',
        fixed: 'right',
        minWidth: 150,
        field: [{ value: '' }],
        is_select: 'input',
      },
    ];
  };

  return {
    getChartTableColumn,
    getTableDataColumn,
    getTableDataColumnForP,
  };
}
