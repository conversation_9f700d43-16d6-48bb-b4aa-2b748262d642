{"root": {"data": {"id": "d4wzcdsiu1s0", "created": 1729056411562, "text": "工单系统设计思路"}, "children": [{"data": {"id": "d4wzcnpjvjs0", "created": 1729056433150, "text": "模版构建（有Section 组成工单）", "layout_mind_offset": {"x": -1, "y": 91}, "priority": 2}, "children": [{"data": {"id": "d4x0rhk7v340", "created": 1729060416340, "text": "将工单，切分成不同Section"}, "children": [{"data": {"id": "d4x0uuxlj4g0", "created": 1729060680539, "text": "每个Section，分配不同Actor,执行各自action"}, "children": []}, {"data": {"id": "d4x0wbj0a600", "created": 1729060795026, "text": "每个Section,可以选择审批、任务分配，进行流转、执行、反馈"}, "children": []}]}]}, {"data": {"id": "d4x0w40lbjs0", "created": 1729060778675, "text": "Section （工单的不同阶段内容）", "layout_mind_offset": {"x": 16, "y": -231}, "priority": 1}, "children": [{"data": {"id": "d4x1218bu800", "created": 1729061242798, "text": "按流转步骤划或处理流程划分"}, "children": []}]}, {"data": {"id": "d4x0ymso66w0", "created": 1729060976284, "text": "状态管理", "layout_mind_offset": {"x": 745, "y": 276}, "priority": 3}, "children": [{"data": {"id": "d4x0z5wv3g80", "created": 1729061017896, "text": "设置每个模版流转机制"}, "children": []}, {"data": {"id": "d4x1077l72g0", "created": 1729061099086, "text": "设置 Section 流转机制", "layout_right_offset": {"x": 4, "y": 3}}, "children": []}, {"data": {"id": "d4x1343hvyw0", "created": 1729061327400, "text": "设定审批机制"}, "children": []}]}, {"data": {"id": "d4x3ojl8heo0", "created": 1729068648992, "text": "工单模版扩充", "layout_mind_offset": {"x": 847, "y": 387}, "priority": 4}, "children": [{"data": {"id": "d4x3owyd4340", "created": 1729068678084, "text": "后续模版扩充，通过维护不同模版Setion "}, "children": []}]}]}, "template": "default", "theme": "fresh-blue", "version": "1.4.43"}