import { fetchInitHandover, fetchHandover } from '../api/index.js'

export function useAddHandover(props) {
  const { onMounted, ref, reactive } = Vue
  const { ElMessage } = ElementPlus
  // 交接提示
  const headerTips = ref('')

  // 查询条件
  const queryParams = reactive({
    esn: '',
    esnName: '',
    sm: '',
    flow: props.flow,
    type: props.type,
    teamName: '',
    shift: '',
    shiftName: '',
    teamId: '',
    id_by_to_receive: '',
  })
  // 加载状态
  const loading = ref(false)
  // 表格数据
  const tableData = ref([])
  // 初始化表格数据
  const initTableData = async () => {
    loading.value = true
    try {
      const res = await fetchInitHandover()
      headerTips.value = res.str_title
      // tableData.value = res.pTHandovers.map((item) => ({
      //   id: item.pTHandover.id || '',
      //   code: item.pTHandover.int_type || 0,
      //   legend: item.pTHandover.str_category || '',
      //   tip: item.pTHandover.str_help || '暂无信息',
      //   desc: item.pTHandover.str_content || '',
      //   isTransferred: item.pTHandover.int_status === 1,
      //   isOriginal: true,
      //   attachment: item.files || [],
      //   id_model: item.pTHandover.id_model,
      //   sm: item.pTHandover.str_sm || '',
      // }))
      queryParams.teamId = res.id_team
      queryParams.teamName = res.str_team_name
      queryParams.is_pending = '0'
      queryParams.is_completed = 0
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const editFirstMainData = ref({})
  const mainData = ref({})

  // 查询
  const handleQuery = async (isChange = false) => {
    if (!props.isEdit) {
      if (queryParams.type === 102) {
        if (!queryParams.shift || !queryParams.str_task_type) {
          ElMessage.error('请选择班次和工序后进行查询')
          return
        }
      }
      if (queryParams.type === 101) {
        if (!queryParams.esn || !queryParams.shift || !queryParams.str_task_type) {
          ElMessage.error('请选择esn、班次和工序后进行查询')
          return
        }
      }
      if (queryParams.type != 101 && queryParams.type != 102) {
        if (!queryParams.esn || !queryParams.shift || !queryParams.sm || !queryParams.task) {
          ElMessage.error('请选择发动机、单元体、班次、任务后进行查询')
          return
        }
      }
    }
    const editParams = {
      id: props.currentEditRow.id,
      id_sms: props.currentEditRow.id_sm,
      is_edit: 1,
    }

    const params = {
      id_wo: queryParams.esn,
      id_team: queryParams.teamId,
      dt_pt: moment().format('YYYY-MM-DD'),
      id_sms: queryParams.sm,
      id_shift: queryParams.shift,
      id_tasks: queryParams.task,
      str_handover_type: queryParams.type,
    }
    try {
      loading.value = true
      const res = await fetchHandover(props.isEdit && !isChange ? editParams : params)
      if (props.isEdit && res.pTHandoverMain) {
        editFirstMainData.value = res.pTHandoverMain
      }
      mainData.value = res.pTHandoverMain
      if (props.isEdit && mainData.value) {
        queryParams.esn = res.pTHandoverMain.id_wo
        queryParams.sm = props.currentEditRow.id_sm
        queryParams.shift = res.pTHandoverMain.id_shift
        queryParams.task = res.pTHandoverMain.id_task
        queryParams.id_by_to_receive = res.pTHandoverMain.id_by_to_receive
        queryParams.is_pengindg = res.pTHandoverMain.is_penging
        queryParams.str_task_type = res.pTHandoverMain.str_task_type
      }
      // 如果查询条件改变，或者有mainData， 或者不是编辑状态，则更新表格数据
      if (!isChange || mainData.value || !props.isEdit) {
        tableData.value = res.pTHandovers.map((item) => ({
          id: item.pTHandover.id || '',
          code: item.pTHandover.int_type || 0,
          legend: item.pTHandover.str_category || '',
          tip: item.pTHandover.str_help || '暂无信息',
          desc: item.pTHandover.str_content || '',
          attachment: item.files || [],
          id_model: item.pTHandover.id_model,
          sm: item.pTHandover.str_sm || '',
          isTransferred: item.pTHandover.int_status === 1,
          isOriginal: true,
        }))
      }
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  // 重置查询
  const handleReset = () => {
    Object.keys(queryParams).forEach((key) => {
      queryParams[key] = ''
    })
    queryParams.type = props.type
    queryParams.flow = props.flow
    initTableData()
  }
  onMounted(() => {
    initTableData()
    if (props.isEdit) {
      handleQuery()
    }
  })

  // 新增行
  const handleAddRow = (row) => {
    if (!row?.code || !row?.legend) {
      ElMessage.warning('无效的行数据')
      return
    }

    // 创建新行，使用解构赋值确保数据结构一致性
    const newRow = {
      ...row,
      id: '', // 清空ID，因为这是新行
      desc: '',
      attachment: [],
      isExpanded: false,
      isOriginal: false,
      isTransferred: false,
    }

    // 找到最后一个相同code的行的索引
    let lastSameCodeIndex = -1
    for (let i = 0; i < tableData.value.length; i++) {
      if (tableData.value[i].code === row.code) {
        lastSameCodeIndex = i
      }
    }

    // 创建新的数组副本
    const updatedTableData = [...tableData.value]

    if (lastSameCodeIndex === -1) {
      // 如果没有找到相同code的行，找到第一个大于当前code的位置插入
      const insertBeforeIndex = tableData.value.findIndex((item) => item.code > row.code)
      if (insertBeforeIndex === -1) {
        // 如果没有找到更大的code，则添加到末尾
        updatedTableData.push(newRow)
      } else {
        // 插入到更大code的行前面
        updatedTableData.splice(insertBeforeIndex, 0, newRow)
      }
    } else {
      // 插入到最后一个相同code的行后面
      updatedTableData.splice(lastSameCodeIndex + 1, 0, newRow)
    }

    // 更新表格数据
    tableData.value = updatedTableData

    ElMessage.success('新增行成功')
  }

  return {
    tableData,
    headerTips,
    queryParams,
    mainData,
    editFirstMainData,
    handleQuery,
    handleReset,
    handleAddRow,
  }
}
