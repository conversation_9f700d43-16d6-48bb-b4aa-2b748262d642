const { useVModel } = VueUse
const { reactive, onMounted, computed, ref } = Vue
import { getWhiteToYellowData } from '../api/index.js'
import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'

export default {
  name: 'WhiteToYellowDrawer',
  components: {
    HtVxeTable,
  },
  props: {
    idWo: {
      type: String,
      required: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: Number,
      default: 0,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)
    const tableState = reactive({
      data: null,
      columns: [
        {
          title: '证书条码',
          field: 'str_bcode_cert',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件条码',
          field: 'str_bcode',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'WO',
          field: 'str_wo',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'ESN',
          field: 'str_esn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '机型',
          field: 'str_engine_type',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'Flow',
          field: 'str_flow',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'SM',
          field: 'str_sm',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },{
          title: 'offlog_id',
          field: 'id_offlog',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        }, 
        {
          title: '件号',
          field: 'str_pn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        }, {
          title: '零件名称',
          field: 'str_part_name',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
         {
          title: '零件标签',
          field: 'is_result_main_dispname',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
         {
          title: '数量',
          field: 'int_qty',
          minWidth: 100
        },
      ],
    })
    const title = computed(() => {
      return props.type === 200 ? '待转PNR' : props.type === 300 ? '已转PNR' : props.type === 400 ? '待点灯扫描' : ''
    })
    const getTableData = async () => {
      const { data } = await getWhiteToYellowData(props.idWo, props.type)
      if (data.code === 'success') {
        tableState.data = data.data
        tableState.total = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    const tableRef = ref(null)
    const exportTableData = () => {
      tableRef.value.exportData()
    }

    const jumpToWhiteToYellow = (type) => {
      const selectedData = tableRef.value.getSelectedData()
      if (selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }
      const idList = selectedData.map((item) => item.id).join(',')
      const moduleMap = {
        200: '1849024676945350656',
        300: '1435767004610564097',
        400: '1435767004610564097',
      }
      const moduleTitleMap = {
        200: '待转PNR',
        300: '已转PNR',
        400: '待点灯扫描',
      }
      const url = `/Page/?moduleid=${moduleMap[type]}&qrc_id=${idList}`
      com.refreshTab(moduleTitleMap[type], url)
    }

    onMounted(() => {
      getTableData()
    })

    return {
      visible,
      title,
      tableState,
      tableRef,
      getTableData,
      exportTableData,
      jumpToWhiteToYellow,
    }
  },
  template: /*html*/ `
    <el-drawer
      class="my_drawer"
      v-model="visible"
      size="80%"
      :show-close="false"
      destroy-on-close
      :modal="false"
      :z-index="6"
    >
      <template #title>
        <div class="flex items-center justify-between">
          <div class="text-white">{{ title }}零件清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <el-button type="primary" @click="jumpToWhiteToYellow(type)">{{ title }}跳转</el-button>
          <el-button type="primary" @click="exportTableData">导出</el-button>
        </div>
        <div class="flex items-center">
          <span>总数：{{ tableState.total }}条</span>
        </div>
      </div>
      <div style="height: calc(100% - 50px)">
        <HtVxeTable :table-data="tableState.data" :table-columns="tableState.columns">
          <template #checkbox>
            <vxe-column type="checkbox" width="50" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
  `,
}
