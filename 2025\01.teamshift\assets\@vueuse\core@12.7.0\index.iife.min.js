(function(R,S,n){"use strict";function Pe(e,t,o){let l;n.isRef(o)?l={evaluating:o}:l=o||{};const{lazy:r=!1,evaluating:i=void 0,shallow:s=!0,onError:a=S.noop}=l,u=n.shallowRef(!r),f=s?n.shallowRef(t):n.ref(t);let c=0;return n.watchEffect(async d=>{if(!u.value)return;c++;const h=c;let m=!1;i&&Promise.resolve().then(()=>{i.value=!0});try{const v=await e(b=>{d(()=>{i&&(i.value=!1),m||b()})});h===c&&(f.value=v)}catch(v){a(v)}finally{i&&h===c&&(i.value=!1),m=!0}}),r?n.computed(()=>(u.value=!0,f.value)):f}function Ot(e,t,o,l){let r=n.inject(e);return o&&(r=n.inject(e,o)),l&&(r=n.inject(e,o,l)),typeof t=="function"?n.computed(i=>t(r,i)):n.computed({get:i=>t.get(r,i),set:t.set})}function kt(e={}){const{inheritAttrs:t=!0}=e,o=n.shallowRef(),l=n.defineComponent({setup(i,{slots:s}){return()=>{o.value=s.default}}}),r=n.defineComponent({inheritAttrs:t,props:e.props,setup(i,{attrs:s,slots:a}){return()=>{var u;if(!o.value&&process.env.NODE_ENV!=="production")throw new Error("[VueUse] Failed to find the definition of reusable template");const f=(u=o.value)==null?void 0:u.call(o,{...e.props==null?_t(s):i,$slots:a});return t&&f?.length===1?f[0]:f}}});return S.makeDestructurable({define:l,reuse:r},[l,r])}function _t(e){const t={};for(const o in e)t[S.camelize(o)]=e[o];return t}function Vt(e={}){let t=0;const o=n.ref([]);function l(...s){const a=n.shallowReactive({key:t++,args:s,promise:void 0,resolve:()=>{},reject:()=>{},isResolving:!1,options:e});return o.value.push(a),a.promise=new Promise((u,f)=>{a.resolve=c=>(a.isResolving=!0,u(c)),a.reject=f}).finally(()=>{a.promise=void 0;const u=o.value.indexOf(a);u!==-1&&o.value.splice(u,1)}),a.promise}function r(...s){return e.singleton&&o.value.length>0?o.value[0].promise:l(...s)}const i=n.defineComponent((s,{slots:a})=>{const u=()=>o.value.map(f=>{var c;return n.h(n.Fragment,{key:f.key},(c=a.default)==null?void 0:c.call(a,f))});return e.transition?()=>n.h(n.TransitionGroup,e.transition,u):u});return i.start=r,i}function Ft(e){return function(...t){return e.apply(this,t.map(o=>n.toValue(o)))}}const A=S.isClient?window:void 0,j=S.isClient?window.document:void 0,q=S.isClient?window.navigator:void 0,Pt=S.isClient?window.location:void 0;function N(e){var t;const o=n.toValue(e);return(t=o?.$el)!=null?t:o}function O(...e){const t=[],o=()=>{t.forEach(a=>a()),t.length=0},l=(a,u,f,c)=>(a.addEventListener(u,f,c),()=>a.removeEventListener(u,f,c)),r=n.computed(()=>{const a=S.toArray(n.toValue(e[0])).filter(u=>u!=null);return a.every(u=>typeof u!="string")?a:void 0}),i=S.watchImmediate(()=>{var a,u;return[(u=(a=r.value)==null?void 0:a.map(f=>N(f)))!=null?u:[A].filter(f=>f!=null),S.toArray(n.toValue(r.value?e[1]:e[0])),S.toArray(n.unref(r.value?e[2]:e[1])),n.toValue(r.value?e[3]:e[2])]},([a,u,f,c])=>{if(o(),!a?.length||!u?.length||!f?.length)return;const d=S.isObject(c)?{...c}:c;t.push(...a.flatMap(h=>u.flatMap(m=>f.map(v=>l(h,m,v,d)))))},{flush:"post"}),s=()=>{i(),o()};return S.tryOnScopeDispose(o),s}let Ce=!1;function Ct(e,t,o={}){const{window:l=A,ignore:r=[],capture:i=!0,detectIframe:s=!1,controls:a=!1}=o;if(!l)return a?{stop:S.noop,cancel:S.noop,trigger:S.noop}:S.noop;if(S.isIOS&&!Ce){Ce=!0;const p={passive:!0};Array.from(l.document.body.children).forEach(w=>O(w,"click",S.noop,p)),O(l.document.documentElement,"click",S.noop,p)}let u=!0;const f=p=>n.toValue(r).some(w=>{if(typeof w=="string")return Array.from(l.document.querySelectorAll(w)).some(y=>y===p.target||p.composedPath().includes(y));{const y=N(w);return y&&(p.target===y||p.composedPath().includes(y))}});function c(p){const w=n.toValue(p);return w&&w.$.subTree.shapeFlag===16}function d(p,w){const y=n.toValue(p),g=y.$.subTree&&y.$.subTree.children;return g==null||!Array.isArray(g)?!1:g.some(T=>T.el===w.target||w.composedPath().includes(T.el))}const h=p=>{const w=N(e);if(p.target!=null&&!(!(w instanceof Element)&&c(e)&&d(e,p))&&!(!w||w===p.target||p.composedPath().includes(w))){if("detail"in p&&p.detail===0&&(u=!f(p)),!u){u=!0;return}t(p)}};let m=!1;const v=[O(l,"click",p=>{m||(m=!0,setTimeout(()=>{m=!1},0),h(p))},{passive:!0,capture:i}),O(l,"pointerdown",p=>{const w=N(e);u=!f(p)&&!!(w&&!p.composedPath().includes(w))},{passive:!0}),s&&O(l,"blur",p=>{setTimeout(()=>{var w;const y=N(e);((w=l.document.activeElement)==null?void 0:w.tagName)==="IFRAME"&&!y?.contains(l.document.activeElement)&&t(p)},0)},{passive:!0})].filter(Boolean),b=()=>v.forEach(p=>p());return a?{stop:b,cancel:()=>{u=!1},trigger:p=>{u=!0,h(p),u=!1}}:b}function De(){const e=n.shallowRef(!1),t=n.getCurrentInstance();return t&&n.onMounted(()=>{e.value=!0},t),e}function x(e){const t=De();return n.computed(()=>(t.value,!!e()))}function J(e,t,o={}){const{window:l=A,...r}=o;let i;const s=x(()=>l&&"MutationObserver"in l),a=()=>{i&&(i.disconnect(),i=void 0)},u=n.computed(()=>{const h=n.toValue(e),m=S.toArray(h).map(N).filter(S.notNullish);return new Set(m)}),f=n.watch(()=>u.value,h=>{a(),s.value&&h.size&&(i=new MutationObserver(t),h.forEach(m=>i.observe(m,r)))},{immediate:!0,flush:"post"}),c=()=>i?.takeRecords(),d=()=>{f(),a()};return S.tryOnScopeDispose(d),{isSupported:s,stop:d,takeRecords:c}}function ye(e,t,o={}){const{window:l=A,document:r=l?.document,flush:i="sync"}=o;if(!l||!r)return S.noop;let s;const a=c=>{s?.(),s=c},u=n.watchEffect(()=>{const c=N(e);if(c){const{stop:d}=J(r,h=>{h.map(v=>[...v.removedNodes]).flat().some(v=>v===c||v.contains(c))&&t(h)},{window:l,childList:!0,subtree:!0});a(d)}},{flush:i}),f=()=>{u(),a()};return S.tryOnScopeDispose(f),f}function Dt(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function re(...e){let t,o,l={};e.length===3?(t=e[0],o=e[1],l=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,o=e[0],l=e[1]):(t=e[0],o=e[1]):(t=!0,o=e[0]);const{target:r=A,eventName:i="keydown",passive:s=!1,dedupe:a=!1}=l,u=Dt(t);return O(r,i,c=>{c.repeat&&n.toValue(a)||u(c)&&o(c)},s)}function At(e,t,o={}){return re(e,t,{...o,eventName:"keydown"})}function Mt(e,t,o={}){return re(e,t,{...o,eventName:"keypress"})}function It(e,t,o={}){return re(e,t,{...o,eventName:"keyup"})}const Lt=500,Nt=10;function xt(e,t,o){var l,r;const i=n.computed(()=>N(e));let s,a,u,f=!1;function c(){s&&(clearTimeout(s),s=void 0),a=void 0,u=void 0,f=!1}function d(w){var y,g,T;const[k,C,E]=[u,a,f];if(c(),!o?.onMouseUp||!C||!k||(y=o?.modifiers)!=null&&y.self&&w.target!==i.value)return;(g=o?.modifiers)!=null&&g.prevent&&w.preventDefault(),(T=o?.modifiers)!=null&&T.stop&&w.stopPropagation();const V=w.x-C.x,P=w.y-C.y,I=Math.sqrt(V*V+P*P);o.onMouseUp(w.timeStamp-k,I,E)}function h(w){var y,g,T,k;(y=o?.modifiers)!=null&&y.self&&w.target!==i.value||(c(),(g=o?.modifiers)!=null&&g.prevent&&w.preventDefault(),(T=o?.modifiers)!=null&&T.stop&&w.stopPropagation(),a={x:w.x,y:w.y},u=w.timeStamp,s=setTimeout(()=>{f=!0,t(w)},(k=o?.delay)!=null?k:Lt))}function m(w){var y,g,T,k;if((y=o?.modifiers)!=null&&y.self&&w.target!==i.value||!a||o?.distanceThreshold===!1)return;(g=o?.modifiers)!=null&&g.prevent&&w.preventDefault(),(T=o?.modifiers)!=null&&T.stop&&w.stopPropagation();const C=w.x-a.x,E=w.y-a.y;Math.sqrt(C*C+E*E)>=((k=o?.distanceThreshold)!=null?k:Nt)&&c()}const v={capture:(l=o?.modifiers)==null?void 0:l.capture,once:(r=o?.modifiers)==null?void 0:r.once},b=[O(i,"pointerdown",h,v),O(i,"pointermove",m,v),O(i,["pointerup","pointerleave"],d,v)];return()=>b.forEach(w=>w())}function Wt(){const{activeElement:e,body:t}=document;if(!e||e===t)return!1;switch(e.tagName){case"INPUT":case"TEXTAREA":return!0}return e.hasAttribute("contenteditable")}function Ht({keyCode:e,metaKey:t,ctrlKey:o,altKey:l}){return t||o||l?!1:e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122}function Ut(e,t={}){const{document:o=j}=t;o&&O(o,"keydown",r=>{!Wt()&&Ht(r)&&e(r)},{passive:!0})}function $t(e,t=null){const o=n.getCurrentInstance();let l=()=>{};const r=n.customRef((i,s)=>(l=s,{get(){var a,u;return i(),(u=(a=o?.proxy)==null?void 0:a.$refs[e])!=null?u:t},set(){}}));return S.tryOnMounted(l),n.onUpdated(l),r}function Ae(e={}){var t;const{window:o=A,deep:l=!0,triggerOnRemoval:r=!1}=e,i=(t=e.document)!=null?t:o?.document,s=()=>{var f;let c=i?.activeElement;if(l)for(;c?.shadowRoot;)c=(f=c?.shadowRoot)==null?void 0:f.activeElement;return c},a=n.ref(),u=()=>{a.value=s()};if(o){const f={capture:!0,passive:!0};O(o,"blur",c=>{c.relatedTarget===null&&u()},f),O(o,"focus",u,f)}return r&&ye(a,u,{document:i}),u(),a}function Y(e,t={}){const{immediate:o=!0,fpsLimit:l=void 0,window:r=A,once:i=!1}=t,s=n.shallowRef(!1),a=n.computed(()=>l?1e3/n.toValue(l):null);let u=0,f=null;function c(m){if(!s.value||!r)return;u||(u=m);const v=m-u;if(a.value&&v<a.value){f=r.requestAnimationFrame(c);return}if(u=m,e({delta:v,timestamp:m}),i){s.value=!1,f=null;return}f=r.requestAnimationFrame(c)}function d(){!s.value&&r&&(s.value=!0,u=0,f=r.requestAnimationFrame(c))}function h(){s.value=!1,f!=null&&r&&(r.cancelAnimationFrame(f),f=null)}return o&&d(),S.tryOnScopeDispose(h),{isActive:n.readonly(s),pause:h,resume:d}}function Bt(e,t,o){let l,r;S.isObject(o)?(l=o,r=S.objectOmit(o,["window","immediate","commitStyles","persist","onReady","onError"])):(l={duration:o},r=o);const{window:i=A,immediate:s=!0,commitStyles:a,persist:u,playbackRate:f=1,onReady:c,onError:d=M=>{console.error(M)}}=l,h=x(()=>i&&HTMLElement&&"animate"in HTMLElement.prototype),m=n.shallowRef(void 0),v=n.shallowReactive({startTime:null,currentTime:null,timeline:null,playbackRate:f,pending:!1,playState:s?"idle":"paused",replaceState:"active"}),b=n.computed(()=>v.pending),p=n.computed(()=>v.playState),w=n.computed(()=>v.replaceState),y=n.computed({get(){return v.startTime},set(M){v.startTime=M,m.value&&(m.value.startTime=M)}}),g=n.computed({get(){return v.currentTime},set(M){v.currentTime=M,m.value&&(m.value.currentTime=M,H())}}),T=n.computed({get(){return v.timeline},set(M){v.timeline=M,m.value&&(m.value.timeline=M)}}),k=n.computed({get(){return v.playbackRate},set(M){v.playbackRate=M,m.value&&(m.value.playbackRate=M)}}),C=()=>{if(m.value)try{m.value.play(),H()}catch(M){W(),d(M)}else D()},E=()=>{var M;try{(M=m.value)==null||M.pause(),W()}catch(U){d(U)}},V=()=>{var M;m.value||D();try{(M=m.value)==null||M.reverse(),H()}catch(U){W(),d(U)}},P=()=>{var M;try{(M=m.value)==null||M.finish(),W()}catch(U){d(U)}},I=()=>{var M;try{(M=m.value)==null||M.cancel(),W()}catch(U){d(U)}};n.watch(()=>N(e),M=>{M?D():m.value=void 0}),n.watch(()=>t,M=>{m.value&&D(),!N(e)&&m.value&&(m.value.effect=new KeyframeEffect(N(e),n.toValue(M),r))},{deep:!0}),S.tryOnMounted(()=>D(!0),!1),S.tryOnScopeDispose(I);function D(M){const U=N(e);!h.value||!U||(m.value||(m.value=U.animate(n.toValue(t),r)),u&&m.value.persist(),f!==1&&(m.value.playbackRate=f),M&&!s?m.value.pause():H(),c?.(m.value))}const _={passive:!0};O(m,["cancel","finish","remove"],W,_),O(m,"finish",()=>{var M;a&&((M=m.value)==null||M.commitStyles())},_);const{resume:F,pause:L}=Y(()=>{m.value&&(v.pending=m.value.pending,v.playState=m.value.playState,v.replaceState=m.value.replaceState,v.startTime=m.value.startTime,v.currentTime=m.value.currentTime,v.timeline=m.value.timeline,v.playbackRate=m.value.playbackRate)},{immediate:!1});function H(){h.value&&F()}function W(){h.value&&i&&i.requestAnimationFrame(L)}return{isSupported:h,animate:m,play:C,pause:E,reverse:V,finish:P,cancel:I,pending:b,playState:p,replaceState:w,startTime:y,currentTime:g,timeline:T,playbackRate:k}}function jt(e,t){const{interrupt:o=!0,onError:l=S.noop,onFinished:r=S.noop,signal:i}=t||{},s={aborted:"aborted",fulfilled:"fulfilled",pending:"pending",rejected:"rejected"},a=Array.from(Array.from({length:e.length}),()=>({state:s.pending,data:null})),u=n.reactive(a),f=n.shallowRef(-1);if(!e||e.length===0)return r(),{activeIndex:f,result:u};function c(d,h){f.value++,u[f.value].data=h,u[f.value].state=d}return e.reduce((d,h)=>d.then(m=>{var v;if(i?.aborted){c(s.aborted,new Error("aborted"));return}if(((v=u[f.value])==null?void 0:v.state)===s.rejected&&o){r();return}const b=h(m).then(p=>(c(s.fulfilled,p),f.value===e.length-1&&r(),p));return i?Promise.race([b,zt(i)]):b}).catch(m=>i?.aborted?(c(s.aborted,m),m):(c(s.rejected,m),l(),m)),Promise.resolve()),{activeIndex:f,result:u}}function zt(e){return new Promise((t,o)=>{const l=new Error("aborted");e.aborted?o(l):e.addEventListener("abort",()=>o(l),{once:!0})})}function Me(e,t,o){const{immediate:l=!0,delay:r=0,onError:i=S.noop,onSuccess:s=S.noop,resetOnExecute:a=!0,shallow:u=!0,throwError:f}=o??{},c=u?n.shallowRef(t):n.ref(t),d=n.shallowRef(!1),h=n.shallowRef(!1),m=n.shallowRef(void 0);async function v(w=0,...y){a&&(c.value=t),m.value=void 0,d.value=!1,h.value=!0,w>0&&await S.promiseTimeout(w);const g=typeof e=="function"?e(...y):e;try{const T=await g;c.value=T,d.value=!0,s(T)}catch(T){if(m.value=T,i(T),f)throw T}finally{h.value=!1}return c.value}l&&v(r);const b={state:c,isReady:d,isLoading:h,error:m,execute:v};function p(){return new Promise((w,y)=>{S.until(h).toBe(!1).then(()=>w(b)).catch(y)})}return{...b,then(w,y){return p().then(w,y)}}}const ee={array:e=>JSON.stringify(e),object:e=>JSON.stringify(e),set:e=>JSON.stringify(Array.from(e)),map:e=>JSON.stringify(Object.fromEntries(e)),null:()=>""};function qt(e){return e?e instanceof Map?ee.map:e instanceof Set?ee.set:Array.isArray(e)?ee.array:ee.object:ee.null}function Gt(e,t){const o=n.shallowRef(""),l=n.shallowRef();function r(){if(S.isClient)return l.value=new Promise((i,s)=>{try{const a=n.toValue(e);if(a==null)i("");else if(typeof a=="string")i(we(new Blob([a],{type:"text/plain"})));else if(a instanceof Blob)i(we(a));else if(a instanceof ArrayBuffer)i(window.btoa(String.fromCharCode(...new Uint8Array(a))));else if(a instanceof HTMLCanvasElement)i(a.toDataURL(t?.type,t?.quality));else if(a instanceof HTMLImageElement){const u=a.cloneNode(!1);u.crossOrigin="Anonymous",Yt(u).then(()=>{const f=document.createElement("canvas"),c=f.getContext("2d");f.width=u.width,f.height=u.height,c.drawImage(u,0,0,f.width,f.height),i(f.toDataURL(t?.type,t?.quality))}).catch(s)}else if(typeof a=="object"){const f=(t?.serializer||qt(a))(a);return i(we(new Blob([f],{type:"application/json"})))}else s(new Error("target is unsupported types"))}catch(a){s(a)}}),l.value.then(i=>{o.value=t?.dataUrl===!1?i.replace(/^data:.*?;base64,/,""):i}),l.value}return n.isRef(e)||typeof e=="function"?n.watch(e,r,{immediate:!0}):r(),{base64:o,promise:l,execute:r}}function Yt(e){return new Promise((t,o)=>{e.complete?t():(e.onload=()=>{t()},e.onerror=o)})}function we(e){return new Promise((t,o)=>{const l=new FileReader;l.onload=r=>{t(r.target.result)},l.onerror=o,l.readAsDataURL(e)})}function Xt(e={}){const{navigator:t=q}=e,o=["chargingchange","chargingtimechange","dischargingtimechange","levelchange"],l=x(()=>t&&"getBattery"in t&&typeof t.getBattery=="function"),r=n.shallowRef(!1),i=n.shallowRef(0),s=n.shallowRef(0),a=n.shallowRef(1);let u;function f(){r.value=this.charging,i.value=this.chargingTime||0,s.value=this.dischargingTime||0,a.value=this.level}return l.value&&t.getBattery().then(c=>{u=c,f.call(u),O(u,o,f,{passive:!0})}),{isSupported:l,charging:r,chargingTime:i,dischargingTime:s,level:a}}function Kt(e){let{acceptAllDevices:t=!1}=e||{};const{filters:o=void 0,optionalServices:l=void 0,navigator:r=q}=e||{},i=x(()=>r&&"bluetooth"in r),s=n.shallowRef(),a=n.shallowRef(null);n.watch(s,()=>{h()});async function u(){if(i.value){a.value=null,o&&o.length>0&&(t=!1);try{s.value=await r?.bluetooth.requestDevice({acceptAllDevices:t,filters:o,optionalServices:l})}catch(m){a.value=m}}}const f=n.shallowRef(),c=n.shallowRef(!1);function d(){c.value=!1,s.value=void 0,f.value=void 0}async function h(){if(a.value=null,s.value&&s.value.gatt){O(s,"gattserverdisconnected",d,{passive:!0});try{f.value=await s.value.gatt.connect(),c.value=f.value.connected}catch(m){a.value=m}}}return S.tryOnMounted(()=>{var m;s.value&&((m=s.value.gatt)==null||m.connect())}),S.tryOnScopeDispose(()=>{var m;s.value&&((m=s.value.gatt)==null||m.disconnect())}),{isSupported:i,isConnected:n.readonly(c),device:s,requestDevice:u,server:f,error:a}}const ge=Symbol("vueuse-ssr-width");function be(){const e=n.hasInjectionContext()?S.injectLocal(ge,null):null;return typeof e=="number"?e:void 0}function Jt(e,t){t!==void 0?t.provide(ge,e):S.provideLocal(ge,e)}function $(e,t={}){const{window:o=A,ssrWidth:l=be()}=t,r=x(()=>o&&"matchMedia"in o&&typeof o.matchMedia=="function"),i=n.ref(typeof l=="number"),s=n.shallowRef(),a=n.shallowRef(!1),u=f=>{a.value=f.matches};return n.watchEffect(()=>{if(i.value){i.value=!r.value;const f=n.toValue(e).split(",");a.value=f.some(c=>{const d=c.includes("not all"),h=c.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),m=c.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let v=!!(h||m);return h&&v&&(v=l>=S.pxValue(h[1])),m&&v&&(v=l<=S.pxValue(m[1])),d?!v:v});return}r.value&&(s.value=o.matchMedia(n.toValue(e)),a.value=s.value.matches)}),O(s,"change",u,{passive:!0}),n.computed(()=>a.value)}const Qt={sm:640,md:768,lg:1024,xl:1280,"2xl":1536},Zt={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400},Ie={xs:0,sm:600,md:960,lg:1264,xl:1904},en={xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560},tn=Ie,nn={xs:480,sm:576,md:768,lg:992,xl:1200,xxl:1600},on={xs:0,sm:600,md:1024,lg:1440,xl:1920},ln={mobileS:320,mobileM:375,mobileL:425,tablet:768,laptop:1024,laptopL:1440,desktop4K:2560},an={"3xs":360,"2xs":480,xs:600,sm:768,md:1024,lg:1280,xl:1440,"2xl":1600,"3xl":1920,"4xl":2560},rn={sm:576,md:768,lg:992,xl:1200},sn={xs:0,sm:768,md:992,lg:1200,xl:1920};function un(e,t={}){function o(m,v){let b=n.toValue(e[n.toValue(m)]);return v!=null&&(b=S.increaseWithUnit(b,v)),typeof b=="number"&&(b=`${b}px`),b}const{window:l=A,strategy:r="min-width",ssrWidth:i=be()}=t,s=typeof i=="number",a=s?n.shallowRef(!1):{value:!0};s&&S.tryOnMounted(()=>a.value=!!l);function u(m,v){return!a.value&&s?m==="min"?i>=S.pxValue(v):i<=S.pxValue(v):l?l.matchMedia(`(${m}-width: ${v})`).matches:!1}const f=m=>$(()=>`(min-width: ${o(m)})`,t),c=m=>$(()=>`(max-width: ${o(m)})`,t),d=Object.keys(e).reduce((m,v)=>(Object.defineProperty(m,v,{get:()=>r==="min-width"?f(v):c(v),enumerable:!0,configurable:!0}),m),{});function h(){const m=Object.keys(e).map(v=>[v,d[v],S.pxValue(o(v))]).sort((v,b)=>v[2]-b[2]);return n.computed(()=>m.filter(([,v])=>v.value).map(([v])=>v))}return Object.assign(d,{greaterOrEqual:f,smallerOrEqual:c,greater(m){return $(()=>`(min-width: ${o(m,.1)})`,t)},smaller(m){return $(()=>`(max-width: ${o(m,-.1)})`,t)},between(m,v){return $(()=>`(min-width: ${o(m)}) and (max-width: ${o(v,-.1)})`,t)},isGreater(m){return u("min",o(m,.1))},isGreaterOrEqual(m){return u("min",o(m))},isSmaller(m){return u("max",o(m,-.1))},isSmallerOrEqual(m){return u("max",o(m))},isInBetween(m,v){return u("min",o(m))&&u("max",o(v,-.1))},current:h,active(){const m=h();return n.computed(()=>m.value.length===0?"":m.value.at(r==="min-width"?-1:0))}})}function cn(e){const{name:t,window:o=A}=e,l=x(()=>o&&"BroadcastChannel"in o),r=n.shallowRef(!1),i=n.ref(),s=n.ref(),a=n.shallowRef(null),u=c=>{i.value&&i.value.postMessage(c)},f=()=>{i.value&&i.value.close(),r.value=!0};return l.value&&S.tryOnMounted(()=>{a.value=null,i.value=new BroadcastChannel(t);const c={passive:!0};O(i,"message",d=>{s.value=d.data},c),O(i,"messageerror",d=>{a.value=d},c),O(i,"close",()=>{r.value=!0},c)}),S.tryOnScopeDispose(()=>{f()}),{isSupported:l,channel:i,data:s,post:u,close:f,error:a,isClosed:r}}const Le=["hash","host","hostname","href","pathname","port","protocol","search"];function fn(e={}){const{window:t=A}=e,o=Object.fromEntries(Le.map(i=>[i,n.ref()]));for(const[i,s]of S.objectEntries(o))n.watch(s,a=>{!t?.location||t.location[i]===a||(t.location[i]=a)});const l=i=>{var s;const{state:a,length:u}=t?.history||{},{origin:f}=t?.location||{};for(const c of Le)o[c].value=(s=t?.location)==null?void 0:s[c];return n.reactive({trigger:i,state:a,length:u,origin:f,...o})},r=n.ref(l("load"));if(t){const i={passive:!0};O(t,"popstate",()=>r.value=l("popstate"),i),O(t,"hashchange",()=>r.value=l("hashchange"),i)}return r}function dn(e,t=(l,r)=>l===r,o){const l=n.ref(e.value);return n.watch(()=>e.value,r=>{t(r,l.value)||(l.value=r)},o),l}function ie(e,t={}){const{controls:o=!1,navigator:l=q}=t,r=x(()=>l&&"permissions"in l),i=n.shallowRef(),s=typeof e=="string"?{name:e}:e,a=n.shallowRef(),u=()=>{var c,d;a.value=(d=(c=i.value)==null?void 0:c.state)!=null?d:"prompt"};O(i,"change",u,{passive:!0});const f=S.createSingletonPromise(async()=>{if(r.value){if(!i.value)try{i.value=await l.permissions.query(s)}catch{i.value=void 0}finally{u()}if(o)return n.toRaw(i.value)}});return f(),o?{state:a,isSupported:r,query:f}:a}function mn(e={}){const{navigator:t=q,read:o=!1,source:l,copiedDuring:r=1500,legacy:i=!1}=e,s=x(()=>t&&"clipboard"in t),a=ie("clipboard-read"),u=ie("clipboard-write"),f=n.computed(()=>s.value||i),c=n.shallowRef(""),d=n.shallowRef(!1),h=S.useTimeoutFn(()=>d.value=!1,r,{immediate:!1});function m(){let y=!(s.value&&w(a.value));if(!y)try{t.clipboard.readText().then(g=>{c.value=g})}catch{y=!0}y&&(c.value=p())}f.value&&o&&O(["copy","cut"],m,{passive:!0});async function v(y=n.toValue(l)){if(f.value&&y!=null){let g=!(s.value&&w(u.value));if(!g)try{await t.clipboard.writeText(y)}catch{g=!0}g&&b(y),c.value=y,d.value=!0,h.start()}}function b(y){const g=document.createElement("textarea");g.value=y??"",g.style.position="absolute",g.style.opacity="0",document.body.appendChild(g),g.select(),document.execCommand("copy"),g.remove()}function p(){var y,g,T;return(T=(g=(y=document?.getSelection)==null?void 0:y.call(document))==null?void 0:g.toString())!=null?T:""}function w(y){return y==="granted"||y==="prompt"}return{isSupported:f,text:c,copied:d,copy:v}}function vn(e={}){const{navigator:t=q,read:o=!1,source:l,copiedDuring:r=1500}=e,i=x(()=>t&&"clipboard"in t),s=n.ref([]),a=n.shallowRef(!1),u=S.useTimeoutFn(()=>a.value=!1,r,{immediate:!1});function f(){i.value&&t.clipboard.read().then(d=>{s.value=d})}i.value&&o&&O(["copy","cut"],f,{passive:!0});async function c(d=n.toValue(l)){i.value&&d!=null&&(await t.clipboard.write(d),s.value=d,a.value=!0,u.start())}return{isSupported:i,content:s,copied:a,copy:c}}function te(e){return JSON.parse(JSON.stringify(e))}function pn(e,t={}){const o=n.ref({}),l=n.ref(!1);let r=!1;const{manual:i,clone:s=te,deep:a=!0,immediate:u=!0}=t;n.watch(o,()=>{if(r){r=!1;return}l.value=!0},{deep:!0,flush:"sync"});function f(){r=!0,l.value=!1,o.value=s(n.toValue(e))}return!i&&(n.isRef(e)||typeof e=="function")?n.watch(e,f,{...t,deep:a,immediate:u}):f(),{cloned:o,isModified:l,sync:f}}const se=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ue="__vueuse_ssr_handlers__",Ne=hn();function hn(){return ue in se||(se[ue]=se[ue]||{}),se[ue]}function ce(e,t){return Ne[e]||t}function yn(e,t){Ne[e]=t}function xe(e){return $("(prefers-color-scheme: dark)",e)}function We(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Se={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Re="vueuse-storage";function fe(e,t,o,l={}){var r;const{flush:i="pre",deep:s=!0,listenToStorageChanges:a=!0,writeDefaults:u=!0,mergeDefaults:f=!1,shallow:c,window:d=A,eventFilter:h,onError:m=D=>{console.error(D)},initOnMounted:v}=l,b=(c?n.shallowRef:n.ref)(typeof t=="function"?t():t),p=n.computed(()=>n.toValue(e));if(!o)try{o=ce("getDefaultStorage",()=>{var D;return(D=A)==null?void 0:D.localStorage})()}catch(D){m(D)}if(!o)return b;const w=n.toValue(t),y=We(w),g=(r=l.serializer)!=null?r:Se[y],{pause:T,resume:k}=S.pausableWatch(b,()=>E(b.value),{flush:i,deep:s,eventFilter:h});n.watch(p,()=>P(),{flush:i}),d&&a&&S.tryOnMounted(()=>{o instanceof Storage?O(d,"storage",P,{passive:!0}):O(d,Re,I),v&&P()}),v||P();function C(D,_){if(d){const F={key:p.value,oldValue:D,newValue:_,storageArea:o};d.dispatchEvent(o instanceof Storage?new StorageEvent("storage",F):new CustomEvent(Re,{detail:F}))}}function E(D){try{const _=o.getItem(p.value);if(D==null)C(_,null),o.removeItem(p.value);else{const F=g.write(D);_!==F&&(o.setItem(p.value,F),C(_,F))}}catch(_){m(_)}}function V(D){const _=D?D.newValue:o.getItem(p.value);if(_==null)return u&&w!=null&&o.setItem(p.value,g.write(w)),w;if(!D&&f){const F=g.read(_);return typeof f=="function"?f(F,w):y==="object"&&!Array.isArray(F)?{...w,...F}:F}else return typeof _!="string"?_:g.read(_)}function P(D){if(!(D&&D.storageArea!==o)){if(D&&D.key==null){b.value=w;return}if(!(D&&D.key!==p.value)){T();try{D?.newValue!==g.write(b.value)&&(b.value=V(D))}catch(_){m(_)}finally{D?n.nextTick(k):k()}}}}function I(D){P(D.detail)}return b}const wn="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function He(e={}){const{selector:t="html",attribute:o="class",initialValue:l="auto",window:r=A,storage:i,storageKey:s="vueuse-color-scheme",listenToStorageChanges:a=!0,storageRef:u,emitAuto:f,disableTransition:c=!0}=e,d={auto:"",light:"light",dark:"dark",...e.modes||{}},h=xe({window:r}),m=n.computed(()=>h.value?"dark":"light"),v=u||(s==null?S.toRef(l):fe(s,l,i,{window:r,listenToStorageChanges:a})),b=n.computed(()=>v.value==="auto"?m.value:v.value),p=ce("updateHTMLAttrs",(T,k,C)=>{const E=typeof T=="string"?r?.document.querySelector(T):N(T);if(!E)return;const V=new Set,P=new Set;let I=null;if(k==="class"){const _=C.split(/\s/g);Object.values(d).flatMap(F=>(F||"").split(/\s/g)).filter(Boolean).forEach(F=>{_.includes(F)?V.add(F):P.add(F)})}else I={key:k,value:C};if(V.size===0&&P.size===0&&I===null)return;let D;c&&(D=r.document.createElement("style"),D.appendChild(document.createTextNode(wn)),r.document.head.appendChild(D));for(const _ of V)E.classList.add(_);for(const _ of P)E.classList.remove(_);I&&E.setAttribute(I.key,I.value),c&&(r.getComputedStyle(D).opacity,document.head.removeChild(D))});function w(T){var k;p(t,o,(k=d[T])!=null?k:T)}function y(T){e.onChanged?e.onChanged(T,w):w(T)}n.watch(b,y,{flush:"post",immediate:!0}),S.tryOnMounted(()=>y(b.value));const g=n.computed({get(){return f?v.value:b.value},set(T){v.value=T}});return Object.assign(g,{store:v,system:m,state:b})}function gn(e=n.shallowRef(!1)){const t=S.createEventHook(),o=S.createEventHook(),l=S.createEventHook();let r=S.noop;const i=u=>(l.trigger(u),e.value=!0,new Promise(f=>{r=f})),s=u=>{e.value=!1,t.trigger(u),r({data:u,isCanceled:!1})},a=u=>{e.value=!1,o.trigger(u),r({data:u,isCanceled:!0})};return{isRevealed:n.computed(()=>e.value),reveal:i,confirm:s,cancel:a,onReveal:l.on,onConfirm:t.on,onCancel:o.on}}function bn(e,t){var o,l;const r=n.shallowRef(n.toValue(e)),i=S.useIntervalFn(()=>{var c,d;const h=r.value-1;r.value=h<0?0:h,(c=t?.onTick)==null||c.call(t),r.value<=0&&(i.pause(),(d=t?.onComplete)==null||d.call(t))},(o=t?.interval)!=null?o:1e3,{immediate:(l=t?.immediate)!=null?l:!1}),s=c=>{var d;r.value=(d=n.toValue(c))!=null?d:n.toValue(e)},a=()=>{i.pause(),s()},u=()=>{i.isActive.value||r.value>0&&i.resume()};return{remaining:r,reset:s,stop:a,start:c=>{s(c),i.resume()},pause:i.pause,resume:u,isActive:i.isActive}}function ne(e,t,o={}){const{window:l=A,initialValue:r,observe:i=!1}=o,s=n.shallowRef(r),a=n.computed(()=>{var f;return N(t)||((f=l?.document)==null?void 0:f.documentElement)});function u(){var f;const c=n.toValue(e),d=n.toValue(a);if(d&&l&&c){const h=(f=l.getComputedStyle(d).getPropertyValue(c))==null?void 0:f.trim();s.value=h||s.value||r}}return i&&J(a,u,{attributeFilter:["style","class"],window:l}),n.watch([a,()=>n.toValue(e)],(f,c)=>{c[0]&&c[1]&&c[0].style.removeProperty(c[1]),u()}),n.watch([s,a],([f,c])=>{const d=n.toValue(e);c?.style&&d&&(f==null?c.style.removeProperty(d):c.style.setProperty(d,f))},{immediate:!0}),s}function Ue(e){const t=n.getCurrentInstance(),o=S.computedWithControl(()=>null,()=>e?N(e):t.proxy.$el);return n.onUpdated(o.trigger),n.onMounted(o.trigger),o}function Sn(e,t){const o=n.shallowRef(f()),l=S.toRef(e),r=n.computed({get(){var c;const d=l.value;let h=t?.getIndexOf?t.getIndexOf(o.value,d):d.indexOf(o.value);return h<0&&(h=(c=t?.fallbackIndex)!=null?c:0),h},set(c){i(c)}});function i(c){const d=l.value,h=d.length,m=(c%h+h)%h,v=d[m];return o.value=v,v}function s(c=1){return i(r.value+c)}function a(c=1){return s(c)}function u(c=1){return s(-c)}function f(){var c,d;return(d=n.toValue((c=t?.initialValue)!=null?c:n.toValue(e)[0]))!=null?d:void 0}return n.watch(l,()=>i(r.value)),{state:o,index:r,next:a,prev:u,go:i}}function Rn(e={}){const{valueDark:t="dark",valueLight:o=""}=e,l=He({...e,onChanged:(s,a)=>{var u;e.onChanged?(u=e.onChanged)==null||u.call(e,s==="dark",a,s):a(s)},modes:{dark:t,light:o}}),r=n.computed(()=>l.system.value);return n.computed({get(){return l.value==="dark"},set(s){const a=s?"dark":"light";r.value===a?l.value="auto":l.value=a}})}function $e(e){return e}function En(e,t){return e.value=t}function Tn(e){return e?typeof e=="function"?e:te:$e}function On(e){return e?typeof e=="function"?e:te:$e}function Be(e,t={}){const{clone:o=!1,dump:l=Tn(o),parse:r=On(o),setSource:i=En}=t;function s(){return n.markRaw({snapshot:l(e.value),timestamp:S.timestamp()})}const a=n.ref(s()),u=n.ref([]),f=n.ref([]),c=g=>{i(e,r(g.snapshot)),a.value=g},d=()=>{u.value.unshift(a.value),a.value=s(),t.capacity&&u.value.length>t.capacity&&u.value.splice(t.capacity,Number.POSITIVE_INFINITY),f.value.length&&f.value.splice(0,f.value.length)},h=()=>{u.value.splice(0,u.value.length),f.value.splice(0,f.value.length)},m=()=>{const g=u.value.shift();g&&(f.value.unshift(a.value),c(g))},v=()=>{const g=f.value.shift();g&&(u.value.unshift(a.value),c(g))},b=()=>{c(a.value)},p=n.computed(()=>[a.value,...u.value]),w=n.computed(()=>u.value.length>0),y=n.computed(()=>f.value.length>0);return{source:e,undoStack:u,redoStack:f,last:a,history:p,canUndo:w,canRedo:y,clear:h,commit:d,reset:b,undo:m,redo:v}}function Ee(e,t={}){const{deep:o=!1,flush:l="pre",eventFilter:r}=t,{eventFilter:i,pause:s,resume:a,isActive:u}=S.pausableFilter(r),{ignoreUpdates:f,ignorePrevAsyncUpdates:c,stop:d}=S.watchIgnorable(e,p,{deep:o,flush:l,eventFilter:i});function h(T,k){c(),f(()=>{T.value=k})}const m=Be(e,{...t,clone:t.clone||o,setSource:h}),{clear:v,commit:b}=m;function p(){c(),b()}function w(T){a(),T&&p()}function y(T){let k=!1;const C=()=>k=!0;f(()=>{T(C)}),k||p()}function g(){d(),v()}return{...m,isTracking:u,pause:s,resume:w,commit:p,batch:y,dispose:g}}function kn(e,t={}){const o=t.debounce?S.debounceFilter(t.debounce):void 0;return{...Ee(e,{...t,eventFilter:o})}}function _n(e={}){const{window:t=A,requestPermissions:o=!1,eventFilter:l=S.bypassFilter}=e,r=x(()=>typeof DeviceMotionEvent<"u"),i=x(()=>r.value&&"requestPermission"in DeviceMotionEvent&&typeof DeviceMotionEvent.requestPermission=="function"),s=n.shallowRef(!1),a=n.ref({x:null,y:null,z:null}),u=n.ref({alpha:null,beta:null,gamma:null}),f=n.shallowRef(0),c=n.ref({x:null,y:null,z:null});function d(){if(t){const m=S.createFilterWrapper(l,v=>{var b,p,w,y,g,T,k,C,E;a.value={x:((b=v.acceleration)==null?void 0:b.x)||null,y:((p=v.acceleration)==null?void 0:p.y)||null,z:((w=v.acceleration)==null?void 0:w.z)||null},c.value={x:((y=v.accelerationIncludingGravity)==null?void 0:y.x)||null,y:((g=v.accelerationIncludingGravity)==null?void 0:g.y)||null,z:((T=v.accelerationIncludingGravity)==null?void 0:T.z)||null},u.value={alpha:((k=v.rotationRate)==null?void 0:k.alpha)||null,beta:((C=v.rotationRate)==null?void 0:C.beta)||null,gamma:((E=v.rotationRate)==null?void 0:E.gamma)||null},f.value=v.interval});O(t,"devicemotion",m,{passive:!0})}}const h=async()=>{if(i.value||(s.value=!0),!s.value&&i.value){const m=DeviceMotionEvent.requestPermission;try{await m()==="granted"&&(s.value=!0,d())}catch(v){console.error(v)}}};return r.value&&(o&&i.value?h().then(()=>d()):d()),{acceleration:a,accelerationIncludingGravity:c,rotationRate:u,interval:f,isSupported:r,requirePermissions:i,ensurePermissions:h,permissionGranted:s}}function je(e={}){const{window:t=A}=e,o=x(()=>t&&"DeviceOrientationEvent"in t),l=n.shallowRef(!1),r=n.shallowRef(null),i=n.shallowRef(null),s=n.shallowRef(null);return t&&o.value&&O(t,"deviceorientation",a=>{l.value=a.absolute,r.value=a.alpha,i.value=a.beta,s.value=a.gamma},{passive:!0}),{isSupported:o,isAbsolute:l,alpha:r,beta:i,gamma:s}}function Vn(e={}){const{window:t=A}=e,o=n.shallowRef(1),l=$(()=>`(resolution: ${o.value}dppx)`,e);let r=S.noop;return t&&(r=S.watchImmediate(l,()=>o.value=t.devicePixelRatio)),{pixelRatio:n.readonly(o),stop:r}}function Fn(e={}){const{navigator:t=q,requestPermissions:o=!1,constraints:l={audio:!0,video:!0},onUpdated:r}=e,i=n.ref([]),s=n.computed(()=>i.value.filter(v=>v.kind==="videoinput")),a=n.computed(()=>i.value.filter(v=>v.kind==="audioinput")),u=n.computed(()=>i.value.filter(v=>v.kind==="audiooutput")),f=x(()=>t&&t.mediaDevices&&t.mediaDevices.enumerateDevices),c=n.shallowRef(!1);let d;async function h(){f.value&&(i.value=await t.mediaDevices.enumerateDevices(),r?.(i.value),d&&(d.getTracks().forEach(v=>v.stop()),d=null))}async function m(){if(!f.value)return!1;if(c.value)return!0;const{state:v,query:b}=ie("camera",{controls:!0});if(await b(),v.value!=="granted"){let p=!0;try{d=await t.mediaDevices.getUserMedia(l)}catch{d=null,p=!1}h(),c.value=p}else c.value=!0;return c.value}return f.value&&(o&&m(),O(t.mediaDevices,"devicechange",h,{passive:!0}),h()),{devices:i,ensurePermissions:m,permissionGranted:c,videoInputs:s,audioInputs:a,audioOutputs:u,isSupported:f}}function Pn(e={}){var t;const o=n.shallowRef((t=e.enabled)!=null?t:!1),l=e.video,r=e.audio,{navigator:i=q}=e,s=x(()=>{var m;return(m=i?.mediaDevices)==null?void 0:m.getDisplayMedia}),a={audio:r,video:l},u=n.shallowRef();async function f(){var m;if(!(!s.value||u.value))return u.value=await i.mediaDevices.getDisplayMedia(a),(m=u.value)==null||m.getTracks().forEach(v=>O(v,"ended",d,{passive:!0})),u.value}async function c(){var m;(m=u.value)==null||m.getTracks().forEach(v=>v.stop()),u.value=void 0}function d(){c(),o.value=!1}async function h(){return await f(),u.value&&(o.value=!0),u.value}return n.watch(o,m=>{m?f():c()},{immediate:!0}),{isSupported:s,stream:u,start:h,stop:d,enabled:o}}function ze(e={}){const{document:t=j}=e;if(!t)return n.shallowRef("visible");const o=n.ref(t.visibilityState);return O(t,"visibilitychange",()=>{o.value=t.visibilityState},{passive:!0}),o}function Cn(e,t={}){var o;const{pointerTypes:l,preventDefault:r,stopPropagation:i,exact:s,onMove:a,onEnd:u,onStart:f,initialValue:c,axis:d="both",draggingElement:h=A,containerElement:m,handle:v=e,buttons:b=[0]}=t,p=n.ref((o=n.toValue(c))!=null?o:{x:0,y:0}),w=n.ref(),y=E=>l?l.includes(E.pointerType):!0,g=E=>{n.toValue(r)&&E.preventDefault(),n.toValue(i)&&E.stopPropagation()},T=E=>{var V;if(!n.toValue(b).includes(E.button)||n.toValue(t.disabled)||!y(E)||n.toValue(s)&&E.target!==n.toValue(e))return;const P=n.toValue(m),I=(V=P?.getBoundingClientRect)==null?void 0:V.call(P),D=n.toValue(e).getBoundingClientRect(),_={x:E.clientX-(P?D.left-I.left+P.scrollLeft:D.left),y:E.clientY-(P?D.top-I.top+P.scrollTop:D.top)};f?.(_,E)!==!1&&(w.value=_,g(E))},k=E=>{if(n.toValue(t.disabled)||!y(E)||!w.value)return;const V=n.toValue(m),P=n.toValue(e).getBoundingClientRect();let{x:I,y:D}=p.value;(d==="x"||d==="both")&&(I=E.clientX-w.value.x,V&&(I=Math.min(Math.max(0,I),V.scrollWidth-P.width))),(d==="y"||d==="both")&&(D=E.clientY-w.value.y,V&&(D=Math.min(Math.max(0,D),V.scrollHeight-P.height))),p.value={x:I,y:D},a?.(p.value,E),g(E)},C=E=>{n.toValue(t.disabled)||!y(E)||w.value&&(w.value=void 0,u?.(p.value,E),g(E))};if(S.isClient){const E=()=>{var V;return{capture:(V=t.capture)!=null?V:!0,passive:!n.toValue(r)}};O(v,"pointerdown",T,E),O(h,"pointermove",k,E),O(h,"pointerup",C,E)}return{...S.toRefs(p),position:p,isDragging:n.computed(()=>!!w.value),style:n.computed(()=>`left:${p.value.x}px;top:${p.value.y}px;`)}}function Dn(e,t={}){var o,l;const r=n.shallowRef(!1),i=n.shallowRef(null);let s=0,a=!0;if(S.isClient){const u=typeof t=="function"?{onDrop:t}:t,f=(o=u.multiple)!=null?o:!0,c=(l=u.preventDefaultForUnhandled)!=null?l:!1,d=p=>{var w,y;const g=Array.from((y=(w=p.dataTransfer)==null?void 0:w.files)!=null?y:[]);return g.length===0?null:f?g:[g[0]]},h=p=>{const w=n.unref(u.dataTypes);return typeof w=="function"?w(p):w?.length?p.length===0?!1:p.every(y=>w.some(g=>y.includes(g))):!0},m=p=>{const w=Array.from(p??[]).map(T=>T.type),y=h(w),g=f||p.length<=1;return y&&g},v=()=>/^(?:(?!chrome|android).)*safari/i.test(navigator.userAgent)&&!("chrome"in window),b=(p,w)=>{var y,g,T,k,C,E;const V=(y=p.dataTransfer)==null?void 0:y.items;if(a=(g=V&&m(V))!=null?g:!1,c&&p.preventDefault(),!v()&&!a){p.dataTransfer&&(p.dataTransfer.dropEffect="none");return}p.preventDefault(),p.dataTransfer&&(p.dataTransfer.dropEffect="copy");const P=d(p);switch(w){case"enter":s+=1,r.value=!0,(T=u.onEnter)==null||T.call(u,null,p);break;case"over":(k=u.onOver)==null||k.call(u,null,p);break;case"leave":s-=1,s===0&&(r.value=!1),(C=u.onLeave)==null||C.call(u,null,p);break;case"drop":s=0,r.value=!1,a&&(i.value=P,(E=u.onDrop)==null||E.call(u,P,p));break}};O(e,"dragenter",p=>b(p,"enter")),O(e,"dragover",p=>b(p,"over")),O(e,"dragleave",p=>b(p,"leave")),O(e,"drop",p=>b(p,"drop"))}return{files:i,isOverDropZone:r}}function de(e,t,o={}){const{window:l=A,...r}=o;let i;const s=x(()=>l&&"ResizeObserver"in l),a=()=>{i&&(i.disconnect(),i=void 0)},u=n.computed(()=>{const d=n.toValue(e);return Array.isArray(d)?d.map(h=>N(h)):[N(d)]}),f=n.watch(u,d=>{if(a(),s.value&&l){i=new ResizeObserver(t);for(const h of d)h&&i.observe(h,r)}},{immediate:!0,flush:"post"}),c=()=>{a(),f()};return S.tryOnScopeDispose(c),{isSupported:s,stop:c}}function An(e,t={}){const{reset:o=!0,windowResize:l=!0,windowScroll:r=!0,immediate:i=!0,updateTiming:s="sync"}=t,a=n.shallowRef(0),u=n.shallowRef(0),f=n.shallowRef(0),c=n.shallowRef(0),d=n.shallowRef(0),h=n.shallowRef(0),m=n.shallowRef(0),v=n.shallowRef(0);function b(){const w=N(e);if(!w){o&&(a.value=0,u.value=0,f.value=0,c.value=0,d.value=0,h.value=0,m.value=0,v.value=0);return}const y=w.getBoundingClientRect();a.value=y.height,u.value=y.bottom,f.value=y.left,c.value=y.right,d.value=y.top,h.value=y.width,m.value=y.x,v.value=y.y}function p(){s==="sync"?b():s==="next-frame"&&requestAnimationFrame(()=>b())}return de(e,p),n.watch(()=>N(e),w=>!w&&p()),J(e,p,{attributeFilter:["style","class"]}),r&&O("scroll",p,{capture:!0,passive:!0}),l&&O("resize",p,{passive:!0}),S.tryOnMounted(()=>{i&&p()}),{height:a,bottom:u,left:f,right:c,top:d,width:h,x:m,y:v,update:p}}function Mn(e){const{x:t,y:o,document:l=j,multiple:r,interval:i="requestAnimationFrame",immediate:s=!0}=e,a=x(()=>n.toValue(r)?l&&"elementsFromPoint"in l:l&&"elementFromPoint"in l),u=n.ref(null),f=()=>{var d,h;u.value=n.toValue(r)?(d=l?.elementsFromPoint(n.toValue(t),n.toValue(o)))!=null?d:[]:(h=l?.elementFromPoint(n.toValue(t),n.toValue(o)))!=null?h:null},c=i==="requestAnimationFrame"?Y(f,{immediate:s}):S.useIntervalFn(f,i,{immediate:s});return{isSupported:a,element:u,...c}}function In(e,t={}){const{delayEnter:o=0,delayLeave:l=0,triggerOnRemoval:r=!1,window:i=A}=t,s=n.shallowRef(!1);let a;const u=f=>{const c=f?o:l;a&&(clearTimeout(a),a=void 0),c?a=setTimeout(()=>s.value=f,c):s.value=f};return i&&(O(e,"mouseenter",()=>u(!0),{passive:!0}),O(e,"mouseleave",()=>u(!1),{passive:!0}),r&&ye(n.computed(()=>N(e)),()=>u(!1))),s}function qe(e,t={width:0,height:0},o={}){const{window:l=A,box:r="content-box"}=o,i=n.computed(()=>{var d,h;return(h=(d=N(e))==null?void 0:d.namespaceURI)==null?void 0:h.includes("svg")}),s=n.ref(t.width),a=n.ref(t.height),{stop:u}=de(e,([d])=>{const h=r==="border-box"?d.borderBoxSize:r==="content-box"?d.contentBoxSize:d.devicePixelContentBoxSize;if(l&&i.value){const m=N(e);if(m){const v=m.getBoundingClientRect();s.value=v.width,a.value=v.height}}else if(h){const m=S.toArray(h);s.value=m.reduce((v,{inlineSize:b})=>v+b,0),a.value=m.reduce((v,{blockSize:b})=>v+b,0)}else s.value=d.contentRect.width,a.value=d.contentRect.height},o);S.tryOnMounted(()=>{const d=N(e);d&&(s.value="offsetWidth"in d?d.offsetWidth:t.width,a.value="offsetHeight"in d?d.offsetHeight:t.height)});const f=n.watch(()=>N(e),d=>{s.value=d?t.width:0,a.value=d?t.height:0});function c(){u(),f()}return{width:s,height:a,stop:c}}function Ge(e,t,o={}){const{root:l,rootMargin:r="0px",threshold:i=0,window:s=A,immediate:a=!0}=o,u=x(()=>s&&"IntersectionObserver"in s),f=n.computed(()=>{const v=n.toValue(e);return S.toArray(v).map(N).filter(S.notNullish)});let c=S.noop;const d=n.ref(a),h=u.value?n.watch(()=>[f.value,N(l),d.value],([v,b])=>{if(c(),!d.value||!v.length)return;const p=new IntersectionObserver(t,{root:N(b),rootMargin:r,threshold:i});v.forEach(w=>w&&p.observe(w)),c=()=>{p.disconnect(),c=S.noop}},{immediate:a,flush:"post"}):S.noop,m=()=>{c(),h(),d.value=!1};return S.tryOnScopeDispose(m),{isSupported:u,isActive:d,pause(){c(),d.value=!1},resume(){d.value=!0},stop:m}}function Ye(e,t={}){const{window:o=A,scrollTarget:l,threshold:r=0,rootMargin:i,once:s=!1}=t,a=n.shallowRef(!1),{stop:u}=Ge(e,f=>{let c=a.value,d=0;for(const h of f)h.time>=d&&(d=h.time,c=h.isIntersecting);a.value=c,s&&S.watchOnce(a,()=>{u()})},{root:l,window:o,threshold:r,rootMargin:n.toValue(i)});return a}const oe=new Map;function Ln(e){const t=n.getCurrentScope();function o(a){var u;const f=oe.get(e)||new Set;f.add(a),oe.set(e,f);const c=()=>r(a);return(u=t?.cleanups)==null||u.push(c),c}function l(a){function u(...f){r(u),a(...f)}return o(u)}function r(a){const u=oe.get(e);u&&(u.delete(a),u.size||i())}function i(){oe.delete(e)}function s(a,u){var f;(f=oe.get(e))==null||f.forEach(c=>c(a,u))}return{on:o,once:l,off:r,emit:s,reset:i}}function Nn(e){return e===!0?{}:e}function xn(e,t=[],o={}){const l=n.ref(null),r=n.ref(null),i=n.shallowRef("CONNECTING"),s=n.ref(null),a=n.shallowRef(null),u=S.toRef(e),f=n.shallowRef(null);let c=!1,d=0;const{withCredentials:h=!1,immediate:m=!0,autoConnect:v=!0,autoReconnect:b}=o,p=()=>{S.isClient&&s.value&&(s.value.close(),s.value=null,i.value="CLOSED",c=!0)},w=()=>{if(c||typeof u.value>"u")return;const g=new EventSource(u.value,{withCredentials:h});i.value="CONNECTING",s.value=g,g.onopen=()=>{i.value="OPEN",a.value=null},g.onerror=T=>{if(i.value="CLOSED",a.value=T,g.readyState===2&&!c&&b){g.close();const{retries:k=-1,delay:C=1e3,onFailed:E}=Nn(b);d+=1,typeof k=="number"&&(k<0||d<k)||typeof k=="function"&&k()?setTimeout(w,C):E?.()}},g.onmessage=T=>{l.value=null,r.value=T.data,f.value=T.lastEventId};for(const T of t)O(g,T,k=>{l.value=T,r.value=k.data||null},{passive:!0})},y=()=>{S.isClient&&(p(),c=!1,d=0,w())};return m&&y(),v&&n.watch(u,y),S.tryOnScopeDispose(p),{eventSource:s,event:l,data:r,status:i,error:a,open:y,close:p,lastEventId:f}}function Wn(e={}){const{initialValue:t=""}=e,o=x(()=>typeof window<"u"&&"EyeDropper"in window),l=n.shallowRef(t);async function r(i){if(!o.value)return;const a=await new window.EyeDropper().open(i);return l.value=a.sRGBHex,a}return{isSupported:o,sRGBHex:l,open:r}}function Hn(e=null,t={}){const{baseUrl:o="",rel:l="icon",document:r=j}=t,i=S.toRef(e),s=a=>{const u=r?.head.querySelectorAll(`link[rel*="${l}"]`);if(!u||u.length===0){const f=r?.createElement("link");f&&(f.rel=l,f.href=`${o}${a}`,f.type=`image/${a.split(".").pop()}`,r?.head.append(f));return}u?.forEach(f=>f.href=`${o}${a}`)};return n.watch(i,(a,u)=>{typeof a=="string"&&a!==u&&s(a)},{immediate:!0}),i}const Un={json:"application/json",text:"text/plain"};function me(e){return e&&S.containsProp(e,"immediate","refetch","initialData","timeout","beforeFetch","afterFetch","onFetchError","fetch","updateDataOnError")}const $n=/^(?:[a-z][a-z\d+\-.]*:)?\/\//i;function Bn(e){return $n.test(e)}function le(e){return typeof Headers<"u"&&e instanceof Headers?Object.fromEntries(e.entries()):e}function Q(e,...t){return e==="overwrite"?async o=>{let l;for(let r=t.length-1;r>=0;r--)if(t[r]!=null){l=t[r];break}return l?{...o,...await l(o)}:o}:async o=>{for(const l of t)l&&(o={...o,...await l(o)});return o}}function jn(e={}){const t=e.combination||"chain",o=e.options||{},l=e.fetchOptions||{};function r(i,...s){const a=n.computed(()=>{const c=n.toValue(e.baseUrl),d=n.toValue(i);return c&&!Bn(d)?zn(c,d):d});let u=o,f=l;return s.length>0&&(me(s[0])?u={...u,...s[0],beforeFetch:Q(t,o.beforeFetch,s[0].beforeFetch),afterFetch:Q(t,o.afterFetch,s[0].afterFetch),onFetchError:Q(t,o.onFetchError,s[0].onFetchError)}:f={...f,...s[0],headers:{...le(f.headers)||{},...le(s[0].headers)||{}}}),s.length>1&&me(s[1])&&(u={...u,...s[1],beforeFetch:Q(t,o.beforeFetch,s[1].beforeFetch),afterFetch:Q(t,o.afterFetch,s[1].afterFetch),onFetchError:Q(t,o.onFetchError,s[1].onFetchError)}),Xe(a,f,u)}return r}function Xe(e,...t){var o;const l=typeof AbortController=="function";let r={},i={immediate:!0,refetch:!1,timeout:0,updateDataOnError:!1};const s={method:"GET",type:"text",payload:void 0};t.length>0&&(me(t[0])?i={...i,...t[0]}:r=t[0]),t.length>1&&me(t[1])&&(i={...i,...t[1]});const{fetch:a=(o=A)==null?void 0:o.fetch,initialData:u,timeout:f}=i,c=S.createEventHook(),d=S.createEventHook(),h=S.createEventHook(),m=n.shallowRef(!1),v=n.shallowRef(!1),b=n.shallowRef(!1),p=n.ref(null),w=n.shallowRef(null),y=n.shallowRef(null),g=n.shallowRef(u||null),T=n.computed(()=>l&&v.value);let k,C;const E=()=>{l&&(k?.abort(),k=new AbortController,k.signal.onabort=()=>b.value=!0,r={...r,signal:k.signal})},V=W=>{v.value=W,m.value=!W};f&&(C=S.useTimeoutFn(E,f,{immediate:!1}));let P=0;const I=async(W=!1)=>{var M,U;E(),V(!0),y.value=null,p.value=null,b.value=!1,P+=1;const G=P,z={method:s.method,headers:{}},Z=n.toValue(s.payload);if(Z){const B=le(z.headers),ae=Object.getPrototypeOf(Z);!s.payloadType&&Z&&(ae===Object.prototype||Array.isArray(ae))&&!(Z instanceof FormData)&&(s.payloadType="json"),s.payloadType&&(B["Content-Type"]=(M=Un[s.payloadType])!=null?M:s.payloadType),z.body=s.payloadType==="json"?JSON.stringify(Z):Z}let Tt=!1;const X={url:n.toValue(e),options:{...z,...r},cancel:()=>{Tt=!0}};if(i.beforeFetch&&Object.assign(X,await i.beforeFetch(X)),Tt||!a)return V(!1),Promise.resolve(null);let K=null;return C&&C.start(),a(X.url,{...z,...X.options,headers:{...le(z.headers),...le((U=X.options)==null?void 0:U.headers)}}).then(async B=>{if(w.value=B,p.value=B.status,K=await B.clone()[s.type](),!B.ok)throw g.value=u||null,new Error(B.statusText);return i.afterFetch&&({data:K}=await i.afterFetch({data:K,response:B,context:X,execute:I})),g.value=K,c.trigger(B),B}).catch(async B=>{let ae=B.message||B.name;if(i.onFetchError&&({error:ae,data:K}=await i.onFetchError({data:K,error:B,response:w.value,context:X,execute:I})),y.value=ae,i.updateDataOnError&&(g.value=K),d.trigger(B),W)throw B;return null}).finally(()=>{G===P&&V(!1),C&&C.stop(),h.trigger(null)})},D=S.toRef(i.refetch);n.watch([D,S.toRef(e)],([W])=>W&&I(),{deep:!0});const _={isFinished:n.readonly(m),isFetching:n.readonly(v),statusCode:p,response:w,error:y,data:g,canAbort:T,aborted:b,abort:E,execute:I,onFetchResponse:c.on,onFetchError:d.on,onFetchFinally:h.on,get:F("GET"),put:F("PUT"),post:F("POST"),delete:F("DELETE"),patch:F("PATCH"),head:F("HEAD"),options:F("OPTIONS"),json:H("json"),text:H("text"),blob:H("blob"),arrayBuffer:H("arrayBuffer"),formData:H("formData")};function F(W){return(M,U)=>{if(!v.value)return s.method=W,s.payload=M,s.payloadType=U,n.isRef(s.payload)&&n.watch([D,S.toRef(s.payload)],([G])=>G&&I(),{deep:!0}),{..._,then(G,z){return L().then(G,z)}}}}function L(){return new Promise((W,M)=>{S.until(m).toBe(!0).then(()=>W(_)).catch(M)})}function H(W){return()=>{if(!v.value)return s.type=W,{..._,then(M,U){return L().then(M,U)}}}}return i.immediate&&Promise.resolve().then(()=>I()),{..._,then(W,M){return L().then(W,M)}}}function zn(e,t){return!e.endsWith("/")&&!t.startsWith("/")?`${e}/${t}`:e.endsWith("/")&&t.startsWith("/")?`${e.slice(0,-1)}${t}`:`${e}${t}`}const qn={multiple:!0,accept:"*",reset:!1,directory:!1};function Gn(e){if(!e)return null;if(e instanceof FileList)return e;const t=new DataTransfer;for(const o of e)t.items.add(o);return t.files}function Yn(e={}){const{document:t=j}=e,o=n.ref(Gn(e.initialFiles)),{on:l,trigger:r}=S.createEventHook(),{on:i,trigger:s}=S.createEventHook();let a;t&&(a=t.createElement("input"),a.type="file",a.onchange=c=>{const d=c.target;o.value=d.files,r(o.value)},a.oncancel=()=>{s()});const u=()=>{o.value=null,a&&a.value&&(a.value="",r(null))},f=c=>{if(!a)return;const d={...qn,...e,...c};a.multiple=d.multiple,a.accept=d.accept,a.webkitdirectory=d.directory,S.hasOwn(d,"capture")&&(a.capture=d.capture),d.reset&&u(),a.click()};return{files:n.readonly(o),open:f,reset:u,onCancel:i,onChange:l}}function Xn(e={}){const{window:t=A,dataType:o="Text"}=e,l=t,r=x(()=>l&&"showSaveFilePicker"in l&&"showOpenFilePicker"in l),i=n.shallowRef(),s=n.shallowRef(),a=n.shallowRef(),u=n.computed(()=>{var y,g;return(g=(y=a.value)==null?void 0:y.name)!=null?g:""}),f=n.computed(()=>{var y,g;return(g=(y=a.value)==null?void 0:y.type)!=null?g:""}),c=n.computed(()=>{var y,g;return(g=(y=a.value)==null?void 0:y.size)!=null?g:0}),d=n.computed(()=>{var y,g;return(g=(y=a.value)==null?void 0:y.lastModified)!=null?g:0});async function h(y={}){if(!r.value)return;const[g]=await l.showOpenFilePicker({...n.toValue(e),...y});i.value=g,await w()}async function m(y={}){r.value&&(i.value=await l.showSaveFilePicker({...e,...y}),s.value=void 0,await w())}async function v(y={}){if(r.value){if(!i.value)return b(y);if(s.value){const g=await i.value.createWritable();await g.write(s.value),await g.close()}await p()}}async function b(y={}){if(r.value){if(i.value=await l.showSaveFilePicker({...e,...y}),s.value){const g=await i.value.createWritable();await g.write(s.value),await g.close()}await p()}}async function p(){var y;a.value=await((y=i.value)==null?void 0:y.getFile())}async function w(){var y,g;await p();const T=n.toValue(o);T==="Text"?s.value=await((y=a.value)==null?void 0:y.text()):T==="ArrayBuffer"?s.value=await((g=a.value)==null?void 0:g.arrayBuffer()):T==="Blob"&&(s.value=a.value)}return n.watch(()=>n.toValue(o),w),{isSupported:r,data:s,file:a,fileName:u,fileMIME:f,fileSize:c,fileLastModified:d,open:h,create:m,save:v,saveAs:b,updateData:w}}function Kn(e,t={}){const{initialValue:o=!1,focusVisible:l=!1,preventScroll:r=!1}=t,i=n.shallowRef(!1),s=n.computed(()=>N(e)),a={passive:!0};O(s,"focus",f=>{var c,d;(!l||(d=(c=f.target).matches)!=null&&d.call(c,":focus-visible"))&&(i.value=!0)},a),O(s,"blur",()=>i.value=!1,a);const u=n.computed({get:()=>i.value,set(f){var c,d;!f&&i.value?(c=s.value)==null||c.blur():f&&!i.value&&((d=s.value)==null||d.focus({preventScroll:r}))}});return n.watch(s,()=>{u.value=o},{immediate:!0,flush:"post"}),{focused:u}}const Jn="focusin",Qn="focusout",Zn=":focus-within";function eo(e,t={}){const{window:o=A}=t,l=n.computed(()=>N(e)),r=n.shallowRef(!1),i=n.computed(()=>r.value),s=Ae(t);if(!o||!s.value)return{focused:i};const a={passive:!0};return O(l,Jn,()=>r.value=!0,a),O(l,Qn,()=>{var u,f,c;return r.value=(c=(f=(u=l.value)==null?void 0:u.matches)==null?void 0:f.call(u,Zn))!=null?c:!1},a),{focused:i}}function to(e){var t;const o=n.shallowRef(0);if(typeof performance>"u")return o;const l=(t=e?.every)!=null?t:10;let r=performance.now(),i=0;return Y(()=>{if(i+=1,i>=l){const s=performance.now(),a=s-r;o.value=Math.round(1e3/(a/i)),r=s,i=0}}),o}const Ke=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function no(e,t={}){const{document:o=j,autoExit:l=!1}=t,r=n.computed(()=>{var y;return(y=N(e))!=null?y:o?.documentElement}),i=n.shallowRef(!1),s=n.computed(()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find(y=>o&&y in o||r.value&&y in r.value)),a=n.computed(()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find(y=>o&&y in o||r.value&&y in r.value)),u=n.computed(()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find(y=>o&&y in o||r.value&&y in r.value)),f=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find(y=>o&&y in o),c=x(()=>r.value&&o&&s.value!==void 0&&a.value!==void 0&&u.value!==void 0),d=()=>f?o?.[f]===r.value:!1,h=()=>{if(u.value){if(o&&o[u.value]!=null)return o[u.value];{const y=r.value;if(y?.[u.value]!=null)return!!y[u.value]}}return!1};async function m(){if(!(!c.value||!i.value)){if(a.value)if(o?.[a.value]!=null)await o[a.value]();else{const y=r.value;y?.[a.value]!=null&&await y[a.value]()}i.value=!1}}async function v(){if(!c.value||i.value)return;h()&&await m();const y=r.value;s.value&&y?.[s.value]!=null&&(await y[s.value](),i.value=!0)}async function b(){await(i.value?m():v())}const p=()=>{const y=h();(!y||y&&d())&&(i.value=y)},w={capture:!1,passive:!0};return O(o,Ke,p,w),O(()=>N(r),Ke,p,w),l&&S.tryOnScopeDispose(m),{isSupported:c,isFullscreen:i,enter:v,exit:m,toggle:b}}function oo(e){return n.computed(()=>e.value?{buttons:{a:e.value.buttons[0],b:e.value.buttons[1],x:e.value.buttons[2],y:e.value.buttons[3]},bumper:{left:e.value.buttons[4],right:e.value.buttons[5]},triggers:{left:e.value.buttons[6],right:e.value.buttons[7]},stick:{left:{horizontal:e.value.axes[0],vertical:e.value.axes[1],button:e.value.buttons[10]},right:{horizontal:e.value.axes[2],vertical:e.value.axes[3],button:e.value.buttons[11]}},dpad:{up:e.value.buttons[12],down:e.value.buttons[13],left:e.value.buttons[14],right:e.value.buttons[15]},back:e.value.buttons[8],start:e.value.buttons[9]}:null)}function lo(e={}){const{navigator:t=q}=e,o=x(()=>t&&"getGamepads"in t),l=n.ref([]),r=S.createEventHook(),i=S.createEventHook(),s=v=>{const b=[],p="vibrationActuator"in v?v.vibrationActuator:null;return p&&b.push(p),v.hapticActuators&&b.push(...v.hapticActuators),{id:v.id,index:v.index,connected:v.connected,mapping:v.mapping,timestamp:v.timestamp,vibrationActuator:v.vibrationActuator,hapticActuators:b,axes:v.axes.map(w=>w),buttons:v.buttons.map(w=>({pressed:w.pressed,touched:w.touched,value:w.value}))}},a=()=>{const v=t?.getGamepads()||[];for(const b of v)b&&l.value[b.index]&&(l.value[b.index]=s(b))},{isActive:u,pause:f,resume:c}=Y(a),d=v=>{l.value.some(({index:b})=>b===v.index)||(l.value.push(s(v)),r.trigger(v.index)),c()},h=v=>{l.value=l.value.filter(b=>b.index!==v.index),i.trigger(v.index)},m={passive:!0};return O("gamepadconnected",v=>d(v.gamepad),m),O("gamepaddisconnected",v=>h(v.gamepad),m),S.tryOnMounted(()=>{const v=t?.getGamepads()||[];for(const b of v)b&&l.value[b.index]&&d(b)}),f(),{isSupported:o,onConnected:r.on,onDisconnected:i.on,gamepads:l,pause:f,resume:c,isActive:u}}function ao(e={}){const{enableHighAccuracy:t=!0,maximumAge:o=3e4,timeout:l=27e3,navigator:r=q,immediate:i=!0}=e,s=x(()=>r&&"geolocation"in r),a=n.ref(null),u=n.shallowRef(null),f=n.ref({accuracy:0,latitude:Number.POSITIVE_INFINITY,longitude:Number.POSITIVE_INFINITY,altitude:null,altitudeAccuracy:null,heading:null,speed:null});function c(v){a.value=v.timestamp,f.value=v.coords,u.value=null}let d;function h(){s.value&&(d=r.geolocation.watchPosition(c,v=>u.value=v,{enableHighAccuracy:t,maximumAge:o,timeout:l}))}i&&h();function m(){d&&r&&r.geolocation.clearWatch(d)}return S.tryOnScopeDispose(()=>{m()}),{isSupported:s,coords:f,locatedAt:a,error:u,resume:h,pause:m}}const ro=["mousemove","mousedown","resize","keydown","touchstart","wheel"],io=6e4;function so(e=io,t={}){const{initialState:o=!1,listenForVisibilityChange:l=!0,events:r=ro,window:i=A,eventFilter:s=S.throttleFilter(50)}=t,a=n.shallowRef(o),u=n.shallowRef(S.timestamp());let f;const c=()=>{a.value=!1,clearTimeout(f),f=setTimeout(()=>a.value=!0,e)},d=S.createFilterWrapper(s,()=>{u.value=S.timestamp(),c()});if(i){const h=i.document,m={passive:!0};for(const v of r)O(i,v,d,m);l&&O(h,"visibilitychange",()=>{h.hidden||d()},m),c()}return{idle:a,lastActive:u,reset:c}}async function uo(e){return new Promise((t,o)=>{const l=new Image,{src:r,srcset:i,sizes:s,class:a,loading:u,crossorigin:f,referrerPolicy:c,width:d,height:h,decoding:m,fetchPriority:v,ismap:b,usemap:p}=e;l.src=r,i!=null&&(l.srcset=i),s!=null&&(l.sizes=s),a!=null&&(l.className=a),u!=null&&(l.loading=u),f!=null&&(l.crossOrigin=f),c!=null&&(l.referrerPolicy=c),d!=null&&(l.width=d),h!=null&&(l.height=h),m!=null&&(l.decoding=m),v!=null&&(l.fetchPriority=v),b!=null&&(l.isMap=b),p!=null&&(l.useMap=p),l.onload=()=>t(l),l.onerror=o})}function co(e,t={}){const o=Me(()=>uo(n.toValue(e)),void 0,{resetOnExecute:!0,...t});return n.watch(()=>n.toValue(e),()=>o.execute(t.delay),{deep:!0}),o}function ve(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}const Je=1;function Te(e,t={}){const{throttle:o=0,idle:l=200,onStop:r=S.noop,onScroll:i=S.noop,offset:s={left:0,right:0,top:0,bottom:0},eventListenerOptions:a={capture:!1,passive:!0},behavior:u="auto",window:f=A,onError:c=E=>{console.error(E)}}=t,d=n.shallowRef(0),h=n.shallowRef(0),m=n.computed({get(){return d.value},set(E){b(E,void 0)}}),v=n.computed({get(){return h.value},set(E){b(void 0,E)}});function b(E,V){var P,I,D,_;if(!f)return;const F=n.toValue(e);if(!F)return;(D=F instanceof Document?f.document.body:F)==null||D.scrollTo({top:(P=n.toValue(V))!=null?P:v.value,left:(I=n.toValue(E))!=null?I:m.value,behavior:n.toValue(u)});const L=((_=F?.document)==null?void 0:_.documentElement)||F?.documentElement||F;m!=null&&(d.value=L.scrollLeft),v!=null&&(h.value=L.scrollTop)}const p=n.shallowRef(!1),w=n.reactive({left:!0,right:!1,top:!0,bottom:!1}),y=n.reactive({left:!1,right:!1,top:!1,bottom:!1}),g=E=>{p.value&&(p.value=!1,y.left=!1,y.right=!1,y.top=!1,y.bottom=!1,r(E))},T=S.useDebounceFn(g,o+l),k=E=>{var V;if(!f)return;const P=((V=E?.document)==null?void 0:V.documentElement)||E?.documentElement||N(E),{display:I,flexDirection:D,direction:_}=getComputedStyle(P),F=_==="rtl"?-1:1,L=P.scrollLeft;y.left=L<d.value,y.right=L>d.value;const H=L*F<=(s.left||0),W=L*F+P.clientWidth>=P.scrollWidth-(s.right||0)-Je;I==="flex"&&D==="row-reverse"?(w.left=W,w.right=H):(w.left=H,w.right=W),d.value=L;let M=P.scrollTop;E===f.document&&!M&&(M=f.document.body.scrollTop),y.top=M<h.value,y.bottom=M>h.value;const U=M<=(s.top||0),G=M+P.clientHeight>=P.scrollHeight-(s.bottom||0)-Je;I==="flex"&&D==="column-reverse"?(w.top=G,w.bottom=U):(w.top=U,w.bottom=G),h.value=M},C=E=>{var V;if(!f)return;const P=(V=E.target.documentElement)!=null?V:E.target;k(P),p.value=!0,T(E),i(E)};return O(e,"scroll",o?S.useThrottleFn(C,o,!0,!1):C,a),S.tryOnMounted(()=>{try{const E=n.toValue(e);if(!E)return;k(E)}catch(E){c(E)}}),O(e,"scrollend",g,a),{x:m,y:v,isScrolling:p,arrivedState:w,directions:y,measure(){const E=n.toValue(e);f&&E&&k(E)}}}function fo(e,t,o={}){var l;const{direction:r="bottom",interval:i=100,canLoadMore:s=()=>!0}=o,a=n.reactive(Te(e,{...o,offset:{[r]:(l=o.distance)!=null?l:0,...o.offset}})),u=n.ref(),f=n.computed(()=>!!u.value),c=n.computed(()=>ve(n.toValue(e))),d=Ye(c);function h(){if(a.measure(),!c.value||!d.value||!s(c.value))return;const{scrollHeight:v,clientHeight:b,scrollWidth:p,clientWidth:w}=c.value,y=r==="bottom"||r==="top"?v<=b:p<=w;(a.arrivedState[r]||y)&&(u.value||(u.value=Promise.all([t(a),new Promise(g=>setTimeout(g,i))]).finally(()=>{u.value=null,n.nextTick(()=>h())})))}const m=n.watch(()=>[a.arrivedState[r],d.value],h,{immediate:!0});return S.tryOnUnmounted(m),{isLoading:f,reset(){n.nextTick(()=>h())}}}const mo=["mousedown","mouseup","keydown","keyup"];function vo(e,t={}){const{events:o=mo,document:l=j,initial:r=null}=t,i=n.ref(r);return l&&o.forEach(s=>{O(l,s,a=>{typeof a.getModifierState=="function"&&(i.value=a.getModifierState(e))},{passive:!0})}),i}function po(e,t,o={}){const{window:l=A}=o;return fe(e,t,l?.localStorage,o)}const Qe={ctrl:"control",command:"meta",cmd:"meta",option:"alt",up:"arrowup",down:"arrowdown",left:"arrowleft",right:"arrowright"};function ho(e={}){const{reactive:t=!1,target:o=A,aliasMap:l=Qe,passive:r=!0,onEventFired:i=S.noop}=e,s=n.reactive(new Set),a={toJSON(){return{}},current:s},u=t?n.reactive(a):a,f=new Set,c=new Set;function d(b,p){b in u&&(t?u[b]=p:u[b].value=p)}function h(){s.clear();for(const b of c)d(b,!1)}function m(b,p){var w,y;const g=(w=b.key)==null?void 0:w.toLowerCase(),k=[(y=b.code)==null?void 0:y.toLowerCase(),g].filter(Boolean);g&&(p?s.add(g):s.delete(g));for(const C of k)c.add(C),d(C,p);g==="meta"&&!p?(f.forEach(C=>{s.delete(C),d(C,!1)}),f.clear()):typeof b.getModifierState=="function"&&b.getModifierState("Meta")&&p&&[...s,...k].forEach(C=>f.add(C))}O(o,"keydown",b=>(m(b,!0),i(b)),{passive:r}),O(o,"keyup",b=>(m(b,!1),i(b)),{passive:r}),O("blur",h,{passive:r}),O("focus",h,{passive:r});const v=new Proxy(u,{get(b,p,w){if(typeof p!="string")return Reflect.get(b,p,w);if(p=p.toLowerCase(),p in l&&(p=l[p]),!(p in u))if(/[+_-]/.test(p)){const g=p.split(/[+_-]/g).map(T=>T.trim());u[p]=n.computed(()=>g.map(T=>n.toValue(v[T])).every(Boolean))}else u[p]=n.shallowRef(!1);const y=Reflect.get(b,p,w);return t?n.toValue(y):y}});return v}function Oe(e,t){n.toValue(e)&&t(n.toValue(e))}function yo(e){let t=[];for(let o=0;o<e.length;++o)t=[...t,[e.start(o),e.end(o)]];return t}function ke(e){return Array.from(e).map(({label:t,kind:o,language:l,mode:r,activeCues:i,cues:s,inBandMetadataTrackDispatchType:a},u)=>({id:u,label:t,kind:o,language:l,mode:r,activeCues:i,cues:s,inBandMetadataTrackDispatchType:a}))}const wo={src:"",tracks:[]};function go(e,t={}){e=S.toRef(e),t={...wo,...t};const{document:o=j}=t,l={passive:!0},r=n.shallowRef(0),i=n.shallowRef(0),s=n.shallowRef(!1),a=n.shallowRef(1),u=n.shallowRef(!1),f=n.shallowRef(!1),c=n.shallowRef(!1),d=n.shallowRef(1),h=n.shallowRef(!1),m=n.ref([]),v=n.ref([]),b=n.shallowRef(-1),p=n.shallowRef(!1),w=n.shallowRef(!1),y=o&&"pictureInPictureEnabled"in o,g=S.createEventHook(),T=S.createEventHook(),k=_=>{Oe(e,F=>{if(_){const L=typeof _=="number"?_:_.id;F.textTracks[L].mode="disabled"}else for(let L=0;L<F.textTracks.length;++L)F.textTracks[L].mode="disabled";b.value=-1})},C=(_,F=!0)=>{Oe(e,L=>{const H=typeof _=="number"?_:_.id;F&&k(),L.textTracks[H].mode="showing",b.value=H})},E=()=>new Promise((_,F)=>{Oe(e,async L=>{y&&(p.value?o.exitPictureInPicture().then(_).catch(F):L.requestPictureInPicture().then(_).catch(F))})});n.watchEffect(()=>{if(!o)return;const _=n.toValue(e);if(!_)return;const F=n.toValue(t.src);let L=[];F&&(typeof F=="string"?L=[{src:F}]:Array.isArray(F)?L=F:S.isObject(F)&&(L=[F]),_.querySelectorAll("source").forEach(H=>{H.remove()}),L.forEach(({src:H,type:W,media:M})=>{const U=o.createElement("source");U.setAttribute("src",H),U.setAttribute("type",W||""),U.setAttribute("media",M||""),O(U,"error",g.trigger,l),_.appendChild(U)}),_.load())}),n.watch([e,a],()=>{const _=n.toValue(e);_&&(_.volume=a.value)}),n.watch([e,w],()=>{const _=n.toValue(e);_&&(_.muted=w.value)}),n.watch([e,d],()=>{const _=n.toValue(e);_&&(_.playbackRate=d.value)}),n.watchEffect(()=>{if(!o)return;const _=n.toValue(t.tracks),F=n.toValue(e);!_||!_.length||!F||(F.querySelectorAll("track").forEach(L=>L.remove()),_.forEach(({default:L,kind:H,label:W,src:M,srcLang:U},G)=>{const z=o.createElement("track");z.default=L||!1,z.kind=H,z.label=W,z.src=M,z.srclang=U,z.default&&(b.value=G),F.appendChild(z)}))});const{ignoreUpdates:V}=S.watchIgnorable(r,_=>{const F=n.toValue(e);F&&(F.currentTime=_)}),{ignoreUpdates:P}=S.watchIgnorable(c,_=>{const F=n.toValue(e);F&&(_?F.play().catch(L=>{throw T.trigger(L),L}):F.pause())});O(e,"timeupdate",()=>V(()=>r.value=n.toValue(e).currentTime),l),O(e,"durationchange",()=>i.value=n.toValue(e).duration,l),O(e,"progress",()=>m.value=yo(n.toValue(e).buffered),l),O(e,"seeking",()=>s.value=!0,l),O(e,"seeked",()=>s.value=!1,l),O(e,["waiting","loadstart"],()=>{u.value=!0,P(()=>c.value=!1)},l),O(e,"loadeddata",()=>u.value=!1,l),O(e,"playing",()=>{u.value=!1,f.value=!1,P(()=>c.value=!0)},l),O(e,"ratechange",()=>d.value=n.toValue(e).playbackRate,l),O(e,"stalled",()=>h.value=!0,l),O(e,"ended",()=>f.value=!0,l),O(e,"pause",()=>P(()=>c.value=!1),l),O(e,"play",()=>P(()=>c.value=!0),l),O(e,"enterpictureinpicture",()=>p.value=!0,l),O(e,"leavepictureinpicture",()=>p.value=!1,l),O(e,"volumechange",()=>{const _=n.toValue(e);_&&(a.value=_.volume,w.value=_.muted)},l);const I=[],D=n.watch([e],()=>{const _=n.toValue(e);_&&(D(),I[0]=O(_.textTracks,"addtrack",()=>v.value=ke(_.textTracks),l),I[1]=O(_.textTracks,"removetrack",()=>v.value=ke(_.textTracks),l),I[2]=O(_.textTracks,"change",()=>v.value=ke(_.textTracks),l))});return S.tryOnScopeDispose(()=>I.forEach(_=>_())),{currentTime:r,duration:i,waiting:u,seeking:s,ended:f,stalled:h,buffered:m,playing:c,rate:d,volume:a,muted:w,tracks:v,selectedTrack:b,enableTrack:C,disableTrack:k,supportsPictureInPicture:y,togglePictureInPicture:E,isPictureInPicture:p,onSourceError:g.on,onPlaybackError:T.on}}function bo(e,t){const l=t?.cache?n.shallowReactive(t.cache):n.shallowReactive(new Map),r=(...c)=>t?.getKey?t.getKey(...c):JSON.stringify(c),i=(c,...d)=>(l.set(c,e(...d)),l.get(c)),s=(...c)=>i(r(...c),...c),a=(...c)=>{l.delete(r(...c))},u=()=>{l.clear()},f=(...c)=>{const d=r(...c);return l.has(d)?l.get(d):i(d,...c)};return f.load=s,f.delete=a,f.clear=u,f.generateKey=r,f.cache=l,f}function So(e={}){const t=n.ref(),o=x(()=>typeof performance<"u"&&"memory"in performance);if(o.value){const{interval:l=1e3}=e;S.useIntervalFn(()=>{t.value=performance.memory},l,{immediate:e.immediate,immediateCallback:e.immediateCallback})}return{isSupported:o,memory:t}}const Ro={page:e=>[e.pageX,e.pageY],client:e=>[e.clientX,e.clientY],screen:e=>[e.screenX,e.screenY],movement:e=>e instanceof MouseEvent?[e.movementX,e.movementY]:null};function Ze(e={}){const{type:t="page",touch:o=!0,resetOnTouchEnds:l=!1,initialValue:r={x:0,y:0},window:i=A,target:s=i,scroll:a=!0,eventFilter:u}=e;let f=null,c=0,d=0;const h=n.shallowRef(r.x),m=n.shallowRef(r.y),v=n.shallowRef(null),b=typeof t=="function"?t:Ro[t],p=E=>{const V=b(E);f=E,V&&([h.value,m.value]=V,v.value="mouse"),i&&(c=i.scrollX,d=i.scrollY)},w=E=>{if(E.touches.length>0){const V=b(E.touches[0]);V&&([h.value,m.value]=V,v.value="touch")}},y=()=>{if(!f||!i)return;const E=b(f);f instanceof MouseEvent&&E&&(h.value=E[0]+i.scrollX-c,m.value=E[1]+i.scrollY-d)},g=()=>{h.value=r.x,m.value=r.y},T=u?E=>u(()=>p(E),{}):E=>p(E),k=u?E=>u(()=>w(E),{}):E=>w(E),C=u?()=>u(()=>y(),{}):()=>y();if(s){const E={passive:!0};O(s,["mousemove","dragover"],T,E),o&&t!=="movement"&&(O(s,["touchstart","touchmove"],k,E),l&&O(s,"touchend",g,E)),a&&t==="page"&&O(i,"scroll",C,E)}return{x:h,y:m,sourceType:v}}function et(e,t={}){const{handleOutside:o=!0,window:l=A}=t,r=t.type||"page",{x:i,y:s,sourceType:a}=Ze(t),u=n.ref(e??l?.document.body),f=n.shallowRef(0),c=n.shallowRef(0),d=n.shallowRef(0),h=n.shallowRef(0),m=n.shallowRef(0),v=n.shallowRef(0),b=n.shallowRef(!0);let p=()=>{};return l&&(p=n.watch([u,i,s],()=>{const w=N(u);if(!w||!(w instanceof Element))return;const{left:y,top:g,width:T,height:k}=w.getBoundingClientRect();d.value=y+(r==="page"?l.pageXOffset:0),h.value=g+(r==="page"?l.pageYOffset:0),m.value=k,v.value=T;const C=i.value-d.value,E=s.value-h.value;b.value=T===0||k===0||C<0||E<0||C>T||E>k,(o||!b.value)&&(f.value=C,c.value=E)},{immediate:!0}),O(document,"mouseleave",()=>b.value=!0,{passive:!0})),{x:i,y:s,sourceType:a,elementX:f,elementY:c,elementPositionX:d,elementPositionY:h,elementHeight:m,elementWidth:v,isOutside:b,stop:p}}function Eo(e={}){const{touch:t=!0,drag:o=!0,capture:l=!1,initialValue:r=!1,window:i=A}=e,s=n.ref(r),a=n.ref(null);if(!i)return{pressed:s,sourceType:a};const u=h=>m=>{var v;s.value=!0,a.value=h,(v=e.onPressed)==null||v.call(e,m)},f=h=>{var m;s.value=!1,a.value=null,(m=e.onReleased)==null||m.call(e,h)},c=n.computed(()=>N(e.target)||i),d={passive:!0,capture:l};return O(c,"mousedown",u("mouse"),d),O(i,"mouseleave",f,d),O(i,"mouseup",f,d),o&&(O(c,"dragstart",u("mouse"),d),O(i,"drop",f,d),O(i,"dragend",f,d)),t&&(O(c,"touchstart",u("touch"),d),O(i,"touchend",f,d),O(i,"touchcancel",f,d)),{pressed:s,sourceType:a}}function To(e={}){const{window:t=A}=e,o=t?.navigator,l=x(()=>o&&"language"in o),r=n.ref(o?.language);return O(t,"languagechange",()=>{o&&(r.value=o.language)},{passive:!0}),{isSupported:l,language:r}}function tt(e={}){const{window:t=A}=e,o=t?.navigator,l=x(()=>o&&"connection"in o),r=n.shallowRef(!0),i=n.shallowRef(!1),s=n.ref(void 0),a=n.ref(void 0),u=n.ref(void 0),f=n.ref(void 0),c=n.ref(void 0),d=n.ref(void 0),h=n.ref("unknown"),m=l.value&&o.connection;function v(){o&&(r.value=o.onLine,s.value=r.value?void 0:Date.now(),a.value=r.value?Date.now():void 0,m&&(u.value=m.downlink,f.value=m.downlinkMax,d.value=m.effectiveType,c.value=m.rtt,i.value=m.saveData,h.value=m.type))}const b={passive:!0};return t&&(O(t,"offline",()=>{r.value=!1,s.value=Date.now()},b),O(t,"online",()=>{r.value=!0,a.value=Date.now()},b)),m&&O(m,"change",v,b),v(),{isSupported:l,isOnline:n.readonly(r),saveData:n.readonly(i),offlineAt:n.readonly(s),onlineAt:n.readonly(a),downlink:n.readonly(u),downlinkMax:n.readonly(f),effectiveType:n.readonly(d),rtt:n.readonly(c),type:n.readonly(h)}}function nt(e={}){const{controls:t=!1,interval:o="requestAnimationFrame"}=e,l=n.ref(new Date),r=()=>l.value=new Date,i=o==="requestAnimationFrame"?Y(r,{immediate:!0}):S.useIntervalFn(r,o,{immediate:!0});return t?{now:l,...i}:l}function Oo(e){const t=n.ref(),o=()=>{t.value&&URL.revokeObjectURL(t.value),t.value=void 0};return n.watch(()=>n.toValue(e),l=>{o(),l&&(t.value=URL.createObjectURL(l))},{immediate:!0}),S.tryOnScopeDispose(o),n.readonly(t)}function ot(e,t,o){if(typeof e=="function"||n.isReadonly(e))return n.computed(()=>S.clamp(n.toValue(e),n.toValue(t),n.toValue(o)));const l=n.ref(e);return n.computed({get(){return l.value=S.clamp(l.value,n.toValue(t),n.toValue(o))},set(r){l.value=S.clamp(r,n.toValue(t),n.toValue(o))}})}function ko(e){const{total:t=Number.POSITIVE_INFINITY,pageSize:o=10,page:l=1,onPageChange:r=S.noop,onPageSizeChange:i=S.noop,onPageCountChange:s=S.noop}=e,a=ot(o,1,Number.POSITIVE_INFINITY),u=n.computed(()=>Math.max(1,Math.ceil(n.toValue(t)/n.toValue(a)))),f=ot(l,1,u),c=n.computed(()=>f.value===1),d=n.computed(()=>f.value===u.value);n.isRef(l)&&S.syncRef(l,f,{direction:n.isReadonly(l)?"ltr":"both"}),n.isRef(o)&&S.syncRef(o,a,{direction:n.isReadonly(o)?"ltr":"both"});function h(){f.value--}function m(){f.value++}const v={currentPage:f,currentPageSize:a,pageCount:u,isFirstPage:c,isLastPage:d,prev:h,next:m};return n.watch(f,()=>{r(n.reactive(v))}),n.watch(a,()=>{i(n.reactive(v))}),n.watch(u,()=>{s(n.reactive(v))}),v}function _o(e={}){const{isOnline:t}=tt(e);return t}function Vo(e={}){const{window:t=A}=e,o=n.shallowRef(!1),l=r=>{if(!t)return;r=r||t.event;const i=r.relatedTarget||r.toElement;o.value=!i};if(t){const r={passive:!0};O(t,"mouseout",l,r),O(t.document,"mouseleave",l,r),O(t.document,"mouseenter",l,r)}return o}function lt(e={}){const{window:t=A}=e,o=x(()=>t&&"screen"in t&&"orientation"in t.screen),l=o.value?t.screen.orientation:{},r=n.ref(l.type),i=n.ref(l.angle||0);return o.value&&O(t,"orientationchange",()=>{r.value=l.type,i.value=l.angle},{passive:!0}),{isSupported:o,orientation:r,angle:i,lockOrientation:u=>o.value&&typeof l.lock=="function"?l.lock(u):Promise.reject(new Error("Not supported")),unlockOrientation:()=>{o.value&&typeof l.unlock=="function"&&l.unlock()}}}function Fo(e,t={}){const{deviceOrientationTiltAdjust:o=p=>p,deviceOrientationRollAdjust:l=p=>p,mouseTiltAdjust:r=p=>p,mouseRollAdjust:i=p=>p,window:s=A}=t,a=n.reactive(je({window:s})),u=n.reactive(lt({window:s})),{elementX:f,elementY:c,elementWidth:d,elementHeight:h}=et(e,{handleOutside:!1,window:s}),m=n.computed(()=>a.isSupported&&(a.alpha!=null&&a.alpha!==0||a.gamma!=null&&a.gamma!==0)?"deviceOrientation":"mouse"),v=n.computed(()=>{if(m.value==="deviceOrientation"){let p;switch(u.orientation){case"landscape-primary":p=a.gamma/90;break;case"landscape-secondary":p=-a.gamma/90;break;case"portrait-primary":p=-a.beta/90;break;case"portrait-secondary":p=a.beta/90;break;default:p=-a.beta/90}return l(p)}else{const p=-(c.value-h.value/2)/h.value;return i(p)}}),b=n.computed(()=>{if(m.value==="deviceOrientation"){let p;switch(u.orientation){case"landscape-primary":p=a.beta/90;break;case"landscape-secondary":p=-a.beta/90;break;case"portrait-primary":p=a.gamma/90;break;case"portrait-secondary":p=-a.gamma/90;break;default:p=a.gamma/90}return o(p)}else{const p=(f.value-d.value/2)/d.value;return r(p)}});return{roll:v,tilt:b,source:m}}function Po(e=Ue()){const t=n.shallowRef(),o=()=>{const l=N(e);l&&(t.value=l.parentElement)};return S.tryOnMounted(o),n.watch(()=>n.toValue(e),o),t}function Co(e,t){const{window:o=A,immediate:l=!0,...r}=e,i=x(()=>o&&"PerformanceObserver"in o);let s;const a=()=>{s?.disconnect()},u=()=>{i.value&&(a(),s=new PerformanceObserver(t),s.observe(r))};return S.tryOnScopeDispose(a),l&&u(),{isSupported:i,start:u,stop:a}}const at={x:0,y:0,pointerId:0,pressure:0,tiltX:0,tiltY:0,width:0,height:0,twist:0,pointerType:null},Do=Object.keys(at);function Ao(e={}){const{target:t=A}=e,o=n.shallowRef(!1),l=n.ref(e.initialValue||{});Object.assign(l.value,at,l.value);const r=i=>{o.value=!0,!(e.pointerTypes&&!e.pointerTypes.includes(i.pointerType))&&(l.value=S.objectPick(i,Do,!1))};if(t){const i={passive:!0};O(t,["pointerdown","pointermove","pointerup"],r,i),O(t,"pointerleave",()=>o.value=!1,i)}return{...S.toRefs(l),isInside:o}}function Mo(e,t={}){const{document:o=j}=t,l=x(()=>o&&"pointerLockElement"in o),r=n.ref(),i=n.ref();let s;if(l.value){const f={passive:!0};O(o,"pointerlockchange",()=>{var c;const d=(c=o.pointerLockElement)!=null?c:r.value;s&&d===s&&(r.value=o.pointerLockElement,r.value||(s=i.value=null))},f),O(o,"pointerlockerror",()=>{var c;const d=(c=o.pointerLockElement)!=null?c:r.value;if(s&&d===s){const h=o.pointerLockElement?"release":"acquire";throw new Error(`Failed to ${h} pointer lock.`)}},f)}async function a(f){var c;if(!l.value)throw new Error("Pointer Lock API is not supported by your browser.");if(i.value=f instanceof Event?f.currentTarget:null,s=f instanceof Event?(c=N(e))!=null?c:i.value:N(f),!s)throw new Error("Target element undefined.");return s.requestPointerLock(),await S.until(r).toBe(s)}async function u(){return r.value?(o.exitPointerLock(),await S.until(r).toBeNull(),!0):!1}return{isSupported:l,element:r,triggerElement:i,lock:a,unlock:u}}function Io(e,t={}){const o=S.toRef(e),{threshold:l=50,onSwipe:r,onSwipeEnd:i,onSwipeStart:s,disableTextSelect:a=!1}=t,u=n.reactive({x:0,y:0}),f=(V,P)=>{u.x=V,u.y=P},c=n.reactive({x:0,y:0}),d=(V,P)=>{c.x=V,c.y=P},h=n.computed(()=>u.x-c.x),m=n.computed(()=>u.y-c.y),{max:v,abs:b}=Math,p=n.computed(()=>v(b(h.value),b(m.value))>=l),w=n.shallowRef(!1),y=n.shallowRef(!1),g=n.computed(()=>p.value?b(h.value)>b(m.value)?h.value>0?"left":"right":m.value>0?"up":"down":"none"),T=V=>{var P,I,D;const _=V.buttons===0,F=V.buttons===1;return(D=(I=(P=t.pointerTypes)==null?void 0:P.includes(V.pointerType))!=null?I:_||F)!=null?D:!0},k={passive:!0},C=[O(e,"pointerdown",V=>{if(!T(V))return;y.value=!0;const P=V.target;P?.setPointerCapture(V.pointerId);const{clientX:I,clientY:D}=V;f(I,D),d(I,D),s?.(V)},k),O(e,"pointermove",V=>{if(!T(V)||!y.value)return;const{clientX:P,clientY:I}=V;d(P,I),!w.value&&p.value&&(w.value=!0),w.value&&r?.(V)},k),O(e,"pointerup",V=>{T(V)&&(w.value&&i?.(V,g.value),y.value=!1,w.value=!1)},k)];S.tryOnMounted(()=>{var V,P,I,D,_,F,L,H;(P=(V=o.value)==null?void 0:V.style)==null||P.setProperty("touch-action","none"),a&&((D=(I=o.value)==null?void 0:I.style)==null||D.setProperty("-webkit-user-select","none"),(F=(_=o.value)==null?void 0:_.style)==null||F.setProperty("-ms-user-select","none"),(H=(L=o.value)==null?void 0:L.style)==null||H.setProperty("user-select","none"))});const E=()=>C.forEach(V=>V());return{isSwiping:n.readonly(w),direction:n.readonly(g),posStart:n.readonly(u),posEnd:n.readonly(c),distanceX:h,distanceY:m,stop:E}}function Lo(e){const t=$("(prefers-color-scheme: light)",e),o=$("(prefers-color-scheme: dark)",e);return n.computed(()=>o.value?"dark":t.value?"light":"no-preference")}function No(e){const t=$("(prefers-contrast: more)",e),o=$("(prefers-contrast: less)",e),l=$("(prefers-contrast: custom)",e);return n.computed(()=>t.value?"more":o.value?"less":l.value?"custom":"no-preference")}function xo(e={}){const{window:t=A}=e;if(!t)return n.ref(["en"]);const o=t.navigator,l=n.ref(o.languages);return O(t,"languagechange",()=>{l.value=o.languages},{passive:!0}),l}function Wo(e){const t=$("(prefers-reduced-motion: reduce)",e);return n.computed(()=>t.value?"reduce":"no-preference")}function Ho(e){const t=$("(prefers-reduced-transparency: reduce)",e);return n.computed(()=>t.value?"reduce":"no-preference")}function Uo(e,t){const o=n.shallowRef(t);return n.watch(S.toRef(e),(l,r)=>{o.value=r},{flush:"sync"}),n.readonly(o)}const rt="--vueuse-safe-area-top",it="--vueuse-safe-area-right",st="--vueuse-safe-area-bottom",ut="--vueuse-safe-area-left";function $o(){const e=n.shallowRef(""),t=n.shallowRef(""),o=n.shallowRef(""),l=n.shallowRef("");if(S.isClient){const i=ne(rt),s=ne(it),a=ne(st),u=ne(ut);i.value="env(safe-area-inset-top, 0px)",s.value="env(safe-area-inset-right, 0px)",a.value="env(safe-area-inset-bottom, 0px)",u.value="env(safe-area-inset-left, 0px)",r(),O("resize",S.useDebounceFn(r),{passive:!0})}function r(){e.value=pe(rt),t.value=pe(it),o.value=pe(st),l.value=pe(ut)}return{top:e,right:t,bottom:o,left:l,update:r}}function pe(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function Bo(e,t=S.noop,o={}){const{immediate:l=!0,manual:r=!1,type:i="text/javascript",async:s=!0,crossOrigin:a,referrerPolicy:u,noModule:f,defer:c,document:d=j,attrs:h={}}=o,m=n.ref(null);let v=null;const b=y=>new Promise((g,T)=>{const k=P=>(m.value=P,g(P),P);if(!d){g(!1);return}let C=!1,E=d.querySelector(`script[src="${n.toValue(e)}"]`);E?E.hasAttribute("data-loaded")&&k(E):(E=d.createElement("script"),E.type=i,E.async=s,E.src=n.toValue(e),c&&(E.defer=c),a&&(E.crossOrigin=a),f&&(E.noModule=f),u&&(E.referrerPolicy=u),Object.entries(h).forEach(([P,I])=>E?.setAttribute(P,I)),C=!0);const V={passive:!0};O(E,"error",P=>T(P),V),O(E,"abort",P=>T(P),V),O(E,"load",()=>{E.setAttribute("data-loaded","true"),t(E),k(E)},V),C&&(E=d.head.appendChild(E)),y||k(E)}),p=(y=!0)=>(v||(v=b(y)),v),w=()=>{if(!d)return;v=null,m.value&&(m.value=null);const y=d.querySelector(`script[src="${n.toValue(e)}"]`);y&&d.head.removeChild(y)};return l&&!r&&S.tryOnMounted(p),r||S.tryOnUnmounted(w),{scriptTag:m,load:p,unload:w}}function ct(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const o=e.parentNode;return!o||o.tagName==="BODY"?!1:ct(o)}}function jo(e){const t=e||window.event,o=t.target;return ct(o)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const _e=new WeakMap;function zo(e,t=!1){const o=n.ref(t);let l=null,r="";n.watch(S.toRef(e),a=>{const u=ve(n.toValue(a));if(u){const f=u;if(_e.get(f)||_e.set(f,f.style.overflow),f.style.overflow!=="hidden"&&(r=f.style.overflow),f.style.overflow==="hidden")return o.value=!0;if(o.value)return f.style.overflow="hidden"}},{immediate:!0});const i=()=>{const a=ve(n.toValue(e));!a||o.value||(S.isIOS&&(l=O(a,"touchmove",u=>{jo(u)},{passive:!1})),a.style.overflow="hidden",o.value=!0)},s=()=>{const a=ve(n.toValue(e));!a||!o.value||(S.isIOS&&l?.(),a.style.overflow=r,_e.delete(a),o.value=!1)};return S.tryOnScopeDispose(s),n.computed({get(){return o.value},set(a){a?i():s()}})}function qo(e,t,o={}){const{window:l=A}=o;return fe(e,t,l?.sessionStorage,o)}function Go(e={},t={}){const{navigator:o=q}=t,l=o,r=x(()=>l&&"canShare"in l);return{isSupported:r,share:async(s={})=>{if(r.value){const a={...n.toValue(e),...n.toValue(s)};let u=!0;if(a.files&&l.canShare&&(u=l.canShare({files:a.files})),u)return l.share(a)}}}}const Yo=(e,t)=>e.sort(t),he=(e,t)=>e-t;function Xo(...e){var t,o,l,r;const[i]=e;let s=he,a={};e.length===2?typeof e[1]=="object"?(a=e[1],s=(t=a.compareFn)!=null?t:he):s=(o=e[1])!=null?o:he:e.length>2&&(s=(l=e[1])!=null?l:he,a=(r=e[2])!=null?r:{});const{dirty:u=!1,sortFn:f=Yo}=a;return u?(n.watchEffect(()=>{const c=f(n.toValue(i),s);n.isRef(i)?i.value=c:i.splice(0,i.length,...c)}),i):n.computed(()=>f([...n.toValue(i)],s))}function Ko(e={}){const{interimResults:t=!0,continuous:o=!0,maxAlternatives:l=1,window:r=A}=e,i=S.toRef(e.lang||"en-US"),s=n.shallowRef(!1),a=n.shallowRef(!1),u=n.shallowRef(""),f=n.shallowRef(void 0);let c;const d=()=>{s.value=!0},h=()=>{s.value=!1},m=(p=!s.value)=>{p?d():h()},v=r&&(r.SpeechRecognition||r.webkitSpeechRecognition),b=x(()=>v);return b.value&&(c=new v,c.continuous=o,c.interimResults=t,c.lang=n.toValue(i),c.maxAlternatives=l,c.onstart=()=>{s.value=!0,a.value=!1},n.watch(i,p=>{c&&!s.value&&(c.lang=p)}),c.onresult=p=>{const w=p.results[p.resultIndex],{transcript:y}=w[0];a.value=w.isFinal,u.value=y,f.value=void 0},c.onerror=p=>{f.value=p},c.onend=()=>{s.value=!1,c.lang=n.toValue(i)},n.watch(s,(p,w)=>{p!==w&&(p?c.start():c.stop())})),S.tryOnScopeDispose(()=>{h()}),{isSupported:b,isListening:s,isFinal:a,recognition:c,result:u,error:f,toggle:m,start:d,stop:h}}function Jo(e,t={}){const{pitch:o=1,rate:l=1,volume:r=1,window:i=A}=t,s=i&&i.speechSynthesis,a=x(()=>s),u=n.shallowRef(!1),f=n.ref("init"),c=S.toRef(e||""),d=S.toRef(t.lang||"en-US"),h=n.shallowRef(void 0),m=(y=!u.value)=>{u.value=y},v=y=>{y.lang=n.toValue(d),y.voice=n.toValue(t.voice)||null,y.pitch=n.toValue(o),y.rate=n.toValue(l),y.volume=r,y.onstart=()=>{u.value=!0,f.value="play"},y.onpause=()=>{u.value=!1,f.value="pause"},y.onresume=()=>{u.value=!0,f.value="play"},y.onend=()=>{u.value=!1,f.value="end"},y.onerror=g=>{h.value=g}},b=n.computed(()=>{u.value=!1,f.value="init";const y=new SpeechSynthesisUtterance(c.value);return v(y),y}),p=()=>{s.cancel(),b&&s.speak(b.value)},w=()=>{s.cancel(),u.value=!1};return a.value&&(v(b.value),n.watch(d,y=>{b.value&&!u.value&&(b.value.lang=y)}),t.voice&&n.watch(t.voice,()=>{s.cancel()}),n.watch(u,()=>{u.value?s.resume():s.pause()})),S.tryOnScopeDispose(()=>{u.value=!1}),{isSupported:a,isPlaying:u,status:f,utterance:b,error:h,stop:w,toggle:m,speak:p}}function Qo(e,t){const o=n.ref(e),l=n.computed(()=>Array.isArray(o.value)?o.value:Object.keys(o.value)),r=n.ref(l.value.indexOf(t??l.value[0])),i=n.computed(()=>c(r.value)),s=n.computed(()=>r.value===0),a=n.computed(()=>r.value===l.value.length-1),u=n.computed(()=>l.value[r.value+1]),f=n.computed(()=>l.value[r.value-1]);function c(k){return Array.isArray(o.value)?o.value[k]:o.value[l.value[k]]}function d(k){if(l.value.includes(k))return c(l.value.indexOf(k))}function h(k){l.value.includes(k)&&(r.value=l.value.indexOf(k))}function m(){a.value||r.value++}function v(){s.value||r.value--}function b(k){T(k)&&h(k)}function p(k){return l.value.indexOf(k)===r.value+1}function w(k){return l.value.indexOf(k)===r.value-1}function y(k){return l.value.indexOf(k)===r.value}function g(k){return r.value<l.value.indexOf(k)}function T(k){return r.value>l.value.indexOf(k)}return{steps:o,stepNames:l,index:r,current:i,next:u,previous:f,isFirst:s,isLast:a,at:c,get:d,goTo:h,goToNext:m,goToPrevious:v,goBackTo:b,isNext:p,isPrevious:w,isCurrent:y,isBefore:g,isAfter:T}}function Zo(e,t,o,l={}){var r;const{flush:i="pre",deep:s=!0,listenToStorageChanges:a=!0,writeDefaults:u=!0,mergeDefaults:f=!1,shallow:c,window:d=A,eventFilter:h,onError:m=g=>{console.error(g)}}=l,v=n.toValue(t),b=We(v),p=(c?n.shallowRef:n.ref)(n.toValue(t)),w=(r=l.serializer)!=null?r:Se[b];if(!o)try{o=ce("getDefaultStorageAsync",()=>{var g;return(g=A)==null?void 0:g.localStorage})()}catch(g){m(g)}async function y(g){if(!(!o||g&&g.key!==e))try{const T=g?g.newValue:await o.getItem(e);if(T==null)p.value=v,u&&v!==null&&await o.setItem(e,await w.write(v));else if(f){const k=await w.read(T);typeof f=="function"?p.value=f(k,v):b==="object"&&!Array.isArray(k)?p.value={...v,...k}:p.value=k}else p.value=await w.read(T)}catch(T){m(T)}}return y(),d&&a&&O(d,"storage",g=>Promise.resolve().then(()=>y(g)),{passive:!0}),o&&S.watchWithFilter(p,async()=>{try{p.value==null?await o.removeItem(e):await o.setItem(e,await w.write(p.value))}catch(g){m(g)}},{flush:i,deep:s,eventFilter:h}),p}let el=0;function tl(e,t={}){const o=n.shallowRef(!1),{document:l=j,immediate:r=!0,manual:i=!1,id:s=`vueuse_styletag_${++el}`}=t,a=n.shallowRef(e);let u=()=>{};const f=()=>{if(!l)return;const d=l.getElementById(s)||l.createElement("style");d.isConnected||(d.id=s,t.media&&(d.media=t.media),l.head.appendChild(d)),!o.value&&(u=n.watch(a,h=>{d.textContent=h},{immediate:!0}),o.value=!0)},c=()=>{!l||!o.value||(u(),l.head.removeChild(l.getElementById(s)),o.value=!1)};return r&&!i&&S.tryOnMounted(f),i||S.tryOnScopeDispose(c),{id:s,css:a,unload:c,load:f,isLoaded:n.readonly(o)}}function nl(e,t={}){const{threshold:o=50,onSwipe:l,onSwipeEnd:r,onSwipeStart:i,passive:s=!0}=t,a=n.reactive({x:0,y:0}),u=n.reactive({x:0,y:0}),f=n.computed(()=>a.x-u.x),c=n.computed(()=>a.y-u.y),{max:d,abs:h}=Math,m=n.computed(()=>d(h(f.value),h(c.value))>=o),v=n.shallowRef(!1),b=n.computed(()=>m.value?h(f.value)>h(c.value)?f.value>0?"left":"right":c.value>0?"up":"down":"none"),p=E=>[E.touches[0].clientX,E.touches[0].clientY],w=(E,V)=>{a.x=E,a.y=V},y=(E,V)=>{u.x=E,u.y=V},g={passive:s,capture:!s},T=E=>{v.value&&r?.(E,b.value),v.value=!1},k=[O(e,"touchstart",E=>{if(E.touches.length!==1)return;const[V,P]=p(E);w(V,P),y(V,P),i?.(E)},g),O(e,"touchmove",E=>{if(E.touches.length!==1)return;const[V,P]=p(E);y(V,P),g.capture&&!g.passive&&Math.abs(f.value)>Math.abs(c.value)&&E.preventDefault(),!v.value&&m.value&&(v.value=!0),v.value&&l?.(E)},g),O(e,["touchend","touchcancel"],T,g)];return{isSwiping:v,direction:b,coordsStart:a,coordsEnd:u,lengthX:f,lengthY:c,stop:()=>k.forEach(E=>E()),isPassiveEventSupported:!0}}function ol(){const e=n.ref([]);return e.value.set=t=>{t&&e.value.push(t)},n.onBeforeUpdate(()=>{e.value.length=0}),e}function ll(e={}){const{document:t=j,selector:o="html",observe:l=!1,initialValue:r="ltr"}=e;function i(){var a,u;return(u=(a=t?.querySelector(o))==null?void 0:a.getAttribute("dir"))!=null?u:r}const s=n.ref(i());return S.tryOnMounted(()=>s.value=i()),l&&t&&J(t.querySelector(o),()=>s.value=i(),{attributes:!0}),n.computed({get(){return s.value},set(a){var u,f;s.value=a,t&&(s.value?(u=t.querySelector(o))==null||u.setAttribute("dir",s.value):(f=t.querySelector(o))==null||f.removeAttribute("dir"))}})}function al(e){var t;const o=(t=e.rangeCount)!=null?t:0;return Array.from({length:o},(l,r)=>e.getRangeAt(r))}function rl(e={}){const{window:t=A}=e,o=n.ref(null),l=n.computed(()=>{var a,u;return(u=(a=o.value)==null?void 0:a.toString())!=null?u:""}),r=n.computed(()=>o.value?al(o.value):[]),i=n.computed(()=>r.value.map(a=>a.getBoundingClientRect()));function s(){o.value=null,t&&(o.value=t.getSelection())}return t&&O(t.document,"selectionchange",s,{passive:!0}),{text:l,rects:i,ranges:r,selection:o}}function il(e=A,t){e&&typeof e.requestAnimationFrame=="function"?e.requestAnimationFrame(t):t()}function sl(e={}){var t,o;const{window:l=A}=e,r=S.toRef(e?.element),i=S.toRef((t=e?.input)!=null?t:""),s=(o=e?.styleProp)!=null?o:"height",a=n.shallowRef(1),u=n.shallowRef(0);function f(){var c;if(!r.value)return;let d="";r.value.style[s]="1px",a.value=(c=r.value)==null?void 0:c.scrollHeight;const h=n.toValue(e?.styleTarget);h?h.style[s]=`${a.value}px`:d=`${a.value}px`,r.value.style[s]=d}return n.watch([i,r],()=>n.nextTick(f),{immediate:!0}),n.watch(a,()=>{var c;return(c=e?.onResize)==null?void 0:c.call(e)}),de(r,([{contentRect:c}])=>{u.value!==c.width&&il(l,()=>{u.value=c.width,f()})}),e?.watch&&n.watch(e.watch,f,{immediate:!0,deep:!0}),{textarea:r,input:i,triggerResize:f}}function ul(e,t={}){const{throttle:o=200,trailing:l=!0}=t,r=S.throttleFilter(o,l);return{...Ee(e,{...t,eventFilter:r})}}const cl=[{max:6e4,value:1e3,name:"second"},{max:276e4,value:6e4,name:"minute"},{max:72e6,value:36e5,name:"hour"},{max:5184e5,value:864e5,name:"day"},{max:24192e5,value:6048e5,name:"week"},{max:28512e6,value:2592e6,name:"month"},{max:Number.POSITIVE_INFINITY,value:31536e6,name:"year"}],fl={justNow:"just now",past:e=>e.match(/\d/)?`${e} ago`:e,future:e=>e.match(/\d/)?`in ${e}`:e,month:(e,t)=>e===1?t?"last month":"next month":`${e} month${e>1?"s":""}`,year:(e,t)=>e===1?t?"last year":"next year":`${e} year${e>1?"s":""}`,day:(e,t)=>e===1?t?"yesterday":"tomorrow":`${e} day${e>1?"s":""}`,week:(e,t)=>e===1?t?"last week":"next week":`${e} week${e>1?"s":""}`,hour:e=>`${e} hour${e>1?"s":""}`,minute:e=>`${e} minute${e>1?"s":""}`,second:e=>`${e} second${e>1?"s":""}`,invalid:""};function dl(e){return e.toISOString().slice(0,10)}function ml(e,t={}){const{controls:o=!1,updateInterval:l=3e4}=t,{now:r,...i}=nt({interval:l,controls:!0}),s=n.computed(()=>ft(new Date(n.toValue(e)),t,n.toValue(r)));return o?{timeAgo:s,...i}:s}function ft(e,t={},o=Date.now()){var l;const{max:r,messages:i=fl,fullDateFormatter:s=dl,units:a=cl,showSecond:u=!1,rounding:f="round"}=t,c=typeof f=="number"?p=>+p.toFixed(f):Math[f],d=+o-+e,h=Math.abs(d);function m(p,w){return c(Math.abs(p)/w.value)}function v(p,w){const y=m(p,w),g=p>0,T=b(w.name,y,g);return b(g?"past":"future",T,g)}function b(p,w,y){const g=i[p];return typeof g=="function"?g(w,y):g.replace("{0}",w.toString())}if(h<6e4&&!u)return i.justNow;if(typeof r=="number"&&h>r)return s(new Date(e));if(typeof r=="string"){const p=(l=a.find(w=>w.name===r))==null?void 0:l.max;if(p&&h>p)return s(new Date(e))}for(const[p,w]of a.entries()){if(m(d,w)<=0&&a[p-1])return v(d,a[p-1]);if(h<w.max)return v(d,w)}return i.invalid}function vl(e,t,o={}){const{immediate:l=!0,immediateCallback:r=!1}=o,{start:i}=S.useTimeoutFn(a,t,{immediate:l}),s=n.shallowRef(!1);async function a(){s.value&&(await e(),i())}function u(){s.value||(s.value=!0,r&&e(),i())}function f(){s.value=!1}return l&&S.isClient&&u(),S.tryOnScopeDispose(f),{isActive:s,pause:f,resume:u}}function pl(e={}){const{controls:t=!1,offset:o=0,immediate:l=!0,interval:r="requestAnimationFrame",callback:i}=e,s=n.shallowRef(S.timestamp()+o),a=()=>s.value=S.timestamp()+o,u=i?()=>{a(),i(s.value)}:a,f=r==="requestAnimationFrame"?Y(u,{immediate:l}):S.useIntervalFn(u,r,{immediate:l});return t?{timestamp:s,...f}:s}function hl(e=null,t={}){var o,l,r;const{document:i=j,restoreOnUnmount:s=d=>d}=t,a=(o=i?.title)!=null?o:"",u=S.toRef((l=e??i?.title)!=null?l:null),f=!!(e&&typeof e=="function");function c(d){if(!("titleTemplate"in t))return d;const h=t.titleTemplate||"%s";return typeof h=="function"?h(d):n.toValue(h).replace(/%s/g,d)}return n.watch(u,(d,h)=>{d!==h&&i&&(i.title=c(d??""))},{immediate:!0}),t.observe&&!t.titleTemplate&&i&&!f&&J((r=i.head)==null?void 0:r.querySelector("title"),()=>{i&&i.title!==u.value&&(u.value=c(i.title))},{childList:!0}),S.tryOnScopeDispose(()=>{if(s){const d=s(a,u.value||"");d!=null&&i&&(i.title=d)}}),u}const yl={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},wl=Object.assign({},{linear:S.identity},yl);function gl([e,t,o,l]){const r=(c,d)=>1-3*d+3*c,i=(c,d)=>3*d-6*c,s=c=>3*c,a=(c,d,h)=>((r(d,h)*c+i(d,h))*c+s(d))*c,u=(c,d,h)=>3*r(d,h)*c*c+2*i(d,h)*c+s(d),f=c=>{let d=c;for(let h=0;h<4;++h){const m=u(d,e,o);if(m===0)return d;const v=a(d,e,o)-c;d-=v/m}return d};return c=>e===t&&o===l?c:a(f(c),t,l)}function dt(e,t,o){return e+o*(t-e)}function Ve(e){return(typeof e=="number"?[e]:e)||[]}function mt(e,t,o,l={}){var r,i;const s=n.toValue(t),a=n.toValue(o),u=Ve(s),f=Ve(a),c=(r=n.toValue(l.duration))!=null?r:1e3,d=Date.now(),h=Date.now()+c,m=typeof l.transition=="function"?l.transition:(i=n.toValue(l.transition))!=null?i:S.identity,v=typeof m=="function"?m:gl(m);return new Promise(b=>{e.value=s;const p=()=>{var w;if((w=l.abort)!=null&&w.call(l)){b();return}const y=Date.now(),g=v((y-d)/c),T=Ve(e.value).map((k,C)=>dt(u[C],f[C],g));Array.isArray(e.value)?e.value=T.map((k,C)=>{var E,V;return dt((E=u[C])!=null?E:0,(V=f[C])!=null?V:0,g)}):typeof e.value=="number"&&(e.value=T[0]),y<h?requestAnimationFrame(p):(e.value=a,b())};p()})}function bl(e,t={}){let o=0;const l=()=>{const i=n.toValue(e);return typeof i=="number"?i:i.map(n.toValue)},r=n.ref(l());return n.watch(l,async i=>{var s,a;if(n.toValue(t.disabled))return;const u=++o;if(t.delay&&await S.promiseTimeout(n.toValue(t.delay)),u!==o)return;const f=Array.isArray(i)?i.map(n.toValue):n.toValue(i);(s=t.onStarted)==null||s.call(t),await mt(r,r.value,f,{...t,abort:()=>{var c;return u!==o||((c=t.abort)==null?void 0:c.call(t))}}),(a=t.onFinished)==null||a.call(t)},{deep:!0}),n.watch(()=>n.toValue(t.disabled),i=>{i&&(o++,r.value=l())}),S.tryOnScopeDispose(()=>{o++}),n.computed(()=>n.toValue(t.disabled)?l():r.value)}function Sl(e="history",t={}){const{initialValue:o={},removeNullishValues:l=!0,removeFalsyValues:r=!1,write:i=!0,writeMode:s="replace",window:a=A}=t;if(!a)return n.reactive(o);const u=n.reactive({});function f(){if(e==="history")return a.location.search||"";if(e==="hash"){const g=a.location.hash||"",T=g.indexOf("?");return T>0?g.slice(T):""}else return(a.location.hash||"").replace(/^#/,"")}function c(g){const T=g.toString();if(e==="history")return`${T?`?${T}`:""}${a.location.hash||""}`;if(e==="hash-params")return`${a.location.search||""}${T?`#${T}`:""}`;const k=a.location.hash||"#",C=k.indexOf("?");return C>0?`${a.location.search||""}${k.slice(0,C)}${T?`?${T}`:""}`:`${a.location.search||""}${k}${T?`?${T}`:""}`}function d(){return new URLSearchParams(f())}function h(g){const T=new Set(Object.keys(u));for(const k of g.keys()){const C=g.getAll(k);u[k]=C.length>1?C:g.get(k)||"",T.delete(k)}Array.from(T).forEach(k=>delete u[k])}const{pause:m,resume:v}=S.pausableWatch(u,()=>{const g=new URLSearchParams("");Object.keys(u).forEach(T=>{const k=u[T];Array.isArray(k)?k.forEach(C=>g.append(T,C)):l&&k==null||r&&!k?g.delete(T):g.set(T,k)}),b(g,!1)},{deep:!0});function b(g,T){m(),T&&h(g),s==="replace"?a.history.replaceState(a.history.state,a.document.title,a.location.pathname+c(g)):a.history.pushState(a.history.state,a.document.title,a.location.pathname+c(g)),v()}function p(){i&&b(d(),!0)}const w={passive:!0};O(a,"popstate",p,w),e!=="history"&&O(a,"hashchange",p,w);const y=d();return y.keys().next().value?h(y):Object.assign(u,o),u}function Rl(e={}){var t,o;const l=n.ref((t=e.enabled)!=null?t:!1),r=n.ref((o=e.autoSwitch)!=null?o:!0),i=n.ref(e.constraints),{navigator:s=q}=e,a=x(()=>{var b;return(b=s?.mediaDevices)==null?void 0:b.getUserMedia}),u=n.shallowRef();function f(b){switch(b){case"video":{if(i.value)return i.value.video||!1;break}case"audio":{if(i.value)return i.value.audio||!1;break}}}async function c(){if(!(!a.value||u.value))return u.value=await s.mediaDevices.getUserMedia({video:f("video"),audio:f("audio")}),u.value}function d(){var b;(b=u.value)==null||b.getTracks().forEach(p=>p.stop()),u.value=void 0}function h(){d(),l.value=!1}async function m(){return await c(),u.value&&(l.value=!0),u.value}async function v(){return d(),await m()}return n.watch(l,b=>{b?c():d()},{immediate:!0}),n.watch(i,()=>{r.value&&u.value&&v()},{immediate:!0}),S.tryOnScopeDispose(()=>{h()}),{isSupported:a,stream:u,start:m,stop:h,restart:v,constraints:i,enabled:l,autoSwitch:r}}function vt(e,t,o,l={}){var r,i,s;const{clone:a=!1,passive:u=!1,eventName:f,deep:c=!1,defaultValue:d,shouldEmit:h}=l,m=n.getCurrentInstance(),v=o||m?.emit||((r=m?.$emit)==null?void 0:r.bind(m))||((s=(i=m?.proxy)==null?void 0:i.$emit)==null?void 0:s.bind(m?.proxy));let b=f;t||(t="modelValue"),b=b||`update:${t.toString()}`;const p=g=>a?typeof a=="function"?a(g):te(g):g,w=()=>S.isDef(e[t])?p(e[t]):d,y=g=>{h?h(g)&&v(b,g):v(b,g)};if(u){const g=w(),T=n.ref(g);let k=!1;return n.watch(()=>e[t],C=>{k||(k=!0,T.value=p(C),n.nextTick(()=>k=!1))}),n.watch(T,C=>{!k&&(C!==e[t]||c)&&y(C)},{deep:c}),T}else return n.computed({get(){return w()},set(g){y(g)}})}function El(e,t,o={}){const l={};for(const r in e)l[r]=vt(e,r,t,o);return l}function Tl(e){const{pattern:t=[],interval:o=0,navigator:l=q}=e||{},r=x(()=>typeof l<"u"&&"vibrate"in l),i=S.toRef(t);let s;const a=(f=i.value)=>{r.value&&l.vibrate(f)},u=()=>{r.value&&l.vibrate(0),s?.pause()};return o>0&&(s=S.useIntervalFn(a,o,{immediate:!1,immediateCallback:!1})),{isSupported:r,pattern:t,intervalControls:s,vibrate:a,stop:u}}function Ol(e,t){const{containerStyle:o,wrapperProps:l,scrollTo:r,calculateRange:i,currentList:s,containerRef:a}="itemHeight"in t?Vl(t,e):_l(t,e);return{list:s,scrollTo:r,containerProps:{ref:a,onScroll:()=>{i()},style:o},wrapperProps:l}}function pt(e){const t=n.shallowRef(null),o=qe(t),l=n.ref([]),r=n.shallowRef(e);return{state:n.ref({start:0,end:10}),source:r,currentList:l,size:o,containerRef:t}}function ht(e,t,o){return l=>{if(typeof o=="number")return Math.ceil(l/o);const{start:r=0}=e.value;let i=0,s=0;for(let a=r;a<t.value.length;a++){const u=o(a);if(i+=u,s=a,i>l)break}return s-r}}function yt(e,t){return o=>{if(typeof t=="number")return Math.floor(o/t)+1;let l=0,r=0;for(let i=0;i<e.value.length;i++){const s=t(i);if(l+=s,l>=o){r=i;break}}return r+1}}function wt(e,t,o,l,{containerRef:r,state:i,currentList:s,source:a}){return()=>{const u=r.value;if(u){const f=o(e==="vertical"?u.scrollTop:u.scrollLeft),c=l(e==="vertical"?u.clientHeight:u.clientWidth),d=f-t,h=f+c+t;i.value={start:d<0?0:d,end:h>a.value.length?a.value.length:h},s.value=a.value.slice(i.value.start,i.value.end).map((m,v)=>({data:m,index:v+i.value.start}))}}}function gt(e,t){return o=>typeof e=="number"?o*e:t.value.slice(0,o).reduce((r,i,s)=>r+e(s),0)}function bt(e,t,o,l){n.watch([e.width,e.height,t,o],()=>{l()})}function St(e,t){return n.computed(()=>typeof e=="number"?t.value.length*e:t.value.reduce((o,l,r)=>o+e(r),0))}const kl={horizontal:"scrollLeft",vertical:"scrollTop"};function Rt(e,t,o,l){return r=>{l.value&&(l.value[kl[e]]=o(r),t())}}function _l(e,t){const o=pt(t),{state:l,source:r,currentList:i,size:s,containerRef:a}=o,u={overflowX:"auto"},{itemWidth:f,overscan:c=5}=e,d=ht(l,r,f),h=yt(r,f),m=wt("horizontal",c,h,d,o),v=gt(f,r),b=n.computed(()=>v(l.value.start)),p=St(f,r);bt(s,t,a,m);const w=Rt("horizontal",m,v,a),y=n.computed(()=>({style:{height:"100%",width:`${p.value-b.value}px`,marginLeft:`${b.value}px`,display:"flex"}}));return{scrollTo:w,calculateRange:m,wrapperProps:y,containerStyle:u,currentList:i,containerRef:a}}function Vl(e,t){const o=pt(t),{state:l,source:r,currentList:i,size:s,containerRef:a}=o,u={overflowY:"auto"},{itemHeight:f,overscan:c=5}=e,d=ht(l,r,f),h=yt(r,f),m=wt("vertical",c,h,d,o),v=gt(f,r),b=n.computed(()=>v(l.value.start)),p=St(f,r);bt(s,t,a,m);const w=Rt("vertical",m,v,a),y=n.computed(()=>({style:{width:"100%",height:`${p.value-b.value}px`,marginTop:`${b.value}px`}}));return{calculateRange:m,scrollTo:w,containerStyle:u,wrapperProps:y,currentList:i,containerRef:a}}function Fl(e={}){const{navigator:t=q,document:o=j}=e,l=n.ref(!1),r=n.shallowRef(null),i=ze({document:o}),s=x(()=>t&&"wakeLock"in t),a=n.computed(()=>!!r.value&&i.value==="visible");s.value&&(O(r,"release",()=>{var d,h;l.value=(h=(d=r.value)==null?void 0:d.type)!=null?h:!1},{passive:!0}),S.whenever(()=>i.value==="visible"&&o?.visibilityState==="visible"&&l.value,d=>{l.value=!1,u(d)}));async function u(d){var h;await((h=r.value)==null?void 0:h.release()),r.value=s.value?await t.wakeLock.request(d):null}async function f(d){i.value==="visible"?await u(d):l.value=d}async function c(){l.value=!1;const d=r.value;r.value=null,await d?.release()}return{sentinel:r,isSupported:s,isActive:a,request:f,forceRequest:u,release:c}}function Pl(e={}){const{window:t=A,requestPermissions:o=!0}=e,l=e,r=x(()=>{if(!t||!("Notification"in t))return!1;if(Notification.permission==="granted")return!0;try{const y=new Notification("");y.onshow=()=>{y.close()}}catch(y){if(y.name==="TypeError")return!1}return!0}),i=n.ref(r.value&&"permission"in Notification&&Notification.permission==="granted"),s=n.ref(null),a=async()=>{if(r.value)return!i.value&&Notification.permission!=="denied"&&await Notification.requestPermission()==="granted"&&(i.value=!0),i.value},{on:u,trigger:f}=S.createEventHook(),{on:c,trigger:d}=S.createEventHook(),{on:h,trigger:m}=S.createEventHook(),{on:v,trigger:b}=S.createEventHook(),p=async y=>{if(!r.value||!i.value)return;const g=Object.assign({},l,y);return s.value=new Notification(g.title||"",g),s.value.onclick=f,s.value.onshow=d,s.value.onerror=m,s.value.onclose=b,s.value},w=()=>{s.value&&s.value.close(),s.value=null};if(o&&S.tryOnMounted(a),S.tryOnScopeDispose(w),r.value&&t){const y=t.document;O(y,"visibilitychange",g=>{g.preventDefault(),y.visibilityState==="visible"&&w()})}return{isSupported:r,notification:s,ensurePermissions:a,permissionGranted:i,show:p,close:w,onClick:u,onShow:c,onError:h,onClose:v}}const Et="ping";function Fe(e){return e===!0?{}:e}function Cl(e,t={}){const{onConnected:o,onDisconnected:l,onError:r,onMessage:i,immediate:s=!0,autoConnect:a=!0,autoClose:u=!0,protocols:f=[]}=t,c=n.ref(null),d=n.ref("CLOSED"),h=n.ref(),m=S.toRef(e);let v,b,p=!1,w=0,y=[],g,T;const k=()=>{if(y.length&&h.value&&d.value==="OPEN"){for(const _ of y)h.value.send(_);y=[]}},C=()=>{g!=null&&(clearTimeout(g),g=void 0)},E=()=>{clearTimeout(T),T=void 0},V=(_=1e3,F)=>{C(),!(!S.isClient&&!S.isWorker||!h.value)&&(p=!0,E(),v?.(),h.value.close(_,F),h.value=void 0)},P=(_,F=!0)=>!h.value||d.value!=="OPEN"?(F&&y.push(_),!1):(k(),h.value.send(_),!0),I=()=>{if(p||typeof m.value>"u")return;const _=new WebSocket(m.value,f);h.value=_,d.value="CONNECTING",_.onopen=()=>{d.value="OPEN",w=0,o?.(_),b?.(),k()},_.onclose=F=>{if(d.value="CLOSED",l?.(_,F),!p&&t.autoReconnect&&(h.value==null||_===h.value)){const{retries:L=-1,delay:H=1e3,onFailed:W}=Fe(t.autoReconnect);typeof L=="number"&&(L<0||w<L)?(w+=1,g=setTimeout(I,H)):typeof L=="function"&&L()?g=setTimeout(I,H):W?.()}},_.onerror=F=>{r?.(_,F)},_.onmessage=F=>{if(t.heartbeat){E();const{message:L=Et,responseMessage:H=L}=Fe(t.heartbeat);if(F.data===n.toValue(H))return}c.value=F.data,i?.(_,F)}};if(t.heartbeat){const{message:_=Et,interval:F=1e3,pongTimeout:L=1e3}=Fe(t.heartbeat),{pause:H,resume:W}=S.useIntervalFn(()=>{P(n.toValue(_),!1),T==null&&(T=setTimeout(()=>{V(),p=!1},L))},F,{immediate:!1});v=H,b=W}u&&(S.isClient&&O("beforeunload",()=>V(),{passive:!0}),S.tryOnScopeDispose(V));const D=()=>{!S.isClient&&!S.isWorker||(V(),p=!1,w=0,I())};return s&&D(),a&&n.watch(m,D),{data:c,status:d,close:V,send:P,open:D,ws:h}}function Dl(e,t,o){const{window:l=A}=o??{},r=n.ref(null),i=n.shallowRef(),s=(...u)=>{i.value&&i.value.postMessage(...u)},a=function(){i.value&&i.value.terminate()};return l&&(typeof e=="string"?i.value=new Worker(e,t):typeof e=="function"?i.value=e():i.value=e,i.value.onmessage=u=>{r.value=u.data},S.tryOnScopeDispose(()=>{i.value&&i.value.terminate()})),{data:r,post:s,terminate:a,worker:i}}function Al(e,t){if(e.length===0&&t.length===0)return"";const o=e.map(i=>`'${i}'`).toString(),l=t.filter(i=>typeof i=="function").map(i=>{const s=i.toString();return s.trim().startsWith("function")?s:`const ${i.name} = ${s}`}).join(";"),r=`importScripts(${o});`;return`${o.trim()===""?"":r} ${l}`}function Ml(e){return t=>{const o=t.data[0];return Promise.resolve(e.apply(void 0,o)).then(l=>{postMessage(["SUCCESS",l])}).catch(l=>{postMessage(["ERROR",l])})}}function Il(e,t,o){const l=`${Al(t,o)}; onmessage=(${Ml})(${e})`,r=new Blob([l],{type:"text/javascript"});return URL.createObjectURL(r)}function Ll(e,t={}){const{dependencies:o=[],localDependencies:l=[],timeout:r,window:i=A}=t,s=n.ref(),a=n.ref("PENDING"),u=n.ref({}),f=n.ref(),c=(v="PENDING")=>{s.value&&s.value._url&&i&&(s.value.terminate(),URL.revokeObjectURL(s.value._url),u.value={},s.value=void 0,i.clearTimeout(f.value),a.value=v)};c(),S.tryOnScopeDispose(c);const d=()=>{const v=Il(e,o,l),b=new Worker(v);return b._url=v,b.onmessage=p=>{const{resolve:w=()=>{},reject:y=()=>{}}=u.value,[g,T]=p.data;switch(g){case"SUCCESS":w(T),c(g);break;default:y(T),c("ERROR");break}},b.onerror=p=>{const{reject:w=()=>{}}=u.value;p.preventDefault(),w(p),c("ERROR")},r&&(f.value=setTimeout(()=>c("TIMEOUT_EXPIRED"),r)),b},h=(...v)=>new Promise((b,p)=>{var w;u.value={resolve:b,reject:p},(w=s.value)==null||w.postMessage([[...v]]),a.value="RUNNING"});return{workerFn:(...v)=>a.value==="RUNNING"?(console.error("[useWebWorkerFn] You can only run one instance of the worker at a time."),Promise.reject()):(s.value=d(),h(...v)),workerStatus:a,workerTerminate:c}}function Nl(e={}){const{window:t=A}=e;if(!t)return n.shallowRef(!1);const o=n.ref(t.document.hasFocus()),l={passive:!0};return O(t,"blur",()=>{o.value=!1},l),O(t,"focus",()=>{o.value=!0},l),o}function xl(e={}){const{window:t=A,...o}=e;return Te(t,o)}function Wl(e={}){const{window:t=A,initialWidth:o=Number.POSITIVE_INFINITY,initialHeight:l=Number.POSITIVE_INFINITY,listenOrientation:r=!0,includeScrollbar:i=!0,type:s="inner"}=e,a=n.ref(o),u=n.ref(l),f=()=>{if(t)if(s==="outer")a.value=t.outerWidth,u.value=t.outerHeight;else if(s==="visual"&&t.visualViewport){const{width:d,height:h,scale:m}=t.visualViewport;a.value=Math.round(d*m),u.value=Math.round(h*m)}else i?(a.value=t.innerWidth,u.value=t.innerHeight):(a.value=t.document.documentElement.clientWidth,u.value=t.document.documentElement.clientHeight)};f(),S.tryOnMounted(f);const c={passive:!0};if(O("resize",f,c),t&&s==="visual"&&t.visualViewport&&O(t.visualViewport,"resize",f,c),r){const d=$("(orientation: portrait)");n.watch(d,()=>f())}return{width:a,height:u}}R.DefaultMagicKeysAliasMap=Qe,R.StorageSerializers=Se,R.TransitionPresets=wl,R.asyncComputed=Pe,R.breakpointsAntDesign=nn,R.breakpointsBootstrapV5=Zt,R.breakpointsElement=sn,R.breakpointsMasterCss=an,R.breakpointsPrimeFlex=rn,R.breakpointsQuasar=on,R.breakpointsSematic=ln,R.breakpointsTailwind=Qt,R.breakpointsVuetify=tn,R.breakpointsVuetifyV2=Ie,R.breakpointsVuetifyV3=en,R.cloneFnJSON=te,R.computedAsync=Pe,R.computedInject=Ot,R.createFetch=jn,R.createReusableTemplate=kt,R.createTemplatePromise=Vt,R.createUnrefFn=Ft,R.customStorageEventName=Re,R.defaultDocument=j,R.defaultLocation=Pt,R.defaultNavigator=q,R.defaultWindow=A,R.executeTransition=mt,R.formatTimeAgo=ft,R.getSSRHandler=ce,R.mapGamepadToXbox360Controller=oo,R.onClickOutside=Ct,R.onElementRemoval=ye,R.onKeyDown=At,R.onKeyPressed=Mt,R.onKeyStroke=re,R.onKeyUp=It,R.onLongPress=xt,R.onStartTyping=Ut,R.provideSSRWidth=Jt,R.setSSRHandler=yn,R.templateRef=$t,R.unrefElement=N,R.useActiveElement=Ae,R.useAnimate=Bt,R.useAsyncQueue=jt,R.useAsyncState=Me,R.useBase64=Gt,R.useBattery=Xt,R.useBluetooth=Kt,R.useBreakpoints=un,R.useBroadcastChannel=cn,R.useBrowserLocation=fn,R.useCached=dn,R.useClipboard=mn,R.useClipboardItems=vn,R.useCloned=pn,R.useColorMode=He,R.useConfirmDialog=gn,R.useCountdown=bn,R.useCssVar=ne,R.useCurrentElement=Ue,R.useCycleList=Sn,R.useDark=Rn,R.useDebouncedRefHistory=kn,R.useDeviceMotion=_n,R.useDeviceOrientation=je,R.useDevicePixelRatio=Vn,R.useDevicesList=Fn,R.useDisplayMedia=Pn,R.useDocumentVisibility=ze,R.useDraggable=Cn,R.useDropZone=Dn,R.useElementBounding=An,R.useElementByPoint=Mn,R.useElementHover=In,R.useElementSize=qe,R.useElementVisibility=Ye,R.useEventBus=Ln,R.useEventListener=O,R.useEventSource=xn,R.useEyeDropper=Wn,R.useFavicon=Hn,R.useFetch=Xe,R.useFileDialog=Yn,R.useFileSystemAccess=Xn,R.useFocus=Kn,R.useFocusWithin=eo,R.useFps=to,R.useFullscreen=no,R.useGamepad=lo,R.useGeolocation=ao,R.useIdle=so,R.useImage=co,R.useInfiniteScroll=fo,R.useIntersectionObserver=Ge,R.useKeyModifier=vo,R.useLocalStorage=po,R.useMagicKeys=ho,R.useManualRefHistory=Be,R.useMediaControls=go,R.useMediaQuery=$,R.useMemoize=bo,R.useMemory=So,R.useMounted=De,R.useMouse=Ze,R.useMouseInElement=et,R.useMousePressed=Eo,R.useMutationObserver=J,R.useNavigatorLanguage=To,R.useNetwork=tt,R.useNow=nt,R.useObjectUrl=Oo,R.useOffsetPagination=ko,R.useOnline=_o,R.usePageLeave=Vo,R.useParallax=Fo,R.useParentElement=Po,R.usePerformanceObserver=Co,R.usePermission=ie,R.usePointer=Ao,R.usePointerLock=Mo,R.usePointerSwipe=Io,R.usePreferredColorScheme=Lo,R.usePreferredContrast=No,R.usePreferredDark=xe,R.usePreferredLanguages=xo,R.usePreferredReducedMotion=Wo,R.usePreferredReducedTransparency=Ho,R.usePrevious=Uo,R.useRafFn=Y,R.useRefHistory=Ee,R.useResizeObserver=de,R.useSSRWidth=be,R.useScreenOrientation=lt,R.useScreenSafeArea=$o,R.useScriptTag=Bo,R.useScroll=Te,R.useScrollLock=zo,R.useSessionStorage=qo,R.useShare=Go,R.useSorted=Xo,R.useSpeechRecognition=Ko,R.useSpeechSynthesis=Jo,R.useStepper=Qo,R.useStorage=fe,R.useStorageAsync=Zo,R.useStyleTag=tl,R.useSupported=x,R.useSwipe=nl,R.useTemplateRefsList=ol,R.useTextDirection=ll,R.useTextSelection=rl,R.useTextareaAutosize=sl,R.useThrottledRefHistory=ul,R.useTimeAgo=ml,R.useTimeoutPoll=vl,R.useTimestamp=pl,R.useTitle=hl,R.useTransition=bl,R.useUrlSearchParams=Sl,R.useUserMedia=Rl,R.useVModel=vt,R.useVModels=El,R.useVibrate=Tl,R.useVirtualList=Ol,R.useWakeLock=Fl,R.useWebNotification=Pl,R.useWebSocket=Cl,R.useWebWorker=Dl,R.useWebWorkerFn=Ll,R.useWindowFocus=Nl,R.useWindowScroll=xl,R.useWindowSize=Wl,Object.keys(S).forEach(function(e){e!=="default"&&!Object.prototype.hasOwnProperty.call(R,e)&&Object.defineProperty(R,e,{enumerable:!0,get:function(){return S[e]}})})})(this.VueUse=this.VueUse||{},VueUse,Vue);