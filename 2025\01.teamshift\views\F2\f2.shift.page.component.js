const { ref, reactive, onMounted, toRefs, defineComponent, computed, nextTick, onUnmounted, watch } = Vue
import { queryF2TeamPlan, queryHeadData } from '../../api/teams/index.js'
import { calculateTableHeight } from '../../utils/common.js'
import SearchForm from './components/SearchForm.js'
import EsnCell from './components/EsnCell.js'
import TaskItem from './components/TaskItem.js'
import TeamPlanAdd from './components/team.plan.add.js'

// 主组件
export default defineComponent({
  name: 'F2ShiftPage',
  components: {
    SearchForm,
    TaskItem,
    TeamPlanAdd,
    EsnCell,
  },
  props: {
    strFlow: String,
    inputIdWo: String,
    inputPlanDate: String,
    inputIdMain: String,
    inputStrFlow: String,
    inputStrEsnType: String,
    inspectType: String,
    inputGroupType: String,
    businessType: Number,
  },
  setup(props) {
    const loading = ref(false)
    const state = reactive({
      columnList: [],
      showHeader: true,
      loadingData: false,
      tableMaxHeight: 850,
      tableData: [],
    })

    // 添加控制表头显示/隐藏的状态
    const isHeaderExpanded = ref(false)

    const teamPlanAddRef = ref(null)

    const teamPlanAddVisible = ref(false)
    // 打开班组计划批量新增弹窗
    const handleOpenTeamPlanAdd = () => {
      teamPlanAddVisible.value = true
    }
    // 切换表头显示/隐藏状态
    const toggleHeader = (value) => {
      if (value !== undefined) {
        isHeaderExpanded.value = value
      } else {
        isHeaderExpanded.value = !isHeaderExpanded.value
      }
    }

    // 判断是否是周末
    const isWeekend = (weekday) => {
      return weekday === '星期六' || weekday === '星期日'
    }

    const xTable = ref(null)
    const searchFormRef = ref(null)
    const teamInfoRef = ref(null)
    const tableContainerRef = ref(null)

    // 使用公共的计算表格高度方法
    const updateTableHeight = () => {
      const height = calculateTableHeight(tableContainerRef, 0, 30)
      if (height) {
        state.tableMaxHeight = parseInt(height)
      }
    }

    // 监听窗口大小变化
    const handleResize = () => {
      updateTableHeight()
    }

    // 监听表头展开/收起状态变化，重新计算表格高度
    watch(isHeaderExpanded, () => {
      nextTick(() => {
        updateTableHeight()
      })
    })

    const loadList = async () => {
      loading.value = true
      await initColumns()
      const data = await mockList()

      if (xTable.value) {
        await xTable.value.reloadData(data)
        loading.value = false
        // 数据加载完成后重新计算高度
        nextTick(() => {
          updateTableHeight()
        })
      }
    }

    const initColumns = async () => {
      const searchParams = searchFormRef.value?.searchParams
      const params = {
        str_flow: props.inputStrFlow,
        start_date: (searchParams.dt_date && searchParams.dt_date[0]) || moment().format('YYYY-MM-DD'),
        end_date: (searchParams.dt_date && searchParams.dt_date[1]) || moment().add(14, 'days').format('YYYY-MM-DD'),
      }
      try {
        const response = await queryHeadData(params)
        state.columnList = response
      } catch (error) {
        console.error(error)
      }
    }

    const deptId = ref('')
    const teamId = ref('')
    const mockList = async () => {
      state.loadingData = true
      const searchParams = searchFormRef.value?.searchParams
      try {
        const params = {
          start_date: (searchParams.dt_date && searchParams.dt_date[0]) || moment().format('YYYY-MM-DD'),
          end_date: (searchParams.dt_date && searchParams.dt_date[1]) || moment().add(14, 'days').format('YYYY-MM-DD'),
          str_esn: searchParams.str_esn,
          id_staff: searchParams.id_staff,
          id_task: searchParams.id_task,
          id_shift: searchParams.id_shift,
          str_task_type: searchParams.str_task_type,
        }

        const response = await queryF2TeamPlan(params)
        state.tableData = response?.team_plans || []
        teamId.value = response?.id_team
        deptId.value = response?.id_dept
        state.loadingData = false
      } catch (error) {
        console.error(error)
        state.loadingData = false
      }
    }

    const handleHeaderCellClassName = ({ column }) => {
      // 只处理展开状态下的周末列
      if (column && column.title) {
        if (column.title === '星期六' || column.title === '星期日') {
          return 'bg-weekend'
        }
      }
      return ''
    }

    const handleSearch = (searchParams) => {
      state.searchParams = searchParams
      loadList()
    }

    const isExistData = (row, column) => {
      const planList = row.plans || []
      const planDay = column.day
      const taskIndex = planList.findIndex((item) => item.dt_shift === planDay)
      return taskIndex !== -1
    }

    onMounted(() => {
      updateTableHeight()
      loadList()
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', handleResize)
    })

    const handleRefresh = () => {
      handleSearch(state.searchParams)
    }

    return {
      ...toRefs(state),
      loading,
      deptId,
      teamId,
      handleSearch,
      handleHeaderCellClassName,
      isExistData,
      searchFormRef,
      teamInfoRef,
      tableContainerRef,
      handleRefresh,
      isHeaderExpanded,
      toggleHeader,
      isWeekend,
      teamPlanAddRef,
      teamPlanAddVisible,
      handleOpenTeamPlanAdd,
    }
  },
  template: /*html*/ `
    <div class="p-4">
      <SearchForm
        ref="searchFormRef"
        @search="handleSearch"
        @add="handleOpenTeamPlanAdd"
        @toggle-header="toggleHeader"
      />

      <div ref="tableContainerRef">
        <vxe-table
          :data="tableData"
          ref="xTable"
          border
          :header-cell-class-name="handleHeaderCellClassName"
          :header-cell-style="{color:'#fff',border:'0.01rem solid #fff'}"
          :column-config="{resizable: true,minWidth:170}"
          :loading="loadingData"
          :height="tableMaxHeight"
          :show-header="true"
        >
        <template>
          <!-- 固定列 -->
          <vxe-column type="seq" title="序号" width="60" align="center" fixed="left"></vxe-column>
          <vxe-column title="Team" field="str_team" width="100" align="center" fixed="left">
            <template #default="{ row }">
              <EsnCell :row="row" />
            </template>
          </vxe-column>
          <vxe-column title="Name" field="str_staff_name" width="100" align="center" fixed="left"></vxe-column>
          <vxe-colgroup v-for="(column, index) in columnList" :key="index" :title="column.str_week">
            <!-- 星期 -->
            <vxe-column :title="column.day">
              <template #default="{ row }">
                <!-- 是否有数据 -->
                <div v-if="isExistData(row, column)" class="h-full w-full">
                  <TaskItem
                    :row="row"
                    :deptId="deptId"
                    :flow="inputStrFlow"
                    :teamId="teamId"
                    :column="column"
                    :businessType="businessType"
                    @refresh="handleRefresh"
                  />
                </div>
                <div v-else class="h-full min-h-[60px] w-full"></div>
              </template>
            </vxe-column>
          </vxe-colgroup>
        </template>
        </vxe-table>
      </div>

      <TeamPlanAdd
        v-if="teamPlanAddVisible"
        ref="teamPlanAddRef"
        v-model:visible="teamPlanAddVisible"
        :deptId="deptId"
        :teamId="teamId"
        @refresh="handleRefresh"
      />
    </div>
  `,
})
