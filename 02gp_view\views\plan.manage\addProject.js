import {queryProjectById, saveProject, updateProject} from '../../api/calculation.js';
import {useAddProject} from "./useAddProject.js";

const AddProject = {
    props: {
        id: {
            type: String,
            default: '',
        },
    },
    setup(props) {
        const {handleNewProjectParams, handleProjectData, getEngineList, getProjectList, getTemplateList, optionState} = useAddProject();
        const radioValue = Vue.ref('1');
        const changeRadioValue = (value) => {
            radioValue.value = value;
            // 如果选择已有Project计划，获取Project计划列表
            if (value === '2') {
                // TODO 待完善
            }
        };

        const newProjectRef = Vue.ref(null);
        // 新建Project项目Form
        const newProjectForm = Vue.reactive({});
        // 新建Project项目Form校验规则
        const newProjectRules = {
            name: [
                {required: true, message: '请输入Project名称', trigger: 'blur'},
                //   长度限制2-20个字符
                {min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur'},
            ],
            startDate: [
                {required: true, message: '请选择计划开始时间', trigger: 'change'},
            ],
            endDate: [
                {required: true, message: '请选择计划结束时间', trigger: 'change'},
            ],
        };
        // 现有的Project计划Form
        const existingProjectForm = Vue.reactive({});

        // 校验新建Project项目
        const validateNewProject = async () => {
            return await newProjectRef.value.validate();
        };

        // 添加project
        const addAndEditProject = async () => {
            let params = {
                str_type: 'F4',
            };
            // 如果选择新建Project项目，校验新建Project项目
            if (newProjectRef.value) {
                const valid = await validateNewProject();
                if (!valid) {
                    return;
                }
                _.assign(params, handleNewProjectParams(newProjectForm));
            }
            if (newProjectForm.id) {
                return await updateProject(params);
            }
            // 如果选择现有Project计划，校验现有Project计划
            // TODO 待完善
            return await saveProject(params);
        };

        // 获取project详情
        const getProjectDetail = async (id) => {
            const res = await queryProjectById(id);
            if (!res) {
                return
            }
            _.assign(newProjectForm, handleProjectData(res));
        }

        Vue.onMounted(async () => {
            await getEngineList();
            if (props.id) {
                await getProjectDetail(props.id);
            }
        });

        return {
            radioValue,
            newProjectRef,
            newProjectForm,
            newProjectRules,
            existingProjectForm,
            optionState,
            getTemplateList,
            addAndEditProject,
            getProjectDetail
        };
    },
    template: `
      <!--    <div class="flex items-center justify-center gap-2 m-2">-->
      <!--      <el-radio-group v-model="radioValue" @change="changeRadioValue">-->
      <!--        <el-radio label="1">新建Project项目</el-radio>-->
      <!--        <el-radio label="2">使用现有Project计划</el-radio>-->
      <!--      </el-radio-group>-->
      <!--    </div>-->
      <div v-if="radioValue === '1'" class="m-4">
        <el-form ref="newProjectRef" :model="newProjectForm" label-width="120px" class="grid grid-cols-2"
                 :rules="newProjectRules">
          <el-form-item label="Project名称" prop="name" class="col-span-2">
            <el-input v-model="newProjectForm.name" placeholder="请输入Project名称"></el-input>
          </el-form-item>
          <el-form-item label="发动机" class="col-span-2">
            <el-select v-model="newProjectForm.engine" placeholder="请选择发动机" value-key="id_wo"
                       @change="(item) => getTemplateList(item.engineType)">
              <el-option v-for="item in optionState.engineList" :key="item.id_wo" :label="item.label"
                         :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="计划开始时间" prop="startDate">
            <el-date-picker v-model="newProjectForm.startDate" type="date" placeholder="选择日期"
                            value-format="YYYY-MM-DD"></el-date-picker>
          </el-form-item>
          <el-form-item label="计划结束时间" prop="endDate">
            <el-date-picker v-model="newProjectForm.endDate" type="date" placeholder="选择日期"
                            value-format="YYYY-MM-DD"></el-date-picker>
          </el-form-item>
          <el-form-item label="是否关联MP" prop="isMp">
            <el-checkbox v-model="newProjectForm.ismp" :true-value="1" :false-value="0"></el-checkbox>
          </el-form-item>
          <el-form-item label="是否关联RID" prop="isRid">
            <el-checkbox v-model="newProjectForm.isRid" :true-value="1" :false-value="0"></el-checkbox>
          </el-form-item>
          <el-form-item label="模板" class="col-span-2">
            <el-select v-model="newProjectForm.templateId" placeholder="请选择模板">
              <el-option v-for="item in optionState.templateList" :key="item.id" :label="item.label"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div v-else-if="radioValue === '2'">
        <el-form :model="existingProjectForm" label-width="120px">
          <el-form-item label="Project名称">
            <el-select v-model="existingProjectForm.projectName" placeholder="请选择Project名称">
              <el-option label="Project1" value="1"></el-option>
              <el-option label="Project2" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="计划开始时间">
            <el-date-picker v-model="existingProjectForm.startDate" type="date" placeholder="选择日期"
                            value-format="YYYY-MM-DD"></el-date-picker>
          </el-form-item>
          <el-form-item label="计划结束时间">
            <el-date-picker v-model="existingProjectForm.endDate" type="date" placeholder="选择日期"
                            value-format="YYYY-MM-DD"></el-date-picker>
          </el-form-item>
          <el-form-item label="模板">
            <el-select v-model="existingProjectForm.template" placeholder="请选择模板">
              <el-option label="模板1" value="1"></el-option>
              <el-option label="模板2" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    `,
};
export default AddProject;
