/**
 * 计算表格高度
 * @param {object} tableContainerRef 表格容器
 * @param {number} paginationHeight 分页器高度
 * @param {number} bottomMargin 底部边距
 * @returns {string} 高度
 */
export const calculateTableHeight = (tableContainerRef, paginationHeight = 60, bottomMargin = 80) => {
  // 获取窗口高度
  let windowHeight = window.innerHeight

  // 检查是否在iframe中
  const isInIframe = window !== window.parent

  if (isInIframe) {
    try {
      // 尝试获取iframe的高度
      const iframe = window.frameElement
      if (iframe) {
        windowHeight = iframe.clientHeight
      }
    } catch (e) {
      console.error('获取iframe高度失败:', e)
    }
  }

  // 获取表格容器元素
  const tableContainer = tableContainerRef.value
  if (!tableContainer) return

  // 获取表格容器到视口顶部的距离
  const containerTop = tableContainer.getBoundingClientRect().top

  // 计算表格可用高度 (窗口高度 - 表格顶部位置 - 分页器高度 - 底部边距)
  const availableHeight = windowHeight - containerTop - paginationHeight - bottomMargin

  // 设置最小高度
  const minHeight = 300

  return `${Math.max(availableHeight, minHeight)}px`
}
