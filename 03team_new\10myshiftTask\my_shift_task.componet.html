<!-- StringBuilder strhtml = new StringBuilder(); strhtml.Append(@" -->
<!DOCTYPE html>
<html lang='en'>

<head>
    <meta charset='UTF-8'>
    <meta content='IE=edge' http-equiv='X-UA-Compatible'>
    <meta content='width=device-width, initial-scale=1.0' name='viewport'>
    <title> 我的班次任务 </title>
   <link rel='stylesheet' href='/Content/assets_huatek/wepapp/assets/css/index.css'>
    <script src='/Content/assets_huatek/webapp/assets/jquery/jquery.js'></script>
    <script src='/Content/assets_huatek/webapp/assets/vue/vue.js'></script>
    <script src='/Content/assets_huatek/webapp/assets/element/index.js'></script>
    <script src='/Content/assets_huatek/webapp/assets/axios/axios.min.js'></script>

    <script src='/Content/assets_huatek/webapp/03team_new/comm/api_environment.js'></script>
    <script src='/Content/assets_huatek/webapp/assets/echarts/echarts.min.js'></script>
    <link href='/Content/assets_huatek/webapp/assets/element/index.css' rel='stylesheet'>
    <link href='/Content/assets_huatek/webapp/assets/css/el.dialog.css' rel='stylesheet'>
    <link href='/Content/assets_huatek/webapp/assets/css/table.self.css' rel='stylesheet'>
    <link href='/Content/assets_huatek/webapp/assets/css/myshifttask.index.self.css' rel='stylesheet'>

    <script src='/Content/assets_huatek/webapp/03team_new/10myshiftTask/my_shift_task.componet.js'></script>
  <script src='/Content/assets_huatek/webapp/assets/moment/moment.min.js'></script>
    <!-- <link href='/Content/assets_huatek/wepapp//Content/assets_huatek/wepapp/teamshift/asset/css/vg_hsg_sm.compoent.css' rel='stylesheet'> -->
</head>

<body>
<div id='app'>
    <!-- 1：任务管理 2：责任人处理 3：sms处理 4：审批管理 -->
    <y-shift-task-page ></y-shift-task-page>
</div>

<script>
  var vue1 = new Vue({
    el: '#app',
    data: function () {
      return {
        id_main: ''
      }
    },
    methods: {
      backCall() {

      }
    }
  })
</script>
</body>


</html>
<!-- "); arg.redata = strhtml.ToString(); return arg.redata; -->
