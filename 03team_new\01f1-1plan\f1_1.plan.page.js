Vue.component('y-pt-allot-page', {
	props: ['input_id_main', 'input_page_type', 'input_active_name','input_group_type'], // input_page_type：页面类型 1：任务管理，2：责任人处理，3：sms 4:审批
	data: function () {
		return {
			id_main: '',

			/**选择的审批 id:主数据ID */
			audit_ids: [],
			select_str_code: '', // 选择的工作指令
			select_str_code_count: 0, // 选择的工作指令 个数
			// 总数据
			tableData: [],
			wip_wo: [],
			wip_wo_t: [], //  未关闭的发动机
			// 默认显示第几页
			currentPage: 1,
			// 总条数，根据接口获取数据长度(注意：这里不能为空)
			totalCount: 0,
			// 个数选择器（可修改）
			pageSizes: [5, 10, 20, 30],
			// 默认每页显示的条数（可修改）
			PageSize: 10,
			queryParams: {
				allot_page_type: this.input_page_type,
				str_group_type:this.input_group_type
				// str_esn_type:"CFM"
			},
			currentRow: null, // 当前行
			is_allot_show: false,
			echartBoxHeight: '400',
			form_allot: {},
			allot_table: [], //  固定类别分配任务
			teams: [],// team
			id_wo_select: "",
			str_flow_select: "",// 选择的Flow
			str_flow_esn: "",// 选择的Flow
			id: "", // 编辑数据id

			flow_type: [{
				flow: "F1-1",
				types: ["B1", "B2/3"] //"BSI",
			}, {
				flow: "F1-2",
				types: ["Cleaning", "TI", "NDT"]
			}, {
				flow: "F4-1",
				types: ["FAN", "Core", "LPT", "B1"]
			}, {
				flow: "F4-2",
				types: ["Core", "LPT", "FAN", "B1"]
			}, {
				flow: "F4-3",
				types: ["Core", "LPT", "FAN", "B1"]
			}],
			date_max: null, //Core,FAN,LPT选择发动机的最大时间
			date_b1_max: null,//B1选择发动机的最大之间
			
			task_data_obery:[], // e_obery 所有任务
			dialogData: null, // 弹框数据
			wip_filters: [
				{text: 'F0',value: 'F0'},{text: 'F1-1',value: 'F1-1'},{text: 'F1-2',value: 'F1-2'},{text: 'F2',value: 'F2'},
				{text: 'F3',value: 'F3'},{text: 'F4-1',value: 'F4-1'},{text: 'F4-2',value: 'F4-2'},{text: 'F4-3',value: 'F4-3'}
			],
			wip_flow: [],
			loading: null,
			searchInput: ''
		}
	},
	created: function () {
		this.str_flow_select = this.input_page_type
		Promise.all([this.get_task_list()]).then(() => {
		})
		this.getData(this.PageSize, this.currentPage);
		// Promise.all([this.getData(this.PageSize, this.currentPage)]).then(() => {
			
		// });

	},
	mounted() {
		this.get_team()
		//this.get_staff()
		this.str_flow_select = this.input_page_type;
		this.echartBoxHeight = $(window).height() - 200;

	},
	methods: {
		/**获取所有project 任务 */
		async get_task_list() {

			let _this = this;
			// _this.loading = _this.$loading({
            //     Lock: true,
            //     text: "Proofread in project ...",
            //     spinner: 'el-icon-loading',
			// 	background: "rgba(0,0,0,0.35)"
            // });
			await axios
				.post(globalApiUrl, {
					au: "ssamc",
					ap: "api2018",
					ak: "",
					ac: "e_obeya2_api_plan_QueryTaskGroupAndUnitList",
				})
				.then(function (response) {
					_this.task_data_obery = response.data?.data || [];
					_this.pt_excute_pt_task_allot(); // 判断Eobery 变化
				})
				.catch(function (error) {

					console.log(error);
				});


		},
		/**对比project 任务变化*/
		async pt_excute_pt_task_allot() {

			let _this = this;
			await axios
				.post(globalApiUrl, {
					au: "ssamc",
					ap: "api2018",
					ak: "",
					ac: "pt_excute_pt_task_allot",
					project_info:_this.task_data_obery,
					str_flow:_this.str_flow_select
				})
				.then(function (response) {				
					//_this.getData(_this.PageSize, _this.currentPage);

				})
				.catch(function (error) {

					console.log(error);
				});


		},
		// 将页码，及每页显示的条数以参数传递提交给后台
		async getData(n1, n2) {
			let _this = this
			// 这里使用axios，使用时请提前引入
			_this.loading = _this.$loading({
				Lock: true,
				text: 'loading...',
				spinner: 'el-icon-loading',
				background: 'rgba(0,0,0,0.35)'
			  })
			await axios
				.post(
					globalApiUrl,
					{
						au: 'ssamc',
						ap: 'api2018',
						ak: '',
						ac: 'pt_get_f1_1_project_page',
						orgCode: 1,
						// 每页显示的条数
						PageSize: n1,
						// 显示第几页
						currentPage: n2,
						// 查询参数
						Filter: _this.queryParams,
						wip_flow: _this.wip_flow,
						esn: _this.searchInput,
						project_info:'',
					},
					{
						emulateJSON: true,
					},
					{
						headers: {
							'Content-Type':
								'application/x-www-form-urlencoded;charset=utf-8',
						},
					}
				)
				.then((reponse) => {
					// 将数据赋值给tableData
					_this.tableData = reponse.data.data?.page?.items;
					// this.tableData.forEach(item=>{
					// 	item.allots.forEach(allort=>{
					// 		if(allort.id_teams!=null){
					// 			allort.id_teams=allort.id_teams.split(",") || "";
					// 		}
					// 	})

					// })
					// 将数据的长度赋值给totalCount
					if (reponse.data.code === 'success') {
						_this.totalCount = reponse.data.data?.page?.totalCount || 0
						_this.loading.close()
					} else {
						_this.$message({
							message: reponse.data.text,
							type: 'error',
							duration: 1000
						})
						_this.loading.close()
					}
				})
		},

		// 分页
		// 每页显示的条数
		handleSizeChange(val) {
			// 改变每页显示的条数
			this.PageSize = val
			// 点击每页显示的条数时，显示第一页
			this.getData(val, 1)
			// 注意：在改变每页显示的条数时，要将页码显示到第一页
			this.currentPage = 1
		},
		// 显示第几页
		handleCurrentChange(val) {
			// 改变默认的页数
			this.currentPage = val
			// 切换页码时，要获取每页显示的条数
			this.getData(this.PageSize, val)
			// this.getData(this.PageSize, (val) * (this.PageSize))
		},

		//搜索查询
		doQuery() {
			let _this = this;
			_this.getData(this.PageSize, this.currentPage)
		},
		handleRowCurrentChange(val) {
			this.currentRow = val;
		},
		async get_team() {
			let _this = this;
			const params = {
				au: "ssamc",
				ap: "api2018",
				ak: "",
				ac: "pt_get_teams"
			}
			const { data } = await axios.post(globalApiUrl, params)
			_this.teams = data.data.data || [];
		},
	
		/**编辑 */
		editRow(row) {
			let _this = this
			if (_this.teams && _this.teams.length) {
				_this.dialogData = row
				_this.is_allot_show = true;
				_this.allot_table = row.allots;
				_this.allot_table.forEach(element => {
					if (element.id_teams != null && element.id_teams !== "" && !Array.isArray(element.id_teams)) {
						element.id_teams = element.id_teams.split(",") || "";
					}
					if (element.str_id_surveys != null && element.str_id_surveys !== "" && !Array.isArray(element.str_id_surveys)) {
						element.str_id_surveys = element.str_id_surveys.split(",") || "";
					}else{
						element.str_id_surveys=["1435414238138601473"];//没有数据默认向兴阳
					}
					if(element.dt_start_project ==null ){
						element.dt_start_project=row.main.dt_f11_begin						
						const currentDate = new Date(row.main.dt_f11_begin);
						const currentTime = currentDate.getTime();
						const oneDayMilliseconds = 24 * 60 * 60 * 1000;
						debugger;
						if(element.str_type=="B2/3"){
							const nextDate = new Date(currentTime +oneDayMilliseconds);
							const month = nextDate.getMonth()+1;
							if(month<10){
								element.dt_start_project=nextDate.getFullYear()+'-0'+month+'-'+nextDate.getDate()
							}else{
								element.dt_start_project=nextDate.getFullYear()+'-'+month+'-'+nextDate.getDate()
							}
							
						}
						if(element.dt_end_project ==null && row.main.type =="OH"){
							const nextDate = new Date(currentTime + oneDayMilliseconds*7);
							const month = nextDate.getMonth()+1;
							if(month<10){
								element.dt_end_project=nextDate.getFullYear()+'-0'+month+'-'+nextDate.getDate()
							}else{
								element.dt_end_project=nextDate.getFullYear()+'-'+month+'-'+nextDate.getDate()
							}
						}
						if(element.dt_end_project ==null && row.main.type =="SV"){
							const nextDate = new Date(currentTime + oneDayMilliseconds*5);
							const month = nextDate.getMonth()+1;
							if(month<10){
								element.dt_end_project=nextDate.getFullYear()+'-0'+month+'-'+nextDate.getDate()
							}else{
								element.dt_end_project=nextDate.getFullYear()+'-'+month+'-'+nextDate.getDate()
							}
						}
					}

				});
			}
		},
		/**删除数据 */
		delete_allot(row) {
			let _this = this;
			_this.$confirm('此操作将清除分配数据, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {

				axios
					.post(globalApiUrl, {
						au: "ssamc",
						ap: "api2018",
						ak: "",
						ac: "pt_delete_project_allot",
						id: row.main.id
					})
					.then(function (response) {
						_this.$message({
							type: 'success',
							message: '删除成功!'
						});
						_this.doQuery();

					})
					.catch(function (error) {
						console.log(error);
					});

			}).catch(() => {

			});
		},
		/**关闭弹 */
		closeDialog() {
			let _this = this;
			_this.is_allot_show = false;
			_this.allot_table = [];
			_this.id_wo_select = null;
			_this.str_flow_esn = null;
			_this.date_max = null;
			_this.date_b1_max = null;
		},
		
		getTypeCellClassName(item) {
			if(item.is_change_type) {
				return 'bgPurple'
			}
		},
		getTimeCellClassName(item) {
			if(item.is_change_date) {
				return 'bgPurple'
			}
		},
		closeDialogHandler() {
			let _this = this;
			_this.dialogData= {}
			_this.allot_table = [];
			_this.doQuery()
			_this.is_allot_show = false
		},
		saveHandler () {
			let _this = this
			_this.closeDialogHandler();
			_this.$message({
				type: "success",
				message: "Save success",
			});
			_this.doQuery()
		},
		filterTableHandler(filter) {
			let _this = this
			if(filter.wip_flow) {
				_this.wip_flow = filter.wip_flow
				_this.doQuery()
			}
		},
		searchHandler() {
			let _this = this
			_this.getData(this.PageSize, 1)
		}

	},


	template: `
    <div>
		<div class='pading-page-com' v-if="select_str_code_count>0">
			<div class='searchBox searchY'>
			  <span>Select (<span  style="color:red">{{select_str_code_count}}</span>) :</span> 	  <span >{{select_str_code}}</span>   
			</div>
		</div>
		<el-row style="margin-bottom: 6px" type="flex" justify="end">
			<el-col :span="6">
				<el-input 
					v-model="searchInput" 
					size="small"
					placeholder="WO/ESN" 
					clearable
				></el-input>
			</el-col>
			<el-col :span="1" :offset="1">
				<el-button 
					type="primary" 
					size="small"
					icon="el-icon-search" 
					@click="searchHandler"
				></el-button>
			</el-col>
		</el-row>
		<div class='pading-page-com'>
			<div class="pageY">
				<el-table
					ref="multipleTable" 
					:data='tableData' 
					tooltip-effect='dark' 
					:header-cell-style="{background:'#606266',color:'#fff'}" 
					border 
					style="width: 100%"
					highlight-current-row
					@filter-change='filterTableHandler'
					@current-change="handleRowCurrentChange"
				>	
					<el-table-column label='WO' prop="main.str_code" width="100" >
			
					</el-table-column>
						
					<el-table-column label='ESN' prop="main.str_esn" width="80" >
			
					</el-table-column>
			
					<el-table-column label='F1-1 Close' prop="main.dt_f11_close"  >
			
					</el-table-column> 
					<el-table-column label='Level' prop="main.type"  >
					
					</el-table-column> 
					<el-table-column 
						label='WIP FLOW' 
						prop="main.wip_flow"
						:filters='wip_filters'
						column-key='wip_flow'
					></el-table-column> 
					<el-table-column label='Type'>
						<template slot-scope='scope'>
							<el-row v-for='(item,index) in scope.row.allots' :key="index">
								<div :class='getTypeCellClassName(item)'>
									<span class='newheadtitle'>{{index+1}}:{{item.str_type}}</span>
								</div>
							</el-row>
						</template>
					</el-table-column> 
						
						<el-table-column label='Start'>
							<template slot-scope='scope'>
								<el-row v-for='(item,index) in scope.row.allots' :key="index">
									<div :class='getTimeCellClassName(item)'>
										<span class='newheadtitle'>{{index+1}}:{{item.dt_start_project}}</span>
									</div>						
								</el-row>
							</template>
						</el-table-column> 
						<el-table-column label='End'>
							<template slot-scope='scope'>
								<el-row v-for='(item,index) in scope.row.allots' :key="index">
									<div :class='getTimeCellClassName(item)'>
										<span class='newheadtitle'>{{index+1}}:{{item.dt_end_project}}</span>
									</div>						
								</el-row>
							</template>
						</el-table-column> 						
						
			
						<el-table-column fixed='right' label='Operate' width='180' align="center">
							<template slot-scope='scope'>
								<el-button @click.native.stop='editRow(scope.row)' type='text' size='mini'>Edit</el-button>
								<el-divider direction="vertical" content-position="center"/>
								<el-button @click.native.stop='delete_allot(scope.row)' type='text' size='mini'>Reset</el-button>
								
							</template>
						</el-table-column>
				</el-table>
		</div>
			<div class='tabListPage'>
				<el-pagination @size-change='handleSizeChange' background @current-change='handleCurrentChange'
					:current-page='currentPage' :page-sizes='pageSizes' :page-size='PageSize'
					layout='total, sizes, prev, pager, next, jumper' :total='totalCount'>
				</el-pagination>
			</div>
		</div>
		<div v-if="is_allot_show">
			<allot-edit-page 
				:is_allot_show.sync='is_allot_show' 
				:input_page_type='input_page_type'
				:dialogData='dialogData'
				:allot_table="allot_table"
				@close-dialog='closeDialogHandler'
				@save='saveHandler'
			></allot-edit-page>
		</div>
	</div>

    `,
})
