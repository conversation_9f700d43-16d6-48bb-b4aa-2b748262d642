import { post } from '../../../../config/axios/httpReuest.js'
import { useOption } from '../../../../09deduce/hooks/useOption.js'
// 引入单元体散点图组件
import UnitScatterChart from './unitScatterChart.js'
import { useFilter } from '../../../../09deduce/hooks/useFilter.js'
import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
import ReasonRemarkDialog from '../../../../09deduce/views/components/ReasonRemarkDialog.js'
import { useReasonRemark } from '../../../../09deduce/views/hooks/useReasonRemark.js'
const { ref, reactive, onMounted, toRaw } = Vue
const { useVModel } = VueUse
const UnitPlanDrawer = {
  components: {
    UnitScatterChart,
    HtVxeTable,
    ReasonRemarkDialog,
  },
  props: {
    visible: Boolean,
    id: {
      type: String,
      required: true,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const { recombineFilter } = useFilter()
    const visible = useVModel(props, 'visible', emit)

    // 查询条件
    const unitGroupForm = reactive({})

    // 重置查询条件
    const restForm = () => {
      Object.keys(unitGroupForm).forEach((key) => {
        unitGroupForm[key] = ''
      })
      getUnitPlan()
    }

    const unitGroupList = ref([])
    const parentFilterFields = ref([])
    const parentIntEkdType = ref(null)
    let allData = []
    /**
     * 获取单元计划
     * @param {number} intEkdType  0推演前 1推演后 2模拟前 3模拟后
     */
    const getUnitPlan = async (intEkdType = null) => {
      unitGroupList.value = []
      parentFilterFields.value = recombineFilter(unitGroupForm)
      parentIntEkdType.value = intEkdType
      const params = {
        ac: 'de_sm_group_by_idwo',
        id_wo: props.id,
        filter_fields: parentFilterFields.value,
        int_ekd_type: intEkdType,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        unitGroupList.value = data.data
          .map((item) => {
            // 按照CORE FAN LPT B1 排序
            // * 当item.str_group为core时，返回1，排在1
            // * 当item.str_group为fan时，返回2，排在2
            // * 当item.str_group为lpt时，返回3，排在3
            // * 当item.str_group为b1时，返回4，排在4
            const order =
              item.str_group === 'ESN'
                ? 1
                : item.str_group === 'B1'
                  ? 2
                  : item.str_group === 'CORE'
                    ? 3
                    : item.str_group === 'FAN'
                      ? 4
                      : 5
            return {
              ...item,
              order,
            }
          })
          .sort((a, b) => a.order - b.order)
        allData = toRaw(unitGroupList.value)
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    // 左侧蓝色框中的颜色
    const colors = {
      红色: 'int_red',
      绿色: 'int_green',
      蓝色: 'int_blue',
      紫色: 'int_purple',
      红框后零件: 'int_after_red',
    }
    const colorKey = {
      红色: 1,
      绿色: 3,
      蓝色: 2,
      紫色: 4,
      红框后零件: 110,
    }

    const titleByKey = {
      红色: '不能串',
      绿色: '可串件',
      蓝色: '有条件串',
      紫色: '不具备串件条件',
      红框后零件: '红框后零件',
    }

    const partDrawer = reactive({
      visible: false,
      title: '',
    })

    const handleColorClick = async (oneItem, key) => {
      // 打开弹框
      partDrawer.visible = true
      partDrawer.title = `${titleByKey[key]}零件列表`
      const { id_wo, str_group } = oneItem
      const params = {
        id_wo,
        str_group,
        int_point_type: colorKey[key],
      }
      await getPartList(params)
    }
    const tableRef = ref(null)
    // 表格数据
    const tableState = reactive({
      data: null,
      columns: [
        {
          title: '站点名称',
          field: 'str_nodename',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '目标WO',
          field: 'str_code',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '目标ESN',
          field: 'str_esn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台',
          field: 'str_wo_code_ori',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台 ESN',
          field: 'str_wo_esn_ori',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PN',
          field: 'str_pn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件标签',
          field: 'str_label',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件名称',
          field: 'str_part_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'SM',
          field: 'str_sm',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '客户',
          field: 'str_client',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件类别',
          field: 'str_item_type',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PKP',
          field: 'id_pkp',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '机型',
          field: 'str_engine_type',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '数量',
          field: 'int_qty',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'EKD',
          field: 'dt_ekd',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原因备注',
          field: 'str_reason',
          minWidth: 200,
        },
      ],
      total: 0,
    })
    // 获取零件接口
    const getPartList = async (params) => {
      const { id_wo, str_group, int_point_type, id } = params
      const filterFields = parentFilterFields.value.map((item) => {
        return {
          ...item,
        }
      })
      filterFields.push({
        str_key: 'str_group',
        str_value: str_group,
      })
      const searchParams = {
        ac: 'de_getpnlist_bygroup',
        id,
        id_wo,
        int_ekd_type: parentIntEkdType.value ?? '1',
        int_point_type,
        filter_fields: filterFields,
      }
      const { data } = await post(searchParams)
      if (data.code === 'success') {
        tableState.data = data.data
        tableState.total = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    const handleChartPartClick = async (item) => {
      partDrawer.visible = true
      partDrawer.title = `${item.str_group}零件列表`
      await getPartList(item)
    }
    // * 导出表格数据
    const exportTableData = () => {
      tableRef.value.exportData()
    }

    const { siteOptions, getSiteOptions, deptOptions, getDeptOptions } = useOption()

    const unitScatterChartRef = ref({})
    const setUnitScatterChartRef = (el, type) => {
      if (el) {
        unitScatterChartRef.value[type] = el
      }
    }

    // 刷新图表
    const refreshChart = (group) => {
      unitScatterChartRef.value[group].getChartList()
    }
    // 刷新图表
    const refreshChartByClick = async (group, oneItem, intEkdType) => {
      unitScatterChartRef.value[group].getChartList(intEkdType)
      let parentFilterFields = recombineFilter(unitGroupForm)
      const params = {
        ac: 'de_sm_group_count_by_idwo',
        id_wo: props.id,
        filter_fields: parentFilterFields,
        str_type: group,
        int_ekd_type: intEkdType,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        oneItem.int_red = data.data.int_red
        oneItem.int_green = data.data.int_green
        oneItem.int_purple = data.data.int_purple
        oneItem.int_blue = data.data.int_blue
        oneItem.int_after_red = data.data.int_after_red
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    /**点击推演前后、模拟前后，前端汇总各类型数字 */
    // const changePnSum=async ({ oneData}) => {
    //   unitGroupList.value.filter((item) => {
    //     if (item.str_group === oneData.str_group) {
    //      item.dt_pr = oneData.dt_pr
    //     }
    //   })
    //   let m1=oneData;
    // }
    // 改变allData中的值
    const changeAllData = (group, start, end) => {
      allData = allData.map((item) => {
        if (item.str_group === group) {
          return {
            ...item,
            dt_project_start: start,
            dt_project_end: end,
          }
        }
        return item
      })
    }
    // 计算Core fan lpt的中的最小的开始时间
    const getCoreFanLPTMinStart = () => {
      const core = allData.find((item) => item.str_group === 'CORE')
      const fan = allData.find((item) => item.str_group === 'FAN')
      const lpt = allData.find((item) => item.str_group === 'LPT')
      return [core.dt_project_start, fan.dt_project_start, lpt.dt_project_start].reduce((prev, next) => {
        return moment(next).isBefore(prev) ? moment(next) : prev
      })
    }
    /**
     * @description: 改变ESN的红框通过移动Core Fan LPT的红框
     * @param {String} group
     * @param {Date} start
     * @param {Date} end  B1的结束时间
     */
    const refreshRedBox = (group, start, end) => {
      let esnStart = ''
      // 1. 获取Core Fan LPT的中最晚的结束时间，取其对象的开始时间
      const core = allData.find((item) => item.str_group === 'CORE')
      const fan = allData.find((item) => item.str_group === 'FAN')
      const lpt = allData.find((item) => item.str_group === 'LPT')
      // 1.1 找出core fan lpt中最晚的结束时间
      const maxEnd = [core.dt_project_end, fan.dt_project_end, lpt.dt_project_end].reduce((prev, next) => {
        return moment(next).isAfter(prev) ? moment(next) : prev
      })
      // 1.2 找出core fan lpt中与最晚的结束时间相等的对象
      const equalArr = [core, fan, lpt].filter((item) => moment(item.dt_project_end).isSame(maxEnd))
      // 1.3 如果有相等的对象
      if (equalArr.length > 1) {
        // 1.3.1 找出equalArr中dt_project_start最小的值
        const minStart = equalArr.reduce((prev, next) => {
          return moment(next.dt_project_start).isBefore(prev) ? moment(next.dt_project_start) : prev
        }, moment(equalArr[0].dt_project_start))
        esnStart = moment(minStart).format('YYYY-MM-DD')
      } else {
        // 1.3.2 如果没有相等的对象,则直接赋值给esnStart
        esnStart = moment(equalArr[0].dt_project_start).format('YYYY-MM-DD')
      }
      changeAllData(group, esnStart, end)
      // 改变Esn的红框
      unitScatterChartRef.value[group].changeEngineRedBox(group, esnStart, end)
    }
    // FIXME Core Fan LPT的逻辑处理
    const changeRedBoxByCoreFanLPT = ({ start, end, group }) => {
      // 1. core fan lpt拖动之后的结束时间不能大于等于B1的开始时间
      const b1 = allData.find((item) => item.str_group === 'B1')
      const b1RedBoxStart = b1.dt_project_start
      const b1RedBoxEnd = b1.dt_project_end
      const isAfterB1 = moment(end).isSameOrAfter(b1RedBoxStart)
      // 1.1 当超出了B1的结束时间, 则提示并刷新图表, 并且ESN的图表红框回归原位
      if (isAfterB1) {
        ElementPlus.ElMessage.error('Core Fan LPT的结束时间不能大于等于B1的开始时间')
        refreshChart(group)
        const start = unitGroupList.value.find((item) => item.str_group === group).dt_project_start
        const end = unitGroupList.value.find((item) => item.str_group === group).dt_project_end
        changeAllData(group, start, end)
        refreshRedBox('ESN', start, b1RedBoxEnd)
        return
      }
      // 2.1 改变的值赋值给allData
      changeAllData(group, start, end)
      // 3. 刷新ESN的图表的红框
      refreshRedBox('ESN', start, b1RedBoxEnd)
    }

    // B1的逻辑处理
    const changeRedBoxByB1 = ({ start, end }) => {
      // 1. B1的开始时间小于等于Core Fan LPT的结束时间, Core fan lpt的开始时间和结束时间向前移动一天
      // 1.1 获取Core Fan LPT的开始时间和结束时间
      const core = allData.find((item) => item.str_group === 'CORE')
      const fan = allData.find((item) => item.str_group === 'FAN')
      const lpt = allData.find((item) => item.str_group === 'LPT')
      // 1.3 判断B1的开始时间是否小于等于Core Fan LPT的结束时间
      const isBeforeCore = moment(start).isSameOrBefore(core.dt_project_end)
      const isBeforeFan = moment(start).isSameOrBefore(fan.dt_project_end)
      const isBeforeLpt = moment(start).isSameOrBefore(lpt.dt_project_end)
      changeAllData('B1', start, end)
      // 1.4 如果小于等于,则Core Fan LPT的开始时间和结束时间向前移动一天
      if (isBeforeCore) {
        // 1.4.1 Core的开始时间和结束时间向前移动一天
        const diff = moment(core.dt_project_end).diff(moment(core.dt_project_start), 'days')
        const coreEnd = moment(start).subtract(1, 'days').format('YYYY-MM-DD')
        const coreStart = moment(coreEnd).subtract(diff, 'days').format('YYYY-MM-DD')
        // 改变allData中的值
        changeAllData('CORE', coreStart, coreEnd)
        // 改变core的红框
        unitScatterChartRef.value['CORE'].changeEngineRedBox('CORE', coreStart, coreEnd)
      }
      if (isBeforeFan) {
        // 1.4.2 Fan的开始时间和结束时间向前移动一天
        const diff = moment(fan.dt_project_end).diff(moment(fan.dt_project_start), 'days')
        const fanEnd = moment(start).subtract(1, 'days').format('YYYY-MM-DD')
        const fanStart = moment(fanEnd).subtract(diff, 'days').format('YYYY-MM-DD')
        // 改变allData中的值
        changeAllData('FAN', fanStart, fanEnd)
        // 刷新fan的红框
        unitScatterChartRef.value['FAN'].changeEngineRedBox('FAN', fanStart, fanEnd)
      }
      if (isBeforeLpt) {
        // 1.4.3 LPT的开始时间和结束时间向前移动一天
        const diff = moment(lpt.dt_project_end).diff(moment(lpt.dt_project_start), 'days')
        const lptEnd = moment(start).subtract(1, 'days').format('YYYY-MM-DD')
        const lptStart = moment(lptEnd).subtract(diff, 'days').format('YYYY-MM-DD')
        // 改变allData中的值
        changeAllData('LPT', lptStart, lptEnd)
        // 刷新lpt的红框
        unitScatterChartRef.value['LPT'].changeEngineRedBox('LPT', lptStart, lptEnd)
      }
      // 1.5 获取core fan lpt的最小的开始时间
      const coreFanLPTMinStart = moment(getCoreFanLPTMinStart()).format('YYYY-MM-DD')
      // 2. 改变B1的结束时间, ESN的结束时间为B1的结束时间
      refreshRedBox('ESN', coreFanLPTMinStart, end)
    }

    // * 改变红色框
    const changeRedBox = ({ start, end, group }) => {
      // 当改变的是ESN的时候,不做处理
      if (group === 'ESN') {
        return
      }
      // 当改变的是B1的时候
      if (group === 'B1') {
        // B1逻辑处理
        changeRedBoxByB1({ start, end })
      } else {
        // Core Fan LPT的逻辑处理
        changeRedBoxByCoreFanLPT({ start, end, group })
      }
    }

    //  保存计划
    const savePlan = async () => {
      const groupList = allData.map((item) => {
        return {
          str_group: item.str_group,
          dt_begin: item.dt_project_start,
          dt_end: item.dt_project_end,
        }
      })
      const params = {
        ac: 'de_sm_group_change_plan',
        id_wo: props.id,
        groupList,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        ElementPlus.ElMessage.success(data.text)
        visible.value = false
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    const { reasonRemark, openReasonRemark, saveReasonRemark, closeReasonRemark } = useReasonRemark(tableRef)
    onMounted(() => {
      getUnitPlan()
      getSiteOptions()
      getDeptOptions()
    })
    return {
      visible,
      unitGroupForm,
      restForm,
      unitGroupList,
      setUnitScatterChartRef,
      siteOptions,
      deptOptions,
      getUnitPlan,
      parentFilterFields,
      changeRedBox,
      savePlan,
      refreshChartByClick,
      parentIntEkdType,
      colors,
      handleColorClick,
      partDrawer,
      tableState,
      tableRef,
      handleChartPartClick,
      exportTableData,
      reasonRemark,
      openReasonRemark,
      saveReasonRemark,
      closeReasonRemark,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="85%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-white">单元视角</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <!-- 查询条件 -->
      <el-form :model="unitGroupForm" inline>
        <el-form-item label="B1/B2/B3">
          <el-select v-model="unitGroupForm.id_b123" placeholder="请选择" clearable class="!w-56">
            <el-option label="B1" value="1"></el-option>
            <el-option label="B2" value="2"></el-option>
            <el-option label="B3" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="SM"  prop="str_sm">
        <el-input  v-model="unitGroupForm.str_sm" ></el-option>
        
      </el-form-item>
        <el-form-item label="Site">
          <el-select v-model="unitGroupForm.str_site" placeholder="请选择" clearable class="!w-56">
            <el-option v-for="item in siteOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getUnitPlan">查询</el-button>
          <el-button type="info" @click="restForm">重置</el-button>

          
          <el-button type="success"  @click="getUnitPlan(2)">模拟前</el-button>
          <el-button type="success"  @click="getUnitPlan(3)">模拟后</el-button> 
          
        
          <el-button type="primary"   @click="getUnitPlan(0)">推演前</el-button>
          <el-button type="primary" @click="getUnitPlan(1)">推演后</el-button>
         
        </el-form-item>
      </el-form>
      <!-- 标识 -->
      <div class="flex justify-center items-center">
        <div class="flex items-center">
          <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
          <span>可串件</span>
        </div>
        <div class="flex items-center ml-4">
          <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
          <span>有条件串件</span>
        </div>
        <div class="flex items-center ml-4">
          <div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
          <span>不能串</span>
        </div>
        <div class="flex items-center ml-4">
          <div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
          <span>不具备串件条件</span>
        </div>
      </div>
      <div v-for="(item, index) in unitGroupList" :key="index" class="mt-4 flex items-center h-[30vh]">
        <div class="bg-blue-100 h-full flex-none w-1/6 py-2">
          <div v-if="item.str_esn" class="grid grid-flow-row grid-rows-3">
            <div class="font-bold text-sm">{{ item.str_esn}}({{item.str_wo}})</div>
            <div class="text-sm text-left">GP: {{ item.dt_project_start }}</div>
            <div class="text-sm">建议调整时间: {{ item.dt_adjust_start }}</div>
          </div>
          <ul v-else class="list-none">
            <li class="font-bold text-sm">{{ item.str_group }}</li>
            <li class="text-sm"> 装配:{{ item.dt_project_start }}</li>
            <li 
              v-for="(value, key) in colors" 
              :key="key" 
            >
              <span
                class="text-sm hover:cursor-pointer hover:underline-offset-4 hover:underline hover:decoration-pink-500"
                @click="handleColorClick(item, key)"
              >{{ key }}: {{ item[value] }}</span>
            </li>
          </ul>
        </div>
        <div class="flex-1 h-full">
          <UnitScatterChart 
            :ref="(el) => setUnitScatterChartRef(el, item.str_group)"
            :id="item.id_wo"
            :idEngineType="item.id_engine_type"
            :type="item.str_group"
            :coordRange="[item.dt_project_start, item.dt_project_end]"
            :markAreaRange="[item.dt_adjust_start, item.dt_adjust_end]"
            :markLineDate="item.dt_close"
            :markTeamAreaRange="[item.dt_team_start, item.dt_team_end]"
            :markQecAreaRange="[item.dt_qec_start, item.dt_qec_end]"
            :markTestAreaRange="[item.dt_test_start, item.dt_test_end]"
            :parentFilterFields="parentFilterFields"
            :allData="unitGroupList"
            :int-ekd-type="parentIntEkdType"
            @changeRedBox="changeRedBox"
            @partClick="handleChartPartClick"
          ></UnitScatterChart>
          <!-- @changePnSum="changePnSum"-->
        </div>
      </div>
      <template #footer>
        <el-button type="primary" @click="savePlan">保存计划</el-button>
        <el-button @click="visible = false">取消</el-button>
      </template>
    </el-drawer>
    <el-drawer class="my_drawer" v-model="partDrawer.visible" size="80%" :show-close="false" destroy-on-close append-to-body>
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-white">{{ partDrawer.title }}</div>
          <el-button type="danger" @click="partDrawer.visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <el-button type="primary" @click="exportTableData">导出</el-button>
            <el-button class="ml-4" type="primary" @click="openReasonRemark(0)">原因备注</el-button>
          </div>
          <div class="text-black">共计：{{ tableState.total || 0 }} 条</div>
        </div>
        <HtVxeTable 
          ref="tableRef"
          :tableData="tableState.data"
          :tableColumns="tableState.columns"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
    <!-- 原因备注 -->
    <ReasonRemarkDialog v-model="reasonRemark.visible" :form="reasonRemark.form" @saveReasonRemark="saveReasonRemark" @closeReasonRemark="closeReasonRemark" />
  `,
}
export default UnitPlanDrawer
