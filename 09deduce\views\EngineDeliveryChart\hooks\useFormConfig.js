const { reactive } = Vue

export const useFormConfig = () => {
  const form = reactive({
    f4_1BeginTime: '',
    // 默认当前年的开始和结束
    realeaseTime: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')],
    f2_3ClosedTime: '',
    engineType: '',
    repairType: []
  })

  const resetForm = () => {
    Object.keys(form).forEach((key) => (form[key] = ''))
  }

  return {
    form,
    resetForm
  }
} 