const { reactive, onMounted } = Vue

export default {
  name: 'FilterCalendar',
  props: {
    params: Object,
  },
  setup(props) {
    const state = reactive({
      option: { data: null },
      columnField: null,
    })

    // 初始化数据
    const load = () => {
      const { params } = props
      if (params) {
        const { column } = params
        // 保存列标识
        state.columnField = column.field
        // 获取或创建过滤器选项
        if (column.filters?.[0]) {
          // 保持引用关系
          state.option = column.filters[0]
          // 如果有初始值，转换为 moment 对象
          if (state.option.data) {
            state.option.data = moment(state.option.data).format('YYYY-MM-DD')
          }
        }
      }
    }

    // 值改变事件
    const changeOptionEvent = ({ value }) => {
      const { params } = props
      if (params) {
        const { $panel } = params
        // 确保只更新当前列的过滤器
        if (params.column.field === state.columnField) {
          // 将日期转换为标准格式
          state.option.data = value ? moment(value).format('YYYY-MM-DD') : null
          
          const checked = !!value
          $panel.changeOption({}, checked, state.option)
        }
      }
    }

    // 初始化
    onMounted(() => {
      load()
    })

    return {
      state,
      changeOptionEvent,
    }
  },
  template: /*html*/ `
    <vxe-input
      v-model="state.option.data"
      type="date"
      :transfer="true"
      clearable
      :placeholder="'请选择日期'"
      @change="changeOptionEvent"
    />
  `,
}
