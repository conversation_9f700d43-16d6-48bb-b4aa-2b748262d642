<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!--    全部样式-->
    <link href="../../../assets/css/index.css" rel="stylesheet" />
    <!--    引入element-plus的样式-->
    <link href="../../../assets/element-plus@2.5.5/index.css" rel="stylesheet" />
    <!--    引入drawer的样式-->
    <link href="../../../assets/css/drawer.css" rel="stylesheet" />
    <!--    引入vxe-table的样式-->
    <link href="../../../assets/vxe-table/style.css" rel="stylesheet" />
    <!--    引入VUE-->
    <script src="../../../assets/vue@3.4.15/vue.global.prod.js"></script>
    <!--    引入vxe-table组件-->
    <script src="../../../assets/vxe-table/xe-utils.js"></script>
    <script src="../../../assets/vxe-table/vxe-table.js"></script>
    <!--    引入element-plus-->
    <script src="../../../assets/element-plus@2.5.5/index.js"></script>
    <!--  引入element-plus-icon-->
    <script src="../../../assets/icons-vue@2.3.1/index.iife.min.js"></script>
    <!-- 引入element-plus-ch -->
    <script src="../../../assets/element-plus@2.5.5/lang/zh-cn.js"></script>
    <!-- 引入mock -->
    <script src="../../../assets/mock/mock.js"></script>
    <!--    引入axios-->
    <script src="../../../assets/axios@1.6.7/axios.min.js"></script>
    <!--    引入@vueuse/core-->
    <script src="../../../assets/vueuse/core@10.7.2/index.iife.min.js"></script>
    <script src="../../../assets/vueuse/shared@10.7.2/index.iife.min.js"></script>
    <!--    引入moment-->
    <script src="../../../assets/moment/moment.min.js"></script>
    <script src="../../../03team_new/comm/api_environment.js"></script>
    <!--    引入echarts-->
    <script src="../../../assets/echarts@5.5.0/echarts.min.js"></script>
    <title>发动机交付周期</title>
  </head>
  <body>
    <div id="app">
      <engine-delivery-chart></engine-delivery-chart>
    </div>
  </body>
  <script type="module">
    import EngineDeliveryChart from './index.js'
    import '../../../components/VxeTable/renderer/index.js'
    import roleDirective from '../../directives/role.js'
    moment.updateLocale('en', {
      week: {
        dow: 1,
      },
    })
    const { createApp } = Vue
    const app = createApp({
      components: {
        EngineDeliveryChart,
      },
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    app.directive('role', roleDirective)
    app.use(ElementPlus, {
      locale: ElementPlusLocaleZhCn,
    })
    app.use(VXETable)
    app.mount('#app')
  </script>
</html>
