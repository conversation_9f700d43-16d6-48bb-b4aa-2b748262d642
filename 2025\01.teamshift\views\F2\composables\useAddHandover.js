import { fetchInitHandover, fetchHandover } from '../api/index.js'

export function useAddHandover(props) {
  const { onMounted, ref, reactive } = Vue
  const { ElMessage } = ElementPlus
  // 交接提示
  const headerTips = ref('')

  // 查询条件
  const queryParams = reactive({
    dt_pt: props.column.day || '',
    id_shift: '',
    teamName: props.row.str_team || '',
    shiftName: '',
    id_by_to_receive: '',
    str_task_type: props.row.str_step || '',
    is_pending: '0',
    
  })

  // 加载状态
  const loading = ref(false)
  // 表格数据
  const tableData = ref([])
  const mainData = ref({})
  // 初始化表格数据
  const initTableData = async () => {
    loading.value = true
    const params = {
      dt_pt: props.column.day || '',
      id_shift: queryParams.id_shift || '',
      str_handover_type: props.businessType || '',
      str_task_type: queryParams.str_task_type || '',
    }
    try {
      const res = await fetchHandover(params)
      headerTips.value = res.str_title
      mainData.value = res.pTHandoverMain
      tableData.value = res.pTHandovers.map((item) => ({
        id: item.pTHandover.id || '',
        code: item.pTHandover.int_type || 0,
        legend: item.pTHandover.str_category || '',
        tip: item.pTHandover.str_help || '暂无信息',
        desc: item.pTHandover.str_content || '',
        isTransferred: item.pTHandover.int_status === 1,
        isOriginal: true,
        attachment: item.files || [],
        id_model: item.pTHandover.id_model,
        sm: item.pTHandover.str_sm || '',
      }))
      queryParams.id_by_to_receive = mainData.value?.id_by_to_receive || ''
      queryParams.is_pending = mainData.value?.is_pending || '0'
      queryParams.is_completed = +(mainData.value?.is_completed || 0)
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  // 获取当前班次
  const getCurrentShift = async () => {
    const currentRow = props.row
    const currentColumn = props.column
    const currentTask = currentRow.plans.find((item) => moment(item.dt_shift).isSame(currentColumn.day, 'day'))
    queryParams.shiftName = currentTask.str_shift
    queryParams.id_shift = currentTask.id_shift
  }

  onMounted(() => {
    getCurrentShift()
    initTableData()
  })

  // 新增行
  const handleAddRow = (row) => {
    if (!row?.code || !row?.legend) {
      ElMessage.warning('无效的行数据')
      return
    }

    // 创建新行，使用解构赋值确保数据结构一致性
    const newRow = {
      ...row,
      id: '', // 清空ID，因为这是新行
      desc: '',
      attachment: [],
      isExpanded: false,
      isOriginal: false,
      isTransferred: false,
    }

    // 找到最后一个相同code的行的索引
    let lastSameCodeIndex = -1
    for (let i = 0; i < tableData.value.length; i++) {
      if (tableData.value[i].code === row.code) {
        lastSameCodeIndex = i
      }
    }

    // 创建新的数组副本
    const updatedTableData = [...tableData.value]

    if (lastSameCodeIndex === -1) {
      // 如果没有找到相同code的行，找到第一个大于当前code的位置插入
      const insertBeforeIndex = tableData.value.findIndex((item) => item.code > row.code)
      if (insertBeforeIndex === -1) {
        // 如果没有找到更大的code，则添加到末尾
        updatedTableData.push(newRow)
      } else {
        // 插入到更大code的行前面
        updatedTableData.splice(insertBeforeIndex, 0, newRow)
      }
    } else {
      // 插入到最后一个相同code的行后面
      updatedTableData.splice(lastSameCodeIndex + 1, 0, newRow)
    }

    // 更新表格数据
    tableData.value = updatedTableData

    ElMessage.success('新增行成功')
  }

  return {
    tableData,
    headerTips,
    queryParams,
    mainData,
    handleAddRow,
  }
}
