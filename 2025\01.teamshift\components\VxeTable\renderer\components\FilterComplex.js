const FilterComplex = {
  name: 'FilterComplex',
  props: {
    params: Object,
  },
  setup(props) {
    const selectState = Vue.reactive({
      option: null,
      columnField: null
    });

    const load = () => {
      const { params } = props;
      if (params) {
        const { column } = params;
        // 保存列标识
        selectState.columnField = column.field;
        // 获取过滤器选项
        if (column.filters?.[0]) {
          selectState.option = column.filters[0];
          // 如果数据不存在或结构不正确，初始化数据
          if (!selectState.option.data || typeof selectState.option.data !== 'object') {
            selectState.option.data = { type: 'less', name: '' };
          }
        }
      }
    };

    const changeOptionEvent = () => {
      const { params } = props;
      const { option, columnField } = selectState;
      if (params && option) {
        const { $panel } = params;
        // 确保只更新当前列的过滤器
        if (params.column.field === columnField) {
          const checked = !!option.data.name;
          $panel.changeOption(null, checked, option);
        }
      }
    };

    const confirmEvent = () => {
      const { params } = props;
      if (params) {
        const { $panel } = params;
        $panel.confirmFilter();
      }
    };

    const restEvent = () => {
      const { params } = props;
      if (params) {
        const { $panel } = params;
        $panel.resetFilter();
      }
    };

    // 初始化
    Vue.onMounted(() => {
      load();
    });

    return {
      selectState,
      changeOptionEvent,
      confirmEvent,
      restEvent
    };
  },
  template: `
    <div class="">
      <vxe-radio v-model="selectState.option.data.type" name="fType" label="less">小于</vxe-radio>
      <vxe-radio v-model="selectState.option.data.type" name="fType" label="eq">等于</vxe-radio>
      <vxe-radio v-model="selectState.option.data.type" name="fType" label="greater">大于</vxe-radio>
    </div>
    <div class="">
      <vxe-input v-model="selectState.option.data.name" type="text" placeholder="请输入内容"
                 @input="changeOptionEvent"></vxe-input>
    </div>
    <div>
      <vxe-button status="primary" @click="confirmEvent">确定</vxe-button>
      <vxe-button @click="restEvent">重置</vxe-button>
    </div>
  `
};
export default FilterComplex;
