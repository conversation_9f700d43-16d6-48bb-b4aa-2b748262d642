<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>团队看板</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: Arial, sans-serif;
        background: #f0f0f0;
        padding: 20px;
        color: #333;
      }

      .dashboard {
        display: table;
        width: 100%;
        border-spacing: 20px;
      }

      .section {
        display: table-cell;
        width: 50%;
        vertical-align: top;
      }

      .card {
        background: white;
        border-radius: 8px;
        margin-bottom: 20px;
        padding: 0;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
      }
      
      /* 移除卡片悬浮效果，使界面更加稳定 */
      
      .card-content {
        padding: 15px;
      }

      .header {
        background: #689f38;
        color: white;
        padding: 12px 15px;
        font-size: 17px;
        border-radius: 4px 4px 0 0;
        font-weight: bold;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .chart-container {
        margin-top: 20px;
        position: relative;
        padding-bottom: 30px;
        min-height: 150px;
      }
      
      .chart-container::before {
        content: '';
        position: absolute;
        top: -10px;
        left: 0;
        right: 0;
        height: 1px;
        background: #eaeaea;
      }

      /* x轴刻度线和标签 */
      .x-axis {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 25px;
        display: flex;
        justify-content: space-between;
        padding-left: 30px;
        padding-right: 30px;
      }

      .x-axis-mark {
        position: relative;
        width: 1px;
        height: 6px;
        background-color: #aaa;
      }

      .x-axis-mark:before {
        content: '';
        position: absolute;
        top: -5px;
        left: 0;
        width: 1px;
        height: 5px;
        background-color: rgba(170, 170, 170, 0.2);
      }

      .x-axis-label {
        position: absolute;
        top: 8px;
        font-size: 10px;
        transform: translateX(-50%);
        color: #666;
        font-weight: 500;
      }
      
      /* x轴网格线 */
      .x-grid-lines {
        position: absolute;
        bottom: 25px;
        left: 30px;
        right: 30px;
        top: 0;
        z-index: 0;
      }
      
      .x-grid-line {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 1px;
        background-color: rgba(200, 200, 200, 0.2);
      }

      .chart-group {
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 5px;
      }

      .chart-item {
        margin-bottom: 12px;
        position: relative;
        height: 28px;
      }

      .group-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        padding-bottom: 6px;
        border-bottom: 1px solid #f0f0f0;
      }

      .esn-code {
        font-size: 13px;
        color: #555;
        font-weight: bold;
      }

      .percentage {
        font-size: 13px;
        font-weight: bold;
        color: #689f38;
        padding: 2px 8px;
        border-radius: 10px;
        background-color: #f0f7e6;
      }

      .bar-label {
        position: absolute;
        left: 0;
        top: 5px;
        width: 25px;
        font-size: 12px;
        font-weight: bold;
        text-align: left;
        white-space: nowrap;
      }

      .bar-wrapper {
        position: absolute;
        left: 30px;
        right: 30px;
        top: 0;
        height: 24px;
        background-color: #f9f9f9;
        border-left: 1px solid #ddd;
        border-radius: 2px;
        display: flex;
        flex-wrap: nowrap;
        overflow: hidden; /* 确保超出部分被裁剪 */
      }

      .bar-segment {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: visible; /* 改为visible，允许内容溢出 */
        border-right: 1px solid white;
        position: relative;
        min-width: 20px; /* 确保所有柱子至少有最小宽度 */
      }
      .bar-segment:last-child {
        border-right: none;
      }

      .bar-value {
        font-size: 12px;
        color: white;
        font-weight: bold;
        text-align: center;
        white-space: nowrap;
        text-shadow: 0 1px 1px rgba(0,0,0,0.3);
        padding: 0 2px; /* 增加内边距，确保数字与柱子边缘有间距 */
      }
      
      /* 移除短段的外部数值显示样式 */
      
      /* 移除悬浮提示，让页面更加简洁优雅 */

      .table-container {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #eaeaea;
        border-radius: 4px;
        margin-top: 15px;
        position: relative;
      }
      
      .task-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
      }

      .task-table th,
      .task-table td {
        border: none;
        border-bottom: 1px solid #ddd;
        padding: 10px 8px;
        text-align: left;
        font-size: 12px;
      }
      
      .task-table th:not(:last-child),
      .task-table td:not(:last-child) {
        border-right: 1px solid #eaeaea;
      }

      .task-table th {
        background: #f5f5f5;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 1px 0 #ddd;
        font-weight: bold;
      }

      .task-table tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      .task-table tr:hover {
        background-color: #f0f7e6;
      }

      .task-row {
        height: 40px;
      }
      
      /* 滚动条样式优化 */
      .table-container::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      .table-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }
      
      .table-container::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 4px;
      }
      
      .table-container::-webkit-scrollbar-thumb:hover {
        background: #aaa;
      }

      .info-section {
        background: #689f38;
        color: white;
        padding: 10px 15px;
        border-radius: 0;
        margin-bottom: 20px;
        font-size: 16px;
        position: relative;
      }

      .station-info {
        display: inline-block;
        margin-right: 20px;
      }

      .date {
        position: absolute;
        right: 15px;
        top: 10px;
        font-weight: normal;
      }

      .esn-code-small {
        font-family: monospace;
        font-size: 12px;
      }

      .status-complete {
        color: #689f38;
        font-weight: bold;
      }

      .status-pending {
        color: #ffa000;
        font-weight: bold;
      }

      /* 为低版本安卓添加的兼容性样式 */
      @media screen and (max-width: 1024px) {
        .dashboard {
          display: block;
        }

        .section {
          display: block;
          width: 100%;
        }

        .task-table {
          font-size: 11px;
        }

        .header {
          font-size: 16px;
        }
      }

      .loading {
        text-align: center;
        padding: 20px;
        font-style: italic;
        color: #666;
      }

      .error-message {
        color: #e53935;
        margin: 10px 0;
        padding: 10px;
        border: 1px solid #e53935;
        background-color: #ffebee;
        border-radius: 4px;
      }
      
      /* 刷新时间显示 */
      .refresh-time {
        font-size: 12px;
        font-weight: normal;
        opacity: 0.8;
      }
    </style>
  </head>
  <body>
    <div class="info-section">
      <span id="stationInfo" class="station-info">Work Station: </span>
      <span id="teamNumber" class="station-info">Team: </span>
      <span id="teamLeader" class="station-info">Team Leader: </span>
      <span id="teamRotation" class="station-info" style="font-size: 12px; opacity: 0.8;">(自动切换: 30:00)</span>
      <span class="date">
        DATE:
        <span id="currentDate"></span>
      </span>
    </div>

    <div class="dashboard">
      <div class="section">
        <div class="card">
          <div class="header">
            <span>Month</span>
            <span class="refresh-time"></span>
          </div>
          <div class="card-content">
            <div id="monthChart" class="chart-container">
              <div class="loading">加载数据中...</div>
            </div>
          </div>
        </div>
        <div class="card">
          <div class="header">
            <span>Yesterday</span>
            <span class="refresh-time"></span>
          </div>
          <div class="card-content">
            <div class="table-container">
              <table class="task-table">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>ESN</th>
                    <th>Type</th>
                    <th>Task type</th>
                    <th>Task</th>
                    <th>Date</th>
                    <th>People</th>
                    <th>Complete</th>
                  </tr>
                </thead>
                <tbody id="yesterdayTasks">
                  <tr><td colspan="8" class="loading">加载数据中...</td></tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="section">
        <div class="card">
          <div class="header">
            <span>Week</span>
            <span class="refresh-time"></span>
          </div>
          <div class="card-content">
            <div id="weekChart" class="chart-container">
              <div class="loading">加载数据中...</div>
            </div>
          </div>
        </div>
        <div class="card">
          <div class="header">
            <span>Today</span>
            <span class="refresh-time"></span>
          </div>
          <div class="card-content">
            <div class="table-container">
              <table class="task-table">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>ESN</th>
                    <th>Type</th>
                    <th>Task type</th>
                    <th>Task</th>
                    <th>Date</th>
                    <th>People</th>
                    <th>Complete</th>
                  </tr>
                </thead>
                <tbody id="todayTasks">
                  <tr><td colspan="8" class="loading">加载数据中...</td></tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 引入分离的JavaScript文件 -->
    <script src="dashboard.js"></script>
    
    <!-- 初始化看板，并传入站点代码参数 -->
    <script>
      // 从URL获取站点代码参数
      function getQueryParam(name) {
        var urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
      }
      
      // 获取站点代码参数，如果没有则使用默认值
      var siteCode = getQueryParam('siteCode') || 'CC(CombustorChamber)';
      
      // 初始化看板（如有多个团队，将每30分钟自动轮换显示）
      document.addEventListener('DOMContentLoaded', function() {
        initDashboard(siteCode);
      });
    </script>
  </body>
</html>

