import { queryBaseTaskById } from '../../api/baseTask.js';
import { useConversion } from './useConversion.js';

const { ref } = Vue;
const BasePageView = {
  setup() {
    const state = Vue.reactive({
      baseForm: {
        model: '',
        flow: '',
        type: '',
        smObject: null,
        occupySite: '',
        duration: '',
        taskName: '',
        remark: '',
      },
      workWearList: [], // 工装列表
      relationList: [], // 关联任务列表
    });

    const { apiToForm } = useConversion();

    // 获取详情数据
    const getDetailData = async (id) => {
      const res = await queryBaseTaskById(id);
      if (res) {
        const { workClothes, relateTasks, ...rest } = res;
        state.baseForm = apiToForm(rest);
        // 工装信息
        state.workWearList = workClothes?.map((item) => {
          return {
            name: item.id_workclothes,
            quantity: item.int_num,
          };
        }) || [];
        // 关联信息
        state.relationList = relateTasks && relateTasks.map((item) => {
          return {
            task: item.str_relate_task_name,
            relation: item.int_task_relate_type,
          };
        }) || [];
      }
    };
    // 获取SM名称
    const getSmName = () => {
      return state.baseForm.smObject?.str_key || '';
    };
    // 获取站位名称
    const getOccupySiteName = () => {
      const occupyMap = {
        '0': 'F4-1 VG',
        '1': 'F4-1 LPT',
      }
      const { occupySite } = state.baseForm;
      let m1=occupySite && occupyMap[occupySite];
      return (occupySite && occupyMap[occupySite]) || '';
    };
    // 获取关联类型
    const getRelationName = (row) => {
      const relationMap = {
        '1': 'S-S',
        '3': 'S-F',
        '2': 'F-F',
        '0': 'F-S',
      };
      return relationMap[row.relation];
    };

    return {
      getDetailData,
      ...Vue.toRefs(state),
      getSmName,
      getOccupySiteName,
      getRelationName
    };
  },
  /*html*/
  template: `
    <div class="border-l-4 border-l-emerald-700 text-emerald-700">
      <span class="text-xl pl-4">基础信息</span>
    </div>
    <div class="border-b-2 my-2"></div>
    <el-descriptions :column="3" border>
      <el-descriptions-item label="机型">{{ baseForm.model }}</el-descriptions-item>
      <el-descriptions-item label="Flow">{{ baseForm.flow }}</el-descriptions-item>
      <el-descriptions-item label="Type">{{ baseForm.type }}</el-descriptions-item>
      <el-descriptions-item label="SM">{{ getSmName() }}</el-descriptions-item>
      <el-descriptions-item label="站位">{{ getOccupySiteName() }}</el-descriptions-item>
      <el-descriptions-item label="工期(天)">{{ baseForm.duration }}</el-descriptions-item>
      <el-descriptions-item label="任务名称" :span="3">{{ baseForm.taskName }}</el-descriptions-item>
      <el-descriptions-item label="备注" :span="3">{{ baseForm.remark }}</el-descriptions-item>
    </el-descriptions>
    <div class="border-l-4 border-l-emerald-700 text-emerald-700 mt-2">
      <span class="text-xl pl-4">工装信息</span>
    </div>
    <div class="border-b-2 my-2"></div>
    <el-table :data="workWearList" border stripe>
      <el-table-column prop="name" label="工装名称"></el-table-column>
      <el-table-column prop="quantity" label="数量"></el-table-column>
    </el-table>
    <div class="border-l-4 border-l-emerald-700 text-emerald-700 mt-2">
      <span class="text-xl pl-4">关联任务</span>
    </div>
    <div class="border-b-2 my-2"></div>
    <el-table :data="relationList" border stripe>
      <el-table-column prop="task" label="任务名称"></el-table-column>
      <el-table-column prop="relation" label="关联类型">
        <template #default="{ row }">
          <span>{{ getRelationName(row) }}</span>
        </template>
      </el-table-column>
    </el-table>
  `,
};
export default BasePageView;
