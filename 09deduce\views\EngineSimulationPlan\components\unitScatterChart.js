import { post } from '../../../../config/axios/httpReuest.js'

const { reactive, onMounted, onBeforeMount, ref } = Vue
const unitScatterChart = {
  props: {
    id: {
      type: String,
      required: true,
    },
    idEngineType: {
      type: String,
      required: true,
    },
    intEkdType: {
      type: Number,
      required: false,
    },
    type: {
      type: String,
      required: true,
      default: 'ESN',
    },
    coordRange: {
      type: Array,
      required: false,
    },
    markAreaRange: {
      type: Array,
      required: false,
    },
    markTeamAreaRange: {
      type: Array,
      required: false,
    },
    markQecAreaRange: {
      type: Array,
      required: false,
    },
    markTestAreaRange: {
      type: Array,
      required: false,
    },
    markLineDate: {
      type: String,
      required: false,
    },
    parentFilterFields: {
      type: Array,
      default: [],
    },
  },
  emits: ['changeRedBox', 'partClick'],
  setup(props, { emit }) {
    // 定义图表数据
    const chartData = ref([])
    let myChart = null
    // 获取图表数据
    const getChartList = async () => {
      if (myChart) {
        myChart.dispose()
      }
      myChart = echarts.init(document.getElementById(props.type))
      // 添加loading动画
      myChart.showLoading()
      const params = {
        ac: 'de_sm_site_by_idwo',
        id_wo: props.id,
        id_engine_type: props.idEngineType,
        str_type: props.type ?? 'ESN',
        filter_fields: props.parentFilterFields,
        int_ekd_type: props.intEkdType === null || props.intEkdType === undefined ? 2 : props.intEkdType,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        chartData.value = data.data.map((item) => {
          return {
            z: null,
            ...item,
          }
        })
        initChart(myChart)
        // emit('changePnSum', {oneData:data.data,str_group:props.type} )
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    // ! 用于存储Core Fan LPT的最后一个红框的开始时间和结束时间
    const coreFanLptHistory = reactive({
      lastStartTime: props.coordRange[0] ?? '',
      lastEndTime: props.coordRange[1] ?? '',
    })
    // 初始化图表
    const initChart = (myChart) => {
      const option = {
        backgroundColor: '#f3f4f6',
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'cross',
          },
          formatter: function (params) {
            // ! 通过formatter方法来自定义提示框内容
            return `日期: ${params.value.dt_ekd}<br>名称: ${params.value.str_part_name}`
          },
        },
        grid: {
          left: '3%',
          right: '3%',
          // top: '6%',
          bottom: '15%',
          // containLabel: true,
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            startValue: getRangeMin(props.coordRange[0], props.markAreaRange[0]),
            end: 100,
            // 让datazoom变细
            height: 20,
            bottom: 10,
          },
          {
            type: 'slider',
            show: true,
            yAxisIndex: [0],
            filterMode: 'empty',
            width: 20,
            right: 20,
          },
        ],

        dataset: {
          source: chartData.value,
        },
        brush: {
          toolbox: [''],
          xAxisIndex: 'all', // 指定哪个x轴可以进行刷选
          brushLink: 'all', // 同步所有图表的选中区域
          outOfBrush: {
            colorAlpha: 1,
          },
          transformable: props.type === 'ESN' ? false : true,
          throttleType: 'debounce', // 设置触发brush事件的方式
          throttleDelay: 300, // 设置触发brush事件的延迟
          brushStyle: {
            // 红色
            color: 'rgba(254,0,0,0.1)',
            borderColor: 'red',
          },
          z: 50,
        },
        // x轴为日期,分割以周为单位
        xAxis: [
          {
            type: 'time',
            minInterval: 3600 * 24 * 1000,
            axisLabel: {
              // 旋转45度
              // rotate: -45,
              formatter: function (value) {
                // ? 如果当前日期是周一，则显示该周的标签
                if (moment(value).day() === 1) {
                  return moment(value).week() + ''
                }
                return ''
              },
            },
          },
          {
            type: 'time',
            minInterval: 3600 * 24 * 1000,
            axisLabel: {
              // 旋转45度
              rotate: 90,
              formatter: function (value) {
                // ? 如果当前日期是本月的第一天，则显示该月的标签
                if (moment(value).date() === 1) {
                  // 显示xxxx年xx月
                  return moment(value).month() + 1 + '月'
                }
                return ''
              },
            },
            // 刻度线
            axisTick: {
              interval: 0,
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            show: false,
            type: 'value',
            scale: true,
            axisLabel: {
              formatter: '{value}',
            },
            axisTick: {
              alignWithLabel: true,
              interval: 0,
            },
            splitLine: {
              show: true,
            },
          },
        ],
        series: [
          {
            name: '可串件',
            type: 'scatter',
            large: true,
            symbolSize: 10,
            progressive: 10000,
            xAxisIndex: 0,
            // 高亮下的样式
            emphasis: {
              disabled: false,
              scale: 1.5,
            },
            // 系列颜色为绿色
            itemStyle: {
              color: function (params) {
                /*
                 * int_point_type为1时为红色
                 * int_point_type为2时为蓝色
                 * int_point_type为3时为绿色
                 * int_point_type为4时为紫色
                 */
                if (params.data.int_point_type === '1') {
                  return '#ef4444'
                } else if (params.data.int_point_type === '2') {
                  // ! 当小于建议调整时间的时候，显示绿点
                  if (moment(props.markAreaRange[0]).isAfter(params.data.dt_ekd)) {
                    return '#22c55e'
                  }
                  return '#3b82f6'
                } else if (params.data.int_point_type === '3') {
                  /* 
                    
                  */
                  return '#22c55e'
                } else if (params.data.int_point_type === '4') {
                  return '#a855f7'
                } else {
                  return '#22c55e'
                }
              },
            },
            encode: {
              x: 'dt_ekd',
              y: 'y',
            },
            markArea: {
              silent: true,
              label: {
                show: props.markAreaRange[0] !== null && props.markAreaRange[1] !== null,
                position: 'insideTopLeft',
                fontSize: 12,
                color: 'black',
              },
              itemStyle: {
                // 红色
                color: 'rgba(0, 128, 0, 0.1)',
                borderColor: 'green',
                borderWidth: 1,
              },
              data: [
                [
                  {
                    name: `建议时间: ${props.markAreaRange[0]}`,
                    xAxis: props.markAreaRange[0] ?? '',
                  },
                  {
                    xAxis: props.markAreaRange[1] ?? '',
                  },
                ],
              ],
            },
            // 今天线
            markLine: {
              silent: true,
              // 两边为箭头
              symbol: ['arrow', 'arrow'],
              lineStyle: {
                // 实线
                type: 'solid',
                color: 'black',
                with: 12,
              },
              data: [
                {
                  name: '今天',
                  xAxis: moment().format('YYYY-MM-DD'),
                },
              ],
            },
            z: 100,
          },
          {
            type: 'scatter',
            xAxisIndex: 1,
            encode: {
              x: 'dt_ekd',
              y: 'z',
            },
            markArea: {
              silent: true,
              label: {
                show: false,
              },
              itemStyle: {
                // 绿色
                color: 'rgba(0, 0, 255, 0.1)',
                borderColor: 'blue',
                borderWidth: 1,
              },
              data: [
                [
                  {
                    name: '建议时间',
                    xAxis: props.markTeamAreaRange[0] ?? '',
                  },
                  {
                    xAxis: props.markTeamAreaRange[1] ?? '',
                  },
                ],
              ],
            },
            z: 100,
          },
          {
            type: 'scatter',
            xAxisIndex: 1,
            encode: {
              x: 'dt_ekd',
              y: 'z',
            },
            markArea: {
              silent: true,
              label: {
                show: false,
              },
              itemStyle: {
                // 紫色
                color: 'rgba(213, 0, 255, 0.1)',
                borderColor: 'purple',
                borderWidth: 1,
              },
              data: [
                [
                  {
                    name: '建议时间',
                    xAxis: props.markQecAreaRange[0] ?? '',
                  },
                  {
                    xAxis: props.markQecAreaRange[1] ?? '',
                  },
                ],
              ],
            },
            z: 100,
          },
          {
            type: 'scatter',
            xAxisIndex: 1,
            encode: {
              x: 'dt_ekd',
              y: 'z',
            },
            markArea: {
              silent: true,
              label: {
                show: false,
              },
              itemStyle: {
                // 橘黄色
                color: 'rgba(255, 165, 0, 0.1)',
                borderColor: 'orange',
                borderWidth: 1,
              },
              data: [
                [
                  {
                    name: '建议时间',
                    xAxis: props.markTestAreaRange[0] ?? '',
                  },
                  {
                    xAxis: props.markTestAreaRange[1] ?? '',
                  },
                ],
              ],
            },
            z: 100,
          },
        ],
      }
      myChart.setOption(option)
      // 隐藏loading动画
      myChart.hideLoading()
      // 通过dispatchAction方法来触发刷选
      myChart.dispatchAction({
        type: 'brush',
        areas: [
          {
            brushType: 'lineX',
            coordRange: props.coordRange,
            xAxisIndex: 0,
          },
        ],
      })
      // 当拖动结束的时候触发的事件
      myChart.on('brushEnd', function (params) {
        moveRedBox(params)
      })

      // 点击事件
      myChart.on('click', function (params) {
        const realParams = Object.assign(params.data, { str_group: props.type })
        emit('partClick', realParams)
      })
    }

    const getRangeMin = (redRange, greenRange) => {
      let min = null
      if (redRange) {
        min = redRange
      } else if (greenRange) {
        min = greenRange
      }
      // 向前推一个月
      return min ? moment(min).subtract(1, 'month').format('YYYY-MM-DD') : null
    }
    // 拖动红框的触发的事件
    const moveRedBox = (params) => {
      const { areas } = params
      const [start, end] = areas[0].coordRange
      const newStart = moment(start).format('YYYY-MM-DD')
      const newEnd = moment(end).format('YYYY-MM-DD')
      emit('changeRedBox', { start: newStart, end: newEnd, group: props.type })
    }

    // * 改变发动机红框位置
    const changeEngineRedBox = (type, start, end) => {
      const myChart = echarts.init(document.getElementById(type))
      myChart.dispatchAction({
        type: 'brush',
        areas: [
          {
            brushType: 'lineX',
            coordRange: [start, end],
            xAxisIndex: 0,
          },
        ],
      })
    }

    // 监听窗口变化
    window.addEventListener('resize', () => {
      myChart.resize()
    })

    // 生命周期
    onMounted(() => {
      getChartList()
    })

    onBeforeMount(() => {
      window.removeEventListener('resize', () => {
        myChart.resize()
      })
    })

    return {
      changeEngineRedBox,
      getChartList,
    }
  },
  template: /*html*/ `
    <div :id="type" style="width: 100%; height: 100%;"></div>
  `,
}

export default unitScatterChart
