.zoomable-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: grab;
  user-select: none;
  background-color: #f5f7fa;
}

.zoomable-container.is-dragging {
  cursor: grabbing;
}

.zoomable-content {
  position: relative;
  transition: transform 0.1s ease-out;
  will-change: transform;
}

.zoomable-container.is-dragging .zoomable-content {
  transition: none;
}

/* 缩放控制按钮样式 */
.zoom-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.zoom-controls .el-button {
  min-width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 4px;
}

/* 缩放信息显示 */
.zoom-info {
  position: absolute;
  bottom: 16px;
  left: 16px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

/* 禁用文本选择 */
.zoomable-container * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 滚动条样式 */
.zoomable-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.zoomable-container::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.5);
  border-radius: 4px;
}

.zoomable-container::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .zoom-controls {
    top: 8px;
    right: 8px;
    padding: 4px;
    gap: 4px;
  }
  
  .zoom-controls .el-button {
    min-width: 28px;
    height: 28px;
  }
  
  .zoom-info {
    bottom: 8px;
    left: 8px;
    font-size: 11px;
  }
} 