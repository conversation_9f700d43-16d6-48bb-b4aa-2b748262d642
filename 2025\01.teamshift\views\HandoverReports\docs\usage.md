# 交接统计报表组件使用文档

## 1. 组件概述

交接统计报表组件是一个基于Vue 3和Composition API开发的可视化统计工具，用于展示和分析交接事项的提交和接收情况。该组件提供了直观的图表展示、数据筛选和钻取功能，帮助用户跟踪交接进度和状态。

## 2. 功能特点

- **交接提交统计**：以环形图展示已提交和未提交的交接事项数量及比例
- **交接接收统计**：以环形图展示已接收和未接收的交接事项数量及比例
- **多维度筛选**：支持按时间范围和业务类型进行数据筛选
- **数据钻取**：点击图表可查看详细的数据列表（通过抽屉组件展示）
- **分页展示**：详细数据支持分页浏览，可自定义每页显示条数

## 3. 如何使用

### 3.1 基本引入方式

```html
<!-- 在页面中引入组件相关的JS文件 -->
<script src="./js/filter.js"></script>
<script src="./js/submission-chart.js"></script>
<script src="./js/receiving-chart.js"></script>
<script src="./js/app.js"></script>
```

### 3.2 挂载点

组件需要一个挂载点，通常是一个div元素：

```html
<div id="app"></div>
```

### 3.3 使用方法

组件会自动初始化并挂载到指定的DOM节点。完整的页面示例：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交接统计报表</title>
    <link rel="stylesheet" href="../../../styles/tailwind.css">
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-plus/dist/index.css">
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- Vue 3 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.5.13/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.9.5/dist/index.full.js"></script>
</head>
<body class="bg-gray-50">
    <!-- 应用挂载点 -->
    <div id="app"></div>

    <!-- 导入模块 -->
    <script src="./js/filter.js"></script>
    <script src="./js/submission-chart.js"></script>
    <script src="./js/receiving-chart.js"></script>
    <script src="./js/app.js"></script>
</body>
</html>
```

## 4. 使用场景示例

### 4.1 数据筛选

1. 选择时间范围：
   - 点击日期选择器，可以选择自定义的日期范围
   - 也可以使用快捷选项（本周、本月、本季度）

2. 选择业务类型：
   - 从下拉列表中选择一个或多个业务类型（F1-2、F2、F1分解、F4装配、分配检验、装配检验）
   - 可以多选，也可以不选（表示所有类型）

3. 应用筛选：
   - 点击"应用筛选"按钮应用选择的筛选条件
   - 点击"重置"按钮清除所有筛选条件

### 4.2 数据钻取

1. 在交接提交统计图表中：
   - 点击"已提交"部分，查看已提交的交接事项详情
   - 点击"未提交"部分，查看未提交的交接事项详情

2. 在交接接收统计图表中：
   - 点击"已接收"部分，查看已接收的交接事项详情
   - 点击"未接收"部分，查看未接收的交接事项详情

3. 详情查看：
   - 在抽屉中查看详细列表
   - 使用分页控件浏览更多数据
   - 可以调整每页显示的记录数

## 5. 注意事项

- 该组件依赖于Vue 3、Element Plus和ECharts，使用前请确保已正确引入这些库
- 组件使用Composition API开发，需要Vue 3.0+版本支持
- 图表区域会根据窗口大小自动调整，在小屏幕上会自动切换为单列布局
- 初次加载时会请求默认数据，无需手动触发 