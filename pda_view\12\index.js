import ErrorComponent from '../../components/error.component.js'
import LoadingComponent from '../../components/loading.component.js'
import { currentDateKey, currentNodeKey, currentTypeViewKey, searchDateKey, searchFormKey } from '../../config/keys.js'
import { EXPORT_TRANSPORT_NODE } from '../../config/nodeKey.js'
import { PDA_VIEW_AD } from '../../config/tabPaneKey.js'
import { useCommApi } from '../hooks/useCommApi.js'

const { ref, reactive, provide, defineAsyncComponent, onMounted } = Vue
export default {
  name: 'ExportTransportComponent',
  components: {
    CardComponent: defineAsyncComponent({
      loader: () => import('../components/card.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    TotalViewComponent: defineAsyncComponent({
      loader: () => import('../components/total.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    PViewComponent: defineAsyncComponent({
      loader: () => import('../components/p.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    DMViewComponent: defineAsyncComponent({
      loader: () => import('../components/dm.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HeaderSearchComponent: defineAsyncComponent({
      loader: () => import('../components/HeaderSearch/index.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup() {
    const { getUsers, burialPoint } = useCommApi()
    const userS = ref([])
    const userST = ref([])
    const currentTypeView = ref(PDA_VIEW_AD)
    provide(currentTypeViewKey, {
      currentTypeView,
    })

    const startDate = ref(moment().format('YYYY-MM-DD'))
    const endDate = ref(moment().add(6, 'days').format('YYYY-MM-DD'))
    provide(searchDateKey, {
      startDate,
      endDate,
      updateSearchDate: (start, end) => {
        startDate.value = start
        endDate.value = end
      },
    })

    const currentDate = ref(moment().format('YYYY-MM-DD'))
    provide(currentDateKey, {
      currentDate,
      updateCurrentDate: date => {
        currentDate.value = date
      },
    })

    provide(currentNodeKey, EXPORT_TRANSPORT_NODE)
    const searchForm = reactive({
      date: [moment().format('YYYY-MM-DD'), moment().add(6, 'days').format('YYYY-MM-DD')],
      //id_engine_type: 'cfm56',
    })
    const refreshKey = ref(0)
    const updateSearchForm = form => {
      Object.assign(searchForm, form)
      refreshKey.value += 1
    }
    provide(searchFormKey, {
      searchForm,
      updateSearchForm,
    })
    // 查询
    const handleSearchClick = form => {
      updateSearchForm(form)
    }
    const userFilter = val => {
      if (val) {
        userST.value = userS.value.filter(x => x.str_name?.indexOf(val) > -1 || x.str_code?.indexOf(val) > -1)
      } else {
        userST.value = userS.value
      }
    }
    const cardTarget = ref(null)
    onMounted(async () => {
      burialPoint(PDA_VIEW_AD)
      userS.value = await getUsers('RULT')
      userST.value = await getUsers('RULT')
    })
    return {
      cardTarget,
      userS,
      userST,
      userFilter,
      handleSearchClick,
      refreshKey,
    }
  },
  // language=HTML
  template: `
    <el-tabs v-model="cardTarget" type="card">
      <el-tab-pane label="PDA View 视图" name="0" lazy>
        <div class="mx-4">
          <HeaderSearchComponent @search="handleSearchClick">
          <vxe-form-item title="Owner:" folding>
          <template #default="{data}">
            <el-select v-model.trim="data.str_staff" clearable filterable style="width: 210px;"
                       :filter-method='userFilter'>
              <el-option v-for="item in userST" :key="item.id" :label="item.str_name"
                         :value="item.str_name">{{ item.str_name }}-{{ item.str_code }}
              </el-option>

            </el-select>
          </template>
        </vxe-form-item>
          </HeaderSearchComponent>
        </div>
        <div class="w-full border-b border-gray-200 mb-4"></div>
        <!--    卡片-->
        <div class="mx-4">
          <CardComponent :key="refreshKey"></CardComponent>
        </div>
        <div class="w-full border-b border-gray-200 my-4"></div>
        <div class="mx-4">
          <TotalViewComponent :key="refreshKey"></TotalViewComponent>
        </div>

        <div class="w-full border-b border-gray-200 my-4"></div>
        <div class="mx-4">
          <PViewComponent :key="refreshKey"></PViewComponent>
        </div>
        <div class="mt-4"></div>
      </el-tab-pane>
      <el-tab-pane label="DM View 视图" name="1" lazy>
        <div class="mx-4">
          <DMViewComponent></DMViewComponent>
        </div>
      </el-tab-pane>
    </el-tabs>
  `,
}
