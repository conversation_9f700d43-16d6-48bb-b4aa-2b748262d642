const { ref, computed, watch } = Vue
export function useDynamicColumn(gantt, GANTT_COLUMNS) {
  const visibleColumns = ref(GANTT_COLUMNS.map((col) => col.name))
  // 为列配置组件准备的列定义
  const columnConfig = computed(() => {
    // 确保列的key与GANTT_COLUMNS中的name保持一致
    return GANTT_COLUMNS.map((col) => {
      return {
        key: col.name, // 保持key与甘特图列的name一致
        label: col.label,
        fixed: col.name === 'text', // 任务名称列设为固定列
      }
    })
  })

  // 更新甘特图列显示
  const updateGanttColumns = (columns) => {
    if (gantt.config && gantt.config.columns) {
      // 1. 保留原始GANTT_COLUMNS的完整属性
      const updatedColumns = GANTT_COLUMNS.filter(
        (col) =>
          // 过滤出选中的列和固定列
          columns.includes(col.name) || col.name === 'text',
      )

      // 2. 更新甘特图配置
      gantt.config.columns = updatedColumns

      // 3. 刷新甘特图布局和数据
      gantt.render()
    }
  }
  // 监听列显示变化，更新甘特图显示
  watch(
    visibleColumns,
    (newColumns) => {
      if (gantt && typeof gantt.config !== 'undefined') {
        updateGanttColumns(newColumns)
      }
    },
    { deep: true, immediate: true },
  )

  return {
    visibleColumns,
    columnConfig,
  }
}
