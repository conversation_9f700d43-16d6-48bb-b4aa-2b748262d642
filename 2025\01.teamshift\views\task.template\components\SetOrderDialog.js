import { setOrder, checkOrder } from '../../../api/task/index.js'

const { defineComponent, ref, onMounted } = Vue
const { ElMessage, ElMessageBox } = ElementPlus
const { useVModel } = VueUse

export const SetOrderDialog = defineComponent({
  name: 'SetOrderDialog',
  props: {
    modelValue: { type: Boolean, default: false },
    id: { type: String, default: '' },
    group: { type: String, default: '' },
    idWo: { type: String, default: '' },
    flow: { type: String, default: '' },
    currentSort: { type: Number, default: 0 },
    engineType: { type: String, default: '' },
  },
  emits: ['update:modelValue', 'refresh'],
  setup(props, { emit }) {
    const isSetOrderVisible = useVModel(props, 'modelValue', emit)

    // 状态管理
    const dialogForm = ref({ int_group_sort: 0 })

    // 业务逻辑
    const handleCancel = () => {
      isSetOrderVisible.value = false
    }

    /**
     * 判断是否存在顺序
     * @param {number} order
     * @return
     */
    const hasExistOrder = async (order) => {
      const params = {
        id_wo: props.idWo,
        str_group: props.group,
        int_group_sort: order,
        str_flow: props.flow,
        engine_type: props.engineType,
        id_main: props.id, // 传入id_main
      }

      const data = await checkOrder(params)
      return data
    }

    const confirmOrder = async (order) => {
      const params = {
        id: props.id,
        // id_wo: props.idWo,
        str_group: props.group,
        int_group_sort: order,
        str_flow: props.flow,
        engine_type: props.engineType,
      }
      await setOrder(params).then(() => {
        ElMessage.success('设置顺序成功')
        emit('refresh')
      })


      isSetOrderVisible.value = false
    }

    const handleSave = async () => {
      const order = parseInt(dialogForm.value.int_group_sort)
      const message = await hasExistOrder(order)
      if (message) {
        ElMessageBox.confirm(message, '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
        })
          .then((result) => {
            confirmOrder(order)
          })
          .catch((err) => { })
      } else {
        confirmOrder(order)
      }
    }

    onMounted(() => {
      dialogForm.value.int_group_sort = parseInt(props.currentSort)
    })

    return {
      isSetOrderVisible,
      dialogForm,
      handleCancel,
      handleSave,
    }
  },
  template: /*html*/ `
    <el-dialog class="common-dialog" v-model="isSetOrderVisible" title="设置顺序" width="25%">
      <div class="p-4">
        <el-form :model="dialogForm" label-width="120px">
          <el-form-item label="顺序">
            <el-input-number v-model="dialogForm.int_group_sort"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="danger" @click="handleCancel">关闭</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>
  `,
})
