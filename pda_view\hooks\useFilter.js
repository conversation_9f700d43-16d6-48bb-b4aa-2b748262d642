export function useFilter() {
  const getFilterData = (searchFilter) => {
    // 获取筛选条件
    const filter = []
    // 循环筛选条件
    for (const key in searchFilter) {
      // 过滤出时间筛选条件
      if (key !== 'date' && key !== 'dt_date' && key !== 'reloadTime') {
        // 判断筛选条件不为空
        if (searchFilter[key].toString()?.length > 0) {
          // 将筛选条件添加到filter数组中
          filter.push({
            str_key: key,
            str_value: typeof searchFilter[key] === 'number' ? searchFilter[key].toString() : searchFilter[key],
          })
        }
      }
    }
    return filter
  }
  // 重新组装所有筛选条件
  const getAllFilter = (searchFilter) => {
    // 获取筛选条件
    const filter = []
    // 循环筛选条件
    for (const key in searchFilter) {
      // 判断筛选条件不为空
      if (searchFilter[key]?.toString()?.length > 0) {
        // 将筛选条件添加到filter数组中
        filter.push({
          str_key: key,
          str_value: typeof searchFilter[key] === 'number' ? searchFilter[key].toString() : searchFilter[key],
        })
      }
    }
    return filter
  }
  return {
    getFilterData,
    getAllFilter,
  }
}
