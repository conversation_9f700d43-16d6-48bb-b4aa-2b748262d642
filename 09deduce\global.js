const { reactive, ref, nextTick, onMounted, onUpdated, defineAsyncComponent } = Vue

export default {
    name: 'yGlobalDeuce', // 
    props: [],
    components: {
        // 外部组件
        // 'BorderBox12': BorderBox12,
        // 'y-mp-pn': yPnGlobal,
         'y-pn-scatter': defineAsyncComponent(() => import('../09deduce/pn.index.js')),
    },
    setup(props, ctx) {

        const form = reactive({
            str_esn: ''
        })
        const pnState = ref([
            { str_type_name: '未进入供应量', int_nunber: 10 },
            { str_type_name: '未进入供应量', int_nunber: 10 },
            { str_type_name: '未进入供应量', int_nunber: 10 },
            { str_type_name: '未进入供应量', int_nunber: 10 },
        ])
        /**处理状态 */
        const pnDisposeState = ref([
            { str_type_name: '缺件出厂', int_nunber: 10 },
            { str_type_name: 'F4等待', int_nunber: 10 },
            { str_type_name: '退客户库', int_nunber: 10 },
            { str_type_name: '退仓库', int_nunber: 10 },
        ])
        onMounted(() => {


        });
        onUpdated(() => {


        });
        return {
            form,
            pnState,
            pnDisposeState

        }
    },
    /*html*/
    template:`
    
    <el-container>
    <el-header>
      <el-form :inline="true" class="demo-form-inline" :model="str_esn" label-width="120px">
        <el-form-item label="ESN">
          <el-input v-model="form.str_esn" />
        </el-form-item>
      </el-form>
    </el-header>
    <el-container>
    <el-aside width="60px">顺序1</el-aside>
      <el-aside width="400px">
        <el-row>
          <el-col :span="12">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>666666 (E20240606)</span>
                </div>
              </template>
              <div>GP:2024-06-06</div>
              <div>Suggest:2024-06-06</div>
              <div>Delivery:2024-06-30</div>
              <div>Red:100</div>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card class="box-card">
              <template #header>
                PN State
              </template>


              <div v-for="o in pnState" :key="o" class="text item">
                <el-badge :value="o.int_nunber" class="item">
                  <el-button style="width: 80px">{{o.str_type_name}}</el-button>
                </el-badge>
              </div>


            </el-card>
          </el-col>
        </el-row>

      </el-aside>
      <el-main>
      <y-pn-scatter></y-pn-scatter>
      </el-main>

      <el-aside width="300px">
        <el-row>
          <el-col :span="12">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>666666 (E20240606)</span>
                </div>
              </template>
              <div>GP:2024-06-06</div>
              <div>Suggest:2024-06-06</div>
              <div>Delivery:2024-06-30</div>
              <div>Red:100</div>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card class="box-card">
              <template #header>
                PN Dispose State
              </template>


              <div v-for="o in pnDisposeState" :key="o" class="text item">
                <el-badge :value="o.int_nunber" class="item">
                  <el-button style="width: 80px">{{o.str_type_name}}</el-button>
                </el-badge>
              </div>


            </el-card>
          </el-col>
        </el-row>

      </el-aside>
    </el-container>
  </el-container>
  
  `,

}
