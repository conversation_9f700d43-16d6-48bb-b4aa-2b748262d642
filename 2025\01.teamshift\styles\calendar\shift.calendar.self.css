/* 现代化日历容器样式 */
.calendar-container {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

/* 日历单元格样式 */
.calendar-container .el-calendar-day {
  height: auto !important;
  min-height: 120px !important;
  padding: 0.5rem;
  transition: all 0.2s;
}

.calendar-container .el-calendar-day:hover {
  background-color: #f9fafb;
}

/* 日期单元格内容区域 */
.calendar-date-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 日期数字样式 */
.calendar-date-number {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.calendar-date-number.is-selected {
  color: #2563eb;
  font-weight: 600;
}

/* 班次标签样式 */
.shift-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

/* 任务标签样式 */
.task-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

/* 时间显示样式 */
.time-display {
  font-size: 0.75rem;
  color: #6b7280;
  margin-left: 0.25rem;
}

/* 班次容器 */
.shift-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

/* 任务容器 */
.task-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

/* 夜班颜色 */
.shift-badge.night-shift {
  background-color: #f1f5f9;
  color: #334155;
  border-color: #cbd5e1;
}

/* 白班颜色 */
.shift-badge.day-shift {
  background-color: #dbeafe;
  color: #1e40af;
  border-color: #bfdbfe;
}

/* 中班颜色 */
.shift-badge.middle-shift {
  background-color: #fed7aa;
  color: #c2410c;
  border-color: #fdba74;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .calendar-container .el-calendar-day {
    min-height: 100px !important;
  }
  
  .shift-badge,
  .task-badge {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
  }
}
