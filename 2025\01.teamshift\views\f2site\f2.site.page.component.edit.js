import {
  queryF2TargetEnum,
  queryEnum,
  queryF2TaskEnum,
  queryF2TeamPlanView, 
  queryShifts,
  saveF2TeamPlan,
  queryDeptStaff
} from '../../api/teams/index.js'
export default {
  name: 'F2SiteEdit',
  props: {
    areas: Object,
    visible: Boolean
    
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const { reactive, onMounted, ref } = Vue
    const { useVModel } = VueUse
    const f2SiteEditVisible = useVModel(props, 'visible')

    // 团队计划数据
    const siteData = reactive({
      id_team: '',
      str_site_code: ''
    })


    // 保存计划
    const handleSave = async () => {
       const params = {
        id_staff:props.planItem.id_staff,
        dt_shift:props.planItem.dt_shift,
        shifts: siteData,
      }
      await saveF2TeamPlan(params)
      f2SiteEditVisible.value = false
      emit('refresh')
    }
   

    const f2TaskOptions = ref([])
    // 获取Site 下拉选择
    const getF2TaskOptions = async () => {
      const res = await queryF2TaskEnum()
      f2TaskOptions.value = res ?? []
    }

    

    //const planMainItem = ref(null)

    // 获取编辑页的数据
    const getTeamPlanEditList = async () => {
      try {
        const res = await queryF2TeamPlanView(props.id_staff,props.dt_shift)
        if (!res) return 
        siteData = res
      } catch (error) {
        console.error('获取团队计划编辑数据失败:', error)
      }
    }


    onMounted(async () => {
      await getTeamPlanEditList()
      await getF2TaskOptions();
      
    })

    return {
      f2TaskOptions,
      f2SiteEditVisible,
      siteData,
      handleSave
    }
  },

  template: /*html*/ `
    <el-dialog
      v-model="f2SiteEditVisible"
      title="F2 Site Edit"
      width="40%"
     
      :fullscreen="false"
      :append-to-body="true"
    >
       
          <el-form :model="siteData" label-width="150px"> 
            <div>
              <el-form-item label="Site">
                <el-select v-model="siteData.staffs" multiple placeholder="请选择站位">
                  <el-option v-for="sub in props.areas" :key="sub.str_site_code" :label="sub.str_site_code" :value="sub.str_site_code" />
                </el-select>
              </el-form-item> 

              <el-form-item label="Team">
                <el-select v-model="siteData.id_f2_target"  placeholder="请选择Team" >
                  <el-option v-for="sub in f2TargetOptions" multiple :key="sub.id" :label="sub.str_esn" :value="sub.id" >
                  <span style="float: left">{{ sub.str_esn }}</span>
                  <span
                    style="float: right;
                      color: var(--el-text-color-secondary);
                      font-size: 13px;">
                    {{ sub.str_type }}
                  </span> 
                  </el-option>
                </el-select>
              </el-form-item> 
          </div>
        </el-form>
        <template #footer>
        <div class="flex justify-end">
          <el-button @click="f2SiteEditVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>
  `,
}
