const { useVModel } = VueUse
import { saveHandover, getTaskList } from '../api/index.js'
import { useDropdownOptions } from '../composables/useDropdownOptions.js'
import { useUploader } from '../composables/useUploader.js'
import { useFileHandler } from '../composables/useFileHandler.js'
import FileViewerComponent from '../../../components/Hanover/FileViewer.js'
import FileUploadButtonComponent from '../../../components/Hanover/FileUploadButton.js'
import FileTagsComponent from '../../../components/Hanover/FileTags.js'
import { useAddHandover } from '../composables/useAddHandover.js'

export default {
  name: 'AddHandoverComponent',
  components: {
    FileViewerComponent,
    FileUploadButtonComponent,
    FileTagsComponent,
  },
  props: {
    visible: {
      type: <PERSON>olean,
      required: true,
    },
    flow: {
      flow: String,
      required: true,
    },
    row: {
      type: Object,
      required: false,
    },
    column: {
      type: Object,
      required: false,
    },
    businessType: {
      type: Number,
      required: true,
    },
  },
  emits: ['update:visible', 'refresh'],
  setup(props, { emit }) {
    const { ElMessage } = ElementPlus

    const dialogVisible = useVModel(props, 'visible', emit)

    const { tableData, queryParams, loading, headerTips, mainData, handleAddRow } = useAddHandover(props)

    // 初始化文件处理器
    const fileHandler = useFileHandler()

    // 初始化文件上传器
    const uploader = useUploader()

    // 删除行数据
    const handleDeleteRow = (row) => {
      const index = tableData.value.findIndex((item) => item === row)
      if (index !== -1) {
        tableData.value.splice(index, 1)
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      // 构建主表数据
      const main = {
        id: mainData.value?.id || '',
        dt_pt: queryParams.dt_pt || '',
        id_shift: queryParams.id_shift || '',
        str_shift: queryParams.shiftName || '',
        str_handover_type: props.businessType,
        id_by_to_receive: queryParams.id_by_to_receive || '',
        is_pending: queryParams.is_pending || '0',
        str_task_type: queryParams.str_task_type || '',
        is_completed: queryParams.is_completed || '0',
      }
      const handoverData = tableData.value.map((item) => ({
        pTHandover: {
          id: item.id || '', // 交接单ID
          int_type: item.code, // 使用code作为类型
          str_category: item.legend, // 类别
          str_help: item.tip, // 提示信息
          str_content: item.desc || '', // 描述内容
          int_status: item.isTransferred ? 1 : 0, // 是否交接状态
        },
        files: (item.attachment || []).map((file) => ({
          id: file.id || '',
          str_path: file.str_path,
          str_file_name: file.str_file_name || file.name,
        })),
      }))
      const params = []
      params.push({
        pTHandoverMain: main,
        pTHandovers: handoverData,
      })
      await saveHandover(params)
      ElMessage.success('提交成功')
      dialogVisible.value = false
      emit('refresh')
    }

    const dropdownOptions = useDropdownOptions()

    // 计算每个Legend的行数，用于合并单元格
    const getSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1) {
        // Code和Legend列
        const rows = tableData.value || []
        if (!rows.length) return { rowspan: 1, colspan: 1 }

        const currentCode = row.code
        if (!currentCode) return { rowspan: 1, colspan: 1 }

        // 向上找到第一个具有相同code的行的索引
        let startIndex = rowIndex
        while (startIndex > 0 && rows[startIndex - 1] && rows[startIndex - 1].code === currentCode) {
          startIndex--
        }

        // 如果是该分组的第一行
        if (rowIndex === startIndex) {
          // 计算具有相同code的行数
          let spanCount = 1
          let nextIndex = rowIndex + 1
          while (nextIndex < rows.length && rows[nextIndex] && rows[nextIndex].code === currentCode) {
            spanCount++
            nextIndex++
          }
          return {
            rowspan: spanCount,
            colspan: 1,
          }
        } else {
          // 不是第一行则隐藏
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
      return { rowspan: 1, colspan: 1 }
    }

    const handBeforeClose = () => {
      dialogVisible.value = false
    }

    return {
      dialogVisible,
      headerTips,
      queryParams,
      tableData,
      loading,
      handleSubmit,
      handleAddRow,
      handleDeleteRow,
      getSpanMethod,
      // 使用组合式函数提供的功能
      ...fileHandler, // 提供 drawerVisible, currentFiles, handleViewFiles, handleDownload 等
      ...uploader, // 提供 handleUpload 等
      ...dropdownOptions,
      handBeforeClose,
    }
  },
  template: /*html*/ `
    <el-drawer
      class="common-drawer"
      v-model="dialogVisible"
      size="80%"
      :destroy-on-close="true"
      :before-close="handBeforeClose"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <div class="text-lg font-semibold">{{ queryParams.flow }}交接班</div>
        </div>
      </template>
      <div class="mb-4 rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4 shadow-sm">
        <div class="flex items-center">
          <div class="flex w-full cursor-help items-center">
            <el-icon class="mr-2 flex-shrink-0" :size="20"><InfoFilled /></el-icon>
            <div class="min-w-0 flex-1">
              <div class="mb-1 text-lg font-semibold text-red-700">交接提示</div>
              <div class="whitespace-pre-line text-red-600">{{ headerTips }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Flow/Team/班次/工序信息卡片 -->
      <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-3">
        <div class="rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 p-2 shadow-sm">
          <div class="flex items-center space-x-3">
            <div class="flex items-center justify-center rounded-full bg-blue-100">
              <el-icon class="text-blue-600" :size="20"><Promotion /></el-icon>
            </div>
            <div>
              <div class="text-sm text-gray-500">当前Flow</div>
              <div class="text-lg font-semibold">{{ flow }}</div>
            </div>
          </div>
        </div>

        <div class="rounded-lg bg-gradient-to-r from-purple-50 to-pink-50 p-2 shadow-sm">
          <div class="flex items-center space-x-3">
            <div class="flex items-center justify-center rounded-full bg-purple-100">
              <el-icon class="text-purple-600" :size="20"><User /></el-icon>
            </div>
            <div>
              <div class="text-sm text-gray-500">当前Team</div>
              <div class="text-lg font-semibold">{{ queryParams.teamName }}</div>
            </div>
          </div>
        </div>
        <div class="rounded-lg bg-gradient-to-r from-purple-50 to-pink-50 p-2 shadow-sm">
          <div class="flex items-center space-x-3">
            <div class="flex items-center justify-center rounded-full bg-purple-100">
              <el-icon class="text-purple-600" :size="20"><Clock /></el-icon>
            </div>
            <div>
              <div class="text-sm text-gray-500">当前班次</div>
              <div class="text-lg font-semibold">{{ queryParams.shiftName }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex mb-4 items-center gap-4 rounded-lg border border-blue-200 bg-blue-50 p-3 shadow-sm">
        <div class="flex items-center space-x-3">
          <el-icon class="text-blue-600" :size="20"><Tools /></el-icon>
          <span class="text-sm font-medium text-gray-700">工序:</span>
          <div class="w-48">
            <el-select v-model="queryParams.str_task_type" placeholder="请选择工序" filterable clearable>
              <el-option v-for="item in taskTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <el-icon class="text-blue-600" :size="20"><User /></el-icon>
          <span class="text-sm font-medium text-gray-700">接收人:</span>
          <div class="w-48">
            <el-select v-model="queryParams.id_by_to_receive" placeholder="请选择接收人" filterable clearable>
              <el-option v-for="item in staffOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <el-icon class="text-blue-600" :size="20"><MostlyCloudy/></el-icon>
          <span class="text-sm font-medium text-gray-700">PENDING:</span>
          <div class="w-48">
            <el-select v-model="queryParams.is_pending" placeholder="请选择PENDING">
              <el-option :key="1" label="是" value="1"></el-option>
              <el-option :key="0" label="否" value="0"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex items-center space-x-3">
         <!-- <el-icon class="text-blue-600" :size="20"><MostlyCloudy/></el-icon>-->
          <div class="w-48">
           <el-checkbox border v-model="queryParams.is_completed" label="完成(最后一个任务不需要接收)"  :true-label="1" :false-label="0" size="large" />
            
          </div>
        </div>
      </div>

      <!-- 表格数据 -->
      <el-table
        v-if="tableData.length > 0"
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
        :span-method="getSpanMethod"
      >
        <el-table-column prop="code" label="Code" width="80" align="center" />
        <el-table-column prop="legend" label="Legend" min-width="60">
          <template #default="{ row }">
            <div class="flex items-center">
              <div class="min-w-0 flex-1">{{ row.legend }}</div>
              <el-tooltip v-if="row.tip" effect="dark" placement="top" :content="row.tip">
                <el-icon class="ml-1 text-gray-400" :size="16"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="desc" label="Desc" min-width="300">
          <template #default="{ row }">
            <el-input type="textarea" v-model="row.desc" :rows="2" placeholder="请输入内容" resize="none" />
          </template>
        </el-table-column>
        <el-table-column label="Attachment" min-width="100" align="center">
          <template #default="{ row }">
            <div class="flex items-center">
              <FileUploadButtonComponent
                :row="row"
                :hasFiles="row.attachment?.length > 0"
                @upload="handleUpload"
                @view="handleViewFiles"
              />
              <FileTagsComponent
                v-if="row.attachment?.length > 0"
                :files="row.attachment"
                @view="() => handleViewFiles(row)"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="Operate" width="120" align="center">
          <template #default="{ row }">
            <div class="flex items-center justify-center space-x-2">
              <el-button v-if="!row.isOriginal" type="danger" link @click="handleDeleteRow(row)">删除</el-button>
              <el-button v-if="row.isOriginal" type="primary" link @click="handleAddRow(row)">新增</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="无交接" width="80" align="center">
          <template #default="{ row }">
            <!-- 使用html的选择框 -->
            <input type="checkbox" class="border-blue-500" v-model="row.isTransferred" />
          </template>
        </el-table-column>
      </el-table>
      <div v-else-if="loading" class="flex h-32 items-center justify-center text-gray-500">加载中...</div>
      <div v-else class="flex h-32 items-center justify-center text-gray-500">暂无数据，请先查询</div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handBeforeClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </span>
      </template>

      <!-- 文件查看抽屉 -->
      <FileViewerComponent
        v-model:visible="drawerVisible"
        :files="currentFiles"
        @download="handleDownload"
        @delete="handleDelete"
      />
    </el-drawer>
  `,
}
