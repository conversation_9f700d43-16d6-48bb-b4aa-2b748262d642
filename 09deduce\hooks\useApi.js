import { post } from '../../config/axios/httpReuest.js'
export function useApi() {
  // 公用的下拉框请求接口
  const getCommonOptionApi = async (type) => {
    const params = {
      ac: 'de_enum',
      str_key: type,
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      return data.data.map((item) => {
        return {
          label: item.str_key,
          value: item.str_value,
        }
      })
    } else {
      ElementPlus.ElMessage.error('type:' + type + data.text)
      return []
    }
  }

  // 标记零件属性接口
  const markPartApi = async (id, markType, idList, opType,int_type_sub, str_test) => {
    const params = {
      ac: 'de_markpending',
      id_wo: id,
      int_mark_type: markType,
      idlist: idList,
      int_op_type: opType, // 0 新增 1 移除
      int_mark_typ_sub:int_type_sub,
      str_test: str_test,
      
    }
    const { data } = await post(params)
    if (data.code === 'success') {
    } else {
      ElementPlus.ElMessage.error(data.text)
    }
  }

  /**
   * @description: 执行模拟结果接口
   * @param {String} id_wo
   * @return { promise }
   */
  const runSimulationApi = async (id_wo) => {
    const params = {
      ac: 'de_calculate',
      id_wo,
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      ElementPlus.ElMessage.success(data.text)
      return data.data
    } else {
      ElementPlus.ElMessage.error(data.text)
      return null
    }
  }

  return {
    getCommonOptionApi,
    markPartApi,
    runSimulationApi,
  }
}
