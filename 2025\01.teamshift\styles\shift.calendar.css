.schedule-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden; /* 防止出现双滚动条 */
  position: relative;  /* 新增 */
}

.schedule-table {
  flex: 1;
  height: 100%;
}

.schedule-table .el-table__cell {
  padding: 0 !important;
}

.header-content {
  font-weight: bold;
}

.task-list {
  padding: 4px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding-bottom: 40px; /* 为底部按钮留出足够空间 */
}

/* 任务容器 */
.tasks-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: calc(100% - 40px); /* 减去底部按钮区域的高度 */
  margin-bottom: 4px; /* 与底部按钮区域保持一定距离 */
  border: 1px solid #f0f0f0; /* 一个更柔和的边框 */
  border-radius: 4px;
  padding: 8px; /* 增加一些内边距 */
  min-height: 50px; /* 确保即使为空也有一定高度，更容易看清 */
  background-color: #f9f9f9; /* 非常浅的背景色，以区分单元格背景 */
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out; /* 为可能的悬停效果准备 */
}

/* 当有任务拖动到某个 .tasks-container 上方时的激活样式 */
/* 您需要在 SortableJS 的 onMove 或类似事件中动态给目标 .tasks-container 添加此类名 */
.tasks-container.drag-over-active {
  background-color: #e6f7ff; /* 淡蓝色背景 */
  border-color: #91d5ff; /* 更亮的蓝色边框 */
}

.task-item {
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 12px;
  cursor: move;
  display: flex;
  flex-direction: column;
  margin-bottom: 4px;
  user-select: none;
  transition: all 0.3s ease-in-out;
}

.task-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.task-content {
  flex: 1;
  padding: 4px 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.task-content span:nth-child(2) {
  flex: 1;
  text-align: center;
}

.task-content span:last-child {
  font-size: 11px;
  color: #606266;
  flex-shrink: 0;
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #909399;
}

/* 任务状态样式 */
.status-day {
  background-color: #e1f3ff;
  color: #409EFF;
}

.status-night {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.status-more {
  background-color: #f4f4f5;
  color: #909399;
  cursor: pointer;
}

.non-draggable {
  cursor: not-allowed;
  opacity: 0.7;
}

.is-drop-target {
  background-color: #f5f7fa;
}

.out-of-range {
  background-color: #f5f7fa;
  opacity: 0.7;
}

.view-more {
  text-align: center;
  margin-top: 4px;
}

/* 拖拽相关样式 */
.sortable-ghost {
  visibility: visible !important; /* 确保总是可见 */
  background-color: #d1e9ff; /* 清晰的淡蓝色背景 */
  border: 2px dashed #409eff; /* Element UI 主题色的虚线边框 */
  opacity: 0.8;
  border-radius: 4px;
  color: transparent; /* 隐藏原始文本内容 */
  box-sizing: border-box;
  height: 36px; /* 示例高度，请根据您的 task-item 调整 */
  margin: 2px 0; /* 示例外边距 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.sortable-ghost::before {
  content: "放置于此"; /* 提示文字 */
  color: #409eff;
  font-size: 12px;
  font-weight: bold;
}

.task-item.sortable-drag { /* 跟随鼠标的克隆体 */
  opacity: 0.9 !important;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
  transform: rotate(2deg) scale(1.03); /* 轻微旋转和放大，增加拖动感 */
  background-color: white; /* 确保有背景 */
  z-index: 20; /* 确保在最上层 */
}

.task-item.is-dragging { /* 原始拖动项 */
  opacity: 0.5 !important; /* 使原始拖动项半透明 */
  background: #cce5ff; /* 给一个淡淡的背景色 */
  box-shadow: 0 4px 12px rgba(0,0,0,0.15); /* 添加阴影使其"浮起" */
  transform: scale(1.02); /* 轻微放大 */
  z-index: 10;
}

/* 弹窗样式 */
.task-dialog-content {
  padding: 10px;
}

.task-dialog-content .task-item {
  margin-bottom: 8px;
}

/* 表格样式 */
.el-table .cell {
  white-space: nowrap;
  padding: 2px !important;
}

.el-table__cell {
  padding: 2px !important;
}

.el-table__header th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  padding: 8px 0;
}

/* 多级表头样式 */
.el-table__header tr:first-child th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: bold;
}

.el-table__header tr:last-child th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: normal;
}

/* 拖拽区域样式 */
.task-list.is-dragover {
  background-color: rgba(64, 158, 255, 0.1);
  outline: 2px dashed #409EFF;
  outline-offset: -2px;
}

.search-form {
  flex-shrink: 0;
  margin-bottom: 20px;
}

/* 表格滚动容器样式 */
.el-table__body-wrapper {
  overflow-x: auto !important;
  overflow-y: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #909399 #f4f4f5;
}

/* 自定义滚动条样式 */
.el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 4px;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #f4f4f5;
}

/* 确保表格内容不会被压缩 */
.el-table__body,
.el-table__header {
  min-width: 100%;
}

/* 底部按钮区域 */
.cell-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-top: 1px solid #ebeef5;
  width: 100%;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.05); /* 添加阴影效果增强视觉分离 */
}

.cell-actions .el-button {
  padding: 2px 4px;
  font-size: 12px;
}

.cell-actions .el-button.is-locked {
  color: #909399;
}

.cell-actions .el-button.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 锁定状态样式 */
.task-list[data-expanded="true"][data-locked="true"] {
  overflow: hidden;
}

.task-item.is-locked {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  filter: grayscale(30%);
}

.task-list[data-locked="true"] {
  background-color: #f5f7fa;
  opacity: 0.9;
}

.task-list[data-locked="true"] .task-item {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  filter: grayscale(30%);
}

.task-list[data-locked="true"] .cell-actions {
  background-color: #f5f7fa;
  opacity: 0.9;
}

/* 加载遮罩层样式 */
.loading-mask {
  position: absolute;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.8);
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  text-align: center;
}

.loading-text {
  margin-top: 10px;
  color: #409EFF;
  font-size: 14px;
}

.el-icon.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
