import { post } from '../../config/axios/httpReuest.js'

const { reactive } = Vue
const { ElMessage } = ElementPlus
export function useGuaranteeDelivery(props, emit) {
  const guaranteeDelivery = reactive({
    visible: false,
    form: {
      int_type: 1,
      dt_delivery: null,
      id_pkp: [],
    },
  })
  const handleEnsureDelivery = async () => {
    const params = {
      ac: 'de_pndeliverysave',
      int_type: guaranteeDelivery.form.int_type,
      id_pkp: guaranteeDelivery.form.id_pkp,
      dt_delivery: guaranteeDelivery.form.dt_delivery,
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      ElMessage.success('保交付成功')
      guaranteeDelivery.visible = false
      emit('submit', props.idWo)
    } else {
      ElMessage.error('保交付失败')
    }
  }
  const closeGuaranteeDelivery = () => {
    guaranteeDelivery.visible = false
  }

  return { guaranteeDelivery, handleEnsureDelivery, closeGuaranteeDelivery }
}
