import { post } from '../../config/axios/httpReuest.js'

// 获取登录人角色
export const queryUserRole = () => {
  const params = {
    ac: 'comm_user_roles',
  }
  return post(params)
}

// 公共的获取枚举接口
export const commonEnum = (key) => {
  const params = {
    ac: 'common_get_sys_enum_list',
    key,
  }
  return post(params)
}

// 获取证书信息
export const getCertificateInfo = (queryParams) => {
  const { id_pkp, int_page } = queryParams
  const params = {
    ac: 'de_getcertinfo',
    id_pkp,
    int_page,
  }
  return post(params)
}
