import { post } from '../../config/axios/httpReuest.js';
import { useFilter } from '../hooks/useFilter.js';
import { useTableColumn } from './useTableColumn.js';

const { ref, reactive, nextTick } = Vue;

export function useCharData() {
  const { getChartTableColumn } = useTableColumn();
  const { getAllFilter } = useFilter();
  const barOption = {
    grid: {
      left: '0%',
      right: '10%',
      bottom: '5%',
      top: '1%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        show: true,
        margin: 10,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    series: [{
      type: 'bar',
      data: [],
      label: {
        show: true,
        position: 'right',
      },
      itemStyle: {
        barBorderRadius: [0, 10, 10, 0],
      },
    }],
  };

  const chartRef = ref(null);
  const chartState = reactive({
    total: 0,
    intSiteArr: [],
    zeroData: ''
  });

  // 获取总数为0的数据
  const getZeroData = (data) => {
    const zeroData = data.filter((item) => item.int_num === 0);
    // 去掉(0),并且转换为字符串, 并用逗号分隔，给每一个字符串前后加上双引号
    return zeroData.map((item) => `"${item.str_site.replace(/\(\d+\)/, '')}"`).join('、');
  };

  // get chart data
  const getChartData = async (searchForm, nodes) => {
    const myChart = echarts.init(chartRef.value);
    myChart.showLoading({
      text: 'loading...',
      color: '#c23531',
      textColor: '#000',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      showSpinner: true,
    });
    const params = {
      ac: 'pda_statisticalsitenegative',
      filter_fields: getAllFilter(searchForm),
	   nodes:nodes
    };
    const { data } = await post(params);
	 if (nodes && nodes.length) {
      data.data = data.data.filter((item) => nodes.includes(item.int_site));
    }
    chartState.intSiteArr = data.data.map((item) => item.int_site);
    barOption.yAxis.data = data.data.map((item) => item.str_site);
    barOption.series[0].data = data.data.map((item) => item.int_num);
    chartState.total = barOption.series[0].data.reduce((prev, curr) => prev + curr, 0);
    chartState.zeroData =  getZeroData(data.data)
    initChart(myChart, searchForm);
  };
  const initChart = (chart, searchForm) => {
    chart.setOption(barOption);
    chart.hideLoading();
    window.addEventListener('resize', () => {
      chart.resize();
    });
    chart.on('click', 'series', (params) => {
      clickSeries(params, searchForm);
    });
  };

  const charDrawerState = reactive({
    isShowDrawer: false,
    drawerTableData: null,
    tableColumn: getChartTableColumn(),
    totalNum: 0,
  });
  // 点击series
  const clickSeries = (params, searchForm) => {
    const clickIndex = params.dataIndex;
    const intSiteValue = chartState.intSiteArr[clickIndex];
    const param = {
      ac: 'pda_statisticalsitenegativeList',
      str_type: intSiteValue === '0' ? '' : intSiteValue,
      filter_fields: getAllFilter(searchForm),
    };
    charDrawerState.isShowDrawer = true;
    charDrawerState.drawerTableData = null;
    charDrawerState.totalNum = 0;
    nextTick(async () => {
      const { data } = await post(param);
      if (data.code === 'success') {
        charDrawerState.drawerTableData = data.data;
        charDrawerState.totalNum = data.data.length;
        charDrawerState.isLoading = false;
      } else {
        ElementPlus.ElMessage.error(data.text);
      }
    });
  };

  // click total
  const handleTotalClick = (searchForm,nodes) => {
    charDrawerState.isShowDrawer = true;
    charDrawerState.drawerTableData = null;
    charDrawerState.totalNum = 0;
    const param = {
      ac: 'pda_statisticalsitenegativeList',
      str_type: '',
      filter_fields: getAllFilter(searchForm),
	    nodes:nodes
    };
    nextTick(() => {
      post(param).then((res) => {
        charDrawerState.drawerTableData = res.data.data;
        charDrawerState.totalNum = res.data.data.length;
      });
    });
  };

  return {
    getChartData,
    chartRef,
    chartState,
    charDrawerState,
    handleTotalClick,
  };
}
