const PagePager = {
  setup(props, { attrs }) {
    return {};
  },
  // language=HTML
  template: `
    <vxe-pager
      background
      v-model:current-page="$attrs.currentPage"
      v-model:page-size="$attrs.pageSize"
      :total="$attrs.total"
      :layouts="['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'End', 'Sizes', 'FullJump', 'Total']"
      @page-change="$attrs.onPageChange"
    ></vxe-pager>
  `,
};

export default PagePager;
