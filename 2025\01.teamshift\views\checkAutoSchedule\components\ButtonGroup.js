const { ref } = Vue
export default {
  name: 'ButtonGroup',
  props: {
    isReSchedule: {
      type: Boolean,
      default: false,
    },
  },
  emit: ['assignTask', 'ganttChart', 'reSchedule'],
  setup(props, { emit }) {
    const handleAssignTask = () => {
      emit('assignTask')
    }
    const handleGanttChart = () => {
      emit('ganttChart')
    }
    const handleReSchedule = () => {
      emit('reSchedule')
    }
    return {
      handleAssignTask,
      handleGanttChart,
      handleReSchedule,
    }
  },
  template: /*html */ `
    <div class="flex mb-2">
      <el-button type="primary" @click="handleAssignTask">分配任务</el-button>
      <el-button type="primary" @click="handleGanttChart">展开甘特图</el-button>
      <el-button type="primary" @click="handleReSchedule" :loading="isReSchedule">重排</el-button>
    </div>
  `,
}
