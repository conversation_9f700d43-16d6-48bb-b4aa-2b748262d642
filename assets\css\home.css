.table-container {
    /* 样式代码，根据实际情况进行编写 */
    margin-top: 10px;
    font-size: 14px;
    margin-top: 10px;
    font-size: 14px;
    display: flex;
    /* height: 95%; */
    flex-direction: column;
    justify-content: space-between;

}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.field-name-row td {
    color: #8f8e8d;
}

.field-value-row td {
    color: #333333;
    font-weight: bold;
}

td {
    border: 0px solid #ddd;
    text-align: left;
    padding: 1px 0 1px 10px;
    width: 100px;
}

.demo-collapse {
    margin-left: 10px;
}

.bottom-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 10px;
}

.bottom-amount .name {
    color: #A7A7A7;
}

.bottom-amount .value {
    color: #333333;
}

.pagination {
    /* 分页控制的样式 */
    margin-right: 10px;
}

.pagination button {
    /* 页码按钮的样式 */
    margin: 0 0.5em;
}

.pagination-button, .button-page {
    border: 1px solid #E4E4E4;
    color: #A7A7A7;
    border-radius: 2px;
    background-color: transparent;
    cursor: pointer;
}

.pagination-button.active {
    background-color: #0DAAEE;
    color: white;
    border-color: #0DAAEE;
}

.button-page:disabled {
    background-color: #f3f3f3;
}

.data-body {
    background-color: #F9E2C3 !important;
    margin-bottom: 10px !important;
    display: table;
    border-radius: 5px;
    width: calc(100% - 20px);
    box-sizing: border-box;
}
