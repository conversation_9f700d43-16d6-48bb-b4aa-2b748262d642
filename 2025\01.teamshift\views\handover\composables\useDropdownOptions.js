import {
  fetchEsnOptions,
  fetchSmOptions,
  fetchShiftOptions,
  getStaffList,
  getEnumList,
  getF2TaskTypeList,
} from '../api/index.js'

const { ref, onMounted, watch } = Vue
export const useDropdownOptions = (esn, businessType) => {
  const esnOptions = ref([])
  const getEsnOptions = async () => {
    const res = await fetchEsnOptions()
    esnOptions.value = res
  }
  const taskTypeOptions = ref([])
  const getTaskTypeList = async () => {
    if (businessType == 101) {
      const res = await getEnumList('pt_f12_task_type')
      taskTypeOptions.value = res.map((item) => ({
        value: item.str_key,
        label: item.str_value,
      }))
    }
    if (businessType == 102) {
      const res = await getF2TaskTypeList()
      taskTypeOptions.value = res.map((item) => ({
        value: item.str_value,
        label: item.str_key,
      }))
    }
  }

  const smOptions = ref([])
  const getSmOptions = async () => {
    if (!esn || !esn.value) return
    const res = await fetchSmOptions(esn.value)
    smOptions.value = res
  }

  const shiftOptions = ref([])

  const getShiftOptions = async () => {
    const res = await fetchShiftOptions()
    shiftOptions.value = res.data
      // .filter((_it) => _it.int_type === 2 || _it.int_type === 3)
      .map((_it) => {
        return {
          value: _it.id,
          label: _it.str_name,
        }
      })
  }
  const staffOptions = ref([])
  const getStaffOptions = async () => {
    const res = await getStaffList()
    staffOptions.value = res.map((item) => ({
      value: item.id,
      label: item.str_name,
    }))
  }

  // 只有当 esn 不为 null 时才监听
  if (esn) {
    watch(esn, () => {
      getSmOptions()
    })
  }

  onMounted(() => {
    getEsnOptions()
    getTaskTypeList()
    getShiftOptions()
    getStaffOptions()
  })

  return {
    esnOptions,
    taskTypeOptions,
    smOptions,
    shiftOptions,
    staffOptions,
  }
}
