const { reactive, ref, nextTick, onMounted, onUpdated } = Vue
import { post } from '../../../config/axios/httpReuest.js';
export default {
    name: 'yPnGlobal', // 零件组件
    props: ['id', 'id_wo'],
    components: {
        // 外部组件

    },
    setup(props, ctx) {
        const formInline = ref({})
        const activeName = ref(["1", "2"])
        const height = ref(500)
        const pnDialog = reactive({
            isShow: true

        })

        const tableData =ref([]) 
        const tableRowClassName = (row, rowIndex) => {
            if (rowIndex === 1) {
                return 'warning-row'
            } else if (rowIndex === 3) {
                return 'success-row'
            }
            return ''
        }
        /**查询 */
        const query = async () => {
            const params = {
                au: 'ssamc',
                ap: 'api2018',
                ak: '',
                ac: 'mp_get_pn_global_view',
                str_esn:"602263",
                str_sm:'MM00'
            }
            const { data } = await post(params);

            if (data.code === 'success') {
                tableData.value = data.data;
            } else {
                console.log(data.message)
            }
        }
    /**时间格式 */
    const momentF = (data) => {
        return moment(data).format('YYYY-MM-DD')
      }
        onMounted(() => {
            query();
            nextTick(() => {
                setTimeout(() => {
                    height.value = "800px"

                }, 3000)
            });

        });
        onUpdated(() => {
            nextTick(() => {

                setTimeout(() => {
                    height.value = "1000px"

                }, 3000)

            });

        });
        return {
            formInline,
            activeName,
            tableData,
            tableRowClassName,
            height,
            pnDialog,
            query,
            momentF
        }
    },
    template: /*template*/`

<el-card class="box-card">
<!-- <template #header>
    < <div class="card-header">
        <el-row >  
        <el-col :span="4">  <span style="font-size: 12px;font-weight: 800">660327 </span>   </el-col>
        <el-col :span="4">  <span style="font-size: 12px;font-weight: 800">CFMI/GE/SDAL   </span>   </el-col>
        <el-col :span="4">  <span style="font-size: 12px;font-weight: 800"> EKD:2023-11-28 </span>   </el-col>
        <el-col :span="4"> 总周期： <span style="font-size: 12px;font-weight: 800"> 60  天    </span>   </el-col>
        <el-col :span="4">  <span style="font-size: 12px;font-weight: 800"> TAT: 10 天    </span>   </el-col>
        <el-col :span="4">  <span style="font-size: 12px;font-weight: 800">   F3 TAT: 8 天     </span>   </el-col>
                                   
        </el-row>
      <el-button class="button" text>Operation button</el-button>
      </div>
    </template>-->
    <el-row  :gutter="5"  > 
    <el-col :span="24" >
                
                   
                      <el-table
                      :data="tableData"
                      style="width: 100%"
                      :row-class-name="tableRowClassName"
                    ><el-table-column type="index" width="60" label="#"  />
                      <el-table-column prop="str_sm" label="SM"  width="80"/>
                      <el-table-column prop="str_pn" label="PN"  />
                      <el-table-column prop="str_part_name" label="名称" />
                      <el-table-column prop="dt_ekd" label="EKD" />
                      <el-table-column prop="dt_mp" label="MP" />
                      <el-table-column prop="int_day" label="延期/提前" />
                    </el-table>
                        
              
                 
                  </el-col>
  </el-row>
    <!--<template #footer>Footer content</template>-->
  </el-card>


  `,

}
