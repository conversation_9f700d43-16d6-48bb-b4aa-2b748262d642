const { ref, useTemplateRef, onMounted } = Vue
import SearchForm from './components/SearchForm.js'
import GanttData from './components/GanttData.js'
import { getGrindingGanttChart } from './api/index.js'

export default {
  name: 'GrindingTeamPlan',
  components: {
    SearchForm,
    GanttData,
  },
  setup() {
    const searchForm = ref({
      dt_range: [moment().subtract(7, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
    })
    const handleSearch = (form) => {
      searchForm.value = form
      getGanttData()
    }
    const ganttDataRef = useTemplateRef('ganttDataRef')
    const ganttData = ref([])
    const getGanttData = () => {
      getGrindingGanttChart(searchForm.value).then((res) => {
        if (res) {
          const { link, nodes } = res
          const { nodes: transformNodes } = transformGanttData(nodes)
          ganttDataRef.value.ganttInit(transformNodes)
        } else {
          // 使用mock数据
          const { link, nodes } = getMockGanttData()
          // 数据格式转换
          const { nodes: transformNodes } = transformGanttData(nodes)
          // ganttData.value = transformNodes
          ganttDataRef.value.ganttInit(transformNodes)
        }
      })
    }
    const transformGanttData = (data) => {
      const nodes = data.map((item) => {
        return {
          id: item.id,
          text: item.str_task,
          start_date: moment(item.dt_begin).format('YYYY-MM-DD'),
          dt_end: moment(item.dt_end).format('YYYY-MM-DD'),
          duration: item.duration,
          int_level: item.int_level,
          parent: item.id_root,
        }
      })
      return {
        nodes,
      }
    }

    const getMockGanttData = () => {
      return {
        link: [],
        nodes: [
          {
            id: '1',
            id_wo: '1',
            str_wo: '1',
            str_node: '1',
            str_sm: '1',
            id_task: '0',
            str_task: 'Task1',
            int_level: 0,
            id_root: '0',
            dt_begin: '2025-07-01',
            dt_end: '2025-07-01',
            duration: 1,
          },
          {
            id: '2',
            id_wo: '1',
            str_wo: '1',
            str_node: '1',
            str_sm: '1',
            id_task: '0',
            str_task: 'Task2',
            int_level: 0,
            id_root: '0',
            dt_begin: '2025-07-02',
            dt_end: '2025-07-02',
            duration: 1,
          },
        ],
      }
    }

    onMounted(() => {
      getGanttData()
    })
    return {
      searchForm,
      ganttData,
      ganttDataRef,
      handleSearch,
    }
  },
  template: /*html*/ `
    <div class="h-full w-full p-4">
      <!-- 搜索表单 -->
      <div class="h-10 shadow-md rounded-md px-4 mb-2">
        <search-form v-model:form="searchForm" @search="handleSearch" />
      </div>
      <!-- 甘特图 -->
      <div style="height: calc(100vh - 100px); width: 100%">
        <gantt-data ref="ganttDataRef" :gantt-data="ganttData" />
      </div>
    </div>
  `,
}
