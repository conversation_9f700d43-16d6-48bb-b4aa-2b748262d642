import { post } from '../../../utils/request.js'

export const fetchEsnOptions = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_esn_list',
  })
}

export const fetchSmOptions = (idWo) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_model_list',
    id_wo: idWo,
  })
}

export const fetchShiftOptions = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_shift',
  })
}

export const fetchHandover = (param) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_handover_byid',
    Filter: param,
  })
}

export const fetchInitHandover = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_handover_init',
  })
}

export const saveHandover = (param) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_handover',
    postData: param,
  })
}

export const getTaskList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_task_by_sm',
    ...params,
  })
}


export const getStaffList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_section_user_list'
  })
}


export const getEnumList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_enum',
    str_key: params
  })
}


export const getF2TaskTypeList = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_f2_task_type'
  })
}