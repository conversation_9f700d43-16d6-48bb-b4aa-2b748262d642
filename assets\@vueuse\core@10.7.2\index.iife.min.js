var VueDemi=function(h,f,u){if(h.install)return h;if(!f)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),h;if(f.version.slice(0,4)==="2.7."){let q=function(X,J){var K,L={},V={config:f.config,use:f.use.bind(f),mixin:f.mixin.bind(f),component:f.component.bind(f),provide:function(B,$){return L[B]=$,this},directive:function(B,$){return $?(f.directive(B,$),V):f.directive(B)},mount:function(B,$){return K||(K=new f(Object.assign({propsData:J},X,{provide:Object.assign(L,X.provide)})),K.$mount(B,$),K)},unmount:function(){K&&(K.$destroy(),K=void 0)}};return V};var Ae=q;for(var Y in f)h[Y]=f[Y];h.isVue2=!0,h.isVue3=!1,h.install=function(){},h.Vue=f,h.Vue2=f,h.version=f.version,h.warn=f.util.warn,h.hasInjectionContext=()=>!!h.getCurrentInstance(),h.createApp=q}else if(f.version.slice(0,2)==="2.")if(u){for(var Y in u)h[Y]=u[Y];h.isVue2=!0,h.isVue3=!1,h.install=function(){},h.Vue=f,h.Vue2=f,h.version=f.version,h.hasInjectionContext=()=>!!h.getCurrentInstance()}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(f.version.slice(0,2)==="3."){for(var Y in f)h[Y]=f[Y];h.isVue2=!1,h.isVue3=!0,h.install=function(){},h.Vue=f,h.Vue2=void 0,h.version=f.version,h.set=function(q,X,J){return Array.isArray(q)?(q.length=Math.max(q.length,X),q.splice(X,1,J),J):(q[X]=J,J)},h.del=function(q,X){if(Array.isArray(q)){q.splice(X,1);return}delete q[X]}}else console.error("[vue-demi] Vue version "+f.version+" is unsupported.");return h}(this.VueDemi=this.VueDemi||(typeof VueDemi<"u"?VueDemi:{}),this.Vue||(typeof Vue<"u"?Vue:void 0),this.VueCompositionAPI||(typeof VueCompositionAPI<"u"?VueCompositionAPI:void 0));(function(h,f,u){"use strict";function Y(e,t,n){let o;u.isRef(n)?o={evaluating:n}:o=n||{};const{lazy:l=!1,evaluating:r=void 0,shallow:s=!0,onError:a=f.noop}=o,i=u.ref(!l),c=s?u.shallowRef(t):u.ref(t);let d=0;return u.watchEffect(async v=>{if(!i.value)return;d++;const g=d;let p=!1;r&&Promise.resolve().then(()=>{r.value=!0});try{const y=await e(m=>{v(()=>{r&&(r.value=!1),p||m()})});g===d&&(c.value=y)}catch(y){a(y)}finally{r&&g===d&&(r.value=!1),p=!0}}),l?u.computed(()=>(i.value=!0,c.value)):c}function Ae(e,t,n,o){let l=u.inject(e);return n&&(l=u.inject(e,n)),o&&(l=u.inject(e,n,o)),typeof t=="function"?u.computed(r=>t(l,r)):u.computed({get:r=>t.get(l,r),set:t.set})}function q(e={}){if(!u.isVue3&&!u.version.startsWith("2.7.")){if(process.env.NODE_ENV!=="production")throw new Error("[VueUse] createReusableTemplate only works in Vue 2.7 or above.");return}const{inheritAttrs:t=!0}=e,n=u.shallowRef(),o=u.defineComponent({setup(r,{slots:s}){return()=>{n.value=s.default}}}),l=u.defineComponent({inheritAttrs:t,setup(r,{attrs:s,slots:a}){return()=>{var i;if(!n.value&&process.env.NODE_ENV!=="production")throw new Error("[VueUse] Failed to find the definition of reusable template");const c=(i=n.value)==null?void 0:i.call(n,{...X(s),$slots:a});return t&&c?.length===1?c[0]:c}}});return f.makeDestructurable({define:o,reuse:l},[o,l])}function X(e){const t={};for(const n in e)t[f.camelize(n)]=e[n];return t}function J(e={}){if(!u.isVue3){if(process.env.NODE_ENV!=="production")throw new Error("[VueUse] createTemplatePromise only works in Vue 3 or above.");return}let t=0;const n=u.ref([]);function o(...s){const a=u.shallowReactive({key:t++,args:s,promise:void 0,resolve:()=>{},reject:()=>{},isResolving:!1,options:e});return n.value.push(a),a.promise=new Promise((i,c)=>{a.resolve=d=>(a.isResolving=!0,i(d)),a.reject=c}).finally(()=>{a.promise=void 0;const i=n.value.indexOf(a);i!==-1&&n.value.splice(i,1)}),a.promise}function l(...s){return e.singleton&&n.value.length>0?n.value[0].promise:o(...s)}const r=u.defineComponent((s,{slots:a})=>{const i=()=>n.value.map(c=>{var d;return u.h(u.Fragment,{key:c.key},(d=a.default)==null?void 0:d.call(a,c))});return e.transition?()=>u.h(u.TransitionGroup,e.transition,i):i});return r.start=l,r}function K(e){return function(...t){return e.apply(this,t.map(n=>f.toValue(n)))}}function L(e){var t;const n=f.toValue(e);return(t=n?.$el)!=null?t:n}const V=f.isClient?window:void 0,B=f.isClient?window.document:void 0,$=f.isClient?window.navigator:void 0,Tt=f.isClient?window.location:void 0;function _(...e){let t,n,o,l;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,o,l]=e,t=V):[t,n,o,l]=e,!t)return f.noop;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const r=[],s=()=>{r.forEach(d=>d()),r.length=0},a=(d,v,g,p)=>(d.addEventListener(v,g,p),()=>d.removeEventListener(v,g,p)),i=u.watch(()=>[L(t),f.toValue(l)],([d,v])=>{if(s(),!d)return;const g=f.isObject(v)?{...v}:v;r.push(...n.flatMap(p=>o.map(y=>a(d,p,y,g))))},{immediate:!0,flush:"post"}),c=()=>{i(),s()};return f.tryOnScopeDispose(c),c}let Ve=!1;function Ot(e,t,n={}){const{window:o=V,ignore:l=[],capture:r=!0,detectIframe:s=!1}=n;if(!o)return f.noop;f.isIOS&&!Ve&&(Ve=!0,Array.from(o.document.body.children).forEach(g=>g.addEventListener("click",f.noop)),o.document.documentElement.addEventListener("click",f.noop));let a=!0;const i=g=>l.some(p=>{if(typeof p=="string")return Array.from(o.document.querySelectorAll(p)).some(y=>y===g.target||g.composedPath().includes(y));{const y=L(p);return y&&(g.target===y||g.composedPath().includes(y))}}),d=[_(o,"click",g=>{const p=L(e);if(!(!p||p===g.target||g.composedPath().includes(p))){if(g.detail===0&&(a=!i(g)),!a){a=!0;return}t(g)}},{passive:!0,capture:r}),_(o,"pointerdown",g=>{const p=L(e);a=!i(g)&&!!(p&&!g.composedPath().includes(p))},{passive:!0}),s&&_(o,"blur",g=>{setTimeout(()=>{var p;const y=L(e);((p=o.document.activeElement)==null?void 0:p.tagName)==="IFRAME"&&!y?.contains(o.document.activeElement)&&t(g)},0)})].filter(Boolean);return()=>d.forEach(g=>g())}function kt(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function ce(...e){let t,n,o={};e.length===3?(t=e[0],n=e[1],o=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],o=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:l=V,eventName:r="keydown",passive:s=!1,dedupe:a=!1}=o,i=kt(t);return _(l,r,d=>{d.repeat&&f.toValue(a)||i(d)&&n(d)},s)}function _t(e,t,n={}){return ce(e,t,{...n,eventName:"keydown"})}function Rt(e,t,n={}){return ce(e,t,{...n,eventName:"keypress"})}function Ft(e,t,n={}){return ce(e,t,{...n,eventName:"keyup"})}const Ct=500,Pt=10;function At(e,t,n){var o,l;const r=u.computed(()=>L(e));let s,a;function i(){s&&(clearTimeout(s),s=void 0),a=void 0}function c(y){var m,b,w,S;(m=n?.modifiers)!=null&&m.self&&y.target!==r.value||(i(),(b=n?.modifiers)!=null&&b.prevent&&y.preventDefault(),(w=n?.modifiers)!=null&&w.stop&&y.stopPropagation(),a={x:y.x,y:y.y},s=setTimeout(()=>t(y),(S=n?.delay)!=null?S:Ct))}function d(y){var m,b,w,S;if((m=n?.modifiers)!=null&&m.self&&y.target!==r.value||!a||n?.distanceThreshold===!1)return;(b=n?.modifiers)!=null&&b.prevent&&y.preventDefault(),(w=n?.modifiers)!=null&&w.stop&&y.stopPropagation();const E=y.x-a.x,F=y.y-a.y;Math.sqrt(E*E+F*F)>=((S=n?.distanceThreshold)!=null?S:Pt)&&i()}const v={capture:(o=n?.modifiers)==null?void 0:o.capture,once:(l=n?.modifiers)==null?void 0:l.once},g=[_(r,"pointerdown",c,v),_(r,"pointermove",d,v),_(r,["pointerup","pointerleave"],i,v)];return()=>g.forEach(y=>y())}function Vt(){const{activeElement:e,body:t}=document;if(!e||e===t)return!1;switch(e.tagName){case"INPUT":case"TEXTAREA":return!0}return e.hasAttribute("contenteditable")}function It({keyCode:e,metaKey:t,ctrlKey:n,altKey:o}){return t||n||o?!1:e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122}function Mt(e,t={}){const{document:n=B}=t;n&&_(n,"keydown",l=>{!Vt()&&It(l)&&e(l)},{passive:!0})}function Lt(e,t=null){const n=u.getCurrentInstance();let o=()=>{};const l=u.customRef((r,s)=>(o=s,{get(){var a,i;return r(),(i=(a=n?.proxy)==null?void 0:a.$refs[e])!=null?i:t},set(){}}));return f.tryOnMounted(o),u.onUpdated(o),l}function Ie(e={}){var t;const{window:n=V,deep:o=!0}=e,l=(t=e.document)!=null?t:n?.document,r=()=>{var a;let i=l?.activeElement;if(o)for(;i?.shadowRoot;)i=(a=i?.shadowRoot)==null?void 0:a.activeElement;return i},s=f.computedWithControl(()=>null,()=>r());return n&&(_(n,"blur",a=>{a.relatedTarget===null&&s.trigger()},!0),_(n,"focus",s.trigger,!0)),s}function Me(){const e=u.ref(!1);return u.getCurrentInstance()&&u.onMounted(()=>{e.value=!0}),e}function x(e){const t=Me();return u.computed(()=>(t.value,!!e()))}function Z(e,t={}){const{immediate:n=!0,fpsLimit:o=void 0,window:l=V}=t,r=u.ref(!1),s=o?1e3/o:null;let a=0,i=null;function c(g){if(!r.value||!l)return;const p=g-(a||g);if(s&&p<s){i=l.requestAnimationFrame(c);return}e({delta:p,timestamp:g}),a=g,i=l.requestAnimationFrame(c)}function d(){!r.value&&l&&(r.value=!0,i=l.requestAnimationFrame(c))}function v(){r.value=!1,i!=null&&l&&(l.cancelAnimationFrame(i),i=null)}return n&&d(),f.tryOnScopeDispose(v),{isActive:u.readonly(r),pause:v,resume:d}}function Nt(e,t,n){let o,l;f.isObject(n)?(o=n,l=f.objectOmit(n,["window","immediate","commitStyles","persist","onReady","onError"])):(o={duration:n},l=n);const{window:r=V,immediate:s=!0,commitStyles:a,persist:i,playbackRate:c=1,onReady:d,onError:v=I=>{console.error(I)}}=o,g=x(()=>r&&HTMLElement&&"animate"in HTMLElement.prototype),p=u.shallowRef(void 0),y=u.shallowReactive({startTime:null,currentTime:null,timeline:null,playbackRate:c,pending:!1,playState:s?"idle":"paused",replaceState:"active"}),m=u.computed(()=>y.pending),b=u.computed(()=>y.playState),w=u.computed(()=>y.replaceState),S=u.computed({get(){return y.startTime},set(I){y.startTime=I,p.value&&(p.value.startTime=I)}}),E=u.computed({get(){return y.currentTime},set(I){y.currentTime=I,p.value&&(p.value.currentTime=I,W())}}),F=u.computed({get(){return y.timeline},set(I){y.timeline=I,p.value&&(p.value.timeline=I)}}),O=u.computed({get(){return y.playbackRate},set(I){y.playbackRate=I,p.value&&(p.value.playbackRate=I)}}),P=()=>{if(p.value)try{p.value.play(),W()}catch(I){U(),v(I)}else C()},k=()=>{var I;try{(I=p.value)==null||I.pause(),U()}catch(H){v(H)}},A=()=>{var I;!p.value&&C();try{(I=p.value)==null||I.reverse(),W()}catch(H){U(),v(H)}},R=()=>{var I;try{(I=p.value)==null||I.finish(),U()}catch(H){v(H)}},T=()=>{var I;try{(I=p.value)==null||I.cancel(),U()}catch(H){v(H)}};u.watch(()=>L(e),I=>{I&&C()}),u.watch(()=>t,I=>{!p.value&&C(),!L(e)&&p.value&&(p.value.effect=new KeyframeEffect(L(e),f.toValue(I),l))},{deep:!0}),f.tryOnMounted(()=>{u.nextTick(()=>C(!0))}),f.tryOnScopeDispose(T);function C(I){const H=L(e);!g.value||!H||(p.value=H.animate(f.toValue(t),l),a&&p.value.commitStyles(),i&&p.value.persist(),c!==1&&(p.value.playbackRate=c),I&&!s?p.value.pause():W(),d?.(p.value))}_(p,["cancel","finish","remove"],U);const{resume:M,pause:N}=Z(()=>{p.value&&(y.pending=p.value.pending,y.playState=p.value.playState,y.replaceState=p.value.replaceState,y.startTime=p.value.startTime,y.currentTime=p.value.currentTime,y.timeline=p.value.timeline,y.playbackRate=p.value.playbackRate)},{immediate:!1});function W(){g.value&&M()}function U(){g.value&&r&&r.requestAnimationFrame(N)}return{isSupported:g,animate:p,play:P,pause:k,reverse:A,finish:R,cancel:T,pending:m,playState:b,replaceState:w,startTime:S,currentTime:E,timeline:F,playbackRate:O}}function xt(e,t){const{interrupt:n=!0,onError:o=f.noop,onFinished:l=f.noop,signal:r}=t||{},s={aborted:"aborted",fulfilled:"fulfilled",pending:"pending",rejected:"rejected"},a=Array.from(Array.from({length:e.length}),()=>({state:s.pending,data:null})),i=u.reactive(a),c=u.ref(-1);if(!e||e.length===0)return l(),{activeIndex:c,result:i};function d(v,g){c.value++,i[c.value].data=g,i[c.value].state=v}return e.reduce((v,g)=>v.then(p=>{var y;if(r?.aborted){d(s.aborted,new Error("aborted"));return}if(((y=i[c.value])==null?void 0:y.state)===s.rejected&&n){l();return}const m=g(p).then(b=>(d(s.fulfilled,b),c.value===e.length-1&&l(),b));return r?Promise.race([m,Wt(r)]):m}).catch(p=>r?.aborted?(d(s.aborted,p),p):(d(s.rejected,p),o(),p)),Promise.resolve()),{activeIndex:c,result:i}}function Wt(e){return new Promise((t,n)=>{const o=new Error("aborted");e.aborted?n(o):e.addEventListener("abort",()=>n(o),{once:!0})})}function Le(e,t,n){const{immediate:o=!0,delay:l=0,onError:r=f.noop,onSuccess:s=f.noop,resetOnExecute:a=!0,shallow:i=!0,throwError:c}=n??{},d=i?u.shallowRef(t):u.ref(t),v=u.ref(!1),g=u.ref(!1),p=u.shallowRef(void 0);async function y(w=0,...S){a&&(d.value=t),p.value=void 0,v.value=!1,g.value=!0,w>0&&await f.promiseTimeout(w);const E=typeof e=="function"?e(...S):e;try{const F=await E;d.value=F,v.value=!0,s(F)}catch(F){if(p.value=F,r(F),c)throw F}finally{g.value=!1}return d.value}o&&y(l);const m={state:d,isReady:v,isLoading:g,error:p,execute:y};function b(){return new Promise((w,S)=>{f.until(g).toBe(!1).then(()=>w(m)).catch(S)})}return{...m,then(w,S){return b().then(w,S)}}}const ne={array:e=>JSON.stringify(e),object:e=>JSON.stringify(e),set:e=>JSON.stringify(Array.from(e)),map:e=>JSON.stringify(Object.fromEntries(e)),null:()=>""};function Ht(e){return e?e instanceof Map?ne.map:e instanceof Set?ne.set:Array.isArray(e)?ne.array:ne.object:ne.null}function Bt(e,t){const n=u.ref(""),o=u.ref();function l(){if(f.isClient)return o.value=new Promise((r,s)=>{try{const a=f.toValue(e);if(a==null)r("");else if(typeof a=="string")r(Ee(new Blob([a],{type:"text/plain"})));else if(a instanceof Blob)r(Ee(a));else if(a instanceof ArrayBuffer)r(window.btoa(String.fromCharCode(...new Uint8Array(a))));else if(a instanceof HTMLCanvasElement)r(a.toDataURL(t?.type,t?.quality));else if(a instanceof HTMLImageElement){const i=a.cloneNode(!1);i.crossOrigin="Anonymous",Ut(i).then(()=>{const c=document.createElement("canvas"),d=c.getContext("2d");c.width=i.width,c.height=i.height,d.drawImage(i,0,0,c.width,c.height),r(c.toDataURL(t?.type,t?.quality))}).catch(s)}else if(typeof a=="object"){const c=(t?.serializer||Ht(a))(a);return r(Ee(new Blob([c],{type:"application/json"})))}else s(new Error("target is unsupported types"))}catch(a){s(a)}}),o.value.then(r=>n.value=r),o.value}return u.isRef(e)||typeof e=="function"?u.watch(e,l,{immediate:!0}):l(),{base64:n,promise:o,execute:l}}function Ut(e){return new Promise((t,n)=>{e.complete?t():(e.onload=()=>{t()},e.onerror=n)})}function Ee(e){return new Promise((t,n)=>{const o=new FileReader;o.onload=l=>{t(l.target.result)},o.onerror=n,o.readAsDataURL(e)})}function $t(e={}){const{navigator:t=$}=e,n=["chargingchange","chargingtimechange","dischargingtimechange","levelchange"],o=x(()=>t&&"getBattery"in t&&typeof t.getBattery=="function"),l=u.ref(!1),r=u.ref(0),s=u.ref(0),a=u.ref(1);let i;function c(){l.value=this.charging,r.value=this.chargingTime||0,s.value=this.dischargingTime||0,a.value=this.level}return o.value&&t.getBattery().then(d=>{i=d,c.call(i),_(i,n,c,{passive:!0})}),{isSupported:o,charging:l,chargingTime:r,dischargingTime:s,level:a}}function jt(e){let{acceptAllDevices:t=!1}=e||{};const{filters:n=void 0,optionalServices:o=void 0,navigator:l=$}=e||{},r=x(()=>l&&"bluetooth"in l),s=u.shallowRef(void 0),a=u.shallowRef(null);u.watch(s,()=>{v()});async function i(){if(r.value){a.value=null,n&&n.length>0&&(t=!1);try{s.value=await l?.bluetooth.requestDevice({acceptAllDevices:t,filters:n,optionalServices:o})}catch(g){a.value=g}}}const c=u.ref(),d=u.computed(()=>{var g;return((g=c.value)==null?void 0:g.connected)||!1});async function v(){if(a.value=null,s.value&&s.value.gatt){s.value.addEventListener("gattserverdisconnected",()=>{});try{c.value=await s.value.gatt.connect()}catch(g){a.value=g}}}return f.tryOnMounted(()=>{var g;s.value&&((g=s.value.gatt)==null||g.connect())}),f.tryOnScopeDispose(()=>{var g;s.value&&((g=s.value.gatt)==null||g.disconnect())}),{isSupported:r,isConnected:d,device:s,requestDevice:i,server:c,error:a}}function G(e,t={}){const{window:n=V}=t,o=x(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let l;const r=u.ref(!1),s=c=>{r.value=c.matches},a=()=>{l&&("removeEventListener"in l?l.removeEventListener("change",s):l.removeListener(s))},i=u.watchEffect(()=>{o.value&&(a(),l=n.matchMedia(f.toValue(e)),"addEventListener"in l?l.addEventListener("change",s):l.addListener(s),r.value=l.matches)});return f.tryOnScopeDispose(()=>{i(),a(),l=void 0}),r}const zt={sm:640,md:768,lg:1024,xl:1280,"2xl":1536},qt={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400},Gt={xs:600,sm:960,md:1264,lg:1904},Yt={xs:480,sm:576,md:768,lg:992,xl:1200,xxl:1600},Xt={xs:600,sm:1024,md:1440,lg:1920},Kt={mobileS:320,mobileM:375,mobileL:425,tablet:768,laptop:1024,laptopL:1440,desktop4K:2560},Jt={"3xs":360,"2xs":480,xs:600,sm:768,md:1024,lg:1280,xl:1440,"2xl":1600,"3xl":1920,"4xl":2560},Qt={sm:576,md:768,lg:992,xl:1200};function Zt(e,t={}){function n(a,i){let c=f.toValue(e[a]);return i!=null&&(c=f.increaseWithUnit(c,i)),typeof c=="number"&&(c=`${c}px`),c}const{window:o=V}=t;function l(a){return o?o.matchMedia(a).matches:!1}const r=a=>G(()=>`(min-width: ${n(a)})`,t),s=Object.keys(e).reduce((a,i)=>(Object.defineProperty(a,i,{get:()=>r(i),enumerable:!0,configurable:!0}),a),{});return Object.assign(s,{greater(a){return G(()=>`(min-width: ${n(a,.1)})`,t)},greaterOrEqual:r,smaller(a){return G(()=>`(max-width: ${n(a,-.1)})`,t)},smallerOrEqual(a){return G(()=>`(max-width: ${n(a)})`,t)},between(a,i){return G(()=>`(min-width: ${n(a)}) and (max-width: ${n(i,-.1)})`,t)},isGreater(a){return l(`(min-width: ${n(a,.1)})`)},isGreaterOrEqual(a){return l(`(min-width: ${n(a)})`)},isSmaller(a){return l(`(max-width: ${n(a,-.1)})`)},isSmallerOrEqual(a){return l(`(max-width: ${n(a)})`)},isInBetween(a,i){return l(`(min-width: ${n(a)}) and (max-width: ${n(i,-.1)})`)},current(){const a=Object.keys(e).map(i=>[i,r(i)]);return u.computed(()=>a.filter(([,i])=>i.value).map(([i])=>i))}})}function Dt(e){const{name:t,window:n=V}=e,o=x(()=>n&&"BroadcastChannel"in n),l=u.ref(!1),r=u.ref(),s=u.ref(),a=u.shallowRef(null),i=d=>{r.value&&r.value.postMessage(d)},c=()=>{r.value&&r.value.close(),l.value=!0};return o.value&&f.tryOnMounted(()=>{a.value=null,r.value=new BroadcastChannel(t),r.value.addEventListener("message",d=>{s.value=d.data},{passive:!0}),r.value.addEventListener("messageerror",d=>{a.value=d},{passive:!0}),r.value.addEventListener("close",()=>{l.value=!0})}),f.tryOnScopeDispose(()=>{c()}),{isSupported:o,channel:r,data:s,post:i,close:c,error:a,isClosed:l}}const Ne=["hash","host","hostname","href","pathname","port","protocol","search"];function en(e={}){const{window:t=V}=e,n=Object.fromEntries(Ne.map(r=>[r,u.ref()]));for(const[r,s]of f.objectEntries(n))u.watch(s,a=>{!t?.location||t.location[r]===a||(t.location[r]=a)});const o=r=>{var s;const{state:a,length:i}=t?.history||{},{origin:c}=t?.location||{};for(const d of Ne)n[d].value=(s=t?.location)==null?void 0:s[d];return u.reactive({trigger:r,state:a,length:i,origin:c,...n})},l=u.ref(o("load"));return t&&(_(t,"popstate",()=>l.value=o("popstate"),{passive:!0}),_(t,"hashchange",()=>l.value=o("hashchange"),{passive:!0})),l}function tn(e,t=(o,l)=>o===l,n){const o=u.ref(e.value);return u.watch(()=>e.value,l=>{t(l,o.value)||(o.value=l)},n),o}function fe(e,t={}){const{controls:n=!1,navigator:o=$}=t,l=x(()=>o&&"permissions"in o);let r;const s=typeof e=="string"?{name:e}:e,a=u.ref(),i=()=>{r&&(a.value=r.state)},c=f.createSingletonPromise(async()=>{if(l.value){if(!r)try{r=await o.permissions.query(s),_(r,"change",i),i()}catch{a.value="prompt"}return r}});return c(),n?{state:a,isSupported:l,query:c}:a}function nn(e={}){const{navigator:t=$,read:n=!1,source:o,copiedDuring:l=1500,legacy:r=!1}=e,s=x(()=>t&&"clipboard"in t),a=fe("clipboard-read"),i=fe("clipboard-write"),c=u.computed(()=>s.value||r),d=u.ref(""),v=u.ref(!1),g=f.useTimeoutFn(()=>v.value=!1,l);function p(){s.value&&a.value!=="denied"?t.clipboard.readText().then(w=>{d.value=w}):d.value=b()}c.value&&n&&_(["copy","cut"],p);async function y(w=f.toValue(o)){c.value&&w!=null&&(s.value&&i.value!=="denied"?await t.clipboard.writeText(w):m(w),d.value=w,v.value=!0,g.start())}function m(w){const S=document.createElement("textarea");S.value=w??"",S.style.position="absolute",S.style.opacity="0",document.body.appendChild(S),S.select(),document.execCommand("copy"),S.remove()}function b(){var w,S,E;return(E=(S=(w=document?.getSelection)==null?void 0:w.call(document))==null?void 0:S.toString())!=null?E:""}return{isSupported:c,text:d,copied:v,copy:y}}function on(e={}){const{navigator:t=$,read:n=!1,source:o,copiedDuring:l=1500}=e,r=x(()=>t&&"clipboard"in t),s=u.ref([]),a=u.ref(!1),i=f.useTimeoutFn(()=>a.value=!1,l);function c(){r.value&&t.clipboard.read().then(v=>{s.value=v})}r.value&&n&&_(["copy","cut"],c);async function d(v=f.toValue(o)){r.value&&v!=null&&(await t.clipboard.write(v),s.value=v,a.value=!0,i.start())}return{isSupported:r,content:s,copied:a,copy:d}}function oe(e){return JSON.parse(JSON.stringify(e))}function rn(e,t={}){const n=u.ref({}),{manual:o,clone:l=oe,deep:r=!0,immediate:s=!0}=t;function a(){n.value=l(f.toValue(e))}return!o&&(u.isRef(e)||typeof e=="function")?u.watch(e,a,{...t,deep:r,immediate:s}):a(),{cloned:n,sync:a}}const de=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ve="__vueuse_ssr_handlers__",xe=ln();function ln(){return ve in de||(de[ve]=de[ve]||{}),de[ve]}function pe(e,t){return xe[e]||t}function an(e,t){xe[e]=t}function We(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Te={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Oe="vueuse-storage";function ye(e,t,n,o={}){var l;const{flush:r="pre",deep:s=!0,listenToStorageChanges:a=!0,writeDefaults:i=!0,mergeDefaults:c=!1,shallow:d,window:v=V,eventFilter:g,onError:p=R=>{console.error(R)},initOnMounted:y}=o,m=(d?u.shallowRef:u.ref)(typeof t=="function"?t():t);if(!n)try{n=pe("getDefaultStorage",()=>{var R;return(R=V)==null?void 0:R.localStorage})()}catch(R){p(R)}if(!n)return m;const b=f.toValue(t),w=We(b),S=(l=o.serializer)!=null?l:Te[w],{pause:E,resume:F}=f.pausableWatch(m,()=>O(m.value),{flush:r,deep:s,eventFilter:g});return v&&a&&f.tryOnMounted(()=>{_(v,"storage",A),_(v,Oe,k),y&&A()}),y||A(),m;function O(R){try{if(R==null)n.removeItem(e);else{const T=S.write(R),C=n.getItem(e);C!==T&&(n.setItem(e,T),v&&v.dispatchEvent(new CustomEvent(Oe,{detail:{key:e,oldValue:C,newValue:T,storageArea:n}})))}}catch(T){p(T)}}function P(R){const T=R?R.newValue:n.getItem(e);if(T==null)return i&&b!=null&&n.setItem(e,S.write(b)),b;if(!R&&c){const C=S.read(T);return typeof c=="function"?c(C,b):w==="object"&&!Array.isArray(C)?{...b,...C}:C}else return typeof T!="string"?T:S.read(T)}function k(R){A(R.detail)}function A(R){if(!(R&&R.storageArea!==n)){if(R&&R.key==null){m.value=b;return}if(!(R&&R.key!==e)){E();try{R?.newValue!==S.write(m.value)&&(m.value=P(R))}catch(T){p(T)}finally{R?u.nextTick(F):F()}}}}}function ke(e){return G("(prefers-color-scheme: dark)",e)}function He(e={}){const{selector:t="html",attribute:n="class",initialValue:o="auto",window:l=V,storage:r,storageKey:s="vueuse-color-scheme",listenToStorageChanges:a=!0,storageRef:i,emitAuto:c,disableTransition:d=!0}=e,v={auto:"",light:"light",dark:"dark",...e.modes||{}},g=ke({window:l}),p=u.computed(()=>g.value?"dark":"light"),y=i||(s==null?f.toRef(o):ye(s,o,r,{window:l,listenToStorageChanges:a})),m=u.computed(()=>y.value==="auto"?p.value:y.value),b=pe("updateHTMLAttrs",(F,O,P)=>{const k=typeof F=="string"?l?.document.querySelector(F):L(F);if(!k)return;let A;if(d&&(A=l.document.createElement("style"),A.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),l.document.head.appendChild(A)),O==="class"){const R=P.split(/\s/g);Object.values(v).flatMap(T=>(T||"").split(/\s/g)).filter(Boolean).forEach(T=>{R.includes(T)?k.classList.add(T):k.classList.remove(T)})}else k.setAttribute(O,P);d&&(l.getComputedStyle(A).opacity,document.head.removeChild(A))});function w(F){var O;b(t,n,(O=v[F])!=null?O:F)}function S(F){e.onChanged?e.onChanged(F,w):w(F)}u.watch(m,S,{flush:"post",immediate:!0}),f.tryOnMounted(()=>S(m.value));const E=u.computed({get(){return c?y.value:m.value},set(F){y.value=F}});try{return Object.assign(E,{store:y,system:p,state:m})}catch{return E}}function un(e=u.ref(!1)){const t=f.createEventHook(),n=f.createEventHook(),o=f.createEventHook();let l=f.noop;const r=i=>(o.trigger(i),e.value=!0,new Promise(c=>{l=c})),s=i=>{e.value=!1,t.trigger(i),l({data:i,isCanceled:!1})},a=i=>{e.value=!1,n.trigger(i),l({data:i,isCanceled:!0})};return{isRevealed:u.computed(()=>e.value),reveal:r,confirm:s,cancel:a,onReveal:o.on,onConfirm:t.on,onCancel:n.on}}function re(e,t,n={}){const{window:o=V,...l}=n;let r;const s=x(()=>o&&"MutationObserver"in o),a=()=>{r&&(r.disconnect(),r=void 0)},i=u.watch(()=>L(e),v=>{a(),s.value&&o&&v&&(r=new MutationObserver(t),r.observe(v,l))},{immediate:!0}),c=()=>r?.takeRecords(),d=()=>{a(),i()};return f.tryOnScopeDispose(d),{isSupported:s,stop:d,takeRecords:c}}function le(e,t,n={}){const{window:o=V,initialValue:l="",observe:r=!1}=n,s=u.ref(l),a=u.computed(()=>{var c;return L(t)||((c=o?.document)==null?void 0:c.documentElement)});function i(){var c;const d=f.toValue(e),v=f.toValue(a);if(v&&o){const g=(c=o.getComputedStyle(v).getPropertyValue(d))==null?void 0:c.trim();s.value=g||l}}return r&&re(a,i,{attributeFilter:["style","class"],window:o}),u.watch([a,()=>f.toValue(e)],i,{immediate:!0}),u.watch(s,c=>{var d;(d=a.value)!=null&&d.style&&a.value.style.setProperty(f.toValue(e),c)}),s}function Be(){const e=u.getCurrentInstance(),t=f.computedWithControl(()=>null,()=>e.proxy.$el);return u.onUpdated(t.trigger),u.onMounted(t.trigger),t}function sn(e,t){const n=u.shallowRef(c()),o=f.toRef(e),l=u.computed({get(){var d;const v=o.value;let g=t?.getIndexOf?t.getIndexOf(n.value,v):v.indexOf(n.value);return g<0&&(g=(d=t?.fallbackIndex)!=null?d:0),g},set(d){r(d)}});function r(d){const v=o.value,g=v.length,p=(d%g+g)%g,y=v[p];return n.value=y,y}function s(d=1){return r(l.value+d)}function a(d=1){return s(d)}function i(d=1){return s(-d)}function c(){var d,v;return(v=f.toValue((d=t?.initialValue)!=null?d:f.toValue(e)[0]))!=null?v:void 0}return u.watch(o,()=>r(l.value)),{state:n,index:l,next:a,prev:i}}function cn(e={}){const{valueDark:t="dark",valueLight:n="",window:o=V}=e,l=He({...e,onChanged:(a,i)=>{var c;e.onChanged?(c=e.onChanged)==null||c.call(e,a==="dark",i,a):i(a)},modes:{dark:t,light:n}}),r=u.computed(()=>l.system?l.system.value:ke({window:o}).value?"dark":"light");return u.computed({get(){return l.value==="dark"},set(a){const i=a?"dark":"light";r.value===i?l.value="auto":l.value=i}})}function Ue(e){return e}function fn(e,t){return e.value=t}function dn(e){return e?typeof e=="function"?e:oe:Ue}function vn(e){return e?typeof e=="function"?e:oe:Ue}function $e(e,t={}){const{clone:n=!1,dump:o=dn(n),parse:l=vn(n),setSource:r=fn}=t;function s(){return u.markRaw({snapshot:o(e.value),timestamp:f.timestamp()})}const a=u.ref(s()),i=u.ref([]),c=u.ref([]),d=E=>{r(e,l(E.snapshot)),a.value=E},v=()=>{i.value.unshift(a.value),a.value=s(),t.capacity&&i.value.length>t.capacity&&i.value.splice(t.capacity,Number.POSITIVE_INFINITY),c.value.length&&c.value.splice(0,c.value.length)},g=()=>{i.value.splice(0,i.value.length),c.value.splice(0,c.value.length)},p=()=>{const E=i.value.shift();E&&(c.value.unshift(a.value),d(E))},y=()=>{const E=c.value.shift();E&&(i.value.unshift(a.value),d(E))},m=()=>{d(a.value)},b=u.computed(()=>[a.value,...i.value]),w=u.computed(()=>i.value.length>0),S=u.computed(()=>c.value.length>0);return{source:e,undoStack:i,redoStack:c,last:a,history:b,canUndo:w,canRedo:S,clear:g,commit:v,reset:m,undo:p,redo:y}}function _e(e,t={}){const{deep:n=!1,flush:o="pre",eventFilter:l}=t,{eventFilter:r,pause:s,resume:a,isActive:i}=f.pausableFilter(l),{ignoreUpdates:c,ignorePrevAsyncUpdates:d,stop:v}=f.watchIgnorable(e,b,{deep:n,flush:o,eventFilter:r});function g(F,O){d(),c(()=>{F.value=O})}const p=$e(e,{...t,clone:t.clone||n,setSource:g}),{clear:y,commit:m}=p;function b(){d(),m()}function w(F){a(),F&&b()}function S(F){let O=!1;const P=()=>O=!0;c(()=>{F(P)}),O||b()}function E(){v(),y()}return{...p,isTracking:i,pause:s,resume:w,commit:b,batch:S,dispose:E}}function pn(e,t={}){const n=t.debounce?f.debounceFilter(t.debounce):void 0;return{..._e(e,{...t,eventFilter:n})}}function yn(e={}){const{window:t=V,eventFilter:n=f.bypassFilter}=e,o=u.ref({x:null,y:null,z:null}),l=u.ref({alpha:null,beta:null,gamma:null}),r=u.ref(0),s=u.ref({x:null,y:null,z:null});if(t){const a=f.createFilterWrapper(n,i=>{o.value=i.acceleration,s.value=i.accelerationIncludingGravity,l.value=i.rotationRate,r.value=i.interval});_(t,"devicemotion",a)}return{acceleration:o,accelerationIncludingGravity:s,rotationRate:l,interval:r}}function je(e={}){const{window:t=V}=e,n=x(()=>t&&"DeviceOrientationEvent"in t),o=u.ref(!1),l=u.ref(null),r=u.ref(null),s=u.ref(null);return t&&n.value&&_(t,"deviceorientation",a=>{o.value=a.absolute,l.value=a.alpha,r.value=a.beta,s.value=a.gamma}),{isSupported:n,isAbsolute:o,alpha:l,beta:r,gamma:s}}function gn(e={}){const{window:t=V}=e,n=u.ref(1);if(t){let o=function(){n.value=t.devicePixelRatio,l(),r=t.matchMedia(`(resolution: ${n.value}dppx)`),r.addEventListener("change",o,{once:!0})},l=function(){r?.removeEventListener("change",o)},r;o(),f.tryOnScopeDispose(l)}return{pixelRatio:n}}function mn(e={}){const{navigator:t=$,requestPermissions:n=!1,constraints:o={audio:!0,video:!0},onUpdated:l}=e,r=u.ref([]),s=u.computed(()=>r.value.filter(y=>y.kind==="videoinput")),a=u.computed(()=>r.value.filter(y=>y.kind==="audioinput")),i=u.computed(()=>r.value.filter(y=>y.kind==="audiooutput")),c=x(()=>t&&t.mediaDevices&&t.mediaDevices.enumerateDevices),d=u.ref(!1);let v;async function g(){c.value&&(r.value=await t.mediaDevices.enumerateDevices(),l?.(r.value),v&&(v.getTracks().forEach(y=>y.stop()),v=null))}async function p(){if(!c.value)return!1;if(d.value)return!0;const{state:y,query:m}=fe("camera",{controls:!0});return await m(),y.value!=="granted"&&(v=await t.mediaDevices.getUserMedia(o),g()),d.value=!0,d.value}return c.value&&(n&&p(),_(t.mediaDevices,"devicechange",g),g()),{devices:r,ensurePermissions:p,permissionGranted:d,videoInputs:s,audioInputs:a,audioOutputs:i,isSupported:c}}function hn(e={}){var t;const n=u.ref((t=e.enabled)!=null?t:!1),o=e.video,l=e.audio,{navigator:r=$}=e,s=x(()=>{var p;return(p=r?.mediaDevices)==null?void 0:p.getDisplayMedia}),a={audio:l,video:o},i=u.shallowRef();async function c(){if(!(!s.value||i.value))return i.value=await r.mediaDevices.getDisplayMedia(a),i.value}async function d(){var p;(p=i.value)==null||p.getTracks().forEach(y=>y.stop()),i.value=void 0}function v(){d(),n.value=!1}async function g(){return await c(),i.value&&(n.value=!0),i.value}return u.watch(n,p=>{p?c():d()},{immediate:!0}),{isSupported:s,stream:i,start:g,stop:v,enabled:n}}function wn(e={}){const{document:t=B}=e;if(!t)return u.ref("visible");const n=u.ref(t.visibilityState);return _(t,"visibilitychange",()=>{n.value=t.visibilityState}),n}function bn(e,t={}){var n,o;const{pointerTypes:l,preventDefault:r,stopPropagation:s,exact:a,onMove:i,onEnd:c,onStart:d,initialValue:v,axis:g="both",draggingElement:p=V,containerElement:y,handle:m=e}=t,b=u.ref((n=f.toValue(v))!=null?n:{x:0,y:0}),w=u.ref(),S=k=>l?l.includes(k.pointerType):!0,E=k=>{f.toValue(r)&&k.preventDefault(),f.toValue(s)&&k.stopPropagation()},F=k=>{var A;if(!S(k)||f.toValue(a)&&k.target!==f.toValue(e))return;const R=f.toValue(y),T=(A=R?.getBoundingClientRect)==null?void 0:A.call(R),C=f.toValue(e).getBoundingClientRect(),M={x:k.clientX-(R?C.left-T.left+R.scrollLeft:C.left),y:k.clientY-(R?C.top-T.top+R.scrollTop:C.top)};d?.(M,k)!==!1&&(w.value=M,E(k))},O=k=>{var A;if(!S(k)||!w.value)return;const R=f.toValue(y),T=(A=R?.getBoundingClientRect)==null?void 0:A.call(R),C=f.toValue(e).getBoundingClientRect();let{x:M,y:N}=b.value;(g==="x"||g==="both")&&(M=k.clientX-w.value.x,R&&(M=Math.min(Math.max(0,M),T.width+R.scrollLeft-C.width))),(g==="y"||g==="both")&&(N=k.clientY-w.value.y,R&&(N=Math.min(Math.max(0,N),T.height+R.scrollTop-C.height))),b.value={x:M,y:N},i?.(b.value,k),E(k)},P=k=>{S(k)&&w.value&&(w.value=void 0,c?.(b.value,k),E(k))};if(f.isClient){const k={capture:(o=t.capture)!=null?o:!0};_(m,"pointerdown",F,k),_(p,"pointermove",O,k),_(p,"pointerup",P,k)}return{...f.toRefs(b),position:b,isDragging:u.computed(()=>!!w.value),style:u.computed(()=>`left:${b.value.x}px;top:${b.value.y}px;`)}}function Sn(e,t={}){const n=u.ref(!1),o=u.shallowRef(null);let l=0,r=!0;if(f.isClient){const s=typeof t=="function"?{onDrop:t}:t,a=i=>{var c,d;const v=Array.from((d=(c=i.dataTransfer)==null?void 0:c.files)!=null?d:[]);return o.value=v.length===0?null:v};_(e,"dragenter",i=>{var c,d;const v=Array.from(((c=i?.dataTransfer)==null?void 0:c.items)||[]).map(g=>g.kind==="file"?g.type:null).filter(f.notNullish);if(s.dataTypes&&i.dataTransfer){const g=u.unref(s.dataTypes);if(r=typeof g=="function"?g(v):g?g.some(p=>v.includes(p)):!0,!r)return}i.preventDefault(),l+=1,n.value=!0,(d=s.onEnter)==null||d.call(s,a(i),i)}),_(e,"dragover",i=>{var c;r&&(i.preventDefault(),(c=s.onOver)==null||c.call(s,a(i),i))}),_(e,"dragleave",i=>{var c;r&&(i.preventDefault(),l-=1,l===0&&(n.value=!1),(c=s.onLeave)==null||c.call(s,a(i),i))}),_(e,"drop",i=>{var c;i.preventDefault(),l=0,n.value=!1,(c=s.onDrop)==null||c.call(s,a(i),i)})}return{files:o,isOverDropZone:n}}function ge(e,t,n={}){const{window:o=V,...l}=n;let r;const s=x(()=>o&&"ResizeObserver"in o),a=()=>{r&&(r.disconnect(),r=void 0)},i=u.computed(()=>Array.isArray(e)?e.map(v=>L(v)):[L(e)]),c=u.watch(i,v=>{if(a(),s.value&&o){r=new ResizeObserver(t);for(const g of v)g&&r.observe(g,l)}},{immediate:!0,flush:"post",deep:!0}),d=()=>{a(),c()};return f.tryOnScopeDispose(d),{isSupported:s,stop:d}}function En(e,t={}){const{reset:n=!0,windowResize:o=!0,windowScroll:l=!0,immediate:r=!0}=t,s=u.ref(0),a=u.ref(0),i=u.ref(0),c=u.ref(0),d=u.ref(0),v=u.ref(0),g=u.ref(0),p=u.ref(0);function y(){const m=L(e);if(!m){n&&(s.value=0,a.value=0,i.value=0,c.value=0,d.value=0,v.value=0,g.value=0,p.value=0);return}const b=m.getBoundingClientRect();s.value=b.height,a.value=b.bottom,i.value=b.left,c.value=b.right,d.value=b.top,v.value=b.width,g.value=b.x,p.value=b.y}return ge(e,y),u.watch(()=>L(e),m=>!m&&y()),re(e,y,{attributeFilter:["style","class"]}),l&&_("scroll",y,{capture:!0,passive:!0}),o&&_("resize",y,{passive:!0}),f.tryOnMounted(()=>{r&&y()}),{height:s,bottom:a,left:i,right:c,top:d,width:v,x:g,y:p,update:y}}function Tn(e){const{x:t,y:n,document:o=B,multiple:l,interval:r="requestAnimationFrame",immediate:s=!0}=e,a=x(()=>f.toValue(l)?o&&"elementsFromPoint"in o:o&&"elementFromPoint"in o),i=u.ref(null),c=()=>{var v,g;i.value=f.toValue(l)?(v=o?.elementsFromPoint(f.toValue(t),f.toValue(n)))!=null?v:[]:(g=o?.elementFromPoint(f.toValue(t),f.toValue(n)))!=null?g:null},d=r==="requestAnimationFrame"?Z(c,{immediate:s}):f.useIntervalFn(c,r,{immediate:s});return{isSupported:a,element:i,...d}}function On(e,t={}){const{delayEnter:n=0,delayLeave:o=0,window:l=V}=t,r=u.ref(!1);let s;const a=i=>{const c=i?n:o;s&&(clearTimeout(s),s=void 0),c?s=setTimeout(()=>r.value=i,c):r.value=i};return l&&(_(e,"mouseenter",()=>a(!0),{passive:!0}),_(e,"mouseleave",()=>a(!1),{passive:!0})),r}function ze(e,t={width:0,height:0},n={}){const{window:o=V,box:l="content-box"}=n,r=u.computed(()=>{var v,g;return(g=(v=L(e))==null?void 0:v.namespaceURI)==null?void 0:g.includes("svg")}),s=u.ref(t.width),a=u.ref(t.height),{stop:i}=ge(e,([v])=>{const g=l==="border-box"?v.borderBoxSize:l==="content-box"?v.contentBoxSize:v.devicePixelContentBoxSize;if(o&&r.value){const p=L(e);if(p){const y=o.getComputedStyle(p);s.value=Number.parseFloat(y.width),a.value=Number.parseFloat(y.height)}}else if(g){const p=Array.isArray(g)?g:[g];s.value=p.reduce((y,{inlineSize:m})=>y+m,0),a.value=p.reduce((y,{blockSize:m})=>y+m,0)}else s.value=v.contentRect.width,a.value=v.contentRect.height},n);f.tryOnMounted(()=>{const v=L(e);v&&(s.value="offsetWidth"in v?v.offsetWidth:t.width,a.value="offsetHeight"in v?v.offsetHeight:t.height)});const c=u.watch(()=>L(e),v=>{s.value=v?t.width:0,a.value=v?t.height:0});function d(){i(),c()}return{width:s,height:a,stop:d}}function qe(e,t,n={}){const{root:o,rootMargin:l="0px",threshold:r=.1,window:s=V,immediate:a=!0}=n,i=x(()=>s&&"IntersectionObserver"in s),c=u.computed(()=>{const y=f.toValue(e);return(Array.isArray(y)?y:[y]).map(L).filter(f.notNullish)});let d=f.noop;const v=u.ref(a),g=i.value?u.watch(()=>[c.value,L(o),v.value],([y,m])=>{if(d(),!v.value||!y.length)return;const b=new IntersectionObserver(t,{root:L(m),rootMargin:l,threshold:r});y.forEach(w=>w&&b.observe(w)),d=()=>{b.disconnect(),d=f.noop}},{immediate:a,flush:"post"}):f.noop,p=()=>{d(),g(),v.value=!1};return f.tryOnScopeDispose(p),{isSupported:i,isActive:v,pause(){d(),v.value=!1},resume(){v.value=!0},stop:p}}function Ge(e,t={}){const{window:n=V,scrollTarget:o}=t,l=u.ref(!1);return qe(e,r=>{let s=l.value,a=0;for(const i of r)i.time>=a&&(a=i.time,s=i.isIntersecting);l.value=s},{root:o,window:n,threshold:0}),l}const ae=new Map;function kn(e){const t=u.getCurrentScope();function n(a){var i;const c=ae.get(e)||new Set;c.add(a),ae.set(e,c);const d=()=>l(a);return(i=t?.cleanups)==null||i.push(d),d}function o(a){function i(...c){l(i),a(...c)}return n(i)}function l(a){const i=ae.get(e);i&&(i.delete(a),i.size||r())}function r(){ae.delete(e)}function s(a,i){var c;(c=ae.get(e))==null||c.forEach(d=>d(a,i))}return{on:n,once:o,off:l,emit:s,reset:r}}function _n(e,t=[],n={}){const o=u.ref(null),l=u.ref(null),r=u.ref("CONNECTING"),s=u.ref(null),a=u.shallowRef(null),{withCredentials:i=!1}=n,c=()=>{s.value&&(s.value.close(),s.value=null,r.value="CLOSED")},d=new EventSource(e,{withCredentials:i});s.value=d,d.onopen=()=>{r.value="OPEN",a.value=null},d.onerror=v=>{r.value="CLOSED",a.value=v},d.onmessage=v=>{o.value=null,l.value=v.data};for(const v of t)_(d,v,g=>{o.value=v,l.value=g.data||null});return f.tryOnScopeDispose(()=>{c()}),{eventSource:s,event:o,data:l,status:r,error:a,close:c}}function Rn(e={}){const{initialValue:t=""}=e,n=x(()=>typeof window<"u"&&"EyeDropper"in window),o=u.ref(t);async function l(r){if(!n.value)return;const a=await new window.EyeDropper().open(r);return o.value=a.sRGBHex,a}return{isSupported:n,sRGBHex:o,open:l}}function Fn(e=null,t={}){const{baseUrl:n="",rel:o="icon",document:l=B}=t,r=f.toRef(e),s=a=>{const i=l?.head.querySelectorAll(`link[rel*="${o}"]`);if(!i||i.length===0){const c=l?.createElement("link");c&&(c.rel=o,c.href=`${n}${a}`,c.type=`image/${a.split(".").pop()}`,l?.head.append(c));return}i?.forEach(c=>c.href=`${n}${a}`)};return u.watch(r,(a,i)=>{typeof a=="string"&&a!==i&&s(a)},{immediate:!0}),r}const Cn={json:"application/json",text:"text/plain"};function me(e){return e&&f.containsProp(e,"immediate","refetch","initialData","timeout","beforeFetch","afterFetch","onFetchError","fetch","updateDataOnError")}function Pn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function ue(e){return typeof Headers<"u"&&e instanceof Headers?Object.fromEntries([...e.entries()]):e}function te(e,...t){return e==="overwrite"?async n=>{const o=t[t.length-1];return o?{...n,...await o(n)}:n}:async n=>{for(const o of t)o&&(n={...n,...await o(n)});return n}}function An(e={}){const t=e.combination||"chain",n=e.options||{},o=e.fetchOptions||{};function l(r,...s){const a=u.computed(()=>{const d=f.toValue(e.baseUrl),v=f.toValue(r);return d&&!Pn(v)?Vn(d,v):v});let i=n,c=o;return s.length>0&&(me(s[0])?i={...i,...s[0],beforeFetch:te(t,n.beforeFetch,s[0].beforeFetch),afterFetch:te(t,n.afterFetch,s[0].afterFetch),onFetchError:te(t,n.onFetchError,s[0].onFetchError)}:c={...c,...s[0],headers:{...ue(c.headers)||{},...ue(s[0].headers)||{}}}),s.length>1&&me(s[1])&&(i={...i,...s[1],beforeFetch:te(t,n.beforeFetch,s[1].beforeFetch),afterFetch:te(t,n.afterFetch,s[1].afterFetch),onFetchError:te(t,n.onFetchError,s[1].onFetchError)}),Ye(a,c,i)}return l}function Ye(e,...t){var n;const o=typeof AbortController=="function";let l={},r={immediate:!0,refetch:!1,timeout:0,updateDataOnError:!1};const s={method:"GET",type:"text",payload:void 0};t.length>0&&(me(t[0])?r={...r,...t[0]}:l=t[0]),t.length>1&&me(t[1])&&(r={...r,...t[1]});const{fetch:a=(n=V)==null?void 0:n.fetch,initialData:i,timeout:c}=r,d=f.createEventHook(),v=f.createEventHook(),g=f.createEventHook(),p=u.ref(!1),y=u.ref(!1),m=u.ref(!1),b=u.ref(null),w=u.shallowRef(null),S=u.shallowRef(null),E=u.shallowRef(i||null),F=u.computed(()=>o&&y.value);let O,P;const k=()=>{o&&(O?.abort(),O=new AbortController,O.signal.onabort=()=>m.value=!0,l={...l,signal:O.signal})},A=I=>{y.value=I,p.value=!I};c&&(P=f.useTimeoutFn(k,c,{immediate:!1}));let R=0;const T=async(I=!1)=>{var H,j;k(),A(!0),S.value=null,b.value=null,m.value=!1,R+=1;const se=R,D={method:s.method,headers:{}};if(s.payload){const z=ue(D.headers),Q=f.toValue(s.payload);!s.payloadType&&Q&&Object.getPrototypeOf(Q)===Object.prototype&&!(Q instanceof FormData)&&(s.payloadType="json"),s.payloadType&&(z["Content-Type"]=(H=Cn[s.payloadType])!=null?H:s.payloadType),D.body=s.payloadType==="json"?JSON.stringify(Q):Q}let Et=!1;const ie={url:f.toValue(e),options:{...D,...l},cancel:()=>{Et=!0}};if(r.beforeFetch&&Object.assign(ie,await r.beforeFetch(ie)),Et||!a)return A(!1),Promise.resolve(null);let ee=null;return P&&P.start(),a(ie.url,{...D,...ie.options,headers:{...ue(D.headers),...ue((j=ie.options)==null?void 0:j.headers)}}).then(async z=>{if(w.value=z,b.value=z.status,ee=await z.clone()[s.type](),!z.ok)throw E.value=i||null,new Error(z.statusText);return r.afterFetch&&({data:ee}=await r.afterFetch({data:ee,response:z})),E.value=ee,d.trigger(z),z}).catch(async z=>{let Q=z.message||z.name;if(r.onFetchError&&({error:Q,data:ee}=await r.onFetchError({data:ee,error:z,response:w.value})),S.value=Q,r.updateDataOnError&&(E.value=ee),v.trigger(z),I)throw z;return null}).finally(()=>{se===R&&A(!1),P&&P.stop(),g.trigger(null)})},C=f.toRef(r.refetch);u.watch([C,f.toRef(e)],([I])=>I&&T(),{deep:!0});const M={isFinished:p,statusCode:b,response:w,error:S,data:E,isFetching:y,canAbort:F,aborted:m,abort:k,execute:T,onFetchResponse:d.on,onFetchError:v.on,onFetchFinally:g.on,get:N("GET"),put:N("PUT"),post:N("POST"),delete:N("DELETE"),patch:N("PATCH"),head:N("HEAD"),options:N("OPTIONS"),json:U("json"),text:U("text"),blob:U("blob"),arrayBuffer:U("arrayBuffer"),formData:U("formData")};function N(I){return(H,j)=>{if(!y.value)return s.method=I,s.payload=H,s.payloadType=j,u.isRef(s.payload)&&u.watch([C,f.toRef(s.payload)],([se])=>se&&T(),{deep:!0}),{...M,then(se,D){return W().then(se,D)}}}}function W(){return new Promise((I,H)=>{f.until(p).toBe(!0).then(()=>I(M)).catch(j=>H(j))})}function U(I){return()=>{if(!y.value)return s.type=I,{...M,then(H,j){return W().then(H,j)}}}}return r.immediate&&Promise.resolve().then(()=>T()),{...M,then(I,H){return W().then(I,H)}}}function Vn(e,t){return!e.endsWith("/")&&!t.startsWith("/")?`${e}/${t}`:`${e}${t}`}const In={multiple:!0,accept:"*",reset:!1,directory:!1};function Mn(e={}){const{document:t=B}=e,n=u.ref(null),{on:o,trigger:l}=f.createEventHook();let r;t&&(r=t.createElement("input"),r.type="file",r.onchange=i=>{const c=i.target;n.value=c.files,l(n.value)});const s=()=>{n.value=null,r&&(r.value="",l(null))},a=i=>{if(!r)return;const c={...In,...e,...i};r.multiple=c.multiple,r.accept=c.accept,r.webkitdirectory=c.directory,f.hasOwn(c,"capture")&&(r.capture=c.capture),c.reset&&s(),r.click()};return{files:u.readonly(n),open:a,reset:s,onChange:o}}function Ln(e={}){const{window:t=V,dataType:n="Text"}=e,o=t,l=x(()=>o&&"showSaveFilePicker"in o&&"showOpenFilePicker"in o),r=u.ref(),s=u.ref(),a=u.ref(),i=u.computed(()=>{var S,E;return(E=(S=a.value)==null?void 0:S.name)!=null?E:""}),c=u.computed(()=>{var S,E;return(E=(S=a.value)==null?void 0:S.type)!=null?E:""}),d=u.computed(()=>{var S,E;return(E=(S=a.value)==null?void 0:S.size)!=null?E:0}),v=u.computed(()=>{var S,E;return(E=(S=a.value)==null?void 0:S.lastModified)!=null?E:0});async function g(S={}){if(!l.value)return;const[E]=await o.showOpenFilePicker({...f.toValue(e),...S});r.value=E,await b(),await w()}async function p(S={}){l.value&&(r.value=await o.showSaveFilePicker({...e,...S}),s.value=void 0,await b(),await w())}async function y(S={}){if(l.value){if(!r.value)return m(S);if(s.value){const E=await r.value.createWritable();await E.write(s.value),await E.close()}await b()}}async function m(S={}){if(l.value){if(r.value=await o.showSaveFilePicker({...e,...S}),s.value){const E=await r.value.createWritable();await E.write(s.value),await E.close()}await b()}}async function b(){var S;a.value=await((S=r.value)==null?void 0:S.getFile())}async function w(){var S,E;const F=f.toValue(n);F==="Text"?s.value=await((S=a.value)==null?void 0:S.text()):F==="ArrayBuffer"?s.value=await((E=a.value)==null?void 0:E.arrayBuffer()):F==="Blob"&&(s.value=a.value)}return u.watch(()=>f.toValue(n),w),{isSupported:l,data:s,file:a,fileName:i,fileMIME:c,fileSize:d,fileLastModified:v,open:g,create:p,save:y,saveAs:m,updateData:w}}function Nn(e,t={}){const{initialValue:n=!1,focusVisible:o=!1}=t,l=u.ref(!1),r=u.computed(()=>L(e));_(r,"focus",a=>{var i,c;(!o||(c=(i=a.target).matches)!=null&&c.call(i,":focus-visible"))&&(l.value=!0)}),_(r,"blur",()=>l.value=!1);const s=u.computed({get:()=>l.value,set(a){var i,c;!a&&l.value?(i=r.value)==null||i.blur():a&&!l.value&&((c=r.value)==null||c.focus())}});return u.watch(r,()=>{s.value=n},{immediate:!0,flush:"post"}),{focused:s}}function xn(e,t={}){const n=Ie(t),o=u.computed(()=>L(e));return{focused:u.computed(()=>o.value&&n.value?o.value.contains(n.value):!1)}}function Wn(e){var t;const n=u.ref(0);if(typeof performance>"u")return n;const o=(t=e?.every)!=null?t:10;let l=performance.now(),r=0;return Z(()=>{if(r+=1,r>=o){const s=performance.now(),a=s-l;n.value=Math.round(1e3/(a/r)),l=s,r=0}}),n}const Xe=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function Hn(e,t={}){const{document:n=B,autoExit:o=!1}=t,l=u.computed(()=>{var w;return(w=L(e))!=null?w:n?.querySelector("html")}),r=u.ref(!1),s=u.computed(()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find(w=>n&&w in n||l.value&&w in l.value)),a=u.computed(()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find(w=>n&&w in n||l.value&&w in l.value)),i=u.computed(()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find(w=>n&&w in n||l.value&&w in l.value)),c=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find(w=>n&&w in n),d=x(()=>l.value&&n&&s.value!==void 0&&a.value!==void 0&&i.value!==void 0),v=()=>c?n?.[c]===l.value:!1,g=()=>{if(i.value){if(n&&n[i.value]!=null)return n[i.value];{const w=l.value;if(w?.[i.value]!=null)return!!w[i.value]}}return!1};async function p(){if(!(!d.value||!r.value)){if(a.value)if(n?.[a.value]!=null)await n[a.value]();else{const w=l.value;w?.[a.value]!=null&&await w[a.value]()}r.value=!1}}async function y(){if(!d.value||r.value)return;g()&&await p();const w=l.value;s.value&&w?.[s.value]!=null&&(await w[s.value](),r.value=!0)}async function m(){await(r.value?p():y())}const b=()=>{const w=g();(!w||w&&v())&&(r.value=w)};return _(n,Xe,b,!1),_(()=>L(l),Xe,b,!1),o&&f.tryOnScopeDispose(p),{isSupported:d,isFullscreen:r,enter:y,exit:p,toggle:m}}function Bn(e){return u.computed(()=>e.value?{buttons:{a:e.value.buttons[0],b:e.value.buttons[1],x:e.value.buttons[2],y:e.value.buttons[3]},bumper:{left:e.value.buttons[4],right:e.value.buttons[5]},triggers:{left:e.value.buttons[6],right:e.value.buttons[7]},stick:{left:{horizontal:e.value.axes[0],vertical:e.value.axes[1],button:e.value.buttons[10]},right:{horizontal:e.value.axes[2],vertical:e.value.axes[3],button:e.value.buttons[11]}},dpad:{up:e.value.buttons[12],down:e.value.buttons[13],left:e.value.buttons[14],right:e.value.buttons[15]},back:e.value.buttons[8],start:e.value.buttons[9]}:null)}function Un(e={}){const{navigator:t=$}=e,n=x(()=>t&&"getGamepads"in t),o=u.ref([]),l=f.createEventHook(),r=f.createEventHook(),s=p=>{const y=[],m="vibrationActuator"in p?p.vibrationActuator:null;return m&&y.push(m),p.hapticActuators&&y.push(...p.hapticActuators),{...p,id:p.id,hapticActuators:y,axes:p.axes.map(b=>b),buttons:p.buttons.map(b=>({pressed:b.pressed,touched:b.touched,value:b.value}))}},a=()=>{const p=t?.getGamepads()||[];for(let y=0;y<p.length;++y){const m=p[y];if(m){const b=o.value.findIndex(({index:w})=>w===m.index);b>-1&&(o.value[b]=s(m))}}},{isActive:i,pause:c,resume:d}=Z(a),v=p=>{o.value.some(({index:y})=>y===p.index)||(o.value.push(s(p)),l.trigger(p.index)),d()},g=p=>{o.value=o.value.filter(y=>y.index!==p.index),r.trigger(p.index)};return _("gamepadconnected",p=>v(p.gamepad)),_("gamepaddisconnected",p=>g(p.gamepad)),f.tryOnMounted(()=>{const p=t?.getGamepads()||[];if(p)for(let y=0;y<p.length;++y){const m=p[y];m&&v(m)}}),c(),{isSupported:n,onConnected:l.on,onDisconnected:r.on,gamepads:o,pause:c,resume:d,isActive:i}}function $n(e={}){const{enableHighAccuracy:t=!0,maximumAge:n=3e4,timeout:o=27e3,navigator:l=$,immediate:r=!0}=e,s=x(()=>l&&"geolocation"in l),a=u.ref(null),i=u.shallowRef(null),c=u.ref({accuracy:0,latitude:Number.POSITIVE_INFINITY,longitude:Number.POSITIVE_INFINITY,altitude:null,altitudeAccuracy:null,heading:null,speed:null});function d(y){a.value=y.timestamp,c.value=y.coords,i.value=null}let v;function g(){s.value&&(v=l.geolocation.watchPosition(d,y=>i.value=y,{enableHighAccuracy:t,maximumAge:n,timeout:o}))}r&&g();function p(){v&&l&&l.geolocation.clearWatch(v)}return f.tryOnScopeDispose(()=>{p()}),{isSupported:s,coords:c,locatedAt:a,error:i,resume:g,pause:p}}const jn=["mousemove","mousedown","resize","keydown","touchstart","wheel"],zn=6e4;function qn(e=zn,t={}){const{initialState:n=!1,listenForVisibilityChange:o=!0,events:l=jn,window:r=V,eventFilter:s=f.throttleFilter(50)}=t,a=u.ref(n),i=u.ref(f.timestamp());let c;const d=()=>{a.value=!1,clearTimeout(c),c=setTimeout(()=>a.value=!0,e)},v=f.createFilterWrapper(s,()=>{i.value=f.timestamp(),d()});if(r){const g=r.document;for(const p of l)_(r,p,v,{passive:!0});o&&_(g,"visibilitychange",()=>{g.hidden||v()}),d()}return{idle:a,lastActive:i,reset:d}}async function Gn(e){return new Promise((t,n)=>{const o=new Image,{src:l,srcset:r,sizes:s,class:a,loading:i,crossorigin:c,referrerPolicy:d}=e;o.src=l,r&&(o.srcset=r),s&&(o.sizes=s),a&&(o.className=a),i&&(o.loading=i),c&&(o.crossOrigin=c),d&&(o.referrerPolicy=d),o.onload=()=>t(o),o.onerror=n})}function Yn(e,t={}){const n=Le(()=>Gn(f.toValue(e)),void 0,{resetOnExecute:!0,...t});return u.watch(()=>f.toValue(e),()=>n.execute(t.delay),{deep:!0}),n}const Ke=1;function Je(e,t={}){const{throttle:n=0,idle:o=200,onStop:l=f.noop,onScroll:r=f.noop,offset:s={left:0,right:0,top:0,bottom:0},eventListenerOptions:a={capture:!1,passive:!0},behavior:i="auto",window:c=V}=t,d=u.ref(0),v=u.ref(0),g=u.computed({get(){return d.value},set(P){y(P,void 0)}}),p=u.computed({get(){return v.value},set(P){y(void 0,P)}});function y(P,k){var A,R,T;if(!c)return;const C=f.toValue(e);C&&((T=C instanceof Document?c.document.body:C)==null||T.scrollTo({top:(A=f.toValue(k))!=null?A:p.value,left:(R=f.toValue(P))!=null?R:g.value,behavior:f.toValue(i)}))}const m=u.ref(!1),b=u.reactive({left:!0,right:!1,top:!0,bottom:!1}),w=u.reactive({left:!1,right:!1,top:!1,bottom:!1}),S=P=>{m.value&&(m.value=!1,w.left=!1,w.right=!1,w.top=!1,w.bottom=!1,l(P))},E=f.useDebounceFn(S,n+o),F=P=>{var k;if(!c)return;const A=P.document?P.document.documentElement:(k=P.documentElement)!=null?k:P,{display:R,flexDirection:T}=getComputedStyle(A),C=A.scrollLeft;w.left=C<d.value,w.right=C>d.value;const M=Math.abs(C)<=0+(s.left||0),N=Math.abs(C)+A.clientWidth>=A.scrollWidth-(s.right||0)-Ke;R==="flex"&&T==="row-reverse"?(b.left=N,b.right=M):(b.left=M,b.right=N),d.value=C;let W=A.scrollTop;P===c.document&&!W&&(W=c.document.body.scrollTop),w.top=W<v.value,w.bottom=W>v.value;const U=Math.abs(W)<=0+(s.top||0),I=Math.abs(W)+A.clientHeight>=A.scrollHeight-(s.bottom||0)-Ke;R==="flex"&&T==="column-reverse"?(b.top=I,b.bottom=U):(b.top=U,b.bottom=I),v.value=W},O=P=>{var k;if(!c)return;const A=(k=P.target.documentElement)!=null?k:P.target;F(A),m.value=!0,E(P),r(P)};return _(e,"scroll",n?f.useThrottleFn(O,n,!0,!1):O,a),f.tryOnMounted(()=>{const P=f.toValue(e);P&&F(P)}),_(e,"scrollend",S,a),{x:g,y:p,isScrolling:m,arrivedState:b,directions:w,measure(){const P=f.toValue(e);c&&P&&F(P)}}}function he(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}function Xn(e,t,n={}){var o;const{direction:l="bottom",interval:r=100,canLoadMore:s=()=>!0}=n,a=u.reactive(Je(e,{...n,offset:{[l]:(o=n.distance)!=null?o:0,...n.offset}})),i=u.ref(),c=u.computed(()=>!!i.value),d=u.computed(()=>he(f.toValue(e))),v=Ge(d);function g(){if(a.measure(),!d.value||!v.value||!s(d.value))return;const{scrollHeight:p,clientHeight:y,scrollWidth:m,clientWidth:b}=d.value,w=l==="bottom"||l==="top"?p<=y:m<=b;(a.arrivedState[l]||w)&&(i.value||(i.value=Promise.all([t(a),new Promise(S=>setTimeout(S,r))]).finally(()=>{i.value=null,u.nextTick(()=>g())})))}return u.watch(()=>[a.arrivedState[l],v.value],g,{immediate:!0}),{isLoading:c}}const Kn=["mousedown","mouseup","keydown","keyup"];function Jn(e,t={}){const{events:n=Kn,document:o=B,initial:l=null}=t,r=u.ref(l);return o&&n.forEach(s=>{_(o,s,a=>{typeof a.getModifierState=="function"&&(r.value=a.getModifierState(e))})}),r}function Qn(e,t,n={}){const{window:o=V}=n;return ye(e,t,o?.localStorage,n)}const Qe={ctrl:"control",command:"meta",cmd:"meta",option:"alt",up:"arrowup",down:"arrowdown",left:"arrowleft",right:"arrowright"};function Zn(e={}){const{reactive:t=!1,target:n=V,aliasMap:o=Qe,passive:l=!0,onEventFired:r=f.noop}=e,s=u.reactive(new Set),a={toJSON(){return{}},current:s},i=t?u.reactive(a):a,c=new Set,d=new Set;function v(m,b){m in i&&(t?i[m]=b:i[m].value=b)}function g(){s.clear();for(const m of d)v(m,!1)}function p(m,b){var w,S;const E=(w=m.key)==null?void 0:w.toLowerCase(),O=[(S=m.code)==null?void 0:S.toLowerCase(),E].filter(Boolean);E&&(b?s.add(E):s.delete(E));for(const P of O)d.add(P),v(P,b);E==="meta"&&!b?(c.forEach(P=>{s.delete(P),v(P,!1)}),c.clear()):typeof m.getModifierState=="function"&&m.getModifierState("Meta")&&b&&[...s,...O].forEach(P=>c.add(P))}_(n,"keydown",m=>(p(m,!0),r(m)),{passive:l}),_(n,"keyup",m=>(p(m,!1),r(m)),{passive:l}),_("blur",g,{passive:!0}),_("focus",g,{passive:!0});const y=new Proxy(i,{get(m,b,w){if(typeof b!="string")return Reflect.get(m,b,w);if(b=b.toLowerCase(),b in o&&(b=o[b]),!(b in i))if(/[+_-]/.test(b)){const E=b.split(/[+_-]/g).map(F=>F.trim());i[b]=u.computed(()=>E.every(F=>f.toValue(y[F])))}else i[b]=u.ref(!1);const S=Reflect.get(m,b,w);return t?f.toValue(S):S}});return y}function Re(e,t){f.toValue(e)&&t(f.toValue(e))}function Dn(e){let t=[];for(let n=0;n<e.length;++n)t=[...t,[e.start(n),e.end(n)]];return t}function Fe(e){return Array.from(e).map(({label:t,kind:n,language:o,mode:l,activeCues:r,cues:s,inBandMetadataTrackDispatchType:a},i)=>({id:i,label:t,kind:n,language:o,mode:l,activeCues:r,cues:s,inBandMetadataTrackDispatchType:a}))}const eo={src:"",tracks:[]};function to(e,t={}){t={...eo,...t};const{document:n=B}=t,o=u.ref(0),l=u.ref(0),r=u.ref(!1),s=u.ref(1),a=u.ref(!1),i=u.ref(!1),c=u.ref(!1),d=u.ref(1),v=u.ref(!1),g=u.ref([]),p=u.ref([]),y=u.ref(-1),m=u.ref(!1),b=u.ref(!1),w=n&&"pictureInPictureEnabled"in n,S=f.createEventHook(),E=T=>{Re(e,C=>{if(T){const M=typeof T=="number"?T:T.id;C.textTracks[M].mode="disabled"}else for(let M=0;M<C.textTracks.length;++M)C.textTracks[M].mode="disabled";y.value=-1})},F=(T,C=!0)=>{Re(e,M=>{const N=typeof T=="number"?T:T.id;C&&E(),M.textTracks[N].mode="showing",y.value=N})},O=()=>new Promise((T,C)=>{Re(e,async M=>{w&&(m.value?n.exitPictureInPicture().then(T).catch(C):M.requestPictureInPicture().then(T).catch(C))})});u.watchEffect(()=>{if(!n)return;const T=f.toValue(e);if(!T)return;const C=f.toValue(t.src);let M=[];C&&(typeof C=="string"?M=[{src:C}]:Array.isArray(C)?M=C:f.isObject(C)&&(M=[C]),T.querySelectorAll("source").forEach(N=>{N.removeEventListener("error",S.trigger),N.remove()}),M.forEach(({src:N,type:W})=>{const U=n.createElement("source");U.setAttribute("src",N),U.setAttribute("type",W||""),U.addEventListener("error",S.trigger),T.appendChild(U)}),T.load())}),f.tryOnScopeDispose(()=>{const T=f.toValue(e);T&&T.querySelectorAll("source").forEach(C=>C.removeEventListener("error",S.trigger))}),u.watch([e,s],()=>{const T=f.toValue(e);T&&(T.volume=s.value)}),u.watch([e,b],()=>{const T=f.toValue(e);T&&(T.muted=b.value)}),u.watch([e,d],()=>{const T=f.toValue(e);T&&(T.playbackRate=d.value)}),u.watchEffect(()=>{if(!n)return;const T=f.toValue(t.tracks),C=f.toValue(e);!T||!T.length||!C||(C.querySelectorAll("track").forEach(M=>M.remove()),T.forEach(({default:M,kind:N,label:W,src:U,srcLang:I},H)=>{const j=n.createElement("track");j.default=M||!1,j.kind=N,j.label=W,j.src=U,j.srclang=I,j.default&&(y.value=H),C.appendChild(j)}))});const{ignoreUpdates:P}=f.watchIgnorable(o,T=>{const C=f.toValue(e);C&&(C.currentTime=T)}),{ignoreUpdates:k}=f.watchIgnorable(c,T=>{const C=f.toValue(e);C&&(T?C.play():C.pause())});_(e,"timeupdate",()=>P(()=>o.value=f.toValue(e).currentTime)),_(e,"durationchange",()=>l.value=f.toValue(e).duration),_(e,"progress",()=>g.value=Dn(f.toValue(e).buffered)),_(e,"seeking",()=>r.value=!0),_(e,"seeked",()=>r.value=!1),_(e,["waiting","loadstart"],()=>{a.value=!0,k(()=>c.value=!1)}),_(e,"loadeddata",()=>a.value=!1),_(e,"playing",()=>{a.value=!1,i.value=!1,k(()=>c.value=!0)}),_(e,"ratechange",()=>d.value=f.toValue(e).playbackRate),_(e,"stalled",()=>v.value=!0),_(e,"ended",()=>i.value=!0),_(e,"pause",()=>k(()=>c.value=!1)),_(e,"play",()=>k(()=>c.value=!0)),_(e,"enterpictureinpicture",()=>m.value=!0),_(e,"leavepictureinpicture",()=>m.value=!1),_(e,"volumechange",()=>{const T=f.toValue(e);T&&(s.value=T.volume,b.value=T.muted)});const A=[],R=u.watch([e],()=>{const T=f.toValue(e);T&&(R(),A[0]=_(T.textTracks,"addtrack",()=>p.value=Fe(T.textTracks)),A[1]=_(T.textTracks,"removetrack",()=>p.value=Fe(T.textTracks)),A[2]=_(T.textTracks,"change",()=>p.value=Fe(T.textTracks)))});return f.tryOnScopeDispose(()=>A.forEach(T=>T())),{currentTime:o,duration:l,waiting:a,seeking:r,ended:i,stalled:v,buffered:g,playing:c,rate:d,volume:s,muted:b,tracks:p,selectedTrack:y,enableTrack:F,disableTrack:E,supportsPictureInPicture:w,togglePictureInPicture:O,isPictureInPicture:m,onSourceError:S.on}}function no(){const e=u.shallowReactive({});return{get:t=>e[t],set:(t,n)=>u.set(e,t,n),has:t=>f.hasOwn(e,t),delete:t=>u.del(e,t),clear:()=>{Object.keys(e).forEach(t=>{u.del(e,t)})}}}function oo(e,t){const o=t?.cache?u.shallowReactive(t.cache):u.isVue2?no():u.shallowReactive(new Map),l=(...d)=>t?.getKey?t.getKey(...d):JSON.stringify(d),r=(d,...v)=>(o.set(d,e(...v)),o.get(d)),s=(...d)=>r(l(...d),...d),a=(...d)=>{o.delete(l(...d))},i=()=>{o.clear()},c=(...d)=>{const v=l(...d);return o.has(v)?o.get(v):r(v,...d)};return c.load=s,c.delete=a,c.clear=i,c.generateKey=l,c.cache=o,c}function ro(e={}){const t=u.ref(),n=x(()=>typeof performance<"u"&&"memory"in performance);if(n.value){const{interval:o=1e3}=e;f.useIntervalFn(()=>{t.value=performance.memory},o,{immediate:e.immediate,immediateCallback:e.immediateCallback})}return{isSupported:n,memory:t}}const lo={page:e=>[e.pageX,e.pageY],client:e=>[e.clientX,e.clientY],screen:e=>[e.screenX,e.screenY],movement:e=>e instanceof Touch?null:[e.movementX,e.movementY]};function Ze(e={}){const{type:t="page",touch:n=!0,resetOnTouchEnds:o=!1,initialValue:l={x:0,y:0},window:r=V,target:s=r,scroll:a=!0,eventFilter:i}=e;let c=null;const d=u.ref(l.x),v=u.ref(l.y),g=u.ref(null),p=typeof t=="function"?t:lo[t],y=O=>{const P=p(O);c=O,P&&([d.value,v.value]=P,g.value="mouse")},m=O=>{if(O.touches.length>0){const P=p(O.touches[0]);P&&([d.value,v.value]=P,g.value="touch")}},b=()=>{if(!c||!r)return;const O=p(c);c instanceof MouseEvent&&O&&(d.value=O[0]+r.scrollX,v.value=O[1]+r.scrollY)},w=()=>{d.value=l.x,v.value=l.y},S=i?O=>i(()=>y(O),{}):O=>y(O),E=i?O=>i(()=>m(O),{}):O=>m(O),F=i?()=>i(()=>b(),{}):()=>b();if(s){const O={passive:!0};_(s,["mousemove","dragover"],S,O),n&&t!=="movement"&&(_(s,["touchstart","touchmove"],E,O),o&&_(s,"touchend",w,O)),a&&t==="page"&&_(r,"scroll",F,{passive:!0})}return{x:d,y:v,sourceType:g}}function De(e,t={}){const{handleOutside:n=!0,window:o=V}=t,l=t.type||"page",{x:r,y:s,sourceType:a}=Ze(t),i=u.ref(e??o?.document.body),c=u.ref(0),d=u.ref(0),v=u.ref(0),g=u.ref(0),p=u.ref(0),y=u.ref(0),m=u.ref(!0);let b=()=>{};return o&&(b=u.watch([i,r,s],()=>{const w=L(i);if(!w)return;const{left:S,top:E,width:F,height:O}=w.getBoundingClientRect();v.value=S+(l==="page"?o.pageXOffset:0),g.value=E+(l==="page"?o.pageYOffset:0),p.value=O,y.value=F;const P=r.value-v.value,k=s.value-g.value;m.value=F===0||O===0||P<0||k<0||P>F||k>O,(n||!m.value)&&(c.value=P,d.value=k)},{immediate:!0}),_(document,"mouseleave",()=>{m.value=!0})),{x:r,y:s,sourceType:a,elementX:c,elementY:d,elementPositionX:v,elementPositionY:g,elementHeight:p,elementWidth:y,isOutside:m,stop:b}}function ao(e={}){const{touch:t=!0,drag:n=!0,capture:o=!1,initialValue:l=!1,window:r=V}=e,s=u.ref(l),a=u.ref(null);if(!r)return{pressed:s,sourceType:a};const i=v=>()=>{s.value=!0,a.value=v},c=()=>{s.value=!1,a.value=null},d=u.computed(()=>L(e.target)||r);return _(d,"mousedown",i("mouse"),{passive:!0,capture:o}),_(r,"mouseleave",c,{passive:!0,capture:o}),_(r,"mouseup",c,{passive:!0,capture:o}),n&&(_(d,"dragstart",i("mouse"),{passive:!0,capture:o}),_(r,"drop",c,{passive:!0,capture:o}),_(r,"dragend",c,{passive:!0,capture:o})),t&&(_(d,"touchstart",i("touch"),{passive:!0,capture:o}),_(r,"touchend",c,{passive:!0,capture:o}),_(r,"touchcancel",c,{passive:!0,capture:o})),{pressed:s,sourceType:a}}function uo(e={}){const{window:t=V}=e,n=t?.navigator,o=x(()=>n&&"language"in n),l=u.ref(n?.language);return _(t,"languagechange",()=>{n&&(l.value=n.language)}),{isSupported:o,language:l}}function et(e={}){const{window:t=V}=e,n=t?.navigator,o=x(()=>n&&"connection"in n),l=u.ref(!0),r=u.ref(!1),s=u.ref(void 0),a=u.ref(void 0),i=u.ref(void 0),c=u.ref(void 0),d=u.ref(void 0),v=u.ref(void 0),g=u.ref("unknown"),p=o.value&&n.connection;function y(){n&&(l.value=n.onLine,s.value=l.value?void 0:Date.now(),a.value=l.value?Date.now():void 0,p&&(i.value=p.downlink,c.value=p.downlinkMax,v.value=p.effectiveType,d.value=p.rtt,r.value=p.saveData,g.value=p.type))}return t&&(_(t,"offline",()=>{l.value=!1,s.value=Date.now()}),_(t,"online",()=>{l.value=!0,a.value=Date.now()})),p&&_(p,"change",y,!1),y(),{isSupported:o,isOnline:l,saveData:r,offlineAt:s,onlineAt:a,downlink:i,downlinkMax:c,effectiveType:v,rtt:d,type:g}}function tt(e={}){const{controls:t=!1,interval:n="requestAnimationFrame"}=e,o=u.ref(new Date),l=()=>o.value=new Date,r=n==="requestAnimationFrame"?Z(l,{immediate:!0}):f.useIntervalFn(l,n,{immediate:!0});return t?{now:o,...r}:o}function so(e){const t=u.ref(),n=()=>{t.value&&URL.revokeObjectURL(t.value),t.value=void 0};return u.watch(()=>f.toValue(e),o=>{n(),o&&(t.value=URL.createObjectURL(o))},{immediate:!0}),f.tryOnScopeDispose(n),u.readonly(t)}function nt(e,t,n){if(typeof e=="function"||u.isReadonly(e))return u.computed(()=>f.clamp(f.toValue(e),f.toValue(t),f.toValue(n)));const o=u.ref(e);return u.computed({get(){return o.value=f.clamp(o.value,f.toValue(t),f.toValue(n))},set(l){o.value=f.clamp(l,f.toValue(t),f.toValue(n))}})}function io(e){const{total:t=Number.POSITIVE_INFINITY,pageSize:n=10,page:o=1,onPageChange:l=f.noop,onPageSizeChange:r=f.noop,onPageCountChange:s=f.noop}=e,a=nt(n,1,Number.POSITIVE_INFINITY),i=u.computed(()=>Math.max(1,Math.ceil(f.toValue(t)/f.toValue(a)))),c=nt(o,1,i),d=u.computed(()=>c.value===1),v=u.computed(()=>c.value===i.value);u.isRef(o)&&f.syncRef(o,c,{direction:u.isReadonly(o)?"ltr":"both"}),u.isRef(n)&&f.syncRef(n,a,{direction:u.isReadonly(n)?"ltr":"both"});function g(){c.value--}function p(){c.value++}const y={currentPage:c,currentPageSize:a,pageCount:i,isFirstPage:d,isLastPage:v,prev:g,next:p};return u.watch(c,()=>{l(u.reactive(y))}),u.watch(a,()=>{r(u.reactive(y))}),u.watch(i,()=>{s(u.reactive(y))}),y}function co(e={}){const{isOnline:t}=et(e);return t}function fo(e={}){const{window:t=V}=e,n=u.ref(!1),o=l=>{if(!t)return;l=l||t.event;const r=l.relatedTarget||l.toElement;n.value=!r};return t&&(_(t,"mouseout",o,{passive:!0}),_(t.document,"mouseleave",o,{passive:!0}),_(t.document,"mouseenter",o,{passive:!0})),n}function vo(e,t={}){const{deviceOrientationTiltAdjust:n=m=>m,deviceOrientationRollAdjust:o=m=>m,mouseTiltAdjust:l=m=>m,mouseRollAdjust:r=m=>m,window:s=V}=t,a=u.reactive(je({window:s})),{elementX:i,elementY:c,elementWidth:d,elementHeight:v}=De(e,{handleOutside:!1,window:s}),g=u.computed(()=>a.isSupported&&(a.alpha!=null&&a.alpha!==0||a.gamma!=null&&a.gamma!==0)?"deviceOrientation":"mouse"),p=u.computed(()=>{if(g.value==="deviceOrientation"){const m=-a.beta/90;return o(m)}else{const m=-(c.value-v.value/2)/v.value;return r(m)}}),y=u.computed(()=>{if(g.value==="deviceOrientation"){const m=a.gamma/90;return n(m)}else{const m=(i.value-d.value/2)/d.value;return l(m)}});return{roll:p,tilt:y,source:g}}function po(e=Be()){const t=u.shallowRef(),n=()=>{const o=L(e);o&&(t.value=o.parentElement)};return f.tryOnMounted(n),u.watch(()=>f.toValue(e),n),t}function yo(e,t){const{window:n=V,immediate:o=!0,...l}=e,r=x(()=>n&&"PerformanceObserver"in n);let s;const a=()=>{s?.disconnect()},i=()=>{r.value&&(a(),s=new PerformanceObserver(t),s.observe(l))};return f.tryOnScopeDispose(a),o&&i(),{isSupported:r,start:i,stop:a}}const ot={x:0,y:0,pointerId:0,pressure:0,tiltX:0,tiltY:0,width:0,height:0,twist:0,pointerType:null},go=Object.keys(ot);function mo(e={}){const{target:t=V}=e,n=u.ref(!1),o=u.ref(e.initialValue||{});Object.assign(o.value,ot,o.value);const l=r=>{n.value=!0,!(e.pointerTypes&&!e.pointerTypes.includes(r.pointerType))&&(o.value=f.objectPick(r,go,!1))};if(t){const r={passive:!0};_(t,["pointerdown","pointermove","pointerup"],l,r),_(t,"pointerleave",()=>n.value=!1,r)}return{...f.toRefs(o),isInside:n}}function ho(e,t={}){const{document:n=B,pointerLockOptions:o}=t,l=x(()=>n&&"pointerLockElement"in n),r=u.ref(),s=u.ref();let a;l.value&&(_(n,"pointerlockchange",()=>{var d;const v=(d=n.pointerLockElement)!=null?d:r.value;a&&v===a&&(r.value=n.pointerLockElement,r.value||(a=s.value=null))}),_(n,"pointerlockerror",()=>{var d;const v=(d=n.pointerLockElement)!=null?d:r.value;if(a&&v===a){const g=n.pointerLockElement?"release":"acquire";throw new Error(`Failed to ${g} pointer lock.`)}}));async function i(d,v){var g;if(!l.value)throw new Error("Pointer Lock API is not supported by your browser.");if(s.value=d instanceof Event?d.currentTarget:null,a=d instanceof Event?(g=L(e))!=null?g:s.value:L(d),!a)throw new Error("Target element undefined.");return a.requestPointerLock(v??o),await f.until(r).toBe(a)}async function c(){return r.value?(n.exitPointerLock(),await f.until(r).toBeNull(),!0):!1}return{isSupported:l,element:r,triggerElement:s,lock:i,unlock:c}}function wo(e,t={}){const n=f.toRef(e),{threshold:o=50,onSwipe:l,onSwipeEnd:r,onSwipeStart:s,disableTextSelect:a=!1}=t,i=u.reactive({x:0,y:0}),c=(k,A)=>{i.x=k,i.y=A},d=u.reactive({x:0,y:0}),v=(k,A)=>{d.x=k,d.y=A},g=u.computed(()=>i.x-d.x),p=u.computed(()=>i.y-d.y),{max:y,abs:m}=Math,b=u.computed(()=>y(m(g.value),m(p.value))>=o),w=u.ref(!1),S=u.ref(!1),E=u.computed(()=>b.value?m(g.value)>m(p.value)?g.value>0?"left":"right":p.value>0?"up":"down":"none"),F=k=>{var A,R,T;const C=k.buttons===0,M=k.buttons===1;return(T=(R=(A=t.pointerTypes)==null?void 0:A.includes(k.pointerType))!=null?R:C||M)!=null?T:!0},O=[_(e,"pointerdown",k=>{if(!F(k))return;S.value=!0;const A=k.target;A?.setPointerCapture(k.pointerId);const{clientX:R,clientY:T}=k;c(R,T),v(R,T),s?.(k)}),_(e,"pointermove",k=>{if(!F(k)||!S.value)return;const{clientX:A,clientY:R}=k;v(A,R),!w.value&&b.value&&(w.value=!0),w.value&&l?.(k)}),_(e,"pointerup",k=>{F(k)&&(w.value&&r?.(k,E.value),S.value=!1,w.value=!1)})];f.tryOnMounted(()=>{var k,A,R,T,C,M,N,W;(A=(k=n.value)==null?void 0:k.style)==null||A.setProperty("touch-action","none"),a&&((T=(R=n.value)==null?void 0:R.style)==null||T.setProperty("-webkit-user-select","none"),(M=(C=n.value)==null?void 0:C.style)==null||M.setProperty("-ms-user-select","none"),(W=(N=n.value)==null?void 0:N.style)==null||W.setProperty("user-select","none"))});const P=()=>O.forEach(k=>k());return{isSwiping:u.readonly(w),direction:u.readonly(E),posStart:u.readonly(i),posEnd:u.readonly(d),distanceX:g,distanceY:p,stop:P}}function bo(e){const t=G("(prefers-color-scheme: light)",e),n=G("(prefers-color-scheme: dark)",e);return u.computed(()=>n.value?"dark":t.value?"light":"no-preference")}function So(e){const t=G("(prefers-contrast: more)",e),n=G("(prefers-contrast: less)",e),o=G("(prefers-contrast: custom)",e);return u.computed(()=>t.value?"more":n.value?"less":o.value?"custom":"no-preference")}function Eo(e={}){const{window:t=V}=e;if(!t)return u.ref(["en"]);const n=t.navigator,o=u.ref(n.languages);return _(t,"languagechange",()=>{o.value=n.languages}),o}function To(e){const t=G("(prefers-reduced-motion: reduce)",e);return u.computed(()=>t.value?"reduce":"no-preference")}function Oo(e,t){const n=u.shallowRef(t);return u.watch(f.toRef(e),(o,l)=>{n.value=l},{flush:"sync"}),u.readonly(n)}function ko(e={}){const{window:t=V}=e,n=x(()=>t&&"screen"in t&&"orientation"in t.screen),o=n.value?t.screen.orientation:{},l=u.ref(o.type),r=u.ref(o.angle||0);return n.value&&_(t,"orientationchange",()=>{l.value=o.type,r.value=o.angle}),{isSupported:n,orientation:l,angle:r,lockOrientation:i=>n.value&&typeof o.lock=="function"?o.lock(i):Promise.reject(new Error("Not supported")),unlockOrientation:()=>{n.value&&typeof o.unlock=="function"&&o.unlock()}}}const rt="--vueuse-safe-area-top",lt="--vueuse-safe-area-right",at="--vueuse-safe-area-bottom",ut="--vueuse-safe-area-left";function _o(){const e=u.ref(""),t=u.ref(""),n=u.ref(""),o=u.ref("");if(f.isClient){const r=le(rt),s=le(lt),a=le(at),i=le(ut);r.value="env(safe-area-inset-top, 0px)",s.value="env(safe-area-inset-right, 0px)",a.value="env(safe-area-inset-bottom, 0px)",i.value="env(safe-area-inset-left, 0px)",l(),_("resize",f.useDebounceFn(l))}function l(){e.value=we(rt),t.value=we(lt),n.value=we(at),o.value=we(ut)}return{top:e,right:t,bottom:n,left:o,update:l}}function we(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function Ro(e,t=f.noop,n={}){const{immediate:o=!0,manual:l=!1,type:r="text/javascript",async:s=!0,crossOrigin:a,referrerPolicy:i,noModule:c,defer:d,document:v=B,attrs:g={}}=n,p=u.ref(null);let y=null;const m=S=>new Promise((E,F)=>{const O=A=>(p.value=A,E(A),A);if(!v){E(!1);return}let P=!1,k=v.querySelector(`script[src="${f.toValue(e)}"]`);k?k.hasAttribute("data-loaded")&&O(k):(k=v.createElement("script"),k.type=r,k.async=s,k.src=f.toValue(e),d&&(k.defer=d),a&&(k.crossOrigin=a),c&&(k.noModule=c),i&&(k.referrerPolicy=i),Object.entries(g).forEach(([A,R])=>k?.setAttribute(A,R)),P=!0),k.addEventListener("error",A=>F(A)),k.addEventListener("abort",A=>F(A)),k.addEventListener("load",()=>{k.setAttribute("data-loaded","true"),t(k),O(k)}),P&&(k=v.head.appendChild(k)),S||O(k)}),b=(S=!0)=>(y||(y=m(S)),y),w=()=>{if(!v)return;y=null,p.value&&(p.value=null);const S=v.querySelector(`script[src="${f.toValue(e)}"]`);S&&v.head.removeChild(S)};return o&&!l&&f.tryOnMounted(b),l||f.tryOnUnmounted(w),{scriptTag:p,load:b,unload:w}}function st(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!n||n.tagName==="BODY"?!1:st(n)}}function Fo(e){const t=e||window.event,n=t.target;return st(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const be=new WeakMap;function Co(e,t=!1){const n=u.ref(t);let o=null,l;u.watch(f.toRef(e),a=>{const i=he(f.toValue(a));if(i){const c=i;be.get(c)||be.set(c,l),n.value&&(c.style.overflow="hidden")}},{immediate:!0});const r=()=>{const a=he(f.toValue(e));!a||n.value||(f.isIOS&&(o=_(a,"touchmove",i=>{Fo(i)},{passive:!1})),a.style.overflow="hidden",n.value=!0)},s=()=>{var a;const i=he(f.toValue(e));!i||!n.value||(f.isIOS&&o?.(),i.style.overflow=(a=be.get(i))!=null?a:"",be.delete(i),n.value=!1)};return f.tryOnScopeDispose(s),u.computed({get(){return n.value},set(a){a?r():s()}})}function Po(e,t,n={}){const{window:o=V}=n;return ye(e,t,o?.sessionStorage,n)}function Ao(e={},t={}){const{navigator:n=$}=t,o=n,l=x(()=>o&&"canShare"in o);return{isSupported:l,share:async(s={})=>{if(l.value){const a={...f.toValue(e),...f.toValue(s)};let i=!0;if(a.files&&o.canShare&&(i=o.canShare({files:a.files})),i)return o.share(a)}}}}const Vo=(e,t)=>e.sort(t),Se=(e,t)=>e-t;function Io(...e){var t,n,o,l;const[r]=e;let s=Se,a={};e.length===2?typeof e[1]=="object"?(a=e[1],s=(t=a.compareFn)!=null?t:Se):s=(n=e[1])!=null?n:Se:e.length>2&&(s=(o=e[1])!=null?o:Se,a=(l=e[2])!=null?l:{});const{dirty:i=!1,sortFn:c=Vo}=a;return i?(u.watchEffect(()=>{const d=c(f.toValue(r),s);u.isRef(r)?r.value=d:r.splice(0,r.length,...d)}),r):u.computed(()=>c([...f.toValue(r)],s))}function Mo(e={}){const{interimResults:t=!0,continuous:n=!0,window:o=V}=e,l=f.toRef(e.lang||"en-US"),r=u.ref(!1),s=u.ref(!1),a=u.ref(""),i=u.shallowRef(void 0),c=(m=!r.value)=>{r.value=m},d=()=>{r.value=!0},v=()=>{r.value=!1},g=o&&(o.SpeechRecognition||o.webkitSpeechRecognition),p=x(()=>g);let y;return p.value&&(y=new g,y.continuous=n,y.interimResults=t,y.lang=f.toValue(l),y.onstart=()=>{s.value=!1},u.watch(l,m=>{y&&!r.value&&(y.lang=m)}),y.onresult=m=>{const b=Array.from(m.results).map(w=>(s.value=w.isFinal,w[0])).map(w=>w.transcript).join("");a.value=b,i.value=void 0},y.onerror=m=>{i.value=m},y.onend=()=>{r.value=!1,y.lang=f.toValue(l)},u.watch(r,()=>{r.value?y.start():y.stop()})),f.tryOnScopeDispose(()=>{r.value=!1}),{isSupported:p,isListening:r,isFinal:s,recognition:y,result:a,error:i,toggle:c,start:d,stop:v}}function Lo(e,t={}){const{pitch:n=1,rate:o=1,volume:l=1,window:r=V}=t,s=r&&r.speechSynthesis,a=x(()=>s),i=u.ref(!1),c=u.ref("init"),d=f.toRef(e||""),v=f.toRef(t.lang||"en-US"),g=u.shallowRef(void 0),p=(S=!i.value)=>{i.value=S},y=S=>{S.lang=f.toValue(v),S.voice=f.toValue(t.voice)||null,S.pitch=f.toValue(n),S.rate=f.toValue(o),S.volume=l,S.onstart=()=>{i.value=!0,c.value="play"},S.onpause=()=>{i.value=!1,c.value="pause"},S.onresume=()=>{i.value=!0,c.value="play"},S.onend=()=>{i.value=!1,c.value="end"},S.onerror=E=>{g.value=E}},m=u.computed(()=>{i.value=!1,c.value="init";const S=new SpeechSynthesisUtterance(d.value);return y(S),S}),b=()=>{s.cancel(),m&&s.speak(m.value)},w=()=>{s.cancel(),i.value=!1};return a.value&&(y(m.value),u.watch(v,S=>{m.value&&!i.value&&(m.value.lang=S)}),t.voice&&u.watch(t.voice,()=>{s.cancel()}),u.watch(i,()=>{i.value?s.resume():s.pause()})),f.tryOnScopeDispose(()=>{i.value=!1}),{isSupported:a,isPlaying:i,status:c,utterance:m,error:g,stop:w,toggle:p,speak:b}}function No(e,t){const n=u.ref(e),o=u.computed(()=>Array.isArray(n.value)?n.value:Object.keys(n.value)),l=u.ref(o.value.indexOf(t??o.value[0])),r=u.computed(()=>d(l.value)),s=u.computed(()=>l.value===0),a=u.computed(()=>l.value===o.value.length-1),i=u.computed(()=>o.value[l.value+1]),c=u.computed(()=>o.value[l.value-1]);function d(O){return Array.isArray(n.value)?n.value[O]:n.value[o.value[O]]}function v(O){if(o.value.includes(O))return d(o.value.indexOf(O))}function g(O){o.value.includes(O)&&(l.value=o.value.indexOf(O))}function p(){a.value||l.value++}function y(){s.value||l.value--}function m(O){F(O)&&g(O)}function b(O){return o.value.indexOf(O)===l.value+1}function w(O){return o.value.indexOf(O)===l.value-1}function S(O){return o.value.indexOf(O)===l.value}function E(O){return l.value<o.value.indexOf(O)}function F(O){return l.value>o.value.indexOf(O)}return{steps:n,stepNames:o,index:l,current:r,next:i,previous:c,isFirst:s,isLast:a,at:d,get:v,goTo:g,goToNext:p,goToPrevious:y,goBackTo:m,isNext:b,isPrevious:w,isCurrent:S,isBefore:E,isAfter:F}}function xo(e,t,n,o={}){var l;const{flush:r="pre",deep:s=!0,listenToStorageChanges:a=!0,writeDefaults:i=!0,mergeDefaults:c=!1,shallow:d,window:v=V,eventFilter:g,onError:p=E=>{console.error(E)}}=o,y=f.toValue(t),m=We(y),b=(d?u.shallowRef:u.ref)(t),w=(l=o.serializer)!=null?l:Te[m];if(!n)try{n=pe("getDefaultStorageAsync",()=>{var E;return(E=V)==null?void 0:E.localStorage})()}catch(E){p(E)}async function S(E){if(!(!n||E&&E.key!==e))try{const F=E?E.newValue:await n.getItem(e);if(F==null)b.value=y,i&&y!==null&&await n.setItem(e,await w.write(y));else if(c){const O=await w.read(F);typeof c=="function"?b.value=c(O,y):m==="object"&&!Array.isArray(O)?b.value={...y,...O}:b.value=O}else b.value=await w.read(F)}catch(F){p(F)}}return S(),v&&a&&_(v,"storage",E=>Promise.resolve().then(()=>S(E))),n&&f.watchWithFilter(b,async()=>{try{b.value==null?await n.removeItem(e):await n.setItem(e,await w.write(b.value))}catch(E){p(E)}},{flush:r,deep:s,eventFilter:g}),b}let Wo=0;function Ho(e,t={}){const n=u.ref(!1),{document:o=B,immediate:l=!0,manual:r=!1,id:s=`vueuse_styletag_${++Wo}`}=t,a=u.ref(e);let i=()=>{};const c=()=>{if(!o)return;const v=o.getElementById(s)||o.createElement("style");v.isConnected||(v.id=s,t.media&&(v.media=t.media),o.head.appendChild(v)),!n.value&&(i=u.watch(a,g=>{v.textContent=g},{immediate:!0}),n.value=!0)},d=()=>{!o||!n.value||(i(),o.head.removeChild(o.getElementById(s)),n.value=!1)};return l&&!r&&f.tryOnMounted(c),r||f.tryOnScopeDispose(d),{id:s,css:a,unload:d,load:c,isLoaded:u.readonly(n)}}function Bo(e,t={}){const{threshold:n=50,onSwipe:o,onSwipeEnd:l,onSwipeStart:r,passive:s=!0,window:a=V}=t,i=u.reactive({x:0,y:0}),c=u.reactive({x:0,y:0}),d=u.computed(()=>i.x-c.x),v=u.computed(()=>i.y-c.y),{max:g,abs:p}=Math,y=u.computed(()=>g(p(d.value),p(v.value))>=n),m=u.ref(!1),b=u.computed(()=>y.value?p(d.value)>p(v.value)?d.value>0?"left":"right":v.value>0?"up":"down":"none"),w=R=>[R.touches[0].clientX,R.touches[0].clientY],S=(R,T)=>{i.x=R,i.y=T},E=(R,T)=>{c.x=R,c.y=T};let F;const O=Uo(a?.document);s?F=O?{passive:!0}:{capture:!1}:F=O?{passive:!1,capture:!0}:{capture:!0};const P=R=>{m.value&&l?.(R,b.value),m.value=!1},k=[_(e,"touchstart",R=>{if(R.touches.length!==1)return;F.capture&&!F.passive&&R.preventDefault();const[T,C]=w(R);S(T,C),E(T,C),r?.(R)},F),_(e,"touchmove",R=>{if(R.touches.length!==1)return;const[T,C]=w(R);E(T,C),!m.value&&y.value&&(m.value=!0),m.value&&o?.(R)},F),_(e,["touchend","touchcancel"],P,F)];return{isPassiveEventSupported:O,isSwiping:m,direction:b,coordsStart:i,coordsEnd:c,lengthX:d,lengthY:v,stop:()=>k.forEach(R=>R())}}function Uo(e){if(!e)return!1;let t=!1;const n={get passive(){return t=!0,!1}};return e.addEventListener("x",f.noop,n),e.removeEventListener("x",f.noop),t}function $o(){const e=u.ref([]);return e.value.set=t=>{t&&e.value.push(t)},u.onBeforeUpdate(()=>{e.value.length=0}),e}function jo(e={}){const{document:t=B,selector:n="html",observe:o=!1,initialValue:l="ltr"}=e;function r(){var a,i;return(i=(a=t?.querySelector(n))==null?void 0:a.getAttribute("dir"))!=null?i:l}const s=u.ref(r());return f.tryOnMounted(()=>s.value=r()),o&&t&&re(t.querySelector(n),()=>s.value=r(),{attributes:!0}),u.computed({get(){return s.value},set(a){var i,c;s.value=a,t&&(s.value?(i=t.querySelector(n))==null||i.setAttribute("dir",s.value):(c=t.querySelector(n))==null||c.removeAttribute("dir"))}})}function zo(e){var t;const n=(t=e.rangeCount)!=null?t:0;return Array.from({length:n},(o,l)=>e.getRangeAt(l))}function qo(e={}){const{window:t=V}=e,n=u.ref(null),o=u.computed(()=>{var a,i;return(i=(a=n.value)==null?void 0:a.toString())!=null?i:""}),l=u.computed(()=>n.value?zo(n.value):[]),r=u.computed(()=>l.value.map(a=>a.getBoundingClientRect()));function s(){n.value=null,t&&(n.value=t.getSelection())}return t&&_(t.document,"selectionchange",s),{text:o,rects:r,ranges:l,selection:n}}function Go(e){const t=u.ref(e?.element),n=u.ref(e?.input),o=u.ref(1);function l(){var r,s;if(!t.value)return;let a="";t.value.style.height="1px",o.value=(r=t.value)==null?void 0:r.scrollHeight,e?.styleTarget?f.toValue(e.styleTarget).style.height=`${o.value}px`:a=`${o.value}px`,t.value.style.height=a,(s=e?.onResize)==null||s.call(e)}return u.watch([n,t],()=>u.nextTick(l),{immediate:!0}),ge(t,()=>l()),e?.watch&&u.watch(e.watch,l,{immediate:!0,deep:!0}),{textarea:t,input:n,triggerResize:l}}function Yo(e,t={}){const{throttle:n=200,trailing:o=!0}=t,l=f.throttleFilter(n,o);return{..._e(e,{...t,eventFilter:l})}}const Xo=[{max:6e4,value:1e3,name:"second"},{max:276e4,value:6e4,name:"minute"},{max:72e6,value:36e5,name:"hour"},{max:5184e5,value:864e5,name:"day"},{max:24192e5,value:6048e5,name:"week"},{max:28512e6,value:2592e6,name:"month"},{max:Number.POSITIVE_INFINITY,value:31536e6,name:"year"}],Ko={justNow:"just now",past:e=>e.match(/\d/)?`${e} ago`:e,future:e=>e.match(/\d/)?`in ${e}`:e,month:(e,t)=>e===1?t?"last month":"next month":`${e} month${e>1?"s":""}`,year:(e,t)=>e===1?t?"last year":"next year":`${e} year${e>1?"s":""}`,day:(e,t)=>e===1?t?"yesterday":"tomorrow":`${e} day${e>1?"s":""}`,week:(e,t)=>e===1?t?"last week":"next week":`${e} week${e>1?"s":""}`,hour:e=>`${e} hour${e>1?"s":""}`,minute:e=>`${e} minute${e>1?"s":""}`,second:e=>`${e} second${e>1?"s":""}`,invalid:""};function Jo(e){return e.toISOString().slice(0,10)}function Qo(e,t={}){const{controls:n=!1,updateInterval:o=3e4}=t,{now:l,...r}=tt({interval:o,controls:!0}),s=u.computed(()=>it(new Date(f.toValue(e)),t,f.toValue(l)));return n?{timeAgo:s,...r}:s}function it(e,t={},n=Date.now()){var o;const{max:l,messages:r=Ko,fullDateFormatter:s=Jo,units:a=Xo,showSecond:i=!1,rounding:c="round"}=t,d=typeof c=="number"?b=>+b.toFixed(c):Math[c],v=+n-+e,g=Math.abs(v);function p(b,w){return d(Math.abs(b)/w.value)}function y(b,w){const S=p(b,w),E=b>0,F=m(w.name,S,E);return m(E?"past":"future",F,E)}function m(b,w,S){const E=r[b];return typeof E=="function"?E(w,S):E.replace("{0}",w.toString())}if(g<6e4&&!i)return r.justNow;if(typeof l=="number"&&g>l)return s(new Date(e));if(typeof l=="string"){const b=(o=a.find(w=>w.name===l))==null?void 0:o.max;if(b&&g>b)return s(new Date(e))}for(const[b,w]of a.entries()){if(p(v,w)<=0&&a[b-1])return y(v,a[b-1]);if(g<w.max)return y(v,w)}return r.invalid}function Zo(e,t,n){const{start:o}=f.useTimeoutFn(r,t,{immediate:!1}),l=u.ref(!1);async function r(){l.value&&(await e(),o())}function s(){l.value||(l.value=!0,r())}function a(){l.value=!1}return n?.immediate&&s(),f.tryOnScopeDispose(a),{isActive:l,pause:a,resume:s}}function Do(e={}){const{controls:t=!1,offset:n=0,immediate:o=!0,interval:l="requestAnimationFrame",callback:r}=e,s=u.ref(f.timestamp()+n),a=()=>s.value=f.timestamp()+n,i=r?()=>{a(),r(s.value)}:a,c=l==="requestAnimationFrame"?Z(i,{immediate:o}):f.useIntervalFn(i,l,{immediate:o});return t?{timestamp:s,...c}:s}function er(e=null,t={}){var n,o,l;const{document:r=B,restoreOnUnmount:s=v=>v}=t,a=(n=r?.title)!=null?n:"",i=f.toRef((o=e??r?.title)!=null?o:null),c=e&&typeof e=="function";function d(v){if(!("titleTemplate"in t))return v;const g=t.titleTemplate||"%s";return typeof g=="function"?g(v):f.toValue(g).replace(/%s/g,v)}return u.watch(i,(v,g)=>{v!==g&&r&&(r.title=d(typeof v=="string"?v:""))},{immediate:!0}),t.observe&&!t.titleTemplate&&r&&!c&&re((l=r.head)==null?void 0:l.querySelector("title"),()=>{r&&r.title!==i.value&&(i.value=d(r.title))},{childList:!0}),f.tryOnBeforeUnmount(()=>{if(s){const v=s(a,i.value||"");v!=null&&r&&(r.title=v)}}),i}const tr={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},nr=Object.assign({},{linear:f.identity},tr);function or([e,t,n,o]){const l=(d,v)=>1-3*v+3*d,r=(d,v)=>3*v-6*d,s=d=>3*d,a=(d,v,g)=>((l(v,g)*d+r(v,g))*d+s(v))*d,i=(d,v,g)=>3*l(v,g)*d*d+2*r(v,g)*d+s(v),c=d=>{let v=d;for(let g=0;g<4;++g){const p=i(v,e,n);if(p===0)return v;const y=a(v,e,n)-d;v-=y/p}return v};return d=>e===t&&n===o?d:a(c(d),t,o)}function ct(e,t,n){return e+n*(t-e)}function Ce(e){return(typeof e=="number"?[e]:e)||[]}function ft(e,t,n,o={}){var l,r;const s=f.toValue(t),a=f.toValue(n),i=Ce(s),c=Ce(a),d=(l=f.toValue(o.duration))!=null?l:1e3,v=Date.now(),g=Date.now()+d,p=typeof o.transition=="function"?o.transition:(r=f.toValue(o.transition))!=null?r:f.identity,y=typeof p=="function"?p:or(p);return new Promise(m=>{e.value=s;const b=()=>{var w;if((w=o.abort)!=null&&w.call(o)){m();return}const S=Date.now(),E=y((S-v)/d),F=Ce(e.value).map((O,P)=>ct(i[P],c[P],E));Array.isArray(e.value)?e.value=F.map((O,P)=>{var k,A;return ct((k=i[P])!=null?k:0,(A=c[P])!=null?A:0,E)}):typeof e.value=="number"&&(e.value=F[0]),S<g?requestAnimationFrame(b):(e.value=a,m())};b()})}function rr(e,t={}){let n=0;const o=()=>{const r=f.toValue(e);return typeof r=="number"?r:r.map(f.toValue)},l=u.ref(o());return u.watch(o,async r=>{var s,a;if(f.toValue(t.disabled))return;const i=++n;if(t.delay&&await f.promiseTimeout(f.toValue(t.delay)),i!==n)return;const c=Array.isArray(r)?r.map(f.toValue):f.toValue(r);(s=t.onStarted)==null||s.call(t),await ft(l,l.value,c,{...t,abort:()=>{var d;return i!==n||((d=t.abort)==null?void 0:d.call(t))}}),(a=t.onFinished)==null||a.call(t)},{deep:!0}),u.watch(()=>f.toValue(t.disabled),r=>{r&&(n++,l.value=o())}),f.tryOnScopeDispose(()=>{n++}),u.computed(()=>f.toValue(t.disabled)?o():l.value)}function lr(e="history",t={}){const{initialValue:n={},removeNullishValues:o=!0,removeFalsyValues:l=!1,write:r=!0,window:s=V}=t;if(!s)return u.reactive(n);const a=u.reactive({});function i(){if(e==="history")return s.location.search||"";if(e==="hash"){const w=s.location.hash||"",S=w.indexOf("?");return S>0?w.slice(S):""}else return(s.location.hash||"").replace(/^#/,"")}function c(w){const S=w.toString();if(e==="history")return`${S?`?${S}`:""}${s.location.hash||""}`;if(e==="hash-params")return`${s.location.search||""}${S?`#${S}`:""}`;const E=s.location.hash||"#",F=E.indexOf("?");return F>0?`${E.slice(0,F)}${S?`?${S}`:""}`:`${E}${S?`?${S}`:""}`}function d(){return new URLSearchParams(i())}function v(w){const S=new Set(Object.keys(a));for(const E of w.keys()){const F=w.getAll(E);a[E]=F.length>1?F:w.get(E)||"",S.delete(E)}Array.from(S).forEach(E=>delete a[E])}const{pause:g,resume:p}=f.pausableWatch(a,()=>{const w=new URLSearchParams("");Object.keys(a).forEach(S=>{const E=a[S];Array.isArray(E)?E.forEach(F=>w.append(S,F)):o&&E==null||l&&!E?w.delete(S):w.set(S,E)}),y(w)},{deep:!0});function y(w,S){g(),S&&v(w),s.history.replaceState(s.history.state,s.document.title,s.location.pathname+c(w)),p()}function m(){r&&y(d(),!0)}_(s,"popstate",m,!1),e!=="history"&&_(s,"hashchange",m,!1);const b=d();return b.keys().next().value?v(b):Object.assign(a,n),a}function ar(e={}){var t,n;const o=u.ref((t=e.enabled)!=null?t:!1),l=u.ref((n=e.autoSwitch)!=null?n:!0),r=u.ref(e.constraints),{navigator:s=$}=e,a=x(()=>{var m;return(m=s?.mediaDevices)==null?void 0:m.getUserMedia}),i=u.shallowRef();function c(m){switch(m){case"video":{if(r.value)return r.value.video||!1;break}case"audio":{if(r.value)return r.value.audio||!1;break}}}async function d(){if(!(!a.value||i.value))return i.value=await s.mediaDevices.getUserMedia({video:c("video"),audio:c("audio")}),i.value}function v(){var m;(m=i.value)==null||m.getTracks().forEach(b=>b.stop()),i.value=void 0}function g(){v(),o.value=!1}async function p(){return await d(),i.value&&(o.value=!0),i.value}async function y(){return v(),await p()}return u.watch(o,m=>{m?d():v()},{immediate:!0}),u.watch(r,()=>{l.value&&i.value&&y()},{immediate:!0}),{isSupported:a,stream:i,start:p,stop:g,restart:y,constraints:r,enabled:o,autoSwitch:l}}function dt(e,t,n,o={}){var l,r,s,a,i;const{clone:c=!1,passive:d=!1,eventName:v,deep:g=!1,defaultValue:p,shouldEmit:y}=o,m=u.getCurrentInstance(),b=n||m?.emit||((l=m?.$emit)==null?void 0:l.bind(m))||((s=(r=m?.proxy)==null?void 0:r.$emit)==null?void 0:s.bind(m?.proxy));let w=v;if(!t)if(u.isVue2){const O=(i=(a=m?.proxy)==null?void 0:a.$options)==null?void 0:i.model;t=O?.value||"value",v||(w=O?.event||"input")}else t="modelValue";w=w||`update:${t.toString()}`;const S=O=>c?typeof c=="function"?c(O):oe(O):O,E=()=>f.isDef(e[t])?S(e[t]):p,F=O=>{y?y(O)&&b(w,O):b(w,O)};if(d){const O=E(),P=u.ref(O);let k=!1;return u.watch(()=>e[t],A=>{k||(k=!0,P.value=S(A),u.nextTick(()=>k=!1))}),u.watch(P,A=>{!k&&(A!==e[t]||g)&&F(A)},{deep:g}),P}else return u.computed({get(){return E()},set(O){F(O)}})}function ur(e,t,n={}){const o={};for(const l in e)o[l]=dt(e,l,t,n);return o}function sr(e){const{pattern:t=[],interval:n=0,navigator:o=$}=e||{},l=x(()=>typeof o<"u"&&"vibrate"in o),r=f.toRef(t);let s;const a=(c=r.value)=>{l.value&&o.vibrate(c)},i=()=>{l.value&&o.vibrate(0),s?.pause()};return n>0&&(s=f.useIntervalFn(a,n,{immediate:!1,immediateCallback:!1})),{isSupported:l,pattern:t,intervalControls:s,vibrate:a,stop:i}}function ir(e,t){const{containerStyle:n,wrapperProps:o,scrollTo:l,calculateRange:r,currentList:s,containerRef:a}="itemHeight"in t?dr(t,e):fr(t,e);return{list:s,scrollTo:l,containerProps:{ref:a,onScroll:()=>{r()},style:n},wrapperProps:o}}function vt(e){const t=u.ref(null),n=ze(t),o=u.ref([]),l=u.shallowRef(e);return{state:u.ref({start:0,end:10}),source:l,currentList:o,size:n,containerRef:t}}function pt(e,t,n){return o=>{if(typeof n=="number")return Math.ceil(o/n);const{start:l=0}=e.value;let r=0,s=0;for(let a=l;a<t.value.length;a++){const i=n(a);if(r+=i,s=a,r>o)break}return s-l}}function yt(e,t){return n=>{if(typeof t=="number")return Math.floor(n/t)+1;let o=0,l=0;for(let r=0;r<e.value.length;r++){const s=t(r);if(o+=s,o>=n){l=r;break}}return l+1}}function gt(e,t,n,o,{containerRef:l,state:r,currentList:s,source:a}){return()=>{const i=l.value;if(i){const c=n(e==="vertical"?i.scrollTop:i.scrollLeft),d=o(e==="vertical"?i.clientHeight:i.clientWidth),v=c-t,g=c+d+t;r.value={start:v<0?0:v,end:g>a.value.length?a.value.length:g},s.value=a.value.slice(r.value.start,r.value.end).map((p,y)=>({data:p,index:y+r.value.start}))}}}function mt(e,t){return n=>typeof e=="number"?n*e:t.value.slice(0,n).reduce((l,r,s)=>l+e(s),0)}function ht(e,t,n){u.watch([e.width,e.height,t],()=>{n()})}function wt(e,t){return u.computed(()=>typeof e=="number"?t.value.length*e:t.value.reduce((n,o,l)=>n+e(l),0))}const cr={horizontal:"scrollLeft",vertical:"scrollTop"};function bt(e,t,n,o){return l=>{o.value&&(o.value[cr[e]]=n(l),t())}}function fr(e,t){const n=vt(t),{state:o,source:l,currentList:r,size:s,containerRef:a}=n,i={overflowX:"auto"},{itemWidth:c,overscan:d=5}=e,v=pt(o,l,c),g=yt(l,c),p=gt("horizontal",d,g,v,n),y=mt(c,l),m=u.computed(()=>y(o.value.start)),b=wt(c,l);ht(s,t,p);const w=bt("horizontal",p,y,a),S=u.computed(()=>({style:{height:"100%",width:`${b.value-m.value}px`,marginLeft:`${m.value}px`,display:"flex"}}));return{scrollTo:w,calculateRange:p,wrapperProps:S,containerStyle:i,currentList:r,containerRef:a}}function dr(e,t){const n=vt(t),{state:o,source:l,currentList:r,size:s,containerRef:a}=n,i={overflowY:"auto"},{itemHeight:c,overscan:d=5}=e,v=pt(o,l,c),g=yt(l,c),p=gt("vertical",d,g,v,n),y=mt(c,l),m=u.computed(()=>y(o.value.start)),b=wt(c,l);ht(s,t,p);const w=bt("vertical",p,y,a),S=u.computed(()=>({style:{width:"100%",height:`${b.value-m.value}px`,marginTop:`${m.value}px`}}));return{calculateRange:p,scrollTo:w,containerStyle:i,wrapperProps:S,currentList:r,containerRef:a}}function vr(e={}){const{navigator:t=$,document:n=B}=e;let o;const l=x(()=>t&&"wakeLock"in t),r=u.ref(!1);async function s(){!l.value||!o||(n&&n.visibilityState==="visible"&&(o=await t.wakeLock.request("screen")),r.value=!o.released)}n&&_(n,"visibilitychange",s,{passive:!0});async function a(c){l.value&&(o=await t.wakeLock.request(c),r.value=!o.released)}async function i(){!l.value||!o||(await o.release(),r.value=!o.released,o=null)}return{isSupported:l,isActive:r,request:a,release:i}}function pr(e={}){const{window:t=V,requestPermissions:n=!0}=e,o=e,l=x(()=>!!t&&"Notification"in t),r=u.ref(l.value&&"permission"in Notification&&Notification.permission==="granted"),s=u.ref(null),a=async()=>{if(l.value)return!r.value&&Notification.permission!=="denied"&&await Notification.requestPermission()==="granted"&&(r.value=!0),r.value},{on:i,trigger:c}=f.createEventHook(),{on:d,trigger:v}=f.createEventHook(),{on:g,trigger:p}=f.createEventHook(),{on:y,trigger:m}=f.createEventHook(),b=async S=>{if(!l.value||!r.value)return;const E=Object.assign({},o,S);return s.value=new Notification(E.title||"",E),s.value.onclick=c,s.value.onshow=v,s.value.onerror=p,s.value.onclose=m,s.value},w=()=>{s.value&&s.value.close(),s.value=null};if(n&&f.tryOnMounted(a),f.tryOnScopeDispose(w),l.value&&t){const S=t.document;_(S,"visibilitychange",E=>{E.preventDefault(),S.visibilityState==="visible"&&w()})}return{isSupported:l,notification:s,ensurePermissions:a,permissionGranted:r,show:b,close:w,onClick:i,onShow:d,onError:g,onClose:y}}const St="ping";function Pe(e){return e===!0?{}:e}function yr(e,t={}){const{onConnected:n,onDisconnected:o,onError:l,onMessage:r,immediate:s=!0,autoClose:a=!0,protocols:i=[]}=t,c=u.ref(null),d=u.ref("CLOSED"),v=u.ref(),g=f.toRef(e);let p,y,m=!1,b=0,w=[],S;const E=()=>{if(w.length&&v.value&&d.value==="OPEN"){for(const R of w)v.value.send(R);w=[]}},F=()=>{clearTimeout(S),S=void 0},O=(R=1e3,T)=>{!f.isClient||!v.value||(m=!0,F(),p?.(),v.value.close(R,T))},P=(R,T=!0)=>!v.value||d.value!=="OPEN"?(T&&w.push(R),!1):(E(),v.value.send(R),!0),k=()=>{if(m||typeof g.value>"u")return;const R=new WebSocket(g.value,i);v.value=R,d.value="CONNECTING",R.onopen=()=>{d.value="OPEN",n?.(R),y?.(),E()},R.onclose=T=>{if(d.value="CLOSED",v.value=void 0,o?.(R,T),!m&&t.autoReconnect){const{retries:C=-1,delay:M=1e3,onFailed:N}=Pe(t.autoReconnect);b+=1,typeof C=="number"&&(C<0||b<C)||typeof C=="function"&&C()?setTimeout(k,M):N?.()}},R.onerror=T=>{l?.(R,T)},R.onmessage=T=>{if(t.heartbeat){F();const{message:C=St}=Pe(t.heartbeat);if(T.data===C)return}c.value=T.data,r?.(R,T)}};if(t.heartbeat){const{message:R=St,interval:T=1e3,pongTimeout:C=1e3}=Pe(t.heartbeat),{pause:M,resume:N}=f.useIntervalFn(()=>{P(R,!1),S==null&&(S=setTimeout(()=>{O(),m=!1},C))},T,{immediate:!1});p=M,y=N}a&&(f.isClient&&_("beforeunload",()=>O()),f.tryOnScopeDispose(O));const A=()=>{!f.isClient&&!f.isWorker||(O(),m=!1,b=0,k())};return s&&u.watch(g,A,{immediate:!0}),{data:c,status:d,close:O,send:P,open:A,ws:v}}function gr(e,t,n){const{window:o=V}=n??{},l=u.ref(null),r=u.shallowRef(),s=(...i)=>{r.value&&r.value.postMessage(...i)},a=function(){r.value&&r.value.terminate()};return o&&(typeof e=="string"?r.value=new Worker(e,t):typeof e=="function"?r.value=e():r.value=e,r.value.onmessage=i=>{l.value=i.data},f.tryOnScopeDispose(()=>{r.value&&r.value.terminate()})),{data:l,post:s,terminate:a,worker:r}}function mr(e){return t=>{const n=t.data[0];return Promise.resolve(e.apply(void 0,n)).then(o=>{postMessage(["SUCCESS",o])}).catch(o=>{postMessage(["ERROR",o])})}}function hr(e){return e.length===0?"":`importScripts(${e.map(n=>`'${n}'`).toString()})`}function wr(e,t){const n=`${hr(t)}; onmessage=(${mr})(${e})`,o=new Blob([n],{type:"text/javascript"});return URL.createObjectURL(o)}function br(e,t={}){const{dependencies:n=[],timeout:o,window:l=V}=t,r=u.ref(),s=u.ref("PENDING"),a=u.ref({}),i=u.ref(),c=(p="PENDING")=>{r.value&&r.value._url&&l&&(r.value.terminate(),URL.revokeObjectURL(r.value._url),a.value={},r.value=void 0,l.clearTimeout(i.value),s.value=p)};c(),f.tryOnScopeDispose(c);const d=()=>{const p=wr(e,n),y=new Worker(p);return y._url=p,y.onmessage=m=>{const{resolve:b=()=>{},reject:w=()=>{}}=a.value,[S,E]=m.data;switch(S){case"SUCCESS":b(E),c(S);break;default:w(E),c("ERROR");break}},y.onerror=m=>{const{reject:b=()=>{}}=a.value;m.preventDefault(),b(m),c("ERROR")},o&&(i.value=setTimeout(()=>c("TIMEOUT_EXPIRED"),o)),y},v=(...p)=>new Promise((y,m)=>{a.value={resolve:y,reject:m},r.value&&r.value.postMessage([[...p]]),s.value="RUNNING"});return{workerFn:(...p)=>s.value==="RUNNING"?(console.error("[useWebWorkerFn] You can only run one instance of the worker at a time."),Promise.reject()):(r.value=d(),v(...p)),workerStatus:s,workerTerminate:c}}function Sr(e={}){const{window:t=V}=e;if(!t)return u.ref(!1);const n=u.ref(t.document.hasFocus());return _(t,"blur",()=>{n.value=!1}),_(t,"focus",()=>{n.value=!0}),n}function Er(e={}){const{window:t=V,behavior:n="auto"}=e;if(!t)return{x:u.ref(0),y:u.ref(0)};const o=u.ref(t.scrollX),l=u.ref(t.scrollY),r=u.computed({get(){return o.value},set(a){scrollTo({left:a,behavior:n})}}),s=u.computed({get(){return l.value},set(a){scrollTo({top:a,behavior:n})}});return _(t,"scroll",()=>{o.value=t.scrollX,l.value=t.scrollY},{capture:!1,passive:!0}),{x:r,y:s}}function Tr(e={}){const{window:t=V,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:o=Number.POSITIVE_INFINITY,listenOrientation:l=!0,includeScrollbar:r=!0}=e,s=u.ref(n),a=u.ref(o),i=()=>{t&&(r?(s.value=t.innerWidth,a.value=t.innerHeight):(s.value=t.document.documentElement.clientWidth,a.value=t.document.documentElement.clientHeight))};if(i(),f.tryOnMounted(i),_("resize",i,{passive:!0}),l){const c=G("(orientation: portrait)");u.watch(c,()=>i())}return{width:s,height:a}}h.DefaultMagicKeysAliasMap=Qe,h.StorageSerializers=Te,h.TransitionPresets=nr,h.asyncComputed=Y,h.breakpointsAntDesign=Yt,h.breakpointsBootstrapV5=qt,h.breakpointsMasterCss=Jt,h.breakpointsPrimeFlex=Qt,h.breakpointsQuasar=Xt,h.breakpointsSematic=Kt,h.breakpointsTailwind=zt,h.breakpointsVuetify=Gt,h.cloneFnJSON=oe,h.computedAsync=Y,h.computedInject=Ae,h.createFetch=An,h.createReusableTemplate=q,h.createTemplatePromise=J,h.createUnrefFn=K,h.customStorageEventName=Oe,h.defaultDocument=B,h.defaultLocation=Tt,h.defaultNavigator=$,h.defaultWindow=V,h.executeTransition=ft,h.formatTimeAgo=it,h.getSSRHandler=pe,h.mapGamepadToXbox360Controller=Bn,h.onClickOutside=Ot,h.onKeyDown=_t,h.onKeyPressed=Rt,h.onKeyStroke=ce,h.onKeyUp=Ft,h.onLongPress=At,h.onStartTyping=Mt,h.setSSRHandler=an,h.templateRef=Lt,h.unrefElement=L,h.useActiveElement=Ie,h.useAnimate=Nt,h.useAsyncQueue=xt,h.useAsyncState=Le,h.useBase64=Bt,h.useBattery=$t,h.useBluetooth=jt,h.useBreakpoints=Zt,h.useBroadcastChannel=Dt,h.useBrowserLocation=en,h.useCached=tn,h.useClipboard=nn,h.useClipboardItems=on,h.useCloned=rn,h.useColorMode=He,h.useConfirmDialog=un,h.useCssVar=le,h.useCurrentElement=Be,h.useCycleList=sn,h.useDark=cn,h.useDebouncedRefHistory=pn,h.useDeviceMotion=yn,h.useDeviceOrientation=je,h.useDevicePixelRatio=gn,h.useDevicesList=mn,h.useDisplayMedia=hn,h.useDocumentVisibility=wn,h.useDraggable=bn,h.useDropZone=Sn,h.useElementBounding=En,h.useElementByPoint=Tn,h.useElementHover=On,h.useElementSize=ze,h.useElementVisibility=Ge,h.useEventBus=kn,h.useEventListener=_,h.useEventSource=_n,h.useEyeDropper=Rn,h.useFavicon=Fn,h.useFetch=Ye,h.useFileDialog=Mn,h.useFileSystemAccess=Ln,h.useFocus=Nn,h.useFocusWithin=xn,h.useFps=Wn,h.useFullscreen=Hn,h.useGamepad=Un,h.useGeolocation=$n,h.useIdle=qn,h.useImage=Yn,h.useInfiniteScroll=Xn,h.useIntersectionObserver=qe,h.useKeyModifier=Jn,h.useLocalStorage=Qn,h.useMagicKeys=Zn,h.useManualRefHistory=$e,h.useMediaControls=to,h.useMediaQuery=G,h.useMemoize=oo,h.useMemory=ro,h.useMounted=Me,h.useMouse=Ze,h.useMouseInElement=De,h.useMousePressed=ao,h.useMutationObserver=re,h.useNavigatorLanguage=uo,h.useNetwork=et,h.useNow=tt,h.useObjectUrl=so,h.useOffsetPagination=io,h.useOnline=co,h.usePageLeave=fo,h.useParallax=vo,h.useParentElement=po,h.usePerformanceObserver=yo,h.usePermission=fe,h.usePointer=mo,h.usePointerLock=ho,h.usePointerSwipe=wo,h.usePreferredColorScheme=bo,h.usePreferredContrast=So,h.usePreferredDark=ke,h.usePreferredLanguages=Eo,h.usePreferredReducedMotion=To,h.usePrevious=Oo,h.useRafFn=Z,h.useRefHistory=_e,h.useResizeObserver=ge,h.useScreenOrientation=ko,h.useScreenSafeArea=_o,h.useScriptTag=Ro,h.useScroll=Je,h.useScrollLock=Co,h.useSessionStorage=Po,h.useShare=Ao,h.useSorted=Io,h.useSpeechRecognition=Mo,h.useSpeechSynthesis=Lo,h.useStepper=No,h.useStorage=ye,h.useStorageAsync=xo,h.useStyleTag=Ho,h.useSupported=x,h.useSwipe=Bo,h.useTemplateRefsList=$o,h.useTextDirection=jo,h.useTextSelection=qo,h.useTextareaAutosize=Go,h.useThrottledRefHistory=Yo,h.useTimeAgo=Qo,h.useTimeoutPoll=Zo,h.useTimestamp=Do,h.useTitle=er,h.useTransition=rr,h.useUrlSearchParams=lr,h.useUserMedia=ar,h.useVModel=dt,h.useVModels=ur,h.useVibrate=sr,h.useVirtualList=ir,h.useWakeLock=vr,h.useWebNotification=pr,h.useWebSocket=yr,h.useWebWorker=gr,h.useWebWorkerFn=br,h.useWindowFocus=Sr,h.useWindowScroll=Er,h.useWindowSize=Tr,Object.keys(f).forEach(function(e){e!=="default"&&!Object.prototype.hasOwnProperty.call(h,e)&&Object.defineProperty(h,e,{enumerable:!0,get:function(){return f[e]}})})})(this.VueUse=this.VueUse||{},VueUse,VueDemi);
