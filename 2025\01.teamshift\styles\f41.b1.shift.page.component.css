.vxe-table--body .vxe-cell {
  padding: 0 !important;
}

@keyframes blink-bg {
  0% {
    background-color: rgb(220 38 38);
  }
  50% {
    background-color: rgb(248 113 113);
  }
  100% {
    background-color: rgb(220 38 38);
  }
}
.blink-bg {
  animation: blink-bg 1.5s infinite;
}

@keyframes vg-blink-bg {
  0% {
    background-color: gray;
  }
  50% {
    background-color: #f0f0f0;
  }
  100% {
    background-color: gray;
  }
}
/* 磨削闪烁 */
.vg-blink-bg {
  animation: vg-blink-bg 3000ms infinite;
}

.title {
  font-weight: bold;
  font-size: 14px;
  display: block;
  margin: 20px;
  text-align: center;
}

.demo-form-inline {
  margin: 10px 20px 0;
}

.plTableBox {
  padding: 0 20px;
  box-sizing: border-box;
}

.baseitem {
  text-align: center;
  height: 100%;
  font-size: 14px;
  line-height: 2;
  cursor: pointer;
}

.Isactive {
  background: #409eff;
  color: #ffffff;
  text-align: center;
  font-size: 13px;
  line-height: 30px;
}

.IsDis {
  background-color: #f0f0f4;
  color: #5b5b66;
  cursor: not-allowed;
  pointer-events: none;
  font-size: 13px;
}

.dragColbg {
  background-color: #409eff;
}

.sortable-column-demo .plx-header--row .plx-header--column.col--fixed {
  cursor: no-drop;
}

::-webkit-scrollbar {
  width: 9px;
  height: 9px;
  background-color: hsla(0, 0%, 100%, 0);
}

::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(144, 147, 153, 0.5);
}

.tabheadbt {
  color: #409eff;
  font-size: 14px;
  float: right;
  line-height: 27px;
  display: block;
  position: absolute;
  top: 12px;
  right: 5px;
}

.t_left {
  text-align: left;
}

.headtitle {
  line-height: 1;
}

.headtitle span {
  display: inline-block;
}

.el-input--mini .el-input__inner {
  min-width: 120px !important;
  width: 120px !important;
}

.headtitlenew {
  display: inline-block;
}

.w60 {
  width: 60px;
}

.w120 {
  width: 120px;
}
.summaryclass .vxe-header--row th {
  max-height: 10px !important;
}
.summaryclass .vxe-cell {
  line-height: 0 !important;
}
.summaryclass .vxe-body--row .vxe-cell {
  min-height: 90px !important;
}

.taskitem-new {
  width: 130px;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border: 1px solid #e8eaec;
  text-align: center;
  color: #666;
  font-size: 12px;
  margin: 3px auto;
  cursor: pointer;
  position: relative;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  padding: 0 3px;
}
.taskitem {
  width: 160px;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border: 1px solid #e8eaec;
  text-align: center;
  color: #666;
  font-size: 12px;
  margin: 3px auto;
  cursor: pointer;
  position: relative;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  padding: 0 3px;
}

.taskitem .taskname {
  display: inline-block;
  width: 35%;
  /* border-right: 2px solid #ffffff; */
  float: left;
  text-align: left;
}

.taskitem .right {
  display: inline-block;
  width: 40%;
  border-left: 2px solid #ffffff;
  float: right;
}

.boxBottom {
  position: absolute;
  bottom: 1px;
  left: 0;
  height: 30px;
  width: 100%;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.boxBottom .span_btn:nth-child(1) {
  border-right: 1px solid #e8e8e8;
}

.boxBottom .span_btn:nth-child(2) {
  border-right: 1px solid #e8e8e8;
}

.boxBottom .span_btn:nth-child(3) {
  border-right: 1px solid #e8e8e8;
}

.boxBottom .span {
  display: flex;
  width: 33%;
  height: 32px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.boxBottom .span i {
  display: block;
  width: 12px;
  height: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.boxBottom .span:nth-child(1) {
  border-right: 1px solid #e8e8e8;
}

.boxBottom .span:nth-child(2) {
  border-right: 1px solid #e8e8e8;
}

.boxBottom .span:nth-child(3) {
  border-right: 1px solid #e8e8e8;
}

.popover {
  padding: 0;
}

.popover li {
  height: 30px;
  line-height: 30px;
  color: #1890ff;
  text-align: center;
  cursor: pointer;
  list-style: none;
}

.addbox {
  /* flex-wrap: wrap;
    align-content:flex-start;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center; */
}

.thbox {
  width: 100%;
  text-align: center;
  overflow: auto;
  max-height: 135px;
}

.el-popover {
  min-width: 60px;
  font-size: 12px;
  padding: 8px 12px;
}

.wrapbox {
  flex-wrap: wrap;
  align-content: flex-start;
  /* display: flex; */
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.bgInfo {
  background: #fff;
}

.bgSuccess {
  background: #52c41a;
}

.bgSuccess .taskitem {
  border-color: #ffffff;
  color: #ffffff;
}

.bgWarning {
  background: #fa8c16;
}

.bgWarning .taskitem {
  border-color: #ffffff;
  color: #ffffff;
}

.bgDanger {
  background: #f5222d;
}

.bgDanger .taskitem {
  border-color: #ffffff;
  color: #ffffff;
}

.bgDangerop {
  background: #ffccc7;
  border-color: #ffffff;
  color: #cf1322;
  font-weight: 700;
}

.bgblue {
  background: #1890ff;
}

.bgblue .taskitem {
  border-color: #ffffff;
  color: #ffffff;
}

.bginfoblue {
  background: #c6e2ff;
}
.bginfoblue0 {
  background: #c6e2ff;
}
.bginfoblue1 {
  background: #c6e2ff;
}

.vxe-header--column {
  border-right: 0.5px solid #fff;
  border-bottom: 0.5px solid #fff;
  color: #ffffff;
  background: #5d7092 !important;
  border-right-color: #ffffff;
  /* border: 1px solid #fff; */
}

.bgPurple .newheadtitle {
  /* margin-top: 30px; */
  padding: 15px 0;
  line-height: 0;
  background: #e74c3c;
  animation: bg-color 3s infinite;
  animate-delay: 3s;
  -webkit-animation: bg-color 3s infinite;
  overflow: hidden;
}

@-webkit-keyframes bg-color {
  0% {
    background-color: #e74c3c;
    color: #ffffff !important;
  }

  50% {
    background-color: #ffffff;
    color: #000000 !important;
  }

  100% {
    background-color: #e74c3c;
    color: #ffffff !important;
  }
}

@keyframes bg-color {
  0% {
    background-color: #e74c3c;
    color: #ffffff !important;
  }

  50% {
    background-color: #ffffff;
    color: #000000 !important;
  }

  100% {
    background-color: #e74c3c;
    color: #ffffff !important;
  }
}

.bg-weekend {
  background: #f5222d !important;
}

.bghead {
  background: #303133;
}

.omit_line {
  height: 28px !important;
}

.circle-edit {
  position: absolute;
  top: 8px;
  right: 8px;
}

.self_icon_color_default {
  color: #409eff;
}

@keyframes fade {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }

  to {
    opacity: 1;
  }
}

@-webkit-keyframes fade {
  from {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  to {
    opacity: 1;
  }
}

.header-cell {
  background: #999999 !important;
  color: #fff;
  padding: 10px;
  /*font-size: 15px;*/
  /*height: 60px;*/
  animation: fade 3000ms infinite;
  -webkit-animation: fade 3000ms infinite;
}
.header_row {
  background-color: #5d7092 !important;
  color: #ffffff;
}
.header_red {
  background-color: red !important;
  color: #ffffff;
}
.border-red-first {
  border-collapse: collapse;
  border-left: 2px solid crimson;
  border-top: 1px solid crimson;
  border-bottom: 1px solid crimson;
}
.border-red-other {
  border-collapse: collapse;
  border-top: 1px solid crimson;
  border-bottom: 1px solid crimson;
}
.border-red-end {
  border-collapse: collapse;
  border-right: 2px solid crimson;
  border-top: 1px solid crimson;
  border-bottom: 1px solid crimson;
}
.border-red-all {
  border-collapse: collapse;
  border: 2px solid crimson;
}