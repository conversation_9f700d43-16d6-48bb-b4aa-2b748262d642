import { post } from '../../../../config/axios/httpReuest.js'

export const getWhiteToYellowData = (idWo, type) => {
  const params = {
    ac: 'de_pendingpnlist',
    id_wo: idWo,
    int_point_type: type,
  }
  return post(params)
}

export const getRedFramePartData = (searchParams) => {
  const params = {
    ac: 'de_getpnlist_bygroup',
    id: '',
    id_wo: searchParams.idWo,
    int_ekd_type: 0,
    int_point_type: searchParams.type,
    filter_fields: [],
  }
  return post(params)
}
