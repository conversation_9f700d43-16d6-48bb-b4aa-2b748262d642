const { ref, reactive, onMounted, onBeforeUnmount, computed } = Vue
const { useIntervalFn } = VueUse

export default {
  name: 'WhiteToYellowTable',
  props: {
    type: {
      type: String,
      required: true,
      validator: (value) => ['pending', 'converted', 'scanning'].includes(value)
    },
    autoScroll: {
      type: Boolean,
      default: true
    },
    scrollInterval: {
      type: Number,
      default: 4000
    }
  },
  setup(props) {
    // 根据类型生成不同的表格数据
    const generateTableData = (type) => {
      const baseData = [
        {
          id: 1,
          sequence: '已验证',
          workOrder: 'E20250409',
          engineNumber: '598006',
          flow: 'F3',
          partNumber: 'HARDWAREX01',
          partName: 'BOLTS & NUTS (MM01)'
        },
        {
          id: 2,
          sequence: '已验证错误',
          workOrder: 'E20250508',
          engineNumber: '599393',
          flow: 'F4-1,B1',
          partNumber: 'HARDWAREX02',
          partName: 'BOLTS & NUTS (MM02)'
        },
        {
          id: 3,
          sequence: '已验证错误',
          workOrder: 'E20250204',
          engineNumber: '599522',
          flow: 'F4-1,B2B3',
          partNumber: 'MM03',
          partName: 'LPT ASSY'
        },
        {
          id: 4,
          sequence: '已验证错误',
          workOrder: 'E20250310',
          engineNumber: '599849',
          flow: 'F3',
          partNumber: 'HARDWAREX00',
          partName: 'BOLTS & NUTS (MM00)'
        },
        {
          id: 5,
          sequence: '已验证错误',
          workOrder: 'E20250310',
          engineNumber: '599849',
          flow: 'F3',
          partNumber: '362-090-002-0',
          partName: 'BRACKET'
        }
      ]

      // 根据类型调整数据
      return baseData.map(item => ({
        ...item,
        sequence: type === 'pending' ? '已验证' : 
                 type === 'converted' ? '已验证错误' : 
                 '已验证错误'
      }))
    }

    const tableData = ref(generateTableData(props.type))

    const tableConfig = computed(() => {
      switch (props.type) {
        case 'pending':
          return {
            title: '白转黄 待转PNR (RUPK)',
            bgColor: 'from-orange-500 to-orange-600',
            height: '400px'
          }
        case 'converted':
          return {
            title: '白转黄 已转PNR (F1-2/RUPK)',
            bgColor: 'from-green-500 to-green-600',
            height: '400px'
          }
        case 'scanning':
          return {
            title: '白转黄 待点灯扫描 (RUPK)',
            bgColor: 'from-purple-500 to-purple-600',
            height: '400px'
          }
        default:
          return {
            title: '白转黄表格',
            bgColor: 'from-gray-500 to-gray-600',
            height: '400px'
          }
      }
    })

    const tableColumns = [
      { field: 'sequence', title: '已验证', width: 100 },
      { field: 'workOrder', title: '工作指令', width: 120 },
      { field: 'engineNumber', title: '发动机号', width: 120 },
      { field: 'flow', title: 'FLOW', width: 100 },
      { field: 'partNumber', title: '件号', width: 150 },
      { field: 'partName', title: '名称', width: 200 }
    ]

    const tableRef = ref(null)
    const currentScrollTop = ref(0)
    const maxScrollTop = ref(0)

    // 自动滚动功能
    const { pause, resume } = useIntervalFn(() => {
      if (!props.autoScroll || !tableRef.value) return
      
      const tableBody = tableRef.value.$el.querySelector('.vxe-table--body-wrapper')
      if (!tableBody) return

      const scrollHeight = tableBody.scrollHeight
      const clientHeight = tableBody.clientHeight
      maxScrollTop.value = scrollHeight - clientHeight

      if (maxScrollTop.value <= 0) return

      currentScrollTop.value += 25
      if (currentScrollTop.value >= maxScrollTop.value) {
        currentScrollTop.value = 0
      }
      
      tableBody.scrollTop = currentScrollTop.value
    }, props.scrollInterval)

    onMounted(() => {
      if (props.autoScroll) {
        resume()
      }
    })

    onBeforeUnmount(() => {
      pause()
    })

    return {
      tableData,
      tableColumns,
      tableConfig,
      tableRef
    }
  },
  template: /*html*/ `
    <div class="white-to-yellow-table h-full">
      <div :class="['table-header bg-gradient-to-r text-white p-3 rounded-t-lg', tableConfig.bgColor]">
        <h3 class="text-base font-semibold text-center">{{ tableConfig.title }}</h3>
      </div>
      <div class="table-container bg-white rounded-b-lg shadow-lg overflow-hidden h-full">
        <vxe-table
          ref="tableRef"
          :data="tableData"
          :columns="tableColumns"
          border
          stripe
          :height="tableConfig.height"
          :scroll-y="{ enabled: true, mode: 'wheel' }"
          :row-config="{ isHover: true }"
          class="modern-table"
        >
          <vxe-column type="seq" width="50" fixed="left" title="#"></vxe-column>
          <vxe-column
            v-for="column in tableColumns"
            :key="column.field"
            :field="column.field"
            :title="column.title"
            :width="column.width"
            :min-width="column.minWidth"
          >
            <template #default="{ row }" v-if="column.field === 'sequence'">
              <el-tag 
                :type="row.sequence === '已验证' ? 'success' : 'warning'" 
                size="small"
              >
                {{ row.sequence }}
              </el-tag>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>
  `
} 