/**
 * 浏览器缩放检测和处理工具
 */
export class BrowserZoomHandler {
  constructor() {
    this.currentZoom = 1
    this.callbacks = []
    this.init()
  }

  /**
   * 初始化缩放检测
   */
  init() {
    // 检测缩放变化的方法
    this.detectZoomChange()
    
    // 监听窗口大小变化（可能由缩放引起）
    window.addEventListener('resize', this.handleResize.bind(this))
    
    // 监听视觉视口变化（现代浏览器）
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', this.handleVisualViewportChange.bind(this))
    }
  }

  /**
   * 检测缩放变化
   */
  detectZoomChange() {
    // 方法1: 使用 devicePixelRatio
    const getZoomLevel1 = () => {
      return window.devicePixelRatio || 1
    }

    // 方法2: 使用屏幕宽度比较
    const getZoomLevel2 = () => {
      return window.outerWidth / window.innerWidth
    }

    // 方法3: 使用媒体查询
    const getZoomLevel3 = () => {
      const mediaQuery = window.matchMedia('(min-resolution: 1dppx)')
      return mediaQuery.matches ? window.devicePixelRatio : 1
    }

    // 方法4: 使用元素尺寸检测
    const getZoomLevel4 = () => {
      const testElement = document.createElement('div')
      testElement.style.width = '100px'
      testElement.style.height = '100px'
      testElement.style.position = 'absolute'
      testElement.style.left = '-9999px'
      testElement.style.top = '-9999px'
      
      document.body.appendChild(testElement)
      const rect = testElement.getBoundingClientRect()
      document.body.removeChild(testElement)
      
      return rect.width / 100
    }

    // 综合检测
    const newZoom = this.getBestZoomLevel()
    
    if (Math.abs(newZoom - this.currentZoom) > 0.01) {
      const oldZoom = this.currentZoom
      this.currentZoom = newZoom
      this.notifyZoomChange(oldZoom, newZoom)
    }

    // 持续检测
    requestAnimationFrame(() => this.detectZoomChange())
  }

  /**
   * 获取最准确的缩放级别
   */
  getBestZoomLevel() {
    // 优先使用 visualViewport API（最准确）
    if (window.visualViewport) {
      return window.visualViewport.scale || 1
    }

    // 备用方案：使用 devicePixelRatio
    return window.devicePixelRatio || 1
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    // 延迟检测，避免频繁触发
    clearTimeout(this.resizeTimer)
    this.resizeTimer = setTimeout(() => {
      this.detectZoomChange()
    }, 100)
  }

  /**
   * 处理视觉视口变化
   */
  handleVisualViewportChange() {
    const newZoom = window.visualViewport.scale
    if (Math.abs(newZoom - this.currentZoom) > 0.01) {
      const oldZoom = this.currentZoom
      this.currentZoom = newZoom
      this.notifyZoomChange(oldZoom, newZoom)
    }
  }

  /**
   * 通知缩放变化
   */
  notifyZoomChange(oldZoom, newZoom) {
    const zoomEvent = {
      oldZoom,
      newZoom,
      zoomRatio: newZoom / oldZoom,
      timestamp: Date.now()
    }

    this.callbacks.forEach(callback => {
      try {
        callback(zoomEvent)
      } catch (error) {
        console.error('缩放回调执行错误:', error)
      }
    })
  }

  /**
   * 添加缩放变化监听器
   */
  onZoomChange(callback) {
    if (typeof callback === 'function') {
      this.callbacks.push(callback)
    }
    
    // 返回取消监听的函数
    return () => {
      const index = this.callbacks.indexOf(callback)
      if (index > -1) {
        this.callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 获取当前缩放级别
   */
  getCurrentZoom() {
    return this.currentZoom
  }

  /**
   * 获取缩放百分比
   */
  getZoomPercentage() {
    return Math.round(this.currentZoom * 100)
  }

  /**
   * 检查是否处于缩放状态
   */
  isZoomed() {
    return Math.abs(this.currentZoom - 1) > 0.01
  }

  /**
   * 销毁监听器
   */
  destroy() {
    window.removeEventListener('resize', this.handleResize.bind(this))
    
    if (window.visualViewport) {
      window.visualViewport.removeEventListener('resize', this.handleVisualViewportChange.bind(this))
    }
    
    clearTimeout(this.resizeTimer)
    this.callbacks = []
  }
}

/**
 * 创建全局缩放处理器实例
 */
export const browserZoomHandler = new BrowserZoomHandler()

/**
 * Vue 3 组合式函数
 */
export function useBrowserZoom() {
  const { ref, computed, onMounted, onUnmounted } = Vue
  
  const zoomLevel = ref(1)
  const zoomPercentage = ref(100)
  const isZoomed = ref(false)
  const viewportWidth = ref(window.innerWidth)
  const viewportHeight = ref(window.innerHeight)

  let unsubscribe = null

  const handleZoomChange = (zoomEvent) => {
    zoomLevel.value = zoomEvent.newZoom
    zoomPercentage.value = Math.round(zoomEvent.newZoom * 100)
    isZoomed.value = Math.abs(zoomEvent.newZoom - 1) > 0.01
    
    // 更新视口尺寸
    updateViewportSize()
  }

  const updateViewportSize = () => {
    viewportWidth.value = window.innerWidth
    viewportHeight.value = window.innerHeight
  }

  // 计算适配后的尺寸
  const adaptedDimensions = computed(() => {
    const baseWidth = 1200 // 基准宽度
    const baseHeight = 800 // 基准高度
    
    // 根据缩放级别和视口大小计算适配尺寸
    const availableWidth = viewportWidth.value
    const availableHeight = viewportHeight.value
    
    // 计算缩放因子，确保内容适配视口
    const scaleFactorX = availableWidth / baseWidth
    const scaleFactorY = availableHeight / baseHeight
    const scaleFactor = Math.min(scaleFactorX, scaleFactorY, 1 / zoomLevel.value)
    
    return {
      width: Math.floor(availableWidth),
      height: Math.floor(availableHeight),
      scaleFactor,
      contentWidth: Math.floor(baseWidth * scaleFactor),
      contentHeight: Math.floor(baseHeight * scaleFactor)
    }
  })

  // 计算字体缩放比例
  const fontScale = computed(() => {
    const zoom = zoomLevel.value
    if (zoom < 0.75) return 1.2 // 小缩放时放大字体
    if (zoom > 1.5) return 0.8  // 大缩放时缩小字体
    return 1
  })

  // 计算间距缩放比例
  const spacingScale = computed(() => {
    const zoom = zoomLevel.value
    if (zoom < 0.75) return 1.1
    if (zoom > 1.5) return 0.9
    return 1
  })

  onMounted(() => {
    // 初始化当前缩放状态
    zoomLevel.value = browserZoomHandler.getCurrentZoom()
    zoomPercentage.value = browserZoomHandler.getZoomPercentage()
    isZoomed.value = browserZoomHandler.isZoomed()
    updateViewportSize()

    // 监听缩放变化
    unsubscribe = browserZoomHandler.onZoomChange(handleZoomChange)
    
    // 监听窗口大小变化
    window.addEventListener('resize', updateViewportSize)
  })

  onUnmounted(() => {
    if (unsubscribe) {
      unsubscribe()
    }
    window.removeEventListener('resize', updateViewportSize)
  })

  return {
    zoomLevel,
    zoomPercentage,
    isZoomed,
    viewportWidth,
    viewportHeight,
    adaptedDimensions,
    fontScale,
    spacingScale
  }
}

/**
 * 响应式布局适配工具
 */
export class ResponsiveLayoutAdapter {
  constructor(options = {}) {
    this.options = {
      breakpoints: {
        xs: 0.5,    // 50% 缩放
        sm: 0.75,   // 75% 缩放
        md: 1,      // 100% 缩放
        lg: 1.25,   // 125% 缩放
        xl: 1.5,    // 150% 缩放
        xxl: 2      // 200% 缩放
      },
      ...options
    }
    
    this.currentBreakpoint = 'md'
    this.callbacks = []
    
    this.init()
  }

  init() {
    browserZoomHandler.onZoomChange((zoomEvent) => {
      this.updateBreakpoint(zoomEvent.newZoom)
    })
  }

  updateBreakpoint(zoomLevel) {
    const breakpoints = this.options.breakpoints
    let newBreakpoint = 'md'

    if (zoomLevel <= breakpoints.xs) {
      newBreakpoint = 'xs'
    } else if (zoomLevel <= breakpoints.sm) {
      newBreakpoint = 'sm'
    } else if (zoomLevel <= breakpoints.md) {
      newBreakpoint = 'md'
    } else if (zoomLevel <= breakpoints.lg) {
      newBreakpoint = 'lg'
    } else if (zoomLevel <= breakpoints.xl) {
      newBreakpoint = 'xl'
    } else {
      newBreakpoint = 'xxl'
    }

    if (newBreakpoint !== this.currentBreakpoint) {
      const oldBreakpoint = this.currentBreakpoint
      this.currentBreakpoint = newBreakpoint
      
      this.callbacks.forEach(callback => {
        callback({
          oldBreakpoint,
          newBreakpoint,
          zoomLevel
        })
      })
    }
  }

  onBreakpointChange(callback) {
    this.callbacks.push(callback)
    
    return () => {
      const index = this.callbacks.indexOf(callback)
      if (index > -1) {
        this.callbacks.splice(index, 1)
      }
    }
  }

  getCurrentBreakpoint() {
    return this.currentBreakpoint
  }
}

export const responsiveLayoutAdapter = new ResponsiveLayoutAdapter() 