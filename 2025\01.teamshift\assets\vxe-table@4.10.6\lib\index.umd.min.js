((e,t)=>{"object"==typeof exports&&"object"==typeof module?module.exports=t(require("vue"),require("@vxe-ui/core"),require("xe-utils")):"function"==typeof define&&define.amd?define([,"@vxe-ui/core","xe-utils"],t):"object"==typeof exports?exports.VXETable=t(require("vue"),require("@vxe-ui/core"),require("xe-utils")):e.VXETable=t(e.Vue,e.VxeUI,e.XEUtils)})("undefined"!=typeof self?self:this,function($t,_t,Ht){{var Nt={9274:function(e){e.exports=$t},4345:function(e){e.exports=_t},8871:function(e){e.exports=Ht},9306:function(e,t,r){var l=r(4901),o=r(6823),a=TypeError;e.exports=function(e){if(l(e))return e;throw new a(o(e)+" is not a function")}},679:function(e,t,r){var l=r(1625),o=TypeError;e.exports=function(e,t){if(l(t,e))return e;throw new o("Incorrect invocation")}},8551:function(e,t,r){var l=r(34),o=String,a=TypeError;e.exports=function(e){if(l(e))return e;throw new a(o(e)+" is not an object")}},9617:function(e,t,r){function l(i){return function(e,t,r){var l=s(e),o=d(l);if(0!==o){var a,n=c(r,o);if(i&&t!=t){for(;n<o;)if((a=l[n++])!=a)return!0}else for(;n<o;n++)if((i||n in l)&&l[n]===t)return i||n||0}return!i&&-1}}var s=r(5397),c=r(5610),d=r(6198);e.exports={includes:l(!0),indexOf:l(!1)}},4527:function(e,t,r){var l=r(3724),o=r(4376),a=TypeError,n=Object.getOwnPropertyDescriptor,r=l&&!function(){if(void 0!==this)return 1;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=r?function(e,t){if(o(e)&&!n(e,"length").writable)throw new a("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},6319:function(e,t,r){var o=r(8551),a=r(9539);e.exports=function(t,e,r,l){try{return l?e(o(r)[0],r[1]):e(r)}catch(e){a(t,"throw",e)}}},2195:function(e,t,r){var r=r(9504),l=r({}.toString),o=r("".slice);e.exports=function(e){return o(l(e),8,-1)}},6955:function(e,t,r){var l=r(2140),o=r(4901),a=r(2195),n=r(8227)("toStringTag"),i=Object,s="Arguments"===a(function(){return arguments}());e.exports=l?a:function(e){var t;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(t=((e,t)=>{try{return e[t]}catch(e){}})(e=i(e),n))?t:s?a(e):"Object"===(t=a(e))&&o(e.callee)?"Arguments":t}},7740:function(e,t,r){var s=r(9297),c=r(5031),d=r(7347),u=r(4913);e.exports=function(e,t,r){for(var l=c(t),o=u.f,a=d.f,n=0;n<l.length;n++){var i=l[n];s(e,i)||r&&s(r,i)||o(e,i,a(t,i))}}},2211:function(e,t,r){r=r(9039);e.exports=!r(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})},2529:function(e){e.exports=function(e,t){return{value:e,done:t}}},6699:function(e,t,r){var l=r(3724),o=r(4913),a=r(6980);e.exports=l?function(e,t,r){return o.f(e,t,a(1,r))}:function(e,t,r){return e[t]=r,e}},6980:function(e){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},4659:function(e,t,r){var l=r(3724),o=r(4913),a=r(6980);e.exports=function(e,t,r){l?o.f(e,t,a(0,r)):e[t]=r}},2106:function(e,t,r){var l=r(283),o=r(4913);e.exports=function(e,t,r){return r.get&&l(r.get,t,{getter:!0}),r.set&&l(r.set,t,{setter:!0}),o.f(e,t,r)}},6840:function(e,t,r){var n=r(4901),i=r(4913),s=r(283),c=r(9433);e.exports=function(e,t,r,l){var o=(l=l||{}).enumerable,a=void 0!==l.name?l.name:t;if(n(r)&&s(r,a,l),l.global)o?e[t]=r:c(t,r);else{try{l.unsafe?e[t]&&(o=!0):delete e[t]}catch(e){}o?e[t]=r:i.f(e,t,{value:r,enumerable:!1,configurable:!l.nonConfigurable,writable:!l.nonWritable})}return e}},6279:function(e,t,r){var o=r(6840);e.exports=function(e,t,r){for(var l in t)o(e,l,t[l],r);return e}},9433:function(e,t,r){var l=r(4576),o=Object.defineProperty;e.exports=function(t,r){try{o(l,t,{value:r,configurable:!0,writable:!0})}catch(e){l[t]=r}return r}},3724:function(e,t,r){r=r(9039);e.exports=!r(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},4055:function(e,t,r){var l=r(4576),r=r(34),o=l.document,a=r(o)&&r(o.createElement);e.exports=function(e){return a?o.createElement(e):{}}},6837:function(e){var t=TypeError;e.exports=function(e){if(9007199254740991<e)throw t("Maximum allowed index exceeded");return e}},8727:function(e){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},2839:function(e,t,r){r=r(4576).navigator,r=r&&r.userAgent;e.exports=r?String(r):""},9519:function(e,t,r){var l,o,a=r(4576),r=r(2839),n=a.process,a=a.Deno,n=n&&n.versions||a&&a.version,a=n&&n.v8;!(o=a?0<(l=a.split("."))[0]&&l[0]<4?1:+(l[0]+l[1]):o)&&r&&(!(l=r.match(/Edge\/(\d+)/))||74<=l[1])&&(l=r.match(/Chrome\/(\d+)/))&&(o=+l[1]),e.exports=o},6518:function(e,t,r){var c=r(4576),d=r(7347).f,u=r(6699),p=r(6840),h=r(9433),m=r(7740),v=r(2796);e.exports=function(e,t){var r,l,o,a=e.target,n=e.global,i=e.stat,s=n?c:i?c[a]||h(a,{}):c[a]&&c[a].prototype;if(s)for(r in t){if(l=t[r],o=e.dontCallGetSet?(o=d(s,r))&&o.value:s[r],!v(n?r:a+(i?".":"#")+r,e.forced)&&void 0!==o){if(typeof l==typeof o)continue;m(l,o)}(e.sham||o&&o.sham)&&u(l,"sham",!0),p(s,r,l,e)}}},9039:function(e){e.exports=function(e){try{return!!e()}catch(e){return!0}}},6080:function(e,t,r){var l=r(7476),o=r(9306),a=r(616),n=l(l.bind);e.exports=function(e,t){return o(e),void 0===t?e:a?n(e,t):function(){return e.apply(t,arguments)}}},616:function(e,t,r){r=r(9039);e.exports=!r(function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})},9565:function(e,t,r){var r=r(616),l=Function.prototype.call;e.exports=r?l.bind(l):function(){return l.apply(l,arguments)}},350:function(e,t,r){var l=r(3724),r=r(9297),o=Function.prototype,a=l&&Object.getOwnPropertyDescriptor,r=r(o,"name"),n=r&&"something"===function(){}.name,l=r&&(!l||a(o,"name").configurable);e.exports={EXISTS:r,PROPER:n,CONFIGURABLE:l}},7476:function(e,t,r){var l=r(2195),o=r(9504);e.exports=function(e){if("Function"===l(e))return o(e)}},9504:function(e,t,r){var r=r(616),l=Function.prototype,o=l.call,l=r&&l.bind.bind(o,o);e.exports=r?l:function(e){return function(){return o.apply(e,arguments)}}},7751:function(e,t,r){var l=r(4576),o=r(4901);e.exports=function(e,t){return arguments.length<2?(r=l[e],o(r)?r:void 0):l[e]&&l[e][t];var r}},1767:function(e){e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},851:function(e,t,r){var l=r(6955),o=r(5966),a=r(4117),n=r(6269),i=r(8227)("iterator");e.exports=function(e){if(!a(e))return o(e,i)||o(e,"@@iterator")||n[l(e)]}},81:function(e,t,r){var l=r(9565),o=r(9306),a=r(8551),n=r(6823),i=r(851),s=TypeError;e.exports=function(e,t){t=arguments.length<2?i(e):t;if(o(t))return a(l(t,e));throw new s(n(e)+" is not iterable")}},5966:function(e,t,r){var l=r(9306),o=r(4117);e.exports=function(e,t){e=e[t];return o(e)?void 0:l(e)}},4576:function(e,t,r){function l(e){return e&&e.Math===Math&&e}e.exports=l("object"==typeof globalThis&&globalThis)||l("object"==typeof window&&window)||l("object"==typeof self&&self)||l("object"==typeof r.g&&r.g)||l("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:function(e,t,r){var l=r(9504),o=r(8981),a=l({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return a(o(e),t)}},421:function(e){e.exports={}},397:function(e,t,r){r=r(7751);e.exports=r("document","documentElement")},5917:function(e,t,r){var l=r(3724),o=r(9039),a=r(4055);e.exports=!l&&!o(function(){return 7!==Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a})},7055:function(e,t,r){var l=r(9504),o=r(9039),a=r(2195),n=Object,i=l("".split);e.exports=o(function(){return!n("z").propertyIsEnumerable(0)})?function(e){return"String"===a(e)?i(e,""):n(e)}:n},3706:function(e,t,r){var l=r(9504),o=r(4901),r=r(7629),a=l(Function.toString);o(r.inspectSource)||(r.inspectSource=function(e){return a(e)}),e.exports=r.inspectSource},1181:function(e,t,r){var l,o,a,n,i=r(8622),s=r(4576),c=r(34),d=r(6699),u=r(9297),p=r(7629),h=r(6119),r=r(421),m="Object already initialized",v=s.TypeError,s=s.WeakMap,f=i||p.state?((a=p.state||(p.state=new s)).get=a.get,a.has=a.has,a.set=a.set,l=function(e,t){if(a.has(e))throw new v(m);return t.facade=e,a.set(e,t),t},o=function(e){return a.get(e)||{}},function(e){return a.has(e)}):(r[n=h("state")]=!0,l=function(e,t){if(u(e,n))throw new v(m);return t.facade=e,d(e,n,t),t},o=function(e){return u(e,n)?e[n]:{}},function(e){return u(e,n)});e.exports={set:l,get:o,has:f,enforce:function(e){return f(e)?o(e):l(e,{})},getterFor:function(t){return function(e){if(c(e)&&(e=o(e)).type===t)return e;throw new v("Incompatible receiver, "+t+" required")}}}},4209:function(e,t,r){var l=r(8227),o=r(6269),a=l("iterator"),n=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||n[a]===e)}},4376:function(e,t,r){var l=r(2195);e.exports=Array.isArray||function(e){return"Array"===l(e)}},4901:function(e){var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},2796:function(e,t,r){function l(e,t){return(e=s[i(e)])===d||e!==c&&(a(t)?o(t):!!t)}var o=r(9039),a=r(4901),n=/#|\.prototype\./,i=l.normalize=function(e){return String(e).replace(n,".").toLowerCase()},s=l.data={},c=l.NATIVE="N",d=l.POLYFILL="P";e.exports=l},4117:function(e){e.exports=function(e){return null==e}},34:function(e,t,r){var l=r(4901);e.exports=function(e){return"object"==typeof e?null!==e:l(e)}},6395:function(e){e.exports=!1},757:function(e,t,r){var l=r(7751),o=r(4901),a=r(1625),r=r(7040),n=Object;e.exports=r?function(e){return"symbol"==typeof e}:function(e){var t=l("Symbol");return o(t)&&a(t.prototype,n(e))}},2652:function(e,t,r){function g(e,t){this.stopped=e,this.result=t}var x=r(6080),b=r(9565),C=r(8551),w=r(6823),y=r(4209),T=r(6198),E=r(1625),R=r(81),S=r(851),I=r(9539),D=TypeError,M=g.prototype;e.exports=function(e,t,r){function l(e){return a&&I(a,"normal",e),new g(!0,e)}function o(e){return p?(C(e),v?f(e[0],e[1],l):f(e[0],e[1])):v?f(e,l):f(e)}var a,n,i,s,c,d,u=r&&r.that,p=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_RECORD),m=!(!r||!r.IS_ITERATOR),v=!(!r||!r.INTERRUPTED),f=x(t,u);if(h)a=e.iterator;else if(m)a=e;else{if(!(r=S(e)))throw new D(w(e)+" is not iterable");if(y(r)){for(n=0,i=T(e);n<i;n++)if((s=o(e[n]))&&E(M,s))return s;return new g(!1)}a=R(e,r)}for(c=(h?e:a).next;!(d=b(c,a)).done;){try{s=o(d.value)}catch(e){I(a,"throw",e)}if("object"==typeof s&&s&&E(M,s))return s}return new g(!1)}},9539:function(e,t,r){var a=r(9565),n=r(8551),i=r(5966);e.exports=function(e,t,r){var l,o;n(e);try{if(!(l=i(e,"return"))){if("throw"===t)throw r;return r}l=a(l,e)}catch(e){o=!0,l=e}if("throw"===t)throw r;if(o)throw l;return n(l),r}},9462:function(e,t,r){function l(l){var o=c.getterFor(l?v:m);return i(n(u),{next:function(){var t=o(this);if(l)return t.nextHandler();try{var e=t.done?void 0:t.nextHandler();return p(e,t.done)}catch(e){throw t.done=!0,e}},return:function(){var e,t=o(this),r=t.iterator;if(t.done=!0,l)return(e=d(r,"return"))?a(e,r):p(void 0,!0);if(t.inner)try{h(t.inner.iterator,"normal")}catch(e){return h(r,"throw",e)}return r&&h(r,"normal"),p(void 0,!0)}})}var a=r(9565),n=r(2360),o=r(6699),i=r(6279),s=r(8227),c=r(1181),d=r(5966),u=r(7657).IteratorPrototype,p=r(2529),h=r(9539),r=s("toStringTag"),m="IteratorHelper",v="WrapForValidIterator",f=c.set,g=l(!0),x=l(!1);o(x,r,"Iterator Helper"),e.exports=function(r,l){function e(e,t){t?(t.iterator=e.iterator,t.next=e.next):t=e,t.type=l?v:m,t.nextHandler=r,t.counter=0,t.done=!1,f(this,t)}return e.prototype=l?g:x,e}},713:function(e,t,r){var l=r(9565),o=r(9306),a=r(8551),n=r(1767),i=r(9462),s=r(6319),c=i(function(){var e=this.iterator,t=a(l(this.next,e));if(!(this.done=!!t.done))return s(e,this.mapper,[t.value,this.counter++],!0)});e.exports=function(e){return a(this),o(e),new c(n(this),{mapper:e})}},7657:function(e,t,r){var l,o,a=r(9039),n=r(4901),i=r(34),s=r(2360),c=r(2787),d=r(6840),u=r(8227),r=r(6395),p=u("iterator"),u=!1;[].keys&&("next"in(o=[].keys())?(c=c(c(o)))!==Object.prototype&&(l=c):u=!0),!i(l)||a(function(){var e={};return l[p].call(e)!==e})?l={}:r&&(l=s(l)),n(l[p])||d(l,p,function(){return this}),e.exports={IteratorPrototype:l,BUGGY_SAFARI_ITERATORS:u}},6269:function(e){e.exports={}},6198:function(e,t,r){var l=r(8014);e.exports=function(e){return l(e.length)}},283:function(e,t,r){var l=r(9504),o=r(9039),a=r(4901),n=r(9297),i=r(3724),s=r(350).CONFIGURABLE,c=r(3706),r=r(1181),d=r.enforce,u=r.get,p=String,h=Object.defineProperty,m=l("".slice),v=l("".replace),f=l([].join),g=i&&!o(function(){return 8!==h(function(){},"length",{value:8}).length}),x=String(String).split("String"),r=e.exports=function(e,t,r){"Symbol("===m(p(t),0,7)&&(t="["+v(p(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!n(e,"name")||s&&e.name!==t)&&(i?h(e,"name",{value:t,configurable:!0}):e.name=t),g&&r&&n(r,"arity")&&e.length!==r.arity&&h(e,"length",{value:r.arity});try{r&&n(r,"constructor")&&r.constructor?i&&h(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}r=d(e);return n(r,"source")||(r.source=f(x,"string"==typeof t?t:"")),e};Function.prototype.toString=r(function(){return a(this)&&u(this).source||c(this)},"toString")},741:function(e){var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){e=+e;return(0<e?r:t)(e)}},2360:function(e,t,r){function l(){}function o(e){e.write(g("")),e.close();var t=e.parentWindow.Object;return e=null,t}var a,n=r(8551),i=r(6801),s=r(8727),c=r(421),d=r(397),u=r(4055),r=r(6119),p=">",h="<",m="prototype",v="script",f=r("IE_PROTO"),g=function(e){return h+v+p+e+h+"/"+v+p},x=function(){try{a=new ActiveXObject("htmlfile")}catch(e){}x="undefined"==typeof document||document.domain&&a?o(a):(e=u("iframe"),t="java"+v+":",e.style.display="none",d.appendChild(e),e.src=String(t),(t=e.contentWindow.document).open(),t.write(g("document.F=Object")),t.close(),t.F);for(var e,t,r=s.length;r--;)delete x[m][s[r]];return x()};c[f]=!0,e.exports=Object.create||function(e,t){var r;return null!==e?(l[m]=n(e),r=new l,l[m]=null,r[f]=e):r=x(),void 0===t?r:i.f(r,t)}},6801:function(e,t,r){var l=r(3724),o=r(8686),i=r(4913),s=r(8551),c=r(5397),d=r(1072);t.f=l&&!o?Object.defineProperties:function(e,t){s(e);for(var r,l=c(t),o=d(t),a=o.length,n=0;n<a;)i.f(e,r=o[n++],l[r]);return e}},4913:function(e,t,r){var l=r(3724),o=r(5917),a=r(8686),n=r(8551),i=r(6969),s=TypeError,c=Object.defineProperty,d=Object.getOwnPropertyDescriptor,u="enumerable",p="configurable",h="writable";t.f=l?a?function(e,t,r){var l;return n(e),t=i(t),n(r),"function"==typeof e&&"prototype"===t&&"value"in r&&h in r&&!r[h]&&(l=d(e,t))&&l[h]&&(e[t]=r.value,r={configurable:(p in r?r:l)[p],enumerable:(u in r?r:l)[u],writable:!1}),c(e,t,r)}:c:function(e,t,r){if(n(e),t=i(t),n(r),o)try{return c(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new s("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},7347:function(e,t,r){var l=r(3724),o=r(9565),a=r(8773),n=r(6980),i=r(5397),s=r(6969),c=r(9297),d=r(5917),u=Object.getOwnPropertyDescriptor;t.f=l?u:function(e,t){if(e=i(e),t=s(t),d)try{return u(e,t)}catch(e){}if(c(e,t))return n(!o(a.f,e,t),e[t])}},8480:function(e,t,r){var l=r(1828),o=r(8727).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return l(e,o)}},3717:function(e,t){t.f=Object.getOwnPropertySymbols},2787:function(e,t,r){var l=r(9297),o=r(4901),a=r(8981),n=r(6119),r=r(2211),i=n("IE_PROTO"),s=Object,c=s.prototype;e.exports=r?s.getPrototypeOf:function(e){var t,e=a(e);return l(e,i)?e[i]:(t=e.constructor,o(t)&&e instanceof t?t.prototype:e instanceof s?c:null)}},1625:function(e,t,r){r=r(9504);e.exports=r({}.isPrototypeOf)},1828:function(e,t,r){var l=r(9504),n=r(9297),i=r(5397),s=r(9617).indexOf,c=r(421),d=l([].push);e.exports=function(e,t){var r,l=i(e),o=0,a=[];for(r in l)!n(c,r)&&n(l,r)&&d(a,r);for(;t.length>o;)!n(l,r=t[o++])||~s(a,r)||d(a,r);return a}},1072:function(e,t,r){var l=r(1828),o=r(8727);e.exports=Object.keys||function(e){return l(e,o)}},8773:function(e,t){var r={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,o=l&&!r.call({1:2},1);t.f=o?function(e){e=l(this,e);return!!e&&e.enumerable}:r},4270:function(e,t,r){var o=r(9565),a=r(4901),n=r(34),i=TypeError;e.exports=function(e,t){var r,l;if("string"===t&&a(r=e.toString)&&!n(l=o(r,e)))return l;if(a(r=e.valueOf)&&!n(l=o(r,e)))return l;if("string"!==t&&a(r=e.toString)&&!n(l=o(r,e)))return l;throw new i("Can't convert object to primitive value")}},5031:function(e,t,r){var l=r(7751),o=r(9504),a=r(8480),n=r(3717),i=r(8551),s=o([].concat);e.exports=l("Reflect","ownKeys")||function(e){var t=a.f(i(e)),r=n.f;return r?s(t,r(e)):t}},7750:function(e,t,r){var l=r(4117),o=TypeError;e.exports=function(e){if(l(e))throw new o("Can't call method on "+e);return e}},6119:function(e,t,r){var l=r(5745),o=r(3392),a=l("keys");e.exports=function(e){return a[e]||(a[e]=o(e))}},7629:function(e,t,r){var l=r(6395),o=r(4576),r=r(9433),a="__core-js_shared__",e=e.exports=o[a]||r(a,{});(e.versions||(e.versions=[])).push({version:"3.39.0",mode:l?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:function(e,t,r){var l=r(7629);e.exports=function(e,t){return l[e]||(l[e]=t||{})}},4495:function(e,t,r){var l=r(9519),o=r(9039),a=r(4576).String;e.exports=!!Object.getOwnPropertySymbols&&!o(function(){var e=Symbol("symbol detection");return!a(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&l&&l<41})},5610:function(e,t,r){var l=r(1291),o=Math.max,a=Math.min;e.exports=function(e,t){e=l(e);return e<0?o(e+t,0):a(e,t)}},5397:function(e,t,r){var l=r(7055),o=r(7750);e.exports=function(e){return l(o(e))}},1291:function(e,t,r){var l=r(741);e.exports=function(e){e=+e;return e!=e||0==e?0:l(e)}},8014:function(e,t,r){var l=r(1291),o=Math.min;e.exports=function(e){e=l(e);return 0<e?o(e,9007199254740991):0}},8981:function(e,t,r){var l=r(7750),o=Object;e.exports=function(e){return o(l(e))}},2777:function(e,t,r){var l=r(9565),o=r(34),a=r(757),n=r(5966),i=r(4270),r=r(8227),s=TypeError,c=r("toPrimitive");e.exports=function(e,t){if(!o(e)||a(e))return e;var r=n(e,c);if(r){if(r=l(r,e,t=void 0===t?"default":t),!o(r)||a(r))return r;throw new s("Can't convert object to primitive value")}return i(e,t=void 0===t?"number":t)}},6969:function(e,t,r){var l=r(2777),o=r(757);e.exports=function(e){e=l(e,"string");return o(e)?e:e+""}},2140:function(e,t,r){var l={};l[r(8227)("toStringTag")]="z",e.exports="[object z]"===String(l)},6823:function(e){var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},3392:function(e,t,r){var r=r(9504),l=0,o=Math.random(),a=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++l+o,36)}},7040:function(e,t,r){r=r(4495);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:function(e,t,r){var l=r(3724),r=r(9039);e.exports=l&&r(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},8622:function(e,t,r){var l=r(4576),r=r(4901),l=l.WeakMap;e.exports=r(l)&&/native code/.test(String(l))},8227:function(e,t,r){var l=r(4576),o=r(5745),a=r(9297),n=r(3392),i=r(4495),r=r(7040),s=l.Symbol,c=o("wks"),d=r?s.for||s:s&&s.withoutSetter||n;e.exports=function(e){return a(c,e)||(c[e]=i&&a(s,e)?s[e]:d("Symbol."+e)),c[e]}},4114:function(e,t,r){var l=r(6518),a=r(8981),n=r(6198),i=r(4527),s=r(6837);l({target:"Array",proto:!0,arity:1,forced:r(9039)(function(){return 4294967297!==[].push.call({length:4294967296},1)})||!(()=>{try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}})()},{push:function(e){var t=a(this),r=n(t),l=arguments.length;s(r+l);for(var o=0;o<l;o++)t[r]=arguments[o],r++;return i(t,r),r}})},8111:function(e,t,r){function l(){if(i(this,f),d(this)===f)throw new C("Abstract class Iterator not directly constructable")}function o(t,e){g?u(f,t,{configurable:!0,get:function(){return e},set:function(e){if(s(this),this===f)throw new C("You can't redefine this property");m(this,t)?this[t]=e:p(this,t,e)}}):f[t]=e}var a=r(6518),n=r(4576),i=r(679),s=r(8551),c=r(4901),d=r(2787),u=r(2106),p=r(4659),h=r(9039),m=r(9297),v=r(8227),f=r(7657).IteratorPrototype,g=r(3724),r=r(6395),x="constructor",b="Iterator",v=v("toStringTag"),C=TypeError,w=n[b],n=r||!c(w)||w.prototype!==f||!h(function(){w({})});m(f,v)||o(v,b),!n&&m(f,x)&&f[x]!==Object||o(x,l),l.prototype=f,a({global:!0,constructor:!0,forced:n},{Iterator:l})},1148:function(e,t,r){var l=r(6518),o=r(2652),a=r(9306),n=r(8551),i=r(1767);l({target:"Iterator",proto:!0,real:!0},{every:function(r){n(this),a(r);var e=i(this),l=0;return!o(e,function(e,t){if(!r(e,l++))return t()},{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},2489:function(e,t,r){var l=r(6518),o=r(9565),a=r(9306),n=r(8551),i=r(1767),s=r(9462),c=r(6319),r=r(6395),d=s(function(){for(var e,t=this.iterator,r=this.predicate,l=this.next;;){if(e=n(o(l,t)),this.done=!!e.done)return;if(c(t,r,[e=e.value,this.counter++],!0))return e}});l({target:"Iterator",proto:!0,real:!0,forced:r},{filter:function(e){return n(this),a(e),new d(i(this),{predicate:e})}})},116:function(e,t,r){var l=r(6518),o=r(2652),a=r(9306),n=r(8551),i=r(1767);l({target:"Iterator",proto:!0,real:!0},{find:function(r){n(this),a(r);var e=i(this),l=0;return o(e,function(e,t){if(r(e,l++))return t(e)},{IS_RECORD:!0,INTERRUPTED:!0}).result}})},7588:function(e,t,r){var l=r(6518),o=r(2652),a=r(9306),n=r(8551),i=r(1767);l({target:"Iterator",proto:!0,real:!0},{forEach:function(t){n(this),a(t);var e=i(this),r=0;o(e,function(e){t(e,r++)},{IS_RECORD:!0})}})},1701:function(e,t,r){var l=r(6518),o=r(713);l({target:"Iterator",proto:!0,real:!0,forced:r(6395)},{map:o})},8237:function(e,t,r){var l=r(6518),a=r(2652),n=r(9306),i=r(8551),s=r(1767),c=TypeError;l({target:"Iterator",proto:!0,real:!0},{reduce:function(t){i(this),n(t);var e=s(this),r=arguments.length<2,l=r?void 0:arguments[1],o=0;if(a(e,function(e){l=r?(r=!1,e):t(l,e,o),o++},{IS_RECORD:!0}),r)throw new c("Reduce of empty iterator with no initial value");return l}})},3579:function(e,t,r){var l=r(6518),o=r(2652),a=r(9306),n=r(8551),i=r(1767);l({target:"Iterator",proto:!0,real:!0},{some:function(r){n(this),a(r);var e=i(this),l=0;return o(e,function(e,t){if(r(e,l++))return t()},{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},1806:function(e,t,r){var l=r(6518),o=r(8551),a=r(2652),n=r(1767),i=[].push;l({target:"Iterator",proto:!0,real:!0},{toArray:function(){var e=[];return a(n(o(this)),i,{that:e,IS_RECORD:!0}),e}})},8992:function(e,t,r){r(8111)},3215:function(e,t,r){r(1148)},4520:function(e,t,r){r(2489)},2577:function(e,t,r){r(116)},3949:function(e,t,r){r(7588)},1454:function(e,t,r){r(1701)},8872:function(e,t,r){r(8237)},7550:function(e,t,r){r(3579)},1795:function(e,t,r){r(1806)}},Pt={};function bt(e){var t=Pt[e];return void 0!==t||(t=Pt[e]={exports:{}},Nt[e].call(t.exports,t,t.exports,bt)),t.exports}bt.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return bt.d(t,{a:t}),t},bt.d=function(e,t){for(var r in t)bt.o(t,r)&&!bt.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},bt.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),bt.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},bt.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},bt.p="";var Bt={},zt=(bt.r(Bt),bt.d(Bt,{Colgroup:function(){return ue},Column:function(){return de},Grid:function(){return gt},Table:function(){return tt},Toolbar:function(){return nt},VXETable:function(){return X},VxeColgroup:function(){return l},VxeColumn:function(){return r},VxeGrid:function(){return ft},VxeTable:function(){return a},VxeToolbar:function(){return i},VxeUI:function(){return ol.VxeUI},_t:function(){return q},clipboard:function(){return A},commands:function(){return F},config:function(){return U},default:function(){return _o},formats:function(){return k},getConfig:function(){return m},getI18n:function(){return C},getIcon:function(){return f},getTheme:function(){return c},globalEvents:function(){return y},globalResize:function(){return S},hooks:function(){return V},install:function(){return $o},interceptor:function(){return O},log:function(){return L},menus:function(){return M},modal:function(){return Z},print:function(){return G},readFile:function(){return Y},renderer:function(){return I},saveFile:function(){return K},setConfig:function(){return d},setI18n:function(){return b},setIcon:function(){return v},setLanguage:function(){return g},setTheme:function(){return s},setup:function(){return B},t:function(){return W},use:function(){return $},validators:function(){return D},version:function(){return e}}),{}),ol=(bt.r(zt),bt.d(zt,{Colgroup:function(){return ue},Column:function(){return de},Grid:function(){return gt},Table:function(){return tt},Toolbar:function(){return nt},VXETable:function(){return X},VxeColgroup:function(){return l},VxeColumn:function(){return r},VxeGrid:function(){return ft},VxeTable:function(){return a},VxeToolbar:function(){return i},VxeUI:function(){return ol.VxeUI},_t:function(){return q},clipboard:function(){return A},commands:function(){return F},config:function(){return U},formats:function(){return k},getConfig:function(){return m},getI18n:function(){return C},getIcon:function(){return f},getTheme:function(){return c},globalEvents:function(){return y},globalResize:function(){return S},hooks:function(){return V},install:function(){return $o},interceptor:function(){return O},log:function(){return L},menus:function(){return M},modal:function(){return Z},print:function(){return G},readFile:function(){return Y},renderer:function(){return I},saveFile:function(){return K},setConfig:function(){return d},setI18n:function(){return b},setIcon:function(){return v},setLanguage:function(){return g},setTheme:function(){return s},setup:function(){return B},t:function(){return W},use:function(){return $},validators:function(){return D},version:function(){return e}}),"undefined"!=typeof window&&(Ct=(Ct=window.document.currentScript)&&Ct.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(bt.p=Ct[1]),bt(8992),bt(3949),bt(4345)),Ct=bt(8871),al=bt.n(Ct),wt=null,jt=null,Ut=null,Wt="z-index-manage",qt=null,Xt="z-index-style",Kt={m:1e3,s:1e3};function Yt(){return wt||"undefined"!=typeof document&&(wt=document),wt}function Gt(){return jt=wt&&!jt?wt.body||wt.getElementsByTagName("body")[0]:jt}function Zt(){var e;qt||!(e=Yt())||(qt=e.getElementById(Xt))||((qt=e.createElement("style")).id=Xt,e.getElementsByTagName("head")[0].appendChild(qt)),qt&&(qt.innerHTML=":root{--dom-main"+(e="-z-index")+":"+rr()+";--dom-sub"+e+":"+nr()+"}")}function Qt(){var e,t;return Ut||(e=Yt())&&!(Ut=e.getElementById(Wt))&&(t=Gt())&&((Ut=e.createElement("div")).id=Wt,Ut.style.display="none",t.appendChild(Ut),er(Kt.m),or(Kt.s)),Ut}function Jt(r){return function(e){var t;return e&&(e=Number(e),Kt[r]=e,t=Qt())&&(t.dataset?t.dataset[r]=e+"":t.setAttribute("data-"+r,e+"")),Zt(),Kt[r]}}var er=Jt("m");function tr(l,o){return function(e){var t=Qt(),r=(r=t&&(t=t.dataset?t.dataset[l]:t.getAttribute("data-"+l))?Number(t):r)||Kt[l];return e?Number(e)<r?o():e:r}}var rr=tr("m",lr);function lr(){return er(rr()+1)}var or=Jt("s"),ar=tr("s",ir);function nr(){return rr()+ar()}function ir(){return or(ar()+1),nr()}var Ct={setCurrent:er,getCurrent:rr,getNext:lr,setSubCurrent:or,getSubCurrent:nr,getSubNext:ir,getMax:function(){var e=0;if(Yt()){var t=Gt();if(t)for(var r=t.getElementsByTagName("*"),l=0;l<r.length;l++){var o=r[l];o&&o.style&&1===o.nodeType&&(o=o.style.zIndex)&&/^\d+$/.test(o)&&(e=Math.max(e,Number(o)))}}return e}},sr=(Zt(),Ct);function nl(e){return e&&!1!==e.enabled}function cr(e){return null==e||""===e}function dr(e){var e=e.name,t=al().lastIndexOf(e,"."),r=e.substring(t+1,e.length).toLowerCase();return{filename:e.substring(0,t),type:r}}function Tl(){return sr.getNext()}function El(){return sr.getCurrent()}function Rl(e){return e&&e.children&&0<e.children.length}function il(e,t){var r;return e?(r=ol.VxeUI.getConfig().translate,al().toValueString(r?r(""+e,t):e)):""}function sl(e,t){return""+(cr(e)?t?ol.VxeUI.getConfig().emptyCell:"":e)}function cl(e){return""===e||al().eqNull(e)}let e="4.10.6",s=(ol.VxeUI.version=e,ol.VxeUI.tableVersion=e,ol.VxeUI.setConfig({emptyCell:"　",table:{fit:!0,showHeader:!0,animat:!0,delayHover:250,autoResize:!0,minHeight:144,resizeConfig:{},resizableConfig:{dragMode:"auto",showDragTip:!0,isSyncAutoHeight:!0,isSyncAutoWidth:!0,minHeight:18},radioConfig:{strict:!0},rowDragConfig:{showIcon:!0,animation:!0,showGuidesStatus:!0},columnDragConfig:{showIcon:!0,animation:!0,showGuidesStatus:!0},checkboxConfig:{strict:!0},tooltipConfig:{enterable:!0},validConfig:{showMessage:!0,autoClear:!0,autoPos:!0,message:"inline",msgMode:"single",theme:"beautify"},columnConfig:{maxFixedSize:4},cellConfig:{padding:!0},headerCellConfig:{height:"unset"},footerCellConfig:{height:"unset"},customConfig:{allowVisible:!0,allowResizable:!0,allowFixed:!0,allowSort:!0,showFooter:!0,placement:"top-right",modalOptions:{showMaximize:!0,mask:!0,lockView:!0,resize:!0,escClosable:!0},drawerOptions:{mask:!0,lockView:!0,escClosable:!0,resize:!0}},sortConfig:{showIcon:!0,allowClear:!0,allowBtn:!0,iconLayout:"vertical"},filterConfig:{showIcon:!0},treeConfig:{rowField:"id",parentField:"parentId",childrenField:"children",hasChildField:"hasChild",mapChildrenField:"_X_ROW_CHILD",indent:20,showIcon:!0},expandConfig:{showIcon:!0},editConfig:{showIcon:!0,showAsterisk:!0,autoFocus:!0},importConfig:{_typeMaps:{csv:1,html:1,xml:1,txt:1}},exportConfig:{_typeMaps:{csv:1,html:1,xml:1,txt:1}},printConfig:{},mouseConfig:{extension:!0},keyboardConfig:{isEsc:!0},areaConfig:{autoClear:!0,selectCellByHeader:!0,selectCellByBody:!0,extendDirection:{top:!0,left:!0,bottom:!0,right:!0}},clipConfig:{isCopy:!0,isCut:!0,isPaste:!0},fnrConfig:{isFind:!0,isReplace:!0},virtualXConfig:{enabled:!0,gt:60,preSize:0,oSize:1},virtualYConfig:{enabled:!0,gt:100,preSize:1,oSize:2},scrollbarConfig:{}},grid:{formConfig:{enabled:!0},pagerConfig:{enabled:!0},toolbarConfig:{enabled:!0},proxyConfig:{enabled:!0,autoLoad:!0,showResponseMsg:!0,showActiveMsg:!0,props:{list:null,result:"result",total:"page.total",message:"message"}}},toolbar:{}}),Ct="vxe-table-icon-",ol.VxeUI.setIcon({TABLE_SORT_ASC:Ct+"caret-up",TABLE_SORT_DESC:Ct+"caret-down",TABLE_FILTER_NONE:Ct+"funnel",TABLE_FILTER_MATCH:Ct+"funnel",TABLE_EDIT:Ct+"edit",TABLE_TITLE_PREFIX:Ct+"question-circle-fill",TABLE_TITLE_SUFFIX:Ct+"question-circle-fill",TABLE_TREE_LOADED:Ct+"spinner roll",TABLE_TREE_OPEN:Ct+"caret-right rotate90",TABLE_TREE_CLOSE:Ct+"caret-right",TABLE_EXPAND_LOADED:Ct+"spinner roll",TABLE_EXPAND_OPEN:Ct+"arrow-right rotate90",TABLE_EXPAND_CLOSE:Ct+"arrow-right",TABLE_CHECKBOX_CHECKED:Ct+"checkbox-checked-fill",TABLE_CHECKBOX_UNCHECKED:Ct+"checkbox-unchecked",TABLE_CHECKBOX_INDETERMINATE:Ct+"checkbox-indeterminate-fill",TABLE_RADIO_CHECKED:Ct+"radio-checked-fill",TABLE_RADIO_UNCHECKED:Ct+"radio-unchecked",TABLE_CUSTOM_SORT:Ct+"drag-handle",TABLE_MENU_OPTIONS:Ct+"arrow-right",TABLE_DRAG_ROW:Ct+"drag-handle",TABLE_DRAG_COLUMN:Ct+"drag-handle",TABLE_DRAG_STATUS_ROW:Ct+"sort",TABLE_DRAG_STATUS_SUB_ROW:Ct+"add-sub",TABLE_DRAG_STATUS_COLUMN:Ct+"swap",TABLE_DRAG_DISABLED:Ct+"no-drop",TOOLBAR_TOOLS_REFRESH:Ct+"repeat",TOOLBAR_TOOLS_REFRESH_LOADING:Ct+"repeat roll",TOOLBAR_TOOLS_IMPORT:Ct+"upload",TOOLBAR_TOOLS_EXPORT:Ct+"download",TOOLBAR_TOOLS_PRINT:Ct+"print",TOOLBAR_TOOLS_FULLSCREEN:Ct+"fullscreen",TOOLBAR_TOOLS_MINIMIZE:Ct+"minimize",TOOLBAR_TOOLS_CUSTOM:Ct+"custom-column",TOOLBAR_TOOLS_FIXED_LEFT:Ct+"fixed-left",TOOLBAR_TOOLS_FIXED_LEFT_ACTIVE:Ct+"fixed-left-fill",TOOLBAR_TOOLS_FIXED_RIGHT:Ct+"fixed-right",TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVE:Ct+"fixed-right-fill"}),ol.VxeUI.setTheme),c=ol.VxeUI.getTheme,d=ol.VxeUI.setConfig,m=ol.VxeUI.getConfig,v=ol.VxeUI.setIcon,f=ol.VxeUI.getIcon,g=ol.VxeUI.setLanguage,b=ol.VxeUI.setI18n,C=ol.VxeUI.getI18n,y=ol.VxeUI.globalEvents,S=ol.VxeUI.globalResize,I=ol.VxeUI.renderer,D=ol.VxeUI.validators,M=ol.VxeUI.menus,k=ol.VxeUI.formats,F=ol.VxeUI.commands,O=ol.VxeUI.interceptor,A=ol.VxeUI.clipboard,L=ol.VxeUI.log,V=ol.VxeUI.hooks,$=ol.VxeUI.use,B=e=>ol.VxeUI.setConfig(e),U=(ol.VxeUI.setup=B,e=>ol.VxeUI.setConfig(e)),W=(ol.VxeUI.config=U,(e,t)=>ol.VxeUI.getI18n(e,t)),q=(ol.VxeUI.t=W,(e,t)=>il(e,t)),X=(ol.VxeUI._t=q,ol.VxeUI),K=e=>ol.VxeUI.saveFile(e),Y=e=>ol.VxeUI.readFile(e),G=e=>ol.VxeUI.print(e),Z={get(e){return ol.VxeUI.modal.get(e)},close(e){return ol.VxeUI.modal.close(e)},open(e){return ol.VxeUI.modal.open(e)},alert(e,t,r){return ol.VxeUI.modal.alert(e,t,r)},confirm(e,t,r){return ol.VxeUI.modal.confirm(e,t,r)},message(e,t){return ol.VxeUI.modal.message(e,t)},notification(e,t,r){return ol.VxeUI.modal.notification(e,t,r)}};var dl=bt(9274),Ct=(bt(4114),bt(1454),bt(7550),ol.VxeUI.log),ur="table v4.10.6";let Pr=Ct.create("warn",ur),Br=Ct.create("error",ur),{getI18n:Q,formats:J}=ol.VxeUI;class Ho{constructor(e,t,{renderHeader:r,renderCell:l,renderFooter:o,renderData:a}={}){var n=e.xegrid,i=t.formatter,s=!al().isBoolean(t.visible)||t.visible,c=e.props,d=["seq","checkbox","radio","expand","html"];t.type&&-1===d.indexOf(t.type)&&Pr("vxe.error.errProp",["type="+t.type,d.join(", ")]),(al().isBoolean(t.cellRender)||t.cellRender&&!al().isObject(t.cellRender))&&Pr("vxe.error.errProp",["column.cell-render="+t.cellRender,"column.cell-render={}"]),(al().isBoolean(t.editRender)||t.editRender&&!al().isObject(t.editRender))&&Pr("vxe.error.errProp",["column.edit-render="+t.editRender,"column.edit-render={}"]),t.cellRender&&t.editRender&&Pr("vxe.error.errConflicts",["column.cell-render","column.edit-render"]),"expand"===t.type&&(d=c.treeConfig,c=e.getComputeMaps().computeTreeOpts,e=c.value,d)&&(e.showLine||e.line)&&Br("vxe.error.errConflicts",["tree-config.showLine","column.type=expand"]),i&&(al().isString(i)?(c=J.get(i)||al()[i])&&al().isFunction(c.tableCellFormatMethod||c.cellFormatMethod)||Br("vxe.error.notFormats",[i]):!al().isArray(i)||(d=J.get(i[0])||al()[i[0]])&&al().isFunction(d.tableCellFormatMethod||d.cellFormatMethod)||Br("vxe.error.notFormats",[i[0]])),Object.assign(this,{type:t.type,property:t.field,field:t.field,title:t.title,width:t.width,minWidth:t.minWidth,maxWidth:t.maxWidth,resizable:t.resizable,fixed:t.fixed,align:t.align,headerAlign:t.headerAlign,footerAlign:t.footerAlign,showOverflow:t.showOverflow,showHeaderOverflow:t.showHeaderOverflow,showFooterOverflow:t.showFooterOverflow,className:t.className,headerClassName:t.headerClassName,footerClassName:t.footerClassName,formatter:i,footerFormatter:t.footerFormatter,padding:t.padding,verticalAlign:t.verticalAlign,sortable:t.sortable,sortBy:t.sortBy,sortType:t.sortType,filters:Cr(t.filters),filterMultiple:!al().isBoolean(t.filterMultiple)||t.filterMultiple,filterMethod:t.filterMethod,filterResetMethod:t.filterResetMethod,filterRecoverMethod:t.filterRecoverMethod,filterRender:t.filterRender,treeNode:t.treeNode,dragSort:t.dragSort,rowResize:t.rowResize,cellType:t.cellType,cellRender:t.cellRender,editRender:t.editRender,contentRender:t.contentRender,headerExportMethod:t.headerExportMethod,exportMethod:t.exportMethod,footerExportMethod:t.footerExportMethod,titleHelp:t.titleHelp,titlePrefix:t.titlePrefix,titleSuffix:t.titleSuffix,params:t.params,id:t.colId||al().uniqueId("col_"),parentId:null,visible:s,halfVisible:!1,defaultVisible:s,defaultFixed:t.fixed,checked:!1,halfChecked:!1,disabled:!1,level:1,rowSpan:1,colSpan:1,order:null,sortTime:0,sortNumber:0,renderSortNumber:0,renderFixed:"",renderVisible:!1,renderWidth:0,renderHeight:0,renderResizeWidth:0,renderAutoWidth:0,resizeWidth:0,renderLeft:0,renderArgs:[],model:{},renderHeader:r||t.renderHeader,renderCell:l||t.renderCell,renderFooter:o||t.renderFooter,renderData:a,slots:t.slots}),n&&(e=n.getComputeMaps().computeProxyOpts,(c=e.value).beforeColumn)&&c.beforeColumn({$grid:n,column:this})}getTitle(){return il(this.title||("seq"===this.type?Q("vxe.table.seqTitle"):""))}getKey(){var e=this.type;return this.field||(e?"type="+e:null)}update(e,t){"filters"!==e&&("field"===e&&(this.property=t),this[e]=t)}}let ee={},zr=al().browse(),t;function Sl(){return t||((t=new Image).src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="),t}function Il(){return t||Sl()}function yt(e,t){return e?al().isFunction(e)?e(t):e:""}function pr(e){return ee[e]||(ee[e]=new RegExp(`(?:^|\\s)${e}(?!\\S)`,"g")),ee[e]}function Dl(e){return e&&/^\d+(px)?$/.test(e)}function Ml(e){return e&&/^\d+%$/.test(e)}function ul(e,t){return e&&e.className&&e.className.match&&e.className.match(pr(t))}function pl(e,t){e&&ul(e,t)&&(e.className=e.className.replace(pr(t),""))}function hl(e,t){e&&!ul(e,t)&&(pl(e,t),e.className=e.className+" "+t)}function hr(e,t="px"){return al().isNumber(e)||/^\d+$/.test(""+e)?""+e+t:""+(e||"")}function mr(){var e=document.documentElement,t=document.body;return{scrollTop:e.scrollTop||t.scrollTop,scrollLeft:e.scrollLeft||t.scrollLeft,visibleHeight:e.clientHeight||t.clientHeight,visibleWidth:e.clientWidth||t.clientWidth}}function vr(e){return e?e.offsetHeight:0}function kl(e){return e?(e=getComputedStyle(e),al().toNumber(e.paddingTop)+al().toNumber(e.paddingBottom)):0}function ml(e,t){e&&(e.scrollTop=t)}function vl(e,t){e&&(e.scrollLeft=t)}function fr(e,t){t="html"===t.type?e.innerText:e.textContent;e.getAttribute("title")!==t&&e.setAttribute("title",t)}function fl(e,t,r,l){let o,a=e.target.shadowRoot&&e.composed&&e.composedPath()[0]||e.target;for(;a&&a.nodeType&&a!==document;){if(r&&ul(a,r)&&(!l||l(a)))o=a;else if(a===t)return{flag:!r||!!o,container:t,targetElem:o};a=a.parentNode}return{flag:!1}}function Fl(e,t){return function e(t,r,l){if(t){var o=t.parentNode;if(l.top+=t.offsetTop,l.left+=t.offsetLeft,o&&o!==document.documentElement&&o!==document.body&&(l.top-=o.scrollTop,l.left-=o.scrollLeft),(!r||t!==r&&t.offsetParent!==r)&&t.offsetParent)return e(t.offsetParent,r,l)}return l}(e,t,{left:0,top:0})}function gr(e){var e=e.getBoundingClientRect(),t=e.top,e=e.left,{scrollTop:r,scrollLeft:l,visibleHeight:o,visibleWidth:a}=mr();return{boundingTop:t,top:r+t,boundingLeft:e,left:l+e,visibleHeight:o,visibleWidth:a}}let te="scrollIntoViewIfNeeded",re="scrollIntoView",le=(e,t)=>{let r=[];return e.forEach(e=>{e.parentId=t?t.id:null,e.visible&&(e.children&&e.children.length&&e.children.some(e=>e.visible)?(r.push(e),r.push(...le(e.children,e))):r.push(e))}),r};function Ol(e,t,r){let l=e.internalData;return e.clearScroll().then(()=>{if(t||r)return l.lastScrollLeft=0,l.lastScrollTop=0,l.intoRunScroll=!1,l.inVirtualScroll=!1,l.inWheelScroll=!1,l.inHeaderScroll=!1,l.inBodyScroll=!1,l.inFooterScroll=!1,l.scrollRenderType="",e.scrollTo(t,r)})}function Al(){return al().uniqueId("row_")}function gl(e){var t=e.props,e=e.getComputeMaps().computeRowOpts,t=t.rowId,e=e.value;return t||e.keyField||"_X_ROW_KEY"}function xl(e,t){t=al().get(t,gl(e));return al().eqNull(t)?"":encodeURIComponent(t)}let jr=(e,t)=>t?al().isString(t)||al().isNumber(t)?e.getColumnByField(""+t):t:null;function xr(e){return e?(e=getComputedStyle(e),al().toNumber(e.paddingLeft)+al().toNumber(e.paddingRight)):0}function br(e){var t,r;return e?(r=getComputedStyle(e),t=al().toNumber(r.marginLeft),r=al().toNumber(r.marginRight),e.offsetWidth+t+r):0}function Tt(e,t){return e.querySelector(".vxe-cell"+t)}function Cr(e){return e&&al().isArray(e)?e.map(({label:e,value:t,data:r,resetValue:l,checked:o})=>({label:e,value:t,data:r,resetValue:l,checked:!!o,_checked:!!o})):e}function bl(e,t){return al().get(e,t.field)}function Cl(e,t,r){al().set(e,t.field,r)}function wl(e){if(e){e=e.value;if(e)return e.$el||e}return null}function wr(e){return"unset"!==e&&e||0}function Ll(e){var{$table:t,column:r,cell:l}=e,o=t.props,a=t.internalData,t=t.getComputeMaps().computeResizableOpts,t=t.value.minWidth;if(t){e=al().isFunction(t)?t(e):t;if("auto"!==e)return Math.max(1,al().toNumber(e))}var n,i,s,c,t=a.elemStore,e=o.showHeaderOverflow,{showHeaderOverflow:a,minWidth:o}=r,r=al().isUndefined(a)||al().isNull(a)?e:a,e="title"===r||(!0===r||"tooltip"===r)||"ellipsis"===r;let d=al().floor(1.6*(al().toNumber(getComputedStyle(l).fontSize)||14))+(xr(l)+xr(Tt(l,"")));if(e&&(a=xr(Tt(l,">.vxe-cell--drag-handle")),r=xr(Tt(l,">.vxe-cell--checkbox")),e=br(Tt(l,">.vxe-cell--required-icon")),n=br(Tt(l,">.vxe-cell--edit-icon")),i=br(Tt(l,">.vxe-cell-title-prefix-icon")),s=br(Tt(l,">.vxe-cell-title-suffix-icon")),c=br(Tt(l,">.vxe-cell--sort")),l=br(Tt(l,">.vxe-cell--filter")),d+=a+r+e+n+i+s+l+c),o){a=wl(t["main-body-scroll"]);if(a){if(Ml(o))return r=(a.clientWidth-1)/100,Math.max(d,Math.floor(al().toInteger(o)*r));if(Dl(o))return Math.max(d,al().toInteger(o))}}return d}function yr(e){return e&&(e.constructor===Ho||e instanceof Ho)}function Tr(r,e,l){Object.keys(e).forEach(t=>{(0,dl.watch)(()=>e[t],e=>{l.update(t,e),r&&("filters"===t?(r.setFilter(l,e),r.handleUpdateDataQueue()):["visible","fixed","width","minWidth","maxWidth"].includes(t)&&r.handleRefreshColumnQueue())})})}function Er(e,t,r,l){var e=e.reactData,o=e.staticColumns,a=t.parentNode,l=l?l.columnConfig:null,l=l?l.children:o;a&&l&&(l.splice(al().arrayIndexOf(a.children,t),0,r),e.staticColumns=o.slice(0))}function Rr(e,t){var e=e.reactData,r=e.staticColumns,l=al().findTree(r,e=>e.id===t.id,{children:"children"});l&&l.items.splice(l.index,1),e.staticColumns=r.slice(0)}function Vl(e,t){var e=e.internalData,r=e.fullColumnIdData;if(!t)return null;let l=t.parentId;for(;r[l];){let e=r[l].column;if(!(l=e.parentId))return e}return t}let oe={mini:3,small:2,medium:1},ae=(e,t)=>{let r=1;if(e){var l=t.$table,o=l.getComputeMaps().computeTreeOpts,o=o.value,{transform:a,mapChildrenField:n}=o,o=o.children||o.childrenField,i=e[a?n:o];if(i&&l.isTreeExpandByRow(e))for(let e=0;e<i.length;e++)r+=ae(i[e],t)}return r},ne=e=>{e=e.getComputeMaps().computeSize,e=e.value;return e&&oe[e]||0};function Sr(t,r,l){for(let e=0;e<t.length;e++){var{row:o,col:a,rowspan:n,colspan:i}=t[e];if(-1<a&&-1<o&&n&&i){if(o===r&&a===l)return{rowspan:n,colspan:i};if(o<=r&&r<o+n&&a<=l&&l<a+i)return{rowspan:0,colspan:0}}}}function $l(r,l){var e=r.props,t=r.reactData,o=r.internalData,{computeLeftFixedWidth:a,computeRightFixedWidth:n,computeRowOpts:i,computeCellOpts:s,computeDefaultRowHeight:c}=r.getComputeMaps(),e=e.showOverflow,t=t.scrollYLoad,{elemStore:o,afterFullData:d,fullAllDataRowIdData:u,isResizeCellHeight:p}=o,h=i.value,m=s.value,v=c.value,i=a.value,s=n.value,c=wl(o["main-body-scroll"]),f=xl(r,l);if(c){a=c.clientHeight,n=c.scrollTop,o=c.querySelector(`[rowid="${f}"]`);if(o){c=o.offsetParent,c=o.offsetTop+(c?c.offsetTop:0),o=o.clientHeight;if(c<n||n+a<c)return r.scrollTo(null,c);if(a+n<=c+o)return r.scrollTo(null,n+o)}else if(t){if(!(p||m.height||h.height)&&e)return r.scrollTo(null,(r.findRowIndexOf(d,l)-1)*v);let t=0;c=u[f]||{},o=c&&(c.resizeHeight||m.height||h.height)||v;for(let e=0;e<d.length;e++){var g=d[e],x=xl(r,g);if(g===l||x===f)break;g=u[x]||{};t+=g.resizeHeight||g.height||m.height||h.height||v}return t<n?r.scrollTo(null,t-i-1):r.scrollTo(null,t+o-(a-s-1))}}return Promise.resolve()}function _l(r,l,t){var o=r.reactData,a=r.internalData,{computeLeftFixedWidth:n,computeRightFixedWidth:i}=r.getComputeMaps(),o=o.scrollXLoad,{elemStore:a,visibleColumn:s}=a,n=n.value,i=i.value,a=wl(a["main-body-scroll"]);if(!l.fixed&&a){var c=a.clientWidth,d=a.scrollLeft;let e=null;if(t&&(t=xl(r,t),e=a.querySelector(`[rowid="${t}"] .`+l.id)),e=e||a.querySelector("."+l.id)){t=e.offsetParent,a=e.offsetLeft+(t?t.offsetLeft:0),t=e.clientWidth;if(a<d+n)return r.scrollTo(a-n-1);if(c-i<a+t-d)return r.scrollTo(a+t-(c-i-1))}else if(o){let t=0;a=l.renderWidth;for(let e=0;e<s.length;e++){var u=s[e];if(u===l||u.id===l.id)break;t+=u.renderWidth}return t<d?r.scrollTo(t-n-1):r.scrollTo(t+a-(c-i-1))}}return Promise.resolve()}function Ir(e){return"on"+e.substring(0,1).toLocaleUpperCase()+e.substring(1)}function Dr(e){switch(e.name){case"input":case"textarea":return"input";case"select":return"change"}return"update:modelValue"}function Mr(e){switch(e.name){case"input":case"textarea":case"VxeInput":case"VxeNumberInput":case"VxeTextarea":case"$input":case"$textarea":return"input"}return"change"}function yl(e){return null==e?[]:al().isArray(e)?e:[e]}let{getI18n:h,getIcon:E,renderer:u,formats:ie,renderEmptyElement:x}=ol.VxeUI;function kr(t){let{$table:r,column:e}=t;var l=r.context,l=l.slots,o=e.slots,a=r.props.dragConfig,n=r.getComputeMaps().computeRowDragOpts,{icon:n,trigger:i,disabledMethod:s}=n.value,s=s||(a?a.rowDisabledMethod:null);let c=s&&s(t);s=(o?o.rowDragIcon||o["row-drag-icon"]:null)||l.rowDragIcon||l["row-drag-icon"],o={};return"cell"!==i&&(o.onMousedown=e=>{c||r.handleCellDragMousedownEvent(e,t)},o.onMouseup=r.handleCellDragMouseupEvent),(0,dl.h)("span",{key:"dg",class:["vxe-cell--drag-handle",{"is--disabled":c}],...o},s?r.callSlot(s,t):[(0,dl.h)("i",{class:n||(a?a.rowIcon:"")||E().TABLE_DRAG_ROW})])}function Et(e,t){var{$table:r,column:l,level:o}=e,l=l.dragSort,{treeConfig:a,dragConfig:n}=r.props,{computeRowOpts:r,computeRowDragOpts:i}=r.getComputeMaps(),r=r.value,{showIcon:i,isPeerDrag:s,isCrossDrag:c,visibleMethod:d}=i.value,d=d||(n?n.rowVisibleMethod:null),t=al().isArray(t)?t:[t];return!(l&&r.drag&&(i||n&&n.showRowIcon))||d&&!d(e)||a&&!s&&!c&&o||t.unshift(kr(e)),t}function Rt(e,t){return[(t=>{let{$table:r,column:e}=t,l=e.titlePrefix||e.titleHelp;return l?(0,dl.h)("i",{class:["vxe-cell-title-prefix-icon",l.icon||E().TABLE_TITLE_PREFIX],onMouseenter(e){r.triggerHeaderTitleEvent(e,l,t)},onMouseleave(e){r.handleTargetLeaveEvent(e)}}):x(r)})(e),(r=>{let{$table:l,column:e}=r;var o=(o=l.context).slots,a=e.slots,{computeColumnOpts:n,computeColumnDragOpts:i}=l.getComputeMaps(),n=n.value,{showIcon:i,icon:s,trigger:c,isPeerDrag:t,isCrossDrag:d,visibleMethod:u,disabledMethod:p}=i.value;if(!n.drag||!i||u&&!u(r)||e.fixed||!t&&!d&&e.parentId)return x(l);{let t=p&&p(r);n=(a?a.columnDragIcon||a["column-drag-icon"]:null)||o.columnDragIcon||o["column-drag-icon"],i={};return"cell"!==c&&(i.onMousedown=e=>{t||l.handleHeaderCellDragMousedownEvent(e,r)},i.onMouseup=l.handleHeaderCellDragMouseupEvent),(0,dl.h)("span",{key:"dg",class:["vxe-cell--drag-handle",{"is--disabled":t}],...i},n?l.callSlot(n,r):[(0,dl.h)("i",{class:s||E().TABLE_DRAG_COLUMN})])}})(e),...al().isArray(t)?t:[t],(t=>{let{$table:r,column:e}=t,l=e.titleSuffix;return l?(0,dl.h)("i",{class:["vxe-cell-title-suffix-icon",l.icon||E().TABLE_TITLE_SUFFIX],onMouseenter(e){r.triggerHeaderTitleEvent(e,l,t)},onMouseleave(e){r.handleTargetLeaveEvent(e)}}):x(r)})(e)]}function St(t,e){let{$table:r,column:l}=t;var o=r.props;let a=r.reactData;var n=r.getComputeMaps().computeTooltipOpts,o=o.showHeaderOverflow,{type:i,showHeaderOverflow:s}=l;let c=n.value.showAll;n=al().isUndefined(s)||al().isNull(s)?o:s;let d="title"===n,u=!0===n||"tooltip"===n;o={};return(d||u||c)&&(o.onMouseenter=e=>{a.isDragResize||(d?fr(e.currentTarget,l):(u||c)&&r.triggerHeaderTooltipEvent(e,t))}),(u||c)&&(o.onMouseleave=e=>{a.isDragResize||(u||c)&&r.handleTargetLeaveEvent(e)}),["html"===i&&al().isString(e)?(0,dl.h)("span",{class:"vxe-cell--title",innerHTML:e,...o}):(0,dl.h)("span",{class:"vxe-cell--title",...o},yl(e))]}function Fr(e){var{$table:e,row:t,column:r}=e;return sl(e.getCellLabel(t,r),1)}function Or(e){var{column:t,$table:r}=e,l=r.props.editConfig,{type:t,treeNode:o,editRender:a}=t,{computeEditOpts:r,computeCheckboxOpts:n}=r.getComputeMaps(),i=n.value,n=r.value;switch(t){case"seq":return o?p.renderTreeIndexCell(e):p.renderSeqCell(e);case"radio":return o?p.renderTreeRadioCell(e):p.renderRadioCell(e);case"checkbox":return i.checkField?o?p.renderTreeSelectionCellByProp(e):p.renderCheckboxCellByProp(e):o?p.renderTreeSelectionCell(e):p.renderCheckboxCell(e);case"expand":return p.renderExpandCell(e);case"html":return o?p.renderTreeHTMLCell(e):p.renderHTMLCell(e)}return l&&a?"cell"===n.mode?o?p.renderTreeCellEdit(e):p.renderCellEdit(e):o?p.renderTreeRowEdit(e):p.renderRowEdit(e):o?p.renderTreeCell(e):p.renderDefaultCell(e)}function Ar(e){var{column:t,$table:r}=e,r=r.props.editConfig,{type:t,filters:l,sortable:o,editRender:a}=t;switch(t){case"seq":return p.renderSeqHeader(e);case"radio":return p.renderRadioHeader(e);case"checkbox":return p.renderCheckboxHeader(e);case"html":if(l&&o)return p.renderSortAndFilterHeader(e);if(o)return p.renderSortHeader(e);if(l)return p.renderFilterHeader(e)}return r&&a?p.renderEditHeader(e):l&&o?p.renderSortAndFilterHeader(e):o?p.renderSortHeader(e):l?p.renderFilterHeader(e):p.renderDefaultHeader(e)}function Lr(e){return p.renderDefaultFooter(e)}let p={createColumn(e,t){var r=t.type,l={renderHeader:Ar,renderCell:Or,renderFooter:Lr};return"expand"===r&&(l.renderData=p.renderExpandData),r=e,e=l,yr(l=t)?l:(0,dl.reactive)(new Ho(r,l,e))},renderHeaderTitle(e){var{$table:t,column:r}=e,{slots:l,editRender:o,cellRender:a}=r,o=o||a,a=l?l.header:null;if(a)return St(e,t.callSlot(a,e));if(o){l=u.get(o.name);if(l){t=l.renderTableHeader||l.renderHeader;if(t)return St(e,yl(t(o,e)))}}return St(e,sl(r.getTitle(),1))},renderDefaultHeader(e){return Rt(e,p.renderHeaderTitle(e))},renderDefaultCell(e){var{$table:t,row:r,column:l}=e,{slots:o,editRender:a,cellRender:n}=l,n=a||n,o=o?o.default:null;if(o)return Et(e,t.callSlot(o,e));if(n){o=u.get(n.name);if(o){var i=o.renderTableCell||o.renderCell,o=o.renderTableDefault||o.renderDefault,i=a?i:o;if(i)return Et(e,yl(i(n,Object.assign({$type:a?"edit":"cell"},e))))}}o=t.getCellLabel(r,l),i=a?a.placeholder:"";return Et(e,[(0,dl.h)("span",{class:"vxe-cell--label"},[a&&cl(o)?(0,dl.h)("span",{class:"vxe-cell--placeholder"},sl(il(i),1)):(0,dl.h)("span",sl(o,1))])])},renderTreeCell(e){return p.renderTreeIcon(e,p.renderDefaultCell(e))},renderDefaultFooter(t){{var{$table:r,column:l,_columnIndex:o,items:a,row:n}=t,{slots:i,editRender:s,cellRender:c,footerFormatter:d}=l,s=s||c;if(c=i?i.footer:null)return r.callSlot(c,t);let e="";if(e=al().isArray(a)?a[o]:al().get(n,l.field),i=Object.assign(t,{itemValue:e}),d)return al().isFunction(d)?""+d(i):(a=(c=(r=al().isArray(d))?ie.get(d[0]):ie.get(d))?c.tableFooterCellFormatMethod:null)?""+(r?a(i,...d.slice(1)):a(i)):"";if(s){o=u.get(s.name);if(o){n=o.renderTableFooter||o.renderFooter;if(n)return yl(n(s,i))}}return[sl(e,1)]}},renderTreeIcon(t,e){let{$table:r,isHidden:l}=t;var o=r.reactData,a=r.internalData,n=r.getComputeMaps().computeTreeOpts,{treeExpandedMaps:o,treeExpandLazyLoadedMaps:i}=o,a=a.fullAllDataRowIdData,n=n.value,{row:s,column:c,level:d}=t,c=c.slots,{indent:u,lazy:p,trigger:h,iconLoaded:m,showIcon:v,iconOpen:f,iconClose:g}=n,x=n.children||n.childrenField,n=n.hasChild||n.hasChildField,x=s[x],x=x&&x.length,c=c?c.icon:null;let b=!1,C=!1,w=!1,y=!1;var T={};return c?r.callSlot(c,t):(l||(c=xl(r,s),C=!!o[c],p&&(o=a[c],w=!!i[c],b=s[n],y=!!o.treeLoaded)),h&&"default"!==h||(T.onClick=e=>{r.triggerTreeExpandEvent(e,t)}),[(0,dl.h)("div",{class:["vxe-cell--tree-node",{"is--active":C}],style:{paddingLeft:d*u+"px"}},[v&&(!p||y?x:x||b)?[(0,dl.h)("div",{class:"vxe-tree--btn-wrapper",...T},[(0,dl.h)("i",{class:["vxe-tree--node-btn",w?m||E().TABLE_TREE_LOADED:C?f||E().TABLE_TREE_OPEN:g||E().TABLE_TREE_CLOSE]})])]:null,(0,dl.h)("div",{class:"vxe-tree-cell"},e)])])},renderSeqHeader(e){var{$table:t,column:r}=e,l=r.slots,l=l?l.header:null;return Rt(e,St(e,l?t.callSlot(l,e):sl(r.getTitle(),1)))},renderSeqCell(e){var{$table:t,column:r}=e,l=t.props.treeConfig,o=t.getComputeMaps().computeSeqOpts,o=o.value,r=r.slots,r=r?r.default:null;return r?Et(e,t.callSlot(r,e)):(t=e.seq,r=o.seqMethod,Et(e,[(0,dl.h)("span",""+sl(r?r(e):l?t:(o.startIndex||0)+t,1))]))},renderTreeIndexCell(e){return p.renderTreeIcon(e,p.renderSeqCell(e))},renderRadioHeader(e){var{$table:t,column:r}=e,l=r.slots,o=l?l.header:null,l=l?l.title:null;return Rt(e,St(e,o?t.callSlot(o,e):[(0,dl.h)("span",{class:"vxe-radio--label"},l?t.callSlot(l,e):sl(r.getTitle(),1))]))},renderRadioCell(t){let{$table:r,column:e,isHidden:l}=t;var o=r.reactData,a=r.getComputeMaps().computeRadioOpts,o=o.selectRadioRow,n=e.slots,{labelField:a,checkMethod:i,visibleMethod:s}=a.value,c=t.row,d=n?n.default:null,n=n?n.radio:null,o=r.eqRow(c,o);let u=!s||s({row:c}),p=!!i,h;l||(h={onClick(e){!p&&u&&r.triggerRadioRowEvent(e,t)}},i&&(p=!i({row:c})));s={...t,checked:o,disabled:p,visible:u};return n?Et(t,r.callSlot(n,s)):(i=[],u&&i.push((0,dl.h)("span",{class:["vxe-radio--icon",o?E().TABLE_RADIO_CHECKED:E().TABLE_RADIO_UNCHECKED]})),(d||a)&&i.push((0,dl.h)("span",{class:"vxe-radio--label"},d?r.callSlot(d,s):al().get(c,a))),Et(t,[(0,dl.h)("span",{class:["vxe-cell--radio",{"is--checked":o,"is--disabled":p}],...h},i)]))},renderTreeRadioCell(e){return p.renderTreeIcon(e,p.renderRadioCell(e))},renderCheckboxHeader(e){let{$table:t,column:r,isHidden:l}=e;var o=t.reactData,{computeIsAllCheckboxDisabled:a,computeCheckboxOpts:n}=t.getComputeMaps();let{isAllSelected:i,isIndeterminate:s}=o,c=a.value;var o=r.slots,a=o?o.header:null,o=o?o.title:null,n=n.value,d=r.getTitle(),u={},p=(l||(u.onClick=e=>{c||t.triggerCheckAllEvent(e,!i)}),{...e,checked:i,disabled:c,indeterminate:s});return a?Rt(e,St(p,t.callSlot(a,p))):(n.checkStrictly?n.showHeader:!1!==n.showHeader)?Rt(e,St(p,[(0,dl.h)("span",{class:["vxe-cell--checkbox",{"is--checked":i,"is--disabled":c,"is--indeterminate":s}],title:h("vxe.table.allTitle"),...u},[(0,dl.h)("span",{class:["vxe-checkbox--icon",s?E().TABLE_CHECKBOX_INDETERMINATE:i?E().TABLE_CHECKBOX_CHECKED:E().TABLE_CHECKBOX_UNCHECKED]})].concat(o||d?[(0,dl.h)("span",{class:"vxe-checkbox--label"},o?t.callSlot(o,p):d)]:[]))])):Rt(e,St(p,[(0,dl.h)("span",{class:"vxe-checkbox--label"},o?t.callSlot(o,p):d)]))},renderCheckboxCell(t){let{$table:r,row:e,column:l,isHidden:o}=t;var a=r.props.treeConfig,{selectCheckboxMaps:n,treeIndeterminateMaps:i}=r.reactData,s=r.getComputeMaps().computeCheckboxOpts,{labelField:s,checkMethod:c,visibleMethod:d}=s.value,u=l.slots,p=u?u.default:null,u=u?u.checkbox:null;let h=!1,m=!1,v=!d||d({row:e}),f=!!c;var g,d={},n=(o||(g=xl(r,e),m=!!n[g],d.onClick=e=>{!f&&v&&r.triggerCheckRowEvent(e,t,!m)},c&&(f=!c({row:e})),a&&(h=!!i[g])),{...t,checked:m,disabled:f,visible:v,indeterminate:h});return u?Et(t,r.callSlot(u,n)):(c=[],v&&c.push((0,dl.h)("span",{class:["vxe-checkbox--icon",h?E().TABLE_CHECKBOX_INDETERMINATE:m?E().TABLE_CHECKBOX_CHECKED:E().TABLE_CHECKBOX_UNCHECKED]})),(p||s)&&c.push((0,dl.h)("span",{class:"vxe-checkbox--label"},p?r.callSlot(p,n):al().get(e,s))),Et(t,[(0,dl.h)("span",{class:["vxe-cell--checkbox",{"is--checked":m,"is--disabled":f,"is--indeterminate":h,"is--hidden":!v}],...d},c)]))},renderTreeSelectionCell(e){return p.renderTreeIcon(e,p.renderCheckboxCell(e))},renderCheckboxCellByProp(t){let{$table:r,row:e,column:l,isHidden:o}=t;var a=r.props.treeConfig,n=r.reactData.treeIndeterminateMaps,i=r.getComputeMaps().computeCheckboxOpts,i=i.value,{labelField:s,checkField:c,checkMethod:d,visibleMethod:u}=i,i=i.indeterminateField||i.halfField,p=l.slots,h=p?p.default:null,p=p?p.checkbox:null;let m=!1,v=!1,f=!u||u({row:e}),g=!!d;var x,u={},c=(o||(x=xl(r,e),v=al().get(e,c),u.onClick=e=>{!g&&f&&r.triggerCheckRowEvent(e,t,!v)},d&&(g=!d({row:e})),a&&(m=!!n[x])),{...t,checked:v,disabled:g,visible:f,indeterminate:m});return p?Et(t,r.callSlot(p,c)):(d=[],f&&(d.push((0,dl.h)("span",{class:["vxe-checkbox--icon",m?E().TABLE_CHECKBOX_INDETERMINATE:v?E().TABLE_CHECKBOX_CHECKED:E().TABLE_CHECKBOX_UNCHECKED]})),h||s)&&d.push((0,dl.h)("span",{class:"vxe-checkbox--label"},h?r.callSlot(h,c):al().get(e,s))),Et(t,[(0,dl.h)("span",{class:["vxe-cell--checkbox",{"is--checked":v,"is--disabled":g,"is--indeterminate":i&&!v?e[i]:m,"is--hidden":!f}],...u},d)]))},renderTreeSelectionCellByProp(e){return p.renderTreeIcon(e,p.renderCheckboxCellByProp(e))},renderExpandCell(t){let{$table:r,isHidden:e,row:l,column:o}=t;var{rowExpandedMaps:a,rowExpandLazyLoadedMaps:n}=r.reactData,i=r.getComputeMaps().computeExpandOpts,{lazy:i,labelField:s,iconLoaded:c,showIcon:d,iconOpen:u,iconClose:p,visibleMethod:h}=i.value,m=o.slots,v=m?m.default:null,m=m?m.icon:null;let f=!1,g=!1;return m?Et(t,r.callSlot(m,t)):(e||(m=xl(r,l),f=!!a[m],i&&(g=!!n[m])),Et(t,[!d||h&&!h(t)?x(r):(0,dl.h)("span",{class:["vxe-table--expanded",{"is--active":f}],onClick(e){r.triggerRowExpandEvent(e,t)}},[(0,dl.h)("i",{class:["vxe-table--expand-btn",g?c||E().TABLE_EXPAND_LOADED:f?u||E().TABLE_EXPAND_OPEN:p||E().TABLE_EXPAND_CLOSE]})]),v||s?(0,dl.h)("span",{class:"vxe-table--expand-label"},v?r.callSlot(v,t):al().get(l,s)):x(r)]))},renderExpandData(e){var{$table:t,column:r}=e,{slots:r,contentRender:l}=r,r=r?r.content:null;if(r)return t.callSlot(r,e);if(l){t=u.get(l.name);if(t){r=t.renderTableExpand||t.renderExpand;if(r)return yl(r(l,e))}}return[]},renderHTMLCell(e){var{$table:t,column:r}=e,r=r.slots,r=r?r.default:null;return Et(e,r?t.callSlot(r,e):[(0,dl.h)("span",{class:"vxe-cell--html",innerHTML:Fr(e)})])},renderTreeHTMLCell(e){return p.renderTreeIcon(e,p.renderHTMLCell(e))},renderSortAndFilterHeader(e){return Rt(e,p.renderHeaderTitle(e).concat(p.renderSortIcon(e).concat(p.renderFilterIcon(e))))},renderSortHeader(e){return Rt(e,p.renderHeaderTitle(e).concat(p.renderSortIcon(e)))},renderSortIcon(e){let{$table:t,column:r}=e;var l=t.getComputeMaps().computeSortOpts,{showIcon:l,allowBtn:o,iconLayout:a,iconAsc:n,iconDesc:i,iconVisibleMethod:s}=l.value,c=r.order;return!l||s&&!s(e)?[]:[(0,dl.h)("span",{class:["vxe-cell--sort",`vxe-cell--sort-${a}-layout`]},[(0,dl.h)("i",{class:["vxe-sort--asc-btn",n||E().TABLE_SORT_ASC,{"sort--active":"asc"===c}],title:h("vxe.table.sortAsc"),onClick:o?e=>{e.stopPropagation(),t.triggerSortEvent(e,r,"asc")}:void 0}),(0,dl.h)("i",{class:["vxe-sort--desc-btn",i||E().TABLE_SORT_DESC,{"sort--active":"desc"===c}],title:h("vxe.table.sortDesc"),onClick:o?e=>{e.stopPropagation(),t.triggerSortEvent(e,r,"desc")}:void 0})])]},renderFilterHeader(e){return Rt(e,p.renderHeaderTitle(e).concat(p.renderFilterIcon(e)))},renderFilterIcon(t){let{$table:r,column:e,hasFilter:l}=t;var o=r.reactData.filterStore,a=r.getComputeMaps().computeFilterOpts,{showIcon:a,iconNone:n,iconMatch:i,iconVisibleMethod:s}=a.value;return!a||s&&!s(t)?[]:[(0,dl.h)("span",{class:["vxe-cell--filter",{"is--active":o.visible&&o.column===e}]},[(0,dl.h)("i",{class:["vxe-filter--btn",l?i||E().TABLE_FILTER_MATCH:n||E().TABLE_FILTER_NONE],title:h("vxe.table.filter"),onClick(e){r.triggerFilterEvent&&r.triggerFilterEvent(e,t.column,t)}})])]},renderEditHeader(e){var{$table:t,column:r}=e,l=t.props,o=t.getComputeMaps().computeEditOpts,{editConfig:l,editRules:a}=l,o=o.value,{sortable:n,filters:i,editRender:s}=r;let c=!1,d=(a&&(a=al().get(a,r.field))&&(c=a.some(e=>e.required)),[]);return Rt(e,(d=nl(l)?[c&&o.showAsterisk?(0,dl.h)("i",{class:"vxe-cell--required-icon"}):x(t),nl(s)&&o.showIcon?(0,dl.h)("i",{class:["vxe-cell--edit-icon",o.icon||E().TABLE_EDIT]}):x(t)]:d).concat(p.renderHeaderTitle(e)).concat(n?p.renderSortIcon(e):[]).concat(i?p.renderFilterIcon(e):[]))},renderRowEdit(e){var{$table:t,column:r}=e,t=t.reactData.editStore,t=t.actived,r=r.editRender;return p.runRenderer(e,nl(r)&&t&&t.row===e.row)},renderTreeRowEdit(e){return p.renderTreeIcon(e,p.renderRowEdit(e))},renderCellEdit(e){var{$table:t,column:r}=e,t=t.reactData.editStore,t=t.actived,r=r.editRender;return p.runRenderer(e,nl(r)&&t&&t.row===e.row&&t.column===e.column)},renderTreeCellEdit(e){return p.renderTreeIcon(e,p.renderCellEdit(e))},runRenderer(e,t){var{$table:r,column:l}=e,{slots:l,editRender:o,formatter:a}=l,n=l?l.default:null,l=l?l.edit:null,i=u.get(o.name),i=i?i.renderTableEdit||i.renderEdit:null,s=Object.assign({$type:"",isEdit:t},e);return t?(s.$type="edit",l?r.callSlot(l,s):i?yl(i(o,s)):[]):n?Et(e,r.callSlot(n,s)):a?Et(e,[(0,dl.h)("span",{class:"vxe-cell--label"},Fr(s))]):p.renderDefaultCell(s)}};var Hl=p,Ct={colId:[String,Number],type:String,field:String,title:String,width:[Number,String],minWidth:[Number,String],maxWidth:[Number,String],resizable:{type:Boolean,default:null},fixed:String,align:String,headerAlign:String,footerAlign:String,showOverflow:{type:[Boolean,String],default:null},showHeaderOverflow:{type:[Boolean,String],default:null},showFooterOverflow:{type:[Boolean,String],default:null},className:[String,Function],headerClassName:[String,Function],footerClassName:[String,Function],formatter:[Function,Array,String],footerFormatter:[Function,Array,String],padding:{type:Boolean,default:null},verticalAlign:{type:String,default:null},sortable:Boolean,sortBy:[String,Function],sortType:String,filters:{type:Array,default:null},filterMultiple:{type:Boolean,default:!0},filterMethod:Function,filterResetMethod:Function,filterRecoverMethod:Function,filterRender:Object,treeNode:Boolean,dragSort:Boolean,rowResize:Boolean,visible:{type:Boolean,default:null},headerExportMethod:Function,exportMethod:Function,footerExportMethod:Function,titleHelp:Object,titlePrefix:Object,titleSuffix:Object,cellType:String,cellRender:Object,editRender:Object,contentRender:Object,params:Object},It=(0,dl.defineComponent)({name:"VxeColumn",props:Ct,setup(e,{slots:t}){let r=(0,dl.ref)(),l=(0,dl.inject)("$xeTable",null),o=(0,dl.inject)("$xeColgroup",null);if(!l)return()=>(0,dl.createCommentVNode)();let a=Hl.createColumn(l,e);a.slots=t;var t=()=>(0,dl.h)("div",{ref:r}),n={columnConfig:a,renderVN:t};return Tr(l,e,a),(0,dl.onMounted)(()=>{var e=r.value;e&&Er(l,e,a,o)}),(0,dl.onUnmounted)(()=>{Rr(l,a)}),(0,dl.provide)("$xeColumn",n),(0,dl.provide)("$xeGrid",null),t}});let r=Object.assign({},It,{install(e){e.component(It.name,It),e.component("VxeTableColumn",It)}}),de=(ol.VxeUI.dynamicApp&&(ol.VxeUI.dynamicApp.component(It.name,It),ol.VxeUI.dynamicApp.component("VxeTableColumn",It)),ol.VxeUI.component(It),r);var Dt=(0,dl.defineComponent)({name:"VxeColgroup",props:Ct,setup(e,{slots:t}){let r=(0,dl.ref)(),l=(0,dl.inject)("$xeTable",null),o=(0,dl.inject)("$xeColgroup",null);if(!l)return()=>(0,dl.createCommentVNode)();let a=Hl.createColumn(l,e);var n={},n=(t.header&&(n.header=t.header),a.slots=n,a.children=[],Tr(l,e,a),(0,dl.onMounted)(()=>{var e=r.value;e&&Er(l,e,a,o)}),(0,dl.onUnmounted)(()=>{Rr(l,a)}),{columnConfig:a});return(0,dl.provide)("$xeColgroup",n),(0,dl.provide)("$xeGrid",null),()=>(0,dl.h)("div",{ref:r},t.default?t.default():[])}});let l=Object.assign({},Dt,{install(e){e.component(Dt.name,Dt),e.component("VxeTableColgroup",Dt)}}),ue=(ol.VxeUI.dynamicApp&&(ol.VxeUI.dynamicApp.component(Dt.name,Dt),ol.VxeUI.dynamicApp.component("VxeTableColgroup",Dt)),ol.VxeUI.component(Dt),l),{getI18n:me,renderer:Qe,renderEmptyElement:ve}=(bt(4520),bt(3215),bt(2577),bt(8872),bt(1795),ol.VxeUI),Ze="body";var Nl=(0,dl.defineComponent)({name:"VxeTableBody",props:{tableData:Array,tableColumn:Array,fixedColumn:Array,fixedType:{type:String,default:""}},setup(F){let Ae=(0,dl.inject)("$xeTable",{}),{xID:O,props:Le,context:A,reactData:Ve,internalData:$e}=Ae,{computeEditOpts:_e,computeMouseOpts:He,computeAreaOpts:Ne,computeDefaultRowHeight:Pe,computeEmptyOpts:L,computeTooltipOpts:Be,computeRadioOpts:e,computeExpandOpts:Z,computeTreeOpts:u,computeCheckboxOpts:ze,computeCellOpts:je,computeValidOpts:Ue,computeRowOpts:We,computeColumnOpts:qe,computeRowDragOpts:Xe,computeColumnDragOpts:t,computeLeftFixedWidth:V,computeRightFixedWidth:$,computeResizableOpts:Ke}=Ae.getComputeMaps(),_=(0,dl.ref)(),H=(0,dl.ref)(),N=(0,dl.ref)(),P=(0,dl.ref)(),B=(0,dl.ref)(),z=(0,dl.ref)(),j=(0,dl.ref)(),U=(0,dl.ref)(),Ye=()=>{var e=Le.delayHover,{lastScrollTime:t,isDragResize:r}=Ve;return!!(r||t&&Date.now()<t+e)},Ge=(e,t)=>{var{row:r,column:l}=e,o=$e.afterFullData,a=Le.treeConfig,n=u.value,{slots:l,treeNode:i}=l,s=$e.fullAllDataRowIdData;if(l&&l.line)return Ae.callSlot(l.line,e);l=s[xl(Ae,r)];let c=0,d=null;l&&(c=l.level,d=l.items[l.treeIndex-1]);s=Ae.eqRow(o[0],r);return a&&i&&(n.showLine||n.line)?[(0,dl.h)("div",{key:"tl",class:"vxe-tree--line-wrapper"},[(0,dl.h)("div",{class:"vxe-tree--line",style:{height:`${s?1:((e,t)=>{var{$table:r,row:l}=e,o=r.props.showOverflow,a=r.reactData.scrollYLoad,n=r.internalData.fullAllDataRowIdData,{computeRowOpts:i,computeCellOpts:s,computeDefaultRowHeight:c}=r.getComputeMaps(),i=i.value,s=s.value,c=c.value,l=(n=n[xl(r,l)]).resizeHeight||s.height||i.height||c;let d=1,u=(t&&(d=ae(t,e)),l);return(u=a&&!o?n.height||l:u)*d-(t?1:12-ne(r))})(e,d)}px`,bottom:`-${Math.floor(t/2)}px`,left:c*n.indent+(c?2-ne(Ae):0)+16+"px"}})])]:[]},J=(e,t,r,$,_,l,o,H,a,n,N,P,i)=>{var B=$e.fullAllDataRowIdData,{columnKey:z,resizable:s,border:j,height:c,cellClassName:U,cellStyle:W,align:d,spanMethod:q,mouseConfig:X,editConfig:K,editRules:u,tooltipConfig:p,padding:h}=Le,{tableData:m,overflowX:Y,currentColumn:G,scrollXLoad:Z,scrollYLoad:v,calcCellHeightFlag:Q,resizeHeightFlag:f,mergeList:J,editStore:ee,isAllOverflow:te,validErrorMaps:g}=Ve,{afterFullData:re,scrollXStore:x,scrollYStore:b}=$e,C=je.value,w=Ue.value,le=ze.value,oe=_e.value,ae=Be.value,{isAllColumnDrag:ne,isAllRowDrag:ie}=Ke.value,se=We.value,y=Xe.value,ce=Pe.value,T=C.height||se.height,ce=T||ce,{disabledMethod:E,isCrossDrag:de,isPeerDrag:ue}=y,pe=qe.value,he=He.value,me=Ne.value.selectCellToRow,{type:ve,cellRender:fe,editRender:ge,align:xe,showOverflow:be,className:Ce,treeNode:we,rowResize:ye,padding:R,verticalAlign:S,slots:Te}=n,Ee=C.verticalAlign,ee=ee.actived,B=B[t],I=n.id,D=ge||fe,D=D?Qe.get(D.name):null,Re=D?D.tableCellClassName||D.cellClassName:null,Se=D?D.tableCellStyle||D.cellStyle:"";let Ie=ae.showAll;var ae=Ae.getColumnIndex(n),M=Ae.getVTColumnIndex(n),De=nl(ge),f=f?B.resizeHeight:0;let k=r?n.fixed!==r:n.fixed&&Y;Y=al().eqNull(R)?null===h?C.padding:h:R,C=al().eqNull(be)?te:be,h="ellipsis"===C;let F="title"===C,O=!0===C||"tooltip"===C;R=te||F||O||h,be=al().isBoolean(n.resizable)?n.resizable:pe.resizable||s,C=!!T,s=0<f;let Me;T={},xe=xe||(D?D.tableCellAlign:"")||d,D=al().eqNull(S)?Ee:S,d=g[t+":"+I],Ee=u&&w.showMessage&&("default"===w.message?c||1<m.length:"inline"===w.message),S={colid:I};let A={$table:Ae,$grid:Ae.xegrid,isEdit:!1,seq:e,rowid:t,row:l,rowIndex:o,$rowIndex:H,_rowIndex:a,column:n,columnIndex:ae,$columnIndex:N,_columnIndex:M,fixed:r,type:Ze,isHidden:!!k,level:_,visibleData:re,data:m,items:i},L=!1,ke=!1;if((L=se.drag?"row"===y.trigger||n.dragSort&&"cell"===y.trigger:L)&&(ke=!(!E||!E(A))),(F||O||Ie||p)&&(T.onMouseenter=e=>{Ye()||(F?fr(e.currentTarget,n):(O||Ie)&&Ae.triggerBodyTooltipEvent(e,A),Ae.dispatchEvent("cell-mouseenter",Object.assign({cell:e.currentTarget},A),e))}),(O||Ie||p)&&(T.onMouseleave=e=>{Ye()||((O||Ie)&&Ae.handleTargetLeaveEvent(e),Ae.dispatchEvent("cell-mouseleave",Object.assign({cell:e.currentTarget},A),e))}),(L||le.range||X)&&(T.onMousedown=e=>{Ae.triggerCellMousedownEvent(e,A)}),L&&(T.onMouseup=Ae.triggerCellMouseupEvent),T.onClick=e=>{Ae.triggerCellClickEvent(e,A)},T.onDblclick=e=>{Ae.triggerCellDblclickEvent(e,A)},J.length){g=Sr(J,a,M);if(g){var{rowspan:u,colspan:c}=g;if(!u||!c)return null;1<u&&(S.rowspan=u),1<c&&(S.colspan=c)}}else if(q){var{rowspan:e=1,colspan:o=1}=q(A)||{};if(!e||!o)return null;1<e&&(S.rowspan=e),1<o&&(S.colspan=o)}!(k=k&&J&&(1<S.colspan||1<S.rowspan)?!1:k)&&K&&(ge||fe)&&(oe.showStatus||oe.showUpdateStatus)&&(Me=Ae.isUpdateByRow(l,n.field));H=v&&!R;let V=ce;ae=Q?B.height:0,f?V=f:v&&!R&&(V=ae||ce),re=N===P.length-1,m=!n.resizeWidth&&("auto"===n.minWidth||"auto"===n.width);let Fe=!1;(v&&(a<b.visibleStartIndex-b.preloadSize||a>b.visibleEndIndex+b.preloadSize)||Z&&!n.fixed&&(M<x.visibleStartIndex-x.preloadSize||M>x.visibleEndIndex+x.preloadSize))&&(Fe=!0);i={},v||R||C||s?i.height=V+"px":i.minHeight=V+"px",y=[];k&&te?y.push((0,dl.h)("div",{key:"tc",class:["vxe-cell",{"c--title":F,"c--tooltip":O,"c--ellipsis":h}],style:i})):(y.push(...Ge(A,V),(0,dl.h)("div",{key:"tc",class:["vxe-cell",{"c--title":F,"c--tooltip":O,"c--ellipsis":h}],style:i,title:F?Ae.getCellLabel(l,n):null},Fe?[]:[(0,dl.h)("div",{colid:I,rowid:t,class:"vxe-cell--wrapper"},n.renderCell(A))])),Ee&&d&&(E=d.rule,p=Te?Te.valid:null,le={...A,...d,rule:d},y.push((0,dl.h)("div",{key:"tcv",class:["vxe-cell--valid-error-tip",yt(w.className,le)],style:E&&E.maxWidth?{width:E.maxWidth+"px"}:null},[(0,dl.h)("div",{class:"vxe-cell--valid-error-wrapper vxe-cell--valid-error-theme-"+(w.theme||"normal")},[p?Ae.callSlot(p,le):[(0,dl.h)("span",{class:"vxe-cell--valid-error-msg"},d.content)]])]))));let Oe=!1;return X&&he.area&&me&&((M||!0!==me)&&me!==n.field||(Oe=!0)),!k&&be&&ne&&y.push((0,dl.h)("div",{key:"tcc",class:["vxe-cell--col-resizable",{"is--line":!j||"none"===j}],onMousedown:e=>Ae.handleColResizeMousedownEvent(e,r,A),onDblclick:e=>Ae.handleColResizeDblclickEvent(e,A)})),(ye||ie)&&se.resizable&&y.push((0,dl.h)("div",{key:"tcr",class:"vxe-cell--row-resizable",onMousedown:e=>Ae.handleRowResizeMousedownEvent(e,A),onDblclick:e=>Ae.handleRowResizeDblclickEvent(e,A)})),(0,dl.h)("td",{class:["vxe-body--column",I,D?"col--vertical-"+D:"",xe?"col--"+xe:"",ve?"col--"+ve:"",{"col--last":re,"col--tree-node":we,"col--edit":De,"col--ellipsis":R,"col--cs-height":C,"col--rs-height":s,"col--to-row":Oe,"col--auto-height":H,"fixed--width":!m,"fixed--hidden":k,"is--padding":Y,"is--drag-cell":L&&(de||ue||!_),"is--drag-disabled":ke,"col--dirty":Me,"col--active":K&&De&&ee.row===l&&(ee.column===n||"row"===oe.mode),"col--valid-error":!!d,"col--current":G===n},yt(Re,A),yt(Ce,A),yt(U,A)],key:z||Z||v||pe.useKey||se.useKey||pe.drag?I:N,...S,style:Object.assign({},al().isFunction(Se)?Se(A):Se,al().isFunction(W)?W(A):W),...T},$&&k?[]:y)},Q=(g,x,b,C)=>{let{stripe:w,rowKey:y,highlightHoverRow:T,rowClassName:E,rowStyle:R,editConfig:S,treeConfig:I}=Le,{hasFixedColumn:D,treeExpandedMaps:M,isColLoading:k,scrollXLoad:F,scrollYLoad:O,isAllOverflow:A,rowExpandedMaps:L,expandColumn:V,selectRadioRow:$,pendingRowMaps:_,isDragColMove:H}=Ve,N=$e.fullAllDataRowIdData,P=ze.value,B=e.value,z=u.value,j=_e.value,U=We.value,W=qe.value,q=t.value,{transform:X,seqMode:K}=z,Y=z.children||z.childrenField,G=[];return b.forEach((r,l)=>{var e={};let o,a=(o=Ae.getRowIndex(r),(U.isHover||T)&&(e.onMouseenter=e=>{Ye()||Ae.triggerHoverEvent(e,{row:r,rowIndex:o})},e.onMouseleave=()=>{Ye()||Ae.clearHoverRow()}),xl(Ae,r));var t=N[a];let n=0,i=-1,s=0;t&&(n=t.level,i=I&&X&&"increasing"===K?t._index+1:t.seq,s=t._index);var t={$table:Ae,seq:i,rowid:a,fixed:g,type:Ze,level:n,row:r,rowIndex:o,$rowIndex:l,_rowIndex:s},c=V&&!!L[a];let d=!1,u=[],p=!1;S&&(p=Ae.isInsertByRow(r)),!I||O||X||(u=r[Y],d=u&&0<u.length&&!!M[a]),!U.drag||I&&!X||(e.onDragstart=Ae.handleRowDragDragstartEvent,e.onDragend=Ae.handleRowDragDragendEvent,e.onDragover=Ae.handleRowDragDragoverEvent);var h,m,v=["vxe-body--row",I?"row--level-"+n:"",{"row--stripe":w&&(s+1)%2==0,"is--new":p,"is--expand-row":c,"is--expand-tree":d,"row--new":p&&(j.showStatus||j.showInsertStatus),"row--radio":B.highlight&&Ae.eqRow($,r),"row--checked":P.highlight&&Ae.isCheckedByCheckboxRow(r),"row--pending":!!_[a]},yt(E,t)];let f=C.map((e,t)=>J(i,a,g,x,n,r,o,l,s,e,t,C,b));G.push(!k&&W.drag&&q.animation?(0,dl.h)(dl.TransitionGroup,{name:"vxe-header--col-list"+(H?"":"-disabled"),tag:"tr",class:v,rowid:a,style:R?al().isFunction(R)?R(t):R:null,key:y||F||O||U.useKey||U.drag||W.drag||I?a:l,...e},{default:()=>f}):(0,dl.h)("tr",{class:v,rowid:a,style:R?al().isFunction(R)?R(t):R:null,key:y||F||O||U.useKey||U.drag||W.drag||I?a:l,...e},f)),c&&({height:v,padding:t}=Z.value,c={},h=(v&&(c.height=v+"px"),I&&(c.paddingLeft=n*z.indent+30+"px"),V).showOverflow,h=al().isUndefined(h)||al().isNull(h)?A:h,m={$table:Ae,seq:i,column:V,fixed:g,type:Ze,level:n,row:r,rowIndex:o,$rowIndex:l,_rowIndex:s},G.push((0,dl.h)("tr",{class:["vxe-body--expanded-row",{"is--padding":t}],key:"expand_"+a,style:R?al().isFunction(R)?R(m):R:null,...e},[(0,dl.h)("td",{class:{"vxe-body--expanded-column":1,"fixed--hidden":g&&!D,"col--ellipsis":h},colspan:C.length},[(0,dl.h)("div",{class:{"vxe-body--expanded-cell":1,"is--ellipsis":v},style:c},[V.renderData(m)])])]))),d&&G.push(...Q(g,x,u,C))}),G};(0,dl.onMounted)(()=>{(0,dl.nextTick)(()=>{var e=F.fixedType,t=$e.elemStore,e=`${e||"main"}-body-`;t[e+"wrapper"]=_,t[e+"scroll"]=H,t[e+"table"]=N,t[e+"colgroup"]=P,t[e+"list"]=B,t[e+"xSpace"]=z,t[e+"ySpace"]=j,t[e+"emptyBlock"]=U})}),(0,dl.onUnmounted)(()=>{var e=F.fixedType,t=$e.elemStore,e=`${e||"main"}-body-`;t[e+"wrapper"]=null,t[e+"scroll"]=null,t[e+"table"]=null,t[e+"colgroup"]=null,t[e+"list"]=null,t[e+"xSpace"]=null,t[e+"ySpace"]=null,t[e+"emptyBlock"]=null});return()=>{var e=A.slots;let{fixedColumn:t,fixedType:r,tableColumn:l}=F;var{spanMethod:o,footerSpanMethod:a,mouseConfig:n}=Le,{isGroup:i,tableData:s,isRowLoading:c,isColLoading:d,scrollXLoad:u,scrollYLoad:p,isAllOverflow:h,isDragRowMove:m,expandColumn:v,dragRow:f,dragCol:g}=Ve,{visibleColumn:x,fullAllDataRowIdData:b,fullColumnIdData:C}=$e,w=We.value,y=L.value,T=He.value,E=Xe.value,R=V.value,S=$.value;let I=s,D=l,M=!(u||p||h)||v||o||a?!1:!0;r&&(D=x,M)&&(D=t||[]),p&&f&&2<I.length&&(s=b[xl(Ae,f)])&&(h=s._index,v=I[0],o=I[I.length-1],a=b[xl(Ae,v)],x=b[xl(Ae,o)],a)&&x&&(s=a._index,v=x._index,h<s?I=[f].concat(I):v<h&&(I=I.concat([f]))),r||i||u&&g&&2<D.length&&(b=C[g.id])&&(o=b._index,a=D[0],x=D[D.length-1],s=C[a.id],v=C[x.id],s)&&v&&(h=s._index,f=v._index,o<h?D=[g].concat(D):f<o&&(D=D.concat([g])));let k;i=e?e.empty:null,k=i?Ae.callSlot(i,{$table:Ae,$grid:Ae.xegrid}):(b=(u=y.name?Qe.get(y.name):null)?u.renderTableEmpty||u.renderTableEmptyView||u.renderEmpty:null)?yl(b(y,{$table:Ae})):Le.emptyText||me("vxe.table.emptyText"),a={onScroll(e){Ae.triggerBodyScrollEvent(e,r)}};return(p||R||S)&&(a.onWheel=Ae.triggerBodyWheelEvent),(0,dl.h)("div",{ref:_,class:["vxe-table--body-wrapper",r?`fixed-${r}--wrapper`:"body--wrapper"],xid:O},[(0,dl.h)("div",{ref:H,class:"vxe-table--body-inner-wrapper",...a},[r?ve(Ae):(0,dl.h)("div",{ref:z,class:"vxe-body--x-space"}),(0,dl.h)("div",{ref:j,class:"vxe-body--y-space"}),(0,dl.h)("table",{ref:N,class:"vxe-table--body",xid:O,cellspacing:0,cellpadding:0,border:0},[(0,dl.h)("colgroup",{ref:P},D.map((e,t)=>(0,dl.h)("col",{name:e.id,key:t}))),!c&&!d&&w.drag&&E.animation?(0,dl.h)(dl.TransitionGroup,{ref:B,name:"vxe-body--row-list"+(m?"":"-disabled"),tag:"tbody"},{default:()=>Q(r,M,I,D)}):(0,dl.h)("tbody",{ref:B},Q(r,M,I,D))]),(0,dl.h)("div",{class:"vxe-table--checkbox-range"}),n&&T.area?(0,dl.h)("div",{class:"vxe-table--cell-area"},[(0,dl.h)("span",{class:"vxe-table--cell-main-area"},T.extension?[(0,dl.h)("span",{class:"vxe-table--cell-main-area-btn",onMousedown(e){Ae.triggerCellAreaExtendMousedownEvent&&Ae.triggerCellAreaExtendMousedownEvent(e,{$table:Ae,fixed:r,type:Ze})}})]:[]),(0,dl.h)("span",{class:"vxe-table--cell-copy-area"}),(0,dl.h)("span",{class:"vxe-table--cell-extend-area"}),(0,dl.h)("span",{class:"vxe-table--cell-multi-area"}),(0,dl.h)("span",{class:"vxe-table--cell-active-area"}),(0,dl.h)("span",{class:"vxe-table--cell-row-status-area"})]):ve(Ae),r?ve(Ae):(0,dl.h)("div",{class:"vxe-table--empty-block",ref:U},[(0,dl.h)("div",{class:"vxe-table--empty-content"},k)])])])}}});let{renderer:fe,renderEmptyElement:ge}=ol.VxeUI;var Pl=(0,dl.defineComponent)({name:"VxeTableHeader",props:{tableData:Array,tableColumn:Array,tableGroupColumn:Array,fixedColumn:Array,fixedType:{type:String,default:null}},setup(g){let Z=(0,dl.inject)("$xeTable",{}),{xID:x,props:b,reactData:C,internalData:w}=Z,{computeColumnOpts:y,computeColumnDragOpts:T,computeCellOpts:r,computeMouseOpts:E,computeHeaderCellOpts:l,computeDefaultRowHeight:o}=Z.getComputeMaps(),R=(0,dl.ref)([]),S=(0,dl.ref)(),I=(0,dl.ref)(),D=(0,dl.ref)(),M=(0,dl.ref)(),k=(0,dl.ref)(),F=(0,dl.ref)(),a=(0,dl.ref)(),n=()=>{var e=C.isGroup;R.value=e?(e=>{let t=1,l=(r,e)=>{if(e&&(r.level=e.level+1,t<r.level)&&(t=r.level),r.children&&r.children.length&&r.children.some(e=>e.visible)){let t=0;r.children.forEach(e=>{e.visible&&(l(e,r),t+=e.colSpan)}),r.colSpan=t}else r.colSpan=1},r=(e.forEach(e=>{e.level=1,l(e)}),[]);for(let e=0;e<t;e++)r.push([]);return le(e).forEach(e=>{e.children&&e.children.length&&e.children.some(e=>e.visible)?e.rowSpan=1:e.rowSpan=t-e.level+1,r[e.level-1].push(e)}),r})(g.tableGroupColumn):[]},O=(e,R,S,I)=>{let D=g.fixedType,{resizable:M,border:k,columnKey:F,headerCellClassName:O,headerCellStyle:A,showHeaderOverflow:L,headerAlign:V,align:$,mouseConfig:_}=b,{currentColumn:H,scrollXLoad:N,scrollYLoad:P,overflowX:B}=C,z=w.scrollXStore,j=y.value,U=T.value,W=r.value;var t=o.value;let q=l.value,X=wr(q.height||W.height)||t,{disabledMethod:K,isCrossDrag:Y,isPeerDrag:G}=U;return S.map((e,t)=>{var{type:r,showHeaderOverflow:l,headerAlign:o,align:a,filters:n,headerClassName:i,editRender:s,cellRender:c}=e,d=e.id,s=s||c,c=s?fe.get(s.name):null,s=e.children&&e.children.length,u=D?e.fixed!==D&&!s:!!e.fixed&&B,p=(al().isBoolean(q.padding)?q:W).padding,l=al().eqNull(l)?L:l,o=o||(c?c.tableHeaderCellAlign:"")||V||a||(c?c.tableCellAlign:"")||$;let h="ellipsis"===l;a="title"===l,c=!0===l||"tooltip"===l;let m=a||c||h,v=!1,f=null;n&&(f=n[0],v=n.some(e=>e.checked));var l=Z.getColumnIndex(e),g=Z.getVTColumnIndex(e);let x={$table:Z,$grid:Z.xegrid,$rowIndex:I,column:e,columnIndex:l,$columnIndex:t,_columnIndex:g,firstFilterOption:f,fixed:D,type:"header",isHidden:u,hasFilter:v};var l={colid:d,colspan:1<e.colSpan?e.colSpan:null,rowspan:1<e.rowSpan?e.rowSpan:null},b={onClick:e=>Z.triggerHeaderCellClickEvent(e,x),onDblclick:e=>Z.triggerHeaderCellDblclickEvent(e,x)},C=(N&&!m&&(h=m=!0),j.drag&&"cell"===U.trigger);let w=!1;C&&(w=!(!K||!K(x))),(_||C)&&(b.onMousedown=e=>Z.triggerHeaderCellMousedownEvent(e,x)),j.drag&&(b.onDragstart=Z.handleHeaderCellDragDragstartEvent,b.onDragend=Z.handleHeaderCellDragDragendEvent,b.onDragover=Z.handleHeaderCellDragDragoverEvent,C)&&(b.onMouseup=Z.handleHeaderCellDragMouseupEvent);var C=t===S.length-1,y=al().isBoolean(e.resizable)?e.resizable:j.resizable||M,T=!e.resizeWidth&&("auto"===e.minWidth||"auto"===e.width);let E=!1;N&&!e.fixed&&(g<z.visibleStartIndex-z.preloadSize||g>z.visibleEndIndex+z.preloadSize)&&(E=!0);g={};return m?g.height=X+"px":g.minHeight=X+"px",(0,dl.h)("th",{class:["vxe-header--column",d,{["col--"+o]:o,["col--"+r]:r,"col--last":C,"col--fixed":e.fixed,"col--group":s,"col--ellipsis":m,"fixed--width":!T,"fixed--hidden":u,"is--padding":p,"is--sortable":e.sortable,"col--filter":!!n,"is--filter-active":v,"is--drag-active":!e.fixed&&!w&&(Y||G||!e.parentId),"is--drag-disabled":w,"col--current":H===e},i?al().isFunction(i)?i(x):i:"",O?al().isFunction(O)?O(x):O:""],style:A?al().isFunction(A)?A(x):A:null,...l,...b,key:F||N||P||j.useKey||j.drag||s?d:t},[(0,dl.h)("div",{class:["vxe-cell",{"c--title":a,"c--tooltip":c,"c--ellipsis":h}],style:g},E||R&&u?[]:[(0,dl.h)("div",{colid:d,class:"vxe-cell--wrapper"},e.renderHeader(x))]),!u&&y?(0,dl.h)("div",{class:["vxe-cell--col-resizable",{"is--line":!k||"none"===k}],onMousedown:e=>Z.handleColResizeMousedownEvent(e,D,x),onDblclick:e=>Z.handleColResizeDblclickEvent(e,x)}):ge(Z)])})};return(0,dl.watch)(()=>g.tableColumn,n),(0,dl.onMounted)(()=>{(0,dl.nextTick)(()=>{var e=g.fixedType,t=Z.internalData,t=t.elemStore,e=`${e||"main"}-header-`;t[e+"wrapper"]=S,t[e+"scroll"]=I,t[e+"table"]=D,t[e+"colgroup"]=M,t[e+"list"]=k,t[e+"xSpace"]=F,t[e+"repair"]=a,n()})}),(0,dl.onUnmounted)(()=>{var e=g.fixedType,t=Z.internalData,t=t.elemStore,e=`${e||"main"}-header-`;t[e+"wrapper"]=null,t[e+"scroll"]=null,t[e+"table"]=null,t[e+"colgroup"]=null,t[e+"list"]=null,t[e+"xSpace"]=null,t[e+"repair"]=null}),()=>{let{fixedType:t,fixedColumn:e,tableColumn:r}=g;var{mouseConfig:l,showHeaderOverflow:o,spanMethod:a,footerSpanMethod:n}=b,{isGroup:i,scrollXLoad:s,scrollYLoad:c,dragCol:d}=C,{visibleColumn:u,fullColumnIdData:p}=w,h=E.value;let m=R.value,v=r,f=!1;return i?v=u:(!(s||c||o)||a||n||(f=!0),t&&(v=u,f)&&(v=e||[]),m=[v]),t||i||s&&d&&2<v.length&&(c=p[d.id])&&(o=c._index,a=v[0],n=v[v.length-1],u=p[a.id],s=p[n.id],u)&&s&&(c=u._index,a=s._index,o<c?(v=[d].concat(v),m=[[d].concat(m[0])].concat(m.slice(1))):a<o&&(v=v.concat([d]),m=[m[0].concat([d])].concat(m.slice(1)))),(0,dl.h)("div",{ref:S,class:["vxe-table--header-wrapper",t?`fixed-${t}--wrapper`:"body--wrapper"],xid:x},[(0,dl.h)("div",{ref:I,class:"vxe-table--header-inner-wrapper",onScroll(e){Z.triggerHeaderScrollEvent(e,t)}},[t?ge(Z):(0,dl.h)("div",{ref:F,class:"vxe-body--x-space"}),(0,dl.h)("table",{ref:D,class:"vxe-table--header",xid:x,cellspacing:0,cellpadding:0,border:0},[(0,dl.h)("colgroup",{ref:M},v.map((e,t)=>(0,dl.h)("col",{name:e.id,key:t}))),(0,dl.h)("thead",{ref:k},((l,o,e)=>{let a=g.fixedType,{headerRowClassName:n,headerRowStyle:i}=b,{isColLoading:s,isDragColMove:c}=C,d=y.value,u=T.value;return e.map((e,t)=>{var r={$table:Z,$rowIndex:t,fixed:a,type:"header"};return!s&&d.drag&&u.animation?(0,dl.h)(dl.TransitionGroup,{key:t,name:"vxe-header--col-list"+(c?"":"-disabled"),tag:"tr",class:["vxe-header--row",n?al().isFunction(n)?n(r):n:""],style:i?al().isFunction(i)?i(r):i:null},{default:()=>O(l,o,e,t)}):(0,dl.h)("tr",{key:t,class:["vxe-header--row",n?al().isFunction(n)?n(r):n:""],style:i?al().isFunction(i)?i(r):i:null},O(l,o,e,t))})})(i,f,m))]),l&&h.area?(0,dl.h)("div",{class:"vxe-table--cell-area"},[(0,dl.h)("span",{class:"vxe-table--cell-main-area"}),(0,dl.h)("span",{class:"vxe-table--cell-copy-area"}),(0,dl.h)("span",{class:"vxe-table--cell-extend-area"}),(0,dl.h)("span",{class:"vxe-table--cell-multi-area"}),(0,dl.h)("span",{class:"vxe-table--cell-active-area"}),(0,dl.h)("span",{class:"vxe-table--cell-col-status-area"})]):ge(Z)])])}}});let{renderer:xe,renderEmptyElement:be}=ol.VxeUI;var Bl=(0,dl.defineComponent)({name:"VxeTableFooter",props:{footerTableData:{type:Array,default:()=>[]},tableColumn:{type:Array,default:()=>[]},fixedColumn:{type:Array,default:()=>[]},fixedType:{type:String,default:null}},setup(m){let K=(0,dl.inject)("$xeTable",{}),{xID:v,props:f,reactData:g,internalData:x}=K,{computeTooltipOpts:t,computeColumnOpts:b,computeColumnDragOpts:C,computeCellOpts:r,computeFooterCellOpts:l,computeDefaultRowHeight:o,computeResizableOpts:a}=K.getComputeMaps(),w=(0,dl.ref)(),y=(0,dl.ref)(),T=(0,dl.ref)(),E=(0,dl.ref)(),R=(0,dl.ref)(),S=(0,dl.ref)(),I=(w,y,T,E,R)=>{let S=m.fixedType,{resizable:I,border:D,footerCellClassName:M,footerCellStyle:k,footerAlign:F,footerSpanMethod:O,align:A,columnKey:L,showFooterOverflow:V}=f,{scrollXLoad:$,scrollYLoad:_,overflowX:H,currentColumn:N,mergeFooterList:P}=g,B=x.scrollXStore,z=t.value;let j=a.value.isAllColumnDrag,U=b.value;var e=o.value;let W=r.value,q=l.value,X=wr(q.height||W.height)||e;return w.map((t,e)=>{var{type:r,showFooterOverflow:l,footerAlign:o,align:a,footerClassName:n,editRender:i,cellRender:s}=t,c=t.id,i=i||s,s=i?xe.get(i.name):null;let d=z.showAll;var i=t.children&&t.children.length,i=S?t.fixed!==S&&!i:t.fixed&&H,u=(al().isBoolean(q.padding)?q:W).padding,l=al().eqNull(l)?V:l,o=o||(s?s.tableFooterCellAlign:"")||F||a||(s?s.tableCellAlign:"")||A;let p="ellipsis"===l,h="title"===l,m=!0===l||"tooltip"===l,v=h||m||p;var a=al().isBoolean(t.resizable)?t.resizable:U.resizable||I,s={colid:c},l={},f=K.getColumnIndex(t),g=K.getVTColumnIndex(t),x=g;let b={$table:K,$grid:K.xegrid,row:T,rowIndex:R,_rowIndex:R,$rowIndex:E,column:t,columnIndex:f,$columnIndex:e,_columnIndex:g,itemIndex:x,items:T,fixed:S,type:"footer",data:y};if($&&!v&&(p=v=!0),(h||m||d)&&(l.onMouseenter=e=>{h?fr(e.currentTarget,t):(m||d)&&K.triggerFooterTooltipEvent(e,b)}),(m||d)&&(l.onMouseleave=e=>{(m||d)&&K.handleTargetLeaveEvent(e)}),l.onClick=e=>{K.dispatchEvent("footer-cell-click",Object.assign({cell:e.currentTarget},b),e)},l.onDblclick=e=>{K.dispatchEvent("footer-cell-dblclick",Object.assign({cell:e.currentTarget},b),e)},P.length){f=((t,r,l)=>{for(let e=0;e<t.length;e++){var{row:o,col:a,rowspan:n,colspan:i}=t[e];if(-1<a&&-1<o&&n&&i){if(o===r&&a===l)return{rowspan:n,colspan:i};if(o<=r&&r<o+n&&a<=l&&l<a+i)return{rowspan:0,colspan:0}}}})(P,R,g);if(f){var{rowspan:x,colspan:f}=f;if(!x||!f)return null;1<x&&(s.rowspan=x),1<f&&(s.colspan=f)}}else if(O){var{rowspan:x=1,colspan:f=1}=O(b)||{};if(!x||!f)return null;1<x&&(s.rowspan=x),1<f&&(s.colspan=f)}x=e===w.length-1,f=!t.resizeWidth&&("auto"===t.minWidth||"auto"===t.width);let C=!1;$&&!t.fixed&&(g<B.visibleStartIndex-B.preloadSize||g>B.visibleEndIndex+B.preloadSize)&&(C=!0);g={};return v?g.height=X+"px":g.minHeight=X+"px",(0,dl.h)("td",{class:["vxe-footer--column",t.id,{["col--"+o]:o,["col--"+r]:r,"col--last":x,"fixed--width":!f,"fixed--hidden":i,"is--padding":u,"col--ellipsis":v,"col--current":N===t},yt(n,b),yt(M,b)],...s,style:k?al().isFunction(k)?k(b):k:null,...l,key:L||$||_||U.useKey||U.drag?t.id:e},[(0,dl.h)("div",{class:["vxe-cell",{"c--title":h,"c--tooltip":m,"c--ellipsis":p}],style:g},C?[]:[(0,dl.h)("div",{colid:c,class:"vxe-cell--wrapper"},t.renderFooter(b))]),!i&&a&&j?(0,dl.h)("div",{class:["vxe-cell--col-resizable",{"is--line":!D||"none"===D}],onMousedown:e=>K.handleColResizeMousedownEvent(e,S,b),onDblclick:e=>K.handleColResizeDblclickEvent(e,b)}):be(K)])})};return(0,dl.onMounted)(()=>{(0,dl.nextTick)(()=>{var e=m.fixedType,t=x.elemStore,e=`${e||"main"}-footer-`;t[e+"wrapper"]=w,t[e+"scroll"]=y,t[e+"table"]=T,t[e+"colgroup"]=E,t[e+"list"]=R,t[e+"xSpace"]=S})}),(0,dl.onUnmounted)(()=>{var e=m.fixedType,t=x.elemStore,e=`${e||"main"}-footer-`;t[e+"wrapper"]=null,t[e+"scroll"]=null,t[e+"table"]=null,t[e+"colgroup"]=null,t[e+"list"]=null,t[e+"xSpace"]=null}),()=>{let{fixedType:t,fixedColumn:e,tableColumn:r}=m;var{spanMethod:l,footerSpanMethod:o,showFooterOverflow:a}=f,{visibleColumn:n,fullColumnIdData:i}=x,{isGroup:s,scrollXLoad:c,scrollYLoad:d,dragCol:u}=g;let p=r,h=!(c||d||a)||l||o?!1:!0;return t&&(p=n,h)&&(p=e||[]),t||s||c&&u&&2<p.length&&(d=i[u.id])&&(a=d._index,l=p[0],o=p[p.length-1],n=i[l.id],s=i[o.id],n)&&s&&(c=n._index,d=s._index,a<c?p=[u].concat(p):d<a&&(p=p.concat([u]))),(0,dl.h)("div",{ref:w,class:["vxe-table--footer-wrapper",t?`fixed-${t}--wrapper`:"body--wrapper"],xid:v},[(0,dl.h)("div",{ref:y,class:"vxe-table--footer-inner-wrapper",onScroll(e){K.triggerFooterScrollEvent(e,t)}},[t?be(K):(0,dl.h)("div",{ref:S,class:"vxe-body--x-space"}),(0,dl.h)("table",{ref:T,class:"vxe-table--footer",xid:v,cellspacing:0,cellpadding:0,border:0},[(0,dl.h)("colgroup",{ref:E},p.map((e,t)=>(0,dl.h)("col",{name:e.id,key:t}))),(0,dl.h)("tfoot",{ref:R},(o=>{let{fixedType:a,footerTableData:n}=m,{footerRowClassName:i,footerRowStyle:s}=f,{isColLoading:c,isDragColMove:d}=g,u=b.value,p=C.value;return n.map((e,t)=>{let r=t;var l={$table:K,row:e,_rowIndex:r,$rowIndex:t,fixed:a,type:"footer"};return!c&&u.drag&&p.animation?(0,dl.h)(dl.TransitionGroup,{key:t,name:"vxe-header--col-list"+(d?"":"-disabled"),tag:"tr",class:["vxe-footer--row",i?al().isFunction(i)?i(l):i:""],style:s?al().isFunction(s)?s(l):s:null},{default:()=>I(o,n,e,t,r)}):(0,dl.h)("tr",{key:t,class:["vxe-footer--row",i?al().isFunction(i)?i(l):i:""],style:s?al().isFunction(s)?s(l):s:null},I(o,n,e,t,r))})})(p))])])])}}});let o=ol.VxeUI.getConfig;var zl={id:[String,Function],data:Array,height:[Number,String],minHeight:{type:[Number,String],default:()=>o().table.minHeight},maxHeight:[Number,String],resizable:{type:Boolean,default:()=>o().table.resizable},stripe:{type:Boolean,default:()=>o().table.stripe},border:{type:[Boolean,String],default:()=>o().table.border},padding:{type:Boolean,default:null},round:{type:Boolean,default:()=>o().table.round},size:{type:String,default:()=>o().table.size||o().size},fit:{type:Boolean,default:()=>o().table.fit},loading:Boolean,align:{type:String,default:()=>o().table.align},headerAlign:{type:String,default:()=>o().table.headerAlign},footerAlign:{type:String,default:()=>o().table.footerAlign},showHeader:{type:Boolean,default:()=>o().table.showHeader},highlightCurrentRow:{type:Boolean,default:()=>o().table.highlightCurrentRow},highlightHoverRow:{type:Boolean,default:()=>o().table.highlightHoverRow},highlightCurrentColumn:{type:Boolean,default:()=>o().table.highlightCurrentColumn},highlightHoverColumn:{type:Boolean,default:()=>o().table.highlightHoverColumn},highlightCell:Boolean,showFooter:Boolean,footerData:Array,footerMethod:Function,rowClassName:[String,Function],cellClassName:[String,Function],headerRowClassName:[String,Function],headerCellClassName:[String,Function],footerRowClassName:[String,Function],footerCellClassName:[String,Function],cellStyle:[Object,Function],headerCellStyle:[Object,Function],footerCellStyle:[Object,Function],rowStyle:[Object,Function],headerRowStyle:[Object,Function],footerRowStyle:[Object,Function],mergeCells:Array,mergeFooterItems:Array,spanMethod:Function,footerSpanMethod:Function,showOverflow:{type:[Boolean,String],default:()=>o().table.showOverflow},showHeaderOverflow:{type:[Boolean,String],default:()=>o().table.showHeaderOverflow},showFooterOverflow:{type:[Boolean,String],default:()=>o().table.showFooterOverflow},columnKey:Boolean,rowKey:Boolean,rowId:{type:String,default:()=>o().table.rowId},zIndex:Number,emptyText:{type:String,default:()=>o().table.emptyText},keepSource:{type:Boolean,default:()=>o().table.keepSource},autoResize:{type:Boolean,default:()=>o().table.autoResize},syncResize:[Boolean,String,Number],resizeConfig:Object,columnConfig:Object,cellConfig:Object,headerCellConfig:Object,footerCellConfig:Object,rowConfig:Object,dragConfig:Object,rowDragConfig:Object,columnDragConfig:Object,resizableConfig:Object,seqConfig:Object,sortConfig:Object,filterConfig:Object,radioConfig:Object,checkboxConfig:Object,tooltipConfig:Object,exportConfig:Object,importConfig:Object,printConfig:Object,expandConfig:Object,treeConfig:Object,menuConfig:Object,mouseConfig:Object,areaConfig:Object,keyboardConfig:Object,clipConfig:Object,fnrConfig:Object,editConfig:Object,validConfig:Object,editRules:Object,loadingConfig:Object,emptyRender:Object,customConfig:Object,scrollX:Object,scrollY:Object,virtualXConfig:Object,virtualYConfig:Object,scrollbarConfig:Object,animat:{type:Boolean,default:()=>o().table.animat},delayHover:{type:Number,default:()=>o().table.delayHover},params:Object},Vr=["update:data","keydown-start","keydown","keydown-end","paste","copy","cut","current-change","radio-change","checkbox-change","checkbox-all","checkbox-range-start","checkbox-range-change","checkbox-range-end","checkbox-range-select","cell-click","cell-dblclick","cell-menu","cell-mouseenter","cell-mouseleave","cell-selected","cell-delete-value","cell-backspace-value","header-cell-click","header-cell-dblclick","header-cell-menu","footer-cell-click","footer-cell-dblclick","footer-cell-menu","clear-merge","sort-change","clear-sort","filter-change","filter-visible","clear-filter","resizable-change","column-resizable-change","row-resizable-change","toggle-row-expand","toggle-tree-expand","menu-click","edit-closed","row-dragstart","row-dragover","row-dragend","column-dragstart","column-dragover","column-dragend","enter-append-row","edit-actived","edit-activated","edit-disabled","valid-error","scroll","scroll-boundary","custom","change-fnr","open-fnr","show-fnr","hide-fnr","fnr-change","fnr-find","fnr-find-all","fnr-replace","fnr-replace-all","cell-area-copy","cell-area-cut","cell-area-paste","cell-area-merge","clear-cell-area-selection","clear-cell-area-merge","header-cell-area-selection","cell-area-selection-invalid","cell-area-selection-start","cell-area-selection-drag","cell-area-selection-end","cell-area-extension-start","cell-area-extension-drag","cell-area-extension-end","cell-area-selection-all-start","cell-area-selection-all-end","cell-area-arrows-start","cell-area-arrows-end","active-cell-change-start","active-cell-change-end"];let{getI18n:se,getIcon:ce,renderEmptyElement:Ce}=ol.VxeUI;var jl=(0,dl.defineComponent)({name:"TableCustomPanel",props:{customStore:{type:Object,default:()=>({})}},setup(A){let L=ol.VxeUI.getComponent("VxeModal"),V=ol.VxeUI.getComponent("VxeDrawer"),$=ol.VxeUI.getComponent("VxeButton"),_=ol.VxeUI.getComponent("VxeNumberInput"),H=ol.VxeUI.getComponent("VxeRadioGroup"),N=(0,dl.inject)("$xeTable",{}),{props:P,reactData:B,internalData:a}=N,{computeCustomOpts:z,computeColumnDragOpts:j,computeColumnOpts:ae,computeIsMaxFixedColumn:U,computeResizableOpts:ne}=N.getComputeMaps(),R=(0,dl.ref)(),W=(0,dl.ref)(),i=(0,dl.ref)(),s=(0,dl.ref)(),c=(0,dl.ref)(),b,C=!1,w,S=e=>{var t=A.customStore;t.activeWrapper=!0,N.customOpenEvent(e)},I=e=>{let t=A.customStore;t.activeWrapper=!1,setTimeout(()=>{t.activeBtn||t.activeWrapper||N.customCloseEvent(e)},300)},q=({$event:e})=>{B.isCustomStatus=!0,N.saveCustom(),N.closeCustom(),N.emitCustomEvent("confirm",e)},X=({$event:e})=>{N.closeCustom(),N.emitCustomEvent("close",e)},K=({$event:e})=>{N.cancelCustom(),N.closeCustom(),N.emitCustomEvent("cancel",e)},r=e=>{N.resetCustom(!0),N.closeCustom(),N.emitCustomEvent("reset",e)},Y=({$event:t})=>{ol.VxeUI.modal?ol.VxeUI.modal.confirm({content:se("vxe.custom.cstmConfirmRestore"),className:"vxe-table--ignore-clear",escClosable:!0}).then(e=>{"confirm"===e&&r(t)}):r(t)},l=t=>{var e=B.customColumnList,e=al().findTree(e,e=>e===t);e&&e.parent&&(e=e.parent,e.children)&&e.children.length&&(e.renderVisible=e.children.every(e=>e.renderVisible),e.halfVisible=!e.renderVisible&&e.children.some(e=>e.renderVisible||e.halfVisible),l(e))},G=e=>{let t=!e.renderVisible;z.value.immediate?(al().eachTree([e],e=>{e.visible=t,e.renderVisible=t,e.halfVisible=!1}),B.isCustomStatus=!0,N.handleCustom(),N.saveCustomStore("update:visible")):al().eachTree([e],e=>{e.renderVisible=t,e.halfVisible=!1}),l(e),N.checkCustomStatus()},ie=e=>{z.value.immediate&&e.renderResizeWidth!==e.renderWidth&&(e.resizeWidth=e.renderResizeWidth,e.renderWidth=e.renderResizeWidth,B.isCustomStatus=!0,N.handleCustom(),N.saveCustomStore("update:width"))},Z=(e,t)=>{var r=U.value;z.value.immediate?(e.renderFixed===t?al().eachTree([e],e=>{e.fixed="",e.renderFixed=""}):r&&!e.renderFixed||al().eachTree([e],e=>{e.fixed=t,e.renderFixed=t}),B.isCustomStatus=!0,N.handleCustom(),N.saveCustomStore("update:fixed")):e.renderFixed===t?al().eachTree([e],e=>{e.renderFixed=""}):r&&!e.renderFixed||al().eachTree([e],e=>{e.renderFixed=t})},Q=()=>{N.toggleCustomAllCheckbox()},d=(e,t,r,l)=>{var o,a,n=W.value;n&&(o=n.getBoundingClientRect(),t&&(a=i.value)&&(r?(t=t.getBoundingClientRect(),a.style.display="block",a.style.top=Math.max(1,t.y+n.scrollTop-o.y)+"px",a.style.height=t.height+"px",a.style.width=t.width+"px",a.setAttribute("drag-pos",l),a.setAttribute("drag-to-child",C?"y":"n")):a.style.display=""),t=s.value)&&(t.style.display="block",t.style.top=Math.min(n.clientHeight+n.scrollTop-t.clientHeight,e.clientY+n.scrollTop-o.y)+"px",t.style.left=Math.min(n.clientWidth+n.scrollLeft-t.clientWidth,e.clientX+n.scrollLeft-o.x)+"px",t.setAttribute("drag-status",r?C?"sub":"normal":"disabled"))},o=()=>{var e=s.value,t=i.value;e&&(e.style.display=""),t&&(t.style.display="")},J=e=>{var e=e.currentTarget.parentElement.parentElement.parentElement,t=e.getAttribute("colid"),t=N.getColumnById(t);e.draggable=!0,c.value=t,hl(e,"active--drag-origin")},ee=e=>{e=e.currentTarget.parentElement.parentElement.parentElement;o(),e.draggable=!1,c.value=null,pl(e,"active--drag-origin")},te=e=>{e.dataTransfer&&e.dataTransfer.setDragImage(Il(),0,0)},re=d=>{let u=P.mouseConfig,p=B.customColumnList,h=a.collectColumn;let m=z.value.immediate;var e=d.currentTarget,t=c.value;let{isCrossDrag:v,isSelfToChildDrag:f,isToChildDrag:g,dragEndMethod:r}=j.value,x="bottom"===w?1:0;if(b&&t&&b!==t){let s=t,c=b;Promise.resolve(!r||r({oldColumn:s,newColumn:c,dragPos:w,dragToChild:!!C,offsetIndex:x})).then(o=>{if(o){let e=-1,t=-1,r={},l=(al().eachTree([s],e=>{r[e.id]=e}),!1);if(m){if(s.parentId&&c.parentId){if(!v)return;if(r[c.id]&&(l=!0,!v||!f))return void(ol.VxeUI.modal&&ol.VxeUI.modal.message({status:"error",content:se("vxe.error.treeDragChild")}))}else if(s.parentId){if(!v)return}else if(c.parentId){if(!v)return;if(r[c.id]&&(l=!0,!v||!f))return void(ol.VxeUI.modal&&ol.VxeUI.modal.message({status:"error",content:se("vxe.error.treeDragChild")}))}var a,n,i,o=al().findTree(h,e=>e.id===s.id),o=(l&&v&&f?o&&({items:a,index:i}=o,(n=s.children||[]).forEach(e=>{e.parentId=s.parentId}),a.splice(i,1,...n),s.children=[]):o&&({items:a,index:i,parent:n}=o,a.splice(i,1),n||(e=i)),al().findTree(h,e=>e.id===c.id));o&&({items:a,index:n,parent:i}=o,v&&g&&C?(s.parentId=c.id,c.children=(c.children||[]).concat([s])):(s.parentId=c.parentId,a.splice(n+x,0,s)),i||(t=n)),al().eachTree(h,(e,t,r,l,o)=>{o||(e.renderSortNumber=t+1)})}else e=al().findIndexOf(p,e=>e.id===s.id),p.splice(e,1),t=al().findIndexOf(p,e=>e.id===c.id),p.splice(t+x,0,s);B.isDragColMove=!0,u&&(N.clearSelected&&N.clearSelected(),N.clearCellAreas)&&(N.clearCellAreas(),N.clearCopyCellArea()),N.dispatchEvent("column-dragend",{oldColumn:s,newColumn:c,dragPos:w,offsetIndex:x,_index:{newIndex:t,oldIndex:e}},d),m&&(B.customColumnList=h.slice(0),N.handleColDragSwapColumn())}}).catch(()=>{})}o(),c.value=null,e.draggable=!1,e.removeAttribute("drag-pos"),pl(e,"active--drag-target"),pl(e,"active--drag-origin")},le=e=>{var t,r=z.value.immediate,{isCrossDrag:l,isToChildDrag:o}=j.value,a=e.currentTarget,n=e.ctrlKey,i=a.getAttribute("colid"),i=N.getColumnById(i),s=c.value;i&&(l||1===i.level)&&(e.preventDefault(),t=e.clientY-a.getBoundingClientRect().y<a.clientHeight/2?"top":"bottom",s&&s.id===i.id||!l&&1<i.level||!r&&1<i.level||i.renderFixed?d(e,a,!1,t):(C=!!(l&&o&&n&&r),b=i,w=t,d(e,a,!0,t)))},oe=()=>{var e=c.value,t=j.value;return(0,dl.h)("div",{},[(0,dl.h)("div",{ref:i,class:["vxe-table-custom-popup--drag-line",{"is--guides":t.showGuidesStatus}]}),(0,dl.h)("div",{ref:s,class:"vxe-table-custom-popup--drag-tip"},[(0,dl.h)("div",{class:"vxe-table-custom-popup--drag-tip-wrapper"},[(0,dl.h)("div",{class:"vxe-table-custom-popup--drag-tip-status"},[(0,dl.h)("span",{class:["vxe-table-custom-popup--drag-tip-normal-status",ce().TABLE_DRAG_STATUS_ROW]}),(0,dl.h)("span",{class:["vxe-table-custom-popup--drag-tip-sub-status",ce().TABLE_DRAG_STATUS_SUB_ROW]}),(0,dl.h)("span",{class:["vxe-table-custom-popup--drag-tip-disabled-status",ce().TABLE_DRAG_DISABLED]})]),(0,dl.h)("div",{class:"vxe-table-custom-popup--drag-tip-content"},se("vxe.custom.cstmDragTarget",[e&&"html"!==e.type?e.getTitle():""]))])])])};return(0,dl.nextTick)(()=>{var e=z.value.mode;L||Br("vxe.error.reqComp",["vxe-modal"]),V||"drawer"!==e||Br("vxe.error.reqComp",["vxe-drawer"]),$||Br("vxe.error.reqComp",["vxe-button"]),_||Br("vxe.error.reqComp",["vxe-number-input"]),H||Br("vxe.error.reqComp",["vxe-radio-group"])}),()=>{var e=z.value;return(["modal","drawer","popup"].includes(""+e.mode)?()=>{let t=A.customStore,d=P.resizable,{isCustomStatus:e,customColumnList:r}=B,l=z.value,u=l.immediate;var o=j.value;let{mode:a,modalOptions:n,drawerOptions:i,allowVisible:p,allowSort:h,allowFixed:m,allowResizable:v,checkMethod:f,visibleMethod:g}=l,x=ae.value,s=x.maxFixedSize,{minWidth:b,maxWidth:C}=ne.value;var c=Object.assign({},n),w=Object.assign({},i);let y=U.value,T=o.isCrossDrag;o=l.slots||{};let E=o.header,R=o.top,S=o.bottom,I=o.default,D=o.footer,M=[],k=t.isAll,F=t.isIndeterminate,O={$table:N,$grid:N.xegrid,columns:r,isAllChecked:k,isAllIndeterminate:F,isCustomStatus:e};al().eachTree(r,(l,o,e,t,a)=>{if(!g||g({column:l})){let e=0,t=0;v&&(o={$table:N,column:l,columnIndex:o,$columnIndex:o,$rowIndex:-1},b&&(e=al().toNumber(al().isFunction(b)?b(o):b)),C)&&(t=al().toNumber(al().isFunction(C)?C(o):C));var o=l.renderVisible,n=l.halfVisible,i=sl(l.getTitle(),1),s=l.children&&l.children.length;let r=!!f&&!f({column:l});var c=!o;M.push((0,dl.h)("tr",{key:l.id,colid:l.id,class:["vxe-table-custom-popup--row level--"+l.level,{"is--group":s}],onDragstart:te,onDragend:re,onDragover:le},[p?(0,dl.h)("td",{class:"vxe-table-custom-popup--column-item col--visible"},[(0,dl.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":o,"is--indeterminate":n,"is--disabled":r}],title:se("vxe.custom.setting.colVisible"),onClick:()=>{r||G(l)}},[(0,dl.h)("span",{class:["vxe-checkbox--icon",n?ce().TABLE_CHECKBOX_INDETERMINATE:o?ce().TABLE_CHECKBOX_CHECKED:ce().TABLE_CHECKBOX_UNCHECKED]})])]):(0,dl.createCommentVNode)(),(0,dl.h)("td",{class:"vxe-table-custom-popup--column-item col--name"},[(0,dl.h)("div",{class:"vxe-table-custom-popup--name"},[h?T&&u||1===l.level?(0,dl.h)("div",{class:["vxe-table-custom-popup--column-sort-btn",{"is--disabled":r||c||l.renderFixed}],title:se("vxe.custom.setting.sortHelpTip"),...r||c||l.renderFixed?{}:{onMousedown:J,onMouseup:ee}},[(0,dl.h)("i",{class:ce().TABLE_CUSTOM_SORT})]):(0,dl.h)("div",{class:"vxe-table-custom-popup--column-sort-placeholder"}):(0,dl.createCommentVNode)(),"html"===l.type?(0,dl.h)("div",{key:"1",class:"vxe-table-custom-popup--title",innerHTML:i}):(0,dl.h)("div",{key:"0",class:"vxe-table-custom-popup--title",title:i},i)])]),v?(0,dl.h)("td",{class:"vxe-table-custom-popup--column-item col--resizable"},[l.children&&l.children.length||!(al().isBoolean(l.resizable)?l.resizable:x.resizable||d)?(0,dl.h)("span","-"):_?(0,dl.h)(_,{type:"integer",immediate:!1,disabled:r||c,modelValue:l.renderResizeWidth,min:e||void 0,max:t||void 0,"onUpdate:modelValue"(e){e=Math.max(0,Number(e));l.renderResizeWidth=e},onChange(){ie(l)}}):(0,dl.createCommentVNode)()]):(0,dl.createCommentVNode)(),m?(0,dl.h)("td",{class:"vxe-table-custom-popup--column-item col--fixed"},[a?(0,dl.h)("span","-"):H?(0,dl.h)(H,{modelValue:l.renderFixed||"",type:"button",size:"mini",disabled:r||c,options:[{label:se("vxe.custom.setting.fixedLeft"),value:"left",disabled:r||c||y},{label:se("vxe.custom.setting.fixedUnset"),value:"",disabled:r||c},{label:se("vxe.custom.setting.fixedRight"),value:"right",disabled:r||c||y}],"onUpdate:modelValue"(e){Z(l,e)}}):(0,dl.createCommentVNode)()]):(0,dl.createCommentVNode)()]))}});o={default:()=>I?N.callSlot(I,O):(0,dl.h)("div",{ref:W,class:"vxe-table-custom-popup--body"},[R?(0,dl.h)("div",{class:"vxe-table-custom-popup--table-top"},N.callSlot(R,O)):Ce(N),(0,dl.h)("div",{class:"vxe-table-custom-popup--table-wrapper"},[(0,dl.h)("table",{},[(0,dl.h)("colgroup",{},[p?(0,dl.h)("col",{class:"vxe-table-custom-popup--table-col-seq"}):(0,dl.createCommentVNode)(),(0,dl.h)("col",{class:"vxe-table-custom-popup--table-col-title"}),v?(0,dl.h)("col",{class:"vxe-table-custom-popup--table-col-width"}):(0,dl.createCommentVNode)(),m?(0,dl.h)("col",{class:"vxe-table-custom-popup--table-col-fixed"}):(0,dl.createCommentVNode)()]),(0,dl.h)("thead",{},[(0,dl.h)("tr",{},[p?(0,dl.h)("th",{},[(0,dl.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":k,"is--indeterminate":F}],title:se("vxe.table.allTitle"),onClick:Q},[(0,dl.h)("span",{class:["vxe-checkbox--icon",F?ce().TABLE_CHECKBOX_INDETERMINATE:k?ce().TABLE_CHECKBOX_CHECKED:ce().TABLE_CHECKBOX_UNCHECKED]}),(0,dl.h)("span",{class:"vxe-checkbox--label"},se("vxe.toolbar.customAll"))])]):(0,dl.createCommentVNode)(),(0,dl.h)("th",{},se("vxe.custom.setting.colTitle")),v?(0,dl.h)("th",{},se("vxe.custom.setting.colResizable")):(0,dl.createCommentVNode)(),m?(0,dl.h)("th",{},se("vxe.custom.setting."+(s?"colFixedMax":"colFixed"),[s])):(0,dl.createCommentVNode)()])]),(0,dl.h)(dl.TransitionGroup,{class:"vxe-table-custom--panel-list",tag:"tbody",name:"vxe-table-custom--list"},{default:()=>M})])]),S?(0,dl.h)("div",{class:"vxe-table-custom-popup--table-bottom"},N.callSlot(S,O)):Ce(N),oe()]),footer:()=>D?N.callSlot(D,O):(0,dl.h)("div",{class:"vxe-table-custom-popup--footer"},[$?(0,dl.h)($,{content:l.resetButtonText||se("vxe.custom.cstmRestore"),disabled:!e,onClick:Y}):(0,dl.createCommentVNode)(),u?$?(0,dl.h)($,{content:l.closeButtonText||se("vxe.table.customClose"),onClick:X}):(0,dl.createCommentVNode)():$?(0,dl.h)($,{content:l.cancelButtonText||se("vxe.table.customCancel"),onClick:K}):(0,dl.createCommentVNode)(),!u&&$?(0,dl.h)($,{status:"primary",content:l.confirmButtonText||se("vxe.custom.cstmConfirm"),onClick:q}):(0,dl.createCommentVNode)()])};return E&&(o.header=()=>N.callSlot(E,O)),"drawer"===a?V?(0,dl.h)(V,{key:"drawer",className:["vxe-table-custom-drawer-wrapper","vxe-table--ignore-clear",w.className||""].join(" "),modelValue:t.visible,title:w.title||se("vxe.custom.cstmTitle"),width:w.width||Math.min(880,Math.floor(.6*document.documentElement.clientWidth)),position:w.position,resize:!!w.resize,escClosable:!!w.escClosable,maskClosable:!!w.maskClosable,destroyOnClose:!0,showFooter:!0,"onUpdate:modelValue"(e){t.visible=e}},o):(0,dl.createCommentVNode)():L?(0,dl.h)(L,{key:"modal",className:["vxe-table-custom-modal-wrapper","vxe-table--ignore-clear",c.className||""].join(" "),modelValue:t.visible,title:c.title||se("vxe.custom.cstmTitle"),width:c.width||Math.min(880,document.documentElement.clientWidth),minWidth:c.minWidth||700,height:c.height||Math.min(680,document.documentElement.clientHeight),minHeight:c.minHeight||400,showZoom:c.showZoom,showMaximize:c.showMaximize,showMinimize:c.showMinimize,mask:c.mask,lockView:c.lockView,resize:c.resize,escClosable:!!c.escClosable,maskClosable:!!c.maskClosable,destroyOnClose:!0,showFooter:!0,"onUpdate:modelValue"(e){t.visible=e}},o):(0,dl.createCommentVNode)()}:()=>{var e=A.customStore,{isCustomStatus:t,customColumnList:r}=B,l=z.value;let d=l.immediate;var o=j.value,a=e.maxHeight;let{checkMethod:u,visibleMethod:p,allowVisible:h,allowSort:m,allowFixed:v,trigger:n,placement:i}=l,f=U.value,g=o.isCrossDrag;var o=l.slots||{},s=o.header,c=o.top,x=o.bottom,b=o.default,o=o.footer;let C=[];var w={},y=e.isAll,T=e.isIndeterminate,E=("hover"===n&&(w.onMouseenter=S,w.onMouseleave=I),{$table:N,$grid:N.xegrid,columns:r,isAllChecked:y,isAllIndeterminate:T,isCustomStatus:t});return al().eachTree(r,(t,e,r,l,o)=>{if(!p||p({column:t})){var a=t.renderVisible,n=t.halfVisible,i=t.children&&t.children.length,s=sl(t.getTitle(),1);let e=!!u&&!u({column:t});var c=!a;C.push((0,dl.h)("li",{key:t.id,colid:t.id,class:["vxe-table-custom--option","level--"+t.level,{"is--hidden":e||c,"is--group":i}],onDragstart:te,onDragend:re,onDragover:le},[h?(0,dl.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":a,"is--indeterminate":n,"is--disabled":e}],title:se("vxe.custom.setting.colVisible"),onClick:()=>{e||G(t)}},[(0,dl.h)("span",{class:["vxe-checkbox--icon",n?ce().TABLE_CHECKBOX_INDETERMINATE:a?ce().TABLE_CHECKBOX_CHECKED:ce().TABLE_CHECKBOX_UNCHECKED]})]):(0,dl.createCommentVNode)(),(0,dl.h)("div",{class:"vxe-table-custom--name-option"},[m&&(g&&d||1===t.level)?(0,dl.h)("div",{class:"vxe-table-custom--sort-option"},[(0,dl.h)("span",{class:["vxe-table-custom--sort-btn",{"is--disabled":e||c||t.renderFixed}],title:se("vxe.custom.setting.sortHelpTip"),...e||c||t.renderFixed?{}:{onMousedown:J,onMouseup:ee}},[(0,dl.h)("i",{class:ce().TABLE_CUSTOM_SORT})])]):(0,dl.createCommentVNode)(),"html"===t.type?(0,dl.h)("div",{key:"1",class:"vxe-table-custom--checkbox-label",innerHTML:s}):(0,dl.h)("div",{key:"0",class:"vxe-table-custom--checkbox-label"},s)]),!o&&v?(0,dl.h)("div",{class:"vxe-table-custom--fixed-option"},[$?(0,dl.h)($,{mode:"text",icon:"left"===t.renderFixed?ce().TOOLBAR_TOOLS_FIXED_LEFT_ACTIVE:ce().TOOLBAR_TOOLS_FIXED_LEFT,status:"left"===t.renderFixed?"primary":"",disabled:e||c||f&&!t.renderFixed,title:se("left"===t.renderFixed?"vxe.toolbar.cancelFixed":"vxe.toolbar.fixedLeft"),onClick:()=>{Z(t,"left")}}):(0,dl.createCommentVNode)(),$?(0,dl.h)($,{mode:"text",icon:"right"===t.renderFixed?ce().TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVE:ce().TOOLBAR_TOOLS_FIXED_RIGHT,status:"right"===t.renderFixed?"primary":"",disabled:e||c||f&&!t.renderFixed,title:se("right"===t.renderFixed?"vxe.toolbar.cancelFixed":"vxe.toolbar.fixedRight"),onClick:()=>{Z(t,"right")}}):(0,dl.createCommentVNode)()]):(0,dl.createCommentVNode)()]))}}),(0,dl.h)("div",{ref:R,key:"simple",class:["vxe-table-custom-wrapper","placement--"+i,{"is--active":e.visible}],style:a&&!["left","right"].includes(i)?{maxHeight:a+"px"}:{}},e.visible?[(0,dl.h)("div",{class:"vxe-table-custom--header"},s?N.callSlot(s,E):[(0,dl.h)("ul",{class:"vxe-table-custom--panel-list"},[(0,dl.h)("li",{class:"vxe-table-custom--option"},[h?(0,dl.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":y,"is--indeterminate":T}],title:se("vxe.table.allTitle"),onClick:Q},[(0,dl.h)("span",{class:["vxe-checkbox--icon",T?ce().TABLE_CHECKBOX_INDETERMINATE:y?ce().TABLE_CHECKBOX_CHECKED:ce().TABLE_CHECKBOX_UNCHECKED]}),(0,dl.h)("span",{class:"vxe-checkbox--label"},se("vxe.toolbar.customAll"))]):(0,dl.h)("span",{class:"vxe-checkbox--label"},se("vxe.table.customTitle"))])])]),(0,dl.h)("div",{ref:W,class:"vxe-table-custom--body"},[c?(0,dl.h)("div",{class:"vxe-table-custom--panel-top"},N.callSlot(c,E)):Ce(N),b?(0,dl.h)("div",{class:"vxe-table-custom--panel-body"},N.callSlot(b,E)):(0,dl.h)(dl.TransitionGroup,{class:"vxe-table-custom--panel-list",name:"vxe-table-custom--list",tag:"ul",...w},{default:()=>C}),x?(0,dl.h)("div",{class:"vxe-table-custom--panel-bottom"},N.callSlot(x,E)):Ce(N),oe()]),l.showFooter?(0,dl.h)("div",{class:"vxe-table-custom--footer"},o?N.callSlot(o,E):[(0,dl.h)("div",{class:"vxe-table-custom--footer-buttons"},[$?(0,dl.h)($,{mode:"text",content:l.resetButtonText||se("vxe.table.customRestore"),disabled:!t,onClick:Y}):(0,dl.createCommentVNode)(),d?$?(0,dl.h)($,{mode:"text",content:l.closeButtonText||se("vxe.table.customClose"),onClick:X}):(0,dl.createCommentVNode)():$?(0,dl.h)($,{mode:"text",content:l.cancelButtonText||se("vxe.table.customCancel"),onClick:K}):(0,dl.createCommentVNode)(),!d&&$?(0,dl.h)($,{mode:"text",status:"primary",content:l.confirmButtonText||se("vxe.table.customConfirm"),onClick:q}):(0,dl.createCommentVNode)()])]):null]:[])})()}}});let{getI18n:we,getIcon:w,renderer:ye}=ol.VxeUI;var Ul=(0,dl.defineComponent)({name:"VxeTableFilterPanel",props:{filterStore:Object},setup(d){let u=(0,dl.inject)("$xeTable",{}),{reactData:p,internalData:h,getComputeMaps:e}=u,m=e().computeFilterOpts,v=(0,dl.computed)(()=>{var e=d.filterStore;return e&&e.options.some(e=>e.checked)}),l=(e,t)=>{var r=d.filterStore;r.options.forEach(e=>{e._checked=t,e.checked=t}),r.isAllSelected=t,r.isIndeterminate=!1},f=e=>{u.handleFilterConfirmFilter(e)};let g=e=>{u.handleFilterResetFilter(e)};let x=(e,t,r)=>{u.handleFilterChangeOption(e,t,r)},b=(e,t)=>{var r=d.filterStore;r.multiple?l(0,t):g(e)},C={changeRadioOption:(e,t,r)=>{u.handleFilterChangeRadioOption(e,t,r)},changeMultipleOption:(e,t,r)=>{u.handleFilterChangeMultipleOption(e,t,r)},changeAllOption:b,changeOption:x,confirmFilter:f,resetFilter:g};return()=>{var e=d.filterStore,t=p.initStore,{visible:r,multiple:l,column:o}=e,a=o?o.filterRender:null,n=nl(a)?ye.get(a.name):null,i=n?n.tableFilterClassName||n.filterClassName:"",s=Object.assign({},h._currFilterParams,{$panel:C,$table:u}),c=m.value.destroyOnClose;return(0,dl.h)("div",{class:["vxe-table--filter-wrapper","filter--prevent-default",yt(i,s),{"is--animat":u.props.animat,"is--multiple":l,"is--active":r}],style:e.style},t.filter&&(!c||r)&&o?((e,t)=>{let r=d.filterStore,{column:l,multiple:o,maxHeight:a}=r;var n=l?l.slots:null,n=n?n.filter:null,i=Object.assign({},h._currFilterParams,{$panel:C,$table:u}),t=t?t.renderTableFilter||t.renderFilter:null;return n?[(0,dl.h)("div",{class:"vxe-table--filter-template"},u.callSlot(n,i))]:t?[(0,dl.h)("div",{class:"vxe-table--filter-template"},yl(t(e,i)))]:(n=o?r.isAllSelected:!r.options.some(e=>e._checked),t=o&&r.isIndeterminate,[(0,dl.h)("ul",{class:"vxe-table--filter-header"},[(0,dl.h)("li",{class:["vxe-table--filter-option",{"is--checked":n,"is--indeterminate":t}],title:we(o?"vxe.table.allTitle":"vxe.table.allFilter"),onClick:e=>{b(e,!r.isAllSelected)}},(o?[(0,dl.h)("span",{class:["vxe-checkbox--icon",t?w().TABLE_CHECKBOX_INDETERMINATE:n?w().TABLE_CHECKBOX_CHECKED:w().TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,dl.h)("span",{class:"vxe-checkbox--label"},we("vxe.table.allFilter"))]))]),(0,dl.h)("ul",{class:"vxe-table--filter-body",style:a?{maxHeight:a+"px"}:{}},r.options.map(t=>{var e=t._checked;return(0,dl.h)("li",{class:["vxe-table--filter-option",{"is--checked":t._checked}],title:t.label,onClick:e=>{x(e,!t._checked,t)}},(o?[(0,dl.h)("span",{class:["vxe-checkbox--icon",e?w().TABLE_CHECKBOX_CHECKED:w().TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,dl.h)("span",{class:"vxe-checkbox--label"},sl(t.label,1))]))}))])})(a,n).concat((()=>{var e=d.filterStore,{column:t,multiple:r}=e,l=m.value,o=v.value,t=t.filterRender,t=nl(t)?ye.get(t.name):null,o=!o&&!e.isAllSelected&&!e.isIndeterminate;return r&&(!t||!1!==t.showTableFilterFooter&&!1!==t.showFilterFooter&&!1!==t.isFooter)?[(0,dl.h)("div",{class:"vxe-table--filter-footer"},[(0,dl.h)("button",{class:{"is--disabled":o},disabled:o,onClick:f},l.confirmButtonText||we("vxe.table.confirmFilter")),(0,dl.h)("button",{onClick:g},l.resetButtonText||we("vxe.table.resetFilter"))])]:[]})()):[])}}});let{getI18n:R,getIcon:Te}=ol.VxeUI;var Wl=(0,dl.defineComponent)({name:"VxeTableImportPanel",props:{defaultOptions:Object,storeData:Object},setup(d){let u=ol.VxeUI.getComponent("VxeModal"),p=ol.VxeUI.getComponent("VxeButton"),h=ol.VxeUI.getComponent("VxeSelect"),m=(0,dl.inject)("$xeTable",{}),l=m.getComputeMaps().computeImportOpts,v=(0,dl.reactive)({loading:!1}),f=(0,dl.ref)(),g=(0,dl.computed)(()=>{var e=d.storeData;return e.filename+"."+e.type}),x=(0,dl.computed)(()=>{var e=d.storeData;return e.file&&e.type}),b=(0,dl.computed)(()=>{var e=d.storeData;let{type:t,typeList:r}=e;return t?(e=al().find(r,e=>t===e.value))?e.label:"*.*":"*."+r.map(e=>e.value).join(", *.")}),C=()=>{var e=d.storeData;Object.assign(e,{filename:"",sheetName:"",type:""})},w=()=>{let{storeData:t,defaultOptions:e}=d;m.readFile(e).then(e=>{e=e.file;Object.assign(t,dr(e),{file:e})}).catch(e=>e)},y=()=>{(0,dl.nextTick)(()=>{var e=f.value;e&&e.focus()})},T=()=>{var e=d.storeData;e.visible=!1},E=()=>{let{storeData:e,defaultOptions:t}=d;var r=l.value;v.loading=!0,m.importByFile(e.file,Object.assign({},r,t)).then(()=>{v.loading=!1,e.visible=!1}).catch(()=>{v.loading=!1})};return(0,dl.nextTick)(()=>{u||Br("vxe.error.reqComp",["vxe-modal"]),p||Br("vxe.error.reqComp",["vxe-button"]),h||Br("vxe.error.reqComp",["vxe-select"])}),()=>{let{defaultOptions:t,storeData:r}=d,l=g.value,o=x.value,a=b.value;var e=t.slots||{};let n=e.top,i=e.bottom,s=e.default,c=e.footer;return u?(0,dl.h)(u,{id:"VXE_IMPORT_MODAL",modelValue:r.visible,title:R("vxe.import.impTitle"),className:"vxe-table-export-popup-wrapper",width:540,minWidth:360,minHeight:240,mask:!0,lockView:!0,showFooter:!0,escClosable:!0,maskClosable:!0,showMaximize:!0,resize:!0,loading:v.loading,"onUpdate:modelValue"(e){r.visible=e},onShow:y},{default:()=>{var e={$table:m,$grid:m.xegrid,options:t,params:t.params};return(0,dl.h)("div",{class:"vxe-table-export--panel"},[n?(0,dl.h)("div",{class:"vxe-table-export--panel-top"},m.callSlot(n,e)):(0,dl.createCommentVNode)(),(0,dl.h)("div",{class:"vxe-table-export--panel-body"},s?m.callSlot(s,e):[(0,dl.h)("table",{class:"vxe-table-export--panel-table",cellspacing:0,cellpadding:0,border:0},[(0,dl.h)("tbody",[(0,dl.h)("tr",[(0,dl.h)("td",R("vxe.import.impFile")),(0,dl.h)("td",[o?(0,dl.h)("div",{class:"vxe-table-export--selected--file",title:l},[(0,dl.h)("span",l),(0,dl.h)("i",{class:Te().INPUT_CLEAR,onClick:C})]):(0,dl.h)("button",{ref:f,class:"vxe-table-export--select--file",onClick:w},R("vxe.import.impSelect"))])]),(0,dl.h)("tr",[(0,dl.h)("td",R("vxe.import.impType")),(0,dl.h)("td",a)]),(0,dl.h)("tr",[(0,dl.h)("td",R("vxe.import.impMode")),(0,dl.h)("td",[h?(0,dl.h)(h,{modelValue:t.mode,options:r.modeList,"onUpdate:modelValue"(e){t.mode=e}}):(0,dl.createCommentVNode)()])])])])]),i?(0,dl.h)("div",{class:"vxe-table-export--panel-bottom"},m.callSlot(i,e)):(0,dl.createCommentVNode)()])},footer(){var e={$table:m,$grid:m.xegrid,options:t,params:t.params};return(0,dl.h)("div",{class:"vxe-table-export--panel-footer"},c?m.callSlot(c,e):[(0,dl.h)("div",{class:"vxe-table-export--panel-btns"},[p?(0,dl.h)(p,{content:R("vxe.import.impCancel"),onClick:T}):(0,dl.createCommentVNode)(),p?(0,dl.h)(p,{status:"primary",disabled:!o||v.loading,content:R("vxe.import.impConfirm"),onClick:E}):(0,dl.createCommentVNode)()])])}}):(0,dl.createCommentVNode)()}}});let{getI18n:z,getIcon:j}=ol.VxeUI;var ql=(0,dl.defineComponent)({name:"VxeTableExportPanel",props:{defaultOptions:Object,storeData:Object},setup(w){let y=ol.VxeUI.getComponent("VxeModal"),T=ol.VxeUI.getComponent("VxeButton"),E=ol.VxeUI.getComponent("VxeSelect"),R=ol.VxeUI.getComponent("VxeInput"),S=ol.VxeUI.getComponent("VxeCheckbox"),I=(0,dl.inject)("$xeTable",{}),{computeExportOpts:r,computePrintOpts:l}=I.getComputeMaps(),D=(0,dl.reactive)({isAll:!1,isIndeterminate:!1,loading:!1}),M=(0,dl.ref)(),k=(0,dl.ref)(),F=(0,dl.ref)(),O=(0,dl.computed)(()=>{var e=w.storeData;return e.columns.every(e=>e.checked)}),A=(0,dl.computed)(()=>{var e=w.defaultOptions;return-1<["html","xml","xlsx","pdf"].indexOf(e.type)}),L=(0,dl.computed)(()=>{var{storeData:e,defaultOptions:t}=w;return!t.original&&"current"===t.mode&&(e.isPrint||-1<["html","xlsx"].indexOf(t.type))}),V=(0,dl.computed)(()=>{var e=w.defaultOptions;return!e.original&&-1<["xlsx"].indexOf(e.type)}),$=t=>{var e=w.storeData,e=al().findTree(e.columns,e=>e===t);e&&e.parent&&(e=e.parent,e.children)&&e.children.length&&(e.checked=e.children.every(e=>e.checked),e.halfChecked=!e.checked&&e.children.some(e=>e.checked||e.halfChecked),$(e))},_=()=>{var e=w.storeData,e=e.columns;D.isAll=e.every(e=>e.disabled||e.checked),D.isIndeterminate=!D.isAll&&e.some(e=>!e.disabled&&(e.checked||e.halfChecked))},H=()=>{var e=w.storeData;let t=!D.isAll;al().eachTree(e.columns,e=>{e.disabled||(e.checked=t,e.halfChecked=!1)}),D.isAll=t,_()},N=()=>{(0,dl.nextTick)(()=>{var e=k.value,t=F.value,r=M.value,e=e||t||r;e&&e.focus()}),_()},o=()=>{var{storeData:e,defaultOptions:t}=w,{hasMerge:e,columns:r}=e,l=O.value,o=L.value,r=al().searchTree(r,e=>e.checked,{children:"children",mapChildren:"childNodes",original:!0});return Object.assign({},t,{columns:r,isMerge:!!(e&&o&&l)&&t.isMerge})},t=()=>{var e=w.storeData,t=l.value;e.visible=!1,I.print(Object.assign({},t,o()))},a=()=>{let e=w.storeData;var t=r.value;D.loading=!0,I.exportData(Object.assign({},t,o())).then(()=>{D.loading=!1,e.visible=!1}).catch(()=>{D.loading=!1})},P=()=>{var e=w.storeData;e.visible=!1},B=()=>{var e=w.storeData;(e.isPrint?t:a)()};return(0,dl.nextTick)(()=>{y||Br("vxe.error.reqComp",["vxe-modal"]),T||Br("vxe.error.reqComp",["vxe-button"]),E||Br("vxe.error.reqComp",["vxe-select"]),R||Br("vxe.error.reqComp",["vxe-input"]),S||Br("vxe.error.reqComp",["vxe-checkbox"])}),()=>{let{defaultOptions:t,storeData:r}=w,{isAll:l,isIndeterminate:o}=D,{hasTree:a,hasMerge:n,isPrint:i,hasColgroup:s,columns:c}=r,d=t.isHeader,u=[],p=O.value,h=A.value,m=L.value,v=V.value;var e=t.slots||{};let f=e.top,g=e.bottom,x=e.default,b=e.footer,C=e.parameter;return al().eachTree(c,e=>{var t=sl(e.getTitle(),1),r=e.children&&e.children.length,l=e.checked,o=e.halfChecked,a="html"===e.type;u.push((0,dl.h)("li",{key:e.id,class:["vxe-table-export--panel-column-option","level--"+e.level,{"is--group":r,"is--checked":l,"is--indeterminate":o,"is--disabled":e.disabled}],title:a?"":t,onClick:()=>{e.disabled||(e=>{let t=!e.checked;al().eachTree([e],e=>{e.checked=t,e.halfChecked=!1}),$(e),_()})(e)}},[(0,dl.h)("span",{class:["vxe-checkbox--icon",o?j().TABLE_CHECKBOX_INDETERMINATE:l?j().TABLE_CHECKBOX_CHECKED:j().TABLE_CHECKBOX_UNCHECKED]}),a?(0,dl.h)("span",{key:"1",class:"vxe-checkbox--label",innerHTML:t}):(0,dl.h)("span",{key:"0",class:"vxe-checkbox--label"},t)]))}),y?(0,dl.h)(y,{id:"VXE_EXPORT_MODAL",modelValue:r.visible,title:z(i?"vxe.export.printTitle":"vxe.export.expTitle"),className:"vxe-table-export-popup-wrapper",width:660,minWidth:500,minHeight:400,mask:!0,lockView:!0,showFooter:!0,escClosable:!0,maskClosable:!0,showMaximize:!0,resize:!0,loading:D.loading,"onUpdate:modelValue"(e){r.visible=e},onShow:N},{default:()=>{var e={$table:I,$grid:I.xegrid,options:t,columns:c,params:t.params};return(0,dl.h)("div",{class:"vxe-table-export--panel"},[f?(0,dl.h)("div",{class:"vxe-table-export--panel-top"},I.callSlot(f,e)):(0,dl.createCommentVNode)(),(0,dl.h)("div",{class:"vxe-table-export--panel-body"},x?I.callSlot(x,e):[(0,dl.h)("table",{class:"vxe-table-export--panel-table",cellspacing:0,cellpadding:0,border:0},[(0,dl.h)("tbody",[[i?(0,dl.createCommentVNode)():(0,dl.h)("tr",[(0,dl.h)("td",z("vxe.export.expName")),(0,dl.h)("td",[R?(0,dl.h)(R,{ref:k,modelValue:t.filename,type:"text",clearable:!0,placeholder:z("vxe.export.expNamePlaceholder"),"onUpdate:modelValue"(e){t.filename=e}}):(0,dl.createCommentVNode)()])]),i?(0,dl.createCommentVNode)():(0,dl.h)("tr",[(0,dl.h)("td",z("vxe.export.expType")),(0,dl.h)("td",[E?(0,dl.h)(E,{modelValue:t.type,options:r.typeList,"onUpdate:modelValue"(e){t.type=e}}):(0,dl.createCommentVNode)()])]),i||h?(0,dl.h)("tr",[(0,dl.h)("td",z("vxe.export.expSheetName")),(0,dl.h)("td",[R?(0,dl.h)(R,{ref:F,modelValue:t.sheetName,type:"text",clearable:!0,placeholder:z("vxe.export.expSheetNamePlaceholder"),"onUpdate:modelValue"(e){t.sheetName=e}}):(0,dl.createCommentVNode)()])]):(0,dl.createCommentVNode)(),(0,dl.h)("tr",[(0,dl.h)("td",z("vxe.export.expMode")),(0,dl.h)("td",[E?(0,dl.h)(E,{modelValue:t.mode,options:r.modeList.map(e=>({value:e.value,label:z(e.label)})),"onUpdate:modelValue"(e){t.mode=e}}):(0,dl.createCommentVNode)()])]),(0,dl.h)("tr",[(0,dl.h)("td",[z("vxe.export.expColumn")]),(0,dl.h)("td",[(0,dl.h)("div",{class:"vxe-table-export--panel-column"},[(0,dl.h)("ul",{class:"vxe-table-export--panel-column-header"},[(0,dl.h)("li",{class:["vxe-table-export--panel-column-option",{"is--checked":l,"is--indeterminate":o}],title:z("vxe.table.allTitle"),onClick:H},[(0,dl.h)("span",{class:["vxe-checkbox--icon",o?j().TABLE_CHECKBOX_INDETERMINATE:l?j().TABLE_CHECKBOX_CHECKED:j().TABLE_CHECKBOX_UNCHECKED]}),(0,dl.h)("span",{class:"vxe-checkbox--label"},z("vxe.export.expCurrentColumn"))])]),(0,dl.h)("ul",{class:"vxe-table-export--panel-column-body"},u)])])]),(0,dl.h)("tr",[(0,dl.h)("td",z("vxe.export.expOpts")),C?(0,dl.h)("td",[(0,dl.h)("div",{class:"vxe-table-export--panel-option-row"},I.callSlot(C,e))]):(0,dl.h)("td",[(0,dl.h)("div",{class:"vxe-table-export--panel-option-row"},[S?(0,dl.h)(S,{modelValue:t.isHeader,title:z("vxe.export.expHeaderTitle"),content:z("vxe.export.expOptHeader"),"onUpdate:modelValue"(e){t.isHeader=e}}):(0,dl.createCommentVNode)(),S?(0,dl.h)(S,{modelValue:t.isFooter,disabled:!r.hasFooter,title:z("vxe.export.expFooterTitle"),content:z("vxe.export.expOptFooter"),"onUpdate:modelValue"(e){t.isFooter=e}}):(0,dl.createCommentVNode)(),S?(0,dl.h)(S,{modelValue:t.original,title:z("vxe.export.expOriginalTitle"),content:z("vxe.export.expOptOriginal"),"onUpdate:modelValue"(e){t.original=e}}):(0,dl.createCommentVNode)()]),(0,dl.h)("div",{class:"vxe-table-export--panel-option-row"},[S?(0,dl.h)(S,{modelValue:!!(d&&s&&m)&&t.isColgroup,title:z("vxe.export.expColgroupTitle"),disabled:!d||!s||!m,content:z("vxe.export.expOptColgroup"),"onUpdate:modelValue"(e){t.isColgroup=e}}):(0,dl.createCommentVNode)(),S?(0,dl.h)(S,{modelValue:!!(n&&m&&p)&&t.isMerge,title:z("vxe.export.expMergeTitle"),disabled:!n||!m||!p,content:z("vxe.export.expOptMerge"),"onUpdate:modelValue"(e){t.isMerge=e}}):(0,dl.createCommentVNode)(),i||!S?(0,dl.createCommentVNode)():(0,dl.h)(S,{modelValue:!!v&&t.useStyle,disabled:!v,title:z("vxe.export.expUseStyleTitle"),content:z("vxe.export.expOptUseStyle"),"onUpdate:modelValue"(e){t.useStyle=e}}),S?(0,dl.h)(S,{modelValue:!!a&&t.isAllExpand,disabled:!a,title:z("vxe.export.expAllExpandTitle"),content:z("vxe.export.expOptAllExpand"),"onUpdate:modelValue"(e){t.isAllExpand=e}}):(0,dl.createCommentVNode)()])])])]])])]),g?(0,dl.h)("div",{class:"vxe-table-export--panel-bottom"},I.callSlot(g,e)):(0,dl.createCommentVNode)()])},footer(){var e={$table:I,$grid:I.xegrid,options:t,columns:c,params:t.params};return(0,dl.h)("div",{class:"vxe-table-export--panel-footer"},b?I.callSlot(b,e):[(0,dl.h)("div",{class:"vxe-table-export--panel-btns"},[T?(0,dl.h)(T,{content:z("vxe.export.expCancel"),onClick:P}):(0,dl.createCommentVNode)(),T?(0,dl.h)(T,{ref:M,status:"primary",content:z(i?"vxe.export.expPrint":"vxe.export.expConfirm"),onClick:B}):(0,dl.createCommentVNode)()])])}}):(0,dl.createCommentVNode)()}}});let Ee=ol.VxeUI.getIcon;var Xl=(0,dl.defineComponent)({name:"VxeTableMenuPanel",setup(e,t){var r=al().uniqueId();let s=(0,dl.inject)("$xeTable",{}),l=s.reactData,o=(0,dl.ref)(),a={refElem:o};r={xID:r,props:e,context:t,getRefMaps:()=>a};return r.renderVN=()=>{let i=l.ctxMenuStore;var e=s.getComputeMaps().computeMenuOpts,e=e.value;return(0,dl.h)(dl.Teleport,{to:"body",disabled:!1},[(0,dl.h)("div",{ref:o,class:["vxe-table--context-menu-wrapper",e.className,{"is--visible":i.visible}],style:i.style},i.list.map((e,n)=>e.every(e=>!1===e.visible)?(0,dl.createCommentVNode)():(0,dl.h)("ul",{class:"vxe-context-menu--option-wrapper",key:n},e.map((o,a)=>{var e=o.children&&o.children.some(e=>!1!==e.visible),t=Object.assign({},o.prefixConfig),r=Object.assign({},o.suffixConfig);return!1===o.visible?null:(0,dl.h)("li",{class:[o.className,{"link--disabled":o.disabled,"link--active":o===i.selected}],key:n+"_"+a},[(0,dl.h)("a",{class:"vxe-context-menu--link",onClick(e){s.ctxMenuLinkEvent(e,o)},onMouseover(e){s.ctxMenuMouseoverEvent(e,o)},onMouseout(e){s.ctxMenuMouseoutEvent(e,o)}},[(0,dl.h)("div",{class:["vxe-context-menu--link-prefix",t.className||""]},[(0,dl.h)("i",{class:t.icon||o.prefixIcon}),t.content?(0,dl.h)("span",{},""+t.content):(0,dl.createCommentVNode)()]),(0,dl.h)("div",{class:"vxe-context-menu--link-content"},il(o.name)),(0,dl.h)("div",{class:["vxe-context-menu--link-suffix",r.className||""]},[(0,dl.h)("i",{class:r.icon||o.suffixIcon||(e?Ee().TABLE_MENU_OPTIONS:"")}),r.content?(0,dl.h)("span",""+r.content):(0,dl.createCommentVNode)()])]),e?(0,dl.h)("ul",{class:["vxe-table--context-menu-clild-wrapper",{"is--show":o===i.selected&&i.showChild}]},o.children.map((t,e)=>{var r=Object.assign({},t.prefixConfig),l=Object.assign({},t.suffixConfig);return!1===t.visible?null:(0,dl.h)("li",{class:[t.className,{"link--disabled":t.disabled,"link--active":t===i.selectChild}],key:n+`_${a}_`+e},[(0,dl.h)("a",{class:"vxe-context-menu--link",onClick(e){s.ctxMenuLinkEvent(e,t)},onMouseover(e){s.ctxMenuMouseoverEvent(e,o,t)},onMouseout(e){s.ctxMenuMouseoutEvent(e,o)}},[(0,dl.h)("div",{class:["vxe-context-menu--link-prefix",r.className||""]},[(0,dl.h)("i",{class:r.icon||t.prefixIcon}),r.content?(0,dl.h)("span",""+r.content):(0,dl.createCommentVNode)()]),(0,dl.h)("div",{class:"vxe-context-menu--link-content"},il(t.name)),(0,dl.h)("div",{class:["vxe-context-menu--link-suffix",l.className||""]},[(0,dl.h)("i",{class:l.icon}),l.content?(0,dl.h)("span",""+l.content):(0,dl.createCommentVNode)()])])])})):null])}))))])},r},render(){return this.renderVN()}});let{getConfig:Ur,getIcon:Yr,getI18n:Wr,renderer:Gr,formats:Zr,createEvent:Qr,globalResize:Jr,interceptor:el,hooks:tl,globalEvents:qr,GLOBAL_EVENT_KEYS:Xr,useFns:rl,renderEmptyElement:Kr}=ol.VxeUI,ll="VXE_CUSTOM_STORE";var $r=(0,dl.defineComponent)({name:"VxeTable",props:zl,emits:Vr,setup(z,e){let{slots:j,emit:l}=e,de=al().uniqueId(),ue=ol.VxeUI.getComponent("VxeLoading"),pe=ol.VxeUI.getComponent("VxeTooltip"),t=(0,dl.inject)("$xeTabs",null),he=rl.useSize(z).computeSize,U=(0,dl.reactive)({staticColumns:[],tableGroupColumn:[],tableColumn:[],tableData:[],scrollXLoad:!1,scrollYLoad:!1,overflowY:!0,overflowX:!1,scrollbarWidth:0,scrollbarHeight:0,lastScrollTime:0,rowHeight:0,parentHeight:0,isGroup:!1,isAllOverflow:!1,isAllSelected:!1,isIndeterminate:!1,selectCheckboxMaps:{},currentRow:null,currentColumn:null,selectRadioRow:null,footerTableData:[],expandColumn:null,treeNodeColumn:null,hasFixedColumn:!1,rowExpandedMaps:{},rowExpandLazyLoadedMaps:{},treeExpandedMaps:{},treeExpandLazyLoadedMaps:{},treeIndeterminateMaps:{},mergeList:[],mergeFooterList:[],upDataFlag:0,reColumnFlag:0,pendingRowMaps:{},initStore:{filter:!1,import:!1,export:!1,custom:!1},customStore:{btnEl:null,isAll:!1,isIndeterminate:!1,activeBtn:!1,activeWrapper:!1,visible:!1,maxHeight:0,oldSortMaps:{},oldFixedMaps:{},oldVisibleMaps:{}},customColumnList:[],filterStore:{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1,maxHeight:null},columnStore:{leftList:[],centerList:[],rightList:[],resizeList:[],pxList:[],pxMinList:[],autoMinList:[],scaleList:[],scaleMinList:[],autoList:[],remainList:[]},ctxMenuStore:{selected:null,visible:!1,showChild:!1,selectChild:null,list:[],style:null},editStore:{indexs:{columns:[]},titles:{columns:[]},selected:{row:null,column:null},copyed:{cut:!1,rows:[],columns:[]},actived:{row:null,column:null},focused:{row:null,column:null},insertMaps:{},removeMaps:{}},tooltipStore:{row:null,column:null,content:null,visible:!1,currOpts:{}},validStore:{visible:!1},validErrorMaps:{},importStore:{inited:!1,file:null,type:"",modeList:[],typeList:[],filename:"",visible:!1},importParams:{mode:"",types:null,message:!0},exportStore:{inited:!1,name:"",modeList:[],typeList:[],columns:[],isPrint:!1,hasFooter:!1,hasMerge:!1,hasTree:!1,hasColgroup:!1,visible:!1},exportParams:{filename:"",sheetName:"",mode:"",type:"",isColgroup:!1,isMerge:!1,isAllExpand:!1,useStyle:!1,original:!1,message:!0,isHeader:!1,isFooter:!1},scrollVMLoading:!1,calcCellHeightFlag:0,resizeHeightFlag:0,isCustomStatus:!1,isDragRowMove:!1,dragRow:null,isDragColMove:!1,dragCol:null,dragTipText:"",isDragResize:!1,isRowLoading:!1,isColLoading:!1}),B={tZindex:0,elemStore:{},scrollXStore:{preloadSize:0,offsetSize:0,visibleSize:0,visibleStartIndex:0,visibleEndIndex:0,startIndex:0,endIndex:0},scrollYStore:{preloadSize:0,offsetSize:0,visibleSize:0,visibleStartIndex:0,visibleEndIndex:0,startIndex:0,endIndex:0},tableWidth:0,tableHeight:0,headerHeight:0,footerHeight:0,customHeight:0,customMinHeight:0,customMaxHeight:0,hoverRow:null,lastScrollLeft:0,lastScrollTop:0,radioReserveRow:null,checkboxReserveRowMap:{},rowExpandedReserveRowMap:{},treeExpandedReserveRowMap:{},treeIndeterminateRowMaps:{},tableFullData:[],afterFullData:[],afterTreeFullData:[],afterFullRowMaps:{},tableFullTreeData:[],tableSynchData:[],tableSourceData:[],collectColumn:[],tableFullColumn:[],visibleColumn:[],fullAllDataRowIdData:{},sourceDataRowIdData:{},fullDataRowIdData:{},fullColumnIdData:{},fullColumnFieldData:{},inited:!1,tooltipTimeout:null,initStatus:!1,isActivated:!1},P={},W={},q=(0,dl.ref)(),X=(0,dl.ref)(),me=(0,dl.ref)(),ve=(0,dl.ref)(),fe=(0,dl.ref)(),ge=(0,dl.ref)(),xe=(0,dl.ref)(),c=(0,dl.ref)(),u=(0,dl.ref)(),b=(0,dl.ref)(),p=(0,dl.ref)(),d=(0,dl.ref)(),h=(0,dl.ref)(),m=(0,dl.ref)(),v=(0,dl.ref)(),w=(0,dl.ref)(),y=(0,dl.ref)(),K=(0,dl.ref)(),Y=(0,dl.ref)(),be=(0,dl.ref)(),Ce=(0,dl.ref)(),we=(0,dl.ref)(),ye=(0,dl.ref)(),Te=(0,dl.ref)(),Ee=(0,dl.ref)(),Re=(0,dl.ref)(),Se=(0,dl.ref)(),S=(0,dl.ref)(),Ie=(0,dl.ref)(),De=(0,dl.ref)(),I=(0,dl.ref)(),Me=(0,dl.ref)(),ke=(0,dl.ref)(),Fe=(0,dl.ref)(),Oe=(0,dl.ref)(),k=(0,dl.ref)(),F=(0,dl.ref)(),G=(0,dl.inject)("$xeGrid",null),O,A=(0,dl.computed)(()=>{var e=z.id;return e?al().isFunction(e)?""+(e({$table:se,$grid:G})||""):""+e:""}),Ae=(0,dl.computed)(()=>Object.assign({},Ur().table.validConfig,z.validConfig)),L=(0,dl.computed)(()=>s.value),V=(0,dl.computed)(()=>{var e=L.value.threshold;return e?al().toNumber(e):0}),f=(0,dl.computed)(()=>$.value),s=(0,dl.computed)(()=>Object.assign({},Ur().table.scrollX,Ur().table.virtualXConfig,z.scrollX,z.virtualXConfig)),$=(0,dl.computed)(()=>Object.assign({},Ur().table.scrollY,Ur().table.virtualYConfig,z.scrollY,z.virtualYConfig)),r=(0,dl.computed)(()=>Object.assign({},Ur().table.scrollbarConfig,z.scrollbarConfig)),Z=(0,dl.computed)(()=>{var e=r.value;return!(!e.x||"top"!==e.x.position)}),Q=(0,dl.computed)(()=>{var e=r.value;return!(!e.y||"left"!==e.y.position)}),_=(0,dl.computed)(()=>{var e=f.value.threshold;return e?al().toNumber(e):0}),o=(0,dl.computed)(()=>({default:48,medium:44,small:40,mini:36})),E=(0,dl.computed)(()=>{var e=he.value;return o.value[e||"default"]||18}),J=(0,dl.computed)(()=>Object.assign({},Ur().table.columnConfig,z.columnConfig)),R=(0,dl.computed)(()=>Object.assign({},Ur().table.cellConfig,z.cellConfig));var a=(0,dl.computed)(()=>Object.assign({},Ur().table.headerCellConfig,z.headerCellConfig)),n=(0,dl.computed)(()=>Object.assign({},Ur().table.footerCellConfig,z.footerCellConfig));let ee=(0,dl.computed)(()=>Object.assign({},Ur().table.rowConfig,z.rowConfig)),te=(0,dl.computed)(()=>Object.assign({},Ur().table.rowDragConfig,z.rowDragConfig)),re=(0,dl.computed)(()=>Object.assign({},Ur().table.columnDragConfig,z.columnDragConfig)),i=(0,dl.computed)(()=>Object.assign({},Ur().table.resizeConfig,z.resizeConfig)),Le=(0,dl.computed)(()=>Object.assign({},Ur().table.resizableConfig,z.resizableConfig));var H=(0,dl.computed)(()=>Object.assign({startIndex:0},Ur().table.seqConfig,z.seqConfig));let D=(0,dl.computed)(()=>Object.assign({},Ur().table.radioConfig,z.radioConfig)),le=(0,dl.computed)(()=>Object.assign({},Ur().table.checkboxConfig,z.checkboxConfig)),N=(0,dl.computed)(()=>Object.assign({},Ur().tooltip,Ur().table.tooltipConfig,z.tooltipConfig)),Ve=(0,dl.computed)(()=>{var e=U.tooltipStore,t=N.value;return Object.assign({},t,e.currOpts)}),$e=(0,dl.computed)(()=>{var e=N.value;return Object.assign({},e)}),oe=(0,dl.computed)(()=>Object.assign({},Ur().table.editConfig,z.editConfig)),C=(0,dl.computed)(()=>Object.assign({orders:["asc","desc",null]},Ur().table.sortConfig,z.sortConfig)),_e=(0,dl.computed)(()=>Object.assign({},Ur().table.filterConfig,z.filterConfig)),ae=(0,dl.computed)(()=>Object.assign({},Ur().table.mouseConfig,z.mouseConfig)),He=(0,dl.computed)(()=>Object.assign({},Ur().table.areaConfig,z.areaConfig)),ne=(0,dl.computed)(()=>Object.assign({},Ur().table.keyboardConfig,z.keyboardConfig));var Ne=(0,dl.computed)(()=>Object.assign({},Ur().table.clipConfig,z.clipConfig)),Pe=(0,dl.computed)(()=>Object.assign({},Ur().table.fnrConfig,z.fnrConfig));let Be=(0,dl.computed)(()=>Object.assign({},Ur().table.menuConfig,z.menuConfig)),ze=(0,dl.computed)(()=>{var e=U.columnStore,t=e.leftList;let r=0;for(let e=0;e<t.length;e++){var l=t[e];r+=l.renderWidth}return r}),je=(0,dl.computed)(()=>{var e=U.columnStore,t=e.rightList;let r=0;for(let e=0;e<t.length;e++){var l=t[e];r+=l.renderWidth}return r}),Ue=(0,dl.computed)(()=>{var e=Be.value.header;return e&&e.options?e.options:[]}),We=(0,dl.computed)(()=>{var e=Be.value.body;return e&&e.options?e.options:[]}),qe=(0,dl.computed)(()=>{var e=Be.value.footer;return e&&e.options?e.options:[]}),Xe=(0,dl.computed)(()=>{var e=Be.value,t=Ue.value,r=We.value,l=qe.value;return!!(z.menuConfig&&nl(e)&&(t.length||r.length||l.length))}),Ke=(0,dl.computed)(()=>{var e=U.ctxMenuStore;let t=[];return e.list.forEach(e=>{e.forEach(e=>{t.push(e)})}),t}),Ye=(0,dl.computed)(()=>Object.assign({},Ur().table.exportConfig,z.exportConfig)),Ge=(0,dl.computed)(()=>Object.assign({},Ur().table.importConfig,z.importConfig));var Ze=(0,dl.computed)(()=>Object.assign({},Ur().table.printConfig,z.printConfig));let M=(0,dl.computed)(()=>Object.assign({},Ur().table.expandConfig,z.expandConfig)),ie=(0,dl.computed)(()=>Object.assign({},Ur().table.treeConfig,z.treeConfig)),Qe=(0,dl.computed)(()=>Object.assign({},Ur().table.emptyRender,z.emptyRender)),Je=(0,dl.computed)(()=>Object.assign({},Ur().table.loadingConfig,z.loadingConfig)),et=(0,dl.computed)(()=>z.border?Math.max(2,Math.ceil(U.scrollbarWidth/U.tableColumn.length)):1),T=(0,dl.computed)(()=>Object.assign({},Ur().table.customConfig,z.customConfig)),tt=(0,dl.computed)(()=>{var e=B.visibleColumn,t=U.tableColumn;return t.length||e.length?e.filter(e=>"auto"===e.width||"auto"===e.minWidth):[]}),rt=(0,dl.computed)(()=>{var e=U.tableColumn,t=B.collectColumn;let r=0;return e.length&&t.length&&t.forEach(e=>{e.renderFixed&&r++}),r}),lt=(0,dl.computed)(()=>{var e=rt.value,t=J.value.maxFixedSize;return!!t&&t<=e}),ot=(0,dl.computed)(()=>{var e=z.border;return!0===e?"full":e||"default"});var at=(0,dl.computed)(()=>{var{}=z,e=U.tableData,t=B.tableFullData;let{strict:r,checkMethod:l}=le.value;return!!r&&(!e.length&&!t.length||!!l&&t.every(e=>!l({row:e})))}),nt=(0,dl.computed)(()=>{var{overflowX:e,scrollXLoad:t,overflowY:r,scrollYLoad:l}=U;return{x:e&&t,y:r&&l}});let it={refElem:q,refTooltip:X,refValidTooltip:ve,refTableFilter:ge,refTableCustom:xe,refTableMenu:fe,refTableHeader:u,refTableBody:b,refTableFooter:p,refTableLeftHeader:d,refTableLeftBody:h,refTableLeftFooter:m,refTableRightHeader:v,refTableRightBody:w,refTableRightFooter:y,refLeftContainer:K,refRightContainer:Y,refColResizeBar:be,refRowResizeBar:Ce,refScrollXVirtualElem:Re,refScrollYVirtualElem:Se,refScrollXHandleElem:S,refScrollYHandleElem:I,refScrollXSpaceElem:k,refScrollYSpaceElem:F},st={computeSize:he,computeTableId:A,computeValidOpts:Ae,computeVirtualXOpts:s,computeVirtualYOpts:$,computeScrollbarOpts:r,computeScrollbarXToTop:Z,computeScrollbarYToLeft:Q,computeColumnOpts:J,computeScrollXThreshold:V,computeScrollYThreshold:_,computeDefaultRowHeight:E,computeCellOpts:R,computeHeaderCellOpts:a,computeFooterCellOpts:n,computeRowOpts:ee,computeRowDragOpts:te,computeColumnDragOpts:re,computeResizeOpts:i,computeResizableOpts:Le,computeSeqOpts:H,computeRadioOpts:D,computeCheckboxOpts:le,computeTooltipOpts:N,computeEditOpts:oe,computeSortOpts:C,computeFilterOpts:_e,computeMouseOpts:ae,computeAreaOpts:He,computeKeyboardOpts:ne,computeClipOpts:Ne,computeFNROpts:Pe,computeHeaderMenu:Ue,computeBodyMenu:We,computeFooterMenu:qe,computeIsMenu:Xe,computeMenuOpts:Be,computeExportOpts:Ye,computeImportOpts:Ge,computePrintOpts:Ze,computeExpandOpts:M,computeTreeOpts:ie,computeEmptyOpts:Qe,computeLoadingOpts:Je,computeCellOffsetWidth:et,computeCustomOpts:T,computeLeftFixedWidth:ze,computeRightFixedWidth:je,computeFixedColumnSize:rt,computeIsMaxFixedColumn:lt,computeIsAllCheckboxDisabled:at,computeVirtualScrollBars:nt,computeSXOpts:L,computeSYOpts:f},se={xID:de,props:z,context:e,reactData:U,internalData:B,getRefMaps:()=>it,getComputeMaps:()=>st,xegrid:G},ct=(e,t,r)=>{e=al().get(e,r),t=al().get(t,r);return!(!cl(e)||!cl(t))||(al().isString(e)||al().isNumber(e)?""+e==""+t:al().isEqual(e,t))},dt=e=>{var t=C.value.orders,e=e.order||null,e=t.indexOf(e)+1;return t[e<t.length?e:0]},ut=e=>{var t=Ur().version,r=al().toStringJSON(localStorage.getItem(ll)||""),r=r&&r._v===t?r:{_v:t};return(e?r[e]:r)||{}},pt=e=>{let r=B.fullAllDataRowIdData,l={};return al().each(e,(e,t)=>{r[t]&&(l[t]=e)}),l},ht=e=>{let r=B.fullDataRowIdData,l=[];return al().each(e,(e,t)=>{r[t]&&-1===se.findRowIndexOf(l,r[t].row)&&l.push(r[t].row)}),l},mt=()=>{var{elemStore:e,visibleColumn:a}=B,t=ze.value,n=je.value,e=wl(e["main-body-scroll"]);if(e){var{scrollLeft:e,clientWidth:i}=e,s=e+t,c=e+i-n;let r=-1,l=0,o=0;for(let e=0,t=a.length;e<t&&(l+=a[e].renderWidth,!(0<=(r=-1===r&&s<l?e:r)&&(o++,l>c)));e++);return{toVisibleIndex:Math.max(0,r),visibleSize:Math.max(1,o)}}return{toVisibleIndex:0,visibleSize:6}},vt=()=>{var t=U.isAllOverflow,r=u.value,l=b.value,l=l?l.$el:null,e=E.value;let o=0;if(t){if(l){t=r?r.$el:null;let e;(e=!(e=l.querySelector("tr"))&&t?t.querySelector("tr"):e)&&(o=e.clientHeight)}o=o||e}else o=e;return Math.max(18,o)},ft=e=>{var t=U.isAllOverflow,{elemStore:a,isResizeCellHeight:n,afterFullData:i,fullAllDataRowIdData:s}=B,c=ee.value,d=R.value,u=E.value,a=wl(a["main-body-scroll"]);if(a){var p=a.clientHeight,h=al().isNumber(e)?e:a.scrollTop,m=h+p;let r=-1,l=0,o=0;if(!(n||d.height||c.height)&&t)r=Math.floor(h/u),o=Math.ceil(p/u)+1;else for(let e=0,t=i.length;e<t;e++){var v=i[e],v=s[xl(se,v)]||{};if(l+=v.resizeHeight||v.height||d.height||c.height||u,0<=(r=-1===r&&h<l?e:r)&&(o++,l>m))break}return{toVisibleIndex:Math.max(0,r),visibleSize:Math.max(6,o)}}return{toVisibleIndex:0,visibleSize:6}},gt=(r,l,o)=>{for(let e=0,t=r.length;e<t;e++){var a=r[e],{startIndex:n,endIndex:i}=l,s=a[o],a=s+a[o+"span"];s<n&&n<a&&(l.startIndex=s),s<i&&i<a&&(l.endIndex=a),l.startIndex===n&&l.endIndex===i||(e=-1)}},xt=(e,i,s)=>{if(e){var t=z.treeConfig;let n=B.visibleColumn;al().isArray(e)||(e=[e]),t&&e.length&&Br("vxe.error.noTree",["merge-cells | merge-footer-items"]),e.forEach(e=>{let{row:t,col:r,rowspan:l,colspan:o}=e;var a;s&&al().isNumber(t)&&(t=s[t]),al().isNumber(r)&&(r=n[r]),(s?t:al().isNumber(t))&&r&&(l||o)&&(l=al().toNumber(l)||1,o=al().toNumber(o)||1,1<l||1<o)&&(e=al().findIndexOf(i,e=>!(e._row!==t&&xl(se,e._row)!==xl(se,t)||e._col.id!==r&&e._col.id!==r.id)),(e=i[e])?(e.rowspan=l,e.colspan=o,e._rowspan=l,e._colspan=o):(e=s?se.findRowIndexOf(s,t):t,a=P.getVTColumnIndex(r),i.push({row:e,col:a,rowspan:l,colspan:o,_row:t,_col:r,_rowspan:l,_colspan:o})))})}},bt=(e,o,a)=>{let n=[];if(e){var t=z.treeConfig;let l=B.visibleColumn;al().isArray(e)||(e=[e]),t&&e.length&&Br("vxe.error.noTree",["merge-cells | merge-footer-items"]),e.forEach(e=>{let{row:t,col:r}=e;a&&al().isNumber(t)&&(t=a[t]),al().isNumber(r)&&(r=l[r]);var e=al().findIndexOf(o,e=>!(e._row!==t&&xl(se,e._row)!==xl(se,t)||e._col.id!==r&&e._col.id!==r.id));-1<e&&(e=o.splice(e,1),n.push(e[0]))})}return n},Ct=()=>{var e=B.tableFullColumn;e.forEach(e=>{e.order=null})},wt=e=>{var t,r=U.parentHeight,e=z[e];let l=0;return l=e?"100%"===e||"auto"===e?r:(t=se.getExcludeHeight(),l=Ml(e)?Math.floor((al().toInteger(e)||1)/100*r):al().toNumber(e),Math.max(40,l-t)):l},yt=e=>{var t=B.collectColumn;let{resizableData:n,sortData:i,visibleData:s,fixedData:c}=e,d=!1;n||i||s||c?(al().eachTree(t,(e,t,r,l,o)=>{var a=e.getKey();o||(c&&void 0!==c[a]&&(e.fixed=c[a]),i&&al().isNumber(i[a])&&(d=!0,e.renderSortNumber=i[a])),n&&al().isNumber(n[a])&&(e.resizeWidth=n[a]),s&&al().isBoolean(s[a])&&(e.visible=s[a])}),d&&(t=al().orderBy(t,"renderSortNumber"),B.collectColumn=t,B.tableFullColumn=Qt(t)),U.isCustomStatus=!0):U.isCustomStatus=!1},Tt=()=>{var{tableFullColumn:e,collectColumn:t}=B;let d=B.fullColumnIdData={},u=B.fullColumnFieldData={};var r=ae.value;let p=J.value,{isCrossDrag:h,isSelfToChildDrag:m}=re.value,v=T.value.storage;var l=ee.value,o=t.some(Rl);let f=!!z.showOverflow,g,x,b,C,w,y,n=(e,t,r,l,o)=>{var{id:a,field:n,fixed:i,type:s,treeNode:c}=e,t={$index:-1,_index:-1,column:e,colid:a,index:t,items:r,parent:o||null,width:0};n?(u[n]&&Br("vxe.error.colRepet",["field",n]),u[n]=t):(v&&!s||p.drag&&(h||m))&&Br("vxe.error.reqProp",[`${e.getTitle()||s||""} -> column.field`]),!y&&i&&(y=i),w||"html"!==s||(w=e),c?(x&&Pr("vxe.error.colRepet",["tree-node",c]),x=x||e):"expand"===s&&(g&&Pr("vxe.error.colRepet",["type",s]),g=g||e),"checkbox"===s?(b&&Pr("vxe.error.colRepet",["type",s]),b=b||e):"radio"===s&&(C&&Pr("vxe.error.colRepet",["type",s]),C=C||e),f&&!1===e.showOverflow&&(f=!1),d[a]&&Br("vxe.error.colRepet",["colId",a]),d[a]=t};o?al().eachTree(t,(e,t,r,l,o,a)=>{e.level=a.length,n(e,t,r,0,o)}):e.forEach(n),g&&r.area&&Br("vxe.error.errConflicts",["mouse-config.area","column.type=expand"]),w&&(p.useKey||Br("vxe.error.reqProp",["column-config.useKey & column.type=html"]),l.useKey||Br("vxe.error.reqProp",["row-config.useKey & column.type=html"])),U.isGroup=o,U.treeNodeColumn=x,U.expandColumn=g,U.isAllOverflow=f},Et=()=>{B.customHeight=wt("height"),B.customMinHeight=wt("minHeight"),B.customMaxHeight=wt("maxHeight")},Rt=(e,t)=>{var r=t.querySelectorAll(`.vxe-cell--wrapper[colid="${e.id}"]`);let l=0;var t=r[0];t&&t.parentElement&&(t=getComputedStyle(t.parentElement),l=Math.ceil(al().toNumber(t.paddingLeft)+al().toNumber(t.paddingRight)));let o=e.renderAutoWidth-l;for(let e=0;e<r.length;e++){var a=r[e];o=Math.max(o,a?Math.ceil(a.scrollWidth)+4:0)}return o+l},St=()=>{var e=tt.value;let l=B.fullColumnIdData,o=q.value;o&&(o.setAttribute("data-calc-col","Y"),e.forEach(e=>{var t=e.id,t=l[t],r=Rt(e,o);t&&(t.width=Math.max(r,t.width)),e.renderAutoWidth=r}),se.analyColumnWidth(),o.removeAttribute("data-calc-col"))},It=()=>{var e=B.elemStore,t=r.value,o=b.value,o=o?o.$el:null;if(o){var a=I.value;if(a){var n=S.value;if(n){let r=0;var i=o.clientWidth,s=i;let l=s/100;var c=z.fit,d=U.columnStore,{resizeList:d,pxMinList:u,autoMinList:p,pxList:h,scaleList:m,scaleMinList:v,autoList:f,remainList:g}=d;if(u.forEach(e=>{var t=al().toInteger(e.minWidth);r+=t,e.renderWidth=t}),p.forEach(e=>{var t=Math.max(60,al().toInteger(e.renderAutoWidth));r+=t,e.renderWidth=t}),v.forEach(e=>{var t=Math.floor(al().toInteger(e.minWidth)*l);r+=t,e.renderWidth=t}),m.forEach(e=>{var t=Math.floor(al().toInteger(e.width)*l);r+=t,e.renderWidth=t}),h.forEach(e=>{var t=al().toInteger(e.width);r+=t,e.renderWidth=t}),f.forEach(e=>{var t=Math.max(60,al().toInteger(e.renderAutoWidth));r+=t,e.renderWidth=t}),d.forEach(e=>{var t=al().toInteger(e.resizeWidth);r+=t,e.renderWidth=t}),s-=r,l=0<s?Math.floor(s/(v.length+u.length+p.length+g.length)):0,c?0<s&&v.concat(u).concat(p).forEach(e=>{r+=l,e.renderWidth+=l}):l=40,g.forEach(e=>{var t=Math.max(l,40);e.renderWidth=t,r+=t}),c){var x=m.concat(v).concat(u).concat(p).concat(g);let t=x.length-1;if(0<t){let e=i-r;if(0<e){for(;0<e&&0<=t;)e--,x[t--].renderWidth++;r=i}}}h=o.offsetHeight,f=a.scrollHeight>a.clientHeight,d=(U.scrollbarWidth=Math.max(t.width||0,a.offsetWidth-a.clientWidth),U.overflowY=f,B.tableWidth=r,B.tableHeight=h,wl(e["main-header-table"])),s=wl(e["main-footer-table"]),c=d?d.clientHeight:0,m=r>i,v=s?s.clientHeight:0;U.scrollbarHeight=Math.max(t.height||0,n.offsetHeight-n.clientHeight),B.headerHeight=c,B.footerHeight=v,U.overflowX=m,Et(),U.parentHeight=Math.max(B.headerHeight+v+20,se.getParentHeight()),m&&se.checkScrolling()}}}},Dt=(e,t)=>{var r=t.querySelectorAll(`.vxe-cell--wrapper[rowid="${e.rowid}"]`);let l=e.height;for(let e=0;e<r.length;e++){var o=r[e],a=o.parentElement,a=Math.ceil(al().toNumber(a.style.paddingTop)+al().toNumber(a.style.paddingBottom)),o=o?o.clientHeight:0;l=Math.max(l-a,Math.ceil(o))}return l},Mt=()=>{let{tableData:e,isAllOverflow:t,scrollYLoad:r,scrollXLoad:l}=U,o=B.fullAllDataRowIdData,a=E.value,n=q.value;!t&&r&&n&&(n.setAttribute("data-calc-row","Y"),e.forEach(e=>{var t,e=xl(se,e),e=o[e];e&&(t=Dt(e,n),e.height=Math.max(a,l?Math.max(e.height,t):t)),n.removeAttribute("data-calc-row")}),U.calcCellHeightFlag++)},kt=r=>{let{sortBy:l,sortType:o}=r;return e=>{let t;return t=l?al().isFunction(l)?l({row:e,column:r}):al().get(e,l):P.getCellLabel(e,r),o&&"auto"!==o?"number"===o?al().toNumber(t):"string"===o?al().toValueString(t):t:isNaN(t)?t:al().toNumber(t)}},Ft=()=>{let{afterFullData:e,fullDataRowIdData:a,fullAllDataRowIdData:n}=B,i={};e.forEach((e,t)=>{var r=xl(se,e),l=n[r],o=t+1;l?(l.seq=o,l._index=t):(l={row:e,rowid:r,seq:o,index:-1,$index:-1,_index:t,treeIndex:-1,items:[],parent:null,level:0,height:0,resizeHeight:0,oTop:0},n[r]=l,a[r]=l),i[r]=e}),B.afterFullRowMaps=i},Ot=()=>{var e=z.treeConfig;let{fullDataRowIdData:n,fullAllDataRowIdData:i,afterTreeFullData:t}=B;var r=ie.value,l=r.transform,o=r.children||r.childrenField;let s={};e&&(al().eachTree(t,(e,t,r,l)=>{var o=xl(se,e),a=i[o],l=l.map((e,t)=>t%2==0?Number(e)+1:".").join("");a?(a.seq=l,a.treeIndex=t):(a={row:e,rowid:o,seq:l,index:-1,$index:-1,_index:-1,treeIndex:-1,items:[],parent:null,level:0,height:0,resizeHeight:0,oTop:0},i[o]=a,n[o]=a),s[o]=e},{children:l?r.mapChildrenField:o}),B.afterFullRowMaps=s),Ft()},At=()=>{var e=z.treeConfig;let c=U.treeExpandedMaps,d=B.fullAllDataRowIdData;var t=ie.value,r=t.children||t.childrenField;if(e&&t.transform){let i=[],s={};return al().eachTree(B.afterTreeFullData,(e,t,r,l,o)=>{var a=xl(se,e),n=xl(se,o);(!o||s[n]&&c[n])&&((o=d[a])&&(o._index=i.length),s[a]=1,i.push(e))},{children:r}),B.afterFullData=i,tr(i),i}return B.afterFullData},g=()=>{let{border:x,showHeaderOverflow:b,showFooterOverflow:C,mouseConfig:e,spanMethod:w,footerSpanMethod:y}=z,{isGroup:T,currentRow:t,tableColumn:E,scrollXLoad:R,scrollYLoad:S,overflowX:r,scrollbarWidth:l,overflowY:o,scrollbarHeight:a,columnStore:n,editStore:i,isAllOverflow:I,expandColumn:D}=U,{visibleColumn:M,fullColumnIdData:k,tableHeight:F,tableWidth:O,headerHeight:A,footerHeight:L,elemStore:V,customHeight:$,customMinHeight:s,customMaxHeight:_}=B;var c=q.value;if(c){let p=o?l:0,h=r?a:0;var d=we.value;let m=et.value;var u=ae.value,H=wl(V["main-body-wrapper"]),N=wl(V["main-body-table"]);d&&(d.style.top=A+"px",d.style.height=H?H.offsetHeight-h+"px":"");let v=0,f=0,g=s-A-L-h;_&&(f=Math.max(g,_-A-L-h)),(v=$?$-A-L-h:v)||N&&(v=N.clientHeight),v&&(f&&(v=Math.min(f,v)),v=Math.max(g,v));var d=Ie.value,H=De.value,N=Z.value,P=Re.value,P=(P&&(P.style.height=h+"px",P.style.visibility=r?"visible":"hidden"),ke.value),P=(P&&(P.style.left=N?p+"px":"",P.style.width=c.clientWidth-p+"px"),d&&(d.style.width=N?p+"px":"",d.style.display=N&&p&&h?"block":""),H&&(H.style.width=N?"":p+"px",H.style.display=!N&&p&&h?"block":""),Se.value),c=(P&&(P.style.width=p+"px",P.style.height=v+A+L+"px",P.style.visibility=o?"visible":"hidden"),Me.value),d=(c&&(c.style.height=A+"px",c.style.display=A?"block":""),Fe.value),H=(d&&(d.style.height=v+"px",d.style.top=A+"px"),Oe.value);return H&&(H.style.height=L+"px",H.style.top=A+v+"px",H.style.display=L?"block":""),["main","left","right"].forEach((s,e)=>{let c=0<e?s:"";e="left"===c;let d=[],u;c&&(d=e?n.leftList:n.rightList,u=(e?K:Y).value),["header","body","footer"].forEach(i=>{var r=wl(V[s+`-${i}-wrapper`]),l=wl(V[s+`-${i}-scroll`]),o=wl(V[s+`-${i}-table`]);if("header"===i){var a=O;let e=E,t=!1;T?(e=M,c&&r&&(r.style.width=a?a+"px":"")):(!(R||S||b)||w||y||(t=!0),c&&(e=M,t&&(e=d||[]),t||r&&(r.style.width=a?a+"px":""))),a=e.reduce((e,t)=>e+t.renderWidth,0),l&&(l.style.height=A+"px"),o&&(o.style.width=a?a+"px":"");a=wl(V[s+`-${i}-repair`]),a=(a&&(a.style.width=O+"px"),wl(V[s+`-${i}-list`]));T&&a&&al().arrayEach(a.querySelectorAll(".col--group"),e=>{var o=se.getColumnNode(e);if(o){let t=o.item;o=t.showHeaderOverflow,o=al().isBoolean(o)?o:b,o="title"===o||(!0===o||"tooltip"===o)||"ellipsis"===o;let r=0,l=0;o&&al().eachTree(t.children,e=>{e.children&&t.children.length||l++,r+=e.renderWidth},{children:"children"}),e.style.width=o?r-l-(x?2:0)+"px":""}})}else if("body"===i){l&&(l.style.maxHeight=_?f+"px":"",l.style.height=$?v+"px":"",l.style.minHeight=g+"px"),u&&(r&&(r.style.top=A+"px"),u.style.height=`${0<$?$:F+A+L+h}px`,u.style.width=d.reduce((e,t)=>e+t.renderWidth,0)+"px");a=O;let e=E,t=!1;!(R||S||I)||D||w||y||(t=!0),c&&(e=M,t&&(e=d||[]),t||r&&(r.style.width=a?a+"px":"")),a=e.reduce((e,t)=>e+t.renderWidth,0),o&&(o.style.width=a?a+"px":"",o.style.paddingRight=p&&c&&(zr["-moz"]||zr.safari)?p+"px":"");var n=wl(V[s+`-${i}-emptyBlock`]);n&&(n.style.width=a?a+"px":"")}else if("footer"===i){n=O;let e=E,t=!1;!(R||S||C)||w||y||(t=!0),c&&(e=M,t&&(e=d||[]),t||r&&(r.style.width=n?n+"px":"")),n=e.reduce((e,t)=>e+t.renderWidth,0),l&&(l.style.height=L+"px",u)&&r&&(r.style.top=`${0<$?$-L-h:F+A}px`),o&&(o.style.width=n?n+"px":"")}a=wl(V[s+`-${i}-colgroup`]);a&&al().arrayEach(a.children,t=>{var r=t.getAttribute("name");if(k[r]){let a=k[r].column;var{showHeaderOverflow:r,showFooterOverflow:l,showOverflow:o}=a;let e;t.style.width=a.renderWidth+"px";t="ellipsis"===(e="header"===i?al().isUndefined(r)||al().isNull(r)?b:r:"footer"===i?al().isUndefined(l)||al().isNull(l)?C:l:al().isUndefined(o)||al().isNull(o)?I:o),r="title"===e,l=!0===e||"tooltip"===e;let n=r||l||t;o=wl(V[s+`-${i}-list`]);S&&!n&&(n=!0),o&&al().arrayEach(o.querySelectorAll("."+a.id),e=>{var t=parseInt(e.getAttribute("colspan")||1),e=e.querySelector(".vxe-cell");let r=a.renderWidth;if(e){if(1<t){var l=se.getColumnIndex(a);for(let e=1;e<t;e++){var o=se.getColumns(l+e);o&&(r+=o.renderWidth)}}e.style.width=n?r-m*t+"px":""}})}})})}),t&&se.setCurrentRow(t),e&&u.selected&&i.selected.row&&i.selected.column&&se.addCellSelectedClass(),(0,dl.nextTick)()}},Lt=e=>se.triggerValidate?se.triggerValidate(e):(0,dl.nextTick)(),Vt=(e,t)=>{Lt("blur").catch(e=>e).then(()=>{se.handleEdit(t,e).then(()=>Lt("change")).catch(e=>e)})},$t=e=>{D.value.reserve&&(B.radioReserveRow=e)},_t=(e,t)=>{var r,l=B.checkboxReserveRowMap;le.value.reserve&&(r=xl(se,e),t?l[r]=e:l[r]&&delete l[r])},Ht=(e,t)=>{var r=D.value.checkMethod;return e&&(t||!r||r({row:e}))&&(U.selectRadioRow=e,$t(e)),(0,dl.nextTick)()},x=(e,t,r)=>(e&&!al().isArray(e)&&(e=[e]),se.handleBatchSelectRows(e,!!t,r),se.checkSelectionStatus(),(0,dl.nextTick)()),Nt=(t,r)=>{let l=z.treeConfig,o=U.selectCheckboxMaps,{afterFullData:e,checkboxReserveRowMap:a}=B;var n=ie.value,n=n.children||n.childrenField,i=le.value;let{checkField:s,reserve:c,checkMethod:d}=i,u=i.indeterminateField||i.halfField,p={};return s?(i=e=>{!r&&d&&!d({row:e})||(t&&(p[xl(se,e)]=e),al().set(e,s,t)),l&&u&&al().set(e,u,!1)},l?al().eachTree(e,i,{children:n}):e.forEach(i)):l?t?al().eachTree(e,e=>{!r&&d&&!d({row:e})||(p[xl(se,e)]=e)},{children:n}):!r&&d&&al().eachTree(e,e=>{var t=xl(se,e);!d({row:e})&&o[t]&&(p[t]=e)},{children:n}):t?!r&&d?e.forEach(e=>{var t=xl(se,e);(o[t]||d({row:e}))&&(p[t]=e)}):e.forEach(e=>{p[xl(se,e)]=e}):!r&&d&&e.forEach(e=>{var t=xl(se,e);!d({row:e})&&o[t]&&(p[t]=e)}),c&&(t?al().each(p,(e,t)=>{a[t]=e}):e.forEach(e=>_t(e,!1))),U.selectCheckboxMaps=s?{}:p,U.isAllSelected=t,U.isIndeterminate=!1,U.treeIndeterminateMaps={},B.treeIndeterminateRowMaps={},W.checkSelectionStatus(),(0,dl.nextTick)()},Pt=a=>{var e=ie.value,t=le.value;let{transform:n,loadMethod:i}=e,s=t.checkStrictly;return new Promise(e=>{if(i){let t=U.treeExpandLazyLoadedMaps;var o=B.fullAllDataRowIdData;let r=xl(se,a),l=o[r];t[r]=a,Promise.resolve(i({$table:se,row:a})).then(e=>{if(l&&(l.treeLoaded=!0),t[r]&&delete t[r],e=al().isArray(e)?e:[])return P.loadTreeChildren(a,e).then(e=>{var t=U.treeExpandedMaps;return e.length&&!t[r]&&(t[r]=a),!s&&P.isCheckedByCheckboxRow(a)&&x(e,!0),(0,dl.nextTick)().then(()=>{if(n)return W.handleTableData(),Ot(),(0,dl.nextTick)()})})}).catch(()=>{var e=U.treeExpandLazyLoadedMaps;l&&(l.treeLoaded=!1),e[r]&&delete e[r]}).finally(()=>{(0,dl.nextTick)().then(()=>P.recalculate()).then(()=>e())})}else e()})},Bt=(e,t)=>{var r,l=B.treeExpandedReserveRowMap;ie.value.reserve&&(r=xl(se,e),t?l[r]=e:l[r]&&delete l[r])},zt=n=>new Promise(l=>{var e=M.value.loadMethod;if(e){var o=B.fullAllDataRowIdData,a={...U.rowExpandLazyLoadedMaps};let t=xl(se,n),r=o[t];a[t]=n,U.rowExpandLazyLoadedMaps=a,e({$table:se,row:n,rowIndex:P.getRowIndex(n),$rowIndex:P.getVMRowIndex(n)}).then(()=>{var e={...U.rowExpandedMaps};r&&(r.expandLoaded=!0),e[t]=n,U.rowExpandedMaps=e}).catch(()=>{r&&(r.expandLoaded=!1)}).finally(()=>{var e={...U.rowExpandLazyLoadedMaps};e[t]&&delete e[t],U.rowExpandLazyLoadedMaps=e,(0,dl.nextTick)().then(()=>P.recalculate()).then(()=>l())})}else l()}),jt=(e,t)=>{var r,l=B.rowExpandedReserveRowMap;M.value.reserve&&(r=xl(se,e),t?l[r]=e:l[r]&&delete l[r])},Ut=()=>(0,dl.nextTick)().then(()=>{var e,{scrollXLoad:t,scrollYLoad:r}=U,{scrollXStore:l,scrollYStore:o}=B,a=$.value,n=s.value,n=(t?({toVisibleIndex:t,visibleSize:e}=mt(),i=Math.max(0,n.oSize?al().toNumber(n.oSize):0),l.preloadSize=al().toNumber(n.preSize),l.offsetSize=i,l.visibleSize=e,l.endIndex=Math.max(l.startIndex+l.visibleSize+i,l.endIndex),l.visibleStartIndex=Math.max(l.startIndex,t),l.visibleEndIndex=Math.min(l.endIndex,t+e),se.updateScrollXData().then(()=>{Zt()})):se.updateScrollXSpace(),vt()),{toVisibleIndex:i,visibleSize:l}=(o.rowHeight=n,U.rowHeight=n,ft());r?(t=Math.max(0,a.oSize?al().toNumber(a.oSize):0),o.preloadSize=al().toNumber(a.preSize),o.offsetSize=t,o.visibleSize=l,o.endIndex=Math.max(o.startIndex+l+t,o.endIndex),o.visibleStartIndex=Math.max(o.startIndex,i),o.visibleEndIndex=Math.min(o.endIndex,i+l),se.updateScrollYData().then(()=>{or()})):se.updateScrollYSpace(),(0,dl.nextTick)(()=>{g()})}),Wt=e=>{var t=q.value;return B.rceRunTime=Date.now(),t&&t.clientWidth?(St(),It(),g(),Ut().then(()=>{if(!0===e)return St(),It(),g(),Ut()})):(0,dl.nextTick)()},qt=(e,t)=>{var{keepSource:r,treeConfig:l}=z;let{editStore:o,scrollYLoad:a}=U,{scrollYStore:n,scrollXStore:i,lastScrollLeft:s,lastScrollTop:c}=B;var d=ie.value,u=d.transform,p=d.children||d.childrenField;let h=[],m=(0,dl.reactive)(e?e.slice(0):[]),v=(l&&(u?(d.rowField||Br("vxe.error.reqProp",["tree-config.rowField"]),d.parentField||Br("vxe.error.reqProp",["tree-config.parentField"]),p||Br("vxe.error.reqProp",["tree-config.childrenField"]),d.mapChildrenField||Br("vxe.error.reqProp",["tree-config.mapChildrenField"]),p===d.mapChildrenField&&Br("vxe.error.errConflicts",["tree-config.childrenField","tree-config.mapChildrenField"]),h=al().toArrayTree(m,{key:d.rowField,parentKey:d.parentField,children:p,mapChildren:d.mapChildrenField}),m=h.slice(0)):h=m.slice(0)),n.startIndex=0,n.endIndex=1,i.startIndex=0,i.endIndex=1,U.isRowLoading=!0,U.scrollVMLoading=!1,o.insertMaps={},o.removeMaps={},tr(m));return U.isDragColMove=!1,U.isDragRowMove=!1,B.tableFullData=m,B.tableFullTreeData=h,se.cacheRowMap(!0,t),B.tableSynchData=e,t&&(B.isResizeCellHeight=!1,U.rowExpandedMaps={},U.rowExpandLazyLoadedMaps={},U.treeExpandedMaps={},U.treeExpandLazyLoadedMaps={}),r&&se.cacheSourceMap(m),se.clearCellAreas&&z.mouseConfig&&(se.clearCellAreas(),se.clearCopyCellArea()),P.clearMergeCells(),P.clearMergeFooterItems(),W.handleTableData(!0),P.updateFooter(),(0,dl.nextTick)().then(()=>{Et(),g()}).then(()=>{Ut()}).then(()=>(v&&(n.endIndex=n.visibleSize),v&&(z.height||z.maxHeight||Br("vxe.error.reqProp",["table.height | table.max-height | table.scroll-y={enabled: false}"]),z.spanMethod)&&Pr("vxe.error.scrollErrProp",["table.span-method"]),(()=>{var e=z.treeConfig,{expandColumn:t,currentRow:r,selectCheckboxMaps:l,selectRadioRow:o,rowExpandedMaps:a,treeExpandedMaps:n}=U,{fullDataRowIdData:i,fullAllDataRowIdData:s,radioReserveRow:c}=B,d=M.value,u=ie.value,p=D.value,h=le.value;o&&!s[xl(se,o)]&&(U.selectRadioRow=null),p.reserve&&c&&i[o=xl(se,c)]&&Ht(i[o].row,!0),U.selectCheckboxMaps=pt(l),h.reserve&&x(ht(B.checkboxReserveRowMap),!0,!0),r&&!s[xl(se,r)]&&(U.currentRow=null),U.rowExpandedMaps=t?pt(a):{},t&&d.reserve&&P.setRowExpand(ht(B.rowExpandedReserveRowMap),!0),U.treeExpandedMaps=e?pt(n):{},e&&u.reserve&&P.setTreeExpand(ht(B.treeExpandedReserveRowMap),!0)})(),W.checkSelectionStatus(),new Promise(o=>{(0,dl.nextTick)().then(()=>P.recalculate()).then(()=>{let e=s,t=c;var r=L.value,l=f.value;r.scrollToLeftOnChange&&(e=0),l.scrollToTopOnChange&&(t=0),U.isRowLoading=!1,Mt(),a===v?Ol(se,e,t).then(()=>{o()}):setTimeout(()=>{Ol(se,e,t).then(()=>{o()})})})})))},Xt=()=>{var e,t,r;(()=>{var e=z.checkboxConfig;if(e){let r=B.fullDataRowIdData;var{checkAll:e,checkRowKeys:l}=le.value;if(e)Nt(!0,!0);else if(l){let t=[];l.forEach(e=>{r[e]&&t.push(r[e].row)}),x(t,!0,!0)}}})(),(r=z.radioConfig)&&(r=B.fullDataRowIdData,{checkRowKey:e,reserve:t}=D.value,e)&&(r[e]&&Ht(r[e].row,!0),t)&&(r=gl(se),B.radioReserveRow={[r]:e}),(()=>{var e=z.expandConfig;if(e){let r=B.fullDataRowIdData;var{expandAll:e,expandRowKeys:l}=M.value;if(e)P.setAllRowExpand(!0);else if(l){let t=[];l.forEach(e=>{r[e]&&t.push(r[e].row)}),P.setRowExpand(t,!0)}}})(),(()=>{var e=z.treeConfig;if(e){let o=B.tableFullData;var e=ie.value,{expandAll:t,expandRowKeys:n}=e;let a=e.children||e.childrenField;if(t)P.setAllTreeExpand(!0);else if(n){let r=[],l=gl(se);n.forEach(t=>{var e=al().findTree(o,e=>t===al().get(e,l),{children:a});e&&r.push(e.item)}),P.setTreeExpand(r,!0)}}})(),(t=z.mergeCells)&&P.setMergeCells(t),(r=z.mergeFooterItems)&&P.setMergeFooterItems(r),(0,dl.nextTick)(()=>setTimeout(()=>P.recalculate()))},Kt=()=>{(()=>{var t=z.sortConfig;if(t){var r=C.value;let e=r.defaultSort;e&&(e=al().isArray(e)?e:[e]).length&&((t.multiple?e:e.slice(0,1)).forEach((e,t)=>{var{field:e,order:r}=e;e&&r&&(e=P.getColumnByField(e))&&e.sortable&&(e.order=r,e.sortTime=Date.now()+t)}),r.remote||W.handleTableData(!0).then(g))}})()},Yt=()=>{var e=U.scrollXLoad;let{visibleColumn:t,scrollXStore:r,fullColumnIdData:l}=B;e=e?t.slice(r.startIndex,r.endIndex):t.slice(0);e.forEach((e,t)=>{e=e.id,e=l[e];e&&(e.$index=t)}),U.tableColumn=e},Gt=()=>{var e=al().orderBy(B.collectColumn,"renderSortNumber"),e=(B.collectColumn=e,Qt(e));B.tableFullColumn=e,Tt()},Zt=()=>{var{mergeList:e,mergeFooterList:t}=U,r=B.scrollXStore,{preloadSize:l,startIndex:o,endIndex:a,offsetSize:n}=r,{toVisibleIndex:i,visibleSize:s}=mt(),n={startIndex:Math.max(0,i-1-n-l),endIndex:i+s+n+l},{startIndex:l,endIndex:e}=(r.visibleStartIndex=i,r.visibleEndIndex=i+s,gt(e.concat(t),n,"col"),n);!(i<=o||a-s-1<=i)||o===l&&a===e||(r.startIndex=l,r.endIndex=e,se.updateScrollXData()),se.closeTooltip()},Qt=e=>{let t=[];return e.forEach(e=>{t.push(...e.children&&e.children.length?Qt(e.children):[e])}),t},Jt=e=>{var t=z.showOverflow,r=ee.value;let n=[],i=[],s=[];var{isGroup:l,columnStore:o}=U,a=L.value;let{collectColumn:c,tableFullColumn:d,scrollXStore:u,fullColumnIdData:p}=B;if(l){let t=[],r=[],l=[];al().eachTree(c,(e,t,r,l,o)=>{var a=Rl(e);o&&o.fixed&&(e.fixed=o.fixed),o&&e.fixed!==o.fixed&&Br("vxe.error.groupFixed"),a?e.visible=!!al().findTree(e.children,e=>!Rl(e)&&e.visible):e.visible&&("left"===e.fixed?n:"right"===e.fixed?s:i).push(e)}),c.forEach(e=>{e.visible&&("left"===e.fixed?t:"right"===e.fixed?l:r).push(e)}),U.tableGroupColumn=t.concat(r).concat(l)}else d.forEach(e=>{e.visible&&("left"===e.fixed?n:"right"===e.fixed?s:i).push(e)});let h=n.concat(i).concat(s);l=!!a.enabled&&-1<a.gt&&(0===a.gt||a.gt<d.length);return U.hasFixedColumn=0<n.length||0<s.length,Object.assign(o,{leftList:n,centerList:i,rightList:s}),l&&(t&&!r.height&&(a=B.tableFullColumn.find(e=>!1===e.showOverflow))&&Br("vxe.error.errProp",[`column[field="${a.field}"].show-overflow=false`,"show-overflow=true"]),z.spanMethod&&Pr("vxe.error.scrollErrProp",["span-method"]),z.footerSpanMethod&&Pr("vxe.error.scrollErrProp",["footer-span-method"]),e)&&(o=mt().visibleSize,u.startIndex=0,u.endIndex=o,u.visibleSize=o,u.visibleStartIndex=0,u.visibleEndIndex=o),h.length===B.visibleColumn.length&&B.visibleColumn.every((e,t)=>e===h[t])||(se.clearMergeCells(),se.clearMergeFooterItems()),U.scrollXLoad=l,h.forEach((e,t)=>{e=e.id,e=p[e];e&&(e._index=t)}),B.visibleColumn=h,Yt(),e?se.updateFooter().then(()=>se.recalculate()).then(()=>(se.updateCellAreas(),se.recalculate())):se.updateFooter()},er=e=>{B.collectColumn=e;e=Qt(e);return B.tableFullColumn=e,U.isColLoading=!0,U.isDragColMove=!1,B.collectColumn.forEach((e,t)=>{t+=1;e.sortNumber=t,e.renderSortNumber=t}),Promise.resolve((()=>{var e=z.customConfig,t=A.value,r=T.value,{storage:l,restoreStore:o}=r,a=!0===l,l=a?{}:Object.assign({},l||{}),n=a||l.resizable,i=a||l.visible,s=a||l.fixed,a=a||l.sort;if((e?nl(r):r.enabled)&&(n||i||s||a)){if(t)return l=ut(t),o?Promise.resolve(o({id:t,type:"restore",storeData:l})).then(e=>{if(e)return yt(e)}).catch(e=>e):yt(l);Br("vxe.error.reqProp",["id"])}})()).then(()=>(Tt(),Jt(!0).then(()=>{U.scrollXLoad&&Zt()}),se.clearMergeCells(),se.clearMergeFooterItems(),se.handleTableData(!0),(U.scrollXLoad||U.scrollYLoad)&&U.expandColumn&&Pr("vxe.error.scrollErrProp",["column.type=expand"]),(0,dl.nextTick)().then(()=>(O&&O.syncUpdate({collectColumn:B.collectColumn,$table:se}),se.handleUpdateCustomColumn&&se.handleUpdateCustomColumn(),U.isColLoading=!1,se.recalculate()))))},tr=e=>{var t=z.treeConfig,r=f.value,l=ie.value.transform,e=e||B.tableFullData,l=(l||!t)&&!!r.enabled&&-1<r.gt&&(0===r.gt||r.gt<e.length);return U.scrollYLoad=l},rr=(e,t)=>{let{treeExpandedMaps:r,treeExpandLazyLoadedMaps:l,treeNodeColumn:o}=U,a={...r},{fullAllDataRowIdData:n,tableFullData:i}=B;var s=ie.value;let{reserve:c,lazy:d,accordion:u,toggleMethod:p}=s,h=s.children||s.childrenField,m=s.hasChild||s.hasChildField,v=[],f=P.getColumnIndex(o),g=P.getVMColumnIndex(o),x=p?e.filter(e=>p({$table:se,expanded:t,column:o,columnIndex:f,$columnIndex:g,row:e})):e;return u&&(x=x.length?[x[x.length-1]]:[],s=al().findTree(i,e=>e===x[0],{children:h}))&&s.items.forEach(e=>{e=xl(se,e);a[e]&&delete a[e]}),t?x.forEach(e=>{var t,r=xl(se,e);a[r]||(t=n[r])&&(d&&e[m]&&!t.treeLoaded&&!l[r]?v.push(Pt(e)):e[h]&&e[h].length&&(a[r]=e))}):x.forEach(e=>{e=xl(se,e);a[e]&&delete a[e]}),c&&x.forEach(e=>Bt(e,t)),U.treeExpandedMaps=a,Promise.all(v).then(()=>P.recalculate())},lr=(e,t)=>{Nt(t),e&&ce("checkbox-all",{records:P.getCheckboxRecords(),reserves:P.getCheckboxReserveRecords(),indeterminates:P.getCheckboxIndeterminateRecords(),checked:t},e)},or=e=>{var{mergeList:t,isAllOverflow:r}=U,l=B.scrollYStore,{preloadSize:o,startIndex:a,endIndex:n,offsetSize:i}=l,r=r?i:i+1,{toVisibleIndex:e,visibleSize:s}=ft(e),i={startIndex:Math.max(0,e-1-i-o),endIndex:e+s+r+o},{startIndex:r,endIndex:o}=(l.visibleStartIndex=e,l.visibleEndIndex=e+s,gt(t,i,"row"),i);!(e<=a||n-s-1<=e)||a===r&&n===o||(l.startIndex=r,l.endIndex=o,se.updateScrollYData())};a=r=>function(e){var t=B.fullAllDataRowIdData;if(e){t=t[xl(se,e)];if(t)return t[r]}return-1},n=r=>function(e){var t=B.fullColumnIdData;if(e){t=t[e.id];if(t)return t[r]}return-1};let ce=(e,t,r)=>{l(e,Qr(r,{$table:se,$grid:G},t))},ar=()=>{var e=q.value;e&&e.clientWidth&&e.clientHeight&&P.recalculate()},nr=(e,t)=>{se.analyColumnWidth(),se.recalculate(!0).then(()=>{se.saveCustomStore("update:width"),se.updateCellAreas(),se.dispatchEvent("column-resizable-change",t,e),se.dispatchEvent("resizable-change",t,e),setTimeout(()=>se.recalculate(!0),300)})},ir=(e,t)=>{U.resizeHeightFlag++,se.recalculate(!0).then(()=>{se.updateCellAreas(),se.dispatchEvent("row-resizable-change",t,e),setTimeout(()=>se.recalculate(!0),300)})},sr=(P={dispatchEvent:ce,clearAll(){var e=se,{props:t,internalData:r}=(e.clearFilter&&e.clearFilter(),e);return r.initStatus=!1,e.clearSort(),e.clearCurrentRow(),e.clearCurrentColumn(),e.clearRadioRow(),e.clearRadioReserve(),e.clearCheckboxRow(),e.clearCheckboxReserve(),e.clearRowExpand(),e.clearTreeExpand(),e.clearTreeExpandReserve(),e.clearPendingRow(),e.clearFilter&&e.clearFilter(),e.clearSelected&&(t.keyboardConfig||t.mouseConfig)&&e.clearSelected(),e.clearCellAreas&&t.mouseConfig&&(e.clearCellAreas(),e.clearCopyCellArea()),e.clearScroll()},syncData(){return Pr("vxe.error.delFunc",["syncData","getData"]),(0,dl.nextTick)().then(()=>(U.tableData=[],l("update:data",B.tableFullData),(0,dl.nextTick)()))},updateData(){let{scrollXLoad:e,scrollYLoad:t}=U;return W.handleTableData(!0).then(()=>{if(P.updateFooter(),e||t)return e&&W.updateScrollXSpace(),t&&W.updateScrollYSpace(),P.refreshScroll()}).then(()=>(P.updateCellAreas(),P.recalculate(!0))).then(()=>{setTimeout(()=>se.recalculate(),50)})},loadData(e){let t=B.initStatus;return qt(e,!1).then(()=>(B.inited=!0,B.initStatus=!0,t||Xt(),P.recalculate()))},reloadData(e){return P.clearAll().then(()=>(B.inited=!0,B.initStatus=!0,qt(e,!0))).then(()=>(Xt(),P.recalculate()))},setRow(t,o){if(t&&o){let e=t,l=(al().isArray(t)||(e=[t]),gl(se));e.forEach(e=>{var t=xl(se,e),r=al().clone(Object.assign({},o),!0);al().set(r,l,t),Object.assign(e,r)})}return(0,dl.nextTick)()},reloadRow(e,t,r){var l=z.keepSource,o=U.tableData,a=B.tableSourceData;return l?((l=a[P.getRowIndex(e)])&&e&&(r?(a=al().clone(al().get(t||e,r),!0),al().set(e,r,a),al().set(l,r,a)):(r=gl(se),a=xl(se,e),t=al().clone(Object.assign({},t),!0),al().set(t,r,a),al().destructuring(l,Object.assign(e,t)))),U.tableData=o.slice(0)):Pr("vxe.error.reqProp",["keep-source"]),(0,dl.nextTick)()},getParams(){return z.params},loadTreeChildren(l,e){let t=z.keepSource,{tableSourceData:o,fullDataRowIdData:i,fullAllDataRowIdData:s,sourceDataRowIdData:a}=B;var r=ie.value;let{transform:n,mapChildrenField:c}=r,d=r.children||r.childrenField,u=s[xl(se,l)],p=u?u.level:0;return P.createData(e).then(e=>{if(t){let t=xl(se,l);var r=al().findTree(o,e=>t===xl(se,e),{children:d});r&&(r.item[d]=al().clone(e,!0)),e.forEach(e=>{var t=xl(se,e);a[t]=al().clone(e,!0)})}return al().eachTree(e,(e,t,r,l,o,a)=>{var n=xl(se,e),e={row:e,rowid:n,seq:-1,index:t,_index:-1,$index:-1,treeIndex:-1,items:r,parent:o||u.row,level:p+a.length,height:0,resizeHeight:0,oTop:0};i[n]=e,s[n]=e},{children:d}),l[d]=e,n&&(l[c]=al().clone(e,!1)),Ot(),e})},loadColumn(e){e=al().mapTree(e,e=>(0,dl.reactive)(Hl.createColumn(se,e)));return er(e)},reloadColumn(e){return P.clearAll().then(()=>P.loadColumn(e))},getRowNode(e){if(e){var t=B.fullAllDataRowIdData,e=e.getAttribute("rowid");if(e){t=t[e];if(t)return{rowid:t.rowid,item:t.row,index:t.index,items:t.items,parent:t.parent}}}return null},getColumnNode(e){if(e){var t=B.fullColumnIdData,e=e.getAttribute("colid");if(e){t=t[e];if(t)return{colid:t.colid,item:t.column,index:t.index,items:t.items,parent:t.parent}}}return null},getRowSeq:a("seq"),getRowIndex:a("index"),getVTRowIndex:a("_index"),getVMRowIndex:a("$index"),getColumnIndex:n("index"),getVTColumnIndex:n("_index"),getVMColumnIndex:n("$index"),createData(e){return(0,dl.nextTick)().then(()=>(0,dl.reactive)(W.defineField(e)))},createRow(e){let t=al().isArray(e);return t||(e=[e||{}]),P.createData(e).then(e=>t?e:e[0])},revertData(e,r){var t=z.keepSource;let{tableSourceData:l,sourceDataRowIdData:o}=B;if(!t)return Pr("vxe.error.reqProp",["keep-source"]),(0,dl.nextTick)();let a=e;return e?al().isArray(e)||(a=[e]):a=al().toArray(se.getUpdateRecords()),a.length&&a.forEach(e=>{var t;P.isInsertByRow(e)||(t=xl(se,e),(t=o[t])&&e&&(r?al().set(e,r,al().clone(al().get(t,r),!0)):al().destructuring(e,al().clone(t,!0))))}),e?(0,dl.nextTick)():P.reloadData(l)},clearData(e,t){let{tableFullData:r,visibleColumn:l}=B;return arguments.length?e&&!al().isArray(e)&&(e=[e]):e=r,t?e.forEach(e=>al().set(e,t,null)):e.forEach(t=>{l.forEach(e=>{e.field&&Cl(t,e,null)})}),(0,dl.nextTick)()},getCellElement(e,t){var r=B.elemStore,t=jr(se,t);if(!t)return null;var e=xl(se,e),l=wl(r["main-body-scroll"]),o=wl(r["left-body-scroll"]),r=wl(r["right-body-scroll"]);let a;return t&&(t.fixed&&("left"===t.fixed?o&&(a=o):r&&(a=r)),a=a||l)?a.querySelector(`.vxe-body--row[rowid="${e}"] .`+t.id):null},getCellLabel(t,r){r=jr(se,r);if(!r)return null;var l=r.formatter,o=bl(t,r);let a=o;if(l){let e;var n=B.fullAllDataRowIdData,i=xl(se,t),s=r.id,c=n[i];if(c&&(e=(e=c.formatData)||(n[i].formatData={}),c)&&e[s]&&e[s].value===o)return e[s].label;n={cellValue:o,row:t,rowIndex:P.getRowIndex(t),column:r,columnIndex:P.getColumnIndex(r)};a=al().isString(l)?(c=(i=Zr.get(l))?i.tableCellFormatMethod||i.cellFormatMethod:null)?c(n):"":al().isArray(l)?(r=(t=Zr.get(l[0]))?t.tableCellFormatMethod||t.cellFormatMethod:null)?r(n,...l.slice(1)):"":l(n),e&&(e[s]={value:o,label:a})}return a},isInsertByRow(e){var t=U.editStore,e=xl(se,e);return!!t.insertMaps[e]},removeInsertRow(){var e=U.editStore;return e.insertMaps={},se.remove(se.getInsertRecords())},isUpdateByRow(r,e){var t=z.keepSource,{tableFullColumn:l,fullDataRowIdData:o,sourceDataRowIdData:a}=B;if(t){t=xl(se,r);if(!o[t])return!1;var n=a[t];if(n){if(1<arguments.length)return!ct(n,r,e);for(let e=0,t=l.length;e<t;e++){var i=l[e].field;if(i&&!ct(n,r,i))return!0}}}return!1},getColumns(e){var t=B.visibleColumn;return al().isUndefined(e)?t.slice(0):t[e]},getColid(e){e=jr(se,e);return e?e.id:null},getColumnById(e){var t=B.fullColumnIdData;return e&&t[e]?t[e].column:null},getColumnByField(e){var t=B.fullColumnFieldData;return e&&t[e]?t[e].column:null},getParentColumn(e){var t=B.fullColumnIdData,e=jr(se,e);return e&&e.parentId&&t[e.parentId]?t[e.parentId].column:null},getTableColumn(){return{collectColumn:B.collectColumn.slice(0),fullColumn:B.tableFullColumn.slice(0),visibleColumn:B.visibleColumn.slice(0),tableColumn:U.tableColumn.slice(0)}},getFullColumns(){var e=B.collectColumn;return e.slice(0)},getData(e){var t=z.data||B.tableSynchData;return al().isUndefined(e)?t.slice(0):t[e]},getCheckboxRecords(r){var e=z.treeConfig;let{tableFullData:t,afterFullData:l,afterTreeFullData:o,tableFullTreeData:a,fullDataRowIdData:n,afterFullRowMaps:i}=B;var s=ie.value,{transform:c,mapChildrenField:d}=s;let u=le.value.checkField;s=s.children||s.childrenField;let p=[];var h=r?c?a:t:c?o:l;return u?p=e?al().filterTree(h,e=>al().get(e,u),{children:c?d:s}):h.filter(e=>al().get(e,u)):(e=U.selectCheckboxMaps,al().each(e,(e,t)=>{r?n[t]&&p.push(n[t].row):i[t]&&p.push(i[t])})),p},getTreeRowChildren(t){var r=z.treeConfig,l=B.fullDataRowIdData,o=ie.value,{transform:a,mapChildrenField:n}=o,o=o.children||o.childrenField;if(t&&r){let e;if(e=al().isString(t)?t:xl(se,t)){r=l[e],t=r?r.row:null;if(t)return t[a?n:o]||[]}}return[]},getTreeParentRow(t){var r=z.treeConfig,l=B.fullDataRowIdData;if(t&&r){let e;if(e=al().isString(t)?t:xl(se,t))return(r=l[e])?r.parent:null}return null},getParentRow(e){return Pr("vxe.error.delFunc",["getParentRow","getTreeParentRow"]),se.getTreeParentRow(e)},getRowById(e){var t=B.fullDataRowIdData,e=al().eqNull(e)?"":encodeURIComponent(e||"");return t[e]?t[e].row:null},getRowid(e){return xl(se,e)},getTableData(){var{tableData:e,footerTableData:t}=U,{tableFullData:r,afterFullData:l,tableFullTreeData:o}=B;return{fullData:(z.treeConfig?o:r).slice(0),visibleData:l.slice(0),tableData:e.slice(0),footerData:t.slice(0)}},getFullData(){var e,t,r,l,o=z.treeConfig,{tableFullData:a,tableFullTreeData:n}=B;return o?({transform:e,mapChildrenField:t,rowField:r,parentField:l}=o=ie.value,o=o.children||o.childrenField,e?al().toArrayTree(al().toTreeArray(n,{children:t}),{key:r,parentKey:l,children:o,mapChildren:t}):n.slice(0)):a.slice(0)},setColumnFixed(e,t){let r=!1;var l=al().isArray(e)?e:[e],o=J.value,a=lt.value;for(let e=0;e<l.length;e++){var n=l[e],n=jr(se,n),n=Vl(se,n);if(n&&n.fixed!==t){if(!n.fixed&&a)return ol.VxeUI.modal&&ol.VxeUI.modal.message({status:"error",content:Wr("vxe.table.maxFixedCol",[o.maxFixedSize])}),(0,dl.nextTick)();al().eachTree([n],e=>{e.fixed=t}),W.saveCustomStore("update:fixed"),r=r||!0}}return r?P.refreshColumn():(0,dl.nextTick)()},clearColumnFixed(e){let t=!1;return(al().isArray(e)?e:[e]).forEach(e=>{e=jr(se,e),e=Vl(se,e);e&&e.fixed&&(al().eachTree([e],e=>{e.fixed=null}),W.saveCustomStore("update:fixed"),t=t||!0)}),t?P.refreshColumn():(0,dl.nextTick)()},hideColumn(e){let t=!1;return(al().isArray(e)?e:[e]).forEach(e=>{e=jr(se,e);e&&e.visible&&(e.visible=!1,t=t||!0)}),t?W.handleCustom():(0,dl.nextTick)()},showColumn(e){let t=!1;return(al().isArray(e)?e:[e]).forEach(e=>{e=jr(se,e);e&&!e.visible&&(e.visible=!0,t=t||!0)}),t?W.handleCustom():(0,dl.nextTick)()},setColumnWidth(e,t){var r=B.elemStore;let l=!1;e=al().isArray(e)?e:[e];let o=al().toInteger(t);return Ml(t)&&(r=(t=wl(r["main-body-scroll"]))?t.clientWidth-1:0,o=Math.floor(o*r)),(o&&(e.forEach(e=>{e=jr(se,e);e&&(e.resizeWidth=o,l=l||!0)}),l)?se.refreshColumn():(0,dl.nextTick)()).then(()=>({status:l}))},getColumnWidth(e){e=jr(se,e);return e?e.renderWidth:0},resetColumn(e){return Pr("vxe.error.delFunc",["resetColumn","resetCustom"]),se.resetCustom(e)},refreshColumn(e){return e&&Gt(),Jt(!0).then(()=>P.refreshScroll()).then(()=>P.recalculate())},setRowHeightConf(e){let r=B.fullAllDataRowIdData,l=!1;return e&&(al().each(e,(e,t)=>{t=r[t];t&&(e=al().toInteger(e))&&(t.resizeHeight=e,l=l||!0)}),l)&&(B.isResizeCellHeight=!0,U.resizeHeightFlag++),(0,dl.nextTick)().then(()=>({status:l}))},getRowHeightConf(l){let{fullAllDataRowIdData:o,afterFullData:e}=B,a=ee.value,n=R.value,i=E.value,s={};return e.forEach(e=>{var t,e=xl(se,e),r=o[e];r&&((t=r.resizeHeight)||l)&&(t=t||r.height||n.height||a.height||i,s[e]=t)}),s},setRowHeight(e,t){let r=B.fullAllDataRowIdData,l=!1;e=al().isArray(e)?e:[e];let o=al().toInteger(t);return Ml(t)&&(t=(t=(t=b.value)?t.$el:null)?t.clientHeight-1:0,o=Math.floor(o*t)),o&&(e.forEach(e=>{e=al().isString(e)||al().isNumber(e)?e:xl(se,e),e=r[e];e&&(e.resizeHeight=o,l=l||!0)}),l)&&(B.isResizeCellHeight=!0,U.resizeHeightFlag++),(0,dl.nextTick)().then(()=>({status:l}))},getRowHeight(e){var t=B.fullAllDataRowIdData,r=ee.value,l=R.value,o=E.value,t=t[al().isString(e)||al().isNumber(e)?e:xl(se,e)];return t?t.resizeHeight||t.height||l.height||r.height||o:0},refreshScroll(){let{elemStore:e,lastScrollLeft:t,lastScrollTop:r}=B,l=wl(e["main-header-scroll"]),o=wl(e["main-body-scroll"]),a=wl(e["main-footer-scroll"]),n=wl(e["left-body-scroll"]),i=wl(e["right-body-scroll"]),s=S.value,c=I.value;return new Promise(e=>{if(t||r)return Ol(se,t,r).then().then(()=>{setTimeout(e,10)});B.intoRunScroll=!0,ml(c,r),ml(o,r),ml(n,r),ml(i,r),vl(s,t),vl(o,t),vl(l,t),vl(a,t),setTimeout(()=>{B.intoRunScroll=!1,e()},10)})},recalculate(a){return new Promise(e=>{var{rceTimeout:t,rceRunTime:r}=B,l=i.value.refreshDelay||20,o=q.value;o&&o.clientWidth&&It(),!t||(clearTimeout(t),r&&r+(l-5)<Date.now())?e(Wt(!!a)):(0,dl.nextTick)(()=>{e()}),B.rceTimeout=setTimeout(()=>{B.rceTimeout=void 0,Wt(!!a)},l)})},openTooltip(e,t){var r=me.value;return r&&r.open?r.open(e,t):(0,dl.nextTick)()},closeTooltip(){var e=U.tooltipStore,t=X.value,r=me.value;return e.visible&&(Object.assign(e,{row:null,column:null,content:null,visible:!1,currOpts:{}}),t)&&t.close&&t.close(),r&&r.close&&r.close(),(0,dl.nextTick)()},isAllCheckboxChecked(){return U.isAllSelected},isAllCheckboxIndeterminate(){return!U.isAllSelected&&U.isIndeterminate},getCheckboxIndeterminateRecords(e){var t=z.treeConfig;let o=B.fullDataRowIdData;var a=U.treeIndeterminateMaps;if(t){let r=[],l=[];return al().each(a,(e,t)=>{e&&(r.push(e),o[t])&&l.push(e)}),e?r:l}return[]},setCheckboxRow(e,t){return e&&!al().isArray(e)&&(e=[e]),x(e,t,!0)},setCheckboxRowKey(e,t){let r=B.fullAllDataRowIdData,l=(al().isArray(e)||(e=[e]),[]);return e.forEach(e=>{e=r[e];e&&l.push(e.row)}),x(l,t,!0)},isCheckedByCheckboxRow(e){var t=U.selectCheckboxMaps,r=le.value.checkField;return r?al().get(e,r):!!t[xl(se,e)]},isCheckedByCheckboxRowKey(e){var t=U.selectCheckboxMaps,r=B.fullAllDataRowIdData,l=le.value.checkField;return l?!!(r=r[e])&&al().get(r.row,l):!!t[e]},isIndeterminateByCheckboxRow(e){var t=U.treeIndeterminateMaps;return!!t[xl(se,e)]&&!se.isCheckedByCheckboxRow(e)},isIndeterminateByCheckboxRowKey(e){var t=U.treeIndeterminateMaps;return!!t[e]&&!se.isCheckedByCheckboxRowKey(e)},toggleCheckboxRow(e){var t=U.selectCheckboxMaps,r=le.value.checkField,r=r?!al().get(e,r):!t[xl(se,e)];return W.handleBatchSelectRows([e],r,!0),W.checkSelectionStatus(),(0,dl.nextTick)()},setAllCheckboxRow(e){return Nt(e,!0)},getRadioReserveRecord(e){var l=z.treeConfig,{fullDataRowIdData:t,radioReserveRow:o,afterFullData:a}=B,r=D.value,n=ie.value,n=n.children||n.childrenField;if(r.reserve&&o){let r=xl(se,o);if(e){if(!t[r])return o}else{let t=gl(se);if(l){if(al().findTree(a,e=>r===al().get(e,t),{children:n}))return o}else if(!a.some(e=>r===al().get(e,t)))return o}}return null},clearRadioReserve(){return(B.radioReserveRow=null,dl.nextTick)()},getCheckboxReserveRecords(l){var e=z.treeConfig;let{afterFullData:t,fullDataRowIdData:o,checkboxReserveRowMap:a}=B;var r=le.value,n=ie.value,n=n.children||n.childrenField;let i=[];if(r.reserve){let r={};e?al().eachTree(t,e=>{r[xl(se,e)]=1},{children:n}):t.forEach(e=>{r[xl(se,e)]=1}),al().each(a,(e,t)=>{e&&(l?o[t]||i.push(e):r[t]||i.push(e))})}return i},clearCheckboxReserve(){return B.checkboxReserveRowMap={},(0,dl.nextTick)()},toggleAllCheckboxRow(){return lr(null,!U.isAllSelected),(0,dl.nextTick)()},clearCheckboxRow(){let t=z.treeConfig;var e=B.tableFullData,r=ie.value,r=r.children||r.childrenField,l=le.value;let{checkField:o,reserve:a}=l,n=l.indeterminateField||l.halfField;return o&&(l=e=>{t&&n&&al().set(e,n,!1),al().set(e,o,!1)},t?al().eachTree(e,l,{children:r}):e.forEach(l)),a&&e.forEach(e=>_t(e,!1)),U.isAllSelected=!1,U.isIndeterminate=!1,U.selectCheckboxMaps={},U.treeIndeterminateMaps={},(0,dl.nextTick)()},setCurrentRow(e){var t=ee.value,r=q.value;return P.clearCurrentRow(),U.currentRow=e,(t.isCurrent||z.highlightCurrentRow)&&r&&al().arrayEach(r.querySelectorAll(`[rowid="${xl(se,e)}"]`),e=>hl(e,"row--current")),(0,dl.nextTick)()},isCheckedByRadioRow(e){var t=U.selectRadioRow;return!(!e||!t)&&se.eqRow(t,e)},isCheckedByRadioRowKey(e){var t=U.selectRadioRow;return!!t&&e===xl(se,t)},setRadioRow(e){return Ht(e,!0)},setRadioRowKey(e){var t=B.fullAllDataRowIdData,t=t[e];return t?Ht(t.row,!0):(0,dl.nextTick)()},clearCurrentRow(){var e=q.value;return U.currentRow=null,B.hoverRow=null,e&&al().arrayEach(e.querySelectorAll(".row--current"),e=>pl(e,"row--current")),(0,dl.nextTick)()},clearRadioRow(){return(U.selectRadioRow=null,dl.nextTick)()},getCurrentRecord(){return ee.value.isCurrent||z.highlightCurrentRow?U.currentRow:null},getRadioRecord(e){var{fullDataRowIdData:t,afterFullRowMaps:r}=B,l=U.selectRadioRow;if(l){var o=xl(se,l);if(e){if(t[o])return l}else if(r[o])return l}return null},getCurrentColumn(){return J.value.isCurrent||z.highlightCurrentColumn?U.currentColumn:null},setCurrentColumn(e){e=jr(se,e);return e&&(P.clearCurrentColumn(),U.currentColumn=e),(0,dl.nextTick)()},clearCurrentColumn(){return(U.currentColumn=null,dl.nextTick)()},setPendingRow(e,t){let r=Object.assign({},U.pendingRowMaps);return e&&!al().isArray(e)&&(e=[e]),t?e.forEach(e=>{var t=xl(se,e);t&&!r[t]&&(r[t]=e)}):e.forEach(e=>{e=xl(se,e);e&&r[e]&&delete r[e]}),U.pendingRowMaps=r,(0,dl.nextTick)()},togglePendingRow(e){let r=Object.assign({},U.pendingRowMaps);return(e=e&&!al().isArray(e)?[e]:e).forEach(e=>{var t=xl(se,e);t&&(r[t]?delete r[t]:r[t]=e)}),U.pendingRowMaps=r,(0,dl.nextTick)()},hasPendingByRow(e){return P.isPendingByRow(e)},isPendingByRow(e){var t=U.pendingRowMaps;return!!t[xl(se,e)]},getPendingRecords(){var e=U.pendingRowMaps;let r=B.fullAllDataRowIdData,l=[];return al().each(e,(e,t)=>{r[t]&&l.push(e)}),l},clearPendingRow(){return U.pendingRowMaps={},(0,dl.nextTick)()},sort(e,t){let{multiple:r,remote:l,orders:a}=C.value;return e&&al().isString(e)&&(e=[{field:e,order:t}]),(e=al().isArray(e)?e:[e]).length?(r||Ct(),(r?e:[e[0]]).forEach((e,t)=>{let{field:r,order:l}=e,o=r;(o=al().isString(r)?P.getColumnByField(r):o)&&o.sortable&&(-1===a.indexOf(l)&&(l=dt(o)),o.order!==l&&(o.order=l),o.sortTime=Date.now()+t)}),l||W.handleTableData(!0),(0,dl.nextTick)().then(()=>(P.updateCellAreas(),g()))):(0,dl.nextTick)()},setSort(e,t){let{multiple:r,remote:l,orders:n}=C.value;if((e=al().isArray(e)?e:[e])&&e.length){r||(e=[e[0]],Ct());let a=null;return e.forEach((e,t)=>{let{field:r,order:l}=e,o=r;al().isString(r)&&(o=P.getColumnByField(r)),a=a||o,o&&o.sortable&&(-1===n.indexOf(l)&&(l=dt(o)),o.order!==l&&(o.order=l),o.sortTime=Date.now()+t)}),t&&(l||W.handleTableData(!0),se.handleColumnSortEvent(new Event("click"),a)),(0,dl.nextTick)().then(()=>(P.updateCellAreas(),g()))}return(0,dl.nextTick)()},clearSort(e){var t=C.value;return e?(e=jr(se,e))&&(e.order=null):Ct(),t.remote||W.handleTableData(!0),(0,dl.nextTick)().then(g)},isSort(e){return e?!!(e=jr(se,e))&&e.sortable&&!!e.order:0<P.getSortColumns().length},getSortColumns(){var{multiple:e,chronological:t}=C.value;let l=[];var r=B.tableFullColumn;return r.forEach(e=>{var{field:t,order:r}=e;e.sortable&&r&&l.push({column:e,field:t,property:t,order:r,sortTime:e.sortTime})}),e&&t&&1<l.length?al().orderBy(l,"sortTime"):l},closeFilter(){var e=U.filterStore,{column:t,visible:r}=e;return Object.assign(e,{isAllSelected:!1,isIndeterminate:!1,options:[],visible:!1}),r&&ce("filter-visible",{column:t,property:t.field,field:t.field,filterList:se.getCheckedFilters(),visible:!1},null),(0,dl.nextTick)()},isActiveFilterByColumn(e){e=jr(se,e);return e?e.filters&&e.filters.some(e=>e.checked):0<se.getCheckedFilters().length},isFilter(e){return P.isActiveFilterByColumn(e)},isRowExpandLoaded(e){var t=B.fullAllDataRowIdData,t=t[xl(se,e)];return t&&!!t.expandLoaded},clearRowExpandLoaded(e){var t={...U.rowExpandLazyLoadedMaps},r=B.fullAllDataRowIdData,l=M.value.lazy,e=xl(se,e),r=r[e];return l&&r&&(r.expandLoaded=!1,delete t[e]),U.rowExpandLazyLoadedMaps=t,(0,dl.nextTick)()},reloadRowExpand(e){var t=U.rowExpandLazyLoadedMaps,r=M.value.lazy,l=xl(se,e);return r&&!t[l]&&P.clearRowExpandLoaded(e).then(()=>zt(e)),(0,dl.nextTick)()},reloadExpandContent(e){return Pr("vxe.error.delFunc",["reloadExpandContent","reloadRowExpand"]),P.reloadRowExpand(e)},toggleRowExpand(e){return P.setRowExpand(e,!P.isRowExpandByRow(e))},setAllRowExpand(e){var t=ie.value,{tableFullData:r,tableFullTreeData:l}=B,t=t.children||t.childrenField;let o=[];return z.treeConfig?al().eachTree(l,e=>{o.push(e)},{children:t}):o=r,P.setRowExpand(o,e)},setRowExpand(e,t){let{rowExpandedMaps:r,rowExpandLazyLoadedMaps:l,expandColumn:o}=U,a=B.fullAllDataRowIdData,n={...r};let{reserve:i,lazy:s,accordion:c,toggleMethod:d}=M.value,u=[],p=P.getColumnIndex(o),h=P.getVMColumnIndex(o);return e&&(al().isArray(e)||(e=[e]),c&&(n={},e=e.slice(e.length-1,e.length)),e=d?e.filter(e=>d({$table:se,expanded:t,column:o,columnIndex:p,$columnIndex:h,row:e,rowIndex:P.getRowIndex(e),$rowIndex:P.getVMRowIndex(e)})):e,t?e.forEach(e=>{var t,r=xl(se,e);n[r]||(t=a[r],s&&!t.expandLoaded&&!l[r]?u.push(zt(e)):n[r]=e)}):e.forEach(e=>{e=xl(se,e);n[e]&&delete n[e]}),i)&&e.forEach(e=>jt(e,t)),U.rowExpandedMaps=n,Promise.all(u).then(()=>P.recalculate())},isRowExpandByRow(e){var t=U.rowExpandedMaps;return!!t[xl(se,e)]},isExpandByRow(e){return Pr("vxe.error.delFunc",["isExpandByRow","isRowExpandByRow"]),P.isRowExpandByRow(e)},clearRowExpand(){var e=B.tableFullData,t=M.value.reserve;let r=P.getRowExpandRecords();return U.rowExpandedMaps={},t&&e.forEach(e=>jt(e,!1)),(0,dl.nextTick)().then(()=>{r.length&&P.recalculate()})},clearRowExpandReserve(){return B.rowExpandedReserveRowMap={},(0,dl.nextTick)()},getRowExpandRecords(){let t=[];return al().each(U.rowExpandedMaps,e=>{e&&t.push(e)}),t},getTreeExpandRecords(){let t=[];return al().each(U.treeExpandedMaps,e=>{e&&t.push(e)}),t},isTreeExpandLoaded(e){var t=B.fullAllDataRowIdData,t=t[xl(se,e)];return t&&!!t.treeLoaded},clearTreeExpandLoaded(e){let r={...U.treeExpandedMaps},l=B.fullAllDataRowIdData;var t=ie.value.transform;return e&&(e=al().isArray(e)?e:[e]).forEach(e=>{var e=xl(se,e),t=l[e];t&&(t.treeLoaded=!1,r[e])&&delete r[e]}),U.treeExpandedMaps=r,t?(At(),W.handleTableData()):(0,dl.nextTick)()},reloadTreeExpand(e){var t=U.treeExpandLazyLoadedMaps,r=ie.value,l=r.hasChild||r.hasChildField;let{transform:o,lazy:a}=r;r=xl(se,e);return a&&e[l]&&!t[r]?P.clearTreeExpandLoaded(e).then(()=>Pt(e)).then(()=>{if(o)return At(),W.handleTableData()}).then(()=>P.recalculate()):(0,dl.nextTick)()},reloadTreeChilds(e){return Pr("vxe.error.delFunc",["reloadTreeChilds","reloadTreeExpand"]),P.reloadTreeExpand(e)},toggleTreeExpand(e){return P.setTreeExpand(e,!P.isTreeExpandByRow(e))},setAllTreeExpand(e){var t=B.tableFullData,r=ie.value;let{transform:l,lazy:o}=r,a=r.children||r.childrenField,n=[];return al().eachTree(t,e=>{var t=e[a];(o||t&&t.length)&&n.push(e)},{children:a}),P.setTreeExpand(n,e).then(()=>{if(l)return At(),P.recalculate()})},setTreeExpand(e,t){var r,l=ie.value.transform;return e&&(e=al().isArray(e)?e:[e]).length?l?(l=e,r=t,rr(l,r).then(()=>(At(),W.handleTableData(),Ot(),(0,dl.nextTick)())).then(()=>P.recalculate(!0)).then(()=>{setTimeout(()=>{P.updateCellAreas()},30)})):rr(e,t):(0,dl.nextTick)()},isTreeExpandByRow(e){var t=U.treeExpandedMaps;return!!t[xl(se,e)]},clearTreeExpand(){var e=B.tableFullTreeData,t=ie.value,r=t.children||t.childrenField;let{transform:l,reserve:o}=t,a=P.getTreeExpandRecords();return U.treeExpandedMaps={},o&&al().eachTree(e,e=>Bt(e,!1),{children:r}),W.handleTableData().then(()=>{if(l)return At(),W.handleTableData()}).then(()=>{if(a.length)return P.recalculate()})},clearTreeExpandReserve(){return B.treeExpandedReserveRowMap={},(0,dl.nextTick)()},getScroll(){var{scrollXLoad:e,scrollYLoad:t}=U,r=B.elemStore,r=wl(r["main-body-scroll"]);return{virtualX:e,virtualY:t,scrollTop:r?r.scrollTop:0,scrollLeft:r?r.scrollLeft:0}},scrollTo(e,t){var r=B.elemStore,l=wl(r["main-header-scroll"]),o=wl(r["main-body-scroll"]),a=wl(r["main-footer-scroll"]),n=wl(r["left-body-scroll"]),r=wl(r["right-body-scroll"]),i=S.value,s=I.value;return B.intoRunScroll=!0,al().isNumber(e)&&(vl(i,e),vl(o,e),vl(l,e),vl(a,e)),al().isNumber(t)&&(ml(s,t),ml(o,t),ml(n,t),ml(r,t)),U.scrollXLoad||U.scrollYLoad?new Promise(e=>{setTimeout(()=>{(0,dl.nextTick)(()=>{B.intoRunScroll=!1,e()})},30)}):(0,dl.nextTick)()},scrollToRow(e,t){let{isAllOverflow:r,scrollYLoad:l,scrollXLoad:o}=U;var a,n,i=[];return e&&(z.treeConfig?i.push(W.scrollToTreeRow(e)):i.push($l(se,e))),t&&i.push((t=t,a=e,n=B.fullColumnIdData,(t=jr(se,t))&&n[t.id]?_l(se,t,a):(0,dl.nextTick)())),Promise.all(i).then(()=>{if(e)return r||!l&&!o||(Mt(),St()),(0,dl.nextTick)()})},scrollToColumn(e){var t=B.fullColumnIdData,e=jr(se,e);return e&&t[e.id]?_l(se,e):(0,dl.nextTick)()},clearScroll(){var{elemStore:e,scrollXStore:t,scrollYStore:r}=B,l=wl(e["main-header-scroll"]),o=wl(e["main-body-scroll"]),a=wl(e["main-footer-scroll"]),n=wl(e["left-body-scroll"]),e=wl(e["right-body-scroll"]),i=S.value,s=I.value;return B.intoRunScroll=!0,vl(i,0),vl(o,0),vl(l,0),vl(a,0),ml(s,0),ml(o,0),ml(n,0),ml(e,0),t.startIndex=0,t.visibleStartIndex=0,t.endIndex=t.visibleSize,t.visibleEndIndex=t.visibleSize,r.startIndex=0,r.visibleStartIndex=0,r.endIndex=r.visibleSize,r.visibleEndIndex=r.visibleSize,(0,dl.nextTick)().then(()=>{B.intoRunScroll=!1})},updateFooter(){var{showFooter:e,footerData:t,footerMethod:r}=z,{visibleColumn:l,afterFullData:o}=B;let a=[];return e&&t&&t.length?a=t.slice(0):e&&r&&(a=l.length?r({columns:l,data:o,$table:se,$grid:G}):[]),U.footerTableData=a,(0,dl.nextTick)()},updateStatus(a,n){let i=!al().isUndefined(n);return(0,dl.nextTick)().then(()=>{var e=z.editRules;let o=U.validStore;var t=b.value;if(a&&t&&e){let{row:r,column:l}=a;if(se.hasCellRules&&se.hasCellRules("change",r,l)){let t=P.getCellElement(r,l);if(t)return se.validCellRules("change",r,l,n).then(()=>{i&&o.visible&&Cl(r,l,n),se.clearValidate(r,l)}).catch(({rule:e})=>{i&&Cl(r,l,n),se.showValidTooltip({rule:e,row:r,column:l,cell:t})})}}})},setMergeCells(e){return z.spanMethod&&Br("vxe.error.errConflicts",["merge-cells","span-method"]),xt(e,U.mergeList,B.afterFullData),(0,dl.nextTick)().then(()=>(P.updateCellAreas(),g()))},removeMergeCells(e){z.spanMethod&&Br("vxe.error.errConflicts",["merge-cells","span-method"]);let t=bt(e,U.mergeList,B.afterFullData);return(0,dl.nextTick)().then(()=>(P.updateCellAreas(),g(),t))},getMergeCells(){return U.mergeList.slice(0)},clearMergeCells(){return U.mergeList=[],(0,dl.nextTick)().then(()=>g())},setMergeFooterItems(e){return z.footerSpanMethod&&Br("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]),xt(e,U.mergeFooterList),(0,dl.nextTick)().then(()=>(P.updateCellAreas(),g()))},removeMergeFooterItems(e){z.footerSpanMethod&&Br("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]);let t=bt(e,U.mergeFooterList);return(0,dl.nextTick)().then(()=>(P.updateCellAreas(),g(),t))},getMergeFooterItems(){return U.mergeFooterList.slice(0)},clearMergeFooterItems(){return U.mergeFooterList=[],(0,dl.nextTick)().then(()=>g())},updateCellAreas(){var e=z.mouseConfig,t=ae.value;return e&&t.area&&se.handleRecalculateCellAreas?se.handleRecalculateCellAreas():(0,dl.nextTick)()},getCustomStoreData(){var e=z.id,t=T.value;let c=B.collectColumn,d=t.checkMethod,u={},p={},h={},m={};t={resizableData:void 0,sortData:void 0,visibleData:void 0,fixedData:void 0};if(e){let a=0,n=0,i=0,s=0;al().eachTree(c,(e,t,r,l,o)=>{o||(c.forEach(e=>{var t=e.getKey();t&&(n=1,p[t]=e.renderSortNumber)}),e.fixed!==e.defaultFixed&&(o=e.getKey())&&(i=1,m[o]=e.fixed)),e.resizeWidth&&(o=e.getKey())&&(a=1,u[o]=e.renderWidth),d&&!d({column:e})||(!e.visible&&e.defaultVisible?(o=e.getKey())&&(s=1,h[o]=!1):e.visible&&!e.defaultVisible&&(o=e.getKey())&&(s=1,h[o]=!0))}),a&&(t.resizableData=u),n&&(t.sortData=p),i&&(t.fixedData=m),s&&(t.visibleData=h)}else Br("vxe.error.reqProp",["id"]);return t},focus(){return B.isActivated=!0,(0,dl.nextTick)()},blur(){return(B.isActivated=!1,dl.nextTick)()},connect(e){return e?(O=e).syncUpdate({collectColumn:B.collectColumn,$table:se}):Br("vxe.error.barUnableLink"),(0,dl.nextTick)()}},r=>{var{editStore:e,ctxMenuStore:t,filterStore:l,customStore:o}=U;let{mouseConfig:a,editRules:n}=z,i=q.value,s=oe.value,c=Ae.value;var d=He.value;let u=e.actived;var e=ve.value,p=ge.value,h=xe.value,m=fe.value;if(!p||fl(r,i,"vxe-cell--filter").flag||fl(r,p.$el).flag||fl(r,document.body,"vxe-table--ignore-clear").flag||W.preventEvent(r,"event.clearFilter",l.args,P.closeFilter),!h||o.btnEl===r.target||fl(r,document.body,"vxe-toolbar-custom-target").flag||fl(r,h.$el).flag||fl(r,document.body,"vxe-table--ignore-clear").flag||W.preventEvent(r,"event.clearCustom",{},()=>{se.closeCustom&&se.closeCustom()}),u.row)!1===s.autoClear||(p=u.args.cell)&&fl(r,p).flag||e&&fl(r,e.$el).flag||B._lastCallTime&&!(B._lastCallTime+50<Date.now())||fl(r,document.body,"vxe-table--ignore-clear").flag||W.preventEvent(r,"event.clearEdit",u.args,()=>{let e;var t;(e=(e=(e="row"===s.mode?!!(t=(t=fl(r,i,"vxe-body--row")).flag?P.getRowNode(t.targetElem):null)&&!se.eqRow(t.item,u.args.row):!fl(r,i,"col--edit").flag)||fl(r,i,"vxe-header--row").flag)||fl(r,i,"vxe-footer--row").flag)||!z.height||U.overflowY||ul(t=r.target,"vxe-table--body-wrapper")&&(e=r.offsetY<t.clientHeight),!e&&fl(r,i).flag||setTimeout(()=>{se.handleClearEdit(r).then(()=>{!B.isActivated&&n&&c.autoClear&&(U.validErrorMaps={})})})});else if(a&&!(fl(r,i).flag||G&&fl(r,G.getRefMaps().refElem.value).flag||m&&fl(r,m.getRefMaps().refElem.value).flag||O&&fl(r,O.getRefMaps().refElem.value).flag)&&(se.clearSelected&&se.clearSelected(),d.autoClear)&&se.getCellAreas){let e=se.getCellAreas();e&&e.length&&!fl(r,document.body,"vxe-table--ignore-areas-clear").flag&&W.preventEvent(r,"event.clearAreas",{},()=>{se.clearCellAreas(),se.clearCopyCellArea(),ce("clear-cell-area-selection",{cellAreas:e},r)})}se.closeMenu&&t.visible&&m&&!fl(r,m.getRefMaps().refElem.value).flag&&se.closeMenu();l=fl(r,G?G.getRefMaps().refElem.value:i).flag;!l&&n&&c.autoClear&&(U.validErrorMaps={}),B.isActivated=l}),cr=()=>{P.closeFilter(),se.closeMenu&&se.closeMenu()},dr=()=>{P.closeTooltip(),se.closeMenu&&se.closeMenu()},ur=t=>{let{mouseConfig:e,keyboardConfig:r}=z,{filterStore:l,ctxMenuStore:o,editStore:a}=U,n=ae.value,i=ne.value,s=a.actived;qr.hasKey(t,Xr.ESCAPE)&&W.preventEvent(t,"event.keydown",null,()=>{if(ce("keydown-start",{},t),r&&e&&n.area&&se.handleKeyboardCellAreaEvent)se.handleKeyboardCellAreaEvent(t);else if((s.row||l.visible||o.visible)&&(t.stopPropagation(),se.closeMenu&&se.closeMenu(),P.closeFilter(),r)&&i.isEsc&&s.row){let e=s.args;se.handleClearEdit(t),n.selected&&(0,dl.nextTick)(()=>se.handleSelected(e,t))}ce("keydown",{},t),ce("keydown-end",{},t)})},pr=N=>{B.isActivated&&W.preventEvent(N,"event.keydown",null,()=>{var{mouseConfig:e,keyboardConfig:t,treeConfig:r,editConfig:l,highlightCurrentRow:o}=z;let{ctxMenuStore:a,editStore:n,currentRow:i}=U;var s=B.afterFullData,c=Xe.value,d=We.value,u=ne.value,p=ae.value,h=oe.value,m=ie.value,v=Ke.value,f=ee.value,{selected:g,actived:x}=n,m=m.children||m.childrenField,b=N.keyCode,C=qr.hasKey(N,Xr.ESCAPE),w=qr.hasKey(N,Xr.BACKSPACE),$=qr.hasKey(N,Xr.TAB),y=qr.hasKey(N,Xr.ENTER),T=qr.hasKey(N,Xr.SPACEBAR),E=qr.hasKey(N,Xr.ARROW_LEFT),R=qr.hasKey(N,Xr.ARROW_UP),S=qr.hasKey(N,Xr.ARROW_RIGHT),I=qr.hasKey(N,Xr.ARROW_DOWN),_=qr.hasKey(N,Xr.DELETE),D=qr.hasKey(N,Xr.F2),M=qr.hasKey(N,Xr.CONTEXT_MENU),H=N.metaKey,k=N.ctrlKey,F=N.shiftKey,O=N.altKey,A=E||R||S||I,c=c&&a.visible&&(y||T||A),L=nl(l)&&x.column&&x.row;let V=h.beforeEditMethod||h.activeMethod;if(c)N.preventDefault(),a.showChild&&Rl(a.selected)?se.moveCtxMenu(N,a,"selectChild",E,!1,a.selected.children):se.moveCtxMenu(N,a,"selected",S,!0,v);else if(t&&e&&p.area&&se.handleKeyboardCellAreaEvent)se.handleKeyboardCellAreaEvent(N);else if(C){if(se.closeMenu&&se.closeMenu(),P.closeFilter(),t&&u.isEsc&&x.row){let e=x.args;se.handleClearEdit(N),p.selected&&(0,dl.nextTick)(()=>se.handleSelected(e,N))}}else if(T&&t&&u.isChecked&&g.row&&g.column&&("checkbox"===g.column.type||"radio"===g.column.type))N.preventDefault(),"checkbox"===g.column.type?W.handleToggleCheckRowEvent(N,g.args):W.triggerRadioRowEvent(N,g.args);else if(D&&nl(l))L||g.row&&g.column&&(N.preventDefault(),se.handleEdit(g.args,N));else if(M)B._keyCtx=g.row&&g.column&&d.length,clearTimeout(B.keyCtxTimeout),B.keyCtxTimeout=setTimeout(()=>{B._keyCtx=!1},1e3);else if(y&&!O&&t&&u.isEnter&&(g.row||x.row||r&&(f.isCurrent||o)&&i)){var{isLastEnterAppendRow:c,beforeEnterMethod:v,enterMethod:e}=u;if(k){if(x.row){let e=x.args;se.handleClearEdit(N),p.selected&&(0,dl.nextTick)(()=>{se.handleSelected(e,N)})}}else if(g.row||x.row){let r=(g.row?g:x).args;if(F)u.enterToTab?se.moveTabSelected(r,F,N):se.moveSelected(r,E,!0,S,!1,N);else if(u.enterToTab)se.moveTabSelected(r,F,N);else{var C=g.row||x.row;let t=g.column||x.column;D=se.getVTRowIndex(C),M={row:C,rowIndex:se.getRowIndex(C),$rowIndex:se.getVMRowIndex(C),_rowIndex:D,column:t,columnIndex:se.getColumnIndex(t),$columnIndex:se.getVMColumnIndex(t),_columnIndex:se.getVTColumnIndex(t),$table:se};if(!v||!1!==v(M)){if(c&&D>=s.length-1)return se.insertAt({},-1).then(({row:e})=>{se.scrollToRow(e,t),se.handleSelected({...r,row:e},N)}),void se.dispatchEvent("enter-append-row",M,N);se.moveSelected(r,E,!1,S,!0,N),e&&e(M)}}}else if(r&&(f.isCurrent||o)&&i){d=i[m];if(d&&d.length){N.preventDefault();let e=d[0],t={$table:se,row:e,rowIndex:P.getRowIndex(e),$rowIndex:P.getVMRowIndex(e)};P.setTreeExpand(i,!0).then(()=>P.scrollToRow(e)).then(()=>W.triggerCurrentRowEvent(N,t))}}}else if(A&&t&&u.isArrow)L||(g.row&&g.column?se.moveSelected(g.args,E,R,S,I,N):(R||I)&&(f.isCurrent||o)&&se.moveCurrentRow(R,I,N));else if($&&t&&u.isTab)g.row||g.column?se.moveTabSelected(g.args,F,N):(x.row||x.column)&&se.moveTabSelected(x.args,F,N);else if(t&&u.isDel&&_&&nl(l)&&(g.row||g.column))L||(y=u.delMethod,O={row:g.row,rowIndex:P.getRowIndex(g.row),column:g.column,columnIndex:P.getColumnIndex(g.column),$table:se,$grid:G},V&&!V(O))||(y?y(O):Cl(g.row,g.column,null),P.updateFooter(),ce("cell-delete-value",O,N));else if(w&&t&&u.isBack&&nl(l)&&(g.row||g.column))L||(p=u.backMethod,u.isDel&&nl(l)&&(g.row||g.column)&&(C={row:g.row,rowIndex:P.getRowIndex(g.row),column:g.column,columnIndex:P.getColumnIndex(g.column),$table:se,$grid:G},V&&!V(C)||(p?p(C):(Cl(g.row,g.column,null),se.handleEdit(g.args,N)),ce("cell-backspace-value",C,N))));else if(w&&t&&r&&u.isBack&&(f.isCurrent||o)&&i){let t=al().findTree(B.afterTreeFullData,e=>e===i,{children:m}).parent;if(t){N.preventDefault();let e={row:t,rowIndex:P.getRowIndex(t),$rowIndex:P.getVMRowIndex(t),$table:se,$grid:G};P.setTreeExpand(t,!1).then(()=>P.scrollToRow(t)).then(()=>W.triggerCurrentRowEvent(N,e))}}else if(t&&nl(l)&&u.isEdit&&!k&&!H&&(T||48<=b&&b<=57||65<=b&&b<=90||96<=b&&b<=111||186<=b&&b<=192||219<=b&&b<=222)){var{editMode:v,editMethod:c}=u;if(g.column&&g.row&&nl(g.column.editRender)){let e=h.beforeEditMethod||h.activeMethod;D={row:g.row,rowIndex:P.getRowIndex(g.row),column:g.column,columnIndex:P.getColumnIndex(g.column),$table:se,$grid:G};e&&!e({...g.args,$table:se,$grid:G})||(c?c(D):("insert"!==v&&Cl(g.row,g.column,null),se.handleEdit(g.args,N)))}}ce("keydown",{},N)})},hr=e=>{var{keyboardConfig:t,mouseConfig:r}=z,{editStore:l,filterStore:o}=U,a=B.isActivated,n=ae.value,i=ne.value,l=l.actived;a&&!o.visible&&(l.row||l.column||t&&i.isClip&&r&&n.area&&se.handlePasteCellAreaEvent&&se.handlePasteCellAreaEvent(e),ce("paste",{},e))},mr=e=>{var{keyboardConfig:t,mouseConfig:r}=z,{editStore:l,filterStore:o}=U,a=B.isActivated,n=ae.value,i=ne.value,l=l.actived;a&&!o.visible&&(l.row||l.column||t&&i.isClip&&r&&n.area&&se.handleCopyCellAreaEvent&&se.handleCopyCellAreaEvent(e),ce("copy",{},e))},vr=e=>{var{keyboardConfig:t,mouseConfig:r}=z,{editStore:l,filterStore:o}=U,a=B.isActivated,n=ae.value,i=ne.value,l=l.actived;a&&!o.visible&&(l.row||l.column||t&&i.isClip&&r&&n.area&&se.handleCutCellAreaEvent&&se.handleCutCellAreaEvent(e),ce("cut",{},e))},fr=()=>{se.closeMenu&&se.closeMenu();var e=q.value;if(!e||!e.clientWidth)return(0,dl.nextTick)();P.recalculate(!0),P.updateCellAreas()},gr=e=>{var t=X.value;clearTimeout(B.tooltipTimeout),e?P.closeTooltip():t&&t.setActived&&t.setActived(!0)},xr=()=>{var{dragRow:e,dragCol:t}=U;(e||t)&&(Cr(),br(),yr(),U.dragRow=null,U.dragCol=null,U.isDragColMove=!1,U.isDragRowMove=!1)},br=()=>{var e=q.value;if(e){let t="row--drag-origin";al().arrayEach(e.querySelectorAll("."+t),e=>{e.draggable=!1,pl(e,t)})}},Cr=()=>{var e=q.value;if(e){let t="col--drag-origin";al().arrayEach(e.querySelectorAll("."+t),e=>{e.draggable=!1,pl(e,t)})}},wr=(e,r,l,t,o)=>{var a=q.value;if(a){var{overflowX:n,scrollbarWidth:i,overflowY:s,scrollbarHeight:c}=U,d=B.prevDragToChild,u=a.getBoundingClientRect(),s=s?i:0,i=n?c:0,n=a.clientWidth,c=a.clientHeight;if(r){var p=Te.value;if(p)if(t){var h=Q.value,m=r.getBoundingClientRect();let e=r.clientHeight;r=Math.max(1,m.y-u.y);r+e>c-i&&(e=c-r-i),p.style.display="block",p.style.left=`${h?s:0}px`,p.style.top=r+"px",p.style.height=e+"px",p.style.width=n-s+"px",p.setAttribute("drag-pos",o),p.setAttribute("drag-to-child",d?"y":"n")}else p.style.display=""}else if(l){m=Ee.value;if(m)if(t){var h=Z.value,r=K.value,p=r?r.clientWidth:0,r=Y.value,r=r?r.clientWidth:0,v=l.getBoundingClientRect();let e=l.clientWidth;l=Math.max(0,v.y-u.y);let t=v.x-u.x;t<p&&(e-=p-t,t=p);p=n-r-(r?0:s);t+e>p&&(e=p-t),m.style.display="block",m.style.top=l+"px",m.style.left=t+"px",m.style.width=e+"px",m.style.height=d?v.height+"px":c-l-(h?0:i)+"px",m.setAttribute("drag-pos",o),m.setAttribute("drag-to-child",d?"y":"n")}else m.style.display=""}n=ye.value;n&&(n.style.display="block",n.style.top=Math.min(a.clientHeight-a.scrollTop-n.clientHeight,e.clientY-u.y)+"px",n.style.left=Math.min(a.clientWidth-a.scrollLeft-n.clientWidth-16,e.clientX-u.x)+"px",n.setAttribute("drag-status",t?d?"sub":"normal":"disabled"))}},yr=()=>{var e=ye.value,t=Te.value,r=Ee.value;e&&(e.style.display=""),t&&(t.style.display=""),r&&(r.style.display="")},Tr=(e,l,o,a,n)=>{if(o){n.cell=l;var l=U.tooltipStore,{column:i,row:s}=n,{showAll:c,contentMethod:d}=N.value,n=d?d(n):null,d=d&&!al().eqNull(n);let t=d?n:al().toString("html"===i.type?o.innerText:o.textContent).trim(),r=o.scrollWidth>o.clientWidth;t&&(c||d||r)&&(Object.assign(l,{row:s,column:i,visible:!0,currOpts:{}}),(0,dl.nextTick)(()=>{var e=X.value;e&&e.open&&e.open(!r&&a||o,sl(t))}))}return(0,dl.nextTick)()},Er=(e,t)=>{if(e){if(G)return G.callSlot(e,t);if(al().isFunction(e))return yl(e(t))}return[]},Rr=(W={getSetupOptions(){return Ur()},updateAfterDataIndex:Ot,callSlot:Er,getParentElem(){var e,t=q.value;return G?(e=G.getRefMaps().refElem.value)?e.parentNode:null:t?t.parentNode:null},getParentHeight(){var e=z.height,t=q.value;return t?(t=t.parentNode,e="100%"===e||"auto"===e?kl(t):0,Math.floor(G?G.getParentHeight():al().toNumber(getComputedStyle(t).height)-e)):0},getExcludeHeight(){return G?G.getExcludeHeight():0},defineField(e){let t=z.treeConfig,r=M.value,l=ie.value,a=D.value,n=le.value,i=l.children||l.childrenField,s=gl(se);return(e=al().isArray(e)?e:[e]).map(o=>(B.tableFullColumn.forEach(t=>{var{field:r,editRender:l}=t;if(r&&!al().has(o,r)&&!o[r]){let e=null;l&&(l=l.defaultValue,al().isFunction(l)?e=l({column:t}):al().isUndefined(l)||(e=l)),al().set(o,r,e)}}),[a.labelField,n.checkField,n.labelField,r.labelField].forEach(e=>{e&&cl(al().get(o,e))&&al().set(o,e,null)}),t&&l.lazy&&al().isUndefined(o[i])&&(o[i]=null),cl(al().get(o,s))&&al().set(o,s,Al()),o))},handleTableData(e){var t=U.scrollYLoad;let{scrollYStore:r,fullDataRowIdData:l}=B,o=B.afterFullData;e&&((()=>{var e=z.treeConfig,{tableFullColumn:t,tableFullData:r,tableFullTreeData:l}=B,o=_e.value,i=C.value,s=ie.value,c=s.children||s.childrenField,d=s.transform;let{remote:u,filterMethod:p}=o,{remote:h,sortMethod:m,multiple:v,chronological:f}=i,g=[],x=[];if(u&&h)e&&d?(x=al().searchTree(l,()=>!0,{original:!0,isEvery:!0,children:s.mapChildrenField,mapChildren:c}),g=x):(g=(e?l:r).slice(0),x=g);else{let a=[],n=[];t.forEach(e=>{var{field:t,sortable:r,order:l,filters:o}=e;if(!u&&o&&o.length){let t=[],r=[];o.forEach(e=>{e.checked&&(r.push(e),t.push(e.value))}),r.length&&a.push({column:e,valueList:t,itemList:r})}!h&&r&&l&&n.push({column:e,field:t,property:t,order:l,sortTime:e.sortTime})}),v&&f&&1<n.length&&(n=al().orderBy(n,"sortTime")),!u&&a.length?(o=c=>a.every(({column:t,valueList:e,itemList:r})=>{let{filterMethod:l,filterRender:o}=t;var a=nl(o)?Gr.get(o.name):null;let n=a?a.tableFilterMethod||a.filterMethod:null,i=a?a.tableFilterDefaultMethod||a.defaultTableFilterMethod||a.defaultFilterMethod:null,s=bl(c,t);return l?r.some(e=>l({value:e.value,option:e,cellValue:s,row:c,column:t,$table:se})):n?r.some(e=>n({value:e.value,option:e,cellValue:s,row:c,column:t,$table:se})):p?p({options:r,values:e,cellValue:s,row:c,column:t}):i?r.some(e=>i({value:e.value,option:e,cellValue:s,row:c,column:t,$table:se})):-1<e.indexOf(al().get(c,t.field))}),e&&d?(x=al().searchTree(l,o,{original:!0,isEvery:!0,children:s.mapChildrenField,mapChildren:c}),g=x):(g=(e?l:r).filter(o),x=g)):e&&d?(x=al().searchTree(l,()=>!0,{original:!0,isEvery:!0,children:s.mapChildrenField,mapChildren:c}),g=x):(g=(e?l:r).slice(0),x=g),!h&&n.length&&(e&&d?(x=m?(i=m({data:x,sortList:n,$table:se}),al().isArray(i)?i:x):al().orderBy(x,n.map(({column:e,order:t})=>[kt(e),t])),g=x):(g=m?(t=m({data:g,sortList:n,$table:se}),al().isArray(t)?t:g):al().orderBy(g,n.map(({column:e,order:t})=>[kt(e),t])),x=g))}B.afterFullData=g,B.afterTreeFullData=x,Ot()})(),o=At());e=t?o.slice(r.startIndex,r.endIndex):o.slice(0);return e.forEach((e,t)=>{e=xl(se,e),e=l[e];e&&(e.$index=t)}),U.tableData=e,(0,dl.nextTick)()},cacheRowMap(s,c){let d=z.treeConfig;var e=ie.value;let{fullAllDataRowIdData:u,tableFullData:t,tableFullTreeData:r}=B,p=e.children||e.childrenField,h=e.hasChild||e.hasChildField,m=gl(se),v=d&&e.lazy,f={},g={};e=(e,t,r,l,o,a)=>{let n=xl(se,e);l=d&&l?l.map((e,t)=>t%2==0?Number(e)+1:".").join(""):t+1,a=a?a.length-1:0;cl(n)&&(n=Al(),al().set(e,m,n)),v&&e[h]&&al().isUndefined(e[p])&&(e[p]=null);let i=u[n];(i=!s&&i?i:{row:e,rowid:n,seq:l,index:-1,_index:-1,$index:-1,treeIndex:t,items:r,parent:o,level:a,height:0,resizeHeight:0,oTop:0}).row=e,i.items=r,i.parent=o,i.level=a,i.index=d&&o?-1:t,c&&(g[n]=i),f[n]=i};c&&(B.fullDataRowIdData=g),B.fullAllDataRowIdData=f,d?al().eachTree(r,e,{children:p}):t.forEach(e)},cacheSourceMap(e){var t=z.treeConfig,r=ie.value;let l=B.sourceDataRowIdData;e=al().clone(e,!0);let o=gl(se);l=B.sourceDataRowIdData={};var a=e=>{let t=xl(se,e);cl(t)&&(t=Al(),al().set(e,o,t)),l[t]=e};t?(t=r.children||r.childrenField,al().eachTree(e,a,{children:r.transform?r.mapChildrenField:t})):e.forEach(a),B.tableSourceData=e},analyColumnWidth(){var e=B.tableFullColumn;let{width:t,minWidth:r}=J.value,l=[],o=[],a=[],n=[],i=[],s=[],c=[],d=[];e.forEach(e=>{t&&!e.width&&(e.width=t),r&&!e.minWidth&&(e.minWidth=r),e.visible&&(e.resizeWidth?l:"auto"===e.width?c:Dl(e.width)?o:Ml(e.width)?i:Dl(e.minWidth)?a:"auto"===e.minWidth?n:Ml(e.minWidth)?s:d).push(e)}),Object.assign(U.columnStore,{resizeList:l,pxList:o,pxMinList:a,autoMinList:n,scaleList:i,scaleMinList:s,autoList:c,remainList:d})},handleColResizeMousedownEvent(e,T,l){e.stopPropagation(),e.preventDefault();var t=l.column,{overflowX:r,scrollbarHeight:o}=U;let{elemStore:a,visibleColumn:n}=B,E=Le.value,R=r?o:0,S=q.value,I=K.value,D=Y.value,M=be.value;if(M){let v=M.firstElementChild,f=Z.value,g=e.clientX,x=q.value;r=e.target;let b=t,C=(t.children&&t.children.length&&al().eachTree(t.children,e=>{b=e}),r.parentNode);o=Object.assign(l,{cell:C});let w=0,y=wl(a["main-body-scroll"]);if(y){var t=Fl(r,x),r=r.clientWidth,i=Math.floor(r/2);let c=Ll(o)-i,d=t.left-C.clientWidth+r+c,u=t.left+i,p="left"===T,h="right"===T,m=0;if(p||h){var s=p?"nextElementSibling":"previousElementSibling";let e=C[s];for(;e&&!ul(e,"fixed--hidden");)ul(e,"col--group")||(m+=e.offsetWidth),e=e[s];h&&D&&(u=D.offsetLeft+m)}o=t=>{t.stopPropagation(),t.preventDefault();var r=S.clientHeight,l=t.clientX-g;let e=u+l;l=T?0:y.scrollLeft,p?e=Math.min(e,(D?D.offsetLeft:y.clientWidth)-m-c):h?(d=(I?I.clientWidth:0)+m+c,e=Math.min(e,u+C.clientWidth-c)):d=Math.max(y.scrollLeft,d),w=Math.max(e,d),l=Math.max(1,w-l);if(M.style.left=l+"px",M.style.top=`${f?R:0}px`,M.style.height=`${f?r-R:r}px`,E.showDragTip&&v){v.textContent=Wr("vxe.table.resizeColTip",[b.renderWidth+(h?u-w:w-u)]);var o=S.clientWidth,a=x.getBoundingClientRect(),n=M.clientWidth,i=v.clientWidth,s=v.clientHeight;let e=-i;l<i+n?e=0:o<l&&(e+=o-l),v.style.left=e+"px",v.style.top=Math.min(r-s,Math.max(0,t.clientY-a.y-s/2))+"px"}U.isDragResize=!0};U.isDragResize=!0,hl(S,"col-drag--resize"),M.style.display="block",document.onmousemove=o,document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null,M.style.display="none",B._lastResizeTime=Date.now(),setTimeout(()=>{U.isDragResize=!1},50);var t=b.renderWidth+(h?u-w:w-u),r={...l,resizeWidth:t,resizeColumn:b};"fixed"===E.dragMode&&n.forEach(e=>{e.id===b.id||e.resizeWidth||(e.resizeWidth=e.renderWidth)}),se.handleColResizeCellAreaEvent?se.handleColResizeCellAreaEvent(e,r):(b.resizeWidth=t,nr(e,r)),pl(S,"col-drag--resize")},o(e),se.closeMenu&&se.closeMenu()}}},handleColResizeDblclickEvent(r,l){var o=Le.value.isDblclickAutoWidth,a=q.value;if(o&&a){r.stopPropagation(),r.preventDefault();var o=B.fullColumnIdData,n=l.column;let t=n;n.children&&n.children.length&&al().eachTree(n.children,e=>{t=e});n=o[t.id],o=r.target.parentNode,o=Ll(Object.assign(l,{cell:o}));a.setAttribute("data-calc-col","Y");let e=Rt(t,a);a.removeAttribute("data-calc-col"),n&&(e=Math.max(e,n.width)),e=Math.max(o,e);a={...l,resizeWidth:e,resizeColumn:t};U.isDragResize=!1,B._lastResizeTime=Date.now(),se.handleColResizeDblclickCellAreaEvent?se.handleColResizeDblclickCellAreaEvent(r,a):(t.resizeWidth=e,nr(r,a))}},handleRowResizeMousedownEvent(l,o){l.stopPropagation(),l.preventDefault();let a=o.row;var{overflowX:n,scrollbarWidth:i,overflowY:m,scrollbarHeight:v}=U,{elemStore:f,fullAllDataRowIdData:g}=B;let x=m?i:0,b=n?v:0,C=Q.value,w=Le.value;m=ee.value,i=R.value;let y=q.value,T=Ce.value;if(T){let p=l.clientY,h=T.firstElementChild;n=l.currentTarget.parentNode,v=n.parentNode;if(wl(f["main-body-scroll"])){let r=g[xl(se,a)];if(r){f=E.value;let s=r.resizeHeight||r.height||i.height||m.height||f,c=y.getBoundingClientRect();g=v.getBoundingClientRect();let e=p-g.y-v.clientHeight,d=s;i=n.querySelector(".vxe-cell");let t=0,u=(i&&(m=getComputedStyle(i),t=Math.max(1,Math.ceil(al().toNumber(m.paddingTop)+al().toNumber(m.paddingBottom)))),g.y-c.y+t);f=r=>{r.stopPropagation(),r.preventDefault();var l=y.clientWidth-x,o=y.clientHeight-b;let a=r.clientY-c.y-e;if(a<u?a=u:d=Math.max(t,s+r.clientY-p),T.style.left=`${C?x:0}px`,T.style.top=a+"px",T.style.width=l+"px",w.showDragTip&&h){h.textContent=Wr("vxe.table.resizeRowTip",[d]);var n=h.clientWidth,i=h.clientHeight;let e=Math.max(2,r.clientX-c.x),t=0;e+n>=l-2&&(e=l-n-2),a+i>=o&&(t=o-(a+i)),h.style.left=e+"px",h.style.top=t+"px"}U.isDragResize=!0};U.isDragResize=!0,hl(y,"row-drag--resize"),T.style.display="block",document.onmousemove=f,document.onmouseup=function(e){var t;document.onmousemove=null,document.onmouseup=null,T.style.display="none",B._lastResizeTime=Date.now(),setTimeout(()=>{U.isDragResize=!1},50),d!==s&&(t={...o,resizeHeight:d,resizeRow:a},B.isResizeCellHeight=!0,se.handleRowResizeCellAreaEvent?se.handleRowResizeCellAreaEvent(e,t):(r.resizeHeight=d,ir(e,t))),pl(y,"row-drag--resize")},f(l)}}}},handleRowResizeDblclickEvent(o,a){var e=Le.value.isDblclickAutoHeight;let n=q.value;if(e&&n){o.stopPropagation(),o.preventDefault();var e=U.editStore,t=B.fullAllDataRowIdData,e=e.actived;let r=a.row,l=t[xl(se,r)];l&&(t=()=>{n.setAttribute("data-calc-row","Y");var e=Dt(l,n),t=(n.removeAttribute("data-calc-row"),{...a,resizeHeight:e,resizeRow:r});U.isDragResize=!1,B._lastResizeTime=Date.now(),se.handleRowResizeDblclickCellAreaEvent?se.handleRowResizeDblclickCellAreaEvent(o,t):(l.resizeHeight=e,ir(o,t))},e.row||e.column?se.clearEdit().then(t):t())}},saveCustomStore(e){var t=z.customConfig,r=A.value,l=T.value,{updateStore:o,storage:a}=l,n=!0===a,a=n?{}:Object.assign({},a||{}),i=n||a.resizable,s=n||a.visible,c=n||a.fixed,n=n||a.sort;if("reset"!==e&&(U.isCustomStatus=!0),(t?nl(l):l.enabled)&&(i||s||c||n)){if(!r)return Br("vxe.error.reqProp",["id"]),(0,dl.nextTick)();a="reset"===e?{resizableData:{},sortData:{},visibleData:{},fixedData:{}}:P.getCustomStoreData();if(o)return o({id:r,type:e,storeData:a});t=r,l="reset"===e?null:a,i=Ur().version,(s=ut())[t]=l||void 0,s._v=i,localStorage.setItem(ll,al().toJSONString(s))}return(0,dl.nextTick)()},handleCustom(){var e=z.mouseConfig;return e&&(se.clearSelected&&se.clearSelected(),se.clearCellAreas)&&(se.clearCellAreas(),se.clearCopyCellArea()),W.analyColumnWidth(),P.refreshColumn(!0)},handleUpdateDataQueue(){U.upDataFlag++},handleRefreshColumnQueue(){U.reColumnFlag++},preventEvent(t,e,r,l,o){let a=el.get(e);a.length||"event.clearEdit"!==e||(a=el.get("event.clearActived")).length&&Pr("vxe.error.delEvent",["event.clearActived","event.clearEdit"]);let n;return a.some(e=>!1===e(Object.assign({$grid:G,$table:se,$event:t},r)))||l&&(n=l()),o&&o(),n},updateCheckboxStatus(){var e=z.treeConfig,{selectCheckboxMaps:t,treeIndeterminateMaps:r}=U;let i=Object.assign({},t),s=Object.assign({},r);t=ie.value;let{transform:a,mapChildrenField:n}=t,c=t.children||t.childrenField,{checkField:d,checkStrictly:l,checkMethod:u}=le.value;r=B.afterTreeFullData;if(!l){if(e){let l={},o=[];al().eachTree(r,e=>{var t=xl(se,e),r=e[a?n:c];r&&r.length&&!l[t]&&(l[t]=1,o.unshift([e,t,r]))},{children:a?n:c}),o.forEach(e=>{var t=e[0],r=e[1];let l=0,o=0,a=0;e[2].forEach(u?e=>{var t=xl(se,e),r=d?al().get(e,d):i[t];u({row:e})?(r?l++:s[t]&&o++,a++):r?l++:s[t]&&o++}:e=>{var t=xl(se,e);(d?al().get(e,d):i[t])?l++:s[t]&&o++,a++});var e=l>=a,n=!e&&(1<=l||1<=o);d&&al().get(t,d,e),e?(d||(i[r]=t),s[r]&&delete s[r]):(d||i[r]&&delete i[r],n?s[r]=t:s[r]&&delete s[r])})}U.selectCheckboxMaps=i,U.treeIndeterminateMaps=s}},updateAllCheckboxStatus(){var e=z.treeConfig;let{selectCheckboxMaps:l,treeIndeterminateMaps:o}=U,{checkField:a,checkMethod:n}=le.value;var{afterFullData:t,afterTreeFullData:r}=B;let i=0,s=0,c=0;e=e?r:t,e.forEach(n?e=>{var t=xl(se,e),r=a?al().get(e,a):l[t];n({row:e})?(r?i++:o[t]&&s++,c++):r?i++:o[t]&&s++}:e=>{var t=xl(se,e);(a?al().get(e,a):l[t])?i++:o[t]&&s++,c++}),r=0<c?i>=c:i>=e.length,t=!r&&(1<=i||1<=s);U.isAllSelected=r,U.isIndeterminate=t},checkSelectionStatus(){se.updateCheckboxStatus(),se.updateAllCheckboxStatus()},handleBatchSelectRows(e,r,l){var t=z.treeConfig,o=U.selectCheckboxMaps;let a=Object.assign({},o);var o=ie.value,{transform:n,mapChildrenField:i}=o,o=o.children||o.childrenField,s=le.value;let{checkField:c,checkStrictly:d,checkMethod:u}=s,p=s.indeterminateField||s.halfField;c?t&&!d?al().eachTree(e,e=>{!l&&u&&!u({row:e})||(al().set(e,c,r),p&&al().set(e,p,!1),_t(e,r))},{children:n?i:o}):e.forEach(e=>{!l&&u&&!u({row:e})||(al().set(e,c,r),_t(e,r))}):t&&!d?al().eachTree(e,e=>{var t=xl(se,e);!l&&u&&!u({row:e})||(r?a[t]=e:a[t]&&delete a[t],_t(e,r))},{children:n?i:o}):e.forEach(e=>{var t=xl(se,e);!l&&u&&!u({row:e})||(r?a[t]||(a[t]=e):a[t]&&delete a[t],_t(e,r))}),U.selectCheckboxMaps=a},handleSelectRow({row:e},t,r){se.handleBatchSelectRows([e],t,r)},triggerHeaderTitleEvent(r,e,l){var o=e.content||e.message;if(o){var a=U.tooltipStore,l=l.column;let t=il(o);gr(!0),a.row=null,a.column=l,a.visible=!0,a.currOpts=e,(0,dl.nextTick)(()=>{var e=X.value;e&&e.open&&e.open(r.currentTarget,t)})}},triggerHeaderTooltipEvent(e,t){var r,l=U.tooltipStore,o=t.column,e=(gr(!0),e.currentTarget);!e||!(e=e.parentElement)||!(r=e.parentElement)||l.column===o&&l.visible||Tr(0,r,e,null,t)},triggerBodyTooltipEvent(e,t){var r=z.editConfig,l=U.editStore,o=U.tooltipStore,a=oe.value,l=l.actived,{row:n,column:i}=t,e=e.currentTarget;if(gr(o.column!==i||o.row!==n),i.editRender&&nl(r)){if("row"===a.mode&&l.row===n)return;if(l.row===n&&l.column===i)return}o.column===i&&o.row===n&&o.visible||Tr(0,e,e.querySelector(".vxe-cell--wrapper"),null,t)},triggerFooterTooltipEvent(e,t){var r=t.column,l=U.tooltipStore,e=e.currentTarget;gr(l.column!==r||!!l.row),l.column===r&&l.visible||Tr(0,e,e.querySelector(".vxe-cell--wrapper")||e.children[0],null,t)},handleTargetLeaveEvent(){var e=N.value;let t=X.value;t&&t.setActived&&t.setActived(!1),e.enterable?B.tooltipTimeout=setTimeout(()=>{(t=X.value)&&t.isActived&&!t.isActived()&&se.closeTooltip()},e.leaveDelay):se.closeTooltip()},triggerHeaderCellClickEvent(e,t){var r=B._lastResizeTime,l=C.value,o=J.value,a=t.column,n=e.currentTarget,r=r&&r>Date.now()-300,i=fl(e,n,"vxe-cell--sort").flag,s=fl(e,n,"vxe-cell--filter").flag;"cell"!==l.trigger||r||i||s||W.triggerSortEvent(e,a,dt(a)),ce("header-cell-click",Object.assign({triggerResizable:r,triggerSort:i,triggerFilter:s,cell:n},t),e),(o.isCurrent||z.highlightCurrentColumn)&&W.triggerCurrentColumnEvent(e,t)},triggerHeaderCellDblclickEvent(e,t){ce("header-cell-dblclick",Object.assign({cell:e.currentTarget},t),e)},triggerCellClickEvent(e,t){var r,l,o,a,n,i,s,c,d,u,p,h,m,v,f,g,x,b,{highlightCurrentRow:C,editConfig:w}=z,{editStore:y,isDragResize:T}=U;T||(T=M.value,r=oe.value,l=ie.value,o=D.value,a=le.value,n=ne.value,i=ee.value,{actived:y,focused:s}=y,{row:c,column:d}=t,{type:p,treeNode:u}=d,h="checkbox"===p,m="expand"===p,v=e.currentTarget,f=(p="radio"===p)&&fl(e,v,"vxe-cell--radio").flag,g=h&&fl(e,v,"vxe-cell--checkbox").flag,x=u&&fl(e,v,"vxe-tree--btn-wrapper").flag,b=m&&fl(e,v,"vxe-table--expanded").flag,t=Object.assign({cell:v,triggerRadio:f,triggerCheckbox:g,triggerTreeNode:x,triggerExpandNode:b},t),!g&&!f&&(!b&&("row"===T.trigger||m&&"cell"===T.trigger)&&W.triggerRowExpandEvent(e,t),"row"===l.trigger||u&&"cell"===l.trigger)&&W.triggerTreeExpandEvent(e,t),x||(b||(!i.isCurrent&&!C||g||f||W.triggerCurrentRowEvent(e,t),!f&&("row"===o.trigger||p&&"cell"===o.trigger)&&W.triggerRadioRowEvent(e,t),!g&&("row"===a.trigger||h&&"cell"===a.trigger)&&W.handleToggleCheckRowEvent(e,t)),nl(w)&&(n.arrowCursorLock&&e&&"cell"===r.mode&&e.target&&/^input|textarea$/i.test(e.target.tagName)&&(s.column=d,s.row=c),"manual"===r.trigger?y.args&&y.row===c&&d!==y.column&&Vt(e,t):y.args&&c===y.row&&d===y.column||("click"===r.trigger||"dblclick"===r.trigger&&"row"===r.mode&&y.row===c)&&Vt(e,t))),nl(w)&&"dblclick"===r.trigger&&y.row&&y.column&&("row"===r.mode?se.eqRow(y.row,c)||se.handleClearEdit(e):"cell"!==r.mode||se.eqRow(y.row,c)&&y.column.id===d.id||se.handleClearEdit(e)),ce("cell-click",t,e))},triggerCellDblclickEvent(e,t){var r,l=z.editConfig,{editStore:o,isDragResize:a}=U;a||(a=oe.value,o=o.actived,r=e.currentTarget,t=Object.assign({cell:r},t),!nl(l)||"dblclick"!==a.trigger||o.args&&e.currentTarget===o.args.cell||("row"===a.mode?Lt("blur").catch(e=>e).then(()=>{se.handleEdit(t,e).then(()=>Lt("change")).catch(e=>e)}):"cell"===a.mode&&se.handleEdit(t,e).then(()=>Lt("change")).catch(e=>e)),ce("cell-dblclick",t,e))},handleToggleCheckRowEvent(t,r){var l=U.selectCheckboxMaps,{checkField:o,trigger:e}=le.value,a=r.row;if("manual"!==e){let e=!1;e=o?!al().get(a,o):!l[xl(se,a)],t?W.triggerCheckRowEvent(t,r,e):(W.handleBatchSelectRows([a],e),W.checkSelectionStatus())}},triggerCheckRowEvent(e,t,r){var l=le.value,o=t.row,a=B.afterFullData,{checkMethod:n,trigger:i}=l;if("manual"!==i){if(e.stopPropagation(),l.isShiftKey&&e.shiftKey&&!z.treeConfig){i=P.getCheckboxRecords();if(i.length){l=i[0],i=P.getVTRowIndex(o),l=P.getVTRowIndex(l);if(i!==l)return P.setAllCheckboxRow(!1),a=i<l?a.slice(i,l+1):a.slice(l,i+1),x(a,!0,!1),void ce("checkbox-range-select",Object.assign({rangeRecords:a},t),e)}}n&&!n({row:o})||(W.handleBatchSelectRows([o],r),W.checkSelectionStatus(),ce("checkbox-change",Object.assign({records:P.getCheckboxRecords(),reserves:P.getCheckboxReserveRecords(),indeterminates:P.getCheckboxIndeterminateRecords(),checked:r},t),e))}},triggerCheckAllEvent(e,t){var r=le.value.trigger;"manual"!==r&&(e&&e.stopPropagation(),lr(e,t))},triggerRadioRowEvent(r,l){var o=U.selectRadioRow,a=l.row,n=D.value,e=n.trigger;if("manual"!==e){r.stopPropagation();let e=a,t=o!==e;t?Ht(e):n.strict||(t=o===e)&&(e=null,P.clearRadioRow()),t&&ce("radio-change",{oldValue:o,newValue:e,...l},r)}},triggerCurrentColumnEvent(e,t){var r=J.value.currentMethod,t=t.column;r&&!r({column:t})||P.setCurrentColumn(t)},triggerCurrentRowEvent(e,t){var r=U.currentRow,l=ee.value.currentMethod,o=t.row,a=r!==o;l&&!l({row:o})||(P.setCurrentRow(o),a&&ce("current-change",{oldValue:r,newValue:o,...t},e))},triggerRowExpandEvent(e,t){var{rowExpandLazyLoadedMaps:r,expandColumn:l}=U,t=t.row,{lazy:o,trigger:a}=M.value;"manual"===a||(e.stopPropagation(),a=xl(se,t),o&&r[a])||(o=!P.isRowExpandByRow(t),r=P.getColumnIndex(l),a=P.getVMColumnIndex(l),P.setRowExpand(t,o),ce("toggle-row-expand",{expanded:o,column:l,columnIndex:r,$columnIndex:a,row:t,rowIndex:P.getRowIndex(t),$rowIndex:P.getVMRowIndex(t)},e))},triggerTreeExpandEvent(e,t){var r=U.treeExpandLazyLoadedMaps,{row:t,column:l}=t,{lazy:o,trigger:a}=ie.value;"manual"===a||(e.stopPropagation(),a=xl(se,t),o&&r[a])||(o=!P.isTreeExpandByRow(t),r=P.getColumnIndex(l),a=P.getVMColumnIndex(l),P.setTreeExpand(t,o),ce("toggle-tree-expand",{expanded:o,column:l,columnIndex:r,$columnIndex:a,row:t},e))},handleColumnSortEvent(e,t){var r=z.mouseConfig,l=ae.value,{field:o,sortable:a}=t;a&&(a={$table:se,$event:e,column:t,field:o,property:o,order:t.order,sortList:P.getSortColumns(),sortTime:t.sortTime},r&&l.area&&se.handleSortEvent&&se.handleSortEvent(e,a),ce("sort-change",a,e))},triggerSortEvent(e,t,r){var{multiple:l,allowClear:o}=C.value,{field:a,sortable:n}=t;n&&(r&&t.order!==r?P.sort({field:a,order:r}):o&&P.clearSort(l?t:null),se.handleColumnSortEvent(e,t))},triggerHeaderCellMousedownEvent(e,t){var r=z.mouseConfig,l=ae.value,o=J.value,{trigger:a,isCrossDrag:n,isPeerDrag:i,disabledMethod:s}=re.value,c=e.currentTarget,d=c&&c.tagName&&"input"===c.tagName.toLowerCase(),u=fl(e,c,"vxe-cell--checkbox").flag,p=fl(e,c,"vxe-cell--sort").flag,h=fl(e,c,"vxe-cell--filter").flag;let m=!1;o=o.drag&&"cell"===a;d||u||p||h||(a=t.column,!o)||a.fixed||!n&&!i&&a.parentId||s&&s(t)||(m=!0,se.handleHeaderCellDragMousedownEvent(e,t)),!m&&r&&l.area&&se.handleHeaderCellAreaEvent&&se.handleHeaderCellAreaEvent(e,Object.assign({cell:c,triggerSort:p,triggerFilter:h},t)),se.focus(),se.closeMenu&&se.closeMenu()},triggerCellMousedownEvent(e,t){var r=t.column,{type:l,treeNode:o}=r,a="radio"===l,n="checkbox"===l,l="expand"===l,i=ee.value,{trigger:s,isCrossDrag:c,isPeerDrag:d,disabledMethod:u}=te.value,p=e.currentTarget,h=(t.cell=p)&&p.tagName&&"input"===p.tagName.toLowerCase(),a=a&&fl(e,p,"vxe-cell--radio").flag,n=n&&fl(e,p,"vxe-cell--checkbox").flag,o=o&&fl(e,p,"vxe-tree--btn-wrapper").flag,l=l&&fl(e,p,"vxe-table--expanded").flag;let m=!1,v=(i.drag&&(m="row"===s||r.dragSort&&"cell"===s),!1);h||a||n||o||l||!m||!c&&!d&&t.level||u&&u(t)||(v=!0,se.handleCellDragMousedownEvent(e,t)),!v&&se.handleCellMousedownEvent&&se.handleCellMousedownEvent(e,t),se.focus(),se.closeFilter(),se.closeMenu&&se.closeMenu()},triggerCellMouseupEvent(){xr()},handleRowDragDragstartEvent(e){e.dataTransfer&&e.dataTransfer.setDragImage(Il(),0,0)},handleRowDragSwapEvent(s,e,c,d,u,t){let{treeConfig:p,dragConfig:r}=z;var l=te.value;let h=B.fullAllDataRowIdData,{isPeerDrag:m,isCrossDrag:v,isSelfToChildDrag:f,dragEndMethod:o,dragToChildMethod:a}=l;l=ie.value;let{transform:g,rowField:x,mapChildrenField:b,parentField:C}=l,w=l.children||l.childrenField,{afterFullData:y,tableFullData:T}=B;l=o||(r?r.dragEndMethod:null);let E="bottom"===u?1:0;if(d&&c&&d!==c){var n={oldRow:c,newRow:d,dragPos:u,dragToChild:!!t,offsetIndex:E};let i=f&&a?a(n):t;return Promise.resolve(!l||l(n)).then(r=>{if(r){let e=-1,t=-1;if(p){if(g){var r=xl(se,c),r=h[r],l=xl(se,d),o=h[l];if(r&&o){var a=r.level,n=o.level;let t={},e=(al().eachTree([c],e=>{t[xl(se,e)]=e},{children:b}),!1);if(a&&n)if(m&&!v){if(r.row[C]!==o.row[C])return}else{if(!v)return;if(t[l]&&(e=!0,!v||!f))return void(ol.VxeUI.modal&&ol.VxeUI.modal.message({status:"error",content:Wr("vxe.error.treeDragChild")}))}else if(a){if(!v)return}else if(n){if(!v)return;if(t[l]&&(e=!0,!v||!f))return void(ol.VxeUI.modal&&ol.VxeUI.modal.message({status:"error",content:Wr("vxe.error.treeDragChild")}))}r=al().toTreeArray(B.afterTreeFullData,{key:x,parentKey:C,children:b}),o=se.findRowIndexOf(r,c),a=(r.splice(o,1),se.findRowIndexOf(r,d)),n=a+E;r.splice(n,0,c),e&&v&&f&&al().each(c[w],e=>{e[C]=c[C]}),c[C]=i?d[x]:d[C],B.tableFullTreeData=al().toArrayTree(r,{key:x,parentKey:C,children:w,mapChildren:b})}}}else{e=se.findRowIndexOf(y,c);l=se.findRowIndexOf(T,c),o=(y.splice(e,1),T.splice(l,1),se.findRowIndexOf(y,d)),a=se.findRowIndexOf(T,d),n=(t=o+E,a+E);y.splice(t,0,c),T.splice(n,0,c)}U.isDragRowMove=!0,se.handleTableData(p&&g),se.cacheRowMap(!1),tr(),p&&g||se.updateAfterDataIndex(),se.checkSelectionStatus(),U.scrollYLoad&&se.updateScrollYSpace(),(0,dl.nextTick)().then(()=>{se.updateCellAreas(),se.recalculate()}),ce("row-dragend",{oldRow:c,newRow:d,dragPos:u,dragToChild:i,offsetIndex:E,_index:{newIndex:t,oldIndex:e}},s)}}).catch(()=>{})}return Promise.resolve()},handleRowDragDragendEvent(e){var t=z.treeConfig,{fullAllDataRowIdData:r,prevDragToChild:l}=B,o=U.dragRow,a=ie.value,n=a.lazy,a=a.hasChild||a.hasChildField,{prevDragRow:i,prevDragPos:s}=B;t&&n&&l&&(t=r[xl(se,i)],i[a])&&(!t||!t.treeLoaded)||se.handleRowDragSwapEvent(e,!0,o,i,s,l),yr(),br(),B.prevDragToChild=!1,U.dragRow=null,U.dragCol=null,setTimeout(()=>{U.isDragRowMove=!1},500)},handleRowDragDragoverEvent(r){var l=z.treeConfig,o=B.fullAllDataRowIdData;let e=U.dragRow;var a=ie.value,{lazy:n,transform:i,parentField:s}=a,a=a.hasChild||a.hasChildField,{isPeerDrag:c,isCrossDrag:d,isToChildDrag:u}=te.value;if(e){var p=r.ctrlKey,h=r.currentTarget;let e=h.getAttribute("rowid")||"";var m=o[e];if(m){var v=m.row;let e=xl(se,v);o=o[e];r.preventDefault();let t=U.dragRow;var f=r.clientY-h.getBoundingClientRect().y<h.clientHeight/2?"top":"bottom";B.prevDragToChild=!!(l&&i&&d&&u&&p),B.prevDragRow=v,B.prevDragPos=f,se.eqRow(t,v)||p&&l&&n&&v[a]&&o&&!o.treeLoaded||!d&&l&&i&&(c?t[s]!==v[s]:m.level)?wr(r,h,null,!1,f):(wr(r,h,null,!0,f),ce("row-dragover",{oldRow:t,targetRow:v,dragPos:f},r))}}else r.preventDefault()},handleCellDragMousedownEvent(e,t){e.stopPropagation();var r=z.dragConfig,{trigger:l,dragStartMethod:o}=te.value,a=t.row,n=e.currentTarget,l="cell"===l||"row"===l?n:n.parentElement?.parentElement,n=l.parentElement,o=o||(r?r.dragStartMethod:null);br(),o&&!o(t)?(n.draggable=!1,U.dragRow=null,U.dragCol=null,yr()):(U.dragRow=a,U.dragCol=null,n.draggable=!0,(e=>{var t=q.value;if(t){e=xl(se,e);al().arrayEach(t.querySelectorAll(`[rowid="${e}"]`),e=>{hl(e,"row--drag-origin")})}})(a),(e=>{var t=z.dragConfig,r=U.dragRow,l=te.value.tooltipMethod,l=l||(t?t.rowTooltipMethod:null);let o="";o=l?""+(l({row:r})||""):Wr("vxe.table.dragTip",[e.textContent||""]),U.dragTipText=o})(l),ce("row-dragstart",t,e))},handleCellDragMouseupEvent(){xr()},handleHeaderCellDragDragstartEvent(e){e.dataTransfer&&e.dataTransfer.setDragImage(Il(),0,0)},handleColDragSwapColumn(){Gt(),Jt(!1).then(()=>{se.updateCellAreas(),se.saveCustomStore("update:sort")})},handleColDragSwapEvent(u,p,e,t,h,r){let m=z.mouseConfig;let{isPeerDrag:v,isCrossDrag:f,isSelfToChildDrag:g,isToChildDrag:x,dragEndMethod:l,dragToChildMethod:o}=re.value,b=B.collectColumn,C="right"===h?1:0;if(t&&e&&t!==e){let s=e,c=t;e={oldColumn:s,newColumn:c,dragPos:h,dragToChild:!!r,offsetIndex:C};let d=g&&o?o(e):r;return Promise.resolve(!l||l(e)).then(o=>{if(o){let e=-1,t=-1,r={},l=(al().eachTree([s],e=>{r[e.id]=e}),!1);if(s.parentId&&c.parentId)if(v&&!f){if(s.parentId!==c.parentId)return}else{if(!f)return;if(r[c.id]&&(l=!0,!f||!g))return void(ol.VxeUI.modal&&ol.VxeUI.modal.message({status:"error",content:Wr("vxe.error.treeDragChild")}))}else if(s.parentId){if(!f)return}else if(c.parentId){if(!f)return;if(r[c.id]&&(l=!0,!f||!g))return void(ol.VxeUI.modal&&ol.VxeUI.modal.message({status:"error",content:Wr("vxe.error.treeDragChild")}))}var a,n,i,o=al().findTree(b,e=>e.id===s.id),o=(l&&f&&g?o&&({items:a,index:i}=o,(n=s.children||[]).forEach(e=>{e.parentId=s.parentId}),a.splice(i,1,...n),s.children=[]):o&&({items:a,index:i,parent:n}=o,a.splice(i,1),n||(e=i)),al().findTree(b,e=>e.id===c.id));o&&({items:a,index:n,parent:i}=o,f&&x&&d?(s.parentId=c.id,c.children=(c.children||[]).concat([s])):(s.parentId=c.parentId,a.splice(n+C,0,s)),i||(t=n)),al().eachTree(b,(e,t,r,l,o)=>{o||(e.renderSortNumber=t+1)}),U.isDragColMove=!0,m&&(se.clearSelected&&se.clearSelected(),se.clearCellAreas)&&(se.clearCellAreas(),se.clearCopyCellArea()),ce("column-dragend",{oldColumn:s,newColumn:c,dragPos:h,dragToChild:d,offsetIndex:C,_index:{newIndex:t,oldIndex:e}},u),p&&se.handleColDragSwapColumn()}}).catch(()=>{})}return Promise.resolve()},handleHeaderCellDragDragendEvent(e){var t=U.dragCol,{prevDragCol:r,prevDragPos:l,prevDragToChild:o}=B;se.handleColDragSwapEvent(e,!0,t,r,l,o),yr(),Cr(),B.prevDragToChild=!1,U.dragRow=null,U.dragCol=null,setTimeout(()=>{U.isDragColMove=!1,se.recalculate().then(()=>{Zt()})},500)},handleHeaderCellDragDragoverEvent(e){var t,r,l,o,a,n=U.dragCol,{isToChildDrag:i,isPeerDrag:s,isCrossDrag:c}=re.value;n?(a=e.ctrlKey,l=(r=e.currentTarget).getAttribute("colid"),(l=se.getColumnById(l))&&(e.preventDefault(),t=e.clientX,o=t-r.getBoundingClientRect().x<r.clientWidth/2?"left":"right",B.prevDragToChild=!!(c&&i&&a),B.prevDragCol=l,B.prevDragPos=o,l.fixed||n&&n.id===l.id||!c&&(s?n.parentId!==l.parentId:l.parentId)?wr(e,null,r,!1,o):(wr(e,null,r,!0,o),ce("column-dragover",{oldColumn:n,targetColumn:l,dragPos:o},e),(i=q.value)&&(a=S.value,s=(c=b.value)?c.$el:null,r=a||s)&&(n=i.getBoundingClientRect(),l=i.clientWidth,c=(o=K.value)?o.clientWidth:0,s=(a=Y.value)?a.clientWidth:0,i=t-(n.x+c),o=n.x+l-s-t,0<i&&i<=28?(a=Math.floor(l/(14<i?240:120)),r.scrollLeft-=a*(28-i)):0<o&&o<=28&&(c=Math.floor(l/(14<o?240:120)),r.scrollLeft+=c*(28-o)))))):e.preventDefault()},handleHeaderCellDragMousedownEvent(e,t){e.stopPropagation();var{trigger:r,dragStartMethod:l}=re.value,o=t.column,a=e.currentTarget,r="cell"===r?a:a.parentElement?.parentElement;U.isDragColMove=!1,Cr(),l&&!l(t)?(r.draggable=!1,U.dragRow=null,U.dragCol=null,yr()):(U.dragCol=o,U.dragRow=null,r.draggable=!0,(e=>{var r=q.value;if(r){let t=[];al().eachTree([e],e=>{t.push(`[colid="${e.id}"]`)});al().arrayEach(r.querySelectorAll(t.join(",")),e=>{hl(e,"col--drag-origin")})}})(o),(e=>{var t=U.dragCol,r=re.value.tooltipMethod;let l="";l=r?""+(r({column:t})||""):Wr("vxe.table.dragTip",[e.textContent||""]),U.dragTipText=l})(r),ce("column-dragstart",t,e))},handleHeaderCellDragMouseupEvent(){Cr(),yr(),U.dragRow=null,U.dragCol=null,U.isDragColMove=!1},handleScrollEvent(c,d,u,p,h,m){var v=z.highlightHoverRow,{lastScrollLeft:f,lastScrollTop:g}=B,x=S.value,b=I.value;if(x&&b){var C=ee.value,w=ve.value,y=X.value,T=b.clientHeight,E=x.clientWidth,b=b.scrollHeight,x=x.scrollWidth;let e=!1,t=!1,r=!1,l=!1,o="",a=!1,n=!1,i=!1,s=!1;u&&(R=V.value,(r=h<=0)||(l=x<=h+E),f<h?(o="right",x-R<=h+E&&(s=!0)):(o="left",h<=R&&(i=!0)),se.checkScrolling(),B.lastScrollLeft=h),d&&(f=_.value,(e=p<=0)||(t=b<=p+T),g<p?(o="bottom",b-f<=p+T&&(n=!0)):(o="top",p<=f&&(a=!0)),B.lastScrollTop=p),U.isDragColMove=!1,U.isDragRowMove=!1,U.lastScrollTime=Date.now();var R={scrollTop:p,scrollLeft:h,bodyHeight:T,bodyWidth:E,scrollHeight:b,scrollWidth:x,isX:u,isY:d,isTop:e,isBottom:t,isLeft:r,isRight:l,direction:o,...m};((e,t)=>{let{scrollXLoad:r,scrollYLoad:l}=U;var o=B.lcsTimeout;o&&clearTimeout(o),B.lcsTimeout=setTimeout(()=>{B.lcsRunTime=Date.now(),B.lcsTimeout=void 0,B.intoRunScroll=!1,B.inVirtualScroll=!1,B.inWheelScroll=!1,B.inHeaderScroll=!1,B.inBodyScroll=!1,B.inFooterScroll=!1,B.scrollRenderType="",Mt(),e&&r&&se.updateScrollXData(),t&&l&&se.updateScrollYData().then(()=>{Mt(),se.updateScrollYSpace()}),se.updateCellAreas()},200)})(u,d),(C.isHover||v)&&se.clearHoverRow(),w&&w.reactData.visible&&w.close(),y&&y.reactData.visible&&y.close(),(n||a||s||i)&&ce("scroll-boundary",R,c),ce("scroll",R,c)}},triggerScrollXEvent(){(L.value.immediate?Zt:()=>{var{lxTimeout:e,lxRunTime:t,scrollXStore:r}=B,r=r.visibleSize,r=Math.max(5,Math.min(10,Math.floor(r/3)));e&&clearTimeout(e),(!t||t+r<Date.now())&&(B.lxRunTime=Date.now(),Zt()),B.lxTimeout=setTimeout(()=>{B.lxTimeout=void 0,B.lxRunTime=void 0,Zt()},r)})()},triggerScrollYEvent(){(f.value.immediate?or:()=>{var{lyTimeout:e,lyRunTime:t,scrollYStore:r}=B,r=r.visibleSize,r=Math.floor(Math.max(4,Math.min(10,r/3)));e&&clearTimeout(e),(!t||t+r<Date.now())&&(B.lyRunTime=Date.now(),or()),B.lyTimeout=setTimeout(()=>{B.lyTimeout=void 0,B.lyRunTime=void 0,or()},r)})()},triggerBodyScrollEvent(r,l){var{elemStore:o,intoRunScroll:e,lastScrollTop:a,lastScrollLeft:n,inWheelScroll:i,inVirtualScroll:s,inHeaderScroll:t,inBodyScroll:c,scrollRenderType:d,inFooterScroll:u}=B,p=S.value,h=I.value,m=wl(o["left-body-scroll"]),v=wl(o["main-body-scroll"]),f=wl(o["right-body-scroll"]),g=wl(o["main-header-scroll"]),o=wl(o["main-footer-scroll"]);if(!(i||s||t||u)&&!e&&v&&p&&h&&(!c||d===l)){let e=h.scrollTop,t=p.scrollLeft;m&&"left"===l?e=m.scrollTop:f&&"right"===l?e=f.scrollTop:(e=v.scrollTop,t=v.scrollLeft);i=t!==n,s=e!==a;B.inBodyScroll=!0,B.scrollRenderType=l,s&&("left"===l?(ml(v,e),ml(f,e)):"right"===l?(ml(v,e),ml(m,e)):(ml(m,e),ml(f,e)),ml(h,e),se.triggerScrollYEvent(r)),i&&(vl(p,t),vl(g,t),vl(o,t),se.triggerScrollXEvent(r)),se.handleScrollEvent(r,s,i,e,t,{type:"body",fixed:l})}},triggerHeaderScrollEvent(e,t){var{elemStore:r,intoRunScroll:l,inWheelScroll:o,inVirtualScroll:a,inBodyScroll:n,inFooterScroll:i}=B,s=I.value,c=S.value,d=wl(r["main-body-scroll"]),u=wl(r["main-header-scroll"]),r=wl(r["main-footer-scroll"]);o||a||n||i||l||u&&c&&s&&(o=s.scrollTop,a=u.scrollLeft,B.inHeaderScroll=!0,vl(c,a),vl(r,a),vl(d,a),se.triggerScrollXEvent(e),se.handleScrollEvent(e,!1,!0,o,a,{type:"header",fixed:t}))},triggerFooterScrollEvent(e,t){var{elemStore:r,intoRunScroll:l,inWheelScroll:o,inVirtualScroll:a,inHeaderScroll:n,inBodyScroll:i}=B,s=I.value,c=S.value,d=wl(r["main-body-scroll"]),u=wl(r["main-header-scroll"]),r=wl(r["main-footer-scroll"]);o||a||n||i||l||r&&c&&s&&(o=s.scrollTop,a=r.scrollLeft,B.inFooterScroll=!0,vl(c,a),vl(u,a),vl(d,a),se.triggerScrollXEvent(e),se.handleScrollEvent(e,!1,!0,o,a,{type:"footer",fixed:t}))},triggerBodyWheelEvent(s){var{target:e,deltaY:c,deltaX:d}=s;if(!e||!/^textarea$/i.test(e.tagName)){var e=zl.highlightHoverRow,{elemStore:t,lastScrollTop:u,lastScrollLeft:p}=B,h=ee.value,m=S.value;let o=I.value,a=wl(t["left-body-scroll"]),n=wl(t["main-body-scroll"]),i=wl(t["right-body-scroll"]);if(m&&o&&n){t=(e=>{let t=1;var r=Date.now();return r<e+25?t=1.18:r<e+30?t=1.15:r<e+40?t=1.12:r<e+55?t=1.09:r<e+75?t=1.06:r<e+100&&(t=1.03),t})(U.lastScrollTime),m=c*t,c=d*t,d=m<0,t=n.scrollTop;if(!(d?t<=0:t>=n.scrollHeight-n.clientHeight)){d=n.scrollTop+m;let t=n.scrollLeft+c,r=t!==p,l=d!==u;l&&(s.preventDefault(),(h.isHover||e)&&se.clearHoverRow(),((r,l)=>{let o=Math.abs(r),a=performance.now(),n=0,i=e=>{let t=(e-a)/o;1<t&&(t=1);e=Math.pow(t,2),e=Math.floor(r*e)-n;n+=e,l(e),t<1&&requestAnimationFrame(i)};requestAnimationFrame(i)})(d-n.scrollTop,e=>{e=n.scrollTop+e;B.inWheelScroll=!0,ml(o,e),ml(n,e),ml(a,e),ml(i,e),se.triggerScrollYEvent(s),se.handleScrollEvent(s,l,r,e,t,{type:"table",fixed:""})}))}}}},triggerVirtualScrollXEvent(t){var{elemStore:r,inWheelScroll:l,lastScrollTop:o,inHeaderScroll:a,inBodyScroll:n,inFooterScroll:i}=B;if(!(a||n||i||l)){a=wl(r["main-header-scroll"]),n=wl(r["main-body-scroll"]),i=wl(r["main-footer-scroll"]),l=I.value,r=t.currentTarget.scrollLeft,l=l||n;let e=0;l=(e=l?l.scrollTop:e)!==o;B.inVirtualScroll=!0,vl(n,r),vl(a,r),vl(i,r),se.triggerScrollXEvent(t),se.handleScrollEvent(t,l,!0,e,r,{type:"table",fixed:""})}},triggerVirtualScrollYEvent(t){var{elemStore:r,inWheelScroll:l,lastScrollLeft:o,inHeaderScroll:a,inBodyScroll:n,inFooterScroll:i}=B;if(!(a||n||i||l)){a=wl(r["left-body-scroll"]),n=wl(r["main-body-scroll"]),i=wl(r["right-body-scroll"]),l=S.value,r=t.currentTarget.scrollTop,l=l||n;let e=0;l=(e=l?l.scrollLeft:e)!==o;B.inVirtualScroll=!0,ml(n,r),ml(a,r),ml(i,r),se.triggerScrollYEvent(t),se.handleScrollEvent(t,!0,l,r,e,{type:"table",fixed:""})}},scrollToTreeRow(t){var e=z.treeConfig,l=B.tableFullData;let o=[];if(e){e=ie.value,e=e.children||e.childrenField,l=al().findTree(l,e=>se.eqRow(e,t),{children:e});if(l){let r=l.nodes;r.forEach((e,t)=>{t<r.length-1&&!P.isTreeExpandByRow(e)&&o.push(P.setTreeExpand(e,!0))})}}return Promise.all(o).then(()=>$l(se,t))},updateScrollYStatus:tr,updateScrollXSpace(){let{isGroup:t,scrollXLoad:r}=U,{visibleColumn:l,scrollXStore:o,elemStore:a,tableWidth:n}=B;var i=u.value,s=b.value,c=p.value,s=s?s.$el:null;if(s){var i=i?i.$el:null,c=c?c.$el:null,i=i?i.querySelector(".vxe-table--header"):null,s=s.querySelector(".vxe-table--body"),c=c?c.querySelector(".vxe-table--footer"):null,d=l.slice(0,o.startIndex).reduce((e,t)=>e+t.renderWidth,0);let e="";r&&(e=d+"px"),i&&(i.style.marginLeft=t?"":e),s.style.marginLeft=e,c&&(c.style.marginLeft=e);["main"].forEach(t=>{["header","body","footer"].forEach(e=>{e=wl(a[t+`-${e}-xSpace`]);e&&(e.style.width=r?n+"px":"")})});d=k.value;d&&(d.style.width=n+"px"),(0,dl.nextTick)(()=>{g()})}},updateScrollYSpace(){var{isAllOverflow:e,scrollYLoad:t}=U;let{scrollYStore:r,elemStore:l,isResizeCellHeight:o,afterFullData:a,fullAllDataRowIdData:n}=B;var i=r.startIndex,s=ee.value,c=R.value,d=E.value,u=wl(l["main-body-table"]);let p=0,h=0;if(t)if(!(o||c.height||s.height)&&e)h=a.length*d,p=Math.max(0,i*d);else{for(let e=0;e<a.length;e++){var m=a[e],m=n[xl(se,m)]||{};h+=m.resizeHeight||m.height||c.height||s.height||d}for(let e=0;e<i;e++){var v=a[e],v=n[xl(se,v)]||{};p+=v.resizeHeight||v.height||c.height||s.height||d}}else u&&(h=u.clientHeight);["main","left","right"].forEach(t=>{var e=wl(l[t+"-body-table"]);e&&(e.style.marginTop=p?p+"px":""),["header","body","footer"].forEach(e=>{e=wl(l[t+`-${e}-ySpace`]);e&&(e.style.height=h?h+"px":"")})});t=F.value;return t&&(t.style.height=h?h+"px":""),(0,dl.nextTick)().then(()=>{g()})},updateScrollXData(){let e=U.isAllOverflow;return Yt(),(0,dl.nextTick)().then(()=>{Yt(),se.updateScrollXSpace(),e||se.updateScrollYSpace()})},updateScrollYData(){return se.handleTableData(),(0,dl.nextTick)().then(()=>{se.handleTableData(),se.updateScrollYSpace()})},checkScrolling(){var e=B.elemStore,e=wl(e["main-body-scroll"]),t=K.value,r=Y.value,e=S.value||e;e&&(t&&(0<e.scrollLeft?hl:pl)(t,"scrolling--middle"),r)&&(e.clientWidth<e.scrollWidth-Math.ceil(e.scrollLeft)?hl:pl)(r,"scrolling--middle")},updateZindex(){z.zIndex?B.tZindex=z.zIndex:B.tZindex<El()&&(B.tZindex=Tl())},handleCheckedCheckboxRow:x,triggerHoverEvent(e,{row:t}){W.setHoverRow(t)},setHoverRow(e){var t=xl(se,e),r=q.value;W.clearHoverRow(),r&&al().arrayEach(r.querySelectorAll(`[rowid="${t}"]`),e=>hl(e,"row--hover")),B.hoverRow=e},clearHoverRow(){var e=q.value;e&&al().arrayEach(e.querySelectorAll(".vxe-body--row.row--hover"),e=>pl(e,"row--hover")),B.hoverRow=null},getCell(e,t){return P.getCellElement(e,t)},findRowIndexOf(e,t){return t?al().findIndexOf(e,e=>se.eqRow(e,t)):-1},eqRow(e,t){return!(!e||!t||e!==t&&xl(se,e)!==xl(se,t))}},"openExport,openPrint,exportData,openImport,importData,saveFile,readFile,importByFile,print".split(",").forEach(e=>{se[e]=function(){Br("vxe.error.reqModule",["VxeTableExportModule"])}}),"clearValidate,fullValidate,validate".split(",").forEach(e=>{se[e]=function(){Br("vxe.error.reqModule",["VxeTableValidatorModule"])}}),Object.assign(se,P,W),e=>{var{showHeader:t,showFooter:r}=z,{tableData:l,tableColumn:o,tableGroupColumn:a,columnStore:n,footerTableData:i}=U,s="left"===e,n=s?n.leftList:n.rightList;return(0,dl.h)("div",{ref:s?K:Y,class:`vxe-table--fixed-${e}-wrapper`},[t?(0,dl.h)(Pl,{ref:s?d:v,fixedType:e,tableData:l,tableColumn:o,tableGroupColumn:a,fixedColumn:n}):Kr(se),(0,dl.h)(Nl,{ref:s?h:w,fixedType:e,tableData:l,tableColumn:o,fixedColumn:n}),r?(0,dl.h)(Bl,{ref:s?m:y,footerTableData:i,tableColumn:o,fixedColumn:n,fixedType:e}):Kr(se)])}),Sr=()=>{var e=z.dragConfig,{dragRow:t,dragCol:r,dragTipText:l}=U,o=re.value,e=(te.value.slots||{}).tip||(e&&e.slots?e.slots.rowTip:null),o=(o.slots||{}).tip;return t&&e?Er(e,{row:t}):r&&o?Er(o,{column:r}):[(0,dl.h)("span",l)]},Ir=()=>(0,dl.h)("div",{key:"vsx",ref:Re,class:"vxe-table--scroll-x-virtual"},[(0,dl.h)("div",{ref:Ie,class:"vxe-table--scroll-x-left-corner"}),(0,dl.h)("div",{ref:ke,class:"vxe-table--scroll-x-wrapper"},[(0,dl.h)("div",{ref:S,class:"vxe-table--scroll-x-handle",onScroll:se.triggerVirtualScrollXEvent},[(0,dl.h)("div",{ref:k,class:"vxe-table--scroll-x-space"})])]),(0,dl.h)("div",{ref:De,class:"vxe-table--scroll-x-right-corner"})]),Dr=()=>(0,dl.h)("div",{ref:Se,class:"vxe-table--scroll-y-virtual"},[(0,dl.h)("div",{ref:Me,class:"vxe-table--scroll-y-top-corner"}),(0,dl.h)("div",{ref:Fe,class:"vxe-table--scroll-y-wrapper"},[(0,dl.h)("div",{ref:I,class:"vxe-table--scroll-y-handle",onScroll:se.triggerVirtualScrollYEvent},[(0,dl.h)("div",{ref:F,class:"vxe-table--scroll-y-space"})])]),(0,dl.h)("div",{ref:Oe,class:"vxe-table--scroll-y-bottom-corner"})]),Mr=()=>{var{showHeader:e,showFooter:t}=z,{overflowX:r,tableData:l,tableColumn:o,tableGroupColumn:a,footerTableData:n,columnStore:i}=U,{leftList:i,rightList:s}=i;return(0,dl.h)("div",{ref:c,class:"vxe-table--viewport-wrapper"},[(0,dl.h)("div",{class:"vxe-table--main-wrapper"},[e?(0,dl.h)(Pl,{ref:u,tableData:l,tableColumn:o,tableGroupColumn:a}):Kr(se),(0,dl.h)(Nl,{ref:b,tableData:l,tableColumn:o}),t?(0,dl.h)(Bl,{ref:p,footerTableData:n,tableColumn:o}):Kr(se)]),(0,dl.h)("div",{class:"vxe-table--fixed-wrapper"},[i&&i.length&&r?Rr("left"):Kr(se),s&&s.length&&r?Rr("right"):Kr(se)])])},kr=()=>{var e=Q.value;return(0,dl.h)("div",{class:"vxe-table--layout-wrapper"},e?[Dr(),Mr()]:[Mr(),Dr()])};let Fr=(0,dl.ref)(0),Or=((0,dl.watch)(()=>z.data?z.data.length:-1,()=>{Fr.value++}),(0,dl.watch)(()=>z.data,()=>{Fr.value++}),(0,dl.watch)(Fr,()=>{let l=B.initStatus;var e=z.data||[];e&&5e4<=e.length&&Pr("vxe.error.errLargeData",["loadData(data), reloadData(data)"]),qt(e,!0).then(()=>{var{scrollXLoad:e,scrollYLoad:t,expandColumn:r}=U;return B.inited=!0,B.initStatus=!0,l||Xt(),(e||t)&&r&&Pr("vxe.error.scrollErrProp",["column.type=expand"]),P.recalculate()})}),(0,dl.ref)(0)),Ar=((0,dl.watch)(()=>U.staticColumns.length,()=>{Or.value++}),(0,dl.watch)(()=>U.staticColumns,()=>{Or.value++}),(0,dl.watch)(Or,()=>{er(al().clone(U.staticColumns))}),(0,dl.ref)(0)),Lr=((0,dl.watch)(()=>U.tableColumn.length,()=>{Ar.value++}),(0,dl.watch)(()=>U.tableColumn,()=>{Ar.value++}),(0,dl.watch)(Ar,()=>{W.analyColumnWidth()}),(0,dl.watch)(()=>U.upDataFlag,()=>{(0,dl.nextTick)(()=>{P.updateData()})}),(0,dl.watch)(()=>U.reColumnFlag,()=>{(0,dl.nextTick)(()=>{P.refreshColumn()})}),(0,dl.ref)(0)),Vr=((0,dl.watch)(he,()=>{Lr.value++}),(0,dl.watch)(()=>z.showHeader,()=>{Lr.value++}),(0,dl.watch)(()=>z.showFooter,()=>{Lr.value++}),(0,dl.watch)(Lr,()=>{(0,dl.nextTick)(()=>{P.recalculate(!0).then(()=>P.refreshScroll())})}),(0,dl.ref)(0)),$r=((0,dl.watch)(()=>z.height,()=>{Vr.value++}),(0,dl.watch)(()=>z.maxHeight,()=>{Vr.value++}),(0,dl.watch)(Z,()=>{Vr.value++}),(0,dl.watch)(Q,()=>{Vr.value++}),(0,dl.watch)(Vr,()=>{(0,dl.nextTick)(()=>P.recalculate(!0))}),(0,dl.ref)(0)),_r=((0,dl.watch)(()=>z.footerData?z.footerData.length:-1,()=>{$r.value++}),(0,dl.watch)(()=>z.footerData,()=>{$r.value++}),(0,dl.watch)($r,()=>{P.updateFooter()}),(0,dl.watch)(()=>z.syncResize,e=>{e&&(ar(),(0,dl.nextTick)(()=>{ar(),setTimeout(()=>ar())}))}),(0,dl.ref)(0)),Hr=((0,dl.watch)(()=>z.mergeCells?z.mergeCells.length:-1,()=>{_r.value++}),(0,dl.watch)(()=>z.mergeCells,()=>{_r.value++}),(0,dl.watch)(_r,()=>{P.clearMergeCells(),(0,dl.nextTick)(()=>{z.mergeCells&&P.setMergeCells(z.mergeCells)})}),(0,dl.ref)(0));(0,dl.watch)(()=>z.mergeFooterItems?z.mergeFooterItems.length:-1,()=>{Hr.value++}),(0,dl.watch)(()=>z.mergeFooterItems,()=>{Hr.value++}),(0,dl.watch)(Hr,()=>{P.clearMergeFooterItems(),(0,dl.nextTick)(()=>{z.mergeFooterItems&&P.setMergeFooterItems(z.mergeFooterItems)})}),t&&(0,dl.watch)(()=>t?t.reactData.resizeFlag:null,()=>{fr()}),tl.forEach(e=>{var e=e.setupTable;e&&(e=e(se))&&al().isObject(e)&&Object.assign(se,e)}),W.preventEvent(null,"created",{$table:se});let Nr;return(0,dl.onActivated)(()=>{P.recalculate().then(()=>P.refreshScroll()),W.preventEvent(null,"activated",{$table:se})}),(0,dl.onDeactivated)(()=>{B.isActivated=!1,W.preventEvent(null,"deactivated",{$table:se})}),(0,dl.onMounted)(()=>{var e=J.value,t=ee.value,r=T.value;(e.drag||t.drag||r.allowSort)&&Sl(),(0,dl.nextTick)(()=>{let{data:e,treeConfig:l,showOverflow:o}=z;var{scrollXStore:t,scrollYStore:r}=B,a=f.value,n=oe.value,i=ie.value,s=D.value,c=le.value,d=M.value;let u=ee.value;{z.rowId&&Pr("vxe.error.delProp",["row-id","row-config.keyField"]),z.rowKey&&Pr("vxe.error.delProp",["row-key","row-config.useKey"]),z.columnKey&&Pr("vxe.error.delProp",["column-id","column-config.useKey"]),z.rowId||u.keyField||!(c.reserve||c.checkRowKeys||s.reserve||s.checkRowKey||d.expandRowKeys||i.expandRowKeys)||Pr("vxe.error.reqProp",["row-config.keyField"]),z.editConfig&&(n.showStatus||n.showUpdateStatus||n.showInsertStatus)&&!z.keepSource&&Pr("vxe.error.reqProp",["keep-source"]),!l||!i.showLine&&!i.line||(z.rowKey||u.useKey)&&o||Pr("vxe.error.reqProp",["row-config.useKey | show-overflow"]),l&&!i.transform&&z.stripe&&Pr("vxe.error.noTree",["stripe"]),!z.showFooter||z.footerMethod||z.footerData||Pr("vxe.error.reqProp",["footer-data | footer-method"]),u.height&&Pr("vxe.error.delProp",["row-config.height","cell-config.height"]);var{exportConfig:s,importConfig:d}=z;let t=Ye.value,r=Ge.value;d&&r.types&&!r.importMethod&&!al().includeArrays(al().keys(r._typeMaps),r.types)&&Pr("vxe.error.errProp",["export-config.types="+r.types.join(","),r.types.filter(e=>al().includes(al().keys(r._typeMaps),e)).join(",")||al().keys(r._typeMaps).join(",")]),s&&t.types&&!t.exportMethod&&!al().includeArrays(al().keys(t._typeMaps),t.types)&&Pr("vxe.error.errProp",["export-config.types="+t.types.join(","),t.types.filter(e=>al().includes(al().keys(t._typeMaps),e)).join(",")||al().keys(t._typeMaps).join(",")])}{d=T.value,s=ae.value;let e=ee.value;if(z.id||(z.customConfig?nl(d):d.enabled)&&d.storage&&Br("vxe.error.reqProp",["id"]),z.treeConfig&&c.range&&Br("vxe.error.noTree",["checkbox-config.range"]),e.height&&!z.showOverflow&&Pr("vxe.error.notProp",["table.show-overflow"]),!se.handleMousedownCellAreaEvent&&(z.areaConfig&&Pr("vxe.error.notProp",["area-config"]),z.clipConfig&&Pr("vxe.error.notProp",["clip-config"]),z.fnrConfig&&Pr("vxe.error.notProp",["fnr-config"]),s.area))return void Br("vxe.error.notProp",["mouse-config.area"]);z.dragConfig&&Pr("vxe.error.delProp",["drag-config","row-drag-config"]),z.treeConfig&&i.children&&Pr("vxe.error.delProp",["tree-config.children","tree-config.childrenField"]),z.treeConfig&&i.line&&Pr("vxe.error.delProp",["tree-config.line","tree-config.showLine"]),s.area&&s.selected&&Pr("vxe.error.errConflicts",["mouse-config.area","mouse-config.selected"]),s.area&&z.treeConfig&&!i.transform&&Br("vxe.error.noTree",["mouse-config.area"]),z.editConfig&&n.activeMethod&&Pr("vxe.error.delProp",["edit-config.activeMethod","edit-config.beforeEditMethod"]),z.treeConfig&&c.isShiftKey&&Br("vxe.error.errConflicts",["tree-config","checkbox-config.isShiftKey"]),c.halfField&&Pr("vxe.error.delProp",["checkbox-config.halfField","checkbox-config.indeterminateField"])}z.editConfig&&!se.insert&&Br("vxe.error.reqModule",["Edit"]),z.editRules&&!se.validate&&Br("vxe.error.reqModule",["Validator"]),(c.range||z.keyboardConfig||z.mouseConfig)&&!se.handleCellMousedownEvent&&Br("vxe.error.reqModule",["Keyboard"]),(z.printConfig||z.importConfig||z.exportConfig)&&!se.exportData&&Br("vxe.error.reqModule",["Export"]),Object.assign(r,{startIndex:0,endIndex:0,visibleSize:0,adaptive:!1!==a.adaptive}),Object.assign(t,{startIndex:0,endIndex:0,visibleSize:0}),qt(e||[],!0).then(()=>{e&&e.length&&(B.inited=!0,B.initStatus=!0,Xt()),Kt(),g()}),z.autoResize&&(d=q.value,s=W.getParentElem(),Nr=Jr.create(()=>{z.autoResize&&P.recalculate(!0)}),d&&Nr.observe(d),s)&&Nr.observe(s)}),qr.on(se,"paste",hr),qr.on(se,"copy",mr),qr.on(se,"cut",vr),qr.on(se,"mousedown",sr),qr.on(se,"blur",cr),qr.on(se,"mousewheel",dr),qr.on(se,"keydown",pr),qr.on(se,"resize",fr),qr.on(se,"contextmenu",se.handleGlobalContextmenuEvent),W.preventEvent(null,"mounted",{$table:se})}),(0,dl.onBeforeUnmount)(()=>{Nr&&Nr.disconnect(),P.closeFilter(),se.closeMenu&&se.closeMenu(),W.preventEvent(null,"beforeUnmount",{$table:se})}),(0,dl.onUnmounted)(()=>{qr.off(se,"paste"),qr.off(se,"copy"),qr.off(se,"cut"),qr.off(se,"mousedown"),qr.off(se,"blur"),qr.off(se,"mousewheel"),qr.off(se,"keydown"),qr.off(se,"resize"),qr.off(se,"contextmenu"),W.preventEvent(null,"unmounted",{$table:se})}),(0,dl.nextTick)(()=>{!z.loading||ue||j.loading||Br("vxe.error.reqComp",["vxe-loading"]),!0!==z.showOverflow&&"tooltip"!==z.showOverflow&&!0!==z.showHeaderOverflow&&"tooltip"!==z.showHeaderOverflow&&!0!==z.showFooterOverflow&&"tooltip"!==z.showFooterOverflow&&!z.tooltipConfig&&!z.editRules||pe||Br("vxe.error.reqComp",["vxe-tooltip"])}),(0,dl.provide)("$xeColgroup",null),(0,dl.provide)("$xeTable",se),se.renderVN=()=>{var{loading:e,stripe:t,showHeader:r,height:l,treeConfig:o,mouseConfig:a,showFooter:n,highlightCell:i,highlightHoverRow:s,highlightHoverColumn:c,editConfig:d,editRules:u}=z,{isGroup:p,overflowX:h,overflowY:m,scrollXLoad:v,scrollYLoad:f,tableData:g,initStore:x,columnStore:b,filterStore:C,customStore:$}=U,{leftList:b,rightList:_}=b;let w=j.loading;var y=Ve.value,T=$e.value,E=Ae.value,H=le.value,R=ie.value,N=ee.value,S=J.value,I=he.value,D=ot.value,M=ae.value,k=He.value,F=Je.value,P=Xe.value;let O=U.isColLoading||U.isRowLoading||e;var e=Le.value,A=a&&M.area,B=re.value,L=Z.value,V=Q.value;return(0,dl.h)("div",{ref:q,class:["vxe-table","vxe-table--render-default","tid_"+de,"border--"+D,"sx-pos--"+(L?"top":"bottom"),"sy-pos--"+(V?"left":"right"),{["size--"+I]:I,["valid-msg--"+E.msgMode]:!!u,"vxe-editable":!!d,"old-cell-valid":u&&"obsolete"===Ur().cellVaildMode,"cell--highlight":i,"cell--selected":a&&M.selected,"cell--area":A,"header-cell--area":A&&k.selectCellByHeader,"body-cell--area":A&&k.selectCellByBody,"row--highlight":N.isHover||s,"column--highlight":S.isHover||c,"checkbox--range":H.range,"col--drag-cell":S.drag&&"cell"===B.trigger,"is--header":r,"is--footer":n,"is--group":p,"is--tree-line":o&&(R.showLine||R.line),"is--fixed-left":b.length,"is--fixed-right":_.length,"is--animat":!!z.animat,"is--round":z.round,"is--stripe":!o&&t,"is--loading":O,"is--empty":!O&&!g.length,"is--scroll-y":m,"is--scroll-x":h,"is--virtual-x":v,"is--virtual-y":f}],spellcheck:!1,onKeydown:ur},[(0,dl.h)("div",{class:"vxe-table-slots"},j.default?j.default({}):[]),(0,dl.h)("div",{key:"tw",class:"vxe-table--render-wrapper"},L?[Ir(),kr()]:[kr(),Ir()]),(0,dl.h)("div",{key:"tn",ref:we,class:"vxe-table--empty-placeholder"},[(0,dl.h)("div",{class:"vxe-table--empty-content"},(D=Qe.value,V={$table:se},j.empty?j.empty(V):(I=(I=D.name?Gr.get(D.name):null)?I.renderTableEmpty||I.renderTableEmptyView||I.renderEmpty:null)?yl(I(D,V)):il(z.emptyText)||Wr("vxe.table.emptyText")))]),(0,dl.h)("div",{key:"tl",class:"vxe-table--border-line"}),(0,dl.h)("div",{key:"tcl",ref:be,class:"vxe-table--resizable-col-bar"},e.showDragTip?[(0,dl.h)("div",{class:"vxe-table--resizable-number-tip"})]:[]),(0,dl.h)("div",{key:"trl",ref:Ce,class:"vxe-table--resizable-row-bar"},e.showDragTip?[(0,dl.h)("div",{class:"vxe-table--resizable-number-tip"})]:[]),ue?(0,dl.h)(ue,{key:"lg",class:"vxe-table--loading",modelValue:O,icon:F.icon,text:F.text},w?{default:()=>Er(w,{$table:se,$grid:G,loading:O})}:{}):w?(0,dl.h)("div",{class:["vxe-loading--custom-wrapper",{"is--visible":O}]},Er(w,{$table:se,$grid:G,loading:O})):Kr(se),x.custom?(0,dl.h)(jl,{key:"cs",ref:xe,customStore:$}):Kr(se),x.filter?(0,dl.h)(Ul,{key:"tf",ref:ge,filterStore:C}):Kr(se),x.import&&z.importConfig?(0,dl.h)(Wl,{key:"it",defaultOptions:U.importParams,storeData:U.importStore}):Kr(se),x.export&&(z.exportConfig||z.printConfig)?(0,dl.h)(ql,{key:"et",defaultOptions:U.exportParams,storeData:U.exportStore}):Kr(se),P?(0,dl.h)(Xl,{key:"tm",ref:fe}):Kr(se),(d=U.dragRow,i=ee.value,a=J.value,M=te.value,A=re.value,i.drag||a.drag?(0,dl.h)("div",{class:"vxe-table--drag-wrapper"},[(0,dl.h)("div",{ref:Te,class:["vxe-table--drag-row-line",{"is--guides":M.showGuidesStatus}]}),(0,dl.h)("div",{ref:Ee,class:["vxe-table--drag-col-line",{"is--guides":A.showGuidesStatus}]}),(0,dl.h)("div",{ref:ye,class:"vxe-table--drag-sort-tip"},[(0,dl.h)("div",{class:"vxe-table--drag-sort-tip-wrapper"},[(0,dl.h)("div",{class:"vxe-table--drag-sort-tip-status"},[(0,dl.h)("span",{class:["vxe-table--drag-sort-tip-normal-status",d?Yr().TABLE_DRAG_STATUS_ROW:Yr().TABLE_DRAG_STATUS_COLUMN]}),(0,dl.h)("span",{class:["vxe-table--drag-sort-tip-sub-status",Yr().TABLE_DRAG_STATUS_SUB_ROW]}),(0,dl.h)("span",{class:["vxe-table--drag-sort-tip-disabled-status",Yr().TABLE_DRAG_DISABLED]})]),(0,dl.h)("div",{class:"vxe-table--drag-sort-tip-content"},Sr())])])]):Kr(se)),pe?(0,dl.h)("div",{},[(0,dl.h)(pe,{key:"ctp",ref:me,isArrow:!1,enterable:!1}),(0,dl.h)(pe,{key:"btp",ref:X,theme:y.theme,enterable:y.enterable,enterDelay:y.enterDelay,leaveDelay:y.leaveDelay}),z.editRules&&E.showMessage&&("default"===E.message?!l:"tooltip"===E.message)?(0,dl.h)(pe,{key:"vtp",ref:ve,class:[{"old-cell-valid":u&&"obsolete"===Ur().cellVaildMode},"vxe-table--valid-error"],theme:T.theme,enterable:T.enterable,enterDelay:T.enterDelay,leaveDelay:T.leaveDelay}):Kr(se)]):Kr(se)])},se},render(){return this.renderVN()}});let{renderer:Re,hooks:Se}=ol.VxeUI,Ie=["openFilter","setFilter","clearFilter","saveFilterPanel","resetFilterPanel","getCheckedFilters","updateFilterOptionStatus"],{menus:De,hooks:Me,globalEvents:ke,GLOBAL_EVENT_KEYS:Fe}=(Se.add("tableFilterModule",{setupTable(u){let{props:p,reactData:h,internalData:i}=u,{refTableHeader:g,refTableBody:x,refTableFilter:b}=u.getRefMaps(),{computeFilterOpts:m,computeMouseOpts:v}=u.getComputeMaps(),o=e=>{var t=h.filterStore;t.options.forEach(e=>{e.checked=e._checked}),u.confirmFilterEvent(e)},a=(e,t,r)=>{var l=h.filterStore;l.options.forEach(e=>{e._checked=!1}),r._checked=t,u.checkFilterOptions(),o(e)},n=(e,t,r)=>{r._checked=t,u.checkFilterOptions()},e=e=>{var t=h.filterStore;u.handleClearFilter(t.column),u.confirmFilterEvent(e)},s={checkFilterOptions(){var e=h.filterStore;e.isAllSelected=e.options.every(e=>e._checked),e.isIndeterminate=!e.isAllSelected&&e.options.some(e=>e._checked)},triggerFilterEvent(e,v,t){let{initStore:r,filterStore:f}=h;if(f.column===v&&f.visible)f.visible=!1;else{let{target:p,pageX:h}=e,m=mr().visibleWidth;var{filters:o,filterMultiple:a,filterRender:n}=v,n=nl(n)?Re.get(n.name):null;let l=v.filterRecoverMethod||(n?n.tableFilterRecoverMethod||n.filterRecoverMethod:null);i._currFilterParams=t,Object.assign(f,{multiple:a,options:o,column:v,style:null}),f.options.forEach(e=>{var{_checked:t,checked:r}=e;(e._checked=r)||t===r||l&&l({option:e,column:v,$table:u})}),this.checkFilterOptions(),f.visible=!0,r.filter=!0,(0,dl.nextTick)(()=>{var l=g.value,o=x.value,l=l?l.$el:null,o=o.$el;if(o){var a=b.value,a=a?a.$el:null;if(a){var n=a.offsetWidth,i=a.offsetHeight,s=a.querySelector(".vxe-table--filter-header"),a=a.querySelector(".vxe-table--filter-footer"),c=n/2,d=o.clientWidth-n-10;let e,t;var u={top:p.offsetTop+p.offsetParent.offsetTop+p.offsetHeight+"px"};let r=null;l=o.clientHeight-(l?l.clientHeight/2:0);l<=i&&(r=Math.max(40,l-(a?a.offsetHeight:0)-(s?s.offsetHeight:0))),"left"===v.fixed?e=p.offsetLeft+p.offsetParent.offsetLeft-c:"right"===v.fixed?t=p.offsetParent.offsetWidth-p.offsetLeft+(p.offsetParent.offsetParent.offsetWidth-p.offsetParent.offsetLeft)-v.renderWidth-c:e=p.offsetLeft+p.offsetParent.offsetLeft-c-o.scrollLeft,e?(0<(i=h+n-c+10-m)&&(e-=i),u.left=Math.min(d,Math.max(10,e))+"px"):t&&(0<(l=h+n-c+10-m)&&(t+=l),u.right=Math.max(10,t)+"px"),f.style=u,f.maxHeight=r}}})}u.dispatchEvent("filter-visible",{column:v,field:v.field,property:v.field,filterList:u.getCheckedFilters(),visible:f.visible},e)},handleClearFilter(e){if(e){var{filters:r,filterRender:l}=e;if(r){l=nl(l)?Re.get(l.name):null;let t=e.filterResetMethod||(l?l.tableFilterResetMethod||l.filterResetMethod:null);r.forEach(e=>{e._checked=!1,e.checked=!1,t||(e.data=al().clone(e.resetValue,!0))}),t&&t({options:r,column:e,$table:u})}}},handleColumnConfirmFilter(e,t){var r=p.mouseConfig;let{scrollXLoad:l,scrollYLoad:o}=h;var a=m.value,n=v.value,i=e.field;let s=[],c=[];e.filters.forEach(e=>{e.checked&&(s.push(e.value),c.push(e.data))});var d=u.getCheckedFilters(),e={$table:u,$event:t,column:e,field:i,property:i,values:s,datas:c,filters:d,filterList:d};return a.remote||(u.handleTableData(!0),u.checkSelectionStatus()),r&&n.area&&u.handleFilterEvent&&u.handleFilterEvent(t,e),t&&u.dispatchEvent("filter-change",e,t),u.closeFilter(),u.updateFooter().then(()=>{var{scrollXLoad:e,scrollYLoad:t}=h;if(l||e||o||t)return(l||e)&&u.updateScrollXSpace(),(o||t)&&u.updateScrollYSpace(),u.refreshScroll()}).then(()=>(u.updateCellAreas(),u.recalculate(!0))).then(()=>{setTimeout(()=>u.recalculate(),50)})},confirmFilterEvent(e){var t=h.filterStore,t=t.column;u.handleColumnConfirmFilter(t,e)},handleFilterChangeRadioOption:a,handleFilterChangeMultipleOption:n,handleFilterChangeOption(e,t,r){var l=h.filterStore;l.multiple?n(0,t,r):a(e,t,r)},handleFilterConfirmFilter:o,handleFilterResetFilter:e};return{...{openFilter(e){let o=jr(u,e);if(o&&o.filters){let r=i.elemStore,l=o.fixed;return u.scrollToColumn(o).then(()=>{var e,t=wl(r[`${l||"main"}-header-wrapper`]||r["main-header-wrapper"]);t&&(t=t.querySelector(`.vxe-header--column.${o.id} .vxe-filter--btn`),e="click",t=t)&&t.dispatchEvent(new Event(e))})}return(0,dl.nextTick)()},setFilter(e,t,r){e=jr(u,e);return e&&e.filters&&(e.filters=Cr(t||[]),r)?u.handleColumnConfirmFilter(e,new Event("click")):(0,dl.nextTick)()},clearFilter(e){var t=h.filterStore,r=i.tableFullColumn,l=m.value;let o;return e?(o=jr(u,e))&&s.handleClearFilter(o):r.forEach(s.handleClearFilter),e&&o===t.column||Object.assign(t,{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1}),l.remote?(0,dl.nextTick)():u.updateData()},saveFilterPanel(){return o(null),(0,dl.nextTick)()},resetFilterPanel(){return e(null),(0,dl.nextTick)()},getCheckedFilters(){var e=i.tableFullColumn;let a=[];return e.forEach(e=>{var{field:t,filters:r}=e;let l=[],o=[];r&&r.length&&(r.forEach(e=>{e.checked&&(l.push(e.value),o.push(e.data))}),l.length)&&a.push({column:e,field:t,property:t,values:l,datas:o})}),a},updateFilterOptionStatus(e,t){return e._checked=t,e.checked=t,(0,dl.nextTick)()}},...s}},setupGrid(e){return e.extendTableMethods(Ie)}}),ol.VxeUI),Oe=["closeMenu"],{getConfig:Ae,renderer:Le,hooks:Ve,getI18n:$e}=(Me.add("tableMenuModule",{setupTable(f){let{xID:g,props:x,reactData:b,internalData:C}=f,{refElem:w,refTableFilter:y,refTableMenu:T}=f.getRefMaps(),{computeMouseOpts:E,computeIsMenu:t,computeMenuOpts:R}=f.getComputeMaps(),S,s={},I=(r,l,p)=>{let h=b.ctxMenuStore;var o=t.value,e=R.value,l=e[l];let a=e.visibleMethod;if(l){let{options:t,disabled:e}=l;e?r.preventDefault():o&&t&&t.length&&(p.options=t,f.preventEvent(r,"event.showMenu",p,()=>{if(!a||a(p)){r.preventDefault(),f.updateZindex();let{scrollTop:o,scrollLeft:a,visibleHeight:n,visibleWidth:i}=mr(),s=r.clientY+o,c=r.clientX+a,l=()=>{C._currMenuParams=p,Object.assign(h,{visible:!0,list:t,selected:null,selectChild:null,showChild:!1,style:{zIndex:C.tZindex,top:s+"px",left:c+"px"}}),(0,dl.nextTick)(()=>{var e=T.value.getRefMaps().refElem.value,t=e.clientHeight,r=e.clientWidth,{boundingTop:e,boundingLeft:l}=gr(e),e=e+t-n,l=l+r-i;-10<e&&(h.style.top=Math.max(o+2,s-t-2)+"px"),-10<l&&(h.style.left=Math.max(a+2,c-r-2)+"px")})},{keyboard:e,row:d,column:u}=p;e&&d&&u?f.scrollToRow(d,u).then(()=>{var e,t,r=f.getCellElement(d,u);r&&({boundingTop:e,boundingLeft:t}=gr(r),s=e+o+Math.floor(r.offsetHeight/2),c=t+a+Math.floor(r.offsetWidth/2)),l()}):l()}else S.closeMenu()}))}f.closeFilter()};return S={closeMenu(){return Object.assign(b.ctxMenuStore,{visible:!1,selected:null,selectChild:null,showChild:!1}),(0,dl.nextTick)()}},s={moveCtxMenu(e,t,r,l,o,a){let n;var i=al().findIndexOf(a,e=>t[r]===e);if(l)o&&Rl(t.selected)?t.showChild=!0:(t.showChild=!1,t.selectChild=null);else if(ke.hasKey(e,Fe.ARROW_UP)){for(let e=i-1;0<=e;e--)if(!1!==a[e].visible){n=a[e];break}t[r]=n||a[a.length-1]}else if(ke.hasKey(e,Fe.ARROW_DOWN)){for(let e=i+1;e<a.length;e++)if(!1!==a[e].visible){n=a[e];break}t[r]=n||a[0]}else t[r]&&(ke.hasKey(e,Fe.ENTER)||ke.hasKey(e,Fe.SPACEBAR))&&s.ctxMenuLinkEvent(e,t[r])},handleOpenMenuEvent:I,handleGlobalContextmenuEvent(t){var{mouseConfig:e,menuConfig:r}=x,{editStore:l,ctxMenuStore:o}=b,a=C.visibleColumn,n=y.value,i=T.value,s=E.value,c=R.value,d=w.value,l=l.selected,u=["header","body","footer"];if(nl(r)){if(o.visible&&i&&fl(t,i.getRefMaps().refElem.value).flag)return void t.preventDefault();if(C._keyCtx){r={type:"body",$table:f,keyboard:!0,columns:a.slice(0),$event:t};if(e&&s.area){o=f.getActiveCellArea();if(o&&o.row&&o.column)return r.row=o.row,r.column=o.column,void I(t,"body",r)}else if(e&&s.selected&&l.row&&l.column)return r.row=l.row,r.column=l.column,void I(t,"body",r)}for(let e=0;e<u.length;e++){var p=u[e],h=fl(t,d,`vxe-${p}--column`,e=>e.parentNode.parentNode.parentNode.getAttribute("xid")===g),m={type:p,$table:f,columns:a.slice(0),$event:t};if(h.flag){var h=h.targetElem,v=f.getColumnNode(h),v=v?v.item:null;let e=p+"-";v&&Object.assign(m,{column:v,columnIndex:f.getColumnIndex(v),cell:h}),"body"===p&&(h=(v=f.getRowNode(h.parentNode))?v.item:null,e="",h)&&(m.row=h,m.rowIndex=f.getRowIndex(h));v=e+"cell-menu";return I(t,p,m),void f.dispatchEvent(v,m,t)}if(fl(t,d,`vxe-table--${p}-wrapper`,e=>e.getAttribute("xid")===g).flag)return void("cell"===c.trigger?t.preventDefault():I(t,p,m))}}n&&!fl(t,n.$el).flag&&f.closeFilter(),S.closeMenu()},ctxMenuMouseoverEvent(e,t,r){let c=e.currentTarget;var l=b.ctxMenuStore;e.preventDefault(),e.stopPropagation(),l.selected=t,(l.selectChild=r)||(l.showChild=Rl(t),l.showChild&&(0,dl.nextTick)(()=>{var o=c.nextElementSibling;if(o){var{boundingTop:a,boundingLeft:n,visibleHeight:i,visibleWidth:s}=gr(c),a=a+c.offsetHeight;let e="",t="",r=(n+c.offsetWidth+o.offsetWidth>s-10&&(e="auto",t=c.offsetWidth+"px"),""),l="";a+o.offsetHeight>i-10&&(r="auto",l="0"),o.style.left=e,o.style.right=t,o.style.top=r,o.style.bottom=l}}))},ctxMenuMouseoutEvent(e,t){var r=b.ctxMenuStore;t.children||(r.selected=null),r.selectChild=null},ctxMenuLinkEvent(e,t){var r;t.disabled||!t.code&&t.children&&t.children.length||(r=De.get(t.code),t=Object.assign({},C._currMenuParams,{menu:t,$table:f,$grid:f.xegrid,$event:e}),(r=r?r.tableMenuMethod||r.menuMethod:null)&&r(t,e),f.dispatchEvent("menu-click",t,e),S.closeMenu())}},{...S,...s}},setupGrid(e){return e.extendTableMethods(Oe)}}),ol.VxeUI),_e=["insert","insertAt","insertNextAt","insertChild","insertChildAt","insertChildNextAt","remove","removeCheckboxRow","removeRadioRow","removeCurrentRow","getRecordset","getInsertRecords","getRemoveRecords","getUpdateRecords","getEditRecord","getActiveRecord","getSelectedCell","clearEdit","clearActived","clearSelected","isEditByRow","isActiveByRow","setEditRow","setActiveRow","setEditCell","setActiveCell","setSelectCell"],He=(Ve.add("tableEditModule",{setupTable(b){let{props:C,reactData:w,internalData:y}=b,o=b.getRefMaps().refElem,{computeMouseOpts:u,computeEditOpts:m,computeCheckboxOpts:x,computeTreeOpts:T,computeValidOpts:c}=b.getComputeMaps(),E={},v={},f=(e,t)=>{var{model:r,editRender:l}=t;l&&(r.value=bl(e,t),r.update=!1)},a=(e,t)=>{var{model:r,editRender:l}=t;l&&r.update&&(Cl(e,t,r.value),r.update=!1,r.value=null)},r=()=>{var e=o.value;e&&(e=e.querySelector(".col--selected"))&&pl(e,"col--selected")};function d(){var{editStore:e,tableColumn:t}=w,r=m.value,e=e.actived;let{row:l,column:o}=e;(l||o)&&("row"===r.mode?t.forEach(e=>a(l,e)):a(l,o))}function R(e,t){let{tableFullTreeData:n,afterFullData:i,fullDataRowIdData:s,fullAllDataRowIdData:c}=y;var r=T.value;let{rowField:d,parentField:u,mapChildrenField:p}=r,h=r.children||r.childrenField,m=t?"push":"unshift";e.forEach(r=>{let t=r[u];var l=xl(b,r),o=t?al().findTree(n,e=>t===e[d],{children:p}):null;if(o){var o=o.item,a=c[xl(b,o)],a=a?a.level:0;let e=o[h],t=o[p];al().isArray(e)||(e=o[h]=[]),al().isArray(t)||(t=o[h]=[]),e[m](r),t[m](r);o={row:r,rowid:l,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:e,parent:o,level:a+1,height:0,resizeHeight:0,oTop:0};s[l]=o,c[l]=o}else{t&&Pr("vxe.error.unableInsert"),i[m](r),n[m](r);a={row:r,rowid:l,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:n,parent:null,level:0,height:0,resizeHeight:0,oTop:0};s[l]=a,c[l]=a}})}let s=(t,r,s)=>{let o=C.treeConfig;var{mergeList:e,editStore:l}=w;let{tableFullTreeData:c,afterFullData:a,tableFullData:n,fullDataRowIdData:d,fullAllDataRowIdData:u}=y,p=T.value,{transform:i,rowField:h,mapChildrenField:m}=p,v=p.children||p.childrenField,f=(al().isArray(t)||(t=[t]),(0,dl.reactive)(b.defineField(t.map(e=>Object.assign(o&&i?{[m]:[],[v]:[]}:{},e)))));if(al().eqNull(r))o&&i?R(f,!1):(f.forEach(e=>{var t=xl(b,e),r={row:e,rowid:t,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:a,parent:null,level:0,height:0,resizeHeight:0,oTop:0};d[t]=r,u[t]=r,a.unshift(e),n.unshift(e)}),e.forEach(e=>{var t=e.row;0<t&&(e.row=t+f.length)}));else if(-1===r)o&&i?R(f,!0):(f.forEach(e=>{var t=xl(b,e),r={row:e,rowid:t,seq:-1,index:-1,_index:-1,treeIndex:-1,$index:-1,items:a,parent:null,level:0,height:0,resizeHeight:0,oTop:0};d[t]=r,u[t]=r,a.push(e),n.push(e)}),e.forEach(e=>{var{row:t,rowspan:r}=e;t+r>a.length&&(e.rowspan=r+f.length)}));else if(o&&i){let i=al().findTree(c,e=>r[h]===e[h],{children:m});if(i){let o=i.parent,a=o?o[m]:c;t=u[xl(b,o)];let n=t?t.level:0;if(f.forEach((e,t)=>{var r=xl(b,e);e[p.parentField]&&o&&e[p.parentField]!==o[h]&&Br("vxe.error.errProp",[p.parentField+"="+e[p.parentField],p.parentField+"="+o[h]]),o&&(e[p.parentField]=o[h]);let l=i.index+t;s&&(l+=1),a.splice(l,0,e);t={row:e,rowid:r,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:a,parent:o,level:n+1,height:0,resizeHeight:0,oTop:0};d[r]=t,u[r]=t}),o){t=al().findTree(c,e=>r[h]===e[h],{children:v});if(t){var g=t.items;let e=t.index;s&&(e+=1),g.splice(e,0,...f)}}}else Pr("vxe.error.unableInsert"),R(f,!0)}else{if(o)throw new Error($e("vxe.error.noTree",["insert"]));let l=-1;if(al().isNumber(r)?r<a.length&&(l=r):l=b.findRowIndexOf(a,r),-1===(l=s?Math.min(a.length,l+1):l))throw new Error($e("vxe.error.unableInsert"));a.splice(l,0,...f);t=b.findRowIndexOf(n,r);-1<t?n.splice(t+(s?1:0),0,...f):n.push(...f),e.forEach(e=>{var{row:t,rowspan:r}=e;t>l?e.row=t+f.length:t+r>l&&(e.rowspan=r+f.length)})}let x=l.insertMaps;return f.forEach(e=>{var t=xl(b,e);x[t]=e}),b.cacheRowMap(!1),b.updateScrollYStatus(),b.handleTableData(o&&i),o&&i||b.updateAfterDataIndex(),b.updateFooter(),b.checkSelectionStatus(),w.scrollYLoad&&b.updateScrollYSpace(),(0,dl.nextTick)().then(()=>(b.updateCellAreas(),b.recalculate())).then(()=>({row:f.length?f[f.length-1]:null,rows:f}))},l=(e,t,r,l)=>{var o=C.treeConfig;let{transform:a,rowField:n,parentField:i}=T.value;return o&&a?(al().isArray(e)||(e=[e]),s(e.map(e=>Object.assign({},e,{[i]:t[n]})),r,l)):(Br("vxe.error.errProp",["tree-config.treeConfig=false","tree-config.treeConfig=true"]),Promise.resolve({row:null,rows:[]}))},g=(e,t)=>{let r=C.mouseConfig;var l=w.editStore,{actived:l,focused:o}=l,{row:a,column:n}=l,i=c.value;let s=u.value;if(a||n){if(t&&xl(b,t)!==xl(b,a))return(0,dl.nextTick)();d(),l.args=null,l.row=null,l.column=null,b.updateFooter(),b.dispatchEvent("edit-closed",{row:a,rowIndex:b.getRowIndex(a),$rowIndex:b.getVMRowIndex(a),column:n,columnIndex:b.getColumnIndex(n),$columnIndex:b.getVMColumnIndex(n)},e||null)}return(0,dl.nextTick)(()=>{if(r&&s.area&&b.handleRecalculateCellAreas)return b.handleRecalculateCellAreas()}),i.autoClear&&("full"!==i.msgMode||"obsolete"===Ae().cellVaildMode)&&b.clearValidate?b.clearValidate():(o.row=null,(o.column=null,dl.nextTick)())};return E={insert(e){return s(e,null)},insertAt(e,t){return s(e,t)},insertNextAt(e,t){return s(e,t,!0)},insertChild(e,t){return l(e,t,null)},insertChildAt(e,t,r){return l(e,t,r)},insertChildNextAt(e,t,r){return l(e,t,r,!0)},remove(e){var t=C.treeConfig;let{mergeList:r,editStore:l,selectCheckboxMaps:o}=w,{tableFullTreeData:a,afterFullData:n,tableFullData:i}=y;var s=x.value,c=T.value;let{transform:d,mapChildrenField:u}=c,p=c.children||c.childrenField,{actived:h,removeMaps:m}=l,v=Object.assign({},l.insertMaps),f=Object.assign({},w.pendingRowMaps);c=s.checkField;let g=[];if(e?al().isArray(e)||(e=[e]):e=i,e.forEach(e=>{var t;b.isInsertByRow(e)||(t=xl(b,e),m[t]=e)}),!c){let t={...o};e.forEach(e=>{e=xl(b,e);t[e]&&delete t[e]}),w.selectCheckboxMaps=t}return i===e?(e=g=i.slice(0),y.tableFullData=[],y.afterFullData=[],b.clearMergeCells()):t&&d?e.forEach(e=>{let t=xl(b,e);var r=al().findTree(a,e=>t===xl(b,e),{children:u}),r=(r&&(r=r.items.splice(r.index,1),g.push(r[0])),al().findTree(a,e=>t===xl(b,e),{children:p})),r=(r&&r.items.splice(r.index,1),b.findRowIndexOf(n,e));-1<r&&n.splice(r,1)}):e.forEach(e=>{var t=b.findRowIndexOf(i,e);-1<t&&(t=i.splice(t,1),g.push(t[0]));let l=b.findRowIndexOf(n,e);-1<l&&(r.forEach(e=>{var{row:t,rowspan:r}=e;t>l?e.row=t-1:t+r>l&&(e.rowspan=r-1)}),n.splice(l,1))}),h.row&&-1<b.findRowIndexOf(e,h.row)&&E.clearEdit(),e.forEach(e=>{e=xl(b,e);v[e]&&delete v[e],f[e]&&delete f[e]}),l.insertMaps=v,w.pendingRowMaps=f,b.updateFooter(),b.cacheRowMap(!1),b.handleTableData(t&&d),t&&d||b.updateAfterDataIndex(),b.checkSelectionStatus(),w.scrollYLoad&&b.updateScrollYSpace(),(0,dl.nextTick)().then(()=>(b.updateCellAreas(),b.recalculate())).then(()=>({row:g.length?g[g.length-1]:null,rows:g}))},removeCheckboxRow(){return E.remove(b.getCheckboxRecords()).then(e=>(b.clearCheckboxRow(),e))},removeRadioRow(){var e=b.getRadioRecord();return E.remove(e||[]).then(e=>(b.clearRadioRow(),e))},removeCurrentRow(){var e=b.getCurrentRecord();return E.remove(e||[]).then(e=>(b.clearCurrentRow(),e))},getRecordset(){var e=E.getRemoveRecords(),t=b.getPendingRecords();let r=e.concat(t);var l=E.getUpdateRecords().filter(t=>!r.some(e=>b.eqRow(e,t)));return{insertRecords:E.getInsertRecords(),removeRecords:e,updateRecords:l,pendingRecords:t}},getInsertRecords(){var e=w.editStore;let r=y.fullAllDataRowIdData;e=e.insertMaps;let l=[];return al().each(e,(e,t)=>{r[t]&&l.push(e)}),l},getRemoveRecords(){var e=w.editStore,e=e.removeMaps;let t=[];return al().each(e,e=>{t.push(e)}),t},getUpdateRecords(){var{keepSource:e,treeConfig:t}=C,r=y.tableFullData,l=T.value;return e?(d(),t?al().filterTree(r,e=>b.isUpdateByRow(e),l):r.filter(e=>b.isUpdateByRow(e))):[]},getActiveRecord(){return Pr("vxe.error.delFunc",["getActiveRecord","getEditRecord"]),this.getEditRecord()},getEditRecord(){var e=w.editStore,t=y.afterFullData,r=o.value,{args:e,row:l}=e.actived;return e&&-1<b.findRowIndexOf(t,l)&&r.querySelectorAll(".vxe-body--column.col--active").length?Object.assign({},e):null},getSelectedCell(){var e=w.editStore,{args:e,column:t}=e.selected;return e&&t?Object.assign({},e):null},clearActived(e){return Pr("vxe.error.delFunc",["clearActived","clearEdit"]),this.clearEdit(e)},clearEdit(e){return g(null,e)},clearSelected(){var e=w.editStore,e=e.selected;return e.row=null,e.column=null,r(),(0,dl.nextTick)()},isActiveByRow(e){return Pr("vxe.error.delFunc",["isActiveByRow","isEditByRow"]),this.isEditByRow(e)},isEditByRow(e){var t=w.editStore;return t.actived.row===e},setActiveRow(e){return Pr("vxe.error.delFunc",["setActiveRow","setEditRow"]),E.setEditRow(e)},setEditRow(e,t){var r=y.visibleColumn;let l=al().find(r,e=>nl(e.editRender));return t&&(l=al().isString(t)?b.getColumnByField(t):t),b.setEditCell(e,l)},setActiveCell(e,t){return Pr("vxe.error.delFunc",["setActiveCell","setEditCell"]),E.setEditCell(e,t)},setEditCell(t,e){var r=C.editConfig;let l=al().isString(e)?b.getColumnByField(e):e;return t&&l&&nl(r)&&nl(l.editRender)?b.scrollToRow(t,l).then(()=>{var e=b.getCellElement(t,l);return e&&(v.handleEdit({row:t,rowIndex:b.getRowIndex(t),column:l,columnIndex:b.getColumnIndex(l),cell:e,$table:b}),y._lastCallTime=Date.now()),(0,dl.nextTick)()}):(0,dl.nextTick)()},setSelectCell(e,t){var r=w.tableData,l=m.value,t=al().isString(t)?b.getColumnByField(t):t;return e&&t&&"manual"!==l.trigger&&-1<(l=b.findRowIndexOf(r,e))&&t&&(r=b.getCellElement(e,t),e={row:e,rowIndex:l,column:t,columnIndex:b.getColumnIndex(t),cell:r},b.handleSelected(e,{})),(0,dl.nextTick)()}},v={handleEdit(r,l){var{editConfig:e,mouseConfig:o}=C,{editStore:a,tableColumn:n}=w,i=m.value,s=i.mode,{actived:a,focused:t}=a;let{row:c,column:d}=r;var u=d.editRender,p=r.cell||b.getCellElement(c,d),h=i.beforeEditMethod||i.activeMethod;if((r.cell=p)&&nl(e)&&nl(u)&&!b.isPendingByRow(c)){if(a.row!==c||"cell"===s&&a.column!==d){let t="edit-disabled";if(!h||h({...r,$table:b,$grid:b.xegrid})){o&&(E.clearSelected(),b.clearCellAreas)&&(b.clearCellAreas(),b.clearCopyCellArea()),b.closeTooltip(),a.column&&g(l),t="edit-activated",d.renderHeight=p.offsetHeight,a.args=r,a.row=c,a.column=d,"row"===s?n.forEach(e=>f(c,e)):f(c,d);let e=i.afterEditMethod;(0,dl.nextTick)(()=>{v.handleFocus(r,l),e&&e({...r,$table:b,$grid:b.xegrid})})}b.dispatchEvent(t,{row:c,rowIndex:b.getRowIndex(c),$rowIndex:b.getVMRowIndex(c),column:d,columnIndex:b.getColumnIndex(d),$columnIndex:b.getVMColumnIndex(d)},l),"edit-activated"===t&&b.dispatchEvent("edit-actived",{row:c,rowIndex:b.getRowIndex(c),$rowIndex:b.getVMRowIndex(c),column:d,columnIndex:b.getColumnIndex(d),$columnIndex:b.getVMColumnIndex(d)},l)}else{e=a.column;o&&(E.clearSelected(),b.clearCellAreas)&&(b.clearCellAreas(),b.clearCopyCellArea()),e!==d&&(u=e.model,u.update&&Cl(c,e,u.value),b.clearValidate)&&b.clearValidate(c,d),d.renderHeight=p.offsetHeight,a.args=r,a.column=d,setTimeout(()=>{v.handleFocus(r,l)})}t.column=null,t.row=null,b.focus()}return(0,dl.nextTick)()},handleActived(e,t){return v.handleEdit(e,t)},handleClearEdit:g,handleFocus(l){var{row:o,column:a,cell:n}=l,i=a.editRender,s=m.value;if(nl(i)){var c=Le.get(i.name);let e=i.autofocus||i.autoFocus,t=i.autoSelect||i.autoselect,r;s.autoFocus&&(!e&&c&&(e=c.tableAutoFocus||c.tableAutofocus||c.autofocus),!t&&c&&(t=c.tableAutoSelect||c.autoselect),al().isFunction(e)?r=e(l):e&&(r=!0===e?n.querySelector("input,textarea"):n.querySelector(e))&&r.focus()),r?t?r.select():zr.msie&&((i=r.createTextRange()).collapse(!1),i.select()):s.autoPos&&!a.fixed&&b.scrollToRow(o,a)}},handleSelected(e,t){var r=C.mouseConfig,l=w.editStore,o=u.value;let a=m.value,{actived:n,selected:i}=l,{row:s,column:c}=e,d=r&&o.selected;return!d||i.row===s&&i.column===c||(n.row!==s||"cell"===a.mode&&n.column!==c)&&(g(t),E.clearSelected(),b.clearCellAreas&&(b.clearCellAreas(),b.clearCopyCellArea()),i.args=e,i.row=s,i.column=c,d&&v.addCellSelectedClass(),b.focus(),t)&&b.dispatchEvent("cell-selected",e,t),(0,dl.nextTick)()},addCellSelectedClass(){var e=w.editStore,e=e.selected,{row:e,column:t}=e;r(),e&&t&&(e=b.getCellElement(e,t))&&hl(e,"col--selected")}},{...E,...v}},setupGrid(e){return e.extendTableMethods(_e)}}),'body{margin:0;padding: 0 1px;color:#333333;font-size:14px;font-family:"Microsoft YaHei",微软雅黑,"MicrosoftJhengHei",华文细黑,STHeiti,MingLiu}body *{-webkit-box-sizing:border-box;box-sizing:border-box}.vxe-table{border-collapse:collapse;text-align:left;border-spacing:0}.vxe-table:not(.is--print){table-layout:fixed}.vxe-table,.vxe-table th,.vxe-table td,.vxe-table td{border-color:#D0D0D0;border-style:solid;border-width:0}.vxe-table.is--print{width:100%}.border--default,.border--full,.border--outer{border-top-width:1px}.border--default,.border--full,.border--outer{border-left-width:1px}.border--outer,.border--default th,.border--default td,.border--full th,.border--full td,.border--outer th,.border--inner th,.border--inner td{border-bottom-width:1px}.border--default,.border--outer,.border--full th,.border--full td{border-right-width:1px}.border--default th,.border--full th,.border--outer th{background-color:#f8f8f9}.vxe-table td>div,.vxe-table th>div{padding:.5em .4em}.col--center{text-align:center}.col--right{text-align:right}.vxe-table:not(.is--print) .col--ellipsis>div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:break-all}.vxe-table--tree-node{text-align:left}.vxe-table--tree-node-wrapper{position:relative}.vxe-table--tree-icon-wrapper{position:absolute;top:50%;width:1em;height:1em;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer}.vxe-table--tree-unfold-icon,.vxe-table--tree-fold-icon{position:absolute;width:0;height:0;border-style:solid;border-width:.5em;border-right-color:transparent;border-bottom-color:transparent}.vxe-table--tree-unfold-icon{left:.3em;top:0;border-left-color:#939599;border-top-color:transparent}.vxe-table--tree-fold-icon{left:0;top:.3em;border-left-color:transparent;border-top-color:#939599}.vxe-table--tree-cell{display:block;padding-left:1.5em}.vxe-table input[type="checkbox"]{margin:0}.vxe-table input[type="checkbox"],.vxe-table input[type="radio"],.vxe-table input[type="checkbox"]+span,.vxe-table input[type="radio"]+span{vertical-align:middle;padding-left:0.4em}'),{getI18n:_,hooks:Ne,renderer:Pe}=ol.VxeUI,T,H="\r\n";function _r(e){return e.field||-1<["seq","checkbox","radio"].indexOf(e.type)}let Be=e=>{let t=[];return e.forEach(e=>{e.childNodes&&e.childNodes.length?(t.push(e),t.push(...Be(e.childNodes))):t.push(e)}),t};function Hr(e){return"TRUE"===e||"true"===e||!0===e}function Nr(e,t){let r=e.footerFilterMethod;return r?t.filter((e,t)=>r({items:e,$rowIndex:t})):t}function Kl(e){return/[",\s\n]/.test(e)?`"${e.replace(/"/g,'""')}"`:e}function Mt(e,t){return e.getElementsByTagName(t)}function Yl(e){return`#${e}@`+al().uniqueId()}function Gl(e,t){return e.replace(/#\d+@\d+/g,e=>al().hasOwnProp(t,e)?t[e]:e)}function Zl(e,t){return Gl(e,t).replace(/^"+$/g,e=>'"'.repeat(Math.ceil(e.length/2)))}function Ql(e,t){var{fieldMaps:e,titleMaps:r}=e;return e[t]||(e=r[t])&&e.field&&(t=e.field),t}function Jl(t,e,a){e=e.split(H);let n=[],i=[];if(e.length){let l={},o=Date.now();e.forEach(e=>{if(e){let r={};e=(e=e.replace(/("")|(\n)/g,(e,t)=>{var r=Yl(o);return l[r]=t?'"':"\n",r}).replace(/"(.*?)"/g,(e,t)=>{var r=Yl(o);return l[r]=Gl(t,l),r})).split(a);i.length?(e.forEach((e,t)=>{t<i.length&&(r[i[t]]=Zl(e.trim(),l))}),n.push(r)):i=e.map(e=>Ql(t,Zl(e.trim(),l)))}})}return{fields:i,rows:n}}function eo(e){al().eachTree(e,e=>{delete e._level,delete e._colSpan,delete e._rowSpan,delete e._children,delete e.childNodes},{children:"children"})}let ze=["exportData","importByFile","importData","saveFile","readFile","print","getPrintHtml","openImport","closeImport","openExport","closeExport","openPrint","closePrint"],{getConfig:je,validators:Ue,hooks:We}=(Ne.add("tableExportModule",{setupTable(S){let{props:I,reactData:D,internalData:M}=S,{computeTreeOpts:k,computePrintOpts:n,computeExportOpts:F,computeImportOpts:d,computeCustomOpts:O,computeSeqOpts:i,computeRadioOpts:l,computeCheckboxOpts:o,computeColumnOpts:s}=S.getComputeMaps(),A=(0,dl.inject)("$xeGrid",null),f=e=>{var t=k.value,t=t.children||t.childrenField;return e[t]&&e[t].length},g=(e,t,r,l,o)=>{var a=i.value.seqMethod||l.seqMethod;return a?a({row:t,rowIndex:S.getRowIndex(t),$rowIndex:r,column:l,columnIndex:S.getColumnIndex(l),$columnIndex:o}):e};function L(e,t){var r=s.value,r=t.headerExportMethod||r.headerExportMethod;return r?r({column:t,options:e,$table:S}):(e.original?t.field:t.getTitle())||""}let x=e=>al().isBoolean(e)?e?"TRUE":"FALSE":e,b=e=>cl(e)?"":""+e,c=(d,u,e)=>{let{isAllExpand:n,mode:p}=d;var t=I.treeConfig;let h=l.value,m=o.value;var r=k.value;let v=s.value;if(T=T||document.createElement("div"),t){t=r.children||r.childrenField;let o=[],a=new Map;return al().eachTree(e,(e,i,t,s,r,l)=>{let c=e._row||e;e=r&&r._row?r._row:r;if(n||!e||a.has(e)&&S.isTreeExpandByRow(e)){r=f(c);let n={_row:c,_level:l.length-1,_hasChild:r,_expand:r&&S.isTreeExpandByRow(c)};u.forEach((e,t)=>{let r="";var l=e.editRender||e.cellRender;let o=e.exportMethod||v.exportMethod;if(o=(o=!o&&l&&l.name&&(l=Pe.get(l.name))?l.tableExportMethod||l.exportMethod:o)||v.exportMethod)r=o({$table:S,row:c,column:e,options:d});else switch(e.type){case"seq":var a=s.map((e,t)=>t%2==0?Number(e)+1:".").join("");r="all"===p?a:g(a,c,i,e,t);break;case"checkbox":r=x(S.isCheckedByCheckboxRow(c)),n._checkboxLabel=m.labelField?al().get(c,m.labelField):"",n._checkboxDisabled=m.checkMethod&&!m.checkMethod({row:c});break;case"radio":r=x(S.isCheckedByRadioRow(c)),n._radioLabel=h.labelField?al().get(c,h.labelField):"",n._radioDisabled=h.checkMethod&&!h.checkMethod({row:c});break;default:d.original?r=bl(c,e):(r=S.getCellLabel(c,e),"html"===e.type?(T.innerHTML=r,r=T.innerText.trim()):(a=S.getCellElement(c,e))&&(r=a.innerText.trim()))}n[e.id]=b(r)}),a.set(c,1),o.push(Object.assign(n,c))}},{children:t}),o}return e.map((n,i)=>{let s={_row:n};return u.forEach((e,t)=>{let r="";var l=e.editRender||e.cellRender;let o=e.exportMethod||v.exportMethod;if(o=!o&&l&&l.name&&(l=Pe.get(l.name))?l.tableExportMethod||l.exportMethod:o)r=o({$table:S,row:n,column:e,options:d});else switch(e.type){case"seq":var a=i+1;r="all"===p?a:g(a,n,i,e,t);break;case"checkbox":r=x(S.isCheckedByCheckboxRow(n)),s._checkboxLabel=m.labelField?al().get(n,m.labelField):"",s._checkboxDisabled=m.checkMethod&&!m.checkMethod({row:n});break;case"radio":r=x(S.isCheckedByRadioRow(n)),s._radioLabel=h.labelField?al().get(n,h.labelField):"",s._radioDisabled=h.checkMethod&&!h.checkMethod({row:n});break;default:d.original?r=bl(n,e):(r=S.getCellLabel(n,e),"html"===e.type?(T.innerHTML=r,r=T.innerText.trim()):(a=S.getCellElement(n,e))&&(r=a.innerText.trim()))}s[e.id]=b(r)}),s})},V=(e,t,r)=>{var l=s.value,o=r.editRender||r.cellRender;let a=r.footerExportMethod;a=(a=!a&&o&&o.name&&(o=Pe.get(o.name))?o.tableFooterExportMethod||o.footerExportMethod:a)||l.footerExportMethod;o=S.getVTColumnIndex(r);return a?a({$table:S,items:t,itemIndex:o,row:t,_columnIndex:o,column:r,options:e}):al().isArray(t)?al().toValueString(t[o]):al().get(t,r.field)},u=(r,e,t)=>{let l="\ufeff";return r.isHeader&&(l+=e.map(e=>Kl(L(r,e))).join(",")+H),t.forEach(t=>{l+=e.map(e=>Kl(((e,t)=>{if(t){if("seq"===e.type)return`	`+t;switch(e.cellType){case"string":if(isNaN(t))break;return`	`+t;case"number":break;default:if(12<=t.length&&!isNaN(t))return`	`+t}}return t})(e,t[e.id]))).join(",")+H}),r.isFooter&&(t=D.footerTableData,Nr(r,t).forEach(t=>{l+=e.map(e=>Kl(V(r,t,e))).join(",")+H})),l},p=(r,e,t)=>{let l="";return r.isHeader&&(l+=e.map(e=>Kl(L(r,e))).join("\t")+H),t.forEach(t=>{l+=e.map(e=>Kl(t[e.id])).join("\t")+H}),r.isFooter&&(t=D.footerTableData,Nr(r,t).forEach(t=>{l+=e.map(e=>Kl(V(r,t,e))).join("\t")+H})),l},$=(e,t,r)=>{e=e[t],t=al().isUndefined(e)||al().isNull(e)?r:e;let l="title"===t||(!0===t||"tooltip"===t)||"ellipsis"===t;var{scrollXLoad:r,scrollYLoad:e}=D;return l=r||e?l||!0:l},h=(i,e,t)=>{let{id:c,border:r,treeConfig:l,headerAlign:s,align:d,footerAlign:a,showOverflow:u,showHeaderOverflow:p}=I,{isAllSelected:h,isIndeterminate:o,mergeList:m}=D,n=k.value,{print:v,isHeader:f,isFooter:g,isColgroup:x,isMerge:b,colgroups:C,original:w}=i,y="check-all";let T=[`<table class="${["vxe-table","border--"+(!0===(R=r)?"full":R||"default"),v?"is--print":"",f?"is--header":""].filter(e=>e).join(" ")}" border="0" cellspacing="0" cellpadding="0">`,`<colgroup>${e.map(e=>`<col style="width:${e.renderWidth}px">`).join("")}</colgroup>`];f&&(T.push("<thead>"),x&&!w?C.forEach(e=>{T.push(`<tr>${e.map(t=>{var e=t.headerAlign||t.align||s||d,r=$(t,"showHeaderOverflow",p)?["col--ellipsis"]:[],l=L(i,t);let o=0,a=0;al().eachTree([t],e=>{e.childNodes&&t.childNodes.length||a++,o+=e.renderWidth},{children:"childNodes"});var n=o-a;return e&&r.push("col--"+e),"checkbox"===t.type?`<th class="${r.join(" ")}" colspan="${t._colSpan}" rowspan="${t._rowSpan}"><div ${v?"":`style="width: ${n}px"`}><input type="checkbox" class="${y}" ${h?"checked":""}><span>${l}</span></div></th>`:`<th class="${r.join(" ")}" colspan="${t._colSpan}" rowspan="${t._rowSpan}" title="${l}"><div ${v?"":`style="width: ${n}px"`}><span>${sl(l,!0)}</span></div></th>`}).join("")}</tr>`)}):T.push(`<tr>${e.map(e=>{var t=e.headerAlign||e.align||s||d,r=$(e,"showHeaderOverflow",p)?["col--ellipsis"]:[],l=L(i,e);return t&&r.push("col--"+t),"checkbox"===e.type?`<th class="${r.join(" ")}"><div ${v?"":`style="width: ${e.renderWidth}px"`}><input type="checkbox" class="${y}" ${h?"checked":""}><span>${l}</span></div></th>`:`<th class="${r.join(" ")}" title="${l}"><div ${v?"":`style="width: ${e.renderWidth}px"`}><span>${sl(l,!0)}</span></div></th>`}).join("")}</tr>`),T.push("</thead>")),t.length&&(T.push("<tbody>"),l?t.forEach(o=>{T.push("<tr>"+e.map(t=>{var e=t.align||d,r=$(t,"showOverflow",u)?["col--ellipsis"]:[],l=o[t.id];if(e&&r.push("col--"+e),t.treeNode){let e="";return o._hasChild&&(e=`<i class="${o._expand?"vxe-table--tree-fold-icon":"vxe-table--tree-unfold-icon"}"></i>`),r.push("vxe-table--tree-node"),"radio"===t.type?`<td class="${r.join(" ")}" title="${l}"><div ${v?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${o._level*n.indent}px"><div class="vxe-table--tree-icon-wrapper">${e}</div><div class="vxe-table--tree-cell"><input type="radio" name="radio_${c}" ${o._radioDisabled?"disabled ":""}${Hr(l)?"checked":""}><span>${o._radioLabel}</span></div></div></div></td>`:"checkbox"===t.type?`<td class="${r.join(" ")}" title="${l}"><div ${v?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${o._level*n.indent}px"><div class="vxe-table--tree-icon-wrapper">${e}</div><div class="vxe-table--tree-cell"><input type="checkbox" ${o._checkboxDisabled?"disabled ":""}${Hr(l)?"checked":""}><span>${o._checkboxLabel}</span></div></div></div></td>`:`<td class="${r.join(" ")}" title="${l}"><div ${v?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${o._level*n.indent}px"><div class="vxe-table--tree-icon-wrapper">${e}</div><div class="vxe-table--tree-cell">${l}</div></div></div></td>`}return"radio"===t.type?`<td class="${r.join(" ")}"><div ${v?"":`style="width: ${t.renderWidth}px"`}><input type="radio" name="radio_${c}" ${o._radioDisabled?"disabled ":""}${Hr(l)?"checked":""}><span>${o._radioLabel}</span></div></td>`:"checkbox"===t.type?`<td class="${r.join(" ")}"><div ${v?"":`style="width: ${t.renderWidth}px"`}><input type="checkbox" ${o._checkboxDisabled?"disabled ":""}${Hr(l)?"checked":""}><span>${o._checkboxLabel}</span></div></td>`:`<td class="${r.join(" ")}" title="${l}"><div ${v?"":`style="width: ${t.renderWidth}px"`}>${sl(l,!0)}</div></td>`}).join("")+"</tr>")}):t.forEach(s=>{T.push("<tr>"+e.map(e=>{var t=e.align||d,r=$(e,"showOverflow",u)?["col--ellipsis"]:[],l=s[e.id];let o=1,a=1;if(b&&m.length){var n=S.getVTRowIndex(s._row),i=S.getVTColumnIndex(e),n=Sr(m,n,i);if(n){var{rowspan:i,colspan:n}=n;if(!i||!n)return"";1<i&&(o=i),1<n&&(a=n)}}return t&&r.push("col--"+t),"radio"===e.type?`<td class="${r.join(" ")}" rowspan="${o}" colspan="${a}"><div ${v?"":`style="width: ${e.renderWidth}px"`}><input type="radio" name="radio_${c}" ${s._radioDisabled?"disabled ":""}${Hr(l)?"checked":""}><span>${s._radioLabel}</span></div></td>`:"checkbox"===e.type?`<td class="${r.join(" ")}" rowspan="${o}" colspan="${a}"><div ${v?"":`style="width: ${e.renderWidth}px"`}><input type="checkbox" ${s._checkboxDisabled?"disabled ":""}${Hr(l)?"checked":""}><span>${s._checkboxLabel}</span></div></td>`:`<td class="${r.join(" ")}" rowspan="${o}" colspan="${a}" title="${l}"><div ${v?"":`style="width: ${e.renderWidth}px"`}>${sl(l,!0)}</div></td>`}).join("")+"</tr>")}),T.push("</tbody>")),g&&(R=D.footerTableData,(t=Nr(i,R)).length)&&(T.push("<tfoot>"),t.forEach(o=>{T.push(`<tr>${e.map(e=>{var t=e.footerAlign||e.align||a||d,r=$(e,"showOverflow",u)?["col--ellipsis"]:[],l=V(i,o,e);return t&&r.push("col--"+t),`<td class="${r.join(" ")}" title="${l}"><div ${v?"":`style="width: ${e.renderWidth}px"`}>${sl(l,!0)}</div></td>`}).join("")}</tr>`)}),T.push("</tfoot>"));var E,R=!h&&o?`<script>(function(){var a=document.querySelector(".${y}");if(a){a.indeterminate=true}})()</script>`:"";return T.push("</table>",R),(v?T:(t=i,R=T.join(""),E=t.style,["<!DOCTYPE html><html>","<head>",'<meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,minimal-ui">',`<title>${t.sheetName}</title>`,'<style media="print">.vxe-page-break-before{page-break-before:always;}.vxe-page-break-after{page-break-after:always;}</style>',`<style>${He}</style>`,E?`<style>${E}</style>`:"","</head>",`<body>${R}</body>`,"</html>"])).join("")},m=(r,e,t)=>{let l=['<?xml version="1.0"?>','<?mso-application progid="Excel.Sheet"?>','<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">','<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">',"<Version>16.00</Version>","</DocumentProperties>",'<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">',"<WindowHeight>7920</WindowHeight>","<WindowWidth>21570</WindowWidth>","<WindowTopX>32767</WindowTopX>","<WindowTopY>32767</WindowTopY>","<ProtectStructure>False</ProtectStructure>","<ProtectWindows>False</ProtectWindows>","</ExcelWorkbook>",`<Worksheet ss:Name="${r.sheetName}">`,"<Table>",e.map(e=>`<Column ss:Width="${e.renderWidth}"/>`).join("")].join("");return r.isHeader&&(l+=`<Row>${e.map(e=>`<Cell><Data ss:Type="String">${L(r,e)}</Data></Cell>`).join("")}</Row>`),t.forEach(t=>{l+="<Row>"+e.map(e=>`<Cell><Data ss:Type="String">${t[e.id]}</Data></Cell>`).join("")+"</Row>"}),r.isFooter&&(t=D.footerTableData,Nr(r,t).forEach(t=>{l+=`<Row>${e.map(e=>`<Cell><Data ss:Type="String">${V(r,t,e)}</Data></Cell>`).join("")}</Row>`})),l+"</Table></Worksheet></Workbook>"},v=(e,t)=>{var{filename:r,type:l,download:o}=e;if(!o)return o=new Blob([t],{type:`text/${e.type};charset=utf-8;`}),Promise.resolve({type:l,content:t,blob:o});ol.VxeUI.saveFile&&ol.VxeUI.saveFile({filename:r,type:l,content:t}).then(()=>{!1!==e.message&&ol.VxeUI.modal&&ol.VxeUI.modal.message({content:_("vxe.table.expSuccess"),status:"success"})})},y=r=>{let{remote:l,columns:o,colgroups:a,exportMethod:n,afterExportMethod:t}=r;return new Promise(t=>{if(l){var e={options:r,$table:S,$grid:A};t(n?n(e):e)}else{let e=(e=>{let{columns:t,dataFilterMethod:r}=e,l=e.data;return r&&(l=l.filter((e,t)=>r({row:e,$rowIndex:t}))),c(e,t,l)})(r);t(S.preventEvent(null,"event.export",{options:r,columns:o,colgroups:a,datas:e},()=>v(r,((e,t,r)=>{if(t.length)switch(e.type){case"csv":return u(e,t,r);case"txt":return p(e,t,r);case"html":return h(e,t,r);case"xml":return m(e,t,r)}return""})(r,o,e))))}}).then(e=>(eo(o),r.print||t&&t({status:!0,options:r,$table:S,$grid:A}),Object.assign({status:!0},e))).catch(()=>{eo(o),r.print||t&&t({status:!1,options:r,$table:S,$grid:A});return Promise.reject({status:!1})})},C=(e,r)=>{let{tableFullColumn:t,_importResolve:l,_importReject:o}=M,a={fields:[],rows:[]},n={},i={};t.forEach(e=>{var t=e.field,r=e.getTitle();t&&(n[t]=e),r&&(i[e.getTitle()]=e)});var s={fieldMaps:n,titleMaps:i};switch(r.type){case"csv":a=Jl(s,e,",");break;case"txt":a=Jl(s,e,"\t");break;case"html":a=((t,e)=>{var r,e=Mt((new DOMParser).parseFromString(e,"text/html"),"body");let l=[],o=[];return e.length&&(e=Mt(e[0],"table")).length&&(r=Mt(e[0],"thead")).length&&(al().arrayEach(Mt(r[0],"tr"),e=>{al().arrayEach(Mt(e,"th"),e=>{o.push(Ql(t,e.textContent||""))})}),(r=Mt(e[0],"tbody")).length)&&al().arrayEach(Mt(r[0],"tr"),e=>{let r={};al().arrayEach(Mt(e,"td"),(e,t)=>{o[t]&&(r[o[t]]=e.textContent||"")}),l.push(r)}),{fields:o,rows:l}})(s,e);break;case"xml":a=((t,e)=>{var e=Mt((new DOMParser).parseFromString(e,"application/xml"),"Worksheet");let l=[],o=[];return e.length&&(e=Mt(e[0],"Table")).length&&(e=Mt(e[0],"Row")).length&&(al().arrayEach(Mt(e[0],"Cell"),e=>{o.push(Ql(t,e.textContent||""))}),al().arrayEach(e,(e,t)=>{if(t){let r={};t=Mt(e,"Cell");al().arrayEach(t,(e,t)=>{o[t]&&(r[o[t]]=e.textContent)}),l.push(r)}})),{fields:o,rows:l}})(s,e)}let{fields:c,rows:d}=a;c.some(e=>n[e]||i[e])?S.createData(d).then(e=>{let t;return"insert"!==r.mode&&"insertBottom"!==r.mode||(t=S.insertAt(e,-1)),t="insertTop"===r.mode?S.insert(e):S.reloadData(e),!1!==r.message&&ol.VxeUI.modal&&ol.VxeUI.modal.message({content:_("vxe.table.impSuccess",[d.length]),status:"success"}),t.then(()=>{l&&l({status:!0})})}):!1!==r.message&&(ol.VxeUI.modal&&ol.VxeUI.modal.message({content:_("vxe.error.impFields"),status:"error"}),o)&&o({status:!1})},a=(a,n)=>{let{importMethod:i,afterImportMethod:t}=n,{type:s,filename:c}=dr(a);var e=d.value;return i||al().includes(al().keys(e._typeMaps),s)?new Promise((t,r)=>{let e=e=>{t(e),M._importResolve=null,M._importReject=null},l=e=>{r(e),M._importResolve=null,M._importReject=null};if(M._importResolve=e,M._importReject=l,window.FileReader){let t=Object.assign({mode:"insertTop"},n,{type:s,filename:c});var o;t.remote?i?Promise.resolve(i({file:a,options:t,$table:S})).then(()=>{e({status:!0})}).catch(()=>{e({status:!0})}):e({status:!0}):(o=M.tableFullColumn,S.preventEvent(null,"event.import",{file:a,options:t,columns:o},()=>{var e=new FileReader;e.onerror=()=>{Br("vxe.error.notType",[s]),l({status:!1})},e.onload=e=>{C(e.target.result,t)},e.readAsText(a,t.encoding||"UTF-8")}))}else Br("vxe.error.notExp"),e({status:!0})}).then(()=>{t&&t({status:!0,options:n,$table:S})}).catch(e=>(t&&t({status:!1,options:n,$table:S}),Promise.reject(e))):(!1!==n.message&&ol.VxeUI.modal&&ol.VxeUI.modal.message({content:_("vxe.error.notType",[s]),status:"error"}),Promise.reject({status:!1}))},r=(e,t)=>{var{treeConfig:r,showHeader:l,showFooter:o}=I,{initStore:a,mergeList:n,mergeFooterList:i,isGroup:s,footerTableData:c,exportStore:d,exportParams:u}=D,p=M.collectColumn,h=F.value,m=O.value,v=S.getCheckboxRecords(),f=A?A.getComputeMaps().computeProxyOpts.value:{},c=!!c.length,n=!(!n.length&&!i.length);let g=Object.assign({message:!0,isHeader:l,isFooter:o,isColgroup:s,isMerge:n,useStyle:!0,current:"current",modes:["current","selected"].concat(f.ajax&&f.ajax.queryAll?["all"]:[])},e);i=g.types||al().keys(h._typeMaps),l=g.modes||[];let x=m.checkMethod;o=p.slice(0);let{columns:b,excludeFields:C,includeFields:w}=g;f=i.map(e=>({value:e,label:_("vxe.export.types."+e)})),e=l.map(e=>e&&e.value?{value:e.value,label:e.label||e.value}:{value:e,label:_("vxe.export.modes."+e)});al().eachTree(o,(e,t,r,l,o)=>{var a,n,i,s,c,d=e.children&&e.children.length;let u=!1;u=b&&b.length?(g,c=e,b.some(e=>{var t,r;return yr(e)?c.id===e.id:al().isString(e)?c.field===e:(t=e.id||e.colId,r=e.type,e=e.field,t?c.id===t:e&&r?c.field===e&&c.type===r:e?c.field===e:!!r&&c.type===r)})):C||w?(a=g,n=e,i=w,(!(s=C)||!al().includes(s,n.field))&&(i?!!al().includes(i,n.field):a.original?n.field:_r(n))):e.visible&&(d||_r(e)),e.checked=u,e.halfChecked=!1,e.disabled=o&&o.disabled||!!x&&!x({column:e})}),Object.assign(d,{columns:o,typeList:f,modeList:e,hasFooter:c,hasMerge:n,hasTree:r,isPrint:t,hasColgroup:s,visible:!0}),Object.assign(u,{mode:v.length?"selected":"current"},g);let{filename:y,sheetName:T,mode:E,type:R}=u;return y&&(al().isFunction(y)?u.filename=y({options:g,$table:S,$grid:A}):u.filename=""+y),T&&(al().isFunction(T)?u.sheetName=T({options:g,$table:S,$grid:A}):u.sheetName=""+T),e.some(e=>e.value===E)||(u.mode=e[0].value),f.some(e=>e.value===R)||(u.type=f[0].value),a.export=!0,(0,dl.nextTick)()};var e=()=>ol.VxeUI.modal?ol.VxeUI.modal.close("VXE_EXPORT_MODAL"):Promise.resolve();let w={exportData(i){var e=I.treeConfig,{isGroup:r,tableGroupColumn:s}=D;let{tableFullColumn:a,afterFullData:t}=M;var c=F.value,l=k.value,i=Object.assign({isHeader:!0,isFooter:!0,isColgroup:!0,download:!0,type:"csv",mode:"current"},c,i);let{filename:o,sheetName:n,type:d,mode:u,columns:p,original:h,columnFilterMethod:m,beforeExportMethod:v,includeFields:f,excludeFields:g}=i,x=[];var b=p&&p.length?p:null;let C=Object.assign({},i,{filename:"",sheetName:""}),w=(b||m||(C.columnFilterMethod=({column:e})=>(!g||!al().includes(g,e.field))&&(f?!!al().includes(f,e.field):h?e.field:_r(e))),x=b?(C._isCustomColumn=!0,al().searchTree(al().mapTree(b,e=>{let l;if(e){if(yr(e))l=e;else if(al().isString(e))l=S.getColumnByField(e);else{var o=e.id||e.colId;let t=e.type,r=e.field;o?l=S.getColumnById(o):r&&t?l=a.find(e=>e.field===r&&e.type===t):r?l=S.getColumnByField(r):t&&(l=a.find(e=>e.type===t))}return l||{}}},{children:"childNodes",mapChildren:"_children"}),(e,t)=>yr(e)&&(!m||m({column:e,$columnIndex:t})),{children:"_children",mapChildren:"childNodes",original:!0})):al().searchTree(r?s:a,(e,t)=>e.visible&&(!m||m({column:e,$columnIndex:t})),{children:"children",mapChildren:"childNodes",original:!0}),[]);if(al().eachTree(x,e=>{e.children&&e.children.length||w.push(e)},{children:"childNodes"}),C.columns=w,C.colgroups=(e=>{let t=1,l=(r,e)=>{if(e&&(r._level=e._level+1,t<r._level)&&(t=r._level),r.childNodes&&r.childNodes.length){let t=0;r.childNodes.forEach(e=>{l(e,r),t+=e._colSpan}),r._colSpan=t}else r._colSpan=1},r=(e.forEach(e=>{e._level=1,l(e)}),[]);for(let e=0;e<t;e++)r.push([]);return Be(e).forEach(e=>{e.childNodes&&e.childNodes.length?e._rowSpan=1:e._rowSpan=t-e._level+1,r[e._level-1].push(e)}),r})(x),o&&(al().isFunction(o)?C.filename=o({options:i,$table:S,$grid:A}):C.filename=""+o),C.filename||(C.filename=_(C.original?"vxe.table.expOriginFilename":"vxe.table.expFilename",[al().toDateString(Date.now(),"yyyyMMddHHmmss")])),n&&(al().isFunction(n)?C.sheetName=n({options:i,$table:S,$grid:A}):C.sheetName=""+n),C.sheetName||(C.sheetName=document.title||""),!C.exportMethod&&!al().includes(al().keys(c._typeMaps),d))return Br("vxe.error.notType",[d]),["xlsx","pdf"].includes(d)&&Pr("vxe.error.reqPlugin",[4,"plugin-export-xlsx"]),Promise.reject({status:!1});if(C.print||v&&v({options:C,$table:S,$grid:A}),!C.data)if(C.data=[],"selected"===u){let t=S.getCheckboxRecords();-1<["html","pdf"].indexOf(d)&&e?C.data=al().searchTree(S.getTableData().fullData,e=>-1<S.findRowIndexOf(t,e),Object.assign({},l,{data:"_row"})):C.data=t}else if("all"===u){if(A||Pr("vxe.error.errProp",["all","mode=current,selected"]),A&&!C.remote){b=A.reactData,r=A.getComputeMaps().computeProxyOpts,s=r.value,i=b.sortData;let{beforeQueryAll:e,afterQueryAll:l,ajax:t={}}=s,o=s.response||s.props||{};c=t.queryAll;let a=t.queryAllSuccess,n=t.queryAllError;if(c||Pr("vxe.error.notFunc",["proxy-config.ajax.queryAll"]),c){let r={$table:S,$grid:A,sort:i.length?i[0]:{},sorts:i,filters:b.filterData,form:b.formData,options:C};return Promise.resolve((e||c)(r)).then(e=>{var t=o.list;return C.data=(t?al().isFunction(t)?t({data:e,$grid:A}):al().get(e,t):e)||[],l&&l(r),a&&a({...r,response:e}),y(C)}).catch(e=>{n&&n({...r,response:e})})}}}else"current"===u&&(C.data=t);return y(C)},importByFile(e,t){var t=Object.assign({},t),r=t.beforeImportMethod;return r&&r({options:t,$table:S}),a(e,t)},importData(e){var t=d.value;let r=Object.assign({types:al().keys(t._typeMaps)},t,e),{beforeImportMethod:l,afterImportMethod:o}=r;return l&&l({options:r,$table:S}),ol.VxeUI.readFile(r).catch(e=>(o&&o({status:!1,options:r,$table:S}),Promise.reject(e))).then(e=>{e=e.file;return a(e,r)})},saveFile(e){return ol.VxeUI.saveFile(e)},readFile(e){return ol.VxeUI.readFile(e)},print(e){var t=n.value;let r=Object.assign({original:!1},t,e,{type:"html",download:!1,remote:!1,print:!0});t=r.sheetName;let l="",o=(l=(l=t?al().isFunction(t)?t({options:r,$table:S,$grid:A}):""+t:l)||document.title||"",r.beforePrintMethod),a=r.html||r.content;return new Promise((e,t)=>{ol.VxeUI.print?a?e(ol.VxeUI.print({title:l,html:a,customStyle:r.style,beforeMethod:o?({html:e})=>o({html:e,content:e,options:r,$table:S}):void 0})):e(w.exportData(r).then(({content:e})=>ol.VxeUI.print({title:l,html:e,customStyle:r.style,beforeMethod:o?({html:e})=>o({html:e,content:e,options:r,$table:S}):void 0}))):t({status:!1})})},getPrintHtml(e){var t=n.value,t=Object.assign({original:!1},t,e,{type:"html",download:!1,remote:!1,print:!0});return S.exportData(t).then(({content:e})=>({html:e}))},closeImport(){return ol.VxeUI.modal?ol.VxeUI.modal.close("VXE_IMPORT_MODAL"):Promise.resolve()},openImport(e){var{treeConfig:t,importConfig:r}=I;let{initStore:l,importStore:o,importParams:a}=D;var n=d.value,n=Object.assign({mode:"insertTop",message:!0,types:al().keys(n._typeMaps),modes:["insertTop","covering"]},n,e),e=n.types||[],i=n.modes||[];!t?(r||Br("vxe.error.reqProp",["import-config"]),t=e.map(e=>({value:e,label:_("vxe.export.types."+e)})),r=i.map(e=>e&&e.value?{value:e.value,label:e.label||e.value}:{value:e,label:_("vxe.import.modes."+e)}),Object.assign(o,{file:null,type:"",filename:"",modeList:r,typeList:t,visible:!0}),Object.assign(a,n),r.some(e=>e.value===a.mode)||(a.mode=r[0].value),l.import=!0):n.message&&ol.VxeUI.modal&&ol.VxeUI.modal.message({content:_("vxe.error.treeNotImp"),status:"error"})},closeExport:e,openExport(e){var t=F.value,t=Object.assign({message:!0,types:al().keys(t._typeMaps)},t,e);I.exportConfig||Br("vxe.error.reqProp",["export-config"]),r(t)},closePrint:e,openPrint(e){var t=n.value,t=Object.assign({message:!0},t,e);I.printConfig||Br("vxe.error.reqProp",["print-config"]),r(t,!0)}};return w},setupGrid(e){return e.extendTableMethods(ze)}}),(ur=ol.VxeUI.hooks).add("tableKeyboardModule",{setupTable(D){let{props:M,reactData:k,internalData:F}=D,O=D.getRefMaps().refElem,{computeEditOpts:p,computeCheckboxOpts:s,computeMouseOpts:c,computeTreeOpts:d,computeRowOpts:A,computeCellOpts:L,computeDefaultRowHeight:V}=D.getComputeMaps();let u=(e,I)=>{var t=F.elemStore,r=wl(t["main-body-scroll"]),l=wl(t["left-body-scroll"]),t=wl(t["right-body-scroll"]),{column:o,cell:a}=I;if("checkbox"===o.type){let S=r;if(l&&"left"===o.fixed?S=l:t&&"right"===o.fixed&&(S=t),S){let t=O.value,i=e.clientX,s=e.clientY,c=S.querySelector(".vxe-table--checkbox-range"),d=a.parentElement,u=D.getCheckboxRecords(),p=[],h=1;r=((e,t)=>{let r=0,l=0;var o,a,n=!zr.firefox&&ul(e,"vxe-checkbox--label");for(n&&(o=getComputedStyle(e),r-=al().toNumber(o.paddingTop),l-=al().toNumber(o.paddingLeft));e&&e!==t;)r+=e.offsetTop,l+=e.offsetLeft,e=e.offsetParent,n&&(a=getComputedStyle(e),r-=al().toNumber(a.paddingTop),l-=al().toNumber(a.paddingLeft));return{offsetTop:r,offsetLeft:l}})(e.target,S);let m=r.offsetTop+e.offsetY,v=r.offsetLeft+e.offsetX,f=S.scrollTop,n=d.offsetHeight,g=d.getBoundingClientRect(),x=s-g.y,b=null,C=!1,w=1,y=(e,t)=>{D.dispatchEvent("checkbox-range-"+e,{records:D.getCheckboxRecords(),reserves:D.getCheckboxReserveRecords()},t)},T=e=>{var{clientX:t,clientY:r}=e,t=t-i,r=r-s+(S.scrollTop-f);let l=Math.abs(r),o=Math.abs(t),a=m,n=v;r<h?(a+=r)<h&&(a=h,l=m):l=Math.min(l,S.scrollHeight-m-h),t<h?(n+=t,o>v&&(n=h,o=v)):o=Math.min(o,S.clientWidth-v-h),c.style.height=l+"px",c.style.width=o+"px",c.style.left=n+"px",c.style.top=a+"px",c.style.display="block";t=((e,t,r,l,o)=>{var a=M.showOverflow,{fullAllDataRowIdData:n,isResizeCellHeight:i}=F,s=A.value,c=L.value,d=V.value,e=e.row;let u=0,p=[],h=0;var m=0<o,v=k.scrollYLoad,f=F.afterFullData;if(h=m?l+o:r.height-l+Math.abs(o),v){r=D.getVTRowIndex(e);if(!(i||c.height||s.height)&&a)p=m?f.slice(r,r+Math.ceil(h/d)):f.slice(r-Math.floor(h/d),r+1);else if(m)for(let e=r;e<f.length;e++){var g=f[e],x=n[D.getRowid(g)]||{};if(u+=x.resizeHeight||c.height||s.height||d,p.push(g),u>h)return p}else for(let e=r;0<=e;e--){var b=f[e],C=n[D.getRowid(b)]||{};if(u+=C.resizeHeight||c.height||s.height||d,p.push(b),u>h)return p}}else for(var w=m?"next":"previous";t&&u<h;){var y=D.getRowNode(t);y&&(p.push(y.item),u+=t.offsetHeight,t=t[w+"ElementSibling"])}return p})(I,d,g,x,r<h?-l:l);10<l&&t.length!==p.length&&(p=t,e.ctrlKey?t.forEach(e=>{D.handleBatchSelectRows([e],-1===u.indexOf(e))}):(D.setAllCheckboxRow(!1),D.handleCheckedCheckboxRow(t,!0,!1)),y("change",e))},E=()=>{clearTimeout(b),b=null},R=a=>{E(),b=setTimeout(()=>{var e,t,r,l,o;b&&({scrollLeft:e,scrollTop:t,clientHeight:r,scrollHeight:l}=S,o=Math.ceil(50*w/n),C?t+r<l?(D.scrollTo(e,t+o),R(a),T(a)):E():t?(D.scrollTo(e,t-o),R(a),T(a)):E())},50)};hl(t,"drag--range"),document.onmousemove=e=>{e.preventDefault(),e.stopPropagation();var t=e.clientY,r=gr(S).boundingTop;t<r?(C=!1,w=r-t,b||R(e)):t>r+S.clientHeight?(C=!0,w=t-r-S.clientHeight,b||R(e)):b&&E(),T(e)},document.onmouseup=e=>{E(),pl(t,"drag--range"),c.removeAttribute("style"),document.onmousemove=null,document.onmouseup=null,y("end",e)},y("start",e)}}};return{moveTabSelected(e,t,r){var l=M.editConfig,{afterFullData:o,visibleColumn:a}=F,n=p.value;let i,s,c,d=Object.assign({},e);var e=D.getVTRowIndex(d.row),u=D.getVTColumnIndex(d.column),t=(r.preventDefault(),t?u<=0?0<e&&(s=e-1,i=o[s],c=a.length-1):c=u-1:u>=a.length-1?e<o.length-1&&(s=e+1,i=o[s],c=0):c=u+1,a[c]);t&&(i?(d.rowIndex=s,d.row=i):d.rowIndex=e,d.columnIndex=c,d.column=t,d.cell=D.getCellElement(d.row,d.column),l?"click"!==n.trigger&&"dblclick"!==n.trigger||("row"===n.mode?D.handleEdit(d,r):D.scrollToRow(d.row,d.column).then(()=>D.handleSelected(d,r))):D.scrollToRow(d.row,d.column).then(()=>D.handleSelected(d,r)))},moveCurrentRow(e,t,r){var l=M.treeConfig;let o=k.currentRow;var a=F.afterFullData,n=d.value,n=n.children||n.childrenField;let i;if(r.preventDefault(),o?l?({index:l,items:n}=al().findTree(a,e=>e===o,{children:n}),e&&0<l?i=n[l-1]:t&&l<n.length-1&&(i=n[l+1])):(n=D.getVTRowIndex(o),e&&0<n?i=a[n-1]:t&&n<a.length-1&&(i=a[n+1])):i=a[0],i){let e={$table:D,row:i,rowIndex:D.getRowIndex(i),$rowIndex:D.getVMRowIndex(i)};D.scrollToRow(i).then(()=>D.triggerCurrentRowEvent(r,e))}},moveSelected(e,t,r,l,o,a){var{afterFullData:n,visibleColumn:i}=F;let s=Object.assign({},e);var e=D.getVTRowIndex(s.row),c=D.getVTColumnIndex(s.column);a.preventDefault(),r&&0<e?(s.rowIndex=e-1,s.row=n[s.rowIndex]):o&&e<n.length-1?(s.rowIndex=e+1,s.row=n[s.rowIndex]):t&&c?(s.columnIndex=c-1,s.column=i[s.columnIndex]):l&&c<i.length-1&&(s.columnIndex=c+1,s.column=i[s.columnIndex]),D.scrollToRow(s.row,s.column).then(()=>{s.cell=D.getCellElement(s.row,s.column),D.handleSelected(s,a)})},handleCellMousedownEvent:(e,t)=>{var{editConfig:r,checkboxConfig:l,mouseConfig:o}=M,a=s.value,n=c.value,i=p.value;if(o&&n.area&&D.handleMousedownCellAreaEvent)return D.handleMousedownCellAreaEvent(e,t);l&&a.range&&u(e,t),o&&n.selected&&(r&&"cell"!==i.mode||D.handleSelected(t,e))}}}}),ol.VxeUI);class No{constructor(e){Object.assign(this,{$options:e,required:e.required,min:e.min,max:e.max,type:e.type,pattern:e.pattern,validator:e.validator,trigger:e.trigger,maxWidth:e.maxWidth})}get content(){return il(this.$options.content||this.$options.message)}get message(){return this.content}}let qe=["fullValidate","validate","fullValidateField","validateField","clearValidate"],Xe=(We.add("tableValidatorModule",{setupTable(m){let{props:v,reactData:f,internalData:t}=m,p=m.getRefMaps().refValidTooltip,{computeValidOpts:g,computeTreeOpts:x,computeEditOpts:l}=m.getComputeMaps(),b={},C={},w,r=(e,r,n,a)=>{let i={},{editRules:s,treeConfig:l}=v;var o=t.afterFullData,c=x.value,c=c.children||c.childrenField;let d=g.value,u,p=(!0===e?u=o:e&&(al().isFunction(e)?n=e:u=al().isArray(e)?e:[e]),u=u||(m.getInsertRecords?m.getInsertRecords().concat(m.getUpdateRecords()):[]),[]),h=(t._lastCallTime=Date.now(),w=!1,b.clearValidate(),{});if(s){let t=r&&r.length?r:m.getColumns();o=o=>{if(a||!w){let e=[];t.forEach(r=>{let l=al().isString(r)?r:r.field;!a&&w||!al().has(s,l)||e.push(C.validCellRules("all",o,r).catch(({rule:e,rules:t})=>{t={rule:e,rules:t,rowIndex:m.getRowIndex(o),row:o,columnIndex:m.getColumnIndex(r),column:r,field:l,$table:m};if(i[l]||(i[l]=[]),h[xl(m,o)+":"+r.id]={column:r,row:o,rule:e,content:e.content},i[l].push(t),!a)return w=!0,Promise.reject(t)}))}),p.push(Promise.all(e))}};return l?al().eachTree(u,o,{children:c}):u.forEach(o),Promise.all(p).then(()=>{let e=Object.keys(i);var t,r,l;return f.validErrorMaps=(t=h,"single"===g.value.msgMode?(r={},(l=Object.keys(t)).length&&(r[l=l[0]]=t[l]),r):t),(0,dl.nextTick)().then(()=>{if(e.length)return Promise.reject(i[e[0]][0]);n&&n()})}).catch(a=>new Promise((e,t)=>{let r=()=>{(0,dl.nextTick)(()=>{n?(n(i),e()):("obsolete"===je().validToReject?t:e)(i)})};var l,o=()=>{var e,t;a.cell=m.getCellElement(a.row,a.column),(e=a.cell)&&(e[te]?e[te]():e[re]&&e[re]()),t=a,new Promise(e=>{!1===g.value.autoPos?(m.dispatchEvent("valid-error",t,null),e()):m.handleEdit(t,{type:"valid-error",trigger:"call"}).then(()=>{e(C.showValidTooltip(t))})}).then(r)};!1===d.autoPos?r():(l=a.row,m.scrollToRow(l,a.column).then(o))}))}return f.validErrorMaps={},(0,dl.nextTick)().then(()=>{n&&n()})},y=(b={fullValidate(e,t){return al().isFunction(t)&&Pr("vxe.error.notValidators",["fullValidate(rows, callback)","fullValidate(rows)"]),r(e,null,t,!0)},validate(e,t){return r(e,null,t)},fullValidateField(e,t){t=(al().isArray(t)?t:t?[t]:[]).map(e=>jr(m,e));return t.length?r(e,t,null,!0):(0,dl.nextTick)()},validateField(e,t){t=(al().isArray(t)?t:t?[t]:[]).map(e=>jr(m,e));return t.length?r(e,t,null):(0,dl.nextTick)()},clearValidate(e,t){var l=f.validErrorMaps,r=p.value,o=g.value,e=al().isArray(e)?e:e?[e]:[];let a=(al().isArray(t)?t:t?[t]:[]).map(e=>jr(m,e)),n={};if(r&&r.reactData.visible&&r.close(),"single"===o.msgMode)f.validErrorMaps={};else{if(e.length&&a.length)n=Object.assign({},l),e.forEach(t=>{a.forEach(e=>{e=xl(m,t)+":"+e.id;n[e]&&delete n[e]})});else if(e.length){let r=e.map(e=>""+xl(m,e));al().each(l,(e,t)=>{-1<r.indexOf(t.split(":")[0])&&(n[t]=e)})}else if(a.length){let r=a.map(e=>""+e.id);al().each(l,(e,t)=>{-1<r.indexOf(t.split(":")[1])&&(n[t]=e)})}f.validErrorMaps=n}return(0,dl.nextTick)()}},(e,t)=>{var{type:e,min:r,max:l,pattern:o}=e,e="number"===e,a=e?al().toNumber(t):al().getSize(t);return!(!e||!isNaN(t))||!al().eqNull(r)&&a<al().toNumber(r)||!al().eqNull(l)&&a>al().toNumber(l)||!(!o||(al().isRegExp(o)?o:new RegExp(o)).test(t))});return C={validCellRules(e,d,u,t){var r=v.editRules,l=u.field;let p=[],h=[];if(l&&r){let c=al().get(r,l);if(c){let s=al().isUndefined(t)?al().get(d,l):t;c.forEach(t=>{let{type:r,trigger:l,required:o,validator:a}=t;if("all"===e||!l||e===l)if(a){var n={cellValue:s,rule:t,rules:c,row:d,rowIndex:m.getRowIndex(d),column:u,columnIndex:m.getColumnIndex(u),field:u.field,$table:m,$grid:m.xegrid};let e;al().isString(a)?(i=Ue.get(a))?(i=i.tableCellValidatorMethod||i.cellValidatorMethod)?e=i(n):Pr("vxe.error.notValidators",[a]):Br("vxe.error.notValidators",[a]):e=a(n),e&&(al().isError(e)?(w=!0,p.push(new No({type:"custom",trigger:l,content:e.message,rule:new No(t)}))):e.catch&&h.push(e.catch(e=>{w=!0,p.push(new No({type:"custom",trigger:l,content:e&&e.message?e.message:t.content||t.message,rule:new No(t)}))})))}else{var i="array"===r,n=al().isArray(s);let e=!0;e=i||n?!n||!s.length:al().isString(s)?cl(s.trim()):cl(s),(o?e||y(t,s):!e&&y(t,s))&&(w=!0,p.push(new No(t)))}})}}return Promise.all(h).then(()=>{var e;if(p.length)return e={rules:p,rule:p[0]},Promise.reject(e)})},hasCellRules(t,e,r){var l=v.editRules,r=r.field;return!(!r||!l)&&(l=al().get(l,r))&&!!al().find(l,e=>"all"===t||!e.trigger||t===e.trigger)},triggerValidate(o){var{editConfig:e,editRules:t}=v,a=f.editStore,a=a.actived;let n=l.value;var r=g.value;if(t&&"single"===r.msgMode&&(f.validErrorMaps={}),e&&t&&a.row){let{row:t,column:r,cell:l}=a.args;if(C.hasCellRules(o,t,r))return C.validCellRules(o,t,r).then(()=>{"row"===n.mode&&b.clearValidate(t,r)}).catch(({rule:e})=>e.trigger&&o!==e.trigger?Promise.resolve():(e={rule:e,row:t,column:r,cell:l},C.showValidTooltip(e),Promise.reject(e)))}return Promise.resolve()},showValidTooltip(e){var t=v.height,{tableData:r,validStore:l,validErrorMaps:o}=f,{rule:a,row:n,column:i,cell:s}=e,c=g.value,d=p.value,u=a.content;return l.visible=!0,"single"===c.msgMode?f.validErrorMaps={[xl(m,n)+":"+i.id]:{column:i,row:n,rule:a,content:u}}:f.validErrorMaps=Object.assign({},o,{[xl(m,n)+":"+i.id]:{column:i,row:n,rule:a,content:u}}),m.dispatchEvent("valid-error",e,null),d&&("tooltip"===c.message||"default"===c.message&&!t&&r.length<2)?d.open(s,u):(0,dl.nextTick)()}},{...b,...C}},setupGrid(e){return e.extendTableMethods(qe)}}),["openCustom","closeCustom","saveCustom","cancelCustom","resetCustom","toggleCustomAllCheckbox","setCustomAllCheckbox"]),{getConfig:Ke,renderer:Ye,getI18n:Ge}=(ol.VxeUI.hooks.add("tableCustomModule",{setupTable(c){let{reactData:u,internalData:o}=c,p=c.getComputeMaps().computeCustomOpts,l=c.getRefMaps().refElem,r=c.xegrid,a=()=>{var e=u.customStore,t=l.value;let r=0;t&&(r=t.clientHeight-28),e.maxHeight=Math.max(88,r)},n=()=>{var{initStore:e,customStore:t}=u;return t.visible=!0,e.custom=!0,i(),d(),a(),(0,dl.nextTick)().then(()=>a())},i=()=>{var e=u.customStore,t=o.collectColumn;if(e.visible){let r={},l={},o={};al().eachTree(t,e=>{var t=e.getKey();e.renderFixed=e.fixed,e.renderVisible=e.visible,e.renderResizeWidth=e.renderWidth,r[t]=e.renderSortNumber,l[t]=e.fixed,o[t]=e.visible}),e.oldSortMaps=r,e.oldFixedMaps=l,e.oldVisibleMaps=o,u.customColumnList=t.slice(0)}},s=()=>{var e=u.customStore,t=p.value;return e.visible&&(e.visible=!1,t.immediate||c.handleCustom()),(0,dl.nextTick)()};let t=e=>{var t=u.customStore,r=u.customColumnList,l=p.value;let{checkMethod:o,visibleMethod:a}=l,n=!!e;return l.immediate?(al().eachTree(r,e=>{a&&!a({column:e})||o&&!o({column:e})||(e.visible=n,e.renderVisible=n,e.halfVisible=!1)}),t.isAll=n,u.isCustomStatus=!0,c.handleCustom(),c.saveCustomStore("update:visible")):(al().eachTree(r,e=>{a&&!a({column:e})||o&&!o({column:e})||(e.renderVisible=n,e.halfVisible=!1)}),t.isAll=n),c.checkCustomStatus(),(0,dl.nextTick)()};var e={openCustom:n,closeCustom:s,saveCustom:()=>{var e=u.customColumnList;let{allowVisible:a,allowSort:n,allowFixed:i,allowResizable:s}=p.value;return al().eachTree(e,(e,t,r,l,o)=>{o?e.fixed=o.fixed:(n&&(e.renderSortNumber=t+1),i&&(e.fixed=e.renderFixed)),s&&e.renderVisible&&(!e.children||e.children.length)&&e.renderResizeWidth!==e.renderWidth&&(e.resizeWidth=e.renderResizeWidth,e.renderWidth=e.renderResizeWidth),a&&(e.visible=e.renderVisible)}),u.isCustomStatus=!0,u.isDragColMove=!0,setTimeout(()=>{u.isDragColMove=!1},1e3),c.saveCustomStore("confirm")},cancelCustom:()=>{var{customColumnList:e,customStore:t}=u;let{oldSortMaps:o,oldFixedMaps:a,oldVisibleMaps:n}=t,{allowVisible:i,allowSort:s,allowFixed:c,allowResizable:d}=p.value;return al().eachTree(e,e=>{var t=e.getKey(),r=!!n[t],l=a[t]||"";i&&(e.renderVisible=r,e.visible=r),c&&(e.renderFixed=l,e.fixed=l),s&&(e.renderSortNumber=o[t]||0),d&&(e.renderResizeWidth=e.renderWidth)},{children:"children"}),(0,dl.nextTick)()},resetCustom(e){var t=o.collectColumn;let r=p.value.checkMethod,l=Object.assign({visible:!0,resizable:!0===e,fixed:!0===e,sort:!0===e},e);return al().eachTree(t,e=>{l.resizable&&(e.resizeWidth=0),l.fixed&&(e.fixed=e.defaultFixed),l.sort&&(e.renderSortNumber=e.sortNumber),r&&!r({column:e})||(e.visible=e.defaultVisible),e.renderResizeWidth=e.renderWidth}),u.isCustomStatus=!1,c.saveCustomStore("reset"),c.handleCustom()},toggleCustomAllCheckbox(){var e=u.customStore,e=!e.isAll;return t(e)},setCustomAllCheckbox:t};let d=()=>{var e=u.customStore,t=o.collectColumn;let r=p.value.checkMethod;e.isAll=t.every(e=>!!r&&!r({column:e})||e.renderVisible),e.isIndeterminate=!e.isAll&&t.some(e=>(!r||r({column:e}))&&(e.renderVisible||e.halfVisible))},h=(e,t)=>{(r||c).dispatchEvent("custom",{type:e},t)};var m={checkCustomStatus:d,emitCustomEvent:h,triggerCustomEvent(e){var t=c.reactData.customStore;t.visible?(s(),h("close",e)):(t.btnEl=e.target,n(),h("open",e))},customOpenEvent(e){var t=u.customStore;t.visible||(t.activeBtn=!0,t.btnEl=e.target,c.openCustom(),c.emitCustomEvent("open",e))},customCloseEvent(e){var t=u.customStore;t.visible&&(t.activeBtn=!1,c.closeCustom(),c.emitCustomEvent("close",e))},handleUpdateCustomColumn:i};return{...e,...m}},setupGrid(e){return e.extendTableMethods(Xe)}}),ol.VxeUI),Je="modelValue",et={};function kt(e,t,r){return al().eqNull(e)?al().eqNull(t)?r:t:e}function to(e,t,r){var{dateConfig:l={}}=t;return al().toDateString((t=t,(e=e)&&t.valueFormat?al().toStringDate(e,t.valueFormat):e),l.labelFormat||r)}function ro(e,t){return to(e,t,Ge("vxe.input.date.labelFormat."+(t.type||"date")))}function Ft({name:e}){return(0,dl.resolveComponent)(e)}function lo({name:e}){return(0,dl.resolveComponent)("vxe-"+e.replace("$",""))}function oo(e,t,r){e=e.$panel;e.changeOption({},t,r)}function ao(e){let{name:t,attrs:r}=e;return r="input"===t?Object.assign({type:"text"},r):r}function no(e){var{name:e,immediate:t,props:r}=e;return t||("VxeInput"===e||"$input"===e?(t=(r||{}).type,!(!t||"text"===t||"number"===t||"integer"===t||"float"===t)):"input"!==e&&"textarea"!==e&&"$textarea"!==e)}function Ot(e,t,r,l){return al().assign({immediate:no(e)},et,l,e.props,{[Je]:r})}function io(e,t,r,l){return al().assign({},et,l,e.props,{[Je]:r})}function so(e,t){return"cell"===t.$type||no(e)}function At(e,t,r){e=e.placeholder;return[(0,dl.h)("span",{class:"vxe-cell--label"},e&&cr(r)?[(0,dl.h)("span",{class:"vxe-cell--placeholder"},sl(il(e),1))]:sl(r,1))]}function co(e,r,t,l){let o=e.events,a=Dr(e),n=Mr(e),i=n===a,s={};return o&&al().objectEach(o,(t,e)=>{s[Ir(e)]=function(...e){t(r,...e)}}),t&&(s[Ir(a)]=function(e){t(e),i&&l&&l(e),o&&o[a]&&o[a](r,e)}),!i&&l&&(s[Ir(n)]=function(...e){l(...e),o&&o[n]&&o[n](r,...e)}),s}function uo(e,r,t,l){let o=e.events,a=Dr(e),n=Mr(e),i={};return al().objectEach(o,(t,e)=>{i[Ir(e)]=function(...e){al().isFunction(t)||Br("vxe.error.errFunc",[t]),t(r,...e)}}),t&&(i[Ir(a)]=function(e){t(e),o&&o[a]&&o[a](r,e)}),l&&(i[Ir(n)]=function(...e){l(...e),o&&o[n]&&o[n](r,...e)}),i}function Lt(e,t){let{$table:r,row:l,column:o}=t,a=e.name,n=o.model,i=so(e,t);return uo(e,t,e=>{n.update=!0,n.value=e,i&&Cl(l,o,e)},e=>{!i&&["VxeInput","VxeNumberInput","VxeTextarea","$input","$textarea"].includes(a)?(e=e.value,n.update=!0,n.value=e,r.updateStatus(t,e)):r.updateStatus(t)})}function po(e,t,r){return uo(e,t,e=>{r.data=e},()=>{oo(t,!al().eqNull(r.data),r)})}function ho(t,r){let{$table:l,row:o,column:a}=r,n=a.model;return co(t,r,e=>{e=e.target.value;so(t,r)?Cl(o,a,e):(n.update=!0,n.value=e)},e=>{e=e.target.value;l.updateStatus(r,e)})}function mo(e,t,r){return co(e,t,e=>{r.data=e.target.value},()=>{oo(t,!al().eqNull(r.data),r)})}function vo(e,t){var{row:r,column:l}=t,o=e.name,r=so(e,t)?bl(r,l):l.model.value;return[(0,dl.h)(o,{class:"vxe-default-"+o,...ao(e),value:r,...ho(e,t)})]}function Vt(e,t){var{row:r,column:l}=t,r=bl(r,l);return[(0,dl.h)(Ft(e),{...Ot(e,0,r),...Lt(e,t)})]}function fo(e,t){var r=e.options,{row:l,column:o}=t,l=bl(l,o);return[(0,dl.h)(Ft(e),{options:r,...Ot(e,0,l),...Lt(e,t)})]}function go(e,t){var{row:r,column:l}=t,r=bl(r,l);return[(0,dl.h)(lo(e),{...Ot(e,0,r),...Lt(e,t)})]}function xo(e,t){return[(0,dl.h)((0,dl.resolveComponent)("vxe-button"),{...Ot(e,0,null),...uo(e,t)})]}function bo(r,l,o){var{optionGroups:e,optionGroupProps:t={}}=r;let a=t.options||"options",n=t.label||"label";return e.map((e,t)=>(0,dl.h)("optgroup",{key:t,label:e[n]},o(e[a],r,l)))}function Co(e,t,r){var{optionProps:l={}}=t,{row:o,column:a}=r;let n=l.label||"label",i=l.value||"value",s=l.disabled||"disabled",c=so(t,r)?bl(o,a):a.model.value;return e.map((e,t)=>(0,dl.h)("option",{key:t,value:e[i],disabled:e[s],selected:e[i]==c},e[n]))}function wo(l,o){var e=o.column;return e.filters.map((e,t)=>{var r=e.data;return(0,dl.h)(Ft(l),{key:t,...io(l,0,r),...po(l,o,e)})})}function yo({option:e,row:t,column:r}){e=e.data;return al().get(t,r.field)==e}function To({option:e,row:t,column:r}){e=e.data,t=al().get(t,r.field);return-1<al().toValueString(t).indexOf(e)}function Eo(e,t){return[(0,dl.h)("select",{class:"vxe-default-select",...ao(e),...ho(e,t)},e.optionGroups?bo(e,t,Co):Co(e.options,e,t))]}function Ro(e,t){var{row:r,column:l}=t,{options:o,optionProps:a,optionGroups:n,optionGroupProps:i}=e,r=bl(r,l);return[(0,dl.h)(Ft(e),{...Ot(e,0,r,{options:o,optionProps:a,optionGroups:n,optionGroupProps:i}),...Lt(e,t)})]}function So(e,t){var{row:r,column:l}=t,{options:o,optionProps:a}=e,r=bl(r,l);return[(0,dl.h)(Ft(e),{...Ot(e,0,r,{options:o,optionProps:a}),...Lt(e,t)})]}function Io(e,t){var{row:r,column:l}=t,{options:o,optionProps:a,optionGroups:n,optionGroupProps:i}=e,r=bl(r,l);return[(0,dl.h)(lo(e),{...Ot(e,0,r,{options:o,optionProps:a,optionGroups:n,optionGroupProps:i}),...Lt(e,t)})]}function Do(e,{row:t,column:r}){let{options:l,optionGroups:o,optionProps:a={},optionGroupProps:n={}}=e;e=al().get(t,r.field);let i,s=a.label||"label",c=a.value||"value";return null!=e?al().map(al().isArray(e)?e:[e],o?t=>{var r=n.options||"options";for(let e=0;e<o.length&&!(i=al().find(o[e][r],e=>e[c]==t));e++);return i?i[s]:t}:t=>(i=al().find(l,e=>e[c]==t))?i[s]:t).join(", "):""}function Mo(e){var{row:t,column:r,options:l}=e;return l.original?bl(t,r):Do(r.editRender||r.cellRender,e)}function ko(e,{row:r,column:l}){var{options:e,optionProps:t={}}=e,r=al().get(r,l.field);let o=t.label||"label",a=t.value||"value";l=t.children||"children";if(null==r)return"";{let t={};return al().eachTree(e,e=>{t[al().get(e,a)]=e},{children:l}),al().map(al().isArray(r)?r:[r],e=>{e=t[e];return e&&al().get(e,o)}).join(", ")}}function Fo(e){var{row:t,column:r,options:l}=e;return l.original?bl(t,r):ko(r.editRender||r.cellRender,e)}Ye.mixin({input:{tableAutoFocus:"input",renderTableEdit:vo,renderTableDefault:vo,renderTableFilter:function(r,l){var e=l.column;let o=r.name,a=ao(r);return e.filters.map((e,t)=>(0,dl.h)(o,{key:t,class:"vxe-default-"+o,...a,value:e.data,...mo(r,l,e)}))},tableFilterDefaultMethod:To},textarea:{tableAutoFocus:"textarea",renderTableEdit:vo},select:{renderTableEdit:Eo,renderTableDefault:Eo,renderTableCell(e,t){return At(e,0,Do(e,t))},renderTableFilter(r,l){var e=l.column;return e.filters.map((e,t)=>(0,dl.h)("select",{key:t,class:"vxe-default-select",...ao(r),...mo(r,l,e)},r.optionGroups?bo(r,l,Co):Co(r.options,r,l)))},tableFilterDefaultMethod:yo,tableExportMethod:Mo},VxeInput:{tableAutoFocus:"input",renderTableEdit:Vt,renderTableCell(e,t){var{props:r={}}=e,{row:t,column:l}=t,o=Ke().input||{},a=r.digits||o.digits||2;let n=al().get(t,l.field);if(n)switch(r.type){case"date":case"week":case"month":case"quarter":case"year":n=ro(n,r);break;case"float":n=al().toFixed(al().floor(n,a),a)}return At(e,0,n)},renderTableDefault:Vt,renderTableFilter:wo,tableFilterDefaultMethod:To},VxeNumberInput:{tableAutoFocus:"input",renderTableEdit:Vt,renderTableCell(e,t){var r,{props:l={}}=e,{row:t,column:o}=t,a=l.type;let n=al().get(t,o.field);return n&&(t=Ke().numberInput||{},"float"===a?(o=kt(l.autoFill,t.autoFill,!0),r=kt(l.digits,t.digits,1),n=al().toFixed(al().floor(n,r),r),o||(n=al().toNumber(n))):"amount"===a&&(r=kt(l.autoFill,t.autoFill,!0),o=kt(l.digits,t.digits,2),a=kt(l.showCurrency,t.showCurrency,!1),n=al().commafy(al().toNumber(n),{digits:o}),r||([o,r]=n.split("."),r&&(r=r.replace(/0+$/,""),n=r?[o,".",r].join(""):o)),a)&&(n=""+(l.currencySymbol||t.currencySymbol||Ge("vxe.numberInput.currencySymbol")||"")+n)),At(e,0,n)},renderTableFooter(t,r){var{props:t={}}=t,{row:r,column:l,_columnIndex:o}=r,a=t.type,o=al().isArray(r)?r[o]:al().get(r,l.field);if(al().isNumber(o)){r=Ke().numberInput||{};if("float"===a){var l=kt(t.autoFill,r.autoFill,!0),n=kt(t.digits,r.digits,1);let e=al().toFixed(al().floor(o,n),n);return e=l?e:al().toNumber(e)}if("amount"===a){var n=kt(t.autoFill,r.autoFill,!0),l=kt(t.digits,r.digits,2),a=kt(t.showCurrency,r.showCurrency,!1);let e=al().commafy(al().toNumber(o),{digits:l});return n||([l,n]=e.split("."),n&&(n=n.replace(/0+$/,""),e=n?[l,".",n].join(""):l)),e=a?""+(t.currencySymbol||r.currencySymbol||Ge("vxe.numberInput.currencySymbol")||"")+e:e}}return il(o,1)},renderTableDefault:Vt,renderTableFilter:wo,tableFilterDefaultMethod:To,tableExportMethod(e){var{row:e,column:t}=e;return al().get(e,t.field)}},VxeDatePicker:{tableAutoFocus:"input",renderTableEdit:Vt,renderTableCell(e,t){var{props:r={}}=e,{row:t,column:l}=t;let o=al().get(t,l.field);return At(e,0,o=o&&"time"!==r.type?ro(o,r):o)},renderTableDefault:Vt,renderTableFilter:wo,tableFilterDefaultMethod:yo},VxeTextarea:{tableAutoFocus:"textarea",renderTableEdit:Vt,renderTableCell(e,t){var{row:t,column:r}=t;return At(e,0,al().get(t,r.field))}},VxeButton:{renderTableDefault:function(e,t){return[(0,dl.h)(Ft(e),{...Ot(e,0,null),...uo(e,t)})]}},VxeButtonGroup:{renderTableDefault(e,t){var r=e.options;return[(0,dl.h)(Ft(e),{options:r,...Ot(e,0,null),...uo(e,t)})]}},VxeSelect:{tableAutoFocus:"input",renderTableEdit:Ro,renderTableDefault:Ro,renderTableCell(e,t){return At(e,0,Do(e,t))},renderTableFilter(l,o){var e=o.column;let{options:a,optionProps:n,optionGroups:i,optionGroupProps:s}=l;return e.filters.map((e,t)=>{var r=e.data;return(0,dl.h)(Ft(l),{key:t,...io(l,0,r,{options:a,optionProps:n,optionGroups:i,optionGroupProps:s}),...po(l,o,e)})})},tableFilterDefaultMethod:yo,tableExportMethod:Mo},VxeTreeSelect:{tableAutoFocus:"input",renderTableEdit:So,renderTableCell(e,t){return At(e,0,ko(e,t))},tableExportMethod:Fo},VxeTableSelect:{tableAutoFocus:"input",renderTableEdit:So,renderTableCell(e,t){return At(e,0,ko(e,t))},tableExportMethod:Fo},VxeColorPicker:{tableAutoFocus:"input",renderTableEdit(e,t){var{row:r,column:l}=t,o=e.options,r=bl(r,l);return[(0,dl.h)(Ft(e),{...Ot(e,0,r,{colors:o}),...Lt(e,t)})]},renderTableCell(e,t){var{row:t,column:r}=t,t=al().get(t,r.field);return(0,dl.h)("span",{class:"vxe-color-picker--readonly"},[(0,dl.h)("div",{class:"vxe-color-picker--readonly-color",style:{backgroundColor:t}})])}},VxeIconPicker:{tableAutoFocus:"input",renderTableEdit(e,t){var{row:r,column:l}=t,o=e.options,r=bl(r,l);return[(0,dl.h)(Ft(e),{...Ot(e,0,r,{icons:o}),...Lt(e,t)})]},renderTableCell(e,t){var{row:t,column:r}=t,t=al().get(t,r.field);return(0,dl.h)("i",{class:t})}},VxeRadioGroup:{renderTableDefault:fo},VxeCheckboxGroup:{renderTableDefault:fo},VxeSwitch:{tableAutoFocus:"button",renderTableEdit:Vt,renderTableDefault:Vt},VxeUpload:{renderTableEdit:Vt,renderTableCell:Vt,renderTableDefault:Vt},VxeImage:{renderTableDefault(e,t){var{row:r,column:l}=t,o=e.props,r=bl(r,l);return[(0,dl.h)(Ft(e),{...o,src:r,...Lt(e,t)})]}},VxeImageGroup:{renderTableDefault(e,t){var{row:r,column:l}=t,o=e.props,r=bl(r,l);return[(0,dl.h)(Ft(e),{...o,urlList:r,...Lt(e,t)})]}},VxeTextEllipsis:{renderTableDefault(e,t){var{row:r,column:l}=t,o=e.props,r=bl(r,l);return[(0,dl.h)(Ft(e),{...o,content:r,...Lt(e,t)})]}},VxeRate:{renderTableDefault:Vt},VxeSlider:{renderTableDefault:Vt},$input:{tableAutoFocus:".vxe-input--inner",renderTableEdit:go,renderTableCell(e,t){var{props:r={}}=e,{row:t,column:l}=t,o=r.digits||Ke().input?.digits||2;let a=al().get(t,l.field);if(a)switch(r.type){case"date":case"week":case"month":case"year":a=ro(a,r);break;case"float":a=al().toFixed(al().floor(a,o),o)}return At(e,0,a)},renderTableDefault:go,renderTableFilter:function(l,o){return o.column.filters.map((e,t)=>{var r=e.data;return(0,dl.h)(lo(l),{key:t,...io(l,0,r),...po(l,o,e)})})},tableFilterDefaultMethod:To},$textarea:{tableAutoFocus:".vxe-textarea--inner"},$button:{renderTableDefault:xo},$buttons:{renderTableDefault:function(e,t){return e.children.map(e=>xo(e,t)[0])}},$select:{tableAutoFocus:".vxe-input--inner",renderTableEdit:Io,renderTableDefault:Io,renderTableCell(e,t){return At(e,0,Do(e,t))},renderTableFilter(l,o){var e=o.column;let{options:a,optionProps:n,optionGroups:i,optionGroupProps:s}=l;return e.filters.map((e,t)=>{var r=e.data;return(0,dl.h)(lo(l),{key:t,...io(l,0,r,{options:a,optionProps:n,optionGroups:i,optionGroupProps:s}),...po(l,o,e)})})},tableFilterDefaultMethod:yo,tableExportMethod:Mo},$radio:{tableAutoFocus:".vxe-radio--input"},$checkbox:{tableAutoFocus:".vxe-checkbox--input"},$switch:{tableAutoFocus:".vxe-switch--button",renderTableEdit:go,renderTableDefault:go}});let a=Object.assign({},$r,{install(e){e.component($r.name,$r)}}),tt=(Ct={useCellView:function(l){var e=(0,dl.computed)(()=>{var e=l.renderParams;return e.column}),t=(0,dl.computed)(()=>{var e=l.renderParams;return e.row}),r=(0,dl.computed)(()=>{var e=l.renderOpts;return e.props||{}});return{currColumn:e,currRow:t,cellModel:(0,dl.computed)({get(){var e=l.renderParams,{row:e,column:t}=e;return al().get(e,t.field)},set(e){var t=l.renderParams,{row:t,column:r}=t;return al().set(t,r.field,e)}}),cellOptions:r}}},ol.VxeUI.dynamicApp&&ol.VxeUI.dynamicApp.component($r.name,$r),ol.VxeUI.component($r),ol.VxeUI.tableHandle=Ct,a);var Oo=a;let{getConfig:n,getIcon:N,getI18n:P,renderer:rt,commands:lt,createEvent:ot,useFns:at}=ol.VxeUI;var Ao=(0,dl.defineComponent)({name:"VxeToolbar",props:{loading:Boolean,refresh:[Boolean,Object],import:[Boolean,Object],export:[Boolean,Object],print:[Boolean,Object],zoom:[Boolean,Object],custom:[Boolean,Object],buttons:{type:Array,default:()=>n().toolbar.buttons},tools:{type:Array,default:()=>n().toolbar.tools},perfect:{type:Boolean,default:()=>n().toolbar.perfect},size:{type:String,default:()=>n().toolbar.size||n().size},className:[String,Function]},emits:["button-click","tool-click"],setup(d,e){let{slots:u,emit:l}=e;var t=al().uniqueId();let p=ol.VxeUI.getComponent("VxeButton"),h=at.useSize(d).computeSize,m=(0,dl.reactive)({isRefresh:!1,connectFlag:0,columns:[]}),v={connectTable:null},f=(0,dl.ref)(),r={refElem:f},g={xID:t,props:d,context:e,reactData:m,internalData:v,getRefMaps:()=>r};let x=(0,dl.inject)("$xeGrid",null),b=(0,dl.computed)(()=>Object.assign({},al().clone(n().toolbar.refresh,!0),d.refresh)),C=(0,dl.computed)(()=>Object.assign({},al().clone(n().toolbar.import,!0),d.import)),w=(0,dl.computed)(()=>Object.assign({},al().clone(n().toolbar.export,!0),d.export)),y=(0,dl.computed)(()=>Object.assign({},al().clone(n().toolbar.print,!0),d.print)),T=(0,dl.computed)(()=>Object.assign({},al().clone(n().toolbar.zoom,!0),d.zoom)),E=(0,dl.computed)(()=>Object.assign({},al().clone(n().toolbar.custom,!0),d.custom)),o=(0,dl.computed)(()=>{var e=v.connectTable;if((m.connectFlag||e)&&e)return e=e.getComputeMaps().computeCustomOpts,e.value;return{trigger:""}}),R=(0,dl.computed)(()=>o.value.trigger),a=()=>{var e=v.connectTable;if(e)return!0;Br("vxe.error.barUnableLink")},S=({$event:e})=>{var t=v.connectTable;t&&(t.triggerCustomEvent?t.triggerCustomEvent(e):Br("vxe.error.reqModule",["VxeTableCustomModule"]))},I=({$event:e})=>{var t=v.connectTable;t?t.customOpenEvent(e):Br("vxe.error.reqModule",["VxeTableCustomModule"])},D=({$event:t})=>{var e=v.connectTable;let r=e;if(r){let e=r.reactData.customStore;e.activeBtn=!1,setTimeout(()=>{e.activeBtn||e.activeWrapper||r.customCloseEvent(t)},350)}},M=({$event:e})=>{var t=m.isRefresh,r=b.value;if(!t){t=r.queryMethod||r.query;if(t){m.isRefresh=!0;try{Promise.resolve(t({})).catch(e=>e).then(()=>{m.isRefresh=!1})}catch(e){m.isRefresh=!1}}else x&&(m.isRefresh=!0,x.triggerToolbarCommitEvent({code:r.code||"reload"},e).catch(e=>e).then(()=>{m.isRefresh=!1}))}},k=({$event:e})=>{x&&x.triggerZoomEvent(e)},F=(e,t)=>{var r,l=v.connectTable,o=t.code;o&&(x?x.triggerToolbarBtnEvent(t,e):(r=lt.get(o),t={code:o,button:t,$table:l,$grid:x,$event:e},r&&((l=r.tableCommandMethod||r.commandMethod)?l(t):Br("vxe.error.notCommands",[o])),g.dispatchEvent("button-click",t,e)))},O=(e,t)=>{var r,l=v.connectTable,o=t.code;o&&(x?x.triggerToolbarTolEvent(t,e):(r=lt.get(o),t={code:o,tool:t,$table:l,$grid:x,$event:e},r&&((l=r.tableCommandMethod||r.commandMethod)?l(t):Br("vxe.error.notCommands",[o])),g.dispatchEvent("tool-click",t,e)))},A=()=>{var e;a()&&(e=v.connectTable,e)&&e.openImport()},L=()=>{var e;a()&&(e=v.connectTable,e)&&e.openExport()},V=()=>{var e;a()&&(e=v.connectTable,e)&&e.openPrint()};Object.assign(g,{dispatchEvent:(e,t,r)=>{l(e,ot(r,{$toolbar:g},t))},syncUpdate(e){v.connectTable=e.$table,m.columns=e.collectColumn,m.connectFlag++}});let $=(e,r)=>{e=e.dropdowns;return e?e.map((t,e)=>!1!==t.visible&&p?(0,dl.h)(p,{key:e,disabled:t.disabled,loading:t.loading,type:t.type,mode:t.mode,icon:t.icon,circle:t.circle,round:t.round,status:t.status,content:t.name,title:t.title,routerLink:t.routerLink,permissionCode:t.permissionCode,prefixTooltip:t.prefixTooltip,suffixTooltip:t.suffixTooltip,onClick:({$event:e})=>(r?F:O)(e,t)}):(0,dl.createCommentVNode)()):[]};return g.renderVN=()=>{var{perfect:e,loading:t,refresh:r,zoom:l,custom:o,className:a}=d,n=v.connectTable,i=h.value,s=u.tools,c=u.buttons;return(0,dl.h)("div",{ref:f,class:["vxe-toolbar",a?al().isFunction(a)?a({$toolbar:g}):a:"",{["size--"+i]:i,"is--perfect":e,"is--loading":t}]},[(0,dl.h)("div",{class:"vxe-buttons--wrapper"},c?c({$grid:x,$table:n}):(()=>{var e=d.buttons,t=v.connectTable;let i=t;var t=u.buttonPrefix||u["button-prefix"],r=u.buttonSuffix||u["button-suffix"];let s=[];return t&&s.push(...yl(t({buttons:e||[],$grid:x,$table:i}))),e&&e.forEach((t,e)=>{var r,l,o,{dropdowns:a,buttonRender:n}=t;!1!==t.visible&&(r=n?rt.get(n.name):null,n&&r&&r.renderToolbarButton?(l=r.toolbarButtonClassName,o={$grid:x,$table:i,button:t},s.push((0,dl.h)("span",{key:"br"+(t.code||e),class:["vxe-button--item",l?al().isFunction(l)?l(o):l:""]},yl(r.renderToolbarButton(n,o))))):p&&s.push((0,dl.h)(p,{key:"bd"+(t.code||e),disabled:t.disabled,loading:t.loading,type:t.type,mode:t.mode,icon:t.icon,circle:t.circle,round:t.round,status:t.status,content:t.name,title:t.title,routerLink:t.routerLink,permissionCode:t.permissionCode,prefixTooltip:t.prefixTooltip,suffixTooltip:t.suffixTooltip,destroyOnClose:t.destroyOnClose,placement:t.placement,transfer:t.transfer,onClick:({$event:e})=>F(e,t)},a&&a.length?{dropdowns:()=>$(t,!0)}:{})))}),r&&s.push(...yl(r({buttons:e||[],$grid:x,$table:i}))),s})()),(0,dl.h)("div",{class:"vxe-tools--wrapper"},s?s({$grid:x,$table:n}):(()=>{var e=d.tools,t=v.connectTable;let s=t;var t=u.toolPrefix||u["tool-prefix"],r=u.toolSuffix||u["tool-suffix"];let c=[];return t&&c.push(...yl(t({tools:e||[],$grid:x,$table:s}))),e&&e.forEach((t,e)=>{var r,l,o,a,{dropdowns:n,toolRender:i}=t;!1!==t.visible&&(r=i?i.name:null,l=i?rt.get(r):null,i&&l&&l.renderToolbarTool?(o=l.toolbarToolClassName,a={$grid:x,$table:s,tool:t},c.push((0,dl.h)("span",{key:r,class:["vxe-tool--item",o?al().isFunction(o)?o(a):o:""]},yl(l.renderToolbarTool(i,a))))):p&&c.push((0,dl.h)(p,{key:e,disabled:t.disabled,loading:t.loading,type:t.type,mode:t.mode,icon:t.icon,circle:t.circle,round:t.round,status:t.status,content:t.name,title:t.title,routerLink:t.routerLink,permissionCode:t.permissionCode,prefixTooltip:t.prefixTooltip,suffixTooltip:t.suffixTooltip,destroyOnClose:t.destroyOnClose,placement:t.placement,transfer:t.transfer,onClick:({$event:e})=>O(e,t)},n&&n.length?{dropdowns:()=>$(t,!1)}:{})))}),r&&c.push(...yl(r({tools:e||[],$grid:x,$table:s}))),c})()),(0,dl.h)("div",{class:"vxe-tools--operate"},[d.import&&(a=C.value,p)?(0,dl.h)(p,{key:"import",circle:!0,icon:a.icon||N().TOOLBAR_TOOLS_IMPORT,title:P("vxe.toolbar.import"),onClick:A}):(0,dl.createCommentVNode)(),d.export&&(i=w.value,p)?(0,dl.h)(p,{key:"export",circle:!0,icon:i.icon||N().TOOLBAR_TOOLS_EXPORT,title:P("vxe.toolbar.export"),onClick:L}):(0,dl.createCommentVNode)(),d.print&&(e=y.value,p)?(0,dl.h)(p,{key:"print",circle:!0,icon:e.icon||N().TOOLBAR_TOOLS_PRINT,title:P("vxe.toolbar.print"),onClick:V}):(0,dl.createCommentVNode)(),r&&(t=b.value,p)?(0,dl.h)(p,{key:"refresh",circle:!0,icon:m.isRefresh?t.iconLoading||N().TOOLBAR_TOOLS_REFRESH_LOADING:t.icon||N().TOOLBAR_TOOLS_REFRESH,title:P("vxe.toolbar.refresh"),onClick:M}):(0,dl.createCommentVNode)(),l&&x&&(c=T.value,x)&&p?(0,dl.h)(p,{key:"zoom",circle:!0,icon:x.isMaximized()?c.iconOut||N().TOOLBAR_TOOLS_MINIMIZE:c.iconIn||N().TOOLBAR_TOOLS_FULLSCREEN,title:P("vxe.toolbar.zoom"+(x.isMaximized()?"Out":"In")),onClick:k}):(0,dl.createCommentVNode)(),o&&(s=E.value,n=R.value,a={},"manual"!==n&&("hover"===n?(a.onMouseenter=I,a.onMouseleave=D):a.onClick=S),p)?(0,dl.h)(p,{key:"custom",circle:!0,icon:s.icon||N().TOOLBAR_TOOLS_CUSTOM,title:P("vxe.toolbar.custom"),className:"vxe-toolbar-custom-target",...a}):(0,dl.createCommentVNode)()])])},(0,dl.nextTick)(()=>{var e=d.refresh,t=b.value,t=t.queryMethod||t.query,e=(!e||x||t||Pr("vxe.error.notFunc",["queryMethod"]),E.value);e.isFooter&&Pr("vxe.error.delProp",["toolbar.custom.isFooter","table.custom-config.showFooter"]),e.showFooter&&Pr("vxe.error.delProp",["toolbar.custom.showFooter","table.custom-config.showFooter"]),e.immediate&&Pr("vxe.error.delProp",["toolbar.custom.immediate","table.custom-config.immediate"]),e.trigger&&Pr("vxe.error.delProp",["toolbar.custom.trigger","table.custom-config.trigger"]),(d.refresh||d.import||d.export||d.print||d.zoom)&&!p&&Br("vxe.error.reqComp",["vxe-button"])}),g},render(){return this.renderVN()}});let i=Object.assign({},Ao,{install(e){e.component(Ao.name,Ao)}}),nt=(ol.VxeUI.dynamicApp&&ol.VxeUI.dynamicApp.component(Ao.name,Ao),ol.VxeUI.component(Ao),i);var Lo=i;let{getConfig:pe,getI18n:he,commands:it,hooks:st,useFns:ct,createEvent:dt,globalEvents:ut,GLOBAL_EVENT_KEYS:pt,renderEmptyElement:ht}=ol.VxeUI,mt=Object.keys(zl),vt=["clearAll","syncData","updateData","loadData","reloadData","reloadRow","loadColumn","reloadColumn","getRowNode","getColumnNode","getRowIndex","getVTRowIndex","getVMRowIndex","getColumnIndex","getVTColumnIndex","getVMColumnIndex","setRow","createData","createRow","revertData","clearData","isInsertByRow","isUpdateByRow","getColumns","getColumnById","getColumnByField","getTableColumn","getFullColumns","getData","getCheckboxRecords","getParentRow","getTreeParentRow","getRowSeq","getRowById","getRowid","getTableData","getFullData","setColumnFixed","clearColumnFixed","setColumnWidth","getColumnWidth","setRowHeightConf","getRowHeightConf","setRowHeight","getRowHeight","hideColumn","showColumn","resetColumn","refreshColumn","refreshScroll","recalculate","closeTooltip","isAllCheckboxChecked","isAllCheckboxIndeterminate","getCheckboxIndeterminateRecords","setCheckboxRow","setCheckboxRowKey","isCheckedByCheckboxRow","isCheckedByCheckboxRowKey","isIndeterminateByCheckboxRow","isIndeterminateByCheckboxRowKey","toggleCheckboxRow","setAllCheckboxRow","getRadioReserveRecord","clearRadioReserve","getCheckboxReserveRecords","clearCheckboxReserve","toggleAllCheckboxRow","clearCheckboxRow","setCurrentRow","isCheckedByRadioRow","isCheckedByRadioRowKey","setRadioRow","setRadioRowKey","clearCurrentRow","clearRadioRow","getCurrentRecord","getRadioRecord","getCurrentColumn","setCurrentColumn","clearCurrentColumn","setPendingRow","togglePendingRow","getPendingRecords","clearPendingRow","sort","setSort","clearSort","isSort","getSortColumns","closeFilter","isFilter","isActiveFilterByColumn","isRowExpandLoaded","clearRowExpandLoaded","reloadRowExpand","reloadRowExpand","toggleRowExpand","setAllRowExpand","setRowExpand","isExpandByRow","isRowExpandByRow","clearRowExpand","clearRowExpandReserve","getRowExpandRecords","getTreeExpandRecords","isTreeExpandLoaded","clearTreeExpandLoaded","reloadTreeExpand","reloadTreeChilds","toggleTreeExpand","setAllTreeExpand","setTreeExpand","isTreeExpandByRow","clearTreeExpand","clearTreeExpandReserve","getScroll","scrollTo","scrollToRow","scrollToColumn","clearScroll","updateFooter","updateStatus","setMergeCells","removeInsertRow","removeMergeCells","getMergeCells","clearMergeCells","setMergeFooterItems","removeMergeFooterItems","getMergeFooterItems","clearMergeFooterItems","getCustomStoreData","openTooltip","getCellLabel","getCellElement","focus","blur","connect"];var ur=[...Vr,"page-change","form-submit","form-submit-invalid","form-reset","form-collapse","form-toggle-collapse","proxy-query","proxy-delete","proxy-save","toolbar-button-click","toolbar-tool-click","zoom"],Vo=(0,dl.defineComponent)({name:"VxeGrid",props:{...zl,layouts:Array,columns:Array,pagerConfig:Object,proxyConfig:Object,toolbarConfig:Object,formConfig:Object,zoomConfig:Object,size:{type:String,default:()=>pe().grid.size||pe().size}},emits:ur,setup(F,e){let{slots:s,emit:l}=e;var T=al().uniqueId();let n=ol.VxeUI.getComponent("VxeForm"),i=ol.VxeUI.getComponent("VxePager"),E=[["Form"],["Toolbar","Top","Table","Bottom","Pager"]],R=ct.useSize(F).computeSize,O=(0,dl.reactive)({tableLoading:!1,proxyInited:!1,isZMax:!1,tableData:[],filterData:[],formData:{},sortData:[],tZindex:0,tablePage:{total:0,pageSize:pe().pager?.pageSize||10,currentPage:1}}),c=(0,dl.ref)(),A=(0,dl.ref)(),d=(0,dl.ref)(),a=(0,dl.ref)(),u=(0,dl.ref)(),p=(0,dl.ref)(),h=(0,dl.ref)(),m=(0,dl.ref)(),v=(0,dl.ref)(),f=(0,dl.ref)();var t=e=>{let t={};return e.forEach(r=>{t[r]=(...e)=>{var t=A.value;if(t&&t[r])return t[r](...e)}}),t};let L=t(vt),V=(vt.forEach(r=>{L[r]=(...e)=>{var t=A.value;if(t&&t[r])return t&&t[r](...e)}}),(0,dl.computed)(()=>al().merge({},al().clone(pe().grid.proxyConfig,!0),F.proxyConfig))),U=(0,dl.computed)(()=>{var e=V.value;return al().isBoolean(e.message)?e.message:e.showResponseMsg}),$=(0,dl.computed)(()=>V.value.showActiveMsg),_=(0,dl.computed)(()=>Object.assign({},pe().grid.pagerConfig,F.pagerConfig)),g=(0,dl.computed)(()=>Object.assign({},pe().grid.formConfig,F.formConfig)),H=(0,dl.computed)(()=>Object.assign({},pe().grid.toolbarConfig,F.toolbarConfig)),r=(0,dl.computed)(()=>Object.assign({},pe().grid.zoomConfig,F.zoomConfig)),S=(0,dl.computed)(()=>{var{height:e,maxHeight:t}=F,{isZMax:r,tZindex:l}=O,o={};return r?o.zIndex=l:(e&&(o.height="auto"===e||"100%"===e?"100%":hr(e)),t&&(o.maxHeight="auto"===t||"100%"===t?"100%":hr(t))),o}),I=(0,dl.computed)(()=>{let t={},r=F;return mt.forEach(e=>{t[e]=r[e]}),t}),D=(0,dl.computed)(()=>{var{seqConfig:e,pagerConfig:t,loading:r,editConfig:l,proxyConfig:o}=F,{isZMax:a,tableLoading:n,tablePage:i,tableData:s}=O,c=I.value,d=V.value,u=_.value,p=Object.assign({},c);return a&&(c.maxHeight?p.maxHeight="100%":p.height="100%"),o&&nl(d)&&(p.loading=r||n,p.data=s,t)&&d.seq&&nl(u)&&(p.seqConfig=Object.assign({},e,{startIndex:(i.currentPage-1)*i.pageSize})),l&&(p.editConfig=Object.assign({},l)),p}),M=(0,dl.computed)(()=>{var e=F.layouts;let t=[],r=[],l=[],o=[];return(t=e&&e.length?e:pe().grid.layouts||E).length&&(al().isArray(t[0])?(r=t[0],l=t[1]||[],o=t[2]||[]):l=t),{headKeys:r,bodyKeys:l,footKeys:o}});var k=(0,dl.computed)(()=>{var e=_.value;return""+e.currentPage+e.pageSize});let W={refElem:c,refTable:A,refForm:d,refToolbar:a,refPager:u},q={computeProxyOpts:V,computePagerOpts:_,computeFormOpts:g,computeToolbarOpts:H,computeZoomOpts:r},N={xID:T,props:F,context:e,reactData:O,getRefMaps:()=>W,getComputeMaps:()=>q},o=()=>{var e=H.value;F.toolbarConfig&&nl(e)&&(0,dl.nextTick)(()=>{var e=A.value,t=a.value;e&&t&&e.connect(t)})},P=()=>{var e=F.proxyConfig,t=O.formData,r=V.value,l=g.value;return e&&nl(r)&&r.form?t:l.data},x=()=>{var e=O.tablePage,t=F.pagerConfig,r=_.value,{currentPage:l,pageSize:o}=r;t&&nl(r)&&(l&&(e.currentPage=l),o)&&(e.pageSize=o)},B=(e,t)=>{var r=V.value,r=(r.response||r.props||{}).message;let l;return(l=e&&r?al().isFunction(r)?r({data:e,$grid:N}):al().get(e,r):l)||he(t)},z=(e,t,r)=>{var l=$.value,o=L.getCheckboxRecords();if(l)if(o.length){if(ol.VxeUI.modal)return ol.VxeUI.modal.confirm({id:"cfm_"+e,content:he(t),escClosable:!0}).then(e=>{if("confirm"===e)return r()})}else ol.VxeUI.modal&&ol.VxeUI.modal.message({id:"msg_"+e,content:he("vxe.grid.selectOneRecord"),status:"warning"});else o.length&&r();return Promise.resolve()},X=e=>{var t=F.proxyConfig,r=O.tablePage;let{$event:l,currentPage:o,pageSize:a}=e;var n=V.value;r.currentPage=o,r.pageSize=a,j.dispatchEvent("page-change",e,l),t&&nl(n)&&j.commitProxy("query").then(e=>{j.dispatchEvent("proxy-query",e,l)})},K=t=>{var e=A.value,r=F.proxyConfig,e=e.getComputeMaps().computeSortOpts,l=V.value;e.value.remote&&(O.sortData=t.sortList,r)&&nl(l)&&(O.tablePage.currentPage=1,j.commitProxy("query").then(e=>{j.dispatchEvent("proxy-query",e,t.$event)})),j.dispatchEvent("sort-change",t,t.$event)},Y=t=>{var e=A.value,r=F.proxyConfig,e=e.getComputeMaps().computeFilterOpts,l=V.value;e.value.remote&&(O.filterData=t.filterList,r)&&nl(l)&&(O.tablePage.currentPage=1,j.commitProxy("query").then(e=>{j.dispatchEvent("proxy-query",e,t.$event)})),j.dispatchEvent("filter-change",t,t.$event)},G=t=>{var e=F.proxyConfig,r=V.value;e&&nl(r)&&j.commitProxy("reload").then(e=>{j.dispatchEvent("proxy-query",{...e,isReload:!0},t.$event)}),j.dispatchEvent("form-submit",t,t.$event)},Z=e=>{var t=F.proxyConfig;let r=e.$event;var l=V.value,o=A.value;t&&nl(l)&&(o.clearScroll(),j.commitProxy("reload").then(e=>{j.dispatchEvent("proxy-query",{...e,isReload:!0},r)})),j.dispatchEvent("form-reset",e,r)},Q=e=>{j.dispatchEvent("form-submit-invalid",e,e.$event)},J=e=>{var t=e.$event;(0,dl.nextTick)(()=>L.recalculate(!0)),j.dispatchEvent("form-toggle-collapse",e,t),j.dispatchEvent("form-collapse",e,t)},ee=e=>{var t=O.isZMax;return(e?!t:t)&&(O.isZMax=!t,O.tZindex<El())&&(O.tZindex=Tl()),(0,dl.nextTick)().then(()=>L.recalculate(!0)).then(()=>(setTimeout(()=>L.recalculate(!0),15),O.isZMax))},b=(e,t)=>{e=e[t];if(e){if(!al().isString(e))return e;if(s[e])return s[e];Br("vxe.error.notSlot",[e])}return null},te=()=>{var{formConfig:e,proxyConfig:r}=F,l=O.formData,o=V.value,a=g.value;if(e&&nl(a)||s.form){let e=[];if(s.form)e=s.form({$grid:N});else if(a.items){let t={};if(!a.inited){a.inited=!0;let t=o.beforeItem;o&&t&&a.items.forEach(e=>{t({$grid:N,item:e})})}a.items.forEach(e=>{al().each(e.slots,e=>{al().isFunction(e)||s[e]&&(t[e]=s[e])})}),n&&e.push((0,dl.h)(n,{ref:d,...Object.assign({},a,{data:r&&nl(o)&&o.form?l:a.data}),onSubmit:G,onReset:Z,onSubmitInvalid:Q,onCollapse:J},t))}return(0,dl.h)("div",{ref:p,key:"form",class:"vxe-grid--form-wrapper"},e)}return(0,dl.createCommentVNode)()},re=()=>{var t,r,l=F.toolbarConfig,o=H.value;if(l&&nl(o)||s.toolbar){let e=[];return s.toolbar?e=s.toolbar({$grid:N}):(l={},(r=o.slots)&&(t=b(r,"buttons"),r=b(r,"tools"),t&&(l.buttons=t),r)&&(l.tools=r),e.push((0,dl.h)(Lo,{ref:a,...o},l))),(0,dl.h)("div",{ref:h,key:"toolbar",class:"vxe-grid--toolbar-wrapper"},e)}return(0,dl.createCommentVNode)()},le=()=>s.top?(0,dl.h)("div",{ref:m,key:"top",class:"vxe-grid--top-wrapper"},s.top({$grid:N})):(0,dl.createCommentVNode)(),oe=()=>{var e=s.left;return e?(0,dl.h)("div",{class:"vxe-grid--left-wrapper"},e({$grid:N})):(0,dl.createCommentVNode)()},ae=()=>{var e=s.right;return e?(0,dl.h)("div",{class:"vxe-grid--right-wrapper"},e({$grid:N})):(0,dl.createCommentVNode)()},ne=()=>{var e=F.proxyConfig,t=D.value,r=V.value,l=Object.assign({},ce),o=s.empty,a=s.loading,n=s.rowDragIcon||s["row-drag-icon"],i=s.columnDragIcon||s["column-drag-icon"],e=(e&&nl(r)&&(r.sort&&(l.onSortChange=K),r.filter)&&(l.onFilterChange=Y),{});return o&&(e.empty=o),a&&(e.loading=a),n&&(e.rowDragIcon=n),i&&(e.columnDragIcon=i),(0,dl.h)("div",{class:"vxe-grid--table-wrapper"},[(0,dl.h)(Oo,{ref:A,...t,...l},e)])},ie=()=>s.bottom?(0,dl.h)("div",{ref:v,key:"bottom",class:"vxe-grid--bottom-wrapper"},s.bottom({$grid:N})):(0,dl.createCommentVNode)(),se=()=>{var t,r,{proxyConfig:l,pagerConfig:o}=F,a=V.value,n=_.value;if(o&&nl(n)||s.pager){let e=[];return s.pager?e=s.pager({$grid:N}):(o={},(r=n.slots)&&(t=b(r,"left"),r=b(r,"right"),t&&(o.left=t),r)&&(o.right=r),i&&e.push((0,dl.h)(i,{ref:u,...n,...l&&nl(a)?O.tablePage:{},onPageChange:X},o))),(0,dl.h)("div",{ref:f,key:"pager",class:"vxe-grid--pager-wrapper"},e)}return(0,dl.createCommentVNode)()},C=e=>{let t=[];return e.forEach(e=>{switch(e){case"Form":t.push(te());break;case"Toolbar":t.push(re());break;case"Top":t.push(le());break;case"Table":t.push((0,dl.h)("div",{key:"table",class:"vxe-grid--table-container"},[oe(),ne(),ae()]));break;case"Bottom":t.push(ie());break;case"Pager":t.push(se());break;default:Br("vxe.error.notProp",["layouts -> "+e])}}),t},ce={},de=(Vr.forEach(t=>{var e=al().camelCase("on-"+t);ce[e]=(...e)=>l(t,...e)}),()=>{var{proxyConfig:e,formConfig:t}=F,r=O.proxyInited,l=V.value,a=g.value;if(e&&nl(l)){if(t&&nl(a)&&l.form&&a.items){let o={};a.items.forEach(t=>{var{field:r,itemRender:l}=t;if(r){let e=null;l&&(l=l.defaultValue,al().isFunction(l)?e=l({item:t}):al().isUndefined(l)||(e=l)),o[r]=e}}),O.formData=o}r||!(O.proxyInited=!0)!==l.autoLoad&&(0,dl.nextTick)().then(()=>j.commitProxy("_init")).then(e=>{j.dispatchEvent("proxy-query",{...e,isInited:!0},new Event("init"))})}}),ue=e=>{var t=r.value;ut.hasKey(e,pt.ESCAPE)&&O.isZMax&&!1!==t.escRestore&&w.triggerZoomEvent(e)};let j={dispatchEvent:(e,t,r)=>{l(e,dt(r,{$grid:N},t))},commitProxy(t,...d){let{toolbarConfig:e,pagerConfig:n,editRules:u,validConfig:p}=F,i=O.tablePage,h=$.value,m=U.value;var r=V.value;let s=_.value;var l=H.value;let{beforeQuery:c,afterQuery:v,beforeDelete:f,afterDelete:g,beforeSave:x,afterSave:b,ajax:C={}}=r,w=r.response||r.props||{},y=A.value;var T=P();let E=null,R=null;R=al().isString(t)?(r=l.buttons,l=e&&nl(l)&&r?al().findTree(r,e=>e.code===t,{children:"dropdowns"}):null,E=l?l.item:null,t):(E=t).code;var o=E?E.params:null;switch(R){case"insert":return y.insert({});case"insert_edit":return y.insert({}).then(({row:e})=>y.setEditRow(e));case"insert_actived":return y.insert({}).then(({row:e})=>y.setEditRow(e));case"mark_cancel":I=R,M=$.value,S=A.value,(D=S.getCheckboxRecords()).length?(S.togglePendingRow(D),L.clearCheckboxRow()):M&&ol.VxeUI.modal&&ol.VxeUI.modal.message({id:I,content:he("vxe.grid.selectOneRecord"),status:"warning"});break;case"remove":return z(R,"vxe.grid.removeSelectRecord",()=>y.removeCheckboxRow());case"import":y.importData(o);break;case"open_import":y.openImport(o);break;case"export":y.exportData(o);break;case"open_export":y.openExport(o);break;case"reset_custom":return y.resetCustom(!0);case"_init":case"reload":case"query":{var S=C.query;let o=C.querySuccess,a=C.queryError;if(S){var I,D="_init"===R,M="reload"===R;let t=[],r=[],e={};if(n&&((D||M)&&(i.currentPage=1),nl(s))&&(e={...i}),D){let e=null;y&&(I=y.getComputeMaps().computeSortOpts,k=I.value,e=k.defaultSort),e&&(al().isArray(e)||(e=[e]),t=e.map(e=>({field:e.field,property:e.field,order:e.order}))),y&&(r=y.getCheckedFilters())}else y&&(M?y.clearAll():(t=y.getSortColumns(),r=y.getCheckedFilters()));let l={code:R,button:E,isInited:D,isReload:M,$grid:N,page:e,sort:t.length?t[0]:{},sorts:t,filters:r,form:T,options:S};return O.sortData=t,O.filterData=r,O.tableLoading=!0,Promise.resolve((c||S)(l,...d)).then(e=>{var t,r;return O.tableLoading=!1,e?n&&nl(s)?(r=w.total,r=(al().isFunction(r)?r({data:e,$grid:N}):al().get(e,r||"page.total"))||0,i.total=al().toNumber(r),t=w.result,O.tableData=(al().isFunction(t)?t({data:e,$grid:N}):al().get(e,t||"result"))||[],t=Math.max(Math.ceil(r/i.pageSize),1),i.currentPage>t&&(i.currentPage=t)):(r=w.list,O.tableData=(r?al().isFunction(r)?r({data:e,$grid:N}):al().get(e,r):e)||[]):O.tableData=[],v&&v(l,...d),o&&o({...l,response:e}),{status:!0}}).catch(e=>(O.tableLoading=!1,a&&a({...l,response:e}),{status:!1}))}Br("vxe.error.notFunc",["proxy-config.ajax.query"]);break}case"delete":{let l=C.delete,o=C.deleteSuccess,a=C.deleteError;if(l){let e=L.getCheckboxRecords(),t=e.filter(e=>!y.isInsertByRow(e));var k={removeRecords:t};let r={$grid:N,code:R,button:E,body:k,form:T,options:l};if(e.length)return z(R,"vxe.grid.deleteSelectRecord",()=>t.length?(O.tableLoading=!0,Promise.resolve((f||l)(r,...d)).then(e=>(O.tableLoading=!1,y.setPendingRow(t,!1),m&&ol.VxeUI.modal&&ol.VxeUI.modal.message({content:B(e,"vxe.grid.delSuccess"),status:"success"}),g?g(r,...d):j.commitProxy("query"),o&&o({...r,response:e}),{status:!0})).catch(e=>(O.tableLoading=!1,m&&ol.VxeUI.modal&&ol.VxeUI.modal.message({id:R,content:B(e,"vxe.grid.operError"),status:"error"}),a&&a({...r,response:e}),{status:!1}))):y.remove(e));h&&ol.VxeUI.modal&&ol.VxeUI.modal.message({id:R,content:he("vxe.grid.selectOneRecord"),status:"warning"})}else Br("vxe.error.notFunc",["proxy-config.ajax.delete"]);break}case"save":{let i=C.save,s=C.saveSuccess,c=C.saveError;if(i){let t=y.getRecordset(),{insertRecords:r,removeRecords:l,updateRecords:o,pendingRecords:a}=t,n={$grid:N,code:R,button:E,body:t,form:T,options:i},e=(r.length&&(t.pendingRecords=a.filter(e=>-1===y.findRowIndexOf(r,e))),a.length&&(t.insertRecords=r.filter(e=>-1===y.findRowIndexOf(a,e))),Promise.resolve());return(e=u?y[p&&"full"===p.msgMode?"fullValidate":"validate"](t.insertRecords.concat(o)):e).then(e=>{if(!e)return t.insertRecords.length||l.length||o.length||t.pendingRecords.length?(O.tableLoading=!0,Promise.resolve((x||i)(n,...d)).then(e=>(O.tableLoading=!1,y.clearPendingRow(),m&&ol.VxeUI.modal&&ol.VxeUI.modal.message({content:B(e,"vxe.grid.saveSuccess"),status:"success"}),b?b(n,...d):j.commitProxy("query"),s&&s({...n,response:e}),{status:!0})).catch(e=>(O.tableLoading=!1,m&&ol.VxeUI.modal&&ol.VxeUI.modal.message({id:R,content:B(e,"vxe.grid.operError"),status:"error"}),c&&c({...n,response:e}),{status:!1}))):void(h&&ol.VxeUI.modal&&ol.VxeUI.modal.message({id:R,content:he("vxe.grid.dataUnchanged"),status:"info"}))})}Br("vxe.error.notFunc",["proxy-config.ajax.save"]);break}default:var k=it.get(R);k&&((k=k.tableCommandMethod||k.commandMethod)?k({code:R,button:E,$grid:N,$table:y},...d):Br("vxe.error.notCommands",[R]))}return(0,dl.nextTick)()},zoom(){return O.isZMax?j.revert():j.maximize()},isMaximized(){return O.isZMax},maximize(){return ee(!0)},revert(){return ee()},getFormData:P,getFormItems(e){var t=g.value,r=F.formConfig,l=t.items;let o=[];return al().eachTree(r&&nl(t)&&l?l:[],e=>{o.push(e)},{children:"children"}),al().isUndefined(e)?o:o[e]},getProxyInfo(){var e,t=A.value;return F.proxyConfig?(e=O.sortData,{data:O.tableData,filter:O.filterData,form:P(),sort:e.length?e[0]:{},sorts:e,pager:O.tablePage,pendingRecords:t?t.getPendingRecords():[]}):null},loadColumn:e=>{var t=A.value;return al().eachTree(e,e=>{e.slots&&al().each(e.slots,e=>{al().isFunction(e)||s[e]||Br("vxe.error.notSlot",[e])})}),t?t.loadColumn(e):(0,dl.nextTick)()},reloadColumn:e=>(L.clearAll(),j.loadColumn(e))},w={extendTableMethods:t,callSlot(e,t){return e&&(al().isString(e)&&(e=s[e]||null),al().isFunction(e))?yl(e(t)):[]},getExcludeHeight(){var e=F.height,t=O.isZMax,r=c.value,l=p.value,o=h.value,a=m.value,n=v.value,i=f.value;return(t||"auto"!==e&&"100%"!==e?0:kl(r.parentNode))+kl(r)+vr(l)+vr(o)+vr(a)+vr(n)+vr(i)},getParentHeight(){var e=c.value;return e?(O.isZMax?mr().visibleHeight:al().toNumber(getComputedStyle(e.parentNode).height))-w.getExcludeHeight():0},triggerToolbarCommitEvent(e,t){let r=e.code;return j.commitProxy(e,t).then(e=>{r&&e&&e.status&&["query","reload","delete","save"].includes(r)&&j.dispatchEvent("delete"===r||"save"===r?"proxy-"+r:"proxy-query",{...e,isReload:"reload"===r},t)})},triggerToolbarBtnEvent(e,t){w.triggerToolbarCommitEvent(e,t),j.dispatchEvent("toolbar-button-click",{code:e.code,button:e},t)},triggerToolbarTolEvent(e,t){w.triggerToolbarCommitEvent(e,t),j.dispatchEvent("toolbar-tool-click",{code:e.code,tool:e},t)},triggerZoomEvent(e){j.zoom(),j.dispatchEvent("zoom",{type:O.isZMax?"max":"revert"},e)}},y=(Object.assign(N,L,j,w),(0,dl.ref)(0));(0,dl.watch)(()=>F.columns?F.columns.length:-1,()=>{y.value++}),(0,dl.watch)(()=>F.columns,()=>{y.value++}),(0,dl.watch)(y,()=>{(0,dl.nextTick)(()=>N.loadColumn(F.columns||[]))}),(0,dl.watch)(()=>F.toolbarConfig,()=>{o()}),(0,dl.watch)(k,()=>{x()}),(0,dl.watch)(()=>F.proxyConfig,()=>{de()}),st.forEach(e=>{var e=e.setupGrid;e&&(e=e(N))&&al().isObject(e)&&Object.assign(N,e)}),x(),(0,dl.onMounted)(()=>{(0,dl.nextTick)(()=>{F.formConfig&&!n&&Br("vxe.error.reqComp",["vxe-form"]),F.pagerConfig&&!i&&Br("vxe.error.reqComp",["vxe-pager"])}),(0,dl.nextTick)(()=>{var e=F.columns;e&&e.length&&N.loadColumn(e),o(),de()}),ut.on(N,"keydown",ue)}),(0,dl.onUnmounted)(()=>{ut.off(N,"keydown")});return N.renderVN=()=>{var e=R.value,t=S.value;return(0,dl.h)("div",{ref:c,class:["vxe-grid",{["size--"+e]:e,"is--animat":!!F.animat,"is--round":F.round,"is--maximize":O.isZMax,"is--loading":F.loading||O.tableLoading}],style:t},(()=>{var{headKeys:e,bodyKeys:t,footKeys:r}=M.value,l=s.asideLeft||s["aside-left"],o=s.asideRight||s["aside-right"];return[(0,dl.h)("div",{class:"vxe-grid--layout-header-wrapper"},C(e)),(0,dl.h)("div",{class:"vxe-grid--layout-body-wrapper"},[l?(0,dl.h)("div",{class:"vxe-grid--layout-aside-left-wrapper"},l({})):ht(N),(0,dl.h)("div",{class:"vxe-grid--layout-body-content-wrapper"},C(t)),o?(0,dl.h)("div",{class:"vxe-grid--layout-aside-right-wrapper"},o({})):ht(N)]),(0,dl.h)("div",{class:"vxe-grid--layout-footer-wrapper"},C(r))]})())},(0,dl.provide)("$xeGrid",N),N},render(){return this.renderVN()}});let ft=Object.assign({},Vo,{install(e){e.component(Vo.name,Vo)}}),gt=(ol.VxeUI.dynamicApp&&ol.VxeUI.dynamicApp.component(Vo.name,Vo),ol.VxeUI.component(Vo),ft),xt=[r,l,ft,a,i];function $o(t,e){ol.VxeUI.setConfig(e),xt.forEach(e=>e.install(t))}ol.VxeUI.hasLanguage("zh-CN")||(ol.VxeUI.setI18n("zh-CN",{vxe:{base:{pleaseInput:"请输入",pleaseSelect:"请选择",comma:"，",fullStop:"。"},loading:{text:"加载中..."},error:{downErr:"下载失败",errLargeData:"当绑定的数据量过大时，应该请使用 {0}，否则可能会出现卡顿",groupFixed:"如果使用分组表头，冻结列必须按组设置",groupMouseRange:'分组表头与 "{0}" 不能同时使用，这可能会出现错误',groupTag:'分组列头应该使用 "{0}" 而不是 "{1}"，这可能会出现错误',scrollErrProp:'启用虚拟滚动后不支持该参数 "{0}"',errConflicts:'参数 "{0}" 与 "{1}" 有冲突',unableInsert:"无法插入到指定位置，请检查参数是否正确",useErr:'安装 "{0}" 模块时发生错误，可能顺序不正确，依赖的模块需要在 Table 之前安装',barUnableLink:"工具栏无法关联表格",expandContent:'展开行的插槽应该是 "content"，请检查是否正确',reqComp:'缺少 "{0}" 组件，请检查是否正确安装。 https://vxeui.com/#/start/useGlobal',reqModule:'缺少 "{0}" 模块',reqProp:'缺少必要的 "{0}" 参数，这可能会导致出现错误',emptyProp:'参数 "{0}" 不允许为空',errProp:'不支持的参数 "{0}"，可能为 "{1}"',colRepet:'column.{0}="{1}" 重复了，这可能会导致某些功能无法使用',notFunc:'方法 "{0}" 不存在',errFunc:'参数 "{0}" 不是一个方法',notValidators:'全局校验 "{0}" 不存在',notFormats:'全局格式化 "{0}" 不存在',notCommands:'全局指令 "{0}" 不存在',notSlot:'插槽 "{0}" 不存在',noTree:'树结构不支持 "{0}"',notProp:'不支持的参数 "{0}"',checkProp:'当数据量过大时可能会导致复选框卡顿，建议设置参数 "{0}" 提升渲染速度',coverProp:'"{0}" 的参数 "{1}" 重复定义，这可能会出现错误',uniField:'字段名 "{0}" 重复定义，这可能会出现错误',repeatKey:'主键重复 {0}="{1}"，这可能会出现错误',delFunc:'方法 "{0}" 已废弃，请使用 "{1}"',delProp:'参数 "{0}" 已废弃，请使用 "{1}"',delEvent:'事件 "{0}" 已废弃，请使用 "{1}"',removeProp:'参数 "{0}" 已废弃，不建议使用，这可能会导致出现错误',errFormat:'全局的格式化内容应该使用 "VXETable.formats" 定义，挂载 "formatter={0}" 的方式已不建议使用',notType:'不支持的文件类型 "{0}"',notExp:"该浏览器不支持导入/导出功能",impFields:"导入失败，请检查字段名和数据格式是否正确",treeNotImp:"树表格不支持导入",treeCrossDrag:"只能拖拽第一层级",treeDragChild:"父级不能拖拽到自己的子级中",reqPlugin:'可选扩展插件 "{1}" https://vxeui.com/other{0}/#/{1}/install'},table:{emptyText:"暂无数据",allTitle:"全选/取消",seqTitle:"序号",actionTitle:"操作",confirmFilter:"筛选",resetFilter:"重置",allFilter:"全部",sortAsc:"升序：最低到最高",sortDesc:"降序：最高到最低",filter:"对所选的列启用筛选",impSuccess:"成功导入 {0} 条记录",expLoading:"正在导出中",expSuccess:"导出成功",expError:"导出失败",expFilename:"导出_{0}",expOriginFilename:"导出_源_{0}",customTitle:"列设置",customAll:"全部",customConfirm:"确认",customClose:"关闭",customCancel:"取消",customRestore:"恢复默认",maxFixedCol:"最大冻结列的数量不能超过 {0} 个",dragTip:"移动：{0}",resizeColTip:"宽：{0} 像素",resizeRowTip:"高：{0} 像素"},grid:{selectOneRecord:"请至少选择一条记录！",deleteSelectRecord:"您确定要删除所选记录吗？",removeSelectRecord:"您确定要移除所选记录吗？",dataUnchanged:"数据未改动！",delSuccess:"成功删除所选记录！",saveSuccess:"保存成功！",operError:"发生错误，操作失败！"},select:{search:"搜索",loadingText:"加载中",emptyText:"暂无数据"},pager:{goto:"前往",gotoTitle:"页数",pagesize:"{0}条/页",total:"共 {0} 条记录",pageClassifier:"页",homePage:"首页",homePageTitle:"首页",prevPage:"上一页",prevPageTitle:"上一页",nextPage:"下一页",nextPageTitle:"下一页",prevJump:"向上跳页",prevJumpTitle:"向上跳页",nextJump:"向下跳页",nextJumpTitle:"向下跳页",endPage:"末页",endPageTitle:"末页"},alert:{title:"系统提示"},button:{confirm:"确认",cancel:"取消"},filter:{search:"搜索"},custom:{cstmTitle:"列设置",cstmRestore:"恢复默认",cstmCancel:"取消",cstmConfirm:"确定",cstmConfirmRestore:"请确认是否恢复成默认列配置？",cstmDragTarget:"移动：{0}",setting:{colSort:"排序",sortHelpTip:"点击并拖动图标可以调整列的排序",colTitle:"列标题",colResizable:"列宽（像素）",colVisible:"是否显示",colFixed:"冻结列",colFixedMax:"冻结列（最多 {0} 列）",fixedLeft:"左侧",fixedUnset:"不设置",fixedRight:"右侧"}},import:{modes:{covering:"覆盖方式（直接覆盖表格数据）",insert:"底部追加（在表格的底部追加新数据）",insertTop:"顶部追加（在表格的顶部追加新数据）",insertBottom:"底部追加（在表格的底部追加新数据）"},impTitle:"导入数据",impFile:"文件名",impSelect:"选择文件",impType:"文件类型",impOpts:"参数设置",impMode:"导入模式",impConfirm:"导入",impCancel:"取消"},export:{types:{csv:"CSV (逗号分隔)(*.csv)",html:"网页(*.html)",xml:"XML 数据(*.xml)",txt:"文本文件(制表符分隔)(*.txt)",xls:"Excel 97-2003 工作簿(*.xls)",xlsx:"Excel 工作簿(*.xlsx)",pdf:"PDF (*.pdf)"},modes:{current:"当前数据（当前页的数据）",selected:"选中数据（当前页选中的数据）",all:"全量数据（包括所有分页的数据）"},printTitle:"打印数据",expTitle:"导出数据",expName:"文件名",expNamePlaceholder:"请输入文件名",expSheetName:"标题",expSheetNamePlaceholder:"请输入标题",expType:"保存类型",expMode:"选择数据",expCurrentColumn:"全部字段",expColumn:"选择字段",expOpts:"参数设置",expOptHeader:"表头",expHeaderTitle:"是否需要表头",expOptFooter:"表尾",expFooterTitle:"是否需要表尾",expOptColgroup:"分组表头",expColgroupTitle:"如果存在，则支持带有分组结构的表头",expOptMerge:"合并",expMergeTitle:"如果存在，则支持带有合并结构的单元格",expOptAllExpand:"展开层级",expAllExpandTitle:"如果存在，则支持将带有层级结构的数据全部展开",expOptUseStyle:"样式",expUseStyleTitle:"如果存在，则支持带样式的单元格",expOptOriginal:"源数据",expOriginalTitle:"如果为源数据，则支持导入到表格中",expPrint:"打印",expConfirm:"导出",expCancel:"取消"},modal:{errTitle:"错误提示",zoomMin:"最小化",zoomIn:"最大化",zoomOut:"还原",close:"关闭",miniMaxSize:"最小化窗口的数量不能超过 {0} 个",footPropErr:"show-footer 仅用于启用表尾，需配合 show-confirm-button | show-cancel-button | 插槽使用"},drawer:{close:"关闭"},form:{folding:"收起",unfolding:"展开"},toolbar:{import:"导入",export:"导出",print:"打印",refresh:"刷新",zoomIn:"全屏",zoomOut:"还原",custom:"列设置",customAll:"全部",customConfirm:"确认",customRestore:"重置",fixedLeft:"冻结在左侧",fixedRight:"冻结在右侧",cancelFixed:"取消冻结列"},input:{date:{m1:"01 月",m2:"02 月",m3:"03 月",m4:"04 月",m5:"05 月",m6:"06 月",m7:"07 月",m8:"08 月",m9:"09 月",m10:"10 月",m11:"11 月",m12:"12 月",quarterLabel:"{0} 年",monthLabel:"{0} 年",dayLabel:"{0} 年 {1}",labelFormat:{date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss",week:"yyyy 年第 WW 周",month:"yyyy-MM",quarter:"yyyy 年第 q 季度",year:"yyyy"},weeks:{w:"周",w0:"周日",w1:"周一",w2:"周二",w3:"周三",w4:"周四",w5:"周五",w6:"周六"},months:{m0:"一月",m1:"二月",m2:"三月",m3:"四月",m4:"五月",m5:"六月",m6:"七月",m7:"八月",m8:"九月",m9:"十月",m10:"十一月",m11:"十二月"},quarters:{q1:"第一季度",q2:"第二季度",q3:"第三季度",q4:"第四季度"}}},numberInput:{currencySymbol:"￥"},imagePreview:{popupTitle:"预览",operBtn:{zoomOut:"缩小",zoomIn:"放大",pctFull:"等比例缩放",pct11:"显示原始尺寸",rotateLeft:"向左旋转",rotateRight:"向右旋转",print:"点击打印图片",download:"点击下载图片"}},upload:{fileBtnText:"点击或拖拽上传",imgBtnText:"点击或拖拽上传",dragPlaceholder:"请把文件拖放到这个区域即可上传",imgSizeHint:"单张{0}",imgCountHint:"最多{0}张",fileTypeHint:"支持 {0} 文件类型",fileSizeHint:"单个文件大小不超过{0}",fileCountHint:"最多可上传{0}个文件",uploadTypeErr:"文件类型不匹配！",overCountErr:"最多只能选择{0}个文件！",overCountExtraErr:"已超出最大数量{0}个，超出的{1}个文件将被忽略！",overSizeErr:"文件大小最大不能超过{0}！",reUpload:"重新上传",uploadProgress:"上传中 {0}%",uploadErr:"上传失败",uploadSuccess:"上传成功",moreBtnText:"更多（{0}）",viewItemTitle:"点击查看",morePopup:{readTitle:"查看列表",imageTitle:"上传图片",fileTitle:"上传文件"}},empty:{defText:"暂无数据"},colorPicker:{clear:"清除",confirm:"确认",copySuccess:"已复制到剪贴板：{0}"},formDesign:{formName:"表单名称",defFormTitle:"未命名的表单",widgetPropTab:"控件属性",widgetFormTab:"表单属性",error:{wdFormUni:"该类型的控件在表单中只允许添加一个",wdSubUni:"该类型的控件在子表中只允许添加一个"},styleSetting:{btn:"样式设置",title:"表单的样式设置",layoutTitle:"控件布局",verticalLayout:"上下布局",horizontalLayout:"横向布局",styleTitle:"标题样式",boldTitle:"标题加粗",fontBold:"加粗",fontNormal:"常规",colonTitle:"显示冒号",colonVisible:"显示",colonHidden:"隐藏",alignTitle:"对齐方式",widthTitle:"标题宽度",alignLeft:"居左",alignRight:"居右",unitPx:"像素",unitPct:"百分比"},widget:{group:{base:"基础控件",layout:"布局控件",system:"系统控件",module:"模块控件",chart:"图表控件",advanced:"高级控件"},copyTitle:"副本_{0}",component:{input:"输入框",textarea:"文本域",select:"下拉选择",row:"一行多列",title:"标题",text:"文本",subtable:"子表",VxeSwitch:"是/否",VxeInput:"输入框",VxeNumberInput:"数字",VxeDatePicker:"日期",VxeTextarea:"文本域",VxeSelect:"下拉选择",VxeTreeSelect:"树形选择",VxeRadioGroup:"单选框",VxeCheckboxGroup:"复选框",VxeUploadFile:"文件",VxeUploadImage:"图片",VxeRate:"评分",VxeSlider:"滑块"}},widgetProp:{name:"控件名称",placeholder:"提示语",required:"必填校验",multiple:"允许多选",displaySetting:{name:"显示设置",pc:"电脑端",mobile:"手机端",visible:"显示",hidden:"隐藏"},dataSource:{name:"数据源",defValue:"选项{0}",addOption:"添加选项",batchEditOption:"批量编辑",batchEditTip:"每行对应一个选项，支持从表格、Excel、WPS 中直接复制粘贴。",batchEditSubTip:"每行对应一个选项，如果是分组，子项可以是空格或制表键开头，支持从表格、Excel、WPS 中直接复制粘贴。",buildOption:"生成选项"},rowProp:{colSize:"列数",col2:"两列",col3:"三列",col4:"四列",col6:"六列",layout:"布局"},textProp:{name:"内容",alignTitle:"对齐方式",alignLeft:"居左",alignCenter:"居中",alignRight:"居右",colorTitle:"字体颜色",sizeTitle:"字体大小",boldTitle:"字体加粗",fontNormal:"常规",fontBold:"加粗"},subtableProp:{seqTitle:"序号",showSeq:"显示序号",showCheckbox:"允许多选",errSubDrag:"子表不支持该控件，请使用其他控件",colPlace:"将控件拖拽进来"},uploadProp:{limitFileCount:"文件数量限制",limitFileSize:"文件大小限制",multiFile:"允许上传多个文件",limitImgCount:"图片数量限制",limitImgSize:"图片大小限制",multiImg:"允许上传多张图片"}}},listDesign:{fieldSettingTab:"字段设置",listSettingTab:"参数设置",searchTitle:"查询条件",listTitle:"列表字段",searchField:"查询字段",listField:"列表字段",activeBtn:{ActionButtonUpdate:"编辑",ActionButtonDelete:"删除"},search:{addBtn:"编辑",emptyText:"未配置查询条件",editPopupTitle:"编辑查询字段"},searchPopup:{colTitle:"标题",saveBtn:"保存"}},text:{copySuccess:"已复制到剪贴板",copyError:"当前环境不支持该操作"},countdown:{formats:{yyyy:"年",MM:"月",dd:"天",HH:"时",mm:"分",ss:"秒"}},plugins:{extendCellArea:{area:{mergeErr:"无法对合并单元格进行该操作",multiErr:"无法对多重选择区域进行该操作",selectErr:"无法操作指定区域的单元格",extendErr:"如果延伸的区域包含被合并的单元格，所有合并的单元格需大小相同",pasteMultiErr:"无法粘贴，需要相同大小的复制的区域和粘贴的区域才能执行此操作",cpInvalidErr:"该操作无法进行，您选择的区域中存在被禁止的列（{0}）"},fnr:{title:"查找和替换",findLabel:"查找",replaceLabel:"替换",findTitle:"查找内容：",replaceTitle:"替换为：",tabs:{find:"查找",replace:"替换"},filter:{re:"正则表达式",whole:"全词匹配",sensitive:"区分大小写"},btns:{findNext:"查找下一个",findAll:"查找全部",replace:"替换",replaceAll:"替换全部",cancel:"取消"},header:{seq:"#",cell:"单元格",value:"值"},body:{row:"行：{0}",col:"列：{0}"},empty:"(空值)",reError:"无效的正则表达式",recordCount:"已找到 {0} 个单元格",notCell:"找不到匹配的单元格",replaceSuccess:"成功替换 {0} 个单元格"}},filterComplexInput:{menus:{fixedColumn:"冻结列",fixedGroup:"冻结分组",cancelFixed:"取消冻结",fixedLeft:"冻结左侧",fixedRight:"冻结右侧"},cases:{equal:"等于",gt:"大于",lt:"小于",begin:"开头是",endin:"结尾是",include:"包含",isSensitive:"区分大小写"}},filterCombination:{menus:{clearSort:"清除排序",sortAsc:"升序",sortDesc:"降序",fixedColumn:"冻结列",fixedGroup:"冻结分组",cancelFixed:"取消冻结",fixedLeft:"冻结左侧",fixedRight:"冻结右侧",clearFilter:"清除筛选",textOption:"文本筛选",numberOption:"数值筛选"},popup:{title:"自定义筛选的方式",currColumnTitle:"当前列：",and:"与",or:"或",describeHtml:"可用 ? 代表单个字符<br/>用 * 代表任意多个字符"},cases:{equal:"等于",unequal:"不等于",gt:"大于",ge:"大于或等于",lt:"小于",le:"小于或等于",begin:"开头是",notbegin:"开头不是",endin:"结尾是",notendin:"结尾不是",include:"包含",exclude:"不包含",between:"介于",custom:"自定义筛选",insensitive:"不区分大小写",isSensitive:"区分大小写"},empty:"(空白)",notData:"无匹配项"}},pro:{area:{mergeErr:"无法对合并单元格进行该操作",multiErr:"无法对多重选择区域进行该操作",extendErr:"如果延伸的区域包含被合并的单元格，所有合并的单元格需大小相同",pasteMultiErr:"无法粘贴，需要相同大小的复制的区域和粘贴的区域才能执行此操作"},fnr:{title:"查找和替换",findLabel:"查找",replaceLabel:"替换",findTitle:"查找内容：",replaceTitle:"替换为：",tabs:{find:"查找",replace:"替换"},filter:{re:"正则表达式",whole:"全词匹配",sensitive:"区分大小写"},btns:{findNext:"查找下一个",findAll:"查找全部",replace:"替换",replaceAll:"替换全部",cancel:"取消"},header:{seq:"#",cell:"单元格",value:"值"},empty:"(空值)",reError:"无效的正则表达式",recordCount:"已找到 {0} 个单元格",notCell:"找不到匹配的单元格",replaceSuccess:"成功替换 {0} 个单元格"}},renderer:{search:"搜索",cases:{equal:"等于",unequal:"不等于",gt:"大于",ge:"大于或等于",lt:"小于",le:"小于或等于",begin:"开头是",notbegin:"开头不是",endin:"结尾是",notendin:"结尾不是",include:"包含",exclude:"不包含",between:"介于",custom:"自定义筛选",insensitive:"不区分大小写",isSensitive:"区分大小写"},combination:{menus:{clearSort:"清除排序",sortAsc:"升序",sortDesc:"降序",fixedColumn:"冻结列",fixedGroup:"冻结分组",cancelFixed:"取消冻结",fixedLeft:"冻结到左侧",fixedRight:"冻结到右侧",clearFilter:"清除筛选",textOption:"文本筛选",numberOption:"数值筛选"},popup:{title:"自定义筛选的方式",currColumnTitle:"当前列：",and:"与",or:"或",describeHtml:"可用 ? 代表单个字符<br/>用 * 代表任意多个字符"},empty:"(空白)",notData:"无匹配项"}}}}),ol.VxeUI.setLanguage("zh-CN")),ol.VxeUI.setTheme("light"),"undefined"!=typeof window&&window.Vue&&!window.VxeUITable&&(window.VxeUITable=zt);var _o=zt;return Bt}});
