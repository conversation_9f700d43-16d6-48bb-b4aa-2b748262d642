const { reactive, onMounted, markRaw } = Vue
export default {
  name: 'FilterInput',
  props: {
    params: Object,
  },
  setup(props) {
    const inputState = reactive({
      option: { data: null },
      columnField: null,
    })

    // 保存列和选项的映射关系
    const columnOptionsMap = markRaw(new Map())

    const load = () => {
      const { params } = props
      if (params) {
        const { column } = params
        // 保存列标识
        inputState.columnField = column.field
        
        // 从映射中获取该列的选项，如果没有则使用原始选项
        if (!columnOptionsMap.has(column.field) && column.filters?.[0]) {
          columnOptionsMap.set(column.field, column.filters[0])
        }
        
        if (columnOptionsMap.has(column.field)) {
          inputState.option = columnOptionsMap.get(column.field)
        } else if (column.filters?.[0]) {
          inputState.option = column.filters[0]
        }
      }
    }

    const changeOptionEvent = () => {
      const { params } = props
      if (params) {
        const { $panel } = params
        const { option, columnField } = inputState
        
        if (params.column.field === columnField) {
          const checked = !!option.data
          $panel.changeOption({}, checked, option)
        }
      }
    }

    const keyupEvent = ({ $event }) => {
      const { params } = props
      if (params) {
        const { $panel } = params
        if ($event.keyCode === 13) {
          $panel.confirmFilter($event)
        }
      }
    }

    onMounted(() => {
      load()
    })

    return {
      inputState,
      changeOptionEvent,
      keyupEvent,
    }
  },
  // language=HTML
  template: `
    <vxe-input type="text" v-model="inputState.option.data" placeholder="支持回车搜索" @keyup="keyupEvent"
               @change="changeOptionEvent"></vxe-input>
  `,
}
