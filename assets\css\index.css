/*导入tailwind.css*/
@import 'tailwind.css';
/*导入drawer.css*/
@import 'drawer.css';
/* 导入自定义table.css */
@import 'table.css';
/* 导入自定义gantt.css */
@import 'gantt.css';

.row-green {
  background-color: #d1e3d1;
}

.row-red {
  background-color: #de5683;
}
.row-orange {
  background-color: #f18c08;
}
.row-yellow {
  background-color: hsl(81, 92%, 50%);
}

.table-header {
  background-color: #5d7092 !important;
  color: #ffffff;
}

/* 甘特图message样式 */
.gantt_message_area {
  z-index: 9999 !important;
}

@keyframes scroll {
  0% {
    transform: translateX(0%);
  }
  50% {
    transform: translateX(-50%);
  }
  50.1% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0%);
  }
}
.scroll-content {
  display: inline-block;
  animation: scroll 30s linear infinite;
}

/* 隐藏甘特图开局提示 */
.gantt_message_area {
  display: none;
}

/* 自定义的el-descriptions */
.my-descriptions .el-descriptions__header {
  margin-bottom: 0;
}

.my-descriptions .el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell {
  padding: 0;
}

/* 发动机交付预测趋势图 */
.corner-border {
  overflow: hidden;
  background:
    linear-gradient(to left, #04c886, #04c886) left top no-repeat,
    linear-gradient(to bottom, #04c886, #04c886) left top no-repeat,
    linear-gradient(to left, #04c886, #04c886) left bottom no-repeat,
    linear-gradient(to left, #04c886, #04c886) right top no-repeat,
    linear-gradient(to bottom, #04c886, #04c886) right top no-repeat,
    linear-gradient(to bottom, #04c886, #04c886) left bottom no-repeat,
    linear-gradient(to left, #04c886, #04c886) right bottom no-repeat,
    linear-gradient(to left, #04c886, #04c886) right bottom no-repeat,
    linear-gradient(to left, #0a347c, #5d7092);

  background-size:
    2px 18px,
    18px 2px,
    2px 18px,
    18px 2px;
  box-shadow: 0 4px 8px rgba(173, 216, 230, 0.6);
}

.engine-form .el-form-item__label {
  color: #ffffff;
}

/** 改变浏览器原生滚动条样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
 /* 滚动槽 */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
  /* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 5px;
}
	[data-column-name="deadline"] .gantt_tree_content{
			display: flex;
			justify-content: space-between;
		}
.gantt_scale_cell.weekend {
  color: #fff !important;
  background: red !important;

}