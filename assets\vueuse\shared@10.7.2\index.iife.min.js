var VueDemi=function(c,u,E){if(c.install)return c;if(!u)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),c;if(u.version.slice(0,4)==="2.7."){let v=function(S,b){var T,_={},U={config:u.config,use:u.use.bind(u),mixin:u.mixin.bind(u),component:u.component.bind(u),provide:function(M,P){return _[M]=P,this},directive:function(M,P){return P?(u.directive(M,P),U):u.directive(M)},mount:function(M,P){return T||(T=new u(Object.assign({propsData:b},S,{provide:Object.assign(_,S.provide)})),T.$mount(M,P),T)},unmount:function(){T&&(T.$destroy(),T=void 0)}};return U};var I=v;for(var O in u)c[O]=u[O];c.isVue2=!0,c.isVue3=!1,c.install=function(){},c.Vue=u,c.Vue2=u,c.version=u.version,c.warn=u.util.warn,c.hasInjectionContext=()=>!!c.getCurrentInstance(),c.createApp=v}else if(u.version.slice(0,2)==="2.")if(E){for(var O in E)c[O]=E[O];c.isVue2=!0,c.isVue3=!1,c.install=function(){},c.Vue=u,c.Vue2=u,c.version=u.version,c.hasInjectionContext=()=>!!c.getCurrentInstance()}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(u.version.slice(0,2)==="3."){for(var O in u)c[O]=u[O];c.isVue2=!1,c.isVue3=!0,c.install=function(){},c.Vue=u,c.Vue2=void 0,c.version=u.version,c.set=function(v,S,b){return Array.isArray(v)?(v.length=Math.max(v.length,S),v.splice(S,1,b),b):(v[S]=b,b)},c.del=function(v,S){if(Array.isArray(v)){v.splice(S,1);return}delete v[S]}}else console.error("[vue-demi] Vue version "+u.version+" is unsupported.");return c}(this.VueDemi=this.VueDemi||(typeof VueDemi<"u"?VueDemi:{}),this.Vue||(typeof Vue<"u"?Vue:void 0),this.VueCompositionAPI||(typeof VueCompositionAPI<"u"?VueCompositionAPI:void 0));(function(c,u){"use strict";function E(t,e){var n;const r=u.shallowRef();return u.watchEffect(()=>{r.value=t()},{...e,flush:(n=e?.flush)!=null?n:"sync"}),u.readonly(r)}function O(t,e){let n,r,o;const i=u.ref(!0),a=()=>{i.value=!0,o()};u.watch(t,a,{flush:"sync"});const l=typeof e=="function"?e:e.get,d=typeof e=="function"?void 0:e.set,w=u.customRef((y,h)=>(r=y,o=h,{get(){return i.value&&(n=l(),i.value=!1),r(),n},set(f){d?.(f)}}));return Object.isExtensible(w)&&(w.trigger=a),w}function I(t){return u.getCurrentScope()?(u.onScopeDispose(t),!0):!1}function v(){const t=new Set,e=o=>{t.delete(o)};return{on:o=>{t.add(o);const i=()=>e(o);return I(i),{off:i}},off:e,trigger:(...o)=>Promise.all(Array.from(t).map(i=>i(...o)))}}function S(t){let e=!1,n;const r=u.effectScope(!0);return(...o)=>(e||(n=r.run(()=>t(...o)),e=!0),n)}const b=new WeakMap,T=(t,e)=>{var n;const r=(n=u.getCurrentInstance())==null?void 0:n.proxy;if(r==null)throw new Error("provideLocal must be called in setup");b.has(r)||b.set(r,Object.create(null));const o=b.get(r);o[t]=e,u.provide(t,e)},_=(...t)=>{var e;const n=t[0],r=(e=u.getCurrentInstance())==null?void 0:e.proxy;if(r==null)throw new Error("injectLocal must be called in setup");return b.has(r)&&n in b.get(r)?b.get(r)[n]:u.inject(...t)};function U(t,e){const n=e?.injectionKey||Symbol("InjectionState");return[(...i)=>{const a=t(...i);return T(n,a),a},()=>_(n)]}function M(t){let e=0,n,r;const o=()=>{e-=1,r&&e<=0&&(r.stop(),n=void 0,r=void 0)};return(...i)=>(e+=1,n||(r=u.effectScope(!0),n=r.run(()=>t(...i))),I(o),n)}function P(t,e,{enumerable:n=!1,unwrap:r=!0}={}){if(!u.isVue3&&!u.version.startsWith("2.7.")){if(process.env.NODE_ENV!=="production")throw new Error("[VueUse] extendRef only works in Vue 2.7 or above.");return}for(const[o,i]of Object.entries(e))o!=="value"&&(u.isRef(i)&&r?Object.defineProperty(t,o,{get(){return i.value},set(a){i.value=a},enumerable:n}):Object.defineProperty(t,o,{value:i,enumerable:n}));return t}function gt(t,e){return e==null?u.unref(t):u.unref(t)[e]}function mt(t){return u.unref(t)!=null}function pt(t,e){if(typeof Symbol<"u"){const n={...t};return Object.defineProperty(n,Symbol.iterator,{enumerable:!1,value(){let r=0;return{next:()=>({value:e[r++],done:r>e.length})}}}),n}else return Object.assign([...e],t)}function s(t){return typeof t=="function"?t():u.unref(t)}const vt=s;function Y(t,e){const n=e?.computedGetter===!1?u.unref:s;return function(...r){return u.computed(()=>t.apply(this,r.map(o=>n(o))))}}function bt(t,e={}){let n=[],r;if(Array.isArray(e))n=e;else{r=e;const{includeOwnProperties:o=!0}=e;n.push(...Object.keys(t)),o&&n.push(...Object.getOwnPropertyNames(t))}return Object.fromEntries(n.map(o=>{const i=t[o];return[o,typeof i=="function"?Y(i.bind(t),r):i]}))}function D(t){if(!u.isRef(t))return u.reactive(t);const e=new Proxy({},{get(n,r,o){return u.unref(Reflect.get(t.value,r,o))},set(n,r,o){return u.isRef(t.value[r])&&!u.isRef(o)?t.value[r].value=o:t.value[r]=o,!0},deleteProperty(n,r){return Reflect.deleteProperty(t.value,r)},has(n,r){return Reflect.has(t.value,r)},ownKeys(){return Object.keys(t.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return u.reactive(e)}function G(t){return D(u.computed(t))}function At(t,...e){const n=e.flat(),r=n[0];return G(()=>Object.fromEntries(typeof r=="function"?Object.entries(u.toRefs(t)).filter(([o,i])=>!r(s(i),o)):Object.entries(u.toRefs(t)).filter(o=>!n.includes(o[0]))))}const j=typeof window<"u"&&typeof document<"u",Ot=typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope,St=t=>typeof t<"u",Tt=t=>t!=null,Pt=(t,...e)=>{t||console.warn(...e)},Ft=Object.prototype.toString,tt=t=>Ft.call(t)==="[object Object]",It=()=>Date.now(),et=()=>+Date.now(),Mt=(t,e,n)=>Math.min(n,Math.max(e,t)),C=()=>{},Ct=(t,e)=>(t=Math.ceil(t),e=Math.floor(e),Math.floor(Math.random()*(e-t+1))+t),Rt=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),Et=kt();function kt(){var t,e;return j&&((t=window?.navigator)==null?void 0:t.userAgent)&&(/iP(ad|hone|od)/.test(window.navigator.userAgent)||((e=window?.navigator)==null?void 0:e.maxTouchPoints)>2&&/iPad|Macintosh/.test(window?.navigator.userAgent))}function N(t,e){function n(...r){return new Promise((o,i)=>{Promise.resolve(t(()=>e.apply(this,r),{fn:e,thisArg:this,args:r})).then(o).catch(i)})}return n}const B=t=>t();function z(t,e={}){let n,r,o=C;const i=l=>{clearTimeout(l),o(),o=C};return l=>{const d=s(t),w=s(e.maxWait);return n&&i(n),d<=0||w!==void 0&&w<=0?(r&&(i(r),r=null),Promise.resolve(l())):new Promise((y,h)=>{o=e.rejectOnCancel?h:y,w&&!r&&(r=setTimeout(()=>{n&&i(n),r=null,y(l())},w)),n=setTimeout(()=>{r&&i(r),r=null,y(l())},d)})}}function q(t,e=!0,n=!0,r=!1){let o=0,i,a=!0,l=C,d;const w=()=>{i&&(clearTimeout(i),i=void 0,l(),l=C)};return h=>{const f=s(t),g=Date.now()-o,m=()=>d=h();return w(),f<=0?(o=Date.now(),m()):(g>f&&(n||!a)?(o=Date.now(),m()):e&&(d=new Promise((A,p)=>{l=r?p:A,i=setTimeout(()=>{o=Date.now(),a=!0,A(m()),w()},Math.max(0,f-g))})),!n&&!i&&(i=setTimeout(()=>a=!0,f)),a=!1,d)}}function nt(t=B){const e=u.ref(!0);function n(){e.value=!1}function r(){e.value=!0}const o=(...i)=>{e.value&&t(...i)};return{isActive:u.readonly(e),pause:n,resume:r,eventFilter:o}}const _t={mounted:u.isVue3?"mounted":"inserted",updated:u.isVue3?"updated":"componentUpdated",unmounted:u.isVue3?"unmounted":"unbind"};function rt(t){const e=Object.create(null);return n=>e[n]||(e[n]=t(n))}const jt=/\B([A-Z])/g,Nt=rt(t=>t.replace(jt,"-$1").toLowerCase()),Lt=/-(\w)/g,Wt=rt(t=>t.replace(Lt,(e,n)=>n?n.toUpperCase():""));function Z(t,e=!1,n="Timeout"){return new Promise((r,o)=>{setTimeout(e?()=>o(n):r,t)})}function Ut(t){return t}function Bt(t){let e;function n(){return e||(e=t()),e}return n.reset=async()=>{const r=e;e=void 0,r&&await r},n}function $t(t){return t()}function ot(t,...e){return e.some(n=>n in t)}function Ht(t,e){var n;if(typeof t=="number")return t+e;const r=((n=t.match(/^-?[0-9]+\.?[0-9]*/))==null?void 0:n[0])||"",o=t.slice(r.length),i=Number.parseFloat(r)+e;return Number.isNaN(i)?t:i+o}function Yt(t,e,n=!1){return e.reduce((r,o)=>(o in t&&(!n||t[o]!==void 0)&&(r[o]=t[o]),r),{})}function Gt(t,e,n=!1){return Object.fromEntries(Object.entries(t).filter(([r,o])=>(!n||o!==void 0)&&!e.includes(r)))}function zt(t){return Object.entries(t)}function L(t){return t||u.getCurrentInstance()}function J(...t){if(t.length!==1)return u.toRef(...t);const e=t[0];return typeof e=="function"?u.readonly(u.customRef(()=>({get:e,set:C}))):u.ref(e)}const qt=J;function Zt(t,...e){const n=e.flat(),r=n[0];return G(()=>Object.fromEntries(typeof r=="function"?Object.entries(u.toRefs(t)).filter(([o,i])=>r(s(i),o)):n.map(o=>[o,J(t,o)])))}function ct(t,e=1e4){return u.customRef((n,r)=>{let o=s(t),i;const a=()=>setTimeout(()=>{o=s(t),r()},s(e));return I(()=>{clearTimeout(i)}),{get(){return n(),o},set(l){o=l,r(),clearTimeout(i),i=a()}}})}function ut(t,e=200,n={}){return N(z(e,n),t)}function X(t,e=200,n={}){const r=u.ref(t.value),o=ut(()=>{r.value=t.value},e,n);return u.watch(t,()=>o()),r}function Jt(t,e){return u.computed({get(){var n;return(n=t.value)!=null?n:e},set(n){t.value=n}})}function it(t,e=200,n=!1,r=!0,o=!1){return N(q(e,n,r,o),t)}function K(t,e=200,n=!0,r=!0){if(e<=0)return t;const o=u.ref(t.value),i=it(()=>{o.value=t.value},e,n,r);return u.watch(t,()=>i()),o}function at(t,e={}){let n=t,r,o;const i=u.customRef((f,g)=>(r=f,o=g,{get(){return a()},set(m){l(m)}}));function a(f=!0){return f&&r(),n}function l(f,g=!0){var m,A;if(f===n)return;const p=n;((m=e.onBeforeChange)==null?void 0:m.call(e,f,p))!==!1&&(n=f,(A=e.onChanged)==null||A.call(e,f,p),g&&o())}return P(i,{get:a,set:l,untrackedGet:()=>a(!1),silentSet:f=>l(f,!1),peek:()=>a(!1),lay:f=>l(f,!1)},{enumerable:!0})}const Xt=at;function Kt(...t){if(t.length===2){const[e,n]=t;e.value=n}if(t.length===3)if(u.isVue2)u.set(...t);else{const[e,n,r]=t;e[n]=r}}function W(t,e,n={}){const{eventFilter:r=B,...o}=n;return u.watch(t,N(r,e),o)}function $(t,e,n={}){const{eventFilter:r,...o}=n,{eventFilter:i,pause:a,resume:l,isActive:d}=nt(r);return{stop:W(t,e,{...o,eventFilter:i}),pause:a,resume:l,isActive:d}}function Qt(t,e,...[n]){const{flush:r="sync",deep:o=!1,immediate:i=!0,direction:a="both",transform:l={}}=n||{},d=[],w="ltr"in l&&l.ltr||(f=>f),y="rtl"in l&&l.rtl||(f=>f);return(a==="both"||a==="ltr")&&d.push($(t,f=>{d.forEach(g=>g.pause()),e.value=w(f),d.forEach(g=>g.resume())},{flush:r,deep:o,immediate:i})),(a==="both"||a==="rtl")&&d.push($(e,f=>{d.forEach(g=>g.pause()),t.value=y(f),d.forEach(g=>g.resume())},{flush:r,deep:o,immediate:i})),()=>{d.forEach(f=>f.stop())}}function Vt(t,e,n={}){const{flush:r="sync",deep:o=!1,immediate:i=!0}=n;return Array.isArray(e)||(e=[e]),u.watch(t,a=>e.forEach(l=>l.value=a),{flush:r,deep:o,immediate:i})}function xt(t,e={}){if(!u.isRef(t))return u.toRefs(t);const n=Array.isArray(t.value)?Array.from({length:t.value.length}):{};for(const r in t.value)n[r]=u.customRef(()=>({get(){return t.value[r]},set(o){var i;if((i=s(e.replaceRef))!=null?i:!0)if(Array.isArray(t.value)){const l=[...t.value];l[r]=o,t.value=l}else{const l={...t.value,[r]:o};Object.setPrototypeOf(l,Object.getPrototypeOf(t.value)),t.value=l}else t.value[r]=o}}));return n}function Dt(t,e=!0,n){L(n)?u.onBeforeMount(t,n):e?t():u.nextTick(t)}function te(t,e){L(e)&&u.onBeforeUnmount(t,e)}function ee(t,e=!0,n){L()?u.onMounted(t,n):e?t():u.nextTick(t)}function ne(t,e){L(e)&&u.onUnmounted(t,e)}function Q(t,e=!1){function n(h,{flush:f="sync",deep:g=!1,timeout:m,throwOnTimeout:A}={}){let p=null;const x=[new Promise(H=>{p=u.watch(t,k=>{h(k)!==e&&(p?.(),H(k))},{flush:f,deep:g,immediate:!0})})];return m!=null&&x.push(Z(m,A).then(()=>s(t)).finally(()=>p?.())),Promise.race(x)}function r(h,f){if(!u.isRef(h))return n(k=>k===h,f);const{flush:g="sync",deep:m=!1,timeout:A,throwOnTimeout:p}=f??{};let F=null;const H=[new Promise(k=>{F=u.watch([t,h],([wt,He])=>{e!==(wt===He)&&(F?.(),k(wt))},{flush:g,deep:m,immediate:!0})})];return A!=null&&H.push(Z(A,p).then(()=>s(t)).finally(()=>(F?.(),s(t)))),Promise.race(H)}function o(h){return n(f=>!!f,h)}function i(h){return r(null,h)}function a(h){return r(void 0,h)}function l(h){return n(Number.isNaN,h)}function d(h,f){return n(g=>{const m=Array.from(g);return m.includes(h)||m.includes(s(h))},f)}function w(h){return y(1,h)}function y(h=1,f){let g=-1;return n(()=>(g+=1,g>=h),f)}return Array.isArray(s(t))?{toMatch:n,toContains:d,changed:w,changedTimes:y,get not(){return Q(t,!e)}}:{toMatch:n,toBe:r,toBeTruthy:o,toBeNull:i,toBeNaN:l,toBeUndefined:a,changed:w,changedTimes:y,get not(){return Q(t,!e)}}}function re(t){return Q(t)}function oe(t,e){return t===e}function ce(...t){var e;const n=t[0],r=t[1];let o=(e=t[2])!=null?e:oe;if(typeof o=="string"){const i=o;o=(a,l)=>a[i]===l[i]}return u.computed(()=>s(n).filter(i=>s(r).findIndex(a=>o(i,a))===-1))}function ue(t,e){return u.computed(()=>s(t).every((n,r,o)=>e(s(n),r,o)))}function ie(t,e){return u.computed(()=>s(t).map(n=>s(n)).filter(e))}function ae(t,e){return u.computed(()=>s(s(t).find((n,r,o)=>e(s(n),r,o))))}function le(t,e){return u.computed(()=>s(t).findIndex((n,r,o)=>e(s(n),r,o)))}function se(t,e){let n=t.length;for(;n-- >0;)if(e(t[n],n,t))return t[n]}function fe(t,e){return u.computed(()=>s(Array.prototype.findLast?s(t).findLast((n,r,o)=>e(s(n),r,o)):se(s(t),(n,r,o)=>e(s(n),r,o))))}function de(t){return tt(t)&&ot(t,"formIndex","comparator")}function he(...t){var e;const n=t[0],r=t[1];let o=t[2],i=0;if(de(o)&&(i=(e=o.fromIndex)!=null?e:0,o=o.comparator),typeof o=="string"){const a=o;o=(l,d)=>l[a]===s(d)}return o=o??((a,l)=>a===s(l)),u.computed(()=>s(n).slice(i).some((a,l,d)=>o(s(a),s(r),l,s(d))))}function ye(t,e){return u.computed(()=>s(t).map(n=>s(n)).join(s(e)))}function we(t,e){return u.computed(()=>s(t).map(n=>s(n)).map(e))}function ge(t,e,...n){const r=(o,i,a)=>e(s(o),s(i),a);return u.computed(()=>{const o=s(t);return n.length?o.reduce(r,s(n[0])):o.reduce(r)})}function me(t,e){return u.computed(()=>s(t).some((n,r,o)=>e(s(n),r,o)))}function pe(t){return Array.from(new Set(t))}function ve(t,e){return t.reduce((n,r)=>(n.some(o=>e(r,o,t))||n.push(r),n),[])}function be(t,e){return u.computed(()=>{const n=s(t).map(r=>s(r));return e?ve(n,e):pe(n)})}function Ae(t=0,e={}){let n=u.unref(t);const r=u.ref(t),{max:o=Number.POSITIVE_INFINITY,min:i=Number.NEGATIVE_INFINITY}=e,a=(h=1)=>r.value=Math.min(o,r.value+h),l=(h=1)=>r.value=Math.max(i,r.value-h),d=()=>r.value,w=h=>r.value=Math.max(i,Math.min(o,h));return{count:r,inc:a,dec:l,get:d,set:w,reset:(h=n)=>(n=h,w(h))}}const Oe=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,Se=/[YMDHhms]o|\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|SSS/g;function Te(t,e,n,r){let o=t<12?"AM":"PM";return r&&(o=o.split("").reduce((i,a)=>i+=`${a}.`,"")),n?o.toLowerCase():o}function R(t){const e=["th","st","nd","rd"],n=t%100;return t+(e[(n-20)%10]||e[n]||e[0])}function lt(t,e,n={}){var r;const o=t.getFullYear(),i=t.getMonth(),a=t.getDate(),l=t.getHours(),d=t.getMinutes(),w=t.getSeconds(),y=t.getMilliseconds(),h=t.getDay(),f=(r=n.customMeridiem)!=null?r:Te,g={Yo:()=>R(o),YY:()=>String(o).slice(-2),YYYY:()=>o,M:()=>i+1,Mo:()=>R(i+1),MM:()=>`${i+1}`.padStart(2,"0"),MMM:()=>t.toLocaleDateString(n.locales,{month:"short"}),MMMM:()=>t.toLocaleDateString(n.locales,{month:"long"}),D:()=>String(a),Do:()=>R(a),DD:()=>`${a}`.padStart(2,"0"),H:()=>String(l),Ho:()=>R(l),HH:()=>`${l}`.padStart(2,"0"),h:()=>`${l%12||12}`.padStart(1,"0"),ho:()=>R(l%12||12),hh:()=>`${l%12||12}`.padStart(2,"0"),m:()=>String(d),mo:()=>R(d),mm:()=>`${d}`.padStart(2,"0"),s:()=>String(w),so:()=>R(w),ss:()=>`${w}`.padStart(2,"0"),SSS:()=>`${y}`.padStart(3,"0"),d:()=>h,dd:()=>t.toLocaleDateString(n.locales,{weekday:"narrow"}),ddd:()=>t.toLocaleDateString(n.locales,{weekday:"short"}),dddd:()=>t.toLocaleDateString(n.locales,{weekday:"long"}),A:()=>f(l,d),AA:()=>f(l,d,!1,!0),a:()=>f(l,d,!0),aa:()=>f(l,d,!0,!0)};return e.replace(Se,(m,A)=>{var p,F;return(F=A??((p=g[m])==null?void 0:p.call(g)))!=null?F:m})}function st(t){if(t===null)return new Date(Number.NaN);if(t===void 0)return new Date;if(t instanceof Date)return new Date(t);if(typeof t=="string"&&!/Z$/i.test(t)){const e=t.match(Oe);if(e){const n=e[2]-1||0,r=(e[7]||"0").substring(0,3);return new Date(e[1],n,e[3]||1,e[4]||0,e[5]||0,e[6]||0,r)}}return new Date(t)}function Pe(t,e="HH:mm:ss",n={}){return u.computed(()=>lt(st(s(t)),s(e),n))}function ft(t,e=1e3,n={}){const{immediate:r=!0,immediateCallback:o=!1}=n;let i=null;const a=u.ref(!1);function l(){i&&(clearInterval(i),i=null)}function d(){a.value=!1,l()}function w(){const y=s(e);y<=0||(a.value=!0,o&&t(),l(),i=setInterval(t,y))}if(r&&j&&w(),u.isRef(e)||typeof e=="function"){const y=u.watch(e,()=>{a.value&&j&&w()});I(y)}return I(d),{isActive:a,pause:d,resume:w}}function Fe(t=1e3,e={}){const{controls:n=!1,immediate:r=!0,callback:o}=e,i=u.ref(0),a=()=>i.value+=1,l=()=>{i.value=0},d=ft(o?()=>{a(),o(i.value)}:a,t,{immediate:r});return n?{counter:i,reset:l,...d}:i}function Ie(t,e={}){var n;const r=u.ref((n=e.initialValue)!=null?n:null);return u.watch(t,()=>r.value=et(),e),r}function dt(t,e,n={}){const{immediate:r=!0}=n,o=u.ref(!1);let i=null;function a(){i&&(clearTimeout(i),i=null)}function l(){o.value=!1,a()}function d(...w){a(),o.value=!0,i=setTimeout(()=>{o.value=!1,i=null,t(...w)},s(e))}return r&&(o.value=!0,j&&d()),I(l),{isPending:u.readonly(o),start:d,stop:l}}function Me(t=1e3,e={}){const{controls:n=!1,callback:r}=e,o=dt(r??C,t,e),i=u.computed(()=>!o.isPending.value);return n?{ready:i,...o}:i}function Ce(t,e={}){const{method:n="parseFloat",radix:r,nanToZero:o}=e;return u.computed(()=>{let i=s(t);return typeof i=="string"&&(i=Number[n](i,r)),o&&Number.isNaN(i)&&(i=0),i})}function Re(t){return u.computed(()=>`${s(t)}`)}function Ee(t=!1,e={}){const{truthyValue:n=!0,falsyValue:r=!1}=e,o=u.isRef(t),i=u.ref(t);function a(l){if(arguments.length)return i.value=l,i.value;{const d=s(n);return i.value=i.value===d?s(r):d,i.value}}return o?a:[i,a]}function ke(t,e,n){let r=n?.immediate?[]:[...t instanceof Function?t():Array.isArray(t)?t:s(t)];return u.watch(t,(o,i,a)=>{const l=Array.from({length:r.length}),d=[];for(const y of o){let h=!1;for(let f=0;f<r.length;f++)if(!l[f]&&y===r[f]){l[f]=!0,h=!0;break}h||d.push(y)}const w=r.filter((y,h)=>!l[h]);e(o,r,d,w,a),r=[...o]},n)}function _e(t,e,n){const{count:r,...o}=n,i=u.ref(0),a=W(t,(...l)=>{i.value+=1,i.value>=s(r)&&u.nextTick(()=>a()),e(...l)},o);return{count:i,stop:a}}function ht(t,e,n={}){const{debounce:r=0,maxWait:o=void 0,...i}=n;return W(t,e,{...i,eventFilter:z(r,{maxWait:o})})}function je(t,e,n){return u.watch(t,e,{...n,deep:!0})}function V(t,e,n={}){const{eventFilter:r=B,...o}=n,i=N(r,e);let a,l,d;if(o.flush==="sync"){const w=u.ref(!1);l=()=>{},a=y=>{w.value=!0,y(),w.value=!1},d=u.watch(t,(...y)=>{w.value||i(...y)},o)}else{const w=[],y=u.ref(0),h=u.ref(0);l=()=>{y.value=h.value},w.push(u.watch(t,()=>{h.value++},{...o,flush:"sync"})),a=f=>{const g=h.value;f(),y.value+=h.value-g},w.push(u.watch(t,(...f)=>{const g=y.value>0&&y.value===h.value;y.value=0,h.value=0,!g&&i(...f)},o)),d=()=>{w.forEach(f=>f())}}return{stop:d,ignoreUpdates:a,ignorePrevAsyncUpdates:l}}function Ne(t,e,n){return u.watch(t,e,{...n,immediate:!0})}function Le(t,e,n){const r=u.watch(t,(...o)=>(u.nextTick(()=>r()),e(...o)),n);return r}function yt(t,e,n={}){const{throttle:r=0,trailing:o=!0,leading:i=!0,...a}=n;return W(t,e,{...a,eventFilter:q(r,o,i)})}function We(t,e,n={}){let r;function o(){if(!r)return;const y=r;r=void 0,y()}function i(y){r=y}const a=(y,h)=>(o(),e(y,h,i)),l=V(t,a,n),{ignoreUpdates:d}=l;return{...l,trigger:()=>{let y;return d(()=>{y=a(Ue(t),Be(t))}),y}}}function Ue(t){return u.isReactive(t)?t:Array.isArray(t)?t.map(e=>s(e)):s(t)}function Be(t){return Array.isArray(t)?t.map(()=>{}):void 0}function $e(t,e,n){return u.watch(t,(r,o,i)=>{r&&e(r,o,i)},n)}c.assert=Pt,c.autoResetRef=ct,c.bypassFilter=B,c.camelize=Wt,c.clamp=Mt,c.computedEager=E,c.computedWithControl=O,c.containsProp=ot,c.controlledComputed=O,c.controlledRef=Xt,c.createEventHook=v,c.createFilterWrapper=N,c.createGlobalState=S,c.createInjectionState=U,c.createReactiveFn=Y,c.createSharedComposable=M,c.createSingletonPromise=Bt,c.debounceFilter=z,c.debouncedRef=X,c.debouncedWatch=ht,c.directiveHooks=_t,c.eagerComputed=E,c.extendRef=P,c.formatDate=lt,c.get=gt,c.getLifeCycleTarget=L,c.hasOwn=Rt,c.hyphenate=Nt,c.identity=Ut,c.ignorableWatch=V,c.increaseWithUnit=Ht,c.injectLocal=_,c.invoke=$t,c.isClient=j,c.isDef=St,c.isDefined=mt,c.isIOS=Et,c.isObject=tt,c.isWorker=Ot,c.makeDestructurable=pt,c.noop=C,c.normalizeDate=st,c.notNullish=Tt,c.now=It,c.objectEntries=zt,c.objectOmit=Gt,c.objectPick=Yt,c.pausableFilter=nt,c.pausableWatch=$,c.promiseTimeout=Z,c.provideLocal=T,c.rand=Ct,c.reactify=Y,c.reactifyObject=bt,c.reactiveComputed=G,c.reactiveOmit=At,c.reactivePick=Zt,c.refAutoReset=ct,c.refDebounced=X,c.refDefault=Jt,c.refThrottled=K,c.refWithControl=at,c.resolveRef=qt,c.resolveUnref=vt,c.set=Kt,c.syncRef=Qt,c.syncRefs=Vt,c.throttleFilter=q,c.throttledRef=K,c.throttledWatch=yt,c.timestamp=et,c.toReactive=D,c.toRef=J,c.toRefs=xt,c.toValue=s,c.tryOnBeforeMount=Dt,c.tryOnBeforeUnmount=te,c.tryOnMounted=ee,c.tryOnScopeDispose=I,c.tryOnUnmounted=ne,c.until=re,c.useArrayDifference=ce,c.useArrayEvery=ue,c.useArrayFilter=ie,c.useArrayFind=ae,c.useArrayFindIndex=le,c.useArrayFindLast=fe,c.useArrayIncludes=he,c.useArrayJoin=ye,c.useArrayMap=we,c.useArrayReduce=ge,c.useArraySome=me,c.useArrayUnique=be,c.useCounter=Ae,c.useDateFormat=Pe,c.useDebounce=X,c.useDebounceFn=ut,c.useInterval=Fe,c.useIntervalFn=ft,c.useLastChanged=Ie,c.useThrottle=K,c.useThrottleFn=it,c.useTimeout=Me,c.useTimeoutFn=dt,c.useToNumber=Ce,c.useToString=Re,c.useToggle=Ee,c.watchArray=ke,c.watchAtMost=_e,c.watchDebounced=ht,c.watchDeep=je,c.watchIgnorable=V,c.watchImmediate=Ne,c.watchOnce=Le,c.watchPausable=$,c.watchThrottled=yt,c.watchTriggerable=We,c.watchWithFilter=W,c.whenever=$e})(this.VueUse=this.VueUse||{},VueDemi);
