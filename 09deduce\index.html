<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <!--    全部样式-->
  <link href="../assets/css/index.css" rel="stylesheet">
  <!--    引入element-plus的样式-->
  <link href="../assets/element-plus@2.5.5/index.css" rel="stylesheet">
  <!--    引入vxe-table的样式-->
  <link href="../assets/vxe-table/style.css" rel="stylesheet">
  <!--    引入VUE-->
  <script src="../assets/vue@3.4.15/vue.global.prod.js"></script>
  <!--    引入vxe-table组件-->
  <script src="../assets/vxe-table/xe-utils.js"></script>
  <script src="../assets/vxe-table/vxe-table.js"></script>
  <!--    引入element-plus-->
  <script src="../assets/element-plus@2.5.5/index.js"></script>
  <script src="../assets/element-plus@2.5.5/lang/zh-cn.js"></script>
  <!--  引入element-plus-icon-->
  <script src="../assets/icons-vue@2.3.1/index.iife.min.js"></script>
  <!--    引入axios-->
  <script src="../assets/axios@1.6.7/axios.min.js"></script>
  <!--    引入@vueuse/core-->
  <script src="../assets/@vueuse/shared@10.7.2/index.iife.min.js"></script>
  <script src="../assets/@vueuse/core@10.7.2/index.iife.min.js"></script>
  <!--    引入moment-->
  <script src="../assets/moment/moment.min.js"></script>
  <!--    引入echarts-->
  <script src="../assets/echarts@5.5.0/echarts.min.js"></script>
  <!--    引入xlsx-->
  <script src="../assets/xlsx@0.16.8/xlsx.full.min.js"></script>

  <!--    引入lodash-->
  <script src="../assets/plugins/lodash/lodash.js"></script>
  <title>沙盘推演</title>
</head>

<body>
  <div id='app'>
    <y-global-deuce></y-global-deuce>
  </div>
  </div>



</body>

<script type='module'>
  import yGlobalDeuce from './global.js'
  const { createApp, ref } = Vue
  const app = createApp({
    components: {

    },
  })
  app.component('y-global-deuce', yGlobalDeuce); // 第三方组件
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
  }
  app.use(ElementPlus,);
  app.mount('#app');
  // {
  //   locale: zhCn,
  // }




</script>

</html>