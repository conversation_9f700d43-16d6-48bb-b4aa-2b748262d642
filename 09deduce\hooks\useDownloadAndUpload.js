import { post } from '../../config/axios/httpReuest.js'

export function useDownloadAndUpload() {
  /**
   * 文件类型验证
   * @param {File} file 文件对象
   * @returns {Boolean} 验证结果
   */
  const validateFile = (file) => {
    const isExcel = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ].includes(file.type)
    
    if (!isExcel) {
      ElementPlus.ElMessage.error('只能上传 Excel 文件！')
      return false
    }
    
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      ElementPlus.ElMessage.error('文件大小不能超过 2MB！')
      return false
    }
    
    return true
  }

  /**
   * 下载模板
   * @param {Object} templateData 模板数据
   * @param {String} templateName 模板名称
   */
  const downloadTemplate = (templateData, templateName) => {
    try {
      const ws = XLSX.utils.aoa_to_sheet(templateData)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
      XLSX.writeFile(wb, templateName)
    } catch (error) {
      ElementPlus.ElMessage.error('模板下载失败：' + error.message)
    }
  }

  /**
   * 解析Excel文件
   * @param {ArrayBuffer} buffer 文件buffer
   * @param {Object} map 映射关系
   * @returns {Array} 解析结果
   */
  const parseExcel = (buffer, map) => {
    console.log(map)
    try {
      const data = new Uint8Array(buffer)
      const workbook = XLSX.read(data, { 
        type: 'array',
        cellDates: true,
        dateNF: 'yyyy-mm-dd'
      })
      
      const sheet = workbook.Sheets[workbook.SheetNames[0]]
      const headers = getHeaderRow(sheet)
      const requiredHeaders = Object.keys(map)
      const missingHeaders = requiredHeaders.filter(header => !headers.includes(header))
      
      if (missingHeaders.length > 0) {
        throw new Error(`缺少必要的表头: ${missingHeaders.join(', ')}`)
      }

      const json = XLSX.utils.sheet_to_json(sheet, { 
        raw: false,
        dateNF: 'yyyy-mm-dd',
        defval: ''
      })

      if (json.length === 0) {
        throw new Error('Excel 文件中没有数据')
      }

      return json.map((item, index) => {
        const obj = {}
        for (const key in item) {
          const value = item[key]
          
          if (value === undefined || value === null || value === '') {
            obj[map[key]] = ''
            continue
          }

          if (typeof value === 'number') {
            obj[map[key]] = value.toString()
            continue
          }

          if (value instanceof Date) {
            obj[map[key]] = formatDate(value)
            continue
          }

          if (typeof value === 'string') {
            const trimmedValue = value.trim()
            
            const datePattern = /^(\d{4}[-/](0[1-9]|1[0-2])[-/](0[1-9]|[12]\d|3[01]))$/
            if (datePattern.test(trimmedValue)) {
              const parsedDate = new Date(trimmedValue.replace(/\//g, '-'))
              if (!isNaN(parsedDate.getTime())) {
                obj[map[key]] = formatDate(parsedDate)
                continue
              }
            }
            
            obj[map[key]] = trimmedValue
            continue
          }

          obj[map[key]] = String(value)
        }
        obj.row = index + 2
        return obj
      })
    } catch (error) {
      throw new Error('Excel解析失败：' + error.message)
    }
  }

  /**
   * 获取表头行
   * @param {Object} sheet 工作表对象
   * @returns {Array} 表头行
   */
  const getHeaderRow = (sheet) => {
    const headers = []
    const range = XLSX.utils.decode_range(sheet['!ref'])
    const R = range.s.r
    for(let C = range.s.c; C <= range.e.c; ++C) {
      const cell = sheet[XLSX.utils.encode_cell({r: R, c: C})]
      let header = cell ? cell.v : undefined
      headers.push(header)
    }
    return headers
  }

  /**
   * 上传Excel文件
   * @param {Object} upload 上传的ref
   * @param {Array} fileList 上传的文件列表
   * @param {Object} map 映射关系
   * @param {String} ac 接口名称
   * @returns {Promise} 上传结果
   */
  const uploadXlsx = async (upload, fileList, map, ac) => {
    if (!fileList.value || !fileList.value.length) {
      ElementPlus.ElMessage.warning('请选择要上传的文件')
      return
    }

    const file = fileList.value[0]
    if (!validateFile(file.raw)) {
      return
    }

    try {
      ElementPlus.ElMessage.info('文件解析中...')
      const buffer = await readFileAsBuffer(file.raw)
      console.log(buffer)
      const result = parseExcel(buffer, map)
      console.log(result)
      if (!result.length) {
        ElementPlus.ElMessage.warning('文件内容为空')
        return
      }

      const params = {
        ac,
        importDatas: result
      }

      const res = await post(params)
      if (res.data.code === 'success') {
        ElementPlus.ElMessage.success('导入成功')
        upload.value?.clearFiles()
        fileList.value = []
      } else {
        ElementPlus.ElMessage.error(res.data.text || '导入失败')
      }
    } catch (error) {
      ElementPlus.ElMessage.error(error.message || '处理失败')
    }
  }

  /**
   * 将文件读取为ArrayBuffer
   * @param {File} file 文件对象
   * @returns {Promise<ArrayBuffer>} 文件buffer
   */
  const readFileAsBuffer = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = (e) => reject(new Error('文件读取失败'))
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * 格式化日期
   * @param {Date} date 日期对象
   * @returns {String} 格式化后的日期字符串
   */
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  return {
    downloadTemplate,
    uploadXlsx,
  }
}
