import ErrorComponent from '../../components/error.component.js'
import LoadingComponent from '../../components/loading.component.js'
import { post } from '../../config/axios/httpReuest.js'
import { currentNodeKey } from '../../config/keys.js'
import { useDMView } from '../hooks/useDMView.js'

const { ref, reactive, onMounted, inject, defineAsyncComponent, toRefs, toRaw } = Vue
export default {
  name: 'DMViewComponent',
  components: {
    HtDrawer: defineAsyncComponent({
      loader: () => import('../../components/ht.drawer.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    TotalTableComponent: defineAsyncComponent({
      loader: () => import('./total.table.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HtVxeTable: defineAsyncComponent({
      loader: () => import('./ht.vxe.table.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HtDialog: defineAsyncComponent({
      loader: () => import('../../components/ht.dialog.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup() {
    const currentNode = inject(currentNodeKey)
    const activeNames = ref(['1'])

    const searchForm = reactive({
      //id_engine_type: 'cfm56',
    })

    const { chartRef, getMethods, drawerState, total, handleTotalClick } = useDMView()

    onMounted(async () => {
      // await getUser();
      await getMethods(currentNode, searchForm)
    })

    const totalSearchForm = ref({})
    const handleSearchClick = async () => {
      // 保留搜索条件，用于总数点击，不进行双向绑定
      totalSearchForm.value = { ...toRaw(searchForm) }
      await getMethods(currentNode, searchForm)
    }

    const collapseStatus = ref(true)

    const addExchangeState = reactive({
      isShowDialog: false,
      qtyInitialValue: 0,
      form: {
        str_wo: '',
        str_pn: '',
        dbl_num: 0,
        id_wo: 0,
        id_pda: 0,
        id_main: '',
        str_need_reason: '',
        str_need_remark: '',
      },
    })
    const currentRow = ref({})
    // const userS = ref([]);
    // const userST = ref([]);
    /**
     * 串件申请
     * @param row
     */
    const handleAddExchange = (row) => {
      currentRow.value = row
      const rawRow = toRaw(row)
      addExchangeState.form.str_wo = rawRow.str_wo
      addExchangeState.form.str_pn = rawRow.str_pn
      addExchangeState.form.dbl_num = rawRow.int_num
      addExchangeState.qtyInitialValue = rawRow.int_num
      addExchangeState.form.id_wo = rawRow.id_wo
      addExchangeState.form.id_pda = rawRow.id
      addExchangeState.form.id_main = rawRow.id_barcode
      addExchangeState.form.str_need_reason = rawRow.str_need_reason
      addExchangeState.form.str_need_remark = rawRow.str_need_remark
      addExchangeState.isShowDialog = true
    }
    // 保存串件申请1702180433808211969
    const saveAddExchangeState = async () => {
      const params = {
        ac: 'se_pda_main_create_or_update',
        request: {
          id_pda: addExchangeState.form.id_pda,
          str_pda_site: currentNode,
          str_need_remark: addExchangeState.form.str_need_remark,
          str_need_reason: addExchangeState.form.str_need_reason,
          partList: [
            {
              dbl_num: addExchangeState.form.dbl_num,
              str_pn: addExchangeState.form.str_pn,
              str_sn: '',
              id_wo: addExchangeState.form.id_wo,
              str_wo: addExchangeState.form.str_wo,
              id_pkp: addExchangeState.form.id_main,
            },
          ],
        },
      }
      if (!addExchangeState.form.str_need_reason) {
        ElementPlus.ElMessage.error('请选择串件原因')
        return
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        ElementPlus.ElMessage.success('串件申请已提交')
        addExchangeState.isShowDialog = false
        currentRow.value.colortype = 1
        // 刷新图
        await getMethods(currentNode, searchForm)
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    // 串件STATE
    const serialState = reactive({
      isShowDialog: false,
      data: [],
    })
    // 串件
    const handleExchange = async (row) => {
      const params = {
        ac: 'se_pda_main_search',
        request: {
          idPdaList: [row.id],
        },
      }
      const { data } = await post(params)
      serialState.data = data.data[0].parts.map((item) => {
        item.str_name = row.str_part_name
        return item
      })
      serialState.isShowDialog = true
    }

    /**
     *  is show operate
     *
     *  @return {boolean}
     */
    const isShowOperate = () => {
      return currentNode !== '12'
    }
    /**获取系统用户 */
    const getUser = async () => {
      const params = {
        ac: 'cont_get_all_users',
      }
      const { data } = await post(params)
      if (data) {
        userST.value = data.data
        userS.value = data.data
      }
    }
    // const userFilter=(val)=>{

    //   if (val) {
    //     userST.value =userS.value.filter(x => x.str_name?.indexOf(val) > -1 || x.str_code?.indexOf(val) > -1)
    //   } else {
    //     userST.value = userS.value;
    //   }
    // }
    const htVxeTableRef = ref(null)
    // skip select event click
    const skipSelectEventClick = () => {
      htVxeTableRef.value.skipSelectEventClick(currentNode)
    }
    return {
      activeNames,
      searchForm,
      chartRef,
      handleSearchClick,
      ...toRefs(drawerState),
      total,
      collapseStatus,
      handleAddExchange,
      addExchangeState,
      saveAddExchangeState,
      handleExchange,
      serialState,
      handleTotalClick,
      isShowOperate,
      skipSelectEventClick,
      htVxeTableRef,
      totalSearchForm,
      // getUser,
      // userS,
      // userST,
      // userFilter
    }
  },
  template: /*html*/ `
    <div class="flex items-end">
      <div class="flex-3">
        <vxe-form
            v-model:collapseStatus="collapseStatus"
            :data="searchForm"
            custom-layout
            title-align="right"
            title-width="100px"
        >
          <vxe-form-item title="WIP:">
            <template #default="{data}">
              <el-select v-model.trim="data.is_wip" clearable style="width: 210px;">
                <el-option label="All" value="all"></el-option>
                <el-option label="Yes" value="1"></el-option>
                <el-option label="No" value="0"></el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="Engine Type:">
            <template #default="{data}">
              <el-select v-model.trim="data.id_engine_type" clearable style="width: 210px;">
                <el-option label="All" value="all"></el-option>
                <el-option label="CFM56" value="cfm56"></el-option>
                <el-option label="LEAP" value="leap"></el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="ESN:" folding>
            <template #default="{data}">
              <el-input v-model.trim="data.str_esn" clearable style="width: 210px;"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="WO:" folding>
            <template #default="{data}">
              <el-input v-model.trim="data.str_wo" clearable style="width: 210px;"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="SM:" folding>
            <template #default="{data}">
              <el-input v-model.trim="data.str_sm" clearable style="width: 210px;"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="PN:" folding>
            <template #default="{data}">
              <el-input v-model.trim="data.str_pn" clearable style="width: 210px;"></el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="F1-2 Closed:" folding>
            <template #default="{data}">
              <el-select v-model.trim="data.int_f12close" clearable style="width: 210px;">
                <el-option label="All" value="all"></el-option>
                <el-option label="Yes" value="1"></el-option>
                <el-option label="No" value="0"></el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <vxe-form-item title="Status:" folding>
            <template #default="{data}">
              <el-select v-model.trim="data.is_abnormal" clearable style="width: 210px;">
                <el-option label="All" value="all"></el-option>
                <el-option label="正常" value="0"></el-option>
                <el-option label="异常" value="1"></el-option>
              </el-select>
            </template>
          </vxe-form-item>

          <vxe-form-item title="AOG:" folding>
          <template #default="{data}">
            <el-select v-model.trim="data.is_aog" clearable style="width: 210px;">
              <el-option label="YES" value="1"></el-option>
              <el-option label="NO" value="0"></el-option>
            </el-select>
          </template>
        </vxe-form-item>
          <!--<vxe-form-item title="Owner:" folding>
          <template #default="{data}">
            <el-select v-model.trim="data.str_staff" clearable filterable style="width: 210px;" :filter-method='userFilter'>
              <el-option v-for="item in userST" :key="item.id" :label="item.str_name" :value="item.str_name">{{item.str_name}}-{{item.str_code}}</el-option>
             
            </el-select>
          </template>
        </vxe-form-item>-->
          <vxe-form-item title="QUA:" folding>
            <template #default="{data}">
              <el-select v-model.trim="data.int_quadrant" clearable style="width: 210px;">
                <el-option label="第一象限" value="1"></el-option>
                <el-option label="第二象限" value="2"></el-option>
                <el-option label="第三象限" value="3"></el-option>
                <el-option label="第四象限" value="4"></el-option>
              </el-select>
            </template>
          </vxe-form-item>
          <slot></slot>
          <vxe-form-item title="Release Plan Date:" folding>
            <template #default="{data}">
              <el-date-picker
                  v-model.trim="data.dt_date"
                  type="daterange"
                  range-separator="to"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  value-format="YYYY-MM-DD"
                  clearable
              ></el-date-picker>
            </template>
          </vxe-form-item>

          <vxe-form-item align="center" collapse-node>
            <el-button type="primary" circle @click="handleSearchClick">
              <template #icon>
                <el-icon>
                  <Search></Search>
                </el-icon>
              </template>
            </el-button>
            <el-button type="info" circle @click="handleSearchClick">
              <template #icon>
                <el-icon>
                  <Refresh></Refresh>
                </el-icon>
              </template>
            </el-button>
          </vxe-form-item>
        </vxe-form>
      </div>
      <div class="flex-initial w-64 ml-auto">
        总数: <span @click="handleTotalClick(totalSearchForm)"
                    class="hover:cursor-pointer hover:text-blue-500 underline">{{ total }}</span>
      </div>
    </div>
    <div class="border-b-2 border-b-slate-200 mb-2"></div>
    <div ref="chartRef" style="width: 100%; height: calc(100vh - 200px)"></div>
    <!--    点击series的抽屉-->
    <HtDrawer v-model:visible="isShowDrawer" title="零件列表" :is-show-save="false">
      <HtVxeTable ref="htVxeTableRef" :tableData :totalNum :tableColumn :isCheckbox="true">
        <template #jump>
          <el-button title="跳转" type="primary" circle @click="skipSelectEventClick">
            <el-icon>
              <Position></Position>
            </el-icon>
          </el-button>
        </template>
        <vxe-table-column v-if="isShowOperate()" title="操作" fixed="right" min-width="150">
          <template #default="{row}">
            <el-button
                text
                type="primary"
                v-if="row.colortype === 1"
                @click="handleExchange(row)"
            >
              Exchange
            </el-button>
            <el-button
                text
                type="primary"
                v-else-if="row.int_d < 0 || row.int_m < 0"
                @click="handleAddExchange(row)"
            >
              Add exchange
            </el-button>
          </template>
        </vxe-table-column>
      </HtVxeTable>
    </HtDrawer>
    <!--    串件申请弹框-->
    <HtDialog v-model:visible="addExchangeState.isShowDialog" title="串件申请" @save="saveAddExchangeState">
      <el-form :model="addExchangeState.form" label-width="120px">
        <el-form-item label="WO">
          <el-input v-model="addExchangeState.form.str_wo" disabled></el-input>
        </el-form-item>
        <el-form-item label="PN">
          <el-input v-model="addExchangeState.form.str_pn" disabled></el-input>
        </el-form-item>
        <el-form-item label="QTY">
          <el-input-number v-model="addExchangeState.form.dbl_num" :min="1"
                           :max="addExchangeState.qtyInitialValue"></el-input-number>
        </el-form-item>
        <el-form-item label="串件原因">
          <el-select v-model="addExchangeState.form.str_need_reason" clearable>
            <el-option label="受体零件转包" value="1"></el-option>
            <el-option label="受体零件内修" value="2"></el-option>
            <el-option label="受体零件报废" value="3"></el-option>
            <el-option label="受体零件被串走" value="4"></el-option>
            <el-option label="受体零件排故" value="5"></el-option>
            <el-option label="受体零件平衡需求" value="6"></el-option>
            <el-option label="受体零件系统无记录" value="7"></el-option>
            <el-option label="受体零件失踪" value="8"></el-option>
            <el-option label="受体零件间隙调整" value="9"></el-option>
            <el-option label="受体零件进厂缺件" value="10"></el-option>
            <el-option label="受体零件工程要求" value="11"></el-option>
            <el-option label="受体零件工程待定" value="12"></el-option>
            <el-option label="受体零件客户要求" value="13"></el-option>
            <el-option label="受体零件客户提走" value="14"></el-option>
            <el-option label="工作指令变更" value="15"></el-option>
            <el-option label="受体零件证书有误" value="16"></el-option>
            <el-option label="受体零件转包报废" value="17"></el-option>
            <el-option label="其他-详情记录到备注中" value="20"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="addExchangeState.form.str_need_remark" type="textarea" :rows="3"></el-input>
        </el-form-item>
      </el-form>
    </HtDialog>
    <!--    查看串件申请弹框-->
    <HtDialog v-model:visible="serialState.isShowDialog" title="串件信息" :isShowSave="false">
      <el-table :data="serialState.data" border>
        <el-table-column prop="str_wo" label="WO"></el-table-column>
        <el-table-column prop="str_esn" label="ESN"></el-table-column>
        <el-table-column prop="str_pn" label="PN"></el-table-column>
        <el-table-column prop="str_name" label="PN Name"></el-table-column>
        <el-table-column prop="dbl_num" label="QTY"></el-table-column>
      </el-table>
    </HtDialog>
  `,
}
