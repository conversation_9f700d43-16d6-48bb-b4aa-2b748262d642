/**
 * 获取表格选中的数据
 * @param {Object} tableRef 表格实例
 * @returns {Array} 选中的数据
 */
export const getSelectedData = (tableRef) => {
  return tableRef.value.getSelectedData()
}

/**
 * 检查是否选择了数据
 * @param {Array} selectedData 选中的数据
 * @returns {boolean} 是都选择了数据
 */
export const checkSelectedData = (selectedData) => {
  if (selectedData.length === 0) {
    return false
  }
  return true
}

/**
 * 检查是否选择了一条数据
 * @param {Array} selectedData 选中的数据
 * @returns {boolean} 是否选择了一条数据
 */
export const checkSelectedOneData = (selectedData) => {
  if (selectedData.length !== 1) {
    ElementPlus.ElMessage.warning('请选择一条数据')
    return false
  }
  return true
}
