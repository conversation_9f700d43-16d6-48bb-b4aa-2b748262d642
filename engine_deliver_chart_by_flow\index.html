<!DOCTYPE html>
<html lang='en'>

<head>
  <meta charset='UTF-8'>
  <!-- 引入tailwindcss -->
  <link href="../assets/css/tailwind.css" rel="stylesheet">
  <!--    引入element-plus的样式-->
  <link href='../assets/element-plus@2.5.5/index.css' rel='stylesheet'>
  <!--    引入VUE-->
  <script src='../assets/vue@3.4.15/vue.global.prod.js'></script>
  <!--    引入element-plus-->
  <script src='../assets/element-plus@2.5.5/index.js'></script>
  <!--  引入element-plus-icon-->
  <script src="../assets/icons-vue@2.3.1/index.iife.min.js"></script>
  <!--    引入axios-->
  <script src='../assets/axios@1.6.7/axios.min.js'></script>
  <!--    引入moment-->
  <script src='../assets/moment/moment.min.js'></script>
  <link href='../assets/styles/common.css' rel='stylesheet'>
  <!-- <link href='../assets/styles/bootstrap.min.css' rel='stylesheet'> -->
  <link href='../assets/styles/bootstrap-table.css' rel='stylesheet'>
  <link href='../assets/styles/pagination.css' rel='stylesheet'>
  <title>Title</title>
</head>

<body>
  <div id='app'>

    <div class='con left' style='width: 98%;margin-left: 1%;'>
      <div class='div_any_child' style='height: 800px'>
        <div style='height: 96%;margin-top: 20px; margin-left: 5px;margin-right: 5px;'>
          <engine-deliver-chart-by-flow str_eng_type='CFM'></engine-deliver-chart-by-flow>
        </div>
      </div>
    </div>

  </div>
</body>
<script type='module'>
  import EngineDeliverChartByFlow from './index.js';

  const { createApp } = Vue;
  const app = createApp({
    components: {
      EngineDeliverChartByFlow,
    },
  });
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
  }
  app.use(ElementPlus);
  app.mount('#app');
</script>
<style>
  .el-table .cell {
    line-height: normal;

  }
</style>

</html>