.tabListPage {
    margin-top: 10px;
}


/* 背景色 */

.cantainer {
    background-color: #f0f2f5;
}

.pagesearch {
    border: 1px solid rgb(235, 238, 245);
    padding: 5px;
    margin-bottom: 10px;
}

.pageexcute {
    border: 1px solid #EBEEF5;
    padding: 5px;
    margin-bottom: 10px;
}

.excueButtonBox {
    border: 1px solid #EBEEF5;
    padding: 5px;
    margin-bottom: 5px;
    background-color: #fff;
}

.searchBox {
    border: 1px solid #EBEEF5;
    padding: 5px;
    margin-bottom: 5px;
    background-color: #fff;
}

.pading-page-com {
    padding: 0px 0px;
    box-sizing: border-box;
    flex-shrink: 0;
}


/* 审批结果字体颜色 */

.audit_await {
    /* 审核中 */
    color: #E6A23C;
}

.audit_succes {
    /* 审批通过 */
    color: #67C23A;
}

.audit_no_pass {
    /* 审批失败 */
    color: #F56C6C;
}

.audit_no {
    /* 未提交 */
    color: #909399;
}

.audit_cancel {
    /* 撤销 */
    color: #bdbfc3;
}

.layui-btn {
    height: 38px;
    line-height: 38px;
    border: 1px solid transparent;
    padding: 0 18px;
    background-color: #009688;
    color: #fff;
    white-space: nowrap;
    text-align: center;
    font-size: 14px;
    border-radius: 2px;
    cursor: pointer;
}

.layui-table-link {
    color: #01AAED;
}


/* jaki  */

.searchY {
    box-shadow: rgb(168 168 168 / 30%) -4px 2px 12px 0px;
}

.pageY {
    box-shadow: rgb(168 168 168 / 30%) -4px -2px 12px 0px;
}

.el-collapse-item__header.is-active {
    border-bottom: 1px solid #c1c1c1 !important;
}

.el-collapse-item__header {
    border-bottom: none;
}

.el-collapse-item__header {
    height: 40px;
    line-height: 40px;
    font-weight: bold;
}

.el-dialog .el-form-item__content,
.el-dialog .el-form-item__label {
    line-height: 20px;
    font-size: 12px;
}

.el-form-item__content,
.el-form-item__label {
    line-height: 30px;
}

.el-input__inner {
    /* height: 30px; */
    line-height: 30px;
    font-size: 12px;
}

.el-range-editor .el-range-input {
    font-size: 12px;
}

.el-form-item {
    margin-bottom: 13px;
}

.el-input__icon {
    line-height: 30px;
}

.el-date-editor .el-range__icon,
.el-date-editor .el-range-separator {
    line-height: 20px;
}

.el-table th.el-table__cell {
    font-size: 12px;
    color: #000;
}

.el-table tr {
    font-size: 12px;
}


.el-radio-button__inner,
.el-radio-group {
    margin-top: 5px;
}


/* 高级筛选 */

.el-collapse {
    border-top: none;
    border-bottom: none;
}

.el-collapse-item__wrap {
    border-bottom: none;
}

.lineClamp {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

.el-tooltip__popper {
    max-width: 400px;
    width: auto;
}

.el-pagination {
    text-align: right;
    /* padding: 15px 20px; */
}

.el-form-item__error {
    padding-top: 1px;
}

.el-dialog {
    border-radius: 4px;
}

.el-table .cell {
    padding-right: 0;
    padding-left: 2px;
}


/*  */

.search .el-button--mini {
    padding: 7px 0px;
    margin: 4px 0;
}

.el-button+.el-button {
    margin-left: 0px;
}

.el-table .el-table__body-wrapper {
    z-index: 2;
}


/* 修改表单插件样式  */

.search .el-form-item__label {
    font-size: 12px;
    padding-right: 6px;
    /*font-weight: bold;*/
}

.search .el-date-editor--daterange.el-input__inner,
.search .el-input__inner,
.search .el-input {
    max-width: 240px;
    width: 100%;
    width: 200px;
}

.search .search_str_reference .el-date-editor--daterange.el-input__inner,
.search .search_str_reference .el-input__inner,
.search .search_str_reference .el-input {
    max-width: 90px;
    /* width: 100%;
    width: 95px; */
}

.search .el-form--inline .el-form-item {
    margin-right: 0px;
}


/* .search .el-form--inline .el-form-item__content {
    width: calc(100% - 145px);
} */

.search .el-range-editor.el-input__inner {
    padding: 3px;
    width: 100%;
    width: 200px;
}

.search .el-date-editor .el-range__close-icon {
    width: 10px;
}

.search .btn .el-form-item {
    margin-bottom: 0px;
}

.searchBox .el-collapse-item__content {
    padding-bottom: 0px;
}



.el-form-item__label-wrap {
    margin-left: 0px!important;
}


.other .el-form-item__content {
    display: flex!important;
}



.el-textarea__inner {
    padding: 0 15px;
    font-size: 12px;
    height: 30px;
    line-height: 30px;
}

.el-table__empty-text {
    font-size: 12px;
}
.offer_final .el-table .cell, .el-table--border .el-table__cell:first-child .cell{
    padding-left: 10px;
    padding-right: 10px;
}
