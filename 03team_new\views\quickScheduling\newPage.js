const NewPage = {
  template: /*html*/ `
    <!-- 头部 -->
    <div class="border">
      <el-form :model="formData" inline>
        <el-form-item label="任务名称">
          <el-select v-model="formData.taskName" clearable placeholder="请选择任务名称">
            <el-option label="Option A" value="F3-1"></el-option>
            <el-option label="Option B" value="F1"></el-option>
            <el-option label="Option C" value="F2-1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执行时间">
          <el-date-picker v-model="formData.execTime" type="datetime" placeholder="选择日期时间"></el-date-picker>  
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSave">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="taskName" label="任务名称"></el-table-column>
      <el-table-column prop="execTime" label="执行时间"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="text" @click="updateRowStatus(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
  `,
  setup(props, { emit }) {},
}
export default NewPage
