// const { reactive, onMounted, inject, watch, computed } = Vue
// import { useApi } from '../../../comm/hooks/useApi.js'

const TopRightComponent = {
  props: ['station'],
  emits: [],
  components: {},
  setup(props, context) {
    const flow = ''
   // const woId = inject('wo-id')
    // watch(
    //   () => woId.value,
    //   (val) => {
    //     // getEngineInfo(val)
    //   },
    // )
    const engineInfo = reactive({
      date: '',
      realTime: '',
      expectedTime: '',
      esn: '',
      engineType: '',
      customer: '',
      planDeliveryFlowDate: '',
      changedDeliveryDate: '',
      flow: '',
    })
    // const { requestDataApi } = useApi()
    // 获取当前小组正在工作的发动机信息
    // const getEngineInfo =  (idTeam) => {
    //   axios.post(globalApiUrl, {
    //     au: 'ssamc',
    //     ap: 'api2018',
    //     ak: '',
    //     ac: 'pt_team_work_engine',
    //     id_wo: woId.value,
    //   }).then((res) => {
    //     const code = res.data.code
    //     if (code === 'success') {
    //       const data = res.data.data
    //       console.log(data)
    //     } else {
    //       console.log(res.data.text)
    //     }
    //   })
    //   // const data = await requestDataApi({
    //   //   ac: 'pt_team_work_engine',
    //   //   id_wo: woId.value,
    //   // })
    //   // 1. 获取的数据是一个数组，只取第一个
    //   const [first] =
    //     data.length > 0
    //       ? data
    //       : [
    //           {
    //             woId: '',
    //             date: '',
    //             esninfo: {
    //               f11sjtat: '',
    //               f11plantat: '',
    //               f4sjtat: '',
    //               f4plantat: '',
    //               str_code: '',
    //               engint_type: '',
    //               customer: '',
    //               dt_f11_close: '',
    //               dt_f11_update: '',
    //               dt_f11_finish: '',
    //               dt_release_close: '',
    //               dt_release_update: '',
    //               dt_release_finish: '',
    //             },
    //           },
    //         ]

    //   engineInfo.date = first.date
    //   if (first.esninfo.length) {
    //     first.esninfo = first.esninfo[0]
    //   }
    //   if (flow.indexOf('F4') > -1) {
    //     engineInfo.expectedTime = first.esninfo.f4plantat
    //     engineInfo.realTime = first.esninfo.f4sjtat
    //     engineInfo.planDeliveryFlowDate = first.esninfo.dt_release_close
    //     engineInfo.changedDeliveryDate = first.esninfo.dt_release_update
    //   } else {
    //     engineInfo.realTime = first.esninfo.f11sjtat
    //     engineInfo.expectedTime = first.esninfo.f11plantat
    //     engineInfo.planDeliveryFlowDate = first.esninfo.dt_f11_close
    //     engineInfo.changedDeliveryDate = first.esninfo.dt_f11_update
    //   }
    //   engineInfo.flow = first.esninfo.str_flow
    //   engineInfo.esn = first.esninfo.str_code
    //   engineInfo.engineType = first.esninfo.engint_type
    //   engineInfo.customer = first.esninfo.customer
    //   engineInfo.customer_png = ''
    //   if (first.esninfo.customer_png) {
    //     engineInfo.customer_png = first.esninfo.customer_png
    //   }
    // }
    return {
      flow,
      engineInfo,
    }
  },
  // language=HTML https://picsum.photos/400/100
  template: /*html*/ `
      <div class="flex">
          <div class="flex-[2]">
              <img v-if="engineInfo.customer_png" :src="engineInfo.customer_png"  alt="" style="height:120px;width:300px">
              <img v-else src="/Content/assets_huatek/webapp/03team_new/views/report/default.png"  alt="" style="height:120px;width:300px">
              
          </div>
          <div class="flex-1 flex flex-col text-center">
              <span class="text-2xl p-3">Real-Time TAT</span>
              <span class="text-2xl">{{ engineInfo.realTime}}</span>
          </div>
          <div class="flex flex-col flex-1 text-center">
              <div class="text-2xl p-3">Expected TAT</div>
              <div class="text-2xl">
                  {{ engineInfo.expectedTime}}
              </div>
          </div>
      </div>
      <div class="grid grid-flow-row-dense grid-cols-2 grid-rows-5 mt-16">
          <div class="ml-auto text-xl" >WorkOrder/ESN:</div>
          <div class="text-xl" style="margin-left:30px">
              {{ engineInfo.esn}}
          </div>
          <div class="ml-auto text-xl" >Engine Type:</div>
          <div class="text-xl" style="margin-left:30px">
              {{ engineInfo.engineType}}
          </div>
          <div class="ml-auto text-xl" >Customer:</div>
          <div class="text-xl" style="margin-left:30px">
              {{ engineInfo.customer}}
          </div>
          <div class="ml-auto text-xl" >Plan Delivery Flow Date:</div>
          <div class="text-xl" style="margin-left:30px">
             {{ engineInfo.planDeliveryFlowDate}} 
          </div>
          <div class="ml-auto text-xl" >Changed Delivery Date:</div>
          <div class="text-xl" style="margin-left:30px">
              {{ engineInfo.changedDeliveryDate}}
          </div>
      </div>
      <div class="flex justify-around mt-8" style="margin-top:10px">
          <div class="my-clip" :class="engineInfo.flow === 'F0' ? 'active' : ''">F0</div>
          <div class="my-clip" :class="engineInfo.flow === 'F1-1' ? 'active' : ''">F1-1/B1</div>
          <div class="my-clip" :class="engineInfo.flow === 'F1-12' ? 'active' : ''">F1-1/B2B3</div>
          <div class="my-clip" :class="engineInfo.flow === 'F1-2' ? 'active' : ''">F1-2</div>
          <div class="my-clip" :class="engineInfo.flow === 'F2' ? 'active' : ''">F2</div>
          <div class="my-clip" :class="engineInfo.flow === 'F3' ? 'active' : ''">F3</div>          
          <div class="my-clip" :class="engineInfo.flow === 'F4-1' ? 'active' : ''">F4-1/B2B3</div>
          <div class="my-clip" :class="engineInfo.flow === 'F4-12' ? 'active' : ''">F4-1/B1</div>
          <div class="my-clip" :class="engineInfo.flow === 'F4-2' ? 'active' : ''">F4-2</div>
          <div class="my-clip" :class="engineInfo.flow === 'F4-3' ? 'active' : ''">F4-3</div>
      </div>
      <div class="flex justify-around mt-8" style="margin-top:15px">
          <div class="flex items-center">
              <span class="text-2xl">WorkStation: </span>
              <span class="text-xl" style="margin-left:30px">{{station}}</span>
          </div>
          <div class="flex items-center">
              <span class="text-2xl">DATE: </span>
              <span class="text-xl" style="margin-left:30px">
                  {{ engineInfo.date}}
              </span>
          </div>
      </div>
  `,
}
//export {
//   TopRightComponent
// }
