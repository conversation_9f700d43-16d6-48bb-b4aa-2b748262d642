const CalendarDemo = {
  setup() {
    const currentYear = Vue.ref(new Date().getFullYear());

    // 上一年
    const prevYear = () => {
      currentYear.value--;
    };
    // 下一年
    const nextYear = () => {
      currentYear.value++;
    };

    // 根据月份返回不同的背景色
    const getBgColotByMonth = (month) => {
      if (month % 2 === 0) {
        return 'bg-blue-100';
      }
      return 'bg-green-100';
    };
    // 根据月份的Map
    const monthMap = {
      1: '1.1 - 1.25',
      2: '2.1 - 2.25',
      3: '3.1 - 3.25',
      4: '4.1 - 4.25',
      5: '5.1 - 5.25',
      6: '5.26 - 6.30',
      7: '7.1 - 7.25',
      8: '8.1 - 8.25',
      9: '9.1 - 9.25',
      10: '10.1 - 10.25',
      11: '11.1 - 11.25',
      12: '11.26 - 12.31',
    };
    // 根据月份返回不同的日期范围
    const getRangeByMonth = (month) => {
      /*
       * 1月份为 1.1 - 1.25
       * 2月份为 2.1 - 2.25
       * 3月份为 3.1 - 3.25
       * 4月份为 4.1 - 4.25
       * 5月份为 5.1 - 5.25
       * 6月份为 5.26 - 6.30
       * 7月份为 7.1 - 7.25
       * 8月份为 8.1 - 8.25
       * 9月份为 9.1 - 9.25
       * 10月份为 10.1 - 10.25
       * 11月份为 11.1 - 11.25
       * 12月份为 11.26 - 12.31
       */
      return monthMap[month];
    };
    return {
      currentYear,
      prevYear,
      nextYear,
      getBgColotByMonth,
      getRangeByMonth,
    };
  },
  template: /*html*/ `
    <div class="flex justify-between m-4 border-b-2 pb-2">
      <div class="text-2xl font-bold">{{ currentYear }}年</div>
      <!-- 操作按钮  -->
      <div>
        <button class="mr-2 bg-blue-500 text-white px-2 py-1 rounded" @click="prevYear">上一年</button>
        <button class="bg-blue-500 text-white px-2 py-1 rounded" @click="nextYear">下一年</button>
      </div>
    </div>
    <!-- 展示每年的12个月 -->
    <div class="grid lg:grid-cols-4 md:grid-cols-3 gap-4 mx-4">
      <!-- 每个月的背景色不同 -->
      <div
        v-for="month in 12"
        :key="month"
        class="flex flex-col p-4 border-2 border-gray-200 rounded-lg min-h-56"
        :class="getBgColotByMonth(month)"
      >
        <div class="flex justify-between text-2xl font-bold mb-4">
          <div>{{ month }}月</div>
          <div>{{ getRangeByMonth(month) }}</div>
        </div>
        <div class="flex-auto flex items-center justify-center">
          <button class="bg-blue-500 text-white px-2 py-1 rounded">绩效展示</button>
        </div>
      </div>
    </div>
  `,
};

export default CalendarDemo;
