<mxfile host="65bd71144e">
    <diagram id="Beoy39dAVzdz9GLk3yon" name="第 1 页">
        <mxGraphModel dx="990" dy="787" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" background="#ffffff" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="3" style="edgeStyle=none;html=1;fontSize=24;" parent="1" source="2" target="4" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="390" y="110" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;开始&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;fillColor=#1ba1e2;fontColor=#ffffff;strokeColor=#006EAF;" parent="1" vertex="1">
                    <mxGeometry x="330" y="30" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="8" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=24;" parent="1" source="4" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="390" y="230" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="提交审批" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=24;fontColor=#FF0000;" parent="8" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" relative="1" as="geometry">
                        <mxPoint y="16" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="工程人员录入偏差申请&lt;br&gt;（签字下发）" style="rounded=1;whiteSpace=wrap;html=1;fontSize=24;" parent="1" vertex="1">
                    <mxGeometry x="240" y="120" width="300" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="" style="edgeStyle=none;html=1;fontSize=24;" parent="1" source="9" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="工程部经理审批" style="rounded=1;whiteSpace=wrap;html=1;fontSize=24;" parent="1" vertex="1">
                    <mxGeometry x="280" y="250" width="220" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="78" style="edgeStyle=none;html=1;fontSize=14;" edge="1" parent="1" source="10">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="760" y="370" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="&lt;font color=&quot;#ff0000&quot;&gt;是否通过&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;fontSize=24;rounded=1;strokeColor=#FF0000;" parent="1" vertex="1">
                    <mxGeometry x="330" y="340" width="125" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="&lt;font color=&quot;#ffffff&quot; style=&quot;font-size: 24px;&quot;&gt;结束&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;strokeColor=#FF0000;fillColor=#FF0000;" parent="1" vertex="1">
                    <mxGeometry x="335" y="890" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="62" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="41" target="4">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="760" y="815"/>
                            <mxPoint x="760" y="180"/>
                            <mxPoint x="760" y="150"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="&lt;font color=&quot;#ff0000&quot;&gt;是否通过&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;fontSize=24;rounded=1;strokeColor=#FF0000;" parent="1" vertex="1">
                    <mxGeometry x="325" y="790" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="57" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;startArrow=none;" edge="1" parent="1" source="68" target="41">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="49" value="质量部经理审批" style="rounded=1;whiteSpace=wrap;html=1;fontSize=24;" vertex="1" parent="1">
                    <mxGeometry x="285" y="670" width="220" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="58" value="" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=24;fontColor=#FF0000;endArrow=none;startArrow=none;" edge="1" parent="1" source="71" target="24">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="390.1530612244899" y="610" as="targetPoint"/>
                        <mxPoint x="392.5" y="380" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="75" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="24" target="60">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="客户支援部经理CSM审批" style="rounded=1;whiteSpace=wrap;html=1;fontSize=24;" parent="1" vertex="1">
                    <mxGeometry x="283" y="450" width="220" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="77" style="edgeStyle=none;html=1;fontSize=14;" edge="1" parent="1" source="60">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="760" y="580" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="730" y="580"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="86" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;fontColor=#FF0000;startArrow=none;" edge="1" parent="1" source="88" target="49">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="60" value="&lt;font color=&quot;#ff0000&quot;&gt;是否通过&lt;/font&gt;" style="rhombus;whiteSpace=wrap;html=1;fontSize=24;rounded=1;strokeColor=#FF0000;" vertex="1" parent="1">
                    <mxGeometry x="330" y="550" width="125" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="65" value="" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;endArrow=none;" edge="1" parent="1" source="41" target="64">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="392.5" y="750" as="sourcePoint"/>
                        <mxPoint x="394.4666877352647" y="860.0015801448608" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="66" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="64" target="39">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="64" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#00F700;" vertex="1" parent="1">
                    <mxGeometry x="365" y="850" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="67" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=14;fontColor=#FF0000;" vertex="1" parent="1">
                    <mxGeometry x="590" y="770" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="69" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=none;" edge="1" parent="1" source="49" target="68">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="392.5" y="670" as="sourcePoint"/>
                        <mxPoint x="392.5" y="730" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="68" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#00F700;" vertex="1" parent="1">
                    <mxGeometry x="365" y="750" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="72" value="" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=24;fontColor=#FF0000;endArrow=none;" edge="1" parent="1" source="10" target="71">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="392.5" y="430" as="targetPoint"/>
                        <mxPoint x="392.5" y="380" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="71" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=14;fontColor=#00F700;" vertex="1" parent="1">
                    <mxGeometry x="363" y="410" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="79" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontColor=#FF0000;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="590" y="340" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="80" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontColor=#FF0000;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="590" y="520" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="89" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;fontColor=#FF0000;endArrow=none;" edge="1" parent="1" source="60" target="88">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="393.3223684210527" y="609.6052631578948" as="sourcePoint"/>
                        <mxPoint x="395" y="670" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="88" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontColor=#00F700;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="362.5" y="630" width="60" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>