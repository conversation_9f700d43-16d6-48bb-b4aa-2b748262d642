import { useCertificateInfo } from '../hooks/useDeduction.js'
/**
 * @description 推演串件结果抽屉组件
 */
// 导入公共的HtVxeTable组件
import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
import { useDeduction } from '../hooks/useDeduction.js'
const { useVModel } = VueUse
const { onMounted } = Vue
const DeductionDrawer = {
  components: {
    HtVxeTable,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
    filterFields: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)

    const { tableRef, tableState, getDeductionData, handleSend, exportTableData, handleFilterChange, changeCheckbox } =
      useDeduction()

    const {
      openCertificateInfo,
      closeCertificateInfo,
      certificateInfoVisible,
      certificateLoading,
      certificateTableData,
      getCertificateTableData,
    } = useCertificateInfo(tableRef)

    onMounted(() => {
      getDeductionData(props.id, props.filterFields)
    })

    return {
      visible,
      tableRef,
      tableState,
      handleSend,
      exportTableData,
      handleFilterChange,
      changeCheckbox,
      openCertificateInfo,
      certificateInfoVisible,
      closeCertificateInfo,
      certificateLoading,
      certificateTableData,
      getCertificateTableData,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="80%" :show-close="false" destroy-on-close :modal="false" :z-index="1">
      <template #title>
        <div class="flex items-center justify-between">
          <div class="text-white">推演串件结果</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <!-- 推演串件结果表格 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <el-button type="primary" @click="handleSend(id)">发送串件</el-button>
          <el-button type="primary" @click="exportTableData">导出</el-button>
          <el-button type="primary" @click="openCertificateInfo">证书信息</el-button>
        </div>
        <div class="text-black">共计：{{ tableState.total || 0 }} 条</div>
      </div>
      <div style="height: calc(100% - 60px)">
        <HtVxeTable
          ref="tableRef"
          :tableData="tableState.data"
          :tableColumns="tableState.columns"
          :isShowHeaderCheckbox="true"
          @filterChange="handleFilterChange"
          @checkbox-change="changeCheckbox"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
      <template #footer>
        <el-button type="primary" @click="visible = false">确定</el-button>
        <el-button @click="visible = false">取消</el-button>
      </template>
    </el-drawer>
    <el-dialog class="my-dialog" v-model="certificateInfoVisible" title="证书信息" width="60%" :show-close="false">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="text-white">证书信息</div>
          <el-button size="small" type="danger" @click="closeCertificateInfo">关闭</el-button>
        </div>
      </template>
      <div class="m-4">
        <el-table :data="certificateTableData" border height="500" v-loading="certificateLoading">
          <el-table-column label="属性信息" prop="property">
            <template #header>
              <div class="text-black font-bold">属性信息</div>
            </template>
            <template #default="scope">
              <div class="text-black">{{ scope.row.property }}</div>
            </template>
          </el-table-column>
          <el-table-column label="目标发动机" prop="value1">
            <template #header>
              <div class="text-black font-bold">目标发动机</div>
            </template>
          </el-table-column>
          <el-table-column label="原发动机" prop="value2">
            <template #header>
              <div class="text-black font-bold">原发动机</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  `,
}

export default DeductionDrawer
