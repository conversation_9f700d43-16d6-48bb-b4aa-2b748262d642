const { useVModel } = VueUse
const { ref, onMounted } = Vue
import { initEdit, queryTeamSecList, queryF12TeamMember } from '../api/index.js'
import { getShiftList } from '../../../api/calendar/index.js'

export default {
  name: 'TeamPlanSee',
  props: {
    planItem: Object,
    visible: Boolean,
    currentRow: {
      type: Object,
    },
    currentColumn: {
      type: Object,
    },
  },
  setup(props) {
    const teamPlanSeeVisible = useVModel(props, 'visible')
    const teamPlanSeeList = ref([])
    const num = ref(0)

    const feedbackItem = ref(null)
    const getTeamPlanSeeList = async () => {
      const ids = props.planItem.task.map((item) => item.id)
      const res = await initEdit(ids)
      teamPlanSeeList.value = res ?? []
      num.value = teamPlanSeeList.value.length
    }

    const convertArrayToTeamSecName = (array) => {
      const strs = teamSecOptions.value
        .filter((item) => array?.map((_item) => _item.id_staff)?.includes(item.id))
        .map((item) => `${item.str_name}`)
      return strs?.join(',')
    }
    const convertArrayToStaffName = (array) => {
      const strs = teamStaffs.value
        .filter((item) => array?.map((_item) => _item.id_staff)?.includes(item.id))
        .map((item) => `${item.str_name}(${item.dbl_hour}H)`)
      return strs?.join(',')
    }
    // 关闭弹窗
    const handleClose = () => {
      teamPlanSeeVisible.value = false
    }

    const teamSecOptions = ref([])
    const getTeamSecOptions = async () => {
      const params = {
        id_team: props.currentRow.id_team,
        pt_dt: props.currentColumn.day,
      }
      const res = await queryTeamSecList(params)
      teamSecOptions.value = res ?? []
    }
    const teamStaffs = ref([])
    // 获取团队人员
    const getTeamStaff = async () => {
      const params = {
        id_team: props.currentRow.id_team,
        dt_shift: props.currentColumn.day,
      }
      const res = await queryF12TeamMember(params)
      teamStaffs.value = res ?? []
    }

    // 加载班次选项
    const shiftOptionList = ref([])
    const loadShiftOptions = async () => {
      try {
        const shiftRes = await getShiftList()
        shiftOptionList.value = shiftRes.data
        return shiftRes.data
      } catch (error) {
        console.error('加载班次选项失败:', error)
        return []
      }
    }

    // 获取班次名称
    const getShiftName = (shiftId) => {
      const shift = shiftOptionList.value.find(s => s.id === shiftId)
      return shift ? shift.str_name : ''
    }

    onMounted(() => {
      getTeamPlanSeeList()
      getTeamStaff()
      getTeamSecOptions()
      loadShiftOptions()
    })
    return {
      teamPlanSeeVisible,
      teamPlanSeeList,
      convertArrayToTeamSecName,
      teamSecOptions,
      handleClose,
      convertArrayToStaffName,
      feedbackItem,
      getShiftName,
    }
  },
  template: /*html*/ `
    <el-dialog
      v-model="teamPlanSeeVisible"
      title="Team Plan See"
      width="80%"
      class="common-dialog"
      :fullscreen="false"
      :append-to-body="true"
    >
      <div class="pr-2">
        <!-- 计划项列表 -->
        <div v-for="(item, index) in teamPlanSeeList" :key="index" class="my-4 rounded-md border border-gray-200 p-4">
          <!-- 表单字段 -->
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <!-- Type -->
            <div class="flex flex-col">
              <label class="mb-1 text-sm font-medium text-gray-700">
                Type
                <span class="text-red-500">*</span>
              </label>
              <div class="rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900">
                {{ item.str_type }}
              </div>
            </div>

            <!-- 特殊工作 -->
            <div class="flex flex-col">
              <label class="mb-1 text-sm font-medium text-gray-700">特殊工作</label>
              <div class="rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900">
                {{ item.str_special_work }}
              </div>
            </div>
          </div>

          <!-- Remark -->
          <div class="mt-4">
            <label class="mb-1 block text-sm font-medium text-gray-700">Remark</label>
            <div class="min-h-[76px] rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900">
              {{ item.str_notes }}
            </div>
          </div>

          <!-- 班次表格 -->
          <div class="mt-4">
            <el-card class="w-full">
              <template #header>
                <div class="card-header">
                  <span class="text-sm font-medium text-gray-700">班次安排</span>
                </div>
              </template>

              <el-table :data="item.shifts || []" :max-height="220">
                <el-table-column prop="id_shift" label="Shift 班次">
                  <template #default="scope">
                    <div class="rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900">
                      {{ getShiftName(scope.row.id_shift) }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="staffs" label="Team 组员">
                  <template #default="scope">
                    <div class="min-h-[32px] rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900">
                      {{ convertArrayToStaffName(scope.row.staffs) }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="teamSecs" label="Team Sec 借调人员">
                  <template #default="scope">
                    <div class="min-h-[32px] rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900">
                      {{ convertArrayToTeamSecName(scope.row.teamSecs) }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </div>

        <!-- Feedback 区域 -->
        <div class="mt-6">
          <div class="rounded-md border border-gray-200 bg-gray-50 p-4">
            <label class="mb-2 block text-sm font-medium text-gray-700">Feedback</label>
            <div class="min-h-[76px] rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900">
              {{ feedbackItem }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button type="primary" @click="handleClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  `,
}
