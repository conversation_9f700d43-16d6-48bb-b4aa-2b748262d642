<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!-- css部分 -->
  <link href="../../../../assets/css/index.css" rel="stylesheet" />
  <link rel="stylesheet" href="../../styles/tailwind.css" rel="stylesheet" />
  <link rel="stylesheet" href="../../assets/element-plus@2.9.4/dist/index.css" rel="stylesheet" />
  <link rel="stylesheet" href="../../styles/shift.calendar.css" rel="stylesheet" />
  <link rel="stylesheet" href="../../styles/common.dialog.css" />
  <!-- CDN js部分 -->
  <script src="../../assets/vue@3.5.13/vue.global.js"></script>
  <script src="../../assets/element-plus@2.9.4/dist/index.full.js"></script>
  <script src="../../assets/sortablejs@latest/Sortable.min.js"></script>
  <script src="../../assets/element-plus@2.9.4/icons-vue/index.full.js"></script>
  <script src="../../assets/moment/moment.min.js"></script>
  <script src="../../assets/lodash@4.17.21/lodash.min.js"></script>
  <script src="../../assets/@vueuse/shared@12.7.0/index.iife.min.js"></script>
  <script src="../../assets/@vueuse/core@12.7.0/index.iife.min.js"></script>
  <!-- api部分 -->
  <script src="../../assets/axios@1.6.7/axios.min.js"></script>

  <!--    引入dhtml-gantt-->
  <link href='../../../../assets/dhtmlx-gantt/index.css' rel='stylesheet' />
  <script src='../../../../assets/dhtmlx-gantt/index.js'></script>
  <title>班次日历 调度页面</title>
</head>

<body>
  <div id="app">
    <Calendar flow="F4-1" type='B1' enginetype='LEAP' />
  </div>

</body>
<script type="module">
  import Calendar from './shift.calendar.js'
  const { createApp } = Vue
  const app = createApp({
    components: {
      Calendar,
    },
  })
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  app.use(ElementPlus)
  app.mount('#app')
</script>

</html>