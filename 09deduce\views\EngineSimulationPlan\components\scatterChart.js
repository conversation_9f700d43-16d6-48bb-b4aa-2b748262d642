import { post } from '../../../../config/axios/httpReuest.js'
import { useScatterChart } from '../composables/useScatterChart.js'
const { onMounted, onBeforeUnmount, ref, reactive, watch } = Vue
// 引入点位颜色抽屉组件
import PointColotDrawer from './pointColorDrawer.js'
const ScatterChart = {
  components: {
    PointColotDrawer,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
    // GM锁定
    isLock: {
      type: String,
      required: false,
    },
    // GP锁定
    isLockGp: {
      type: String,
      required: false,
    },
    coordRange: {
      type: Array,
      required: false,
    },
    markAreaRange: {
      type: Array,
      required: false,
    },
    markLineDate: {
      type: String,
      required: false,
    },
    stantardMarkLineDate: {
      type: String,
      required: false,
    },
    // 模拟开始时间
    dtGpSimulationStart: {
      type: String,
      required: false,
    },
  },
  emits: ['submit', 'simulate', 'dotClick'],
  setup(props, { emit }) {
    // 定义定时器数组
    const timerArr = ref([])

    const { toggleFlashing } = useScatterChart()

    const parentFilterFields = ref([])
    const parentIsBeforeSimulation = ref(true)
    const parentIsSimulation = ref(false)
    // 定义图表数据
    const chartData = ref([])
    // 获取图表数据
    const getChartList = async (
      isBeforeSimulation = true,
      filterFields = [],
      isSimulation = false,
      exception = undefined,
    ) => {
      removeBrush()
      const id = `scatter-${props.id}`
      const myChart = echarts.init(document.getElementById(id))
      // 添加loading动画
      myChart.showLoading()
      parentIsBeforeSimulation.value = isBeforeSimulation
      parentFilterFields.value = filterFields
      parentIsSimulation.value = isSimulation
      drawerState.simulationType = isBeforeSimulation ? '0' : '1'
      const params = {
        ac: isBeforeSimulation ? 'de_getpnlist_before' : 'de_getpnlist_after', // 0:推演前 1:推演后
        id_wo: props.id,
        int_type: isBeforeSimulation ? '' : isSimulation ? '1' : '0', // 0:推演 1:模拟
        filter_fields: filterFields,
        int_exception: exception,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        chartData.value = data.data.map((item) => {
          return {
            z: null,
            ...item,
          }
        })
        initChart(myChart)
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }
    const initChart = (myChart) => {
      myChart.clear()
      // 散点图
      let option = {
        backgroundColor: '#f3f4f6',
        tooltip: {
          // triggerOn: 'none',
          trigger: 'item',
          axisPointer: {
            type: 'cross',
          },
          formatter: function (params) {
            // ! 通过formatter方法来自定义提示框内容
            return `日期: ${params.value.dt_ekd}<br>名称: ${params.value.str_part_name}`
          },
        },
        grid: {
          top: 40,
          left: '3%',
          right: '3%',
          // containLabel: true,
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            startValue: getRangeMin(props.coordRange[0], props.markAreaRange[0]),
            // 让datazoom变细
            height: 20,
          },
          {
            type: 'slider',
            show: true,
            yAxisIndex: [0],
            filterMode: 'empty',
            width: 20,
            // datazoom放在左边
            left: '20',
          },
        ],
        dataset: {
          source: chartData.value,
        },
        brush: {
          toolbox: [''],
          xAxisIndex: 0, // 指定哪个x轴可以进行刷选
          yAxisIndex: 0, // 指定哪个y轴可以进行刷选
          brushLink: 'all', // 同步所有图表的选中区域
          outOfBrush: {
            colorAlpha: 1,
          },
          transformable: true,
          throttleType: 'debounce',
          throttleDelay: 500,
          brushStyle: {
            // 红色
            color: 'rgba(254,0,0,0.1)',
            borderColor: 'red',
          },
          z: 50,
        },
        // x轴为日期,分割以周为单位
        xAxis: [
          {
            type: 'time',
            minInterval: 3600 * 24 * 1000,
            axisLabel: {
              // 旋转45度
              // rotate: -45,
              formatter: function (value) {
                // ? 如果当前日期是周一，则显示该周的标签
                if (moment(value).day() === 1) {
                  return moment(value).week() + ''
                }
                return ''
              },
            },
            // 刻度线
            axisTick: {
              interval: 0,
              alignWithLabel: true,
            },
            // TODO 坐标指示器
            // axisPointer: {
            //   type: 'shadow',
            //   value: moment().format('YYYY-MM-DD'),
            //   snap: true,
            //   label: {
            //     show: true,
            //   },
            //   triggerTooltip: true,
            //   handle: {
            //     show: true,
            //     size: 20,
            //     margin: 15,
            //   },
            //   triggerOn: 'none',
            // },
          },
          {
            type: 'time',
            minInterval: 3600 * 24 * 1000,
            axisLabel: {
              // 旋转45度
              // rotate: 90,
              formatter: function (value) {
                // ? 如果当前日期是本月的第一天，则显示该月的标签
                if (moment(value).date() === 1) {
                  // 显示xxxx年xx月
                  return moment(value).month() + 1 + '月'
                }
                return ''
              },
            },
            // 刻度线
            axisTick: {
              interval: 0,
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            show: false,
            type: 'value',
            scale: true,
            axisLabel: {
              formatter: '{value}',
            },
            axisTick: {
              alignWithLabel: true,
              interval: 0,
            },
            splitLine: {
              show: true,
            },
          },
        ],
        series: [
          {
            name: '可串件',
            type: 'scatter',
            large: true,
            symbolSize: 10,
            progressive: 10000,
            xAxisIndex: 0,
            // 高亮下的样式
            emphasis: {
              disabled: false,
              scale: 1.5,
            },
            // 系列颜色为绿色
            itemStyle: {
              color: function (params) {
                /*
                 * int_point_type为1时为红色
                 * int_point_type为2时为蓝色
                 * int_point_type为3时为绿色
                 * int_point_type为4时为紫色
                 */
                if (params.data.int_point_type === '1') {
                  return '#ef4444'
                } else if (params.data.int_point_type === '2') {
                  return '#3b82f6'
                } else if (params.data.int_point_type === '3') {
                  return '#22c55e'
                } else if (params.data.int_point_type === '4') {
                  return '#a855f7'
                } else {
                  return '#22c55e'
                }
              },
            },
            encode: {
              x: 'dt_ekd',
              y: 'y',
            },
            markArea: {
              silent: true,
              label: {
                show: props.markAreaRange[0] !== null && props.markAreaRange[1] !== null,
                position: 'insideTopLeft',
                fontSize: 12,
                color: 'black',
              },
              itemStyle: {
                // 绿色
                color: 'rgba(0, 128, 0, 0.1)',
                borderColor: 'green',
                borderWidth: 1,
              },
              data: [
                [
                  {
                    name: `建议时间: ${props.markAreaRange[0]}`,
                    xAxis: props.markAreaRange[0] ?? '',
                    // ! 获取当前范围内的最小值
                    // yAxis: getRangeMin(props.markAreaRange[0], props.markAreaRange[1], 'y'),
                  },
                  {
                    xAxis: props.markAreaRange[1] ?? '',
                    // ! 获取当前范围内的最大值
                    // yAxis: getRangeMax(props.markAreaRange[0], props.markAreaRange[1], 'y'),
                  },
                ],
              ],
            },
            markLine: {
              silent: true,
              symbol: ['none', 'arrow'],
              label: {
                show: true,
                distance: 20,
                formatter: `{a|G2 绩效关闭日期: ${props.markLineDate}}`,
                rich: {
                  a: {
                    color: 'green',
                    lineHeight: 10,
                    fontSize: 12,
                  },
                },
              },
              lineStyle: {
                type: 'solid',
                color: 'green',
              },
              data: [
                {
                  name: '绩效关闭日期',
                  xAxis: props.markLineDate ?? '',
                },
              ],
            },
            z: 100,
          },
          {
            type: 'scatter',
            xAxisIndex: 1,
            encode: {
              x: 'dt_ekd',
              y: 'z',
            },
            markLine: {
              silent: true,
              symbol: ['none', 'none'],
              label: {
                show: true,
                fontSize: 12,
                color: 'black',
                formatter: `绩效标准: ${props.stantardMarkLineDate}`,
              },
              lineStyle: {
                // 虚线
                type: 'dashed',
                color: 'black',
              },
              data: [
                {
                  name: '绩效标准',
                  xAxis: props.stantardMarkLineDate ?? '',
                },
              ],
            },
          },
          {
            type: 'scatter',
            xAxisIndex: 1,
            encode: {
              x: 'dt_ekd',
              y: 'z',
            },
            markLine: {
              silent: true,
              symbol: ['arrow', 'none'],
              label: {
                show: true,
                fontSize: 12,
                color: 'red',
                formatter: `模拟时间: ${props.dtGpSimulationStart}`,
                position: 'start',
              },
              lineStyle: {
                // 虚线
                type: 'dashed',
                color: 'red',
                with: 12,
              },
              data: [
                {
                  name: '模拟时间',
                  xAxis: props.dtGpSimulationStart ?? '',
                },
              ],
            },
          },
          {
            type: 'scatter',
            xAxisIndex: 1,
            encode: {
              x: 'dt_ekd',
              y: 'z',
            },
            markLine: {
              silent: true,
              // 两边为箭头
              symbol: ['none', 'arrow'],
              lineStyle: {
                // 实线
                type: 'solid',
                color: 'black',
                with: 12,
              },
              data: [
                {
                  name: '今天',
                  xAxis: moment().format('YYYY-MM-DD'),
                },
              ],
            },
          },
        ],
      }
      myChart.setOption(option)
      // 隐藏loading动画
      myChart.hideLoading()
      const timer = setInterval(() => {
        toggleFlashing(myChart, chartData.value)
      }, 1000)
      // 将定时器推入数组
      timerArr.value.push(timer)
      myChart.dispatchAction({
        type: 'brush',
        areas: [
          {
            brushType: 'lineX',
            coordRange: props.coordRange[0] ? props.coordRange : props.markAreaRange,
            xAxisIndex: 0,
          },
        ],
      })

      // 点击事件
      myChart.on('click', (params) => {
        const { value } = params
        emit('dotClick', value)
      })
      const findMinDate = (data) => {
        return data.reduce((prev, next) => {
          return moment(prev.dt_ekd).isBefore(next.dt_ekd) ? prev : next
        }).dt_ekd
      }
      const findMaxDate = (data) => {
        return data.reduce((prev, next) => {
          return moment(prev.dt_ekd).isAfter(next.dt_ekd) ? prev : next
        }).dt_ekd
      }
      // 红框拖动事件
      myChart.on('brushEnd', (params) => {
        const selectedRange = params.areas[0].coordRange
        const startDate = moment(selectedRange[0]).format('YYYY-MM-DD')
        const endDate = moment(selectedRange[1]).format('YYYY-MM-DD')
        // 找出chartData中的最小日期和最大日期
        const minDate = findMinDate(chartData.value)
        const maxDate = findMaxDate(chartData.value)
        // 给父组件传递的参数
        const parentParams = {
          idWo: props.id,
          startDate,
          endDate,
          minDate,
          maxDate,
        }
        emit('simulate', parentParams)
      })
    }

    /**
     * 当红框的范围值存在时，获取范围内的最小值
     * 当红框的范围值不存在时，获取绿框的范围内的最小值
     */
    const getRangeMin = (redRange, greenRange) => {
      let min = null
      if (redRange) {
        min = redRange
      } else if (greenRange) {
        min = greenRange
      }
      // 向前推一个月
      return min ? moment(min).subtract(1, 'month').format('YYYY-MM-DD') : null
    }

    // 销毁echarts实例
    const removeBrush = () => {
      const id = `scatter-${props.id}`
      const myChart = echarts.init(document.getElementById(id))
      myChart.dispose()
    }

    const drawerState = reactive({
      visible: false,
      simulationType: '0',
      id: '',
      idWo: '',
    })
    // 打开点位抽屉
    const openDrawer = (params) => {
      const { data } = params
      const { id, id_wo } = data
      drawerState.id = id
      drawerState.idWo = id_wo
      drawerState.visible = true
    }

    // 提交事件
    const handleSubmit = async (idWo) => {
      const isBeforeSimulation = drawerState.simulationType === '0'
      await getChartList(isBeforeSimulation, parentFilterFields.value)
      emit('submit', idWo)
      drawerState.visible = false
    }
    const resize = () => {
      const id = `scatter-${props.id}`
      const myChart = echarts.init(document.getElementById(id))
      myChart.resize()
    }

    onMounted(async () => {
      // 监听resize事件
      window.addEventListener('resize', resize)
    })
    onBeforeUnmount(() => {
      // 清除定时器
      timerArr.value.forEach((item) => {
        clearInterval(item)
      })
      // 移除resize事件
      window.removeEventListener('resize', resize)
    })

    const chartRef = ref(null)

    return {
      getChartList,
      drawerState,
      handleSubmit,
      chartRef,
      resize,
    }
  },
  template: /*html*/ `
    <div ref="chartRef" :id="'scatter-'+id" style="width: 100%; height: 100%;"></div>
  `,
}

export default ScatterChart
