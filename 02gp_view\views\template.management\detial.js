import { queryBaseTask } from '../../api/baseTask.js'
import { useTemplateForm } from './useTemplateForm.js'
const { onMounted, defineAsyncComponent, ref, reactive } = Vue
const TemplateDetial = {
  components: {},
  props: {
    id: '',
  },
  setup(props) {
    const { requestFormDataById } = useTemplateForm()
    const formStateRef = ref(null)
    // define form state
    const formState = ref({})
    const tableData = Vue.ref([])
    const tableRef = ref(null)
    /**
     *  @description 根据ID获取form数据
     *  @param {string} id - ID
     */
    const getFormDataById = async () => {
      formState.value = await requestFormDataById(props.id)
      tableData.value = formState.value.nodes
    }
    /**
     * @description 获取label名称
     * @param {object} row
     * @return {*}
     */
    const getLabelName = (row) => {
      const labelMap = {
        1: 'Flow',
        2: 'Type',
        3: 'SM',
        4: 'Task',
      }
      return labelMap[row.int_level]
    }
    onMounted(async () => {
      await getFormDataById()
    })
    return {
      getFormDataById,
      tableRef,
      tableData,
      formState,
      formStateRef,
      getLabelName,
    }
  },
  template: /*html */ `
    <div class="border-l-4 border-l-emerald-700 text-emerald-700">
      <span class="text-xl pl-4">基础信息</span>
    </div>
    <div class="border-b-2 my-2"></div>
    <el-form ref="formStateRef" :model="formState" :rules="formRules" label-width="120px">
      <div class="grid grid-cols-5 grid-rows-2">
        <el-form-item label="模板名称" prop="str_template_name" class="items-center">
          <el-input v-model="formState.str_template_name"></el-input>
        </el-form-item>
        <el-form-item label="机型" prop="str_engine_type" class="items-center">
          <el-select v-model="formState.str_engine_type" placeholder="请选择" @change="changeEngineType">
            <el-option label="CFM56" value="CFM56"></el-option>
            <el-option label="LEAP" value="LEAP"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="is_state" class="items-center">
          <el-switch v-model="formState.is_state" inline-prompt :active-value="1" active-text="启用"
                     inactive-text="禁用" :inactive-value="0"
                     inactive-color="red" active-color="green"></el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="str_remark" class="col-span-5 items-center">
          <el-input v-model="formState.str_remark" type="textarea"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <div class="flex justify-between items-center border-l-4 border-l-emerald-700 text-emerald-700">
      <span class="text-xl pl-4">模板数据</span>
    
    </div>
    <div class="border-b-2 my-2"></div>
    <vxe-table
        ref="tableRef"
        :data="tableData"
        :tree-config="{ transform: true, parentField: 'id_root' }"
        :row-config="{ useKey: true }"
        show-overflow="title"
        :height="500"
        border="inner"
    >
      <vxe-column type="seq" width="80"></vxe-column>
      <vxe-column title="Label" tree-node>
        <template #default="{ row }">
          <span>{{ getLabelName(row) }}</span>
        </template>
      </vxe-column>
      <vxe-column field="str_node" title="Name"></vxe-column>
      <vxe-column title="工期" field="int_tat"></vxe-column>
      <vxe-column title="前置任务" field="str_task_ago"></vxe-column>
  
    </vxe-table>

  `,
}

export default TemplateDetial
