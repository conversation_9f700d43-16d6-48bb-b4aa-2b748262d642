<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.4 Chrome/138.0.7204.97 Electron/37.2.1 Safari/537.36" version="28.0.4">
  <diagram name="第 1 页" id="3OH01qa-r_BAd8d_nl43">
    <mxGraphModel dx="1426" dy="849" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="ZErM0DoNbmO4dGRKbF21-1" value="Horizontal Tree Layout" style="swimlane;startSize=20;horizontal=0;childLayout=treeLayout;horizontalTree=1;sortEdges=1;resizable=0;containerType=tree;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="10" y="290" width="310" height="160" as="geometry" />
        </mxCell>
        <mxCell id="ZErM0DoNbmO4dGRKbF21-2" value="Root" style="whiteSpace=wrap;html=1;" vertex="1" parent="ZErM0DoNbmO4dGRKbF21-1">
          <mxGeometry x="40" y="60" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ZErM0DoNbmO4dGRKbF21-3" value="Child 1" style="whiteSpace=wrap;html=1;" vertex="1" parent="ZErM0DoNbmO4dGRKbF21-1">
          <mxGeometry x="190" y="20" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ZErM0DoNbmO4dGRKbF21-4" value="" style="edgeStyle=elbowEdgeStyle;elbow=horizontal;html=1;rounded=1;curved=0;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startSize=6;endSize=6;" edge="1" parent="ZErM0DoNbmO4dGRKbF21-1" source="ZErM0DoNbmO4dGRKbF21-2" target="ZErM0DoNbmO4dGRKbF21-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZErM0DoNbmO4dGRKbF21-5" value="Child 2" style="whiteSpace=wrap;html=1;" vertex="1" parent="ZErM0DoNbmO4dGRKbF21-1">
          <mxGeometry x="190" y="100" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ZErM0DoNbmO4dGRKbF21-6" value="" style="edgeStyle=elbowEdgeStyle;elbow=horizontal;html=1;rounded=1;curved=0;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startSize=6;endSize=6;" edge="1" parent="ZErM0DoNbmO4dGRKbF21-1" source="ZErM0DoNbmO4dGRKbF21-2" target="ZErM0DoNbmO4dGRKbF21-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
