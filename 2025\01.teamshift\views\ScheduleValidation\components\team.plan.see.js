const { useVModel } = VueUse
const { ref, onMounted } = Vue
 import {
  GetCheckShiftByIds,
  queryShifts,
  queryTeamSecList,
  updateCheckShift
} from '../api/index.js'
export default {
  name: 'TeamPlanSee',
  props: {
    planItem: Object,
    visible: Boolean,
    deptId:String,
    teamId:String
  },
  setup(props) {
    const teamPlanSeeVisible = useVModel(props, 'visible')
    const teamPlanSeeList = ref([])
    const num = ref(0)

    const feedbackItem = ref(null)
    const getTeamPlanSeeList = async () => {
      const ids = props.planItem.task.map(item => item.id) 
      const res = await GetCheckShiftByIds(ids)
      //feedbackItem.value = res.pt_team_plan_dtosee.pt_main.str_content
      teamPlanSeeList.value = res ?? []
      num.value = teamPlanSeeList.value.length
    }
  
    const shiftEnum = ref([])
    // 获取Site 下拉选择
    const getShiftOptions = async () => {
      const res = await queryShifts()
      const shiftOptions = res.data ?? []
      shiftEnum.value = shiftOptions.reduce((map, item) => {
        map[item.id] = item.str_name
        return map
      }, {})
    }
    
  
    // 转化数组为字符串
    const convertArrayToString = (array) => {
      return array?.join('~')
    }

    const convertArrayToNameName = (array) => {
      let strs = [];
      array.forEach((taskId) => {
        strs.push(teamSecOptions.value[taskId]);
      }) 
      return strs?.join(',')
    }
    // 关闭弹窗
    const handleClose = () => {
      teamPlanSeeVisible.value = false
    }

     const teamSecOptions = ref([])
    const getTeamSecOptions = async () => {
      const params = {
        id_main: props.planItem.planId,
        id_team: props.planItem.id_team,
        pt_dt:props.planItem.plan_date
      }
      const res = await queryTeamSecList(params)
      teamSecOptions.value = res ?? []
    }

    onMounted(() => {
      getTeamPlanSeeList();
      getShiftOptions();
      getTeamSecOptions();
    })
    return {
      teamPlanSeeVisible,
      teamPlanSeeList,
      convertArrayToString,
      convertArrayToNameName,
      num,
      teamSecOptions,
      shiftEnum,
      handleClose
      
    }
  },
  template: /*html*/ `
    <el-dialog v-model="teamPlanSeeVisible" title="Team Plan See" width="50%" class="common-dialog" :append-to-body="true">
      <div class="mb-2">
        <el-badge :value="num" class="item" type="info">
          <el-button>Num</el-button>
        </el-badge>
      </div>
      <div class="mb-2" v-for="item in teamPlanSeeList" :key="item.id">
        <el-card>
          <el-descriptions :column="3" border>
          <el-descriptions-item label="Name">{{ item.str_staff }}</el-descriptions-item>
            <el-descriptions-item label="ESN">{{item.str_esn }}</el-descriptions-item>
            <el-descriptions-item label="Model">{{ item.str_sm }}</el-descriptions-item>
            <el-descriptions-item label="Task Type">{{ item.str_check_type }}</el-descriptions-item> 
            <el-descriptions-item label="Task">{{item.str_task }}</el-descriptions-item>
            <el-descriptions-item label="Task Description">{{ item.str_task_description }}</el-descriptions-item>
            <el-descriptions-item label="排班时间">{{ item.dt_shift }}</el-descriptions-item>
            <el-descriptions-item label="Shift">{{ item.str_shift }}</el-descriptions-item>
            <el-descriptions-item label="Shift Time">{{item.str_start_time}} ~{{item.str_end_time}}</el-descriptions-item>
            <el-descriptions-item :span="3" label="Team Secs">{{ convertArrayToNameName(item.teamSecs) }}</el-descriptions-item>
            <el-descriptions-item :span="3" label="Remark">{{ item.str_remark }}</el-descriptions-item>
          </el-descriptions> 
        </el-card>
      </div>
      <div>
        <el-card header="feedback">
          <div>{{ feedbackItem }}</div>
        </el-card>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleClose">Cancel</el-button>
      </template>
    </el-dialog>
  `,
}
