import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'
import { useLanuchPanelTable } from '../composables/useLanuchPanelTable.js'
import ReceiveHandover from '../receive.handover.page.js'
const { defineComponent, h, ref, inject } = Vue
const { useVModel } = VueUse
const { ElDrawer, ElButton } = ElementPlus
const { VxeColumn } = VXETable
const { VxePager } = VxeUI

const LaunchPanelDrawer = defineComponent({
  name: 'LaunchPanelDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    queryParams: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['update:visible', 'close'],
  setup(props, { emit }) {
    const businessType = inject('businessType')
    const visible = useVModel(props, 'visible', emit)
    const page = ref({
      currentPage: 1,
      pageSize: 10,
      total: 0,
    })
    const { tableData, tableColumns, getTableData } = useLanuchPanelTable(props, businessType, page.value)

    const handleClose = () => {
      visible.value = false
      emit('close')
    }

    const receiveVisible = ref(false)
    const currentReceiveRow = ref({})
    const handleReceive = (row) => {
      currentReceiveRow.value = row
      receiveVisible.value = true
    }

    const drawerProps = {
      class: 'common-drawer',
      modelValue: visible.value,
      title: '开工列表',
      size: '80%',
      destroyOnClose: true,
      appendToBody: true,
      onClose: () => handleClose(),
    }

    const operationColumnProps = {
      title: '操作',
      width: 100,
      align: 'center',
      fixed: 'right',
    }
    // 操作列
    const operationColumn = () => {
      return h(VxeColumn, operationColumnProps, {
        default: ({ row }) => {
          return h('div', { class: 'flex justify-center' }, [
            h(ElButton, { text: true, type: 'primary', onClick: () => handleReceive(row) }, { default: () => '接收' }),
          ])
        },
      })
    }

    return () => [
      h(ElDrawer, drawerProps, {
        default: () => [
          h('div', { style: { height: 'calc(100% - 55px)' } }, [
            h(
              HtVxeTable,
              {
                tableData: tableData.value,
                tableColumns: tableColumns.value,
              },
              {
                operation: operationColumn,
              },
            ),
            h(VxePager, {
              currentPage: page.value.currentPage,
              pageSize: page.value.pageSize,
              total: page.value.total,
              onPageChange: ({ currentPage, pageSize }) => {
                page.value.currentPage = currentPage
                page.value.pageSize = pageSize
                getTableData()
              },
            }),
          ]),
          h(ReceiveHandover, {
            visible: receiveVisible.value,
            row: currentReceiveRow.value,
            onRefresh: () => getTableData(),
          }),
        ],
        footer: () => [h(ElButton, { type: 'primary', onClick: () => handleClose() }, { default: () => '取消' })],
      }),
    ]
  },
})

export default LaunchPanelDrawer
