import { post } from '../../../../config/axios/httpReuest.js'
// 引入公共表格组件
import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
import { useApi } from '../../../hooks/useApi.js'

const { reactive, toRefs, ref, onMounted } = Vue
const { useVModel } = VueUse
const PointColotDrawer = {
  components: {
    HtVxeTable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    // 模拟前or模拟后
    simulationType: {
      type: String,
      default: '0',
    },
    id: {
      type: String,
      default: '',
    },
    idWo: {
      type: String,
      required: true,
    },
    filterFields: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)
    const tableRef = ref(null)
    // 表格数据
    const tableState = reactive({
      data: null,
      columns: [
        {
          title: 'Kitting完成/站点',
          field: 'str_nodename',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '目标WO',
          field: 'str_code',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '目标ESN',
          field: 'str_esn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PN',
          field: 'str_pn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件标签',
          field: 'str_label',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件名称',
          field: 'str_part_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'SM',
          field: 'str_sm',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '客户',
          field: 'str_client',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件类别',
          field: 'str_item_type',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PKP',
          field: 'id_pkp',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '机型',
          field: 'str_engine_type',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '数量',
          field: 'int_qty',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'EKD',
          field: 'dt_ekd',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
      ],
      total: 0,
    })
    // * 获取表格数据
    const getTableData = async () => {
      const { id, idWo, simulationType, type, filterFields } = props
      const params = {
        ac: 'de_getpnlist_bygroup',
        id: id,
        id_wo: idWo,
        int_ekd_type: simulationType,
        int_point_type: type,
        filter_fields: filterFields,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        tableState.data = data.data
        tableState.total = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    // * 表格筛选
    const handleFilterChange = (data) => {
      tableState.total = tableRef.value.getCurrentLength()
    }
    const { markPartApi } = useApi()

    // * 可缺件
    const handleCanLack = (type, int_type_sub) => {
      const selectedData = tableRef.value.getSelectedData()
      // 提醒
      if (selectedData.length === 0) {
        ElementPlus.ElMessage.warning('请选择数据')
        return
      }
      // 是否确定提交
      ElementPlus.ElMessageBox.confirm('是否确定提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 提交
        const { idWo } = props
        const idList = selectedData.map((item) => item.id_pkp)
        markPartApi(idWo, type, idList, 0, int_type_sub).then(() => {
          emit('submit', idWo)
        })
      })
    }

    // * 导出表格数据
    const exportTableData = () => {
      tableRef.value.exportData()
    }

    onMounted(() => {
      getTableData()
    })
    return {
      visible,
      tableRef,
      ...toRefs(tableState),
      handleFilterChange,
      handleCanLack,
      exportTableData,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="85%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-white">零件清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <el-button type="primary" @click="exportTableData">导出</el-button>
          </div>
          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable 
          ref="tableRef"
          :tableData="data" 
          :tableColumns="columns"
          :isShowHeaderCheckbox="true"
          @filterChange="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
  `,
}

export default PointColotDrawer
