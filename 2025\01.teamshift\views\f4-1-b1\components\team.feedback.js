import { submitFeedback ,saveFeedBack} from '../../../api/teams/index.js'
const { useVModel } = VueUse
const { defineComponent, ref } = Vue
const { ElMessage } = ElementPlus
export const TeamFeedback = defineComponent({
  name: 'TeamFeedback',
  props: {
    task: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  },
  emits: ['refresh'],
  setup(props, { emit }) {
    const openFeedback = useVModel(props, 'visible')
    const feedback = ref('')
    const handleClose = () => {
      openFeedback.value = false
    }
    const handleSubmit = async () => {
      const planItem = props.task.row.plan.find((item) => item.plan_date === props.task.column.day)
      const taskids = Array.from(new Set(planItem.task.map((item) => item.taskId)))   
      const feedbackList =[];
      taskids.forEach((taskId) => {
        const feedbackdata = {
          id: '',
          id_wo: props.task.row.id_wo || '',
          str_wo: props.task.row.wo || '',
          id_team: props.task.row.id_team || '',
          dt_feed: planItem.plan_date,
          id_sm: planItem.task.filter((item) => item.taskId === taskId).map((item) => item.modelId).join(','),
          id_task: taskId,
          str_task: planItem.task.filter((item) => item.taskId === taskId).map((item) => item.taskname).join(','),
          id_shift:props.task.row.id_shift,
          str_shift:props.task.row.shift_name,
          str_feed_back_type:props.task.business,
          str_flow:props.task.inputStrFlow,
          str_group:props.task.inputGroupType,
          int_status: props.task.status,
          str_content:feedback.value
        }  
        feedbackList.push(feedbackdata)
      })
      try {
        await saveFeedBack(feedbackList)
        openFeedback.value = false
        ElMessage.success('反馈成功')
        emit('refresh')
      } catch (error) {
        ElMessage.error('反馈失败')
      }
      // const params = {
      //   planid: props.task.planId,
      //   status: props.task.status,
      //   reason: feedback.value,
      // }
      // try {
      //   await submitFeedback(params)
      //   openFeedback.value = false
      //   ElMessage.success('反馈成功')
      //   emit('refresh')
      // } catch (error) {
      //   ElMessage.error('反馈失败')
      // }
    }
    return {
      openFeedback,
      feedback,
      handleClose,
      handleSubmit,
    }
  },
  template: /*html*/ `
    <el-dialog class="common-dialog" v-model="openFeedback" title="班组反馈" width="50%">
      <label class="mb-2 el-form-item__label">Remark</label>
      <el-input v-model="feedback" type="textarea" :rows="4" placeholder="Please input" />
      <template #footer>
        <el-button type="primary" @click="handleClose">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit">Submit</el-button>
      </template>
    </el-dialog>
  `,
})

export default TeamFeedback
