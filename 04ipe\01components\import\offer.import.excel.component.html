<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel 导入</title>
    <script src="../../../Offer/asset/vue/vue.js"></script>
    <script src="../../../Offer/asset/element/index.js"></script>
    <script src="../../../Offer/asset/axios/axios.min.js"></script>
    <script src="../../../Offer/comm/offer.comm.pip.js"></script>
    <script src="../../../Offer/comm/offer.comm.tool.js"></script>
    <script src="../../../Offer/comm/api_environment.js"></script>
    <script  src="../../../Offer/offer.comm.component/import/offer.import.excel.component.js"></script>
    <script  src="../../asset/sheetjs/xlsx.full.min.js"></script>
    <link rel="stylesheet" href="../../../Offer/asset/element/index.css" />
    <link rel="stylesheet" href="../../../Offer/asset/css/offer.page.css">
</head>

<body>
    <div id="app">
        <y-offer-import-excel input_type="1" ></y-offer-import-excel>
    </div>
</body>
<script>
    var vue_sm = new Vue({
        el: '#app',
        data() {
            return {

            }
        },
        filters: Object.assign(OfferPip, {


        }),

        methods: {

        },
        created: function () {
            let url = OfferTotal.queryURLParams("");
        },
    })
</script>

</html>