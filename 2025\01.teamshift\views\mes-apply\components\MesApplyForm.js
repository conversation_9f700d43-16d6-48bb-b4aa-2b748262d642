import { getStaffList, createAndUpdateMesApply } from '../api/index.js'

const { ref, computed, watch, onMounted, nextTick } = Vue
const { ElMessage } = ElementPlus
const { useVModel } = VueUse

// 申请表单组件
export const MesApplyForm = {
  name: 'MesApplyForm',
  props: {
    formData: { type: Object, required: true },
    currentView: { type: String, required: true },
    loading: { type: Boolean, default: false },
    businessRules: { type: Object, required: false },
    visible: { type: Boolean, default: false },
  },
  emits: ['back', 'save', 'update:details', 'update:visible', 'success'],
  setup(props, { emit }) {
    const dialogVisible = useVModel(props, 'visible', emit)
    const formRef = ref(null)
    const userOptions = ref([])
    const isLoading = ref(false)
    const isSubmitting = ref(false)

    // 表单验证规则
    const formRules = computed(() => ({
      person: [{ required: true, message: '请选择人员', trigger: 'change' }],
      startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
      endDate: [
        { required: true, message: '请选择结束日期', trigger: 'change' },
        {
          validator: (rule, value, callback) => {
            if (value && props.formData.startDate) {
              // 使用moment.js比较日期
              const startDate = moment(props.formData.startDate, 'YYYY-MM-DD')
              const endDate = moment(value, 'YYYY-MM-DD')

              if (!endDate.isAfter(startDate)) {
                callback(new Error('结束日期必须晚于开始日期'))
              } else {
                callback()
              }
            } else {
              callback()
            }
          },
          trigger: 'change',
        },
      ],
      reason: [
        { required: true, message: '请填写申请事由', trigger: 'blur' },
        { min: 5, message: '申请事由至少需要5个字符', trigger: 'blur' },
      ],
    }))

    // 计算总申请工时
    const totalHours = computed(() => {
      if (!props.formData.details || props.formData.details.length === 0) return 0
      return props.formData.details.reduce((total, detail) => total + (detail.appliedHours || 0), 0)
    })

    // 生成工时详情
    const generateDetails = () => {
      console.log('generateDetails 被调用:', {
        startDate: props.formData.startDate,
        endDate: props.formData.endDate,
        existingDetails: props.formData.details,
      })

      if (props.formData.startDate && props.formData.endDate) {
        const newDetails = props.businessRules.generateWorkTimeDetails(
          props.formData.startDate,
          props.formData.endDate,
          props.formData.details || [],
        )
        console.log('业务规则生成的详情:', newDetails)
        props.formData.details = newDetails
      }
    }

    // 监听日期变化
    watch(
      [() => props.formData.startDate, () => props.formData.endDate],
      ([newStartDate, newEndDate], [oldStartDate, oldEndDate]) => {
        console.log('日期变化:', {
          新开始日期: newStartDate,
          新结束日期: newEndDate,
          旧开始日期: oldStartDate,
          旧结束日期: oldEndDate,
        })

        // 只有当两个日期都有值时才生成详情
        if (newStartDate && newEndDate) {
          generateDetails()
          console.log('生成的工时详情:', props.formData.details)
        } else {
          console.log('日期不完整，清空详情')
          props.formData.details = []
        }
      },
      { immediate: false },
    )

    // 处理工时详情更新
    const handleDetailsUpdate = (newDetails) => {
      props.formData.details = newDetails
      emit('update:details', newDetails)
    }

    // 表单验证
    const validateForm = async () => {
      if (!formRef.value) return false

      try {
        await formRef.value.validate()

        // 业务规则验证
        const businessValidation = props.businessRules.validateForm(props.formData)
        if (!businessValidation.isValid) {
          ElMessage.error(businessValidation.errors[0])
          return false
        }

        return true
      } catch (error) {
        console.error('表单验证失败:', error)
        return false
      }
    }

    // 数据格式转换
    const formatFormData = (submitForApproval = false) => {
      const mesApply = {
        id: props.formData.id || '',
        id_staff: props.formData.person,
        dt_mes_start: props.formData.startDate,
        dt_mes_end: props.formData.endDate,
        int_mes_day: props.formData.details ? props.formData.details.length : 0,
        str_apply_reason: props.formData.reason,
        str_remark: props.formData.remarks || '',
        int_substatus: submitForApproval ? 301 : 0,
      }

      const mesApplySubs = (props.formData.details || []).map((detail) => ({
        dt_date: detail.date,
        dt_start_time: detail.startTime,
        dt_end_time: detail.endTime,
        int_apply_hours: detail.appliedHours,
      }))

      return { mesApply, mesApplySubs }
    }

    // 保存处理
    const handleSave = async (submitForApproval = false) => {
      // 验证表单
      const isValid = await validateForm()
      if (!isValid) return

      try {
        isSubmitting.value = true

        // 格式化数据
        const saveData = formatFormData(submitForApproval)

        // 调用API保存
        await createAndUpdateMesApply(saveData)

        const action = submitForApproval ? '提交' : '保存'
        ElMessage.success(`${action}成功`)

        // 触发成功事件
        emit('success', {
          action: submitForApproval ? 'submit' : 'save',
        })

        // 关闭对话框
        dialogVisible.value = false
      } catch (error) {
        console.error('保存失败:', error)
        // const action = submitForApproval ? '提交' : '保存'
        // ElMessage.error(`${action}失败，请重试`)
      } finally {
        isSubmitting.value = false
      }
    }

    // 获取人员下拉列表
    const getUserOptions = async () => {
      try {
        isLoading.value = true
        const data = await getStaffList()
        userOptions.value = Array.isArray(data) ? data : []
      } catch (error) {
        console.error('获取人员列表失败:', error)
        // ElMessage.error('获取人员列表失败，请重试')
        userOptions.value = []
      } finally {
        isLoading.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields()
      }
    }

    // 关闭对话框前确认
    const handleClose = async () => {
      dialogVisible.value = false
      emit('back')
    }

    // 组件挂载时获取数据
    onMounted(() => {
      getUserOptions()
    })

    // 监听对话框打开，重置表单验证状态
    watch(dialogVisible, (newVal) => {
      if (newVal && formRef.value) {
        nextTick(() => {
          formRef.value.clearValidate()
        })
      }
    })

    return {
      dialogVisible,
      formRef,
      userOptions,
      isLoading,
      isSubmitting,
      formRules,
      totalHours,
      generateDetails,
      handleDetailsUpdate,
      handleSave,
      handleClose,
      resetForm,
    }
  },
  template: /*html*/ `
    <el-drawer
      class="common-drawer"
      v-model="dialogVisible"
      size="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClose"
    >
      <template #title>
        <div class="flex items-center justify-between">
          <h1 class="text-xl font-semibold text-gray-900">
            {{ currentView === 'create' ? '新增申请' : currentView === 'edit' ? '编辑申请' : currentView === 'confirm' ? '确认申请' : '查看申请' }}
          </h1>
          <div v-if="formData.details && formData.details.length > 0" class="text-sm text-gray-600">
            总申请工时:
            <span class="font-medium text-blue-600">{{ totalHours }}</span>
            小时
          </div>
        </div>
      </template>

      <div class="rounded-lg bg-white shadow-sm">
        <!-- 表单 -->
        <div class="p-6">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="100px"
            :disabled="currentView === 'view'"
            class="space-y-4"
          >
            <!-- 主表单 -->
            <div class="mb-8 grid grid-cols-1 gap-6 md:grid-cols-3">
              <el-form-item label="人员" prop="person">
                <el-select
                  v-model="formData.person"
                  :placeholder="isLoading ? '加载人员列表中...' : '请选择人员'"
                  class="w-full"
                  :loading="isLoading"
                  :disabled="isLoading || currentView === 'confirm'"
                  filterable
                  clearable
                >
                  <el-option v-for="user in userOptions" :key="user.id" :label="user.str_name" :value="user.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="开始日期" prop="startDate">
                <el-date-picker
                  v-model="formData.startDate"
                  type="date"
                  placeholder="选择开始日期"
                  class="w-full"
                  :disabled="currentView === 'confirm'"
                  :disabled-date="(time) => time.getTime() < Date.now() - 24 * 60 * 60 * 1000"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>

              <el-form-item label="结束日期" prop="endDate">
                <el-date-picker
                  v-model="formData.endDate"
                  type="date"
                  placeholder="选择结束日期"
                  class="w-full"
                  :disabled="currentView === 'confirm'"
                  :disabled-date="(time) => {
                    const today = Date.now() - 24 * 60 * 60 * 1000
                    const startDate = formData.startDate ? new Date(formData.startDate).getTime() : today
                    return time.getTime() < Math.max(today, startDate)
                  }"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </div>

            <el-form-item label="申请事由" prop="reason">
              <el-input
                v-model="formData.reason"
                type="textarea"
                :rows="3"
                placeholder="请详细描述申请原因（至少5个字符）"
                class="w-full"
                :disabled="currentView === 'confirm'"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="备注">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                :rows="2"
                placeholder="额外备注信息（可选）"
                class="w-full"
                :disabled="currentView === 'confirm'"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <!-- 工时详情 -->
            <el-form-item label="工时详情">
              <div class="w-full">
                <slot
                  name="details"
                  :details="formData.details"
                  :readonly="currentView === 'view'"
                  :confirmMode="currentView === 'confirm'"
                  :onUpdate="handleDetailsUpdate"
                />
              </div>
            </el-form-item>

            <!-- 操作按钮 -->

            <!-- 查看模式的关闭按钮 -->
          </el-form>
        </div>
      </div>
      <template #footer>
        <slot name="footer">
          <div v-if="currentView !== 'view' && currentView !== 'confirm'">
            <div class="space-x-4">
              <el-button 
                type="success" 
                @click="handleSave(false)" 
                :loading="isSubmitting" 
                :disabled="isSubmitting"
                icon="Document"
              >
                {{ isSubmitting ? '保存中...' : '保存' }}
              </el-button>
              <el-button 
                type="primary" 
                @click="handleSave(true)" 
                :loading="isSubmitting" 
                :disabled="isSubmitting"
                icon="DocumentAdd"
              >
                {{ isSubmitting ? '提交中...' : '保存并提交审批' }}
              </el-button>
              <el-button @click="handleClose" :disabled="isSubmitting" icon="Back">取消</el-button>
            </div>
          </div>
          <div v-else-if="currentView === 'view'">
            <el-button @click="handleClose" icon="Back">关闭</el-button>
          </div>
        </slot>
      </template>
    </el-drawer>
  `,
}
