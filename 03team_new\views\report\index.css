        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, "Microsoft YaHei", sans-serif;
            padding: 20px;
            background-color: #fff;
        }

        /* 头部样式 */
        .header {
            width: 100%;
            zoom: 1;
        }

        .header:before,
        .header:after {
            content: "";
            display: table;
            clear: both;
        }

        .logo {
            float: left;
            max-width: 200px;
            height: auto;
        }

        .tat-container {
            float: right;
            margin-right: 20px;
        }

        .tat-container div {
            float: left;
            margin-left: 50px;
        }

        /* 信息容器 */
        .info-container {
            clear: both;
            width: 100%;
            text-align: center;
            padding: 20px 0;
        }

        .info-item {
            line-height: 24px;
            margin: 5px 0;
        }

        /* 流程图 */
        .flow-chart {
            width: 100%;
            text-align: center;
            margin: 30px 0;
            white-space: nowrap;
            overflow-x: auto;
            padding: 10px 0;
            font-size: 0;
            /* 消除inline-block间距 */
        }

        .flow-item {
            display: inline-block;
            vertical-align: top;
            margin-right: 25px;
            text-align: center;
        }

        .flow-step {
            display: block;
            position: relative;
            height: 40px;
            line-height: 40px;
            background: #4876FF;
            color: white;
            text-align: center;
            min-width: 100px;
            padding: 0 20px;
            font-size: 14px;
            clip-path: polygon(15% 0%, 100% 0%, 85% 100%, 0% 100%);
            -webkit-clip-path: polygon(15% 0%, 100% 0%, 85% 100%, 0% 100%);
            margin-bottom: 5px;
        }

        .flow-step-content {
            font-weight: bold;
        }

        .flow-date {
            font-size: 14px;
            margin-top: 5px;
            line-height: 1.2;
            font-weight: bold;        
        }

        .flow-step.current {
            background: #2E8B57;
        }

        /* IE浏览器不支持clip-path的替代方案 */
        @media screen\9 {
            .flow-step {
                background: none;
                margin-right: 0;
            }

            .flow-step:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 20px 0 20px 20px;
                border-color: transparent transparent transparent #4876FF;
                
            }

            .flow-step:after {
                content: '';
                position: absolute;
                right: -20px;
                top: 0;
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 20px 0 20px 20px;
                border-color: transparent transparent transparent #4876FF;
            }

            .flow-step-content {
                position: relative;
                z-index: 1;
                background: #4876FF;
                height: 40px;
                line-height: 40px;
                padding: 0 20px;
            }

            .flow-step.current:before {
                border-color: transparent transparent transparent #2E8B57;
            }

            .flow-step.current:after {
                border-color: transparent transparent transparent #2E8B57;
            }

            .flow-step.current .flow-step-content {
                background: #2E8B57;
            }
        }

        /* 移除滚动条样式，保持简单 */
        .flow-chart::-webkit-scrollbar {
            height: 6px;
        }

        .flow-chart::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .flow-chart::-webkit-scrollbar-thumb {
            background: #ccc;
        }

        /* 工作站信息 */
        .workstation-info {
            width: 100%;
            zoom: 1;
            margin: 20px 0;
            height: 40px;
            line-height: 40px;
            background: #105ea5;
            color: #fff;
        }

        .workstation-info:before,
        .workstation-info:after {
            content: "";
            display: table;
            clear: both;
        }

        .workstation-info div:first-child {
            float: left;
        }

        .workstation-info div:last-child {
            float: right;
            margin-right: 20px;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            margin-top: 20px;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        /* IE8及以下浏览器透明度支持 */
        .opacity {
            filter: alpha(opacity=100);
            -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
        }
        .td_float{
            text-align: right;
        }
      
   .td_border{
            border: 1px solid #ddd;
            padding: 8px;
            border-color: #B3C6E7;
   }
   .td_border_color{
            border: 0px solid #ddd;
            padding: 0px;
            margin: 0px;
            border-color: #105ea5;
            color: #fff; 
            line-height: 23px;
            text-align: left;
   }