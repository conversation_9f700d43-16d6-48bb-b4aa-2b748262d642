const { computed, ref, watch, onMounted } = Vue // Vue 3 global (ensure Vue is available globally)

/**
 * @typedef {Object} ColumnConfig
 * @property {string} key - 列的唯一标识符。
 * @property {string} label - 列的显示名称。
 * @property {boolean} [fixed] - 可选。如果为 true，则该列始终可见且不可隐藏。
 */

const DynamicColumnConfigurator = {
  name: 'DynamicColumnConfigurator',
  props: {
    allColumns: {
      type: Array,
      default: () => [
        { key: 'name', label: '名称' },
        { key: 'date', label: '日期' },
        { key: 'status', label: '状态' },
        { key: 'address', label: '地址' },
      ], // 提供默认列
      validator: (value) =>
        Array.isArray(value) &&
        value.every((col) => col && typeof col.key === 'string' && typeof col.label === 'string'),
    },
    modelValue: {
      type: Array,
      default: () => ['name', 'date'], // 默认显示的列
    },
    defaultSearchTerm: {
      type: String,
      default: '',
    },
    searchPlaceholder: {
      type: String,
      default: '搜索列名...',
    },
    buttonText: {
      type: String,
      default: '列设置',
    },
    buttonIcon: {
      type: String,
      default: 'Setting',
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const searchTerm = ref(props.defaultSearchTerm)
    const popoverVisible = ref(false)

    // 提取所有固定列的 key (基于原始 allColumns)
    const fixedColumnKeys = computed(() => {
      if (!props.allColumns || !Array.isArray(props.allColumns)) {
        return []
      }
      return props.allColumns.filter((col) => col.fixed === true).map((col) => col.key)
    })

    // 内部状态，用于存储选中的非固定列
    const selectedNonFixedKeys = ref([])

    // 初始化选中的列
    onMounted(() => {
      // 确保初始值正确设置(仅非固定列)
      selectedNonFixedKeys.value = [...props.modelValue]
    })

    // 监听 props.modelValue 的变化
    watch(
      () => props.modelValue,
      (newVal) => {
        if (Array.isArray(newVal)) {
          selectedNonFixedKeys.value = [...newVal]
        }
      },
      { deep: true },
    )

    // 过滤后的列，用于在UI中显示
    const filteredColumns = computed(() => {
      if (!props.allColumns || props.allColumns.length === 0) {
        return []
      }
      if (!searchTerm.value) {
        return props.allColumns // 如果没有搜索词，显示所有
      }
      const lowerSearchTerm = searchTerm.value.toLowerCase()
      return props.allColumns.filter((col) => col.label.toLowerCase().includes(lowerSearchTerm))
    })

    // 计算用于UI显示的选中列（包括固定列和非固定列）
    const displaySelectedKeys = computed(() => {
      return [...selectedNonFixedKeys.value, ...fixedColumnKeys.value]
    })

    // 处理选中状态变化
    const handleSelectionChange = (value) => {
      // 获取用户实际选择的非固定列
      const userSelectedNonFixedKeys = value.filter((key) => !fixedColumnKeys.value.includes(key))

      // 更新内部状态
      selectedNonFixedKeys.value = userSelectedNonFixedKeys

      // 通知父组件
      emit('update:modelValue', userSelectedNonFixedKeys)
    }

    const togglePopover = () => {
      popoverVisible.value = !popoverVisible.value
    }

    return {
      searchTerm,
      filteredColumns,
      displaySelectedKeys,
      popoverVisible,
      togglePopover,
      handleSelectionChange,
      fixedColumnKeys,
    }
  },
  template: /*html*/ `
    <div class="dynamic-column-configurator">
      <el-popover
        v-model:visible="popoverVisible"
        trigger="click"
        placement="bottom"
        :width="250"
        popper-class="column-config-popper"
      >
        <template #reference>
          <el-button size="small" type="primary" :icon="buttonIcon">{{ buttonText }}</el-button>
        </template>
        <div class="p-2">
          <el-input v-model="searchTerm" :placeholder="searchPlaceholder" clearable size="small" class="mb-3" />
          <el-checkbox-group
            v-model="displaySelectedKeys"
            @change="handleSelectionChange"
            v-if="allColumns && allColumns.length > 0"
          >
            <div v-if="filteredColumns.length > 0">
              <div v-for="column in filteredColumns" :key="column.key" class="column-config-item mb-1 block">
                <el-checkbox :label="column.key" :disabled="column.fixed === true" class="w-full">
                  {{ column.label }}
                </el-checkbox>
              </div>
            </div>
            <div v-else class="p-2 text-center text-sm text-gray-500">没有匹配的列</div>
          </el-checkbox-group>
          <div v-else class="p-2 text-center text-sm text-gray-500">没有可配置的列</div>
        </div>
      </el-popover>
    </div>
  `,
}

export default DynamicColumnConfigurator
