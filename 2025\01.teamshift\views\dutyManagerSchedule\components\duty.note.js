import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'
import { getDutyNoteList } from '../../../api/dutyManagerSchedule/index.js'
export default {
  name: 'DutyNote',
  components: {
    HtVxeTable,
  },
  props: {
    data: Object,
    isOpen: Boolean,
  },
  setup(props) {
    const { ref, onMounted } = Vue
    const { useVModel } = VueUse
    const openDutyNoteDialog = useVModel(props, 'isOpen')
    const tableData = ref([])
    const tableColumns = ref([
      {
        field: 'str_code',
        title: '员工编号',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_staff',
        title: '姓名',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_dept',
        title: '部门',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_sec',
        title: '分部',
        minWidth: 100,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
    ])

    const fetchTableData = async () => {
      const params = {
        dt_plan: props.data.date,
        // 动态的key 当props.data.row.name为空时，不传str_dept 连字符串都不传
        ...(props.data.row.name !== 'Total' ? { str_dept: props.data.row.name } : {}),
      }
      tableData.value = await getDutyNoteList(params)
    }

    onMounted(() => {
      fetchTableData()
    })
    return {
      openDutyNoteDialog,
      tableData,
      tableColumns,
    }
  },
  template: /*html*/ `
    <el-drawer class="common-drawer" v-model="openDutyNoteDialog" title="清单列表" size="80%">
      <HtVxeTable :tableData="tableData" :tableColumns="tableColumns"></HtVxeTable>
      <template #footer>
        <el-button type="primary" @click="openDutyNoteDialog = false">关闭</el-button>
      </template>
    </el-drawer>
  `,
}
