import { post } from '../../../config/axios/httpReuest.js'
import { currentNodeKey } from '../../../config/keys.js'
import { refreshMap } from './refreshMap.js'

const { reactive, ref, inject } = Vue

export default {
  name: 'HeaderSearchComponent',
  emits: ['search'],
  setup(props, { emit, attrs }) {
    const currentNode = inject(currentNodeKey)
    const collapseStatus = ref(true)
    const searchForm = reactive({
      date: [moment().format('YYYY-MM-DD'), moment().add(6, 'days').format('YYYY-MM-DD')],
      //id_engine_type: 'cfm56',
    })
    // search
    const handleSearchClick = () => {
      emit('search', searchForm)
    }
    // refresh
    const handleRefreshClick = async () => {
      const loading = ElementPlus.ElLoading.service({
        fullscreen: true,
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const paramsOne = {
        ac: 'pda_getpsmbegindate',
      }
      await post(paramsOne)
      const params = {
        ac: refreshMap.get(currentNode),
      }
      // 为了触发watch监听机制，每次刷新都需要重新获取时间
      searchForm.reloadTime = moment().format('YYYY-MM-DD HH:mm:ss')
      await post(params)
      emit('search', searchForm)
      loading.close()
    }
    return {
      collapseStatus,
      searchForm,
      handleSearchClick,
      handleRefreshClick,
    }
  },
  template: /*html*/ `
    <vxe-form v-model:collapseStatus="collapseStatus" :data="searchForm" custom-layout>
      <vxe-form-item title="Date:">
        <template #default="{data}">
          <el-date-picker
            v-model.trim="data.date"
            type="daterange"
            range-separator="to"
            start-placeholder="Start date"
            end-placeholder="End date"
            value-format="YYYY-MM-DD"
            clearable
          ></el-date-picker>
        </template>
      </vxe-form-item>
      <vxe-form-item title="ESN:" folding>
        <template #default="{data}">
          <el-input v-model.trim="data.str_esn" clearable style="width: 210px;"></el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="WO:" folding>
        <template #default="{data}">
          <el-input v-model.trim="data.str_wo" clearable style="width: 210px;"></el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="SM:" folding>
        <template #default="{data}">
          <el-input v-model.trim="data.str_sm" clearable style="width: 210px;"></el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="PN:" folding>
        <template #default="{data}">
          <el-input v-model.trim="data.str_pn" clearable style="width: 210px;"></el-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="WIP:" folding>
        <template #default="{data}">
          <el-select v-model.trim="data.is_wip" clearable style="width: 210px;">
            <el-option label="All" value="all"></el-option>
            <el-option label="Yes" value="1"></el-option>
            <el-option label="No" value="0"></el-option>
          </el-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="F1-2 Closed:" folding>
        <template #default="{data}">
          <el-select v-model.trim="data.int_f12close" clearable style="width: 210px;">
            <el-option label="All" value="all"></el-option>
            <el-option label="Yes" value="1"></el-option>
            <el-option label="No" value="0"></el-option>
          </el-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Engine Type:" folding>
        <template #default="{data}">
          <el-select v-model.trim="data.id_engine_type" clearable style="width: 210px;">
            <el-option label="All" value="all"></el-option>
            <el-option label="CFM56" value="cfm56"></el-option>
            <el-option label="LEAP" value="leap"></el-option>
          </el-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="Status:" folding>
        <template #default="{data}">
          <el-select v-model.trim="data.is_abnormal" clearable style="width: 210px;">
            <el-option label="All" value="all"></el-option>
            <el-option label="正常" value="0"></el-option>
            <el-option label="异常" value="1"></el-option>
          </el-select>
        </template>
      </vxe-form-item>

      <vxe-form-item title="AOG:" folding>
      <template #default="{data}">
        <el-select v-model.trim="data.is_aog" clearable style="width: 210px;">
          <el-option label="Yes" value="1"></el-option>
          <el-option label="No" value="0"></el-option>
        </el-select>
      </template>
    </vxe-form-item>
      <slot> </slot>
      <vxe-form-item align="center" collapse-node>
        <el-button type="primary" circle @click="handleSearchClick">
          <template #icon>
            <el-icon>
              <search></search>
            </el-icon>
          </template>
        </el-button>
        <el-button type="info" circle @click="handleRefreshClick">
          <template #icon>
            <el-icon>
              <Refresh></Refresh>
            </el-icon>
          </template>
        </el-button>
      </vxe-form-item>
    </vxe-form>
  `,
}
