const { useVModel } = VueUse
const { ref, onMounted } = Vue
import { queryTeamPlanSeeList } from '../../../api/teams/index.js'
export default {
  name: 'TeamPlanSee',
  props: {
    planItem: Object,
    visible: <PERSON>ole<PERSON>,
  },
  setup(props) {
    const teamPlanSeeVisible = useVModel(props, 'visible')
    const teamPlanSeeList = ref([])
    const num = ref(0)

    const feedbackItem = ref(null)
    const getTeamPlanSeeList = async () => {
      const res = await queryTeamPlanSeeList(props.planItem.planId)
      feedbackItem.value = res.pt_team_plan_dtosee.pt_main.str_content
      teamPlanSeeList.value = res.pt_team_plan_dtosee.pt_team_plans ?? []
      num.value = teamPlanSeeList.value.length
    }

    // 转化数组为字符串
    const convertArrayToString = (array) => {
      return array?.join(',')
    }
    // 关闭弹窗
    const handleClose = () => {
      teamPlanSeeVisible.value = false
    }

    onMounted(() => {
      getTeamPlanSeeList()
    })
    return {
      teamPlanSeeVisible,
      teamPlanSeeList,
      convertArrayToString,
      num,
      handleClose,
      feedbackItem
    }
  },
  template: /*html*/ `
    <el-dialog v-model="teamPlanSeeVisible" title="Team Plan See" width="50%" class="common-dialog">
      <div class="mb-2">
        <el-badge :value="num" class="item" type="info">
          <el-button>Num</el-button>
        </el-badge>
      </div>
      <div class="mb-2" v-for="item in teamPlanSeeList" :key="item.pt_team_plan.id">
        <el-card>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="Task">{{ item.pt_team_plan.str_task }}</el-descriptions-item>
            <el-descriptions-item label="Task Name">{{ item.pt_team_plan.str_task_name }}</el-descriptions-item>
            <el-descriptions-item label="Model">{{ item.pt_team_plan.str_model }}</el-descriptions-item>
            <el-descriptions-item label="Assembly task">{{ item.pt_team_plan.assembly_task_name }}</el-descriptions-item>
            <el-descriptions-item label="Site">{{ item.pt_team_plan.site_name }}</el-descriptions-item>
            <el-descriptions-item label="Team">{{ convertArrayToString(item.pt_team_plan.str_team_staffs) }}</el-descriptions-item>
            <el-descriptions-item label="Team Sec">{{ convertArrayToString(item.pt_team_plan.str_team_sec_staffs) }}</el-descriptions-item>
            <el-descriptions-item label="Shift">{{ item.pt_team_plan.str_shift }}</el-descriptions-item>
            <el-descriptions-item label="Shift Time">{{ convertArrayToString(item.pt_team_plan.str_shift_time) }}</el-descriptions-item>
            <el-descriptions-item label="Delay Shift">{{ item.pt_team_plan.str_delay_shift }}</el-descriptions-item>
            <el-descriptions-item label="Delay Shift Time">{{ convertArrayToString(item.pt_team_plan.str_delay_shift_times) }}</el-descriptions-item>
            <el-descriptions-item label="Remark">{{ item.pt_team_plan.str_remark }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
      <div>
        <el-card header="feedback">
          <div>{{ feedbackItem }}</div>
        </el-card>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleClose">Cancel</el-button>
      </template>
    </el-dialog>
  `,
}
