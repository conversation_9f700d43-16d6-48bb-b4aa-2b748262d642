import { post } from '../../../utils/request.js'

/**
 * 获取业务类型枚举
 * @returns {Promise} 业务类型枚举数据
 *
 * 返回格式：
 * [
 *   {
 *     "str_key": "F1-2交接",
 *     "str_value": "101"
 *   },
 *   {
 *     "str_key": "F2交接",
 *     "str_value": "102"
 *   },
 *   ...
 * ]
 */
export function getBussinessTypes() {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_enum',
    str_key: 'handover_type',
  })
}

/**
 * 获取交接汇总统计数据
 * @param {string} businessType - 业务类型代码
 * @returns {Promise} 交接汇总统计数据
 *
 * 返回格式：
 * {
 *   "pendingNum": 0,
 *   "unCommitNum": 0,
 *   "unReceiveNum": 0
 * }
 */
export function getHandoverSummary(businessType) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_handover_report',
    strType: businessType,
  })
}

/**
 * 获取交接详情数据（钻取功能）
 * @param {Object} params - 请求参数
 * @param {string} params.businessType - 业务类型代码
 * @param {string} params.status - 状态类型 (pending/unsubmitted/unreceived)
 * @returns {Promise} 详情数据
 * 
 * 返回格式：
{
    "code": "success",
    "text": "success",
    "data": [
        {
            "str_wo": "E20200408",
            "str_esn": "894380",
            "str_sm": "SM56",
            "percentage": "0%",
            "str_task": null,
            "is_pending": 1,
            "dt_pt": "2025-05-22",
            "id_wo": "1455190865592979456",
            "id_model": "1435501857916719105",
            "id_task": "1893982385466249216",
            "id_shift": "1892466580571439105",
            "str_handover_type": "106",
            "str_task_type": null,
            "str_commit": "张勇",
            "str_receive": null,
            "str_group": "LPT",
            "str_shift": "早班",
            "dt_commit": "2025-05-22 18:37:38",
            "dt_receive": null,
            "int_status": 0.0
        }
    ]
}
 */
export function getHandoverDetail(params) {
  // 检查是否有测试数据
  if (window.testData) {
    return Promise.resolve({
      code: 'success',
      text: 'success',
      data: window.testData
    })
  }
  
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_handover_report_list',
    strType: params.businessType,
    numType: params.status,
  })
}
