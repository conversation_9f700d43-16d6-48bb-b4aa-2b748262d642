/**
 * @description: 可缺件抽屉组件
 * @author: rik_tang
 */
import { post } from '../../../../config/axios/httpReuest.js'
// 引入公共表格组件
import HtVxeTable from '../../../../components/VxeTable/HtVxeTable.js'
import ReasonRemarkDialog from '../../components/ReasonRemarkDialog.js'
import ComponentReplacementPlan from './ComponentReplacement/ComponentReplacementPlan.js'
import { useReasonRemark } from '../../hooks/useReasonRemark.js'
import { checkSelectedData, getSelectedData, checkSelectedOneData } from '../utils/utils.js'
const { useVModel } = VueUse
const { ref, reactive, onMounted, toRefs } = Vue
const EngineTopRightDrawer = {
  components: {
    HtVxeTable,
    ReasonRemarkDialog,
    ComponentReplacementPlan,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  },
  emits: ['update:visible', 'sumbit'],
  setup(props, { emit }) {
    const visible = useVModel(props, 'visible', emit)

    const tableRef = ref(null)
    const state = reactive({
      data: [],
      columns: [
        {
          title: 'Kitting完成/站点',
          field: 'str_nodename',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '保证人',
          field: 'str_delivery_user',
          minWidth: 100,
          visible: props.type == 60,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '保证时间',
          field: 'dt_delivery',
          minWidth: 100,
          visible: props.type == 60,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '目标WO',
          field: 'str_code',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '目标ESN',
          field: 'str_esn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原台',
          field: 'str_wo_code_ori',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        // {
        //   title: '原台 ESN',
        //   field: 'str_wo_esn_ori',
        //   minWidth: 100,
        //   filters: [{ data: '' }],
        //   filterRender: { name: 'FilterInput' },
        // },
        {
          title: 'PN',
          field: 'str_pn',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件标签',
          field: 'str_label',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件名称',
          field: 'str_part_name',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'SM',
          field: 'str_sm',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '客户',
          field: 'str_client',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '零件来源',
          field: 'str_class',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'PKP',
          field: 'id_pkp',
          minWidth: 150,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '机型',
          field: 'str_engine_type',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '数量',
          field: 'int_qty',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: 'EKD',
          field: 'dt_ekd',
          minWidth: 100,
          filters: [{ data: '' }],
          filterRender: { name: 'FilterInput' },
        },
        {
          title: '原因备注',
          field: 'str_reason',
          minWidth: 200,
          fixed: 'left',
        },
      ],
      total: 0,
    })
    // * 获取数据
    const getData = async () => {
      const params = {
        ac: 'de_getmarkedpnlist',
        id_wo: props.id,
        int_point_type: props.type,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        state.data = data.data
        state.total = data.data.length
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    // 请求接口
    const requestApi = async (id, markType, list, opType) => {
      const params = {
        ac: 'de_markpending',
        id_wo: id,
        int_mark_type: markType,
        int_op_type: opType,
        idlist: list,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        ElementPlus.ElMessage.success(data.text)
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    // * 表格筛选
    const handleFilterChange = (data) => {
      tableState.total = tableRef.value.getCurrentLength()
    }
    /**
     * 保证交付移除
     * @param {Array} selectedData
     * @returns
     */
    const removeGuaranteedDelivery = (selectedData) => {
      const pkpIds = selectedData.map((_it) => _it.id_pkp)

      const params = {
        ac: 'de_pndeliverysave',
        int_type: '0',
        id_pkp: pkpIds,
      }

      ElementPlus.ElMessageBox.confirm('是否确认移除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        post(params).then((res) => {
          if (res.data.code === 'success') {
            ElementPlus.ElMessage({
              type: 'success',
              message: '取消保证交付成功',
            })
            emit('sumbit', props.id)
          }
        })
      })
    }

    // * 移除
    const handleRemove = () => {
      const { type } = props
      const selectedData = getSelectedData(tableRef)
      if (!checkSelectedData(selectedData)) {
        ElementPlus.ElMessage.warning('请选择要移除的数据')
        return
      }
      if (type === '60') {
        removeGuaranteedDelivery(selectedData)
        return
      }
      const ids = selectedData.map((item) => item.id)
      ElementPlus.ElMessageBox.confirm('是否确认移除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        requestApi(props.id, props.type, ids, 1).then(() => {
          emit('sumbit', props.id)
        })
      })
    }
    // * 导出表格数据
    const exportTableData = () => {
      tableRef.value.exportData()
    }
    const { reasonRemark, openReasonRemark, saveReasonRemark, closeReasonRemark } = useReasonRemark(tableRef)

    const componentReplacementPlanRef = ref(null)
    /**
     * 打开串件方案弹框
     */
    const handleComponentReplacementPlan = () => {
      const selectedData = getSelectedData(tableRef)
      if (!checkSelectedData(selectedData)) {
        ElementPlus.ElMessage.warning('请选择要创建方案的数据')
        return
      }
      if (!checkSelectedOneData(selectedData)) {
        return
      }
      componentReplacementPlanRef.value.openDialog(selectedData)
    }
    /* ---------------------- 跳转 ---------------------- */
    const jumpMap = {
      a: '集件零件管理',
      b: '用料申请',
      c: '零件报表',
      d: '转包零件跟踪',
      e: '锁库处理',
      g: '零件历程',
      h: '采购零件跟踪',
    }
    const jumpMapId = {
      a: '1435766996880461825',
      b: '1453234319950618624',
      c: '1435766981386702849',
      d: '1435766992040235009',
      e: '1453282700483895296',
      g: '1826524987646808064',
      h: '1435766977628606465',
    }
    const idKeyMap = {
      a: 'id_pkp',
      b: 'id_apply',
      c: 'id_offlog',
      d: 'id_po_sub_sc',
      e: 'id_apply',
      g: 'id_pkp',
      h: 'id_po_sub',
    }
    const handleJump = (command) => {
      // 没有选中数据
      if (!tableRef.value.getSelectedData().length) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }
      const idList =
        tableRef.value
          .getSelectedData()
          .map((item) => item[idKeyMap[command]])
          .filter((item) => item !== null)
          .join(',') ?? ''
      if (idList === '') {
        ElementPlus.ElMessage.warning(`无${jumpMap[command]}信息`)
        return
      }
      const getUrl = (command, idList) => {
        const baseUrl = `/Page/?moduleid=${jumpMapId[command]}`
        return command === 'e' ? `${baseUrl}&qrc_id_main=${idList}` : `${baseUrl}&qrc_id=${idList}`
      }

      com.refreshTab('*' + jumpMap[command], getUrl(command, idList))
    }
    onMounted(() => {
      getData()
    })

    return {
      visible,
      tableRef,
      ...toRefs(state),
      handleFilterChange,
      handleRemove,
      exportTableData,
      reasonRemark,
      openReasonRemark,
      saveReasonRemark,
      closeReasonRemark,
      handleComponentReplacementPlan,
      componentReplacementPlanRef,
      handleJump,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="80%" :show-close="false">
      <template #title>
        <div class="flex items-center justify-between">
          <div class="text-white">{{ title }}零件清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-button type="info" @click="handleRemove">移除</el-button>
            <el-button type="primary" @click="exportTableData">导出</el-button>
            <el-button type="primary" @click="openReasonRemark(0)">原因备注</el-button>
            <el-button v-if="type == 50" type="primary" @click="handleComponentReplacementPlan">串件方案</el-button>
            <el-dropdown class="ml-4" @command="handleJump">
            <el-button type="primary">
              跳转
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="a">集件零件管理（On-log）</el-dropdown-item>
                <el-dropdown-item command="b">用料申请</el-dropdown-item>
                <el-dropdown-item command="c">零件报表（Off-log）</el-dropdown-item>
                <el-dropdown-item command="d">转包跟踪</el-dropdown-item>
                <el-dropdown-item command="e">锁库处理</el-dropdown-item>
                <el-dropdown-item command="g">零件历程</el-dropdown-item>
                <el-dropdown-item command="h">采购_厂家交付</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          </div>
          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable
          ref="tableRef"
          :tableData="data"
          :tableColumns="columns"
          :isShowHeaderCheckbox="true"
          @filterChange="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
    <!-- 原因备注 -->
    <ReasonRemarkDialog
      v-model="reasonRemark.visible"
      :form="reasonRemark.form"
      @saveReasonRemark="saveReasonRemark"
      @closeReasonRemark="closeReasonRemark"
    />

    <!-- 串件方案 -->
    <ComponentReplacementPlan ref="componentReplacementPlanRef" :id-wo="id" />
  `,
}

export default EngineTopRightDrawer
