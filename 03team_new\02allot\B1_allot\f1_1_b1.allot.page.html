<!-- StringBuilder strhtml = new StringBuilder(); strhtml.Append(@"  -->
<!DOCTYPE html>
<html lang='en'>

<head>
    <meta charset='UTF-8'>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title> F1-1_B1发动机分配列表</title>
    <script src='../../../assets/jquery/jquery.js'></script>
    <script src='../../../assets/vue/vue.js'></script>
    <script src='../../../assets/element/index.js'></script>
    <script src='../../../assets/axios/axios.min.js'></script>
    <script src='../../02allot/components/common.edit.page.js'></script>
    <script src='../../02allot/components/common.page.js'></script>
    <script src='../../../03team_new/comm/api_environment.js'></script>

    <link rel='stylesheet' href='../../../assets/element/index.css'>
    <link rel='stylesheet' href='../../../assets/css/el.dialog.css'>
    <link rel='stylesheet' href='../../../assets/css/offer.page.css'>
    <link rel='stylesheet' href='../../../assets/css/table.self.css'>
    <link rel='stylesheet' href='../../../assets/css/allot.page.css'>

</head>



<body>
<div id='app'>
    <y-pt-allot-page input_page_type='F1-1' input_group_type='B1'></y-pt-allot-page>
</div>

<script>
  var vue1 = new Vue({
    el: '#app',
    data: function() {
      return {
        id_main: ''
      }
    },
    methods: {
      backCall() {

      }
    }
  })
</script>
</body>


</html>
<!-- "); arg.redata = strhtml.ToString(); return arg.redata; -->
