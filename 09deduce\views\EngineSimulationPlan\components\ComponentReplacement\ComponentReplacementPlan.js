/**
 * 串件方案弹框
 */
import { componentReplacementPlanColumns } from '../../config/tableColumnsConfig.js'
import { getSelectedData } from '../../utils/utils.js'
import { post } from '../../../../../config/axios/httpReuest.js'
import HtVxeTable from '../../../../../components/VxeTable/HtVxeTable.js'
export default {
  name: 'ComponentReplacementPlan',
  components: {
    HtVxeTable,
  },
  props: {
    idWo: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const { ElLoading, ElMessage } = ElementPlus
    const { ref } = Vue
    const visible = ref(false)
    /**
     * 打开弹框
     */
    const openDialog = (data) => {
      visible.value = true
      const { str_pn } = data[0]
      getTableData(str_pn)
    }

    const tableRef = ref(null)
    const tableData = ref([])
    const tableColumns = ref(componentReplacementPlanColumns)
    const total = ref(0)

    /**
     * 获取表格数据
     * @param {string} str_pn
     */
    const getTableData = async (str_pn) => {
      const { idWo } = props
      const params = {
        ac: 'de_query_resource_plan',
        id_wo: idWo,
        str_pn,
        is_all: 1,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        tableData.value = data.data
        total.value = data.data.length
      } else {
        ElMessage.error(data.text)
      }
    }

    /**
     * @description 根据id_pkp进行分组
     * @param {Array} data
     * @returns {Array}
     */
    const recombineArray = (data) => {
      const result = Object.values(
        data.reduce((acc, item) => {
          if (!acc[item.id_pkp]) {
            acc[item.id_pkp] = []
          }
          acc[item.id_pkp].push(item)
          return acc
        }, {}),
      )
      return result.map((item) => {
        return {
          id_se_pkp_rec: item[0].id_pkp,
          dbl_num_need: item[0].int_num,
          matcher_se_main_donors: item.map((i) => {
            return {
              id_se_pkp: sourceMap[i.str_source_type] === 1 ? i.id_pkp_sp : null,
              id_fee_po_sub: null,
              id_stock_list_site: sourceMap[i.str_source_type] === 0 ? i.id_pkp_sp : null,
              dbl_num: i.int_num_inuse,
              int_source_type: sourceMap[i.str_source_type],
            }
          }),
        }
      })
    }
    /**
     * 发送串件
     */
    const submitReplacementPlanToServer = async () => {
      const loading = ElLoading.service({
        text: '处理中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const selectedData = getSelectedData(tableRef)
      const request = {
        id_wo: props.idWo,
        matcher_se_main_receptors: recombineArray(selectedData),
      }

      const params = {
        ac: 'se_pda_to_plan',
        request,
      }
      //return;
      const { data } = await post(params)
      loading.close()
      if (data.code === 'success') {
        changeStatusBySelectedData(props.idWo, selectedData)
      } else {
        ElMessage.error(data.text)
      }
    }
    /**
     * @description  发送串件后标记状态
     * @param {String} idWo
     * @param {Array} selectedData
     */
    const changeStatusBySelectedData = async (idWo, selectedData) => {
      const { str_pn } = selectedData[0]
      const pkps = selectedData.map((item) => item.id_pkp)
      const params = {
        ac: 'de_markexchange',
        id_wo: idWo,
        idlist: pkps,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        // 更新表格数据
        await getTableData(str_pn)
        ElMessage.success(data.text)
      } else {
        ElMessage.error(data.text)
      }
    }

    return {
      visible,
      tableData,
      tableColumns,
      total,
      openDialog,
      submitReplacementPlanToServer,
    }
  },
  template: /*html*/ `
    <el-drawer v-model="visible" class="my_drawer" size="80%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex items-center justify-between">
          <div class="text-white">串件资源方案</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div class="flex items-center justify-between">
        <div class="flex gap-2">
          <el-button type="primary" @click="submitReplacementPlanToServer">发送串件</el-button>
        </div>
        <div class="text-black">共计：{{ total || 0 }} 条</div>
      </div>
      <div style="height: calc(100% - 50px)">
        <HtVxeTable
          ref="tableRef"
          :tableData="tableData"
          :tableColumns="tableColumns"
        ></HtVxeTable>
      </div>
    </el-drawer>
  `,
}
