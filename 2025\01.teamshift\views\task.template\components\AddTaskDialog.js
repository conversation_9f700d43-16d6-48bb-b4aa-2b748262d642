import { queryTaskList } from '../../../api/task/index.js'
import { cancelTask } from '../../../api/calendar/index.js'

const { defineComponent, ref, onMounted } = Vue
const { useVModel } = VueUse

export const AddTaskDialog = defineComponent({
  name: 'AddTaskDialog',
  props: {
    modelValue: { type: Boolean, default: false },
    currentAddRow: { type: Object, default: null },
    idWo: { type: String, default: '' },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    // 状态管理
    const addDialogVisible = useVModel(props, 'modelValue', emit)
    const taskList = ref([])
    const dialogForm = ref({
      id_task: '',
      int_sub: 0,
      int_sort: 0,
      int_task_type: 0,
      str_task_name: '',
      int_type:1,// 默认新增自定义任务
    })
    
    // 业务逻辑
    const getTaskList = async () => {
      const res = await queryTaskList(props.currentAddRow.id)
      taskList.value = res
    }

    const handleTaskChange = (value) => {
      const selectedTask = taskList.value.find((item) => item.id === value)
      dialogForm.value.int_task_type = selectedTask.int_type
      dialogForm.value.str_task_name = selectedTask.str_task
    }

    const handleAddTask = async () => {
      const params = {
        id_wo: props.idWo,
        id_parent_task: props.currentAddRow.id,
        id_task: dialogForm.value.id_task,
        str_task_name: dialogForm.value.str_task_name,
        int_sub: dialogForm.value.int_sub,
        int_sort: dialogForm.value.int_sort,
        int_task_type: dialogForm.value.int_task_type,
        int_type:dialogForm.value.int_type
      }
      await cancelTask(params)
      // 注意：此处需要访问父组件的getEditData方法
      // 这里有个问题：getEditData函数是在父组件中定义的，需要通过provide/inject或props传递
      // 在实际使用中可能需要emit一个事件让父组件去调用getEditData
      emit('task-added')
      addDialogVisible.value = false
    }

    onMounted(() => {
      getTaskList()
    })

    return {
      addDialogVisible,
      taskList,
      dialogForm,
      handleAddTask,
      handleTaskChange,
    }
  },
  template: /*html*/ `
    <el-dialog class="common-dialog" v-model="addDialogVisible" title="添加任务" size="80%">
      <div class="p-4">
        <el-form :model="dialogForm" label-width="120px">
          <el-form-item label="任务名称">
            <el-select v-model="dialogForm.id_task" placeholder="请选择当前单元体下【自定义】任务" @change="handleTaskChange">
              <el-option v-for="item in taskList" :key="item.id" :label="item.str_task" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="SUB数量">
            <el-input-number v-model="dialogForm.int_sub" :min="0" placeholder="请输入SUB数量" />
          </el-form-item>
          <el-form-item label="排序">
            <el-input-number v-model="dialogForm.int_sort" :min="1" placeholder="请输入排序" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleAddTask">保存</el-button>
        <el-button type="danger" @click="addDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  `,
}) 