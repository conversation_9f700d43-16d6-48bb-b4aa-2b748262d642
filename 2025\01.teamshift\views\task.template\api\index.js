import { post } from '../../../utils/request.js'

/**
 * 获取SM配置数据
 * @returns {Promise} SM配置数据
 */
export const getSmConfigData = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_sm_config_data',
   ...params,
  })
}

/**
 * 根据SM保存引擎任务
 * @param {Object} params - 参数对象
 * @param {string} params.id_wo - 工单ID
 * @param {string} params.str_flow - 流程
 * @param {string} params.str_sm - SM标识
 * @returns {Promise} 保存结果
 */
export const saveEngineTaskBySm = (params) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_save_engine_task_by_sm',
    ...params,
  })
}
