const { ref, reactive, onMounted } = Vue
import { getStationGantt } from '../../api/plan.manage.js'
import { useTransform } from './useTransform.js'
import DynamicColumnConfigurator from '../../components/DynamicColumnConfigurator.js'
import { useDynamicColumn } from '../../composables/useDynamicColumn.js'
// 占位甘特图
const StationPlanGantt = {
  components: {
    DynamicColumnConfigurator,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
    isShowBottomBtn: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const { transformSubPlansToGanttData, transformGanttDataToSubPlans } = useTransform()
    const gantt = Gantt.getGanttInstance()
    /* ----------头部搜索---------- */
    const formSearch = reactive({
      str_node: '',
    })
    // 搜索
    const search = async () => {
      gantt.clearAll()
      await getGanttData()
      gantt.parse(ganttData.value)
      expandGantt()
      gantt.render()
    }
    const isCollapse = ref(true)
    // 折叠甘特图
    const collapseGantt = () => {
      isCollapse.value = true
      // 使用批量操作优化性能
      gantt.batchUpdate(() => {
        // 只折叠第一层，保持数据结构
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.close(taskId)
          }
        })
      })
    }
    // 展开甘特图
    const expandGantt = () => {
      isCollapse.value = false
      // 使用批量操作来优化性能
      gantt.batchUpdate(() => {
        const rootTasks = gantt.getChildren(0)
        rootTasks.forEach((taskId) => {
          if (gantt.isTaskExists(taskId)) {
            // 确保任务存在
            gantt.open(taskId)
          }
        })
      })
    }
    const isCollapseGrid = ref(false)
    // 收起grid
    const collapseGrid = () => {
      isCollapseGrid.value = true
      gantt.config.show_grid = false
      gantt.render()
    }
    // 展开grid
    const expandGrid = () => {
      isCollapseGrid.value = false
      gantt.config.show_grid = true
      gantt.render()
    }

    /* ----------头部搜索---------- */
    const ganttData = ref({
      data: [],
      links: [],
    })
    // 获取甘特图数据
    const getGanttData = async () => {
      const data = await getStationGantt(
        props.id,
        formSearch.str_node,
        formSearch.dt_start,
        formSearch.dt_end,
        formSearch.str_engine_type,
      )
      if (data) {
        // 添加层级标记，用于控制初始加载深度
        ganttData.value.data = _.map(data, (task) => ({
          ...transformSubPlansToGanttData(task),
          $has_child: task.children?.length > 0,
          $level: task.level || 1,
        }))
      }
    }
    // 获取甘特图数据中最小的开始时间和最大的结束时间
    const getGanttDataMinMaxTime = () => {
      const minTime = _.minBy(ganttData.value.data, 'start_date')?.start_date
      // 获取当前月份的前一天
      const preDayOfMonth = moment(minTime).subtract(1, 'days').format('YYYY-MM-DD')
      const maxTime = _.maxBy(ganttData.value.data, 'end_date')?.end_date
      // 获取最大日期的年
      const maxYear = moment(maxTime).year()
      // 获取当前年的下一年的第一天
      const lastDayOfYear = moment(`${maxYear + 1}-01-01`).format('YYYY-MM-DD')
      return { preDayOfMonth, lastDayOfYear }
    }

    const GANTT_COLUMNS = [
      { name: 'text', label: '站位编码/任务', width: '*', tree: true },
      { name: 'esn', label: 'ESN', width: 100 },
      { name: 'str_engine_type', label: '机型', width: 100 },
      { name: 'start_date', label: 'Start time', width: 100 },
      { name: 'dt_end', label: 'End time', width: 100 },
      { name: 'duration', label: 'Duration', width: 60 },
      { name: 'dt_f2_3_end', label: 'F2/3结束时间', width: 100 },
    ]

    const ganttInit = () => {
      gantt.config.grid_width = 600
      // 设置时间格式
      gantt.config.date_format = '%Y-%m-%d'
      // 设置起始时间
      gantt.config.start_date = getGanttDataMinMaxTime().preDayOfMonth
      // 设置结束时间
      gantt.config.end_date = getGanttDataMinMaxTime().lastDayOfYear
      // 设置gantt列
      gantt.config.columns = GANTT_COLUMNS
      // 禁用progress
      gantt.config.show_progress = false
      // 禁用链接线
      gantt.config.show_links = false
      // task样式
      gantt.templates.task_class = function (start, end, task) {
        return `station-level-${task.level}`
      }
      // 插件
      gantt.plugins({
        grid: true,
        tooltip: true,
        auto_scheduling: true,
      })
      // 配置动态加载
      gantt.config.branch_loading = true
      gantt.config.show_loading = true
      gantt.config.load_level = 2 // 初始只加载2层

      // 配置延迟渲染
      gantt.config.smart_rendering = true
      gantt.config.smart_scales = true
    }

    gantt.templates.tooltip_text = function (start, end, task) {
      return '<b>Task:</b> ' + task.text + '<br><b>Start:</b> ' + task.dt_start + '<br><b>End:</b> ' + task.dt_end
    }

    const dynamicColumn = useDynamicColumn(gantt, GANTT_COLUMNS)

    // 清除gantt图toolTip
    const clearGanttTooltip = () => {
      gantt.ext.tooltips.tooltip.hide()
      gantt.clearAll()
    }

    onMounted(async () => {
      gantt.clearAll()
      await getGanttData()
      ganttInit()
      gantt.init('gantt_station')
      gantt.parse(ganttData.value)
      // 将滚动条的位置设置到最左边
      gantt.scrollTo(0)
    })
    return {
      ganttData,
      getGanttData,
      formSearch,
      search,
      isCollapse,
      collapseGantt,
      expandGantt,
      isCollapseGrid,
      collapseGrid,
      expandGrid,
      clearGanttTooltip,
      ...dynamicColumn,
    }
  },
  template: /*html*/ `
    <div class="ml-2 mt-2">
      <el-form :model="formSearch" inline size="default">
        <el-form-item label="任务名称">
          <el-input style="width:220px" v-model="formSearch.str_node" placeholder="请输入任务名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            v-model="formSearch.dt_start"
            type="date"
            placeholder="请选择开始时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            v-model="formSearch.dt_end"
            type="date"
            placeholder="请选择结束时间"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </el-form-item>
        <el-form-item class="w-[220px]" label="机型">
          <el-select v-model="formSearch.str_engine_type" placeholder="请选择机型" clearable>
            <el-option label="CFM56" value="CFM56"></el-option>
            <el-option label="LEAP" value="LEAP"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="search">搜索</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button v-if="!isCollapse" type="primary" @click="collapseGantt">折叠甘特图</el-button>
          <el-button v-else type="primary" @click="expandGantt">展开甘特图</el-button>
        </el-form-item>
        <el-form-item label=" ">
          <el-button v-if="!isCollapseGrid" type="primary" @click="collapseGrid">收起grid</el-button>
          <el-button v-else type="primary" @click="expandGrid">展开grid</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex justify-end gap-2 mb-2">
      <DynamicColumnConfigurator
        v-model="visibleColumns"
        :all-columns="columnConfig"
        button-text="列配置"
        button-icon="Grid"
      />
    </div>

    <div id="gantt_station" class="mx-2" :class="[isShowBottomBtn ? 'h-[85vh]' : 'h-[75vh]']"></div>
  <!--  <div v-if="isShowBottomBtn" class="m-2 flex items-center justify-end">
      <el-button type="success" @click="savePlanGantt">正式应用</el-button>
      <el-button type="primary" @click="savePlanGantt">保存</el-button>
      <el-button type="info" @click="closePlanGantt">取消</el-button>
    </div>-->
  `,
}
export default StationPlanGantt
