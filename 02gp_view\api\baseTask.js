// 任务基础数据查询
import { post } from '../../config/axios/httpReuest.js';

export const queryBaseTask = async (queryLists = []) => {
  const list = [];
  const params = {
    ac: 'gp_task_info_search',
    queryLists,
  };
  const { data } = await post(params);
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text);
    return [];
  }
  list.push(...data.data);
  return list;
};
// 根据任务ID查询任务详情
export const queryBaseTaskById = async (id) => {
  const param = {
    ac: 'gp_task_info_search_by_id',
    id,
  };
  const { data } = await post(param);
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text);
    return null;
  }
  return data.data;
};
/**
 * @description 查询发动机
 * @param {Array} queryLists 查询条件
 * @param {String} type 模板类型
 * @returns {Promise<Array>} 发动机列表
 */
export const queryEngine = async (type, queryLists = []) => {
  let list = [];
  const params = {
    ac: 'gp_search_wo',
    str_type: type,
    queryLists,
  };
  const { data } = await post(params);
  if (data.code === 'error') {
    ElementPlus.ElMessage.error(data.text);
    return [];
  }
  list.push(...data.data);
  return list;
};
