let idWoS = [];
let currentIndex = 0;
let currentIdWo = "";
let currentFlow = "F0" // 当前Flow

// 兼容性日期显示
window.onload = function () {

  $('body').append(template);
  document.getElementById('workstationName').innerHTML = stationName;
  let date = new Date();
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  document.getElementById('currentDate').innerHTML = year + '-' + month + '-' + day;

  // // 获取工作站信息
  // let workStation = document.querySelector('.workstation-info div:first-child').textContent;
  // let currentStation = workStation.split(': ')[1]; // 获取 MM03

  // 工作站与流程步骤的映射关系


  // 找到当前流程步骤并添加高亮样式

  getData();// 当前占位 idwo
  getEngineInfo();// 当前占位 idwo

  // 页面加载完成后立即更新高亮显示
  updateFlowHighlight();

  setInterval(getEngineInfo, 1000 * 60 * 5); // 5分钟获取一次10 播放下一台，多台时使用

}

// 更新流程高亮显示的函数
function updateFlowHighlight() {
  console.log('当前Flow:', currentFlow); // 调试信息

  // 先移除所有flow步骤的高亮状态
  var flowSteps = document.querySelectorAll('.flow-step');
  flowSteps.forEach(function (step) {
    step.classList.remove('current');
  });

  // 然后根据当前flow添加高亮
  if (currentFlow) {
    flowSteps.forEach(function (step) {
      // 检查flow-step-content的内容
      var flowContent = step.querySelector('.flow-step-content');
      if (flowContent) {
        var stepText = flowContent.textContent.trim();
        console.log('检查步骤:', stepText, '是否等于:', currentFlow); // 调试信息
        if (stepText === currentFlow) {
          step.classList.add('current');
          console.log('添加高亮到:', stepText); // 调试信息
        }
      }
    });
  }
}
/*** 获取占位下的发动机接口数据*/
function getData() {
  $.ajax({
    async: false,
    type: "POST",
    url: apiEnvironment + "/api/Do/DoAPI",
    data: {
      ac: "pt_get_wo_by_station",
      ak: "",
      ap: "",
      au: "ssamc",
      station: stationName
    },
    dataType: "json",
    success: function (rsp) {
      if (rsp.code == "success" && rsp.data && rsp.data.str_id_wo) {
        idWoS = rsp.data.str_id_wo.split(',');
      }
    }
  });
}

/**获取发动机信息*/
function getEngineInfo() {
  if (currentIndex < idWoS.length) {
    currentIdWo = idWoS[currentIndex]// 获取当前 id wo
    currentIndex++
  } else {
    currentIndex = 0
  }
  $.ajax({
    async: false,
    type: "POST",
    url: apiEnvironment + "/api/Do/DoAPI",
    data: {
      ac: "pt_team_work_engine",
      ak: "",
      ap: "",
      au: "ssamc",
      id_wo: currentIdWo
    },
    dataType: "json",
    success: function (rsp) {
      if (rsp.code == "success" && rsp.data && rsp.data.length > 0) {
        let result = rsp.data[0];
        $("#str_esn").text(result.esninfo[0].str_code.split('/')[1]);
        $("#str_customer").text(result.esninfo[0].customer);
        $("#str_engint_type").text(result.esninfo[0].engint_type);
        $("#dt_release_close").text(result.esninfo[0].dt_release_close);
        $("#dt_release_update").text(result.esninfo[0].dt_release_update);
        $("#real_time_tat").text(result.esninfo[0].f4sjtat);
        $("#expecttst_tat").text(result.esninfo[0].f4plantat);
        $('#customer_png').attr('src', result.esninfo[0].customer_png || "/Content/assets_huatek/webapp/03team_new/views/report/default.png");
        currentFlow = result.esninfo[0] && result.esninfo[0].str_flow;

        $("#dt_f0").text(result.esninfo[0].f0data);
        $("#dt_f1b1").text(result.esninfo[0].f1b1data);
        $("#dt_f1b23").text(result.esninfo[0].f1b23data);
        $("#dt_f12").text(result.esninfo[0].f12data);

        // $("#dt_f2").text(result.esninfo[0].f2data);
        $("#dt_f3").text(result.esninfo[0].f3data);
        $("#dt_f4b23").text(result.esninfo[0].f4b23data);
        $("#dt_f4b1").text(result.esninfo[0].f4b1data);
        $("#dt_f42").text(result.esninfo[0].f42data);
        $("#dt_f43").text(result.esninfo[0].f43data);

        // 更新流程高亮显示
        updateFlowHighlight();

        getTask();// 加载班组任务
      }
    }
  });
}

/** 获取班组任务*/
function getTask() {

  // 假设你的表格有一个ID，例如 'myTable'
  let table = document.getElementById('myTable');
  let rows = table.getElementsByTagName("tr");
  // 循环遍历并删除除了第一行（通常是表头）之外的所有行
  for (var i = rows.length - 1; i > 0; i--) {
    table.deleteRow(i);
  }

  $.ajax({
    async: false,
    type: "POST",
    url: apiEnvironment + "/api/Do/DoAPI",
    data: {
      ac: "pt_team_task",
      ak: "",
      ap: "",
      au: "ssamc",
      id_wo: currentIdWo,
      station: stationName
    },
    dataType: "json",
    success: function (rsp) {
      if (rsp.code == "success" && rsp.data && rsp.data.tasks) {
        let taskList = rsp.data.tasks;
        if (rsp.data.teamdata.length > 0) {
          $("#teamNumber").text(rsp.data.teamdata[0].teams[0].team_name);
          $("#teamLeader").text(rsp.data.teamdata[0].teams[0].team_leader);
        }


        for (let index = 0; index < taskList.length; index++) {
          let css_tr = ""
          if (index % 2 !== 0) {
            css_tr = " background-color: #f9f9f9; "
          } else {
            css_tr = " background-color: #D2DDEF;"
          }
          let newRow = `<tr style="${css_tr}">
          <td>${index + 1}</td>
          <td>${taskList[index].esn.split('/')[1]}</td>
          <td>${taskList[index].sm}</td>
          <td>${taskList[index].task}</td>
          <td>${taskList[index].date}</td>
           <td>${taskList[index].str_shift}</td>
          <td>${taskList[index].people}</td>
          </tr>`;
          $("#myTable tbody").append(newRow);
        }

      }
      else {
        let newRow = '<tr><td colspan="5" style="text-align: center;">No Data</td></tr>';
        $("#myTable tbody").append(newRow);
      }
    }
  });



}

let template =
  `
 <div style=" background-color: #B3C6E7;">
  <div class="header">
    <img id="customer_png" src="" class="logo" style="height:150px;width:330px;margin-left: 20px; margin-top: 20px;" />
    <div class="tat-container" style="margin-right: 20px;">
      <div style="font-size: 30px;">Real-Time TAT: <span id="real_time_tat" style="font-size: 30px;"></span></div>
      <div style="font-size: 30px;">Plan TAT: <span id="expecttst_tat" style="font-size: 30px;"></span></div>
    </div>
  </div>

  <div class="info-container">
    <table class="td_border" style="width: 33%;margin-left: 35%;margin-top: -120px;">
      <tr>
        <td class="td_float td_border" style="font-size: 30px;">ESN: </td>
        <td class="td_border"><span id="str_esn" style="font-size: 30px;"></span></td>
      </tr>
      <tr>
        <td class="td_float td_border" style="font-size: 30px;">Engine Type: </td>
        <td class="td_border"><span id="str_engint_type" style="font-size: 30px;"></span></td>
      </tr>
      <tr>
        <td class="td_float td_border" style="font-size: 30px;">Customer: </td>
        <td class="td_border"><span id="str_customer" style="font-size: 30px;"></span></td>
      </tr>
      <tr>
        <td class="td_float td_border" style="font-size: 30px;">Plan Delivery Flow Date: </td>
        <td class="td_border"><span id="dt_release_close" style="font-size: 30px;"></span></td>
      </tr>
      <tr>
        <td class="td_float td_border" style="font-size: 30px;">Changed Delivery Date: </td>
        <td class="td_border"><span id="dt_release_update" style="font-size: 30px;"></span></td>
      </tr>
    </table>
    
  </div>


  <div class="flow-chart">
    <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F0</div>
      </div>
      <div class="flow-date" id="dt_f0">2024-01-15</div>
    </div>
    <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F1-1/B1</div>
      </div>
      <div class="flow-date" id="dt_f1b1">2024-01-16</div>
    </div>
    <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F1-1/B2B3</div>
      </div>
      <div class="flow-date" id="dt_f1b23">2024-01-17</div>
    </div>
    <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F1-2</div>
      </div>
      <div class="flow-date" id="dt_f12">2024-01-18</div>
    </div>
   <!-- <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F2</div>
      </div>
      <div class="flow-date" id="dt_f2">2024-01-19</div>
    </div>
    -->
    <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F3 </div>
      </div>
      <div class="flow-date" id="dt_f3">2024-01-20</div>
    </div>
    <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F4-1/B2B3</div>
      </div>
      <div class="flow-date" id="dt_f4b23">2024-01-21</div>
    </div>
    <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F4-1/B1</div>
      </div>
      <div class="flow-date" id="dt_f4b1">2024-01-22</div>
    </div>
    <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F4-2</div>
      </div>
      <div class="flow-date" id="dt_f42">2024-01-23</div>
    </div>
    <div class="flow-item">
      <div class="flow-step">
        <div class="flow-step-content" style="font-size: 20px;">F4-3</div>
      </div>
      <div class="flow-date" id="dt_f43">2024-01-24</div>
    </div>
  </div>
</div>
<div  class="workstation-info" style="height:100px;width: 20%;float: left;background: #105ea5;">
  <table  style="width: 100%;border-spacing: 10px;">
    <tr >
      <td class="td_float td_border_color" style="font-size: 25px;">WorkStation: </td>
      <td class="td_border_color"><span id="workstationName" style="font-size: 25px;"></span></td>
    </tr>
    <tr>
      <td class="td_float td_border_color" style="font-size: 25px;">Team number: </td>
      <td class="td_border_color"><span id="teamNumber" style="font-size: 25px;"></span></td>
    </tr>
    <tr>
      <td class="td_float td_border_color" style="font-size: 25px;">Team Leader: </td>
      <td class="td_border_color"><span id="teamLeader" style="font-size: 25px;"></span></td>
    </tr>
    

  </table>

 
</div>
<div class="workstation-info" style="height:100px;width: 20%; float: right; line-height: 100px; ">

  <div style="float: left;font-size: 30px;
    margin-left: 15%;">DATE: <span id="currentDate" style="text-align: center; font-size: 30px;"></span></div>
</div>
<div>
  <table cellpadding="0" cellspacing="0" id="myTable" class="zebra-striped">
    <thead>
      <tr>
        <th>#</th>
        <th>ESN</th>
        <th>SM</th>
        <th>Task</th>
        <th>Date</th>
        <th>Shift</th>
        <th>People</th>
      </tr>
    </thead>
    <tbody>

    </tbody>
  </table>
</div>
 `
