# RUPK异常件与白转黄监控表 - 轮播版

## 功能概述

这是一个高性能的异常转换监控系统，采用轮播图形式展示多个监控表格。系统支持自动循环播放，每个页面显示一个表格，确保页面流畅运行无卡顿。

## 主要功能

### 1. 轮播表格展示
- **一页一表格**: 每个页面只显示一个表格，避免页面过于复杂
- **自动循环播放**: 表格自动轮播，无需手动操作
- **平滑切换**: 表格切换动画流畅，用户体验佳

### 2. 四种监控表格
- **异常集件缺件零件**: 显示未处理的异常零件列表
- **白转黄 待转PNR (RUPK)**: 等待转换的零件
- **白转黄 已转PNR (F1-2/RUPK)**: 已完成转换的零件  
- **白转黄 待点灯扫描 (RUPK)**: 等待扫描的零件

### 3. 高性能优化
- **无滚动条设计**: 页面完全无滚动条，避免布局问题
- **分页循环滚动**: 表格内容分页显示，循环滚动
- **性能监控**: 实时监控FPS和内存使用，确保流畅运行
- **硬件加速**: 使用CSS3硬件加速，提升动画性能

## 技术特性

### 现代化UI设计
- 使用Tailwind CSS构建响应式界面
- 渐变色彩和卡片式布局
- 流畅的动画效果和交互体验

### 轮播功能
- 支持自动轮播和手动切换
- 表格内容分页显示，循环滚动
- 可控制轮播开启/暂停
- 不同表格使用不同滚动间隔

### 模块化架构
- 组件化开发，易于维护
- 分离关注点，职责明确
- 可复用的UI组件

## 文件结构

```
ExceptionConversionMonitor/
├── index.html                          # 主页面HTML
├── index.js                            # 主组件
├── styles.css                          # 自定义样式
├── README.md                           # 说明文档
├── api/
│   └── index.js                        # API服务
├── components/
│   ├── CarouselTable.js                # 轮播表格组件
│   ├── TableCarousel.js                # 表格轮播容器组件
│   ├── ExceptionPartsTable.js          # 异常零件表格组件（旧版）
│   └── WhiteToYellowTable.js           # 白转黄表格组件（旧版）
└── composables/
    └── useExceptionMonitor.js          # 数据管理composable
```

## 组件说明

### ExceptionPartsTable
异常集件缺件零件表格组件
- 支持自动滚动
- 状态标签显示
- 响应式设计

### WhiteToYellowTable
白转黄监控表格组件
- 支持三种类型：pending、converted、scanning
- 不同类型使用不同颜色主题
- 独立的滚动控制

### useExceptionMonitor
数据管理composable
- 统一的数据状态管理
- 自动刷新机制
- 错误处理

## 使用方法

### 基本使用
```javascript
import ExceptionConversionMonitor from './index.js'

// 在Vue应用中注册组件
app.component('ExceptionConversionMonitor', ExceptionConversionMonitor)
```

### 自定义配置
```javascript
// 自定义滚动间隔
<ExceptionPartsTable :scroll-interval="5000" />

// 控制自动滚动
<WhiteToYellowTable :auto-scroll="false" />
```

## API接口

### 异常零件数据
```javascript
// 获取异常集件缺件零件
exceptionMonitorApi.getExceptionParts()

// 获取白转黄数据
exceptionMonitorApi.getWhiteToYellowData('pending')

// 获取统计数据
exceptionMonitorApi.getMonitorStatistics()
```

## 样式定制

### CSS变量
```css
:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
}
```

### 自定义主题
可以通过修改`styles.css`文件来自定义页面主题和样式。

## 浏览器兼容性

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## 注意事项

1. 确保已正确引入所有依赖库（Vue3、Element Plus、VXE Table等）
2. 需要配置正确的API接口地址
3. 建议在现代浏览器中使用以获得最佳体验
4. 自动滚动功能在数据量较少时可能不明显

## 更新日志

### v2.0.0 (2024-01-15)
- 重构为轮播形式
- 一页一表格设计
- 消除页面滚动条
- 添加分页循环滚动
- 性能优化和硬件加速
- 流畅的切换动画

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现基础监控功能
- 添加自动滚动特性
- 现代化UI设计 