import { post } from '../../utils/request.js'

/**
 * 获取任务模板列表
 * @param {Object} postData - 查询参数
 * @param {number} postData.PageSize - 每页显示条数
 * @param {number} postData.PageIndex - 页码
 * @returns {Promise<Object>} 返回Promise对象，解析为任务模板列表数据
 */
export function queryTaskTemplateList(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_engine_task_list',
    Filter: postData,
  })
}

/**
 * 获取任务模板详情
 * @param {Object} postData - 查询参数
 * @param {string} postData.str_flow - Flow
 * @param {string} postData.str_group - 组
 * @param {string} postData.id_wo - WO
 * @returns {Promise<Object>} 返回Promise对象，解析为任务模板详情数据
 */
export function queryTaskTemplateDetail(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_engine_model_shift_task_tree',
    Filter: postData,
  })
}

/**
 * 获取任务列表
 * @returns {Promise<Object>} 返回Promise对象，解析为任务列表数据
 */
export function queryTaskList(id_task_parent) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_task_shift_customer_list',
    id_task_parent:id_task_parent
  })
}

/**
 * 设置顺序
 * @param {Object} postData - 查询参数
 * @param {string} postData.id_wo - WO
 * @param {string} postData.str_group - 组
 * @param {number} postData.int_group_sort - 顺序
 * @param {string} postData.str_flow - 流程
 */
export function setOrder(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_update_engine_group_sort',
    id: postData.id,
    // id_wo: postData.id_wo,
    str_group: postData.str_group,
    int_group_sort: postData.int_group_sort,
    str_flow: postData.str_flow,
    engine_type: postData.engine_type,
  })
}

/**
 * 检查顺序
 * @param {string} postData.id_wo - WO
 * @param {string} postData.str_group - 组
 * @param {number} postData.int_group_sort - 顺序
 * @param {string} postData.str_flow - 流程
 * @param {string} postData.engine_type
 */
export function checkOrder(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_confirm_engine_group_sort',
    id_wo: postData.id_wo,
    str_group: postData.str_group,
    int_group_sort: postData.int_group_sort,
    str_flow: postData.str_flow,
    engine_type: postData.engine_type,
    id_main: postData.id_main ,
  })
}

/**
 * 下发任务
 * @param {Object} postData - 查询参数
 * @param {string} postData.id_wos - WO
 * @param {string} postData.str_group - 组
 * @param {string} postData.str_flow - 流程
 * @param {number} postData.is_issued - 是否下发
 * @param {string} postData.dt_date - 日期
 */
export function sendTask(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_auto_team_shift',
    ids: postData.ids,
    id_wos: postData.id_wos,
    str_group: postData.str_group,
    str_flow: postData.str_flow,
    is_issued: postData.is_issued,
    str_engine_type: postData.str_engine_type,
    dt_date: postData.dt_date || null,
    id_mains: postData.id_mains || [],
    dt_issued: postData.dt_issued || null,
  })
}

/**
 * 完成标记
 * @param {Object} postData - 查询参数
 * @param {string} postData.id_wos - WO
 * @param {string} postData.str_group - 组
 * @param {string} postData.str_flow - 流程
 */
export function completeTask(postData) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_complete_engine_task',
    ids: postData.ids,
    // id_wos: postData.id_wos,
    str_group: postData.str_group,
    str_flow: postData.str_flow,
    str_engine_type: postData.str_engine_type,
  })
}

/**
 * 获取提示信息
 * @param {Object} params - 查询参数
 * @param {string} params.id_wos - WO
 * @param {string} params.str_group - 组
 * @param {string} params.str_flow - 流程
 * @param {string} params.is_issued - 是否下发
 * @param {string} params.dt_date - 日期
 * @returns {Promise<Object>} 返回Promise对象，解析为提示信息数据
 */
export function queryPromptMessage(params) {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_confirm_isure',
    ...params,
  })
}
