
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: Arial, sans-serif;
        background: #f0f0f0;
        padding: 20px;
        color: #333;
        height: 100vh;
        overflow: hidden; /* 移除外层滚动条 */
        display: flex;
        flex-direction: column;
      }

      .info-section {
        background: #689f38;
        color: white;
        padding: 10px 15px;
        border-radius: 0;
        margin-bottom: 20px;
        font-size: 16px;
        position: relative;
        flex-shrink: 0;
      }

      .station-info {
        display: inline-block;
        margin-right: 20px;
      }

      .date {
        position: absolute;
        right: 15px;
        top: 10px;
        font-weight: normal;
      }

      /* 主仪表板布局 */
      .dashboard {
        display: table;
        width: 100%;
        border-spacing: 20px;
        flex: 1;
        height: 100%;
      }

      .section {
        display: table-cell;
        width: 50%;
        vertical-align: top;
        height: 100%;
      }

      .card {
        background: white;
        border-radius: 8px;
        margin-bottom: 20px;
        padding: 0;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        height: calc(50vh - 60px); /* 适应屏幕高度 */
        display: flex;
        flex-direction: column;
      }
      
      .card-content {
        padding: 15px;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .header {
        background: #689f38;
        color: white;
        padding: 12px 15px;
        font-size: 16px;
        border-radius: 4px 4px 0 0;
        font-weight: bold;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
      }

      .chart-container {
        margin-top: 20px;
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden; /* 防止整个容器滚动 */
        min-height: 200px; /* 设置最小高度 */
      }

      /* 图表滚动内容区域 */
      .chart-scroll-content {
        flex: 1;
        overflow-y: auto; /* 只有内容区域滚动 */
        overflow-x: hidden;
        position: relative;
        scroll-behavior: smooth; /* 平滑滚动 */
      }
      
      .chart-container::before {
        content: '';
        position: absolute;
        top: -10px;
        left: 0;
        right: 0;
        height: 1px;
        background: #eaeaea;
      }

      /* 滚动指示器 */
      .chart-scroll-content::after {
        content: '';
        position: absolute;
        top: 10px;
        right: 10px;
        width: 20px;
        height: 20px;
        background: rgba(104, 159, 56, 0.1);
        border-radius: 50%;
        animation: scrollPulse 2s ease-in-out infinite;
        pointer-events: none;
        z-index: 5;
      }

      @keyframes scrollPulse {
        0%, 100% { opacity: 0.3; transform: scale(1); }
        50% { opacity: 0.8; transform: scale(1.1); }
      }

      /* 图表内容区域 */
      .chart-content {
        min-height: calc(100% - 25px); /* 减去X轴的高度 */
        position: relative;
        padding-bottom: 5px; /* 与X轴保持一点间距 */
      }

      /* 为图表组添加最小高度，确保在数据很多时有足够空间 */
      .chart-group {
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 5px;
        min-height: 70px; /* 确保每组有足够的显示空间 */
        /* 添加虚拟化支持的准备 */
        transition: all 0.2s ease;
      }

      .chart-group:hover {
        background-color: #fafafa;
      }

      /* x轴刻度线和标签 - 固定在容器底部 */
      .x-axis {
        position: relative;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 35px;
        display: flex;
        justify-content: space-between;
        padding-left: 40px;
        padding-right: 30px;
        background: white;
        border-top: 1px solid #eee;
        z-index: 10;
        flex-shrink: 0; /* 防止压缩 */
      }

      .x-axis-mark {
        position: relative;
        width: 1px;
        height: 6px;
        background-color: #aaa;
      }

      .x-axis-mark:before {
        content: '';
        position: absolute;
        top: -5px;
        left: 0;
        width: 1px;
        height: 5px;
        background-color: rgba(170, 170, 170, 0.2);
      }

      .x-axis-label {
        position: absolute;
        top: 10px;
        font-size: 16px;
        transform: translateX(-50%);
        color: #666;
        font-weight: 500;
      }
      
      /* x轴网格线 - 覆盖整个图表内容区域 */
      .x-grid-lines {
        position: absolute;
        bottom: 0;
        left: 40px;
        right: 30px;
        top: 0;
        z-index: 1;
        pointer-events: none; /* 允许点击穿透 */
      }
      
      .x-grid-line {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 1px;
        background-color: rgba(200, 200, 200, 0.2);
      }



      .group-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        padding-bottom: 6px;
        border-bottom: 1px solid #f0f0f0;
      }



      .percentage {
        font-size: 16px;
        font-weight: bold;
        color: #689f38;
        padding: 2px 8px;
        border-radius: 10px;
        background-color: #f0f7e6;
      }

      .bar-label {
        position: absolute;
        left: 0;
        top: 5px;
        width: 35px;
        font-size: 16px;
        font-weight: bold;
        text-align: left;
        white-space: nowrap;
      }

      .bar-wrapper {
        position: absolute;
        left: 40px;
        right: 30px;
        top: 0;
        height: 28px;
        background-color: #f9f9f9;
        border-left: 1px solid #ddd;
        border-radius: 2px;
        display: flex;
        flex-wrap: nowrap;
        overflow: hidden; /* 确保超出部分被裁剪 */
      }

      .bar-segment {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: visible; /* 改为visible，允许内容溢出 */
        border-right: 1px solid white;
        position: relative;
        min-width: 20px; /* 确保所有柱子至少有最小宽度 */
      }
      .bar-segment:last-child {
        border-right: none;
      }

      .bar-value {
        font-size: 16px;
        color: white;
        font-weight: bold;
        text-align: center;
        white-space: nowrap;
        text-shadow: 0 1px 1px rgba(0,0,0,0.3);
        padding: 0 2px; /* 增加内边距，确保数字与柱子边缘有间距 */
      }

      .table-container {
        flex: 1;
        overflow-y: auto;
        border: 1px solid #eaeaea;
        border-radius: 4px;
        margin-top: 15px;
        position: relative;
        max-height: calc(50vh - 120px); /* 限制表格高度 */
        scroll-behavior: smooth; /* 平滑滚动 */
      }
      
      .task-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
      }

      .task-table th,
      .task-table td {
        border: none;
        border-bottom: 1px solid #ddd;
        padding: 10px 8px;
        text-align: left;
        font-size: 16px;
      }
      
      .task-table th:not(:last-child),
      .task-table td:not(:last-child) {
        border-right: 1px solid #eaeaea;
      }

      .task-table th {
        background: #f5f5f5;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 1px 0 #ddd;
        font-weight: bold;
      }

      .task-table tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      .task-table tr:hover {
        background-color: #f0f7e6;
      }

      .task-row {
        height: 45px;
      }
      
      /* 滚动条样式优化 */
      .chart-scroll-content::-webkit-scrollbar {
        width: 6px;
      }
      
      .chart-scroll-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      .chart-scroll-content::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 3px;
      }
      
      .chart-scroll-content::-webkit-scrollbar-thumb:hover {
        background: #aaa;
      }

      .table-container::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      .table-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }
      
      .table-container::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 4px;
      }
      
      .table-container::-webkit-scrollbar-thumb:hover {
        background: #aaa;
      }

      .esn-code-small {
        font-family: monospace;
        font-size: 16px;
         font-weight: bold;
      }

      .status-complete {
        color: #689f38;
        font-weight: bold;
      }

      .status-pending {
        color: #ffa000;
        font-weight: bold;
      }

      /* 为低版本安卓添加的兼容性样式 */
      @media screen and (max-width: 1024px) {
        .dashboard {
          display: block;
        }

        .section {
          display: block;
          width: 100%;
        }

        .card {
          height: auto;
          min-height: 300px;
        }

        .task-table {
          font-size: 16px;
        }

        .header {
          font-size: 16px;
        }
      }

      .loading {
        text-align: center;
        padding: 20px;
        font-style: italic;
        color: #666;
        font-size: 16px;
      }

      .error-message {
        color: #e53935;
        margin: 10px 0;
        padding: 10px;
        border: 1px solid #e53935;
        background-color: #ffebee;
        border-radius: 4px;
      }
      
      /* 刷新时间显示 */
      .refresh-time {
        font-size: 16px;
        font-weight: normal;
        opacity: 0.8;
      }



      /* 滚动提示样式 */
      .scroll-hint {
        text-align: center;
        padding: 8px;
        background: #f0f7e6;
        color: #689f38;
        font-size: 16px;
        border-radius: 4px;
        margin-bottom: 10px;
        animation: fadeInOut 3s ease-in-out;
      }

      @keyframes fadeInOut {
        0% { opacity: 0; }
        50% { opacity: 1; }
        100% { opacity: 0.7; }
      }

      /* 优化大数据量时的图表组样式 */
      .chart-group {
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 5px;
        min-height: 60px;
        /* 添加虚拟化支持的准备 */
        transition: all 0.2s ease;
      }

      .chart-group:hover {
        background-color: #fafafa;
      }

      /* 为大数据量优化的条形图样式 */
      .chart-item {
        margin-bottom: 12px;
        position: relative;
        height: 32px;
        /* 确保在大数据量时仍然清晰可见 */
        min-height: 32px;
      }

      /* 优化ESN代码显示，在大数据量时保持可读性 */
      .esn-code {
        font-size: 16px;
        color: #555;
        font-weight: bold;
        /* 添加文本省略，防止过长的ESN影响布局 */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
      }

      /* 为大数据量添加加载优化 */
      .chart-container.loading-more {
        opacity: 0.8;
      }

      .chart-container.loading-more::after {
        content: '加载更多数据...';
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 16px;
        z-index: 5;
      }

      /* 表格滚动指示器 */
      .table-container::after {
        content: '';
        position: absolute;
        top: 10px;
        right: 10px;
        width: 15px;
        height: 15px;
        background: rgba(104, 159, 56, 0.1);
        border-radius: 50%;
        animation: scrollPulse 2s ease-in-out infinite;
        pointer-events: none;
        z-index: 5;
      }

      /* 鼠标悬浮时隐藏滚动指示器 */
      .chart-scroll-content:hover::after,
      .table-container:hover::after {
        display: none;
      }
