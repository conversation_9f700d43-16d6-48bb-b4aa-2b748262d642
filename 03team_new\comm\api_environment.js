/*=======这个JS文件为线上运行文件===========*/

// const globalApiUrl = (typeof apiEnvironment == "undefined" || apiEnvironment == null || apiEnvironment == "") ? "/api/Do/DoAPI" : apiEnvironment + "/api/Do/DoAPI";


/*=======本地开发，请创建本地文件 api_environment.local.js ，然后在Html里面引用  ===========*/
/*=======与当前 api_environment.js 文件同一层级 ===========*/
/*=======api_environment.local.js 文件不会提交 git 服务器 ===========*/
/*=======复制下面的示例代码到 api_environment.local.js， 示例代码如下： ===========*/




//const apiEnvironment = "";
// const apiEnvironment = "http://localhost:5003"; 
// const apiEnvironment = "http://**************";
 const apiEnvironment = "http://**************";
const globalApiUrl = (typeof apiEnvironment == "undefined" || apiEnvironment == null || apiEnvironment == "") ? "/api/Do/DoAPI" : apiEnvironment + "/api/Do/DoAPI";