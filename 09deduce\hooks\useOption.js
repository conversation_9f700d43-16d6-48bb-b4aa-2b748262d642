/**
 * @description Hook to manage options for select components
 * <AUTHOR>
 */
import { useApi } from './useApi.js'
const { ref } = Vue
export function useOption() {
  const { getCommonOptionApi } = useApi()

  const flowOptions = ref([])
  // * 获取FlowOptions
  const getFlowOptions = async () => {
    flowOptions.value = await getCommonOptionApi('Flow')
  }

  const siteOptions = ref([])
  // * 获取siteOptions
  const getSiteOptions = async () => {
    siteOptions.value = await getCommonOptionApi('Site')
  }

  const deptOptions = ref([])
  // * 获取deptOptions
  const getDeptOptions = async () => {
    deptOptions.value = await getCommonOptionApi('Dept')
  }

  const maintenaceOptions = ref([])
  // * 获取maintenaceOptions
  const getMaintenaceOptions = async () => {
    maintenaceOptions.value = await getCommonOptionApi('Maintenace')
  }

  const userListOptions = ref([])
  // * 获取userListOptions
  const getUserListOptions = async () => {
    userListOptions.value = await getCommonOptionApi('Owner')
  }

  const customerOptions = ref([])
  // * 获取客户下拉列表
  const getCustomerOptions = async () => {
    customerOptions.value = await getCommonOptionApi('Client')
  }

  return {
    flowOptions,
    getFlowOptions,
    siteOptions,
    getSiteOptions,
    deptOptions,
    getDeptOptions,
    maintenaceOptions,
    getMaintenaceOptions,
    userListOptions,
    getUserListOptions,
    customerOptions,
    getCustomerOptions,
  }
}
