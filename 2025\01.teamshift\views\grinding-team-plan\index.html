<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>磨削团队计划</title>
    <!-- CSS -->
    <link rel="stylesheet" href="../../styles/index.css" />
    <link rel="stylesheet" href="../../assets/element-plus@2.9.4/dist/index.css" />
    <link rel="stylesheet" href="../../styles/common.dialog.css" />
    <!-- CDN CSS -->
    <link rel="stylesheet" href="../../assets/vxe-pc-ui@4.3.80/lib/style.min.css" />
    <link rel="stylesheet" href="../../assets/vxe-table@4.10.6/lib/style.min.css" />
    <link rel="stylesheet" href="../../assets/dhtmlx-gantt@9.0.1/index.css" />
    <!-- <PERSON><PERSON> js部分 -->
    <script src="../../assets/vue@3.5.13/vue.global.js"></script>
    <script src="../../assets/element-plus@2.9.4/dist/index.full.js"></script>
    <script src="../../assets/sortablejs@latest/Sortable.min.js"></script>
    <script src="../../assets/element-plus@2.9.4/icons-vue/index.full.js"></script>
    <script src="../../assets/moment/moment.min.js"></script>
    <script src="../../assets/lodash@4.17.21/lodash.min.js"></script>
    <script src="../../assets/element-plus@2.9.4/dist/locale/zh-cn.js"></script>
    <script src="../../assets/xe-utils@3.7.0/dist/xe-utils.umd.min.js"></script>
    <script src="../../assets/vxe-pc-ui@4.3.80/lib/index.umd.min.js"></script>
    <script src="../../assets/vxe-table@4.10.6/lib/index.umd.min.js"></script>
    <script src="../../assets/@vueuse/shared@12.7.0/index.iife.min.js"></script>
    <script src="../../assets/@vueuse/core@12.7.0/index.iife.min.js"></script>
    <script src="../../assets/dhtmlx-gantt@9.0.1/index.js"></script>
    <!-- api部分 -->
    <script src="../../assets/axios@1.6.7/axios.min.js"></script>
  </head>
  <body>
    <div id="app">
      <grinding-team-plan></grinding-team-plan>
    </div>
  </body>
  <script type="module">
    import GrindingTeamPlan from './index.js'
    import '../../components/VxeTable/renderer/index.js'

    const { createApp } = Vue

    const app = createApp({
      components: {
        GrindingTeamPlan,
      },
    })

    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
    app.use(VxeUI)
    app.use(VXETable)
    app.mount('#app')
  </script>
</html>
