import { uploadFile } from '../../../api/handover/index.js'
import { useFileHandler } from './useFileHandler.js'

export const useUploader = (additionalParams = {}) => {
  const { ElMessage, ElLoading } = ElementPlus
  const fileHandler = useFileHandler()
  
  // 上传文件
  const handleUpload = async (row) => {
    try {
      const input = document.createElement('input')
      input.type = 'file'
      input.multiple = true
      input.accept = fileHandler.ALLOWED_FILE_TYPES.join(',')

      input.onchange = async (e) => {
        const files = Array.from(e.target.files)
        if (files.length) {
          // 过滤安全的文件
          const safeFiles = files.filter((file) => fileHandler.checkFileSecurity(file))

          if (safeFiles.length === 0) {
            ElMessage.warning('没有符合要求的文件可以上传')
            return
          }

          if (safeFiles.length !== files.length) {
            ElMessage.warning('部分文件未通过安全检查，已被过滤')
          }

          try {
            // 显示上传加载状态
            const loading = ElLoading.service({
              lock: true,
              text: '文件上传中...',
              background: 'rgba(0, 0, 0, 0.7)',
            })

            // 并行上传所有文件
            const uploadPromises = safeFiles.map(async (file) => {
              const formData = new FormData()
              formData.append('file', file)
              formData.append('type', 'handover') // 添加上传类型
              
              // 添加额外参数
              Object.keys(additionalParams).forEach(key => {
                if (additionalParams[key]) {
                  formData.append(key, additionalParams[key])
                }
              })

              try {
                const res = await uploadFile(formData)
                return {
                  name: file.name,
                  size: file.size,
                  type: file.type,
                  uploadTime: new Date().toLocaleString(),
                  str_path: res.path || '', // 保存返回的文件路径
                  str_file_name: file.name, // 保存文件名
                }
              } catch (error) {
                ElMessage.error(`文件 ${file.name} 上传失败: ${error.message || '未知错误'}`)
                return null
              }
            })

            // 等待所有文件上传完成
            const results = await Promise.all(uploadPromises)

            // 过滤掉上传失败的文件
            const successFiles = results.filter((result) => result !== null)

            // 更新行数据
            if (!row.attachment) {
              row.attachment = []
            }
            
            successFiles.forEach((file) => {
              row.attachment.push(file)
            })

            loading.close()

            if (successFiles.length > 0) {
              ElMessage.success(`成功上传 ${successFiles.length} 个文件`)
            } else {
              ElMessage.error('所有文件上传失败')
            }
          } catch (error) {
            ElMessage.error('文件上传过程中发生错误: ' + (error.message || '未知错误'))
          }
        }
      }
      input.click()
    } catch (error) {
      ElMessage.error('文件上传失败: ' + (error.message || '未知错误'))
    }
  }

  return {
    ...fileHandler,
    handleUpload,
  }
} 