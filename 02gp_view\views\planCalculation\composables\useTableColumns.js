const { ref } = Vue

// 过滤器类型枚举
const FILTER_TYPES = {
  INPUT: 'FilterInput',
  CALENDAR: 'FilterCalendar',
  SELECT: 'FilterSelect'
}

// 列配置工厂函数
const createColumn = (field, title, options = {}) => {
  const {
    minWidth = 100,
    filterType = FILTER_TYPES.INPUT,
    filterOptions = null,
    formatter = null,
    type = null,
    filterMultiple = true
  } = options

  const column = {
    field,
    title,
    minWidth,
    filters: filterOptions || [{ data: '' }]
  }

  // 添加类型
  if (type) {
    column.type = type
  }

  // 添加过滤器渲染器
  if (filterType && !filterOptions) {
    column.filterRender = { name: filterType }
  }

  // 添加选择过滤器的多选配置
  if (filterOptions && !filterMultiple) {
    column.filterMultiple = false
  }

  // 添加格式化函数
  if (formatter) {
    column.formatter = formatter
  }

  return column
}

// 状态格式化函数
const formatters = {
  yesNo: (value) => value === '1' ? '是' : '否',
  lockStatus: ({ cellValue }) => `${cellValue === '1' ? '是' : '否'}`,
  f3Changed: ({ cellValue }) => {
    const color = cellValue === 1 ? '#E6A23C' : ''
    const text = cellValue === 1 ? '是' : '否'
    return `<span style="color: ${color}">${text}</span>`
  },
  applicationStatus: ({ cellValue }) => {
    const color = cellValue === 1 ? 'green' : 'red'
    const text = cellValue === 1 ? '已应用' : '演算中'
    return `<span style="color: ${color}">${text}</span>`
  }
}

// 预定义的过滤器选项
const filterOptions = {
  yesNo: [
    { label: '是', value: '1' },
    { label: '否', value: '0' }
  ],
  applicationStatus: [
    { label: '已应用', value: 1 },
    { label: '演算中', value: 0 }
  ]
}

// 表格列配置定义
const COLUMN_CONFIGS = [
  {
    field: 'str_name',
    title: 'project名称',
    options: { minWidth: 100 }
  },
  {
    field: 'str_wo',
    title: 'WO',
    options: { minWidth: 100 }
  },
  {
    field: 'str_esn',
    title: 'ESN',
    options: { minWidth: 100 }
  },
  {
    field: 'str_engine_type',
    title: '机型',
    options: { minWidth: 100 }
  },
  {
    field: 'str_flow',
    title: 'Flow',
    options: { minWidth: 80, filterType: null } // 动态设置
  },
  {
    field: 'str_level',
    title: '修理级别',
    options: { minWidth: 100 }
  },
  {
    field: 'dt_f3_close',
    title: 'F3关闭时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_f11_close',
    title: 'F1-1 B1结束时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_f112_close',
    title: 'F1-1 B2/3结束时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_f12_close',
    title: 'F1-2结束时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_f41_close',
    title: 'F4-1 B2/3结束时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_f412_close',
    title: 'F4-1 B1结束时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_f42_close',
    title: 'F4-2结束时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_f43_close',
    title: 'F4-3结束时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_begin',
    title: '计划开始时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_end',
    title: '计划结束时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'dt_release_end',
    title: 'release结束时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'is_lock',
    title: '冻结状态',
    options: {
      minWidth: 100,
      filterOptions: filterOptions.yesNo,
      filterMultiple: false,
      formatter: formatters.lockStatus
    }
  },
  {
    field: 'dt_up',
    title: '创建时间',
    options: { minWidth: 100, filterType: FILTER_TYPES.CALENDAR }
  },
  {
    field: 'str_by',
    title: '创建人',
    options: { minWidth: 100 }
  },
  {
    field: 'is_f3_changed',
    title: 'F3变化',
    options: {
      type: 'html',
      minWidth: 80,
      filterOptions: filterOptions.yesNo,
      filterMultiple: false,
      formatter: formatters.f3Changed
    }
  },
  {
    field: 'is_state',
    title: '状态',
    options: {
      type: 'html',
      minWidth: 100,
      filterOptions: filterOptions.applicationStatus,
      filterMultiple: false,
      formatter: formatters.applicationStatus
    }
  }
]

export function useTableColumns() {
  const flowList = ref([])

  // 生成表格列配置
  const generateColumns = () => {
    return COLUMN_CONFIGS.map(({ field, title, options }) => {
      // 特殊处理 Flow 列的动态过滤器
      if (field === 'str_flow') {
        return createColumn(field, title, {
          ...options,
          filterOptions: flowList.value
        })
      }
      return createColumn(field, title, options)
    })
  }

  // 更新 Flow 过滤器选项
  const updateFlowOptions = (flows) => {
    flowList.value = flows.map(flow => ({
      label: flow,
      value: flow
    }))
  }

  return {
    flowList,
    generateColumns,
    updateFlowOptions,
    FILTER_TYPES,
    formatters
  }
} 