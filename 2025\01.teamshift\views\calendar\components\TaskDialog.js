import { useTaskDialog } from '../composables/useTaskDialog.js'
import { addTask } from '../../../api/calendar/index.js'

const { defineComponent, computed, watch, reactive ,onMounted} = Vue
const { WarningFilled } = ElementPlusIconsVue
const { ElMessage } = ElementPlus

export default defineComponent({
  name: 'TaskDialog',
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: '任务编辑',
    },
    form: {
      type: Object,
      required: true,
    },
    mode: {
      type: String,
      default: 'edit', // 'edit' | 'add'
      validator: (value) => ['edit', 'add'].includes(value),
    },
    taskOptions: {
      type: Array,
      default: () => [],
    },
    teamOptions: {
      type: Array,
      default: () => [],
    },
    subTeamOptions: {
      type: Array,
      default: () => [],
    },
    shiftOptions: {
      type: Array,
      default: () => [],
    },
    smOptions: {
      type: Array,
      default: () => [],
    },
    personOptions: {
      type: Array,
      default: () => [],
    },
    teamPlaceholder: {
      type: String,
      default: 'select Team',
    },
    type: {
      type: String,
      default: '',
    },

  },
  emits: [
    'update:visible',
    'cancel',
    'save',
    'reset',
    'refresh',
    'team-change',
    'cancel-task',
    'sm-change',
    'sub-team-change',
    'del-task'
  ],
  setup(props, { emit }) {
    const { handleSubTeamChange, messages } = useTaskDialog()

    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value),
    })


    // 是否禁用子团队和人员
    const isShowSubTeamAndPerson = computed(() => props.type.toLowerCase() === 'b2/3' && props.form.is_by_person_or_team == 1)

    const isAddMode = computed(() => props.mode === 'add')
    const isEditMode = computed(() => props.mode === 'edit')

    // 处理取消
    const handleCancel = () => {
      if (isAddMode.value) {
        emit('reset')
      } else {
        emit('cancel')
      }
    }

    // 处理保存
    const handleSave = async () => {

      if (isAddMode.value && !props.form.sm) {
        ElMessage.warning('请选择单元体')
        return
      }
      if (!props.form.shift) {
        ElMessage.warning('请选择班次')
        return
      }
      // if (props.form.is_by_person_or_team == 1 && (!props.form.person || props.form.person.length === 0)) {
      //   ElMessage.warning('请选择人员')
      //   return
      // }
      if (props.form.is_by_person_or_team == 0 && (!props.form.team)) {
        ElMessage.warning('请选择Team')
        return
      }


      if (isAddMode.value) {
         await handleAddTask()
      } else {
        emit('save', props.form)
      }
    }

    // 处理添加任务
    const handleAddTask = async () => {
      try {
        const smName = props.smOptions.find((item) => item.value === props.form.sm)?.label
        const taskId = props.smOptions.find((item) => item.value === props.form.sm)?.id_task
        const smid = props.smOptions.find((item) => item.value === props.form.sm)?.id_sm

        const filterObj = props.personOptions.filter(item => props.form.person.includes(item.id))
        const strsubteams = filterObj.map(item => item.str_name.split('(')[0]).join(',')
        const params = {
          str_sm: smName,
          id_sm: smid,
          id_wo: props.form.id_wo,
          id_shift_task: props.form.taskName,
          id_parent_task: taskId,
          pt_dt: props.form.date,
          id_shift: props.form.shift,
          id_team: props.form.team,
          str_flow: props.form.str_flow,
          str_task_type: props.form.type,
          str_sub_teams: strsubteams,
          id_staffs: props.form.person.join(','),
          is_delay: props.form.is_delay,
        }
        await addTask(params)
        ElMessage.success('添加成功')
        emit('refresh')
      } catch (error) {
        ElMessage.error('添加任务失败')
        console.error('添加任务失败:', error)
      }
    }

    // 处理取消任务
    const handleCancelTask = () => {
      emit('cancel-task')
    }

    // 删除排班任务
    const handleDeleteTaskPlan = () => {
      emit('del-task')
    }

    // 处理SM变更
    const handleSmChange = (value) => {
      props.form.taskName = ''
      props.form.team = ''
      emit('sm-change', value)
    }

    // 处理团队变更
    const handleTaskChange = (value) => {
      const task = props.taskOptions.find((item) => item.value === value)
      props.form.is_by_person_or_team = task.is_by_person_or_team + '';

    }
    // 处理团队变更
    const handleTeamChange = (value) => {
      emit('team-change', value, props.form)
    }

    // 处理子团队变更
    const onSubTeamChange = (value) => {
      // 通知父组件处理子团队变更
      emit('sub-team-change', value, props.form)

      // 处理可能的错误消息
      // handleSubTeamChange(value, props.form, props.subTeamOptions)
    }

    watch(() => props.form.subTeam, (newVal, oldVal) => {
      if (newVal && newVal.length > 0 && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        emit('sub-team-change', newVal, props.form)
      }
    }, { deep: true })

    onMounted(() => {
      // 初始化表单数据
      props.form.is_delay = '0' // 默认任务编辑

    })
    return {
      dialogVisible,
      isAddMode,
      isEditMode,
      messages,
      WarningFilled,
      handleCancel,
      handleSave,
      handleCancelTask,
      handleSmChange,
      handleTeamChange,
      onSubTeamChange,
      isShowSubTeamAndPerson,
      handleTaskChange,
      handleDeleteTaskPlan

    }
  },
  template: /*html*/ `
    <el-dialog
      v-model="dialogVisible"
      class="common-dialog"
      :title="title"
      :width="isAddMode ? '30%' : '500px'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <!-- 统一表单 -->
      <el-form    :model="form" :label-width="isAddMode ? '80px' : '100px'" class="mt-4">
         <el-form-item required label="编辑类型">
          <el-radio-group v-model="form.is_delay">
          <el-radio value="1" size="small"  >任务延期 </el-radio>
          <el-radio value="0" size="small"  >任务新增</el-radio>
         </el-radio-group>
        </el-form-item>
        <!-- 只在添加模式显示的表单项 -->
        <el-form-item v-if="isAddMode" required label="SM">
          <el-select v-model="form.sm" placeholder="请选择SM" clearable @change="handleSmChange">
            <el-option v-for="item in smOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        
        <!-- 任务名称字段 -->
        <el-form-item label="任务名称" required>
          <el-select 
            v-model="form.taskName" 
            placeholder="请选择任务名称" 
            style="width: 100%"
            :disabled="isEditMode"
            @change="handleTaskChange"
          >
            <el-option 
              v-for="option in taskOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>
        
        <!-- 日期字段 :disabled="isEditMode"-->
        <el-form-item label="日期" required>
          <el-date-picker
            v-model="form.date"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            
          />
        </el-form-item>
            <el-form-item label="班次" required>
          <el-select v-model="form.shift" filterable placeholder="请选择班次" style="width: 100%">
            <el-option 
              v-for="option in shiftOptions" 
              :key="option.id" 
              :label="option.str_name" 
              :value="option.id" 
            />
          </el-select>
        </el-form-item>
        <!-- Team字段  multiple-->
       <el-form-item v-show="(type=='B1' ||  form.is_by_person_or_team==0)" label="Team" >
          <el-select clearable
            v-model="form.team" 
            :placeholder="teamPlaceholder" 
            filterable 
            style="width: 100%" 
           
            @change="handleTeamChange"
          >
            <el-option 
              v-for="option in teamOptions" 
              :key="option.id" 
              :label="option.str_code" 
              :value="option.id" 
            />
          </el-select>
        </el-form-item>
        
        
        <!-- SubTeam字段 -->
       <!-- <el-form-item v-if="isShowSubTeamAndPerson" label="SubTeam" required>
          <el-select
            v-model="form.subTeam"
            placeholder="请选择Sub Team"
            filterable
            multiple
            style="width: 100%"
            @change="onSubTeamChange"
          >
            <el-option 
              v-for="option in subTeamOptions" 
              :key="option.id" 
              :label="option.str_code" 
              :value="option.id" 
            />
          </el-select>
          <div v-if="messages.length > 0" class="mt-1 space-y-1">
            <div
              v-for="(msg, index) in messages"
              :key="index"
              class="flex items-center rounded-md bg-orange-50 p-2 text-xs text-orange-600"
            >
              <el-icon :size="16" class="mr-1"><WarningFilled /></el-icon>
              <span class="italic">{{ msg }}</span>
            </div>
          </div>
        </el-form-item>-->
             <!-- 班次字段 -->
    
        <el-form-item v-if="isShowSubTeamAndPerson"  label="人员" >
          <el-select v-model="form.person" placeholder="请选择人员" filterable multiple style="width: 100%" >
            <el-option v-for="option in personOptions" :key="option.id" :label="option.str_name" :value="option.id" />
          </el-select>
        </el-form-item>
        
   
        
        <!-- 资源数量字段 -->
        <el-form-item label="资源数量" >
          <el-input-number disabled v-model="form.resourceCount" :min="1" :max="10" style="width: 100%" />
        </el-form-item>
      </el-form>
      
      <!-- 对话框底部按钮 -->
      <template #footer>
        <template v-if="isEditMode">
        <!-- <el-button type="warning" @click="handleDeleteTaskPlan">删除排班</el-button>-->
          <el-button type="warning" @click="handleCancelTask">取消任务</el-button>
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </template>
        <template v-else>
          <el-button type="primary" @click="handleSave">添加</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </template>
      </template>
    </el-dialog>
  `,
})
