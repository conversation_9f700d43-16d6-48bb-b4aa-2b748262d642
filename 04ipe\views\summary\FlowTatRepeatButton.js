const FlowTatRepeatButton = {
  template: /*html*/ `
    <button class="flex items-center gap-3 bg-white px-5 py-4 text-sm text-black shadow-sm" type="button">
    <svg t="1730887427606" 
    class="icon" viewBox="0 0 1024 1024" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg" 
    p-id="23620" 
    width="52" 
    height="52">
      <path d="M203.328 806.208H429.44v75.392H128V203.328h301.44v75.392H203.328v527.488z m414.528 0h75.328v75.392H617.856v-75.392z m113.024 0h75.328v75.392H730.88v-75.392z m113.024 75.392v-75.392h75.392v75.392h-75.392z m0-263.744V542.464h75.392v75.392h-75.392z m0 113.024v-75.392h75.392v75.392h-75.392z m0-226.112V429.44h75.392v75.328h-75.392z m0-113.024V316.416h75.392V391.68h-75.392z m0-113.024V203.328h75.392v75.392h-75.392z m-37.696 0H730.88V203.328h75.328v75.392z m-113.024 0H617.856V203.328h75.328v75.392zM504.768 128h75.392v828.928H504.768V128z" 
      p-id="23621" 
      fill="#1296db">
      </path>
    </svg>
       <span class="text-2xl"> 发动机重复绩效管理</span>
    </button>
  `,
}
export default FlowTatRepeatButton
