import { getTeamByType, getShiftList, getWarning, getAddSmList, getTaskListByTaskId } from '../../../api/calendar/index.js'

const { ref, reactive, watch } = Vue
const { ElMessage } = ElementPlus

export function useTaskDialog() {
  // 选项列表
  const taskOptionList = ref([])
  const teamOptionList = ref([])
  const subTeamOptionList = ref([])
  const shiftOptionList = ref([])
  const smOptionList = ref([])
  const personOptions = ref([])
  const messages = ref([])

  // 加载小组选项
  const loadTeamOptions = async (params) => {
    try {
      const teamRes = await getTeamByType(params)
      teamOptionList.value = teamRes.filter(item => item.id_parent === null)
      subTeamOptionList.value = teamRes.filter(item => item.id_parent !== null)
      return teamRes
    } catch (error) {
      ElMessage.error('加载Team选项失败')
      console.error('加载Team选项失败:', error)
      return []
    }
  }

  // 加载班次选项
  const loadShiftOptions = async () => {
    try {
      const shiftRes = await getShiftList()
      shiftOptionList.value = shiftRes.data
      return shiftRes.data
    } catch (error) {
      ElMessage.error('加载班次选项失败')
      console.error('加载班次选项失败:', error)
      return []
    }
  }

  // 加载SM选项列表
  const loadSmOptions = async (id_wo, type, flow) => {
    try {
      const res = await getAddSmList(id_wo, type, flow)
      smOptionList.value = res
      return res
    } catch (error) {
      ElMessage.error('加载SM选项失败')
      console.error('加载SM选项失败:', error)
      return []
    }
  }

  // 加载任务选项列表
  const loadTaskOptions = async (taskId) => {
    try {
      const res = await getTaskListByTaskId(taskId)
      taskOptionList.value = res
      return res
    } catch (error) {
      ElMessage.error('加载任务选项失败')
      console.error('加载任务选项失败:', error)
      return []
    }
  }

  // 处理子团队选择变化 - 只保留错误消息处理功能
  const handleSubTeamChange = async (currentlySelectedSubTeamIds, form, subTeamOptions) => {
    messages.value = []
    
    if (!currentlySelectedSubTeamIds || currentlySelectedSubTeamIds.length === 0) {
      return
    }

    // 收集可能的错误或警告消息
    for (const subTeamId of currentlySelectedSubTeamIds) {
      const params = {
        id_sub_team: subTeamId,
        dt_pt: form.date,
      }

      try {
        const res = await getWarning(params)
        if (res.message) {
          messages.value.push(res.message)
        }
      } catch (error) {
        console.error(`Error fetching warning for sub-team ${subTeamId}:`, error)
        const subTeamInfo = subTeamOptions.find((opt) => opt.id === subTeamId)
        const subTeamName = subTeamInfo ? subTeamInfo.str_code : `ID ${subTeamId}`
        const fetchErrorMessage = `获取 SubTeam "${subTeamName}" 信息失败。`
        messages.value.push(fetchErrorMessage)
      }
    }
  }

  return {
    taskOptionList,
    teamOptionList,
    subTeamOptionList,
    shiftOptionList,
    smOptionList,
    personOptions,
    messages,
    loadTeamOptions,
    loadShiftOptions,
    loadSmOptions,
    loadTaskOptions,
    handleSubTeamChange
  }
} 