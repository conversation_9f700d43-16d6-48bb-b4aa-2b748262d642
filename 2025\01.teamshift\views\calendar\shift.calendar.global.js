

// import { post } from '../../../01.teamshift/config/axios/httpReuest.js'
const { onMounted, ref, reactive, defineAsyncComponent } = Vue
const { ArrowLeftBold, ArrowRightBold } = ElementPlusIconsVue

/**
 * 日历组件
 */
const ShiftGlobalCalendar = {
  // 注册需要使用的子组件
  components: {
    PagePager: defineAsyncComponent(() => import('../../../01.teamshift/components/VxePager/PagePager.js')),
    ArrowLeftBold,
    ArrowRightBold
    // 'el-date-picker': ElDatePicker,
  },
  props: {
    page_type: '',
  },
  setup(props, { emit }) {
    const year = ref(new Date().getFullYear()) // 当前年份
    const month = ref(new Date().getMonth() + 1) // 当前月份
    const tableColumns = ref([]) // 表格列
    const tableData = ref([{ str_sm: "111", }])
    const formSearch = reactive({
      str_team: '',

    })
    // 分页数据
    const pagerState = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 1,
    })
    /**上一个月 */
    const reduceMonth = () => {
      if (month.value > 1) {
        month.value = month.value - 1
      } else {
        month.value = 12
        year.value = year.value - 1
      }
      getColumns()
    }
    /**下一个月 */
    const addMonth = () => {
      if (month.value <= 11) {
        month.value = month.value + 1
      } else {
        month.value = 1
        year.value = year.value + 1
      }
      getColumns()
    }
    /** 获取当月所有日期 */
    const getDaysInMonth = (year, month) => {
      month = parseInt(month, 10);
      const daysOfMonth = [];
      const lastDayOfMonth = new Date(year, month, 0).getDate();
      for (let i = 1; i <= lastDayOfMonth; i++) {
        if (i < 10) {
          daysOfMonth.push(year + "-" + (month < 10 ? '0' : '') + month + "-" + "0" + i);
        } else {
          daysOfMonth.push(year + "-" + (month < 10 ? '0' : '') + month + "-" + i);
        }
      }
      return daysOfMonth;
    }
    const getColumns = () => {
      tableColumns.value = getDaysInMonth(year.value, month.value)
      // 请求数据
    }
    const getTableData = async () => {
      getColumns()
      const param = {
        ac: 'gp_task_info_del',
        
      }
      const { data } = await post(param)
      if (data.code === 'error') {
        ElementPlus.ElMessage.error(data.text)
        return false
      }
      ElementPlus.ElMessage.success(data.text)
    }
    // 分页改变
    const handlePageChange = async ({ currentPage, pageSize }) => {
      pagerState.currentPage = currentPage
      pagerState.pageSize = pageSize
      await getTableData(pagerState.currentPage, pagerState.pageSize)
    }
    // 组件挂载时获取表格数据
    onMounted(async () => {
      getColumns()
    })

    // 返回模板需要使用的属性和方法
    return {
      year,
      month,
      reduceMonth,
      addMonth,
      getDaysInMonth,
      tableData,
      tableColumns,
      formSearch,
      getTableData,
      pagerState,
      handlePageChange
    }
  },

  // 组件模板
  template: /*html*/ `
  <div>
  <el-form  :inline="true" class="demo-form-inline" :model="formSearch" label-width="auto" style="max-width: 600px">
    <el-form-item label="Team" prop="str_team">
      <el-input v-model="formSearch.str_team" />
    </el-form-item>
    <el-form-item>
    <el-button type="primary" @click="getTableData">Search</el-button>
    <el-button>Cancel</el-button>
  </el-form-item>
  </el-form>


  <el-row>
  <el-icon @click="reduceMonth"><ArrowLeftBold/></el-icon>
  <span style="width: 100px; text-align: center; font-size: 20px; font-weight: bold; color: #409EFF;">{{year}}-{{(month < 10 ? '0' : '') + month}}</span>
  
  <el-icon @click="addMonth"><ArrowRightBold/></el-icon>
  </el-row>

  <el-table style="width: 100%"  :data='tableData' >
  <el-table-column prop="str_sm" label="SM" width="65"></el-table-column> 
  
  <el-table-column v-for="day in tableColumns" :key="day" :prop ="day" :label="day"></el-table-column> 
  </el-table>
  <PagePager
  :currentPage="currentPage"
  :pageSize="pageSize"
  :total="total"
  @pageChange="handlePageChange"
></PagePager>
</div>
    
  `,
}

export default ShiftGlobalCalendar
