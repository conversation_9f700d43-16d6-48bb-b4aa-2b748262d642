const { reactive, ref, nextTick, onMounted, onUpdated, defineAsyncComponent } = Vue

import { post } from '../../../config/axios/httpReuest.js';
import yPnGlobal from '../../../02gp_view/00view/01global/03pn.compent.js'


export default {
    name: 'ySmGlobal', // 单元体组件
    props: ['id', 'idContract', 'form'],
    components: {
        // 外部组件
        // 'BorderBox12': BorderBox12,
        'y-mp-pn': yPnGlobal,
        'y-el-draw': defineAsyncComponent(() => import('../../../components/ht.drawer.js')),
    },
    setup(props, ctx) {
        const formInline = ref({})
        const activeName = ref(["1", "2"])
        const height = ref(500)
        const pnDialog = reactive({
            isShowSave: false,
            visible: false,
            title: "零件清单"

        })
        const smGroup = ref([]);
        const tableData = [

        ]
        const tableRowClassName = (row, rowIndex) => {
            if (rowIndex === 1) {
                return 'warning-row'
            } else if (rowIndex === 3) {
                return 'success-row'
            }
            return ''
        }
        /**查询 */
        const query = async () => {
            const params = {
                au: 'ssamc',
                ap: 'api2018',
                ak: '',
                ac: 'mp_get_sm_global_view',
                str_esn: props.form.str_esn,
                id_wo: props.form.id_wo
            }
            const { data } = await post(params);

            if (data.code === 'success') {
                smGroup.value = data.data.sm_group
                tableData.value = data.data;
            } else {
                console.log(data.message)
            }
        }
        const openPnDialog = () => {
            pnDialog.visible = true;
        }
        const closePnDialog = () => {
            pnDialog.visible = false;
        }
        /**时间格式 */
        const momentF = (data) => {
            return moment(data).format('YYYY-MM-DD')
        }
        onMounted(() => {
            query()
            nextTick(() => {
                setTimeout(() => {
                }, 3000)
            });

        });
        onUpdated(() => {
            nextTick(() => {

                setTimeout(() => {


                }, 3000)

            });

        });
        return {
            formInline,
            activeName,
            tableData,
            tableRowClassName,
            height,
            pnDialog,
            openPnDialog,
            closePnDialog,
            query,
            smGroup,
            momentF

        }
    },
    template: /*template*/`



<el-card class="box-card">
    <template #header>
      <div class="card-header">
        <el-row >  
        <el-col :span="4">  <span style="font-size: 16px;font-weight: 800">{{form.str_esn}} </span>   </el-col>
        <el-col :span="4">  <span style="font-size: 16px;font-weight: 800">{{form.str_customer}}</span>   </el-col>
        <el-col :span="4">  <span style="font-size: 16px;font-weight: 800"> EKD:{{form.dt_ekd}}</span>   </el-col>
        <el-col :span="4"> 总周期： <span style="font-size: 16px;font-weight: 800"> 60  天    </span>   </el-col>
        <el-col :span="4">  <span style="font-size: 16px;font-weight: 800"> TAT: 10 天    </span>   </el-col>
        <el-col :span="4">  <span style="font-size: 16px;font-weight: 800">   F3 TAT: 8 天     </span>   </el-col>
                                   
        </el-row>
      <!--  <el-button class="button" text>Operation button</el-button>-->
      </div>
    </template>
    <el-row  :gutter="5" style="margin-left: 30px;margin-right: 30px;" > 
    <el-col :span="6" style="margin-bottom: 5px;" v-for="(smG, index) in smGroup">
                  <el-card class="box-card" >
                      <template #header>
                          <div class="card-header">
                          <el-row>  
                          <el-col :span="20">  <span style="font-size: 12px;font-weight: 800">{{smG.str_sm_group}} </span>   </el-col>
                         
                          <el-col :span="4">  <span style="font-size: 12px;font-weight: 800;color:red">-3 </span>   </el-col>
                          </el-row>
                          <el-row>  <span style="font-size: 12px;">MP:2024-01-01</span> </el-row>
                          <el-row>  <span style="font-size: 12px;">EKD:2024-01-01</span> </el-row>
                          </div>
                      </template>
                      <el-row style="margin-top: 5px;"  class="el-table">
                      <el-table
                      :data="smG.smEkds"
                    
                      :row-class-name="tableRowClassName"
                    >
                      <el-table-column prop="str_sm" label="SM" width="65" />
                      <el-table-column prop="dt_mp" label="MP"  />
                      <el-table-column  prop="dt_ekd"  label="EKD">
                      <template #default="scope">
                      <div>{{momentF(scope.row.dt_ekd)}}</div>
                    </template>
                      </el-table-column>
                      <el-table-column prop="int_delay_day" label="Day" width="55" />
                      <el-table-column fixed="right" label="..." width="50">
                      <template #default >
                        <el-button link type="primary" size="small" @click="openPnDialog()"
                          >PN</el-button
                        >
                       
                      </template>
                    </el-table-column>
                    </el-table>
                        
                      </el-row>
                  </el-card>
                  </el-col>
  </el-row>
   <!-- <template #footer>Footer content</template>-->
  </el-card>

  <y-el-draw  width="70%" :visible="pnDialog.visible" :isShowSave="pnDialog.isShowSave" :title="pnDialog.title" @closeDialog="closePnDialog">
  <y-mp-pn ></y-mp-pn>
  </y-el-draw>

  `,

}
