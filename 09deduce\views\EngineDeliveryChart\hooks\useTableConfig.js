const { ref, unref } = Vue
import { post } from '../../../../config/axios/httpReuest.js'

export const useTableConfig = (filterFields) => {
  const columns = [
    {
      field: 'str_esn',
      title: 'ESN',
      minWidth: 120,
      fixed: 'left',
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    { field: 'str_wo', title: 'WO', minWidth: 120, filters: [{ data: '' }], filterRender: { name: 'FilterInput' } },
    { field: 'str_flow', title: 'Flow', minWidth: 100, filters: [{ data: '' }], filterRender: { name: 'FilterInput' } },
    {
      field: 'str_engine_type',
      title: 'Engine Type',
      minWidth: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'str_maintenance_type',
      title: 'Maintenance Type',
      minWidth: 160,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'str_client',
      title: 'Customer',
      minWidth: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'dt_f11_close',
      title: 'F1-1 B1 Close',
      minWidth: 140,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'dt_f112_close',
      title: 'F1-1 B2/B3 Close',
      minWidth: 140,
      filters: [{ data: ''}],
      filterRender: { name: 'FilterInput' }
    },
    {
      field: 'dt_f12_close',
      title: 'F1-2 Close',
      minWidth: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'dt_f13_close',
      title: 'F1-3 Close',
      minWidth: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'dt_f3_close',
      title: 'F2/3 Close',
      minWidth: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'dt_f412_close',
      title: 'F4-1 B2/B3 Close',
      minWidth: 150,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'dt_f41_close',
      title: 'F4-1 B1 Close',
      minWidth: 140,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'dt_f42_close',
      title: 'F4-2 Close',
      minWidth: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'dt_f43_close',
      title: 'F4-3 Close',
      minWidth: 120,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'dt_release_close',
      title: 'Release Close',
      minWidth: 130,
      filters: [{ data: '' }],
      filterRender: { name: 'FilterInput' },
    },
  ]

  const tableData = ref([])
  const drawerVisible = ref(false)

  // 日期格式转换函数
  const formatDateToYYYYMMDD = (dateStr, timeType) => {
    // 如果日期为空，返回空字符串
    if (!dateStr) return ''

    try {
      let momentDate

      switch (timeType) {
        case '0': // 天
          momentDate = moment(dateStr.replace(/\//g, '-'))
          if (!momentDate.isValid()) return ''
          break

        case '1': // 月
          const [year, month] = dateStr.replace('年', '-').replace('月', '').split('-')
          momentDate = moment(`${year}-${month}`, 'YYYY-MM').endOf('month')
          if (!momentDate.isValid()) return ''
          break

        case '2': // 季度
          const [yearQ, quarterNum] = dateStr.replace('年', '-').replace('Q', '').split('-')
          momentDate = moment(`${yearQ}-${quarterNum}`, 'YYYY-Q').endOf('quarter')
          if (!momentDate.isValid()) return ''
          break

        case '3': // 年
          const yearStr = dateStr.replace('年', '')
          momentDate = moment(yearStr, 'YYYY').endOf('year')
          if (!momentDate.isValid()) return ''
          break

        default:
          return ''
      }

      return momentDate.format('YYYY-MM-DD')
    } catch (error) {
      console.error('日期格式转换错误:', error)
      return ''
    }
  }

  const getDetailData = async (date, seriesName) => {
    const currentTimeType = window.timeRangeType?.value || '1'
    const formatDate = formatDateToYYYYMMDD(date, currentTimeType)

    // 如果日期格式转换失败，显示错误信息并返回
    if (!formatDate) {
      ElementPlus.ElMessage.error('无效的日期格式')
      return
    }

    const colorMap = {
      预测: 1,
      计划: 2,
      预进厂: 3,
      发动机已交付数量: 0,
      目标: -1
    }

    try {
      if(colorMap[seriesName] === -1) {
        return
      }
      const params = {
        au: 'ssamc',
        ap: 'api2018',
        ak: '',
        ac: 'de_delivery_forecast_report_list',
        dt_date: formatDate,
        int_color: colorMap[seriesName],
        filter_fields: unref(filterFields)
      }

      const { data } = await post(params)
      if (data.code === 'success') {
        // 处理日期字段的格式
        tableData.value = data.data.map((item) => {
          const processedItem = { ...item }
          return processedItem
        })
        drawerVisible.value = true
      } else {
        ElementPlus.ElMessage.error('获取详细数据失败')
      }
    } catch (error) {
      console.error('获取详细数据失败:', error)
      ElementPlus.ElMessage.error('获取详细数据失败')
    }
  }

  return {
    columns,
    tableData,
    drawerVisible,
    getDetailData,
  }
}
