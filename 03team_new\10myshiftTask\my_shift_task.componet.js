Vue.component('y-shift-task-page', {
  props: ['input_plan_date'], //input_id_wo : 发动机  input_plan_date：计划日期 input_id_main  input_type: hsg/vg
  data: function () {
    return {
      is_show: false,
      tableData3: [],
      data: [],
      data_sm_test: [],
      dtMonth: '',
      calendarDate: new Date(),
      isShowDialog: false,
      dialogData: [],
      isShowEditDialog: false,
      editDialogRow: {
        int_status: '',
        str_content: '',
      },
      color: '',
      options: [
        {
          value: 'red',
          label: '红色',
          realValue: -2,
        },
        {
          value: '#67C23A',
          label: '绿色',
          realValue: 1,
        },
        {
          label: '黄色',
          value: '#FFD700',
          realValue: -1,
        },
      ],
      isDisable: false
    };
  },
  watch: {
    calendarDate(val) {
      this.dtMonth = new Date(val).getFullYear() + '-' + (new Date(val).getMonth() + 1);
      this.query();
    },
  },
  created: function () {
    this.dtMonth = new Date().getFullYear() + '-' + (new Date().getMonth() + 1);
    this.query();
  },
  methods: {
    query() {
      let _this = this;
      // return new Promise(resolve => {
      axios
        .post(globalApiUrl, {
          au: 'ssamc',
          ap: 'api2018',
          ak: '',
          ac: 'pt_get_my_shift_task',
          dt_month: _this.dtMonth,
        })
        .then(function (response) {
          if (response.data.data) {
            // _this.data.unshift()
            _this.data_sm_test = response.data.data;
            console.log(_this.data_sm_test);
          }

          // resolve(_this.data_sm_test)
        })
        .catch(function (error) {
          console.log(error);
        });

      // })
    },

    data_sm_test_pip(day) {
      let _this = this;
      return _this.data_sm_test.filter((f) => new Date(day).getTime() === new Date(f.dt_date).getTime());
    },

    // 日期点击事件
    onCalendarClick(item) {
      if(item.str_flow!="INSPECT"){
        this.$message({
            type: 'warning',
            message: '不是检验任务！'
        });
      }else{
        // 获取当前日期
        const currentDate = moment().format('YYYY-MM-DD');
        // 获取点击日期
        const clickDate = moment(item.pt_dt).format('YYYY-MM-DD');
  
        if (moment(clickDate).isSame(currentDate)) {
          this.editDialogRow = JSON.parse(JSON.stringify(item));
          if (this.editDialogRow.int_status === 1) {
            this.isDisable = false
          } else {
            this.isDisable = true
          }
          this.isShowEditDialog = true;
        }
      }
    },

    onCancelEditDialog() {
      this.isShowEditDialog = false;
    },
    // 保存编辑弹框
    onSubmitEditDialog() {
      let _this=this;
      debugger;
      if(_this.editDialogRow.int_status!=1 && (_this.editDialogRow.str_content=="" || _this.editDialogRow.str_content==null)){
        this.$message({
            type: 'warning',
            message: '请填写备注'
        });
        return
      }
      axios.post(globalApiUrl, {
        au: 'ssamc',
        ap: 'api2018',
        ak: '',
        ac: 'pt_fedback_plan_task',
        id_main: _this.editDialogRow.id_main,
        id_team_plan: _this.editDialogRow.id_team_plan,
        id: _this.editDialogRow.id,
        status: _this.editDialogRow.int_status,
        str_content: _this.editDialogRow.str_content,
      })
        .then(function (response) {
          if (response.data.code == 'success') {
            _this.query();
          }
        })
        .catch(function (error) {
          console.log(error);
        });
      this.onCancelEditDialog();
      this.isShowDialog = false;
    },
    getTitle(item) {
      return `${item.str_esn} : ${item.task_name} : ${item.str_shift} : ${item.dt_shift_start}~${item.dt_shift_end}`;
    },
    changeColor(val) {
      if (val !== 1) {
        this.isDisable = true
      } else {
        this.isDisable = false
      }
    },
  },
  // language=HTML
  template: `
    <div class="self_el_calendar">
      <el-calendar v-model='calendarDate'>
        <template slot="dateCell" slot-scope="{date, data}">
          <div>
            <el-row>{{ data.day.split('-').slice(1).join('-') }}</el-row>
            <div v-for="( vg_t ,index) in data_sm_test_pip(data.day)">
              <div v-for="(item, index) in vg_t.data">
                <div
                  :title="getTitle(item)"
                  @click.stop="onCalendarClick(item)"
                >
                  <!--                  生成一个灯泡的图标-->
                  <div class="flex flex-row items-center">
                    <div v-if="item.int_status === 1" class="w-4 h-4 mr-2" style="background-color: #67C23A"></div>
                    <div v-else-if="item.int_status === -1" class="w-4 h-4 mr-2" style="background-color: #FFD700"></div>
                    <div v-else-if="item.int_status === -2" class="w-4 h-4 mr-2" style="background-color: red"></div>
                    <div class="truncate inline-block w-full">{{ getTitle(item) }}</div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </template>
      </el-calendar>

      <el-dialog
        title="反馈"
        :visible.sync="isShowEditDialog"
        width="50%"
        class="self_dialog"
      >
        
        <el-descriptions class="mb-2" :column="2" border size="mini">
          <el-descriptions-item label="发动机">{{ editDialogRow.str_esn }}</el-descriptions-item>
          <el-descriptions-item label="任务">{{ editDialogRow.task_name }}</el-descriptions-item>
          <el-descriptions-item label="班次">{{ editDialogRow.str_shift }}</el-descriptions-item>
          <el-descriptions-item label="时间">{{ editDialogRow.dt_shift_start }}~{{ editDialogRow.dt_shift_end }}</el-descriptions-item>
        </el-descriptions>
        <div class="text-xl border-b mb-2 border-emerald-200">反馈</div>
        <el-form
          :model="editDialogRow"
          label-width="80px"
          size="mini"
          label-position="left"
        >
          <el-form-item label="状态">
            <el-select v-model="editDialogRow.int_status" placeholder="请选择状态颜色" @change="changeColor">
              <el-option v-for="option in options" :key="option.value" :value="option.realValue" :label="option.label">
                <span
                  :style="{ backgroundColor: option.value, width: '20px', height: '20px', display: 'inline-block', marginRight: '10px' }"></span>
                {{ option.label }}
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" v-if="isDisable">
            <el-input type="textarea" v-model="editDialogRow.str_content"
                      placeholder="请输入备注"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="onCancelEditDialog">取 消</el-button>
          <el-button type="primary" @click="onSubmitEditDialog">确 定</el-button>
        </div>
      </el-dialog>
    </div>

  `,
});
