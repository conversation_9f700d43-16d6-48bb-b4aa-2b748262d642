<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- css部分 -->
    <link rel="stylesheet" href="../../styles/tailwind.css" />
    <link rel="stylesheet" href="../../assets/element-plus@2.9.4/dist/index.css" />
    <link rel="stylesheet" href="../../styles/common.dialog.css" />
    <link rel="stylesheet" href="./css/auto.schedule.css" />
    <link rel="stylesheet" href="./css/gantt.css" />
    <link rel="stylesheet" href="../../assets/dhtmlx-gantt@9.0.1/index.css" />
    <!-- CDN js部分 -->
    <script src="../../assets/vue@3.5.13/vue.global.js"></script>
    <script src="../../assets/element-plus@2.9.4/dist/index.full.js"></script>
    <script src="../../assets/sortablejs@latest/Sortable.min.js"></script>
    <script src="../../assets/element-plus@2.9.4/icons-vue/index.full.js"></script>
    <script src="../../assets/moment/moment.min.js"></script>
    <script src="../../assets/lodash@4.17.21/lodash.min.js"></script>
    <script src="../../assets/@vueuse/shared@12.7.0/index.iife.min.js"></script>
    <script src="../../assets/@vueuse/core@12.7.0/index.iife.min.js"></script>
    <script src="../../assets/dhtmlx-gantt@9.0.1/index.js"></script>
    <script src="../../assets/lodash@4.17.21/lodash.min.js"></script>
    <!-- api部分 -->
    <script src="../../assets/axios@1.6.7/axios.min.js"></script>
    <title>自动排班查看</title>
  </head>
  <body>
    <div id="app">
      <auto-schedule flow="F4-1" type='B23' enginetype='CFM56'/>
    </div>
  </body>
  <script type="module">
    import AutoSchedule from './index.js'
    const { createApp } = Vue
    const app = createApp({
      components: {
        AutoSchedule,
      },
    })
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    app.use(ElementPlus)
    app.mount('#app')
  </script>
</html>
