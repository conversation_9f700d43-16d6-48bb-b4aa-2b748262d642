const CalculationButton = {
  props: {
    title: {
      type: String,
      default: '计算',
    },
    isUpdateFlow: {
      type: <PERSON>olean,
      default: 'false',
    },
    isUpdateTeam: {
      type: Boolean,
      default: 'false',
    },
    isUpdateStaff: {
      type: Boolean,
      default: 'false',
    },
  },
  template: /*html*/ `
    <button class="flex items-center gap-3 bg-white px-5 py-4 text-sm text-black shadow-sm" type="button">
      <el-badge is-dot :hidden="!(title=='Flow TAT Compute' && isUpdateFlow)">
        <el-badge is-dot :hidden="!(title=='Team Score Compute' && isUpdateTeam)">
          <el-badge is-dot :hidden="!(title=='Staff Score Compute' && isUpdateStaff)">
            <svg
              t="1726708582796"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="16880"
              width="52"
              height="52"
            >
              <path
                d="M863.43168 81.92H165.6832A83.88096 83.88096 0 0 0 81.92 165.6832v697.74848A83.87584 83.87584 0 0 0 165.6832 947.2h697.74848A83.87584 83.87584 0 0 0 947.2 863.43168V165.6832A83.87584 83.87584 0 0 0 863.43168 81.92z m25.79456 83.78368v319.86176h-345.67168V139.88864h319.87712a25.82528 25.82528 0 0 1 25.79456 25.79456zM165.6832 139.88864h319.88224v345.6768H139.88864V165.6832a25.82528 25.82528 0 0 1 25.79456-25.79456z m-25.79456 723.54304v-319.88224h345.6768v345.6768H165.6832a25.82528 25.82528 0 0 1-25.79456-25.79456z m723.54304 25.79456h-319.87712v-345.6768h345.67168v319.88224a25.82528 25.82528 0 0 1-25.79456 25.79456z"
                fill="#19a0e7"
                p-id="16881"
                data-spm-anchor-id="a313x.search_index.0.i5.4a0d3a81LLHUp1"
                class="selected"
              ></path>
              <path
                d="M396.288 293.26848H343.808V240.3072a28.928 28.928 0 1 0-57.856 0v52.96128H233.472a29.184 29.184 0 0 0 0 58.41408h52.48v52.9664a28.928 28.928 0 1 0 57.856 0V351.68768H396.288a29.184 29.184 0 0 0 0-58.4192z m0 404.6848H233.472a29.2096 29.2096 0 0 0 0 58.4192h162.816a29.2096 29.2096 0 0 0 0-58.4192z m236.57472 4.13184h162.73408a29.184 29.184 0 0 0 0-58.41408H632.832a29.184 29.184 0 0 0 0 58.41408z m162.75968 64H632.832a29.2096 29.2096 0 0 0 0 58.4192h162.7648a29.2096 29.2096 0 0 0 0-58.4448z m-162.816-364.8256a28.73344 28.73344 0 0 0 40.9088 0l37.08928-37.4528 37.0944 37.4528a28.72832 28.72832 0 0 0 40.90368 0 29.40416 29.40416 0 0 0 0-41.30816l-37.08928-37.44768 37.08928-37.44768a29.40416 29.40416 0 0 0 0-41.30816 28.72832 28.72832 0 0 0-40.90368 0l-37.0944 37.4528-37.09952-37.48864a28.73856 28.73856 0 0 0-40.9088 0 29.40416 29.40416 0 0 0 0 41.30816l37.08928 37.44768-37.08928 37.46816a29.40416 29.40416 0 0 0 0 41.29792z"
                fill="#FD9A16"
                p-id="16882"
              ></path>
            </svg>
          </el-badge>
        </el-badge>
      </el-badge>
      <span class="text-2xl">{{title}}</span>
    </button>
  `,
}
export default CalculationButton
