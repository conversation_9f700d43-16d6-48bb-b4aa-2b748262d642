!function(e,t){"function"==typeof define&&define.amd?define("vxe-table-plugin-export-xlsx",["exports","xe-utils"],t):"undefined"!=typeof exports?t(exports,require("xe-utils")):(t(t={},e.XEUtils),e.VXETablePluginExportXLSX=t)}("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:this,function(e,O){"use strict";var N,R;Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.VXETablePluginExportXLSX=void 0,O=(i=O)&&i.__esModule?i:{default:i};var D="f8f8f9",I="606266",t="thin",o="e8eaec";function $(e,t){if(t){if("seq"===e.type)return O.default.toValueString(t);switch(e.cellType){case"string":return O.default.toValueString(t);case"number":if(isNaN(t))break;return Number(t);default:if(t.length<12&&!isNaN(t))return Number(t)}}return t}function j(e,t){t&&(e.height=O.default.floor(.75*t,12))}function L(e,t){e.protection={locked:!1},e.alignment={vertical:"middle",horizontal:t||"left"}}function F(){return{top:{style:t,color:{argb:o}},left:{style:t,color:{argb:o}},bottom:{style:t,color:{argb:o}},right:{style:t,color:{argb:o}}}}function n(e){function t(){var e=new(R||window.ExcelJS).Workbook,n=e.addWorksheet(b);e.creator="vxe-table",n.columns=V,v&&n.addRows(T).forEach(function(e){C&&j(e,x),e.eachCell(function(e){var t=n.getColumn(e.col),t=u.getColumnById(t.key),o=t.headerAlign,t=t.align;L(e,o||t||g||h),C&&Object.assign(e,{font:{bold:!0,color:{argb:I}},fill:{type:"pattern",pattern:"solid",fgColor:{argb:D}},border:F()})})}),n.addRows(B).forEach(function(e){C&&j(e,x),e.eachCell(function(e){var t=n.getColumn(e.col),t=u.getColumnById(t.key);t&&(L(e,t.align||h),C)&&Object.assign(e,{font:{color:{argb:I}},border:F()})})}),E&&n.addRows(X).forEach(function(e){C&&j(e,x),e.eachCell(function(e){var t,o=n.getColumn(e.col),o=u.getColumnById(o.key);o&&(t=o.footerAlign,o=o.align,L(e,t||o||m||h),C)&&Object.assign(e,{font:{color:{argb:I}},border:F()})})}),k&&k({options:f,workbook:e,worksheet:n,columns:d,colgroups:a,datas:l,$table:u}),_.forEach(function(e){var t=e.s,e=e.e;n.mergeCells(t.r+1,t.c+1,e.r+1,e.c+1)}),e.xlsx.writeBuffer().then(function(e){var t,e=new Blob([e],{type:"application/octet-stream"}),o=f,n=(r=N).modal,r=r.t,a=o.message,l=o.filename,o=o.type,a=!1!==a;window.Blob?navigator.msSaveBlob?navigator.msSaveBlob(e,"".concat(l,".").concat(o)):((t=document.createElement("a")).target="_blank",t.download="".concat(l,".").concat(o),t.href=URL.createObjectURL(e),document.body.appendChild(t),t.click(),document.body.removeChild(t)):a&&n&&n.alert({content:r("vxe.error.notExp"),status:"error"}),M&&s&&(s.close(i),s.message({content:c("vxe.table.expSuccess"),status:"success"}))})}var r,o,i="xlsx",n=N,s=n.modal,c=n.t,u=e.$table,f=e.options,d=e.columns,a=e.colgroups,l=e.datas,n=u.props,e=u.reactData,p=u.getComputeMaps().computeColumnOpts,g=n.headerAlign,h=n.align,m=n.footerAlign,x=e.rowHeight,n=f.message,b=f.sheetName,v=f.isHeader,E=f.isFooter,e=f.isMerge,w=f.isColgroup,y=f.original,C=f.useStyle,k=f.sheetMethod,S=p.value,M=!1!==n,p=u.getMergeCells(),T=[],X=[],V=[],_=[],A=0,B=(d.forEach(function(e){var t=e.id,e=e.renderWidth;V.push({key:t,width:O.default.ceil(e/8,1)})}),v&&(w&&a?a.forEach(function(e,l){var i={};d.forEach(function(e){i[e.id]=null}),e.forEach(function(e){var t=e._colSpan,o=e._rowSpan,n=function e(t){var o=t.childNodes;return o&&o.length?e(o[0]):t}(e),r=d.indexOf(n),a=e.headerExportMethod||S.headerExportMethod;i[n.id]=a?a({column:e,options:f,$table:u}):y?n.field:e.getTitle(),(1<t||1<o)&&_.push({s:{r:l,c:r},e:{r:l+o-1,c:r+t-1}})}),T.push(i)}):(r={},d.forEach(function(e){var t=e.id,o=e.field,n=e.headerExportMethod||S.headerExportMethod;r[t]=n?n({column:e,options:f,$table:u}):y?o:e.getTitle()}),T.push(r)),A+=T.length),e&&p.forEach(function(e){var t=e.row,o=e.rowspan,n=e.col,e=e.colspan;_.push({s:{r:t+A,c:n},e:{r:t+A+o-1,c:n+e-1}})}),l.map(function(t){var o={};return d.forEach(function(e){o[e.id]=$(e,t[e.id])}),o}));A+=B.length,E&&(n=u.getTableData().footerData,w=n,p=(o=f.footerFilterMethod)?w.filter(function(e,t){return o({items:e,$rowIndex:t})}):w,n=u.getMergeFooterItems(),e&&n.forEach(function(e){var t=e.row,o=e.rowspan,n=e.col,e=e.colspan;_.push({s:{r:t+A,c:n},e:{r:t+A+o-1,c:n+e-1}})}),p.forEach(function(n){var r={};d.forEach(function(e){var t,o;r[e.id]=(o=n,e=e,t=(t=u).getVTColumnIndex(e),O.default.isArray(o)?$(e,o[t]):$(e,O.default.get(o,e.field)))}),X.push(r)}));M&&s?(s.message({id:i,content:c("vxe.table.expLoading"),status:"loading",duration:-1}),setTimeout(t,1500)):t()}function d(e){var t=N,o=t.modal,t=t.t,n=e.$table,e=e.options,n=n.internalData._importReject;!1!==e.message&&o&&o.message({content:t("vxe.error.impFields"),status:"error"}),n&&n({status:!1})}function r(a){var e=N,l=e.modal,i=e.t,s=a.$table,o=a.columns,c=a.options,e=a.file,u=s.internalData._importResolve,f=!1!==c.message,t=new FileReader;t.onerror=function(){d(a)},t.onload=function(e){var r=[],t=(o.forEach(function(e){e=e.field;e&&r.push(e)}),new(R||window.ExcelJS).Workbook),e=e.target;e?t.xlsx.load(e.result).then(function(e){var t,n,o,e=e.worksheets[0];e&&(e=Array.from(e.getSheetValues()),t=O.default.findIndexOf(e,function(e){return e&&0<e.length}),n=e[t],o=r,n.some(function(e){return-1<o.indexOf(e)}))?(e=e.slice(t+1).map(function(e){var o={},t=(e.forEach(function(e,t){o[n[t]]=e}),{});return r.forEach(function(e){t[e]=O.default.isUndefined(o[e])?null:o[e]}),t}),s.createData(e).then(function(e){e="insert"===c.mode?s.insertAt(e,-1):s.reloadData(e);return e.then(function(){u&&u({status:!0})})}),f&&l&&l.message({content:i("vxe.table.impSuccess",[e.length]),status:"success"})):d(a)}):d(a)},t.readAsArrayBuffer(e)}function a(e){if("xlsx"===e.options.type)return r(e),!1}function l(e){if("xlsx"===e.options.type)return n(e),!1}var i=e.VXETablePluginExportXLSX={install:function(e,t){/^(4)\./.test(e.version)||/v4/i.test(e.v)||console.error("[vxe-table-plugin-export-pdf 4.x] Version vxe-table 4.x is required"),N=e,R=t?t.ExcelJS:null,(e.setConfig||e.config)({table:{importConfig:{_typeMaps:{xlsx:1}},exportConfig:{_typeMaps:{xlsx:1}}},export:{types:{xlsx:0}}}),e.interceptor.mixin({"event.import":a,"event.export":l})}};"undefined"!=typeof window&&window.VXETable&&window.VXETable.use&&window.VXETable.use(i),e.default=i});