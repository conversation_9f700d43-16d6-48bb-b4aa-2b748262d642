/**
 * @description teamRadioSummary
 */
const { ref, reactive, onMounted } = Vue
import { post } from '../../../config/axios/httpReuest.js'
export default {
  name: 'teamRadioSummary',
  setup(props, { emit }) {
    const searchForm = reactive({
      dtYear: moment().startOf('year').format('YYYY'),
    })
    const tableColumns = [
      { field: 'str_group', title: 'Flow/Section',fixed:true },
      { field: 'str_flow_name', title: 'Category',fixed:true },
      { field: 'str_engine_type', title: 'Engine Type' ,fixed:true},
      { field: 'str_repair_type', title: 'Repair Type' ,fixed:true},
      { field: 'dec_jan', title: 'Jan',width:80},
      { field: 'dec_feb', title: 'Feb' ,width:80},
      { field: 'dec_mar', title: 'Mar' ,width:80},
      { field: 'dec_apr', title: 'Apr' ,width:80},
      { field: 'dec_may', title: 'May' ,width:80},
      { field: 'dec_jun', title: 'Jun' ,width:75},
      { field: 'dec_jul', title: 'Jul' ,width:75},
      { field: 'dec_aug', title: 'Aug' ,width:75},
      { field: 'dec_sep', title: 'Sep' ,width:75},
      { field: 'dec_oct', title: 'Oct' ,width:75},
      { field: 'dec_nov', title: 'Nov' ,width:75},
      { field: 'dec_dec', title: 'Dec' ,width:75},
      { field: 'dec_sum', title: 'Total' ,width:75}
    ]
    const tableRef = ref()
    const tableData = ref([])
    const queryData = ref([])
    // 初始化表格数据
    const initTableData = async () => {
      const params = {
        ac: 'pe_search_qty_summary',
        str_year: searchForm.dtYear,
        str_engine_type:searchForm.strEngineType,
        str_flow_name:searchForm.strFlowName,
        str_repair_type:searchForm.strRepairType
      }
      const { data: res } = await post(params)
      const { data } = res
      queryData.value = data
    }

    const enumFlowData = ref([])

    const getCommonOptionApi = async (type) => {
      const params = {
        ac: 'pe_enum',
        str_type: type,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        return data.data.map((item) => {
          return {
            label: item.str_key,
            value: item.str_value,
          }
        })
      } else {
        ElementPlus.ElMessage.error('type:' + type + data.text)
        return []
      }
    }

    const flowOptions = ref([])
    // * 获取FlowOptions
    const getFlowOptions = async () => {
      flowOptions.value = await getCommonOptionApi('flow_name')
    }

    const engineTypeOptions = ref([])
    // * 获取engineTypeOptions
    const getEngineTypeOptions = async () => {
      engineTypeOptions.value = await getCommonOptionApi('pe_engine_type')
    }

    const repairTypeOptions = ref([])
    // * 获取repairTypeOptions
    const getRepairTypeOptions = async () => {
      repairTypeOptions.value = await getCommonOptionApi('engine_level')
    }



    const handleSearch = async () => {
      await initTableData()
      await handlePageData()
    }

    const exportEvent = () => {
      const $table = tableRef.value
      if ($table) {
        $table.exportData({
          type: 'csv',
        })
      }
    }

    const loading = ref(false)
    // 前端分页
    const handlePageData = async () => {
      loading.value = true
      const { pageSize, currentPage } = pageVO
      pageVO.total = queryData.value.length
      tableData.value = queryData.value.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize,
      )
      loading.value = false
    }
    const pageChange = ({ pageSize, currentPage }) => {
      pageVO.currentPage = currentPage
      pageVO.pageSize = pageSize
      handlePageData()
    }
    const pageVO = reactive({
      total: 0,
      currentPage: 1,
      pageSize: 10,
    })

    onMounted(async () => {
      await initTableData()
      await getFlowOptions()
      await getEngineTypeOptions()
      await getRepairTypeOptions()
      await handlePageData()
    })
    return {
      tableRef,
      flowOptions,
      repairTypeOptions,
      engineTypeOptions,
      searchForm,
      pageVO,
      exportEvent,
      pageChange,
      tableColumns,
      tableData,
      handleSearch,
      handlePageData,
    }
  },
  template: /*html*/ `
    <div class="m-4">
      <el-form :model="searchForm" inline>
        <el-form-item label="Year">
          <el-date-picker
            v-model="searchForm.dtYear"
            type="year"
            placeholder="Select Year"
            value-format="YYYY"
          ></el-date-picker>
        </el-form-item>
      <el-form-item label="Category">
      <el-select v-model="searchForm.strFlowName" clearable placeholder="Select" style="width: 240px" >
        <el-option
          v-for="item in flowOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="Engine Type">
    <el-select v-model="searchForm.strEngineType" clearable placeholder="Select" style="width: 240px" >
      <el-option
        v-for="item in engineTypeOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </el-form-item>
    <el-form-item label="Repair Type">
    <el-select v-model="searchForm.strRepairType" clearable placeholder="Select" style="width: 240px" >
    <el-option 
      v-for="item in repairTypeOptions" 
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
    </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">Search</el-button>
        </el-form-item>
        <el-form-item>
          <vxe-button @click="exportEvent">导出</vxe-button>
        </el-form-item>
        <el-form-item> </el-form-item>
      </el-form>
    </div>
    <div class="m-4">
      <vxe-table
        ref="tableRef"
        :loading="loading"
        :data="tableData"
        stripe
        height="600"
        :column-config="{resizable: true}"
        :row-config="{isHover: true}"
      >
        <vxe-column type="seq" title="Seq" width="50"></vxe-column>
        <vxe-column
          v-for="(col, index1) in tableColumns"
          :field="col.field"
          :title="col.title"
          :width='col.width'
          :fixed='col.fixed'
          
          sortable
        ></vxe-column>
      </vxe-table>
      <vxe-pager
        v-model:currentPage="pageVO.currentPage"
        v-model:pageSize="pageVO.pageSize"
        :total="pageVO.total"
        @page-change="pageChange"
      >
      </vxe-pager>
    </div>
  `,
}
