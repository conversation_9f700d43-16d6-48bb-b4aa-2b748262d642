import ErrorComponent from '../../components/error.component.js'
import LoadingComponent from '../../components/loading.component.js'
import { currentDateKey, currentNodeKey, currentTypeViewKey, searchDateKey, searchForm<PERSON>ey } from '../../config/keys.js'
import { ADMINISTRATIVE_RECEIVE } from '../../config/nodeKey.js'
import { useTypeView } from '../hooks/useTypeView.js'
import { useCommApi } from '../hooks/useCommApi.js'

const { ref, reactive, provide, defineAsyncComponent, onMounted } = Vue
export default {
  name: 'AdministrateReceiveComponent',
  components: {
    CardComponent: defineAsyncComponent({
      loader: () => import('../components/card.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    TotalViewComponent: defineAsyncComponent({
      loader: () => import('../components/total.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    PViewComponent: defineAsyncComponent({
      loader: () => import('../components/p.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    WaybillViewTableComponent: defineAsyncComponent({
      loader: () => import('./waybill.view.table.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HeaderSearchComponent: defineAsyncComponent({
      loader: () => import('../components/HeaderSearch/index.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup() {
    const { getUsers, burialPoint } = useCommApi()
    const userS = ref([])
    const userST = ref([])
    const startDate = ref(moment().format('YYYY-MM-DD'))
    const endDate = ref(moment().add(6, 'days').format('YYYY-MM-DD'))
    provide(searchDateKey, {
      startDate,
      endDate,
      updateSearchDate: (start, end) => {
        startDate.value = start
        endDate.value = end
      },
    })

    const currentDate = ref(moment().format('YYYY-MM-DD'))
    provide(currentDateKey, {
      currentDate,
      updateCurrentDate: date => {
        currentDate.value = date
      },
    })

    provide(currentNodeKey, ADMINISTRATIVE_RECEIVE)

    const searchForm = reactive({
      date: [moment().format('YYYY-MM-DD'), moment().add(6, 'days').format('YYYY-MM-DD')],
      //id_engine_type: 'cfm56',
    })
    // 刷新的key
    const refreshKey = ref(0)
    const updateSearchForm = form => {
      Object.assign(searchForm, form)
      refreshKey.value += 1
    }
    provide(searchFormKey, {
      searchForm,
      updateSearchForm,
    })
    // 查询
    const handleSearchClick = form => {
      updateSearchForm(form)
    }
    const userFilter = val => {
      if (val) {
        userST.value = userS.value.filter(x => x.str_name?.indexOf(val) > -1 || x.str_code?.indexOf(val) > -1)
      } else {
        userST.value = userS.value
      }
    }
    const { currentTypeView, updateCurrentTypeView, activeName, handleTabChange } = useTypeView()
    provide(currentTypeViewKey, {
      currentTypeView,
      updateCurrentTypeView,
    })
    onMounted(async () => {
      burialPoint(ADMINISTRATIVE_RECEIVE)
      userS.value = await getUsers('RULA')
      userST.value = await getUsers('RULA')
    })
    return {
      searchForm,
      activeName,
      userS,
      userST,
      userFilter,
      handleTabChange,
      handleSearchClick,
      refreshKey,
    }
  },
  template: /*html*/ `
    <el-tabs v-model="activeName" type="card" @tab-change="handleTabChange">
      <el-tab-pane label="PDA View 视图" name="0" lazy>
        <div class="mx-4">
          <HeaderSearchComponent @search="handleSearchClick">
            <vxe-form-item title="Owner:" folding>
              <template #default="{data}">
                <el-select
                  v-model.trim="data.str_staff"
                  clearable
                  filterable
                  style="width: 210px;"
                  :filter-method="userFilter"
                >
                  <el-option v-for="item in userST" :key="item.id" :label="item.str_name" :value="item.str_name"
                    >{{ item.str_name }}-{{ item.str_code }}
                  </el-option>
                </el-select>
              </template>
            </vxe-form-item>
          </HeaderSearchComponent>
        </div>
        <div class="mb-4 w-full border-b border-gray-200"></div>
        <!--    卡片-->
        <div class="mx-4">
          <CardComponent :key="refreshKey"></CardComponent>
        </div>
        <div class="my-4 w-full border-b border-gray-200"></div>
        <div class="mx-4">
          <TotalViewComponent :key="refreshKey"></TotalViewComponent>
        </div>

        <div class="my-4 w-full border-b border-gray-200"></div>
        <div class="mx-4">
          <PViewComponent :key="refreshKey"></PViewComponent>
        </div>
        <div class="mt-4"></div>
      </el-tab-pane>
      <el-tab-pane label="Waybill view 运单视图" name="5" lazy>
        <div class="mx-4">
          <WaybillViewTableComponent></WaybillViewTableComponent>
        </div>
      </el-tab-pane>
    </el-tabs>
  `,
}
