import { post } from '../../config/axios/httpReuest.js'
import { useDownloadAndUpload } from '../hooks/useDownloadAndUpload.js'

/**
 * @description 绩效导入
 * <AUTHOR>
 */
const { ref } = Vue
const { genFileId } = ElementPlus
const PerformanceView = {
  setup() {
    const upload = ref(null)
    const fileList = ref([])
    const handleExceed = (files) => {
      upload.value?.clearFiles()
      const file = files[0]
      file.uid = genFileId()
      upload.value?.handleStart(file)
    }
    const { downloadTemplate, uploadXlsx } = useDownloadAndUpload()
    const map = {
      工作指令: 'id_wo',
      Flow: 'str_flow',
      开始使用时间: 'dt_start',
      使用结束时间:'dt_end',
      最小TAT:'dec_min_tat',
      最大TAT: 'dec_max_tat',
      系数:'dec_coefficient',
      F3CAT类型: 'int_cat',
    }
    const submitUpload = () => {
      uploadXlsx(upload, fileList, map, 'pe_special_tat_config_import')
    }

    // * 下载模板
    const handleDownloadTemplate = () => {
      // 使用XLXS.js生成模板
      const data = [
        ['工作指令', 'Flow', '开始使用时间','使用结束时间','最小TAT', '最大TAT', '系数','F3CAT类型']
        
      ]
      downloadTemplate(data, '特殊发动机导入模板.xlsx')
    }

    return {
      upload,
      fileList,
      handleExceed,
      submitUpload,
      handleDownloadTemplate,
    }
  },
  template: /*html*/ `
    <el-upload
      ref="upload"
      class="m-3"
      v-model:file-list="fileList"
      :limit="1"
      :on-exceed="handleExceed"
      :auto-upload="false"
      accpet=".xls,.xlsx"
    >
      <template #trigger>
        <el-button type="primary">select file</el-button>
      </template>  
      <el-button class="ml-3" type="success" @click="submitUpload">upload to server</el-button>
      <el-button class="ml-3" type="warning" @click="handleDownloadTemplate">download template</el-button>
      <template #tip>
        <div class="text-sm italic">only support xls/xlsx file</div>
      </template>
    </el-upload>
  `,
}

export default PerformanceView
