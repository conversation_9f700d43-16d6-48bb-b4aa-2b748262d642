import { useBusinessJump } from '../hooks/useBusinessJump.js';
import { useTable } from '../hooks/useTable.js';

const { onMounted, ref, toRefs, computed } = Vue;
export default {
  name: 'HtVxeTable',
  setup(props, { attrs }) {
    const tableHeight = ref(0);
    const getTableHeight = () => {
      tableHeight.value = window.innerHeight - 185;
    };

    // 判断是否为一个空对象
    const isLoading = computed(() => !attrs.tableData);
    onMounted(() => {
      getTableHeight();
      window.addEventListener('resize', getTableHeight);
    });

    const { filterMethod, filterChange, filterRecoverMethod, exportDataEvent, xTable, handleRowClassName } = useTable();

    const realTotalNum = computed(() => {
      const $table = xTable.value;
      return $table ? $table.getTableData().visibleData.length : 0;
    });
    const { skipSelectEvent } = useBusinessJump();
    const skipSelectEventClick = (currentNode) => {
      skipSelectEvent(xTable, currentNode);
    };

    return {
      ...toRefs(attrs),
      tableHeight,
      filterMethod,
      filterChange,
      filterRecoverMethod,
      exportDataEvent,
      xTable,
      handleRowClassName,
      isLoading,
      realTotalNum,
      skipSelectEventClick,
    };
  },
  template: /*html*/ `
    <vxe-toolbar>
      <template #buttons>
        <el-button type="primary" circle @click="exportDataEvent">
          <el-icon>
            <Download></Download>
          </el-icon>
        </el-button>
        <slot name="jump"></slot>
      </template>
      <template #tools>
        <span class="text-xl">Total: {{ realTotalNum ?? 0 }} </span>
      </template>
    </vxe-toolbar>
    <div class="border-b-2 mb-2"></div>
    <vxe-table
      :loading="isLoading"
      ref="xTable"
      :column-config="{resizable: true}"
      :row-config="{isHover: true, isCurrent: true}"
      show-overflow="title"
      :data="tableData"
      border
      :show-header="true"
      :height="tableHeight"
      :scroll-y="{enabled: true}"
      :row-class-name="handleRowClassName"
      @filter-change="filterChange"
    >
      <vxe-table-column v-if="isCheckbox" fixed="left" type="checkbox" width="60"></vxe-table-column>
      <vxe-table-column fixed="left" type="seq" width="60"></vxe-table-column>
      <vxe-column
        v-for="item in tableColumn"
        :key="item.id"
        :field="item.prop"
        :filter-method="filterMethod"
        :filter-reset-method="filterRecoverMethod"
        :filters="item.field"
        :fixed="item.fixed"
        :title="item.label"
        :visible="item.showColumn"
        :min-width="item.minWidth"
        :formatter="item.formatter"
        sortable
      >
        <!--筛选插槽-->
        <template v-if="item.is_select == 'input'" #filter="{ $panel, column }">
          <div v-for="(option, index) in column.filters" :key="index">
            <el-input
              v-model="option.value"
              placeholder="按回车确认筛选"
              @input="$panel.changeOption($event, !!option.value, option)"
              @keyup.enter="$panel.confirmFilter(option.field = item.prop)"
            ></el-input>
          </div>
        </template>
        <!--单元格显示插槽-->
        <template #default="{ row }">
          <div v-if="item.prop === 'is_out' || item.prop === 'is_sp' || item.prop === 'is_aog'">
            <span v-if="row[item.prop] === 1">是</span>
            <span v-else>否</span>
          </div>
          <div v-else>
            <span>{{ row[item.prop] }}</span>
          </div>
        </template>
      </vxe-column>
      <slot></slot>
    </vxe-table>
  `,
};
