const { Search, Refresh } = ElementPlusIconsVue
/**
 * 筛选器模块 - 负责处理日期和业务类型的筛选功能 (Composition API)
 */
const FilterModule = {
  /**
   * 使用Composition API创建筛选器
   * @returns {Object} 筛选器相关的状态和方法
   */
  setup() {
    // 业务类型选项
    const businessTypes = Vue.ref([])
    // 创建响应式状态
    const state = Vue.reactive({
      dateRange: [moment().format('YYYY-MM-DD'), moment().add(14, 'day').format('YYYY-MM-DD')],
      selectedBusinessTypes: '105',
    })

    /**
     * 获取业务类型
     */
    const fetchBusinessTypes = async () => {
      try {
        const { getBusinessTypes } = await import('../../api/index.js')
        const response = await getBusinessTypes()
        businessTypes.value = response.map((item) => ({
          value: item.str_value,
          label: item.str_key,
        }))
      } catch (error) {
        console.error('获取业务类型失败:', error)
      }
    }

    /**
     * 处理筛选条件变化
     * @param {Function} callback 回调函数
     */
    const handleFilterChange = (callback) => {
      if (typeof callback === 'function') {
        callback({
          dateRange: state.dateRange,
          businessTypes: state.selectedBusinessTypes,
        })
      }
    }

    /**
     * 应用筛选器
     * @param {Function} callback 回调函数
     * @param {Function} refreshCharts 刷新图表的回调函数
     */
    const applyFilters = (callback, refreshCharts) => {
      if (typeof callback === 'function') {
        callback({
          dateRange: state.dateRange,
          businessTypes: state.selectedBusinessTypes,
        })
      }

      // 直接刷新图表
      if (typeof refreshCharts === 'function') {
        refreshCharts()
      }
    }

    // 返回组合式API的结果
    return {
      // 状态
      businessTypes,
      state,

      // 方法
      fetchBusinessTypes,
      handleFilterChange,
      applyFilters,
    }
  },

  /**
   * 创建筛选器组件
   * @param {Object} props 组件属性
   * @returns {Object} 组件配置
   */
  createFilterComponent(props = {}) {
    return {
      name: 'FilterComponent',
      components: {
        Search,
        Refresh,
      },
      props: {
        onFilterChange: {
          type: Function,
          default: () => {},
        },
        refreshCharts: {
          type: Function,
          default: () => {},
        },
        ...props,
      },
      setup(props) {
        const { businessTypes, state, fetchBusinessTypes, handleFilterChange, applyFilters, resetFilters } =
          FilterModule.setup()

        const handleApply = () => {
          applyFilters(
            (filters) => {
              props.onFilterChange && props.onFilterChange(filters)
            },
            () => {
              props.refreshCharts && props.refreshCharts()
            },
          )
        }

        Vue.onMounted(() => {
          fetchBusinessTypes()
        })

        return {
          businessTypes,
          ...Vue.toRefs(state),
          handleApply,
        }
      },
      template: /*html*/ `
        <div class="filter-container bg-white rounded-lg shadow-sm py-4 px-5">
          <div class="flex flex-wrap items-center gap-4">
            <div class="shrink-0">
              <h2 class="text-gray-700 font-medium m-0 mr-2">数据筛选:</h2>
            </div>
            <div class="flex-grow md:flex-grow-0 md:w-64">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                size="default"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </div>
            <div class="flex-grow md:flex-grow-0 md:w-64">
              <el-select
                v-model="selectedBusinessTypes"
                placeholder="选择业务类型"
                style="width: 100%"
                size="default"
              >
                <el-option
                  v-for="item in businessTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
            <div class="flex items-center ml-auto">
              <el-button type="primary" size="default" @click="handleApply" class="mr-2">
                <el-icon class="mr-1"><Search /></el-icon>
                应用筛选
              </el-button>
            </div>
          </div>
        </div>
      `,
    }
  },
}
