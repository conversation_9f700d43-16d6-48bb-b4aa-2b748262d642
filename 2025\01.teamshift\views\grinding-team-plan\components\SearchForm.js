const { ref } = Vue
const { useVModel } = VueUse

export default {
  name: 'SearchForm',
  props: {
    form: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['search', 'update:form'],
  setup(props, { emit }) {
    const searchForm = useVModel(props, 'form', emit)
    // 搜索
    const handleSearch = () => {
      emit('search', searchForm.value)
    }
    // 重置
    const handleReset = () => {
      searchForm.value = {
        dt_range: [moment().subtract(7, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      }
      handleSearch()
    }
    return {
      searchForm,
      handleSearch,
      handleReset,
    }
  },
  template: /*html*/ `
    <el-form :model="searchForm" inline>
      <el-form-item label="日期">
        <el-date-picker
          type="daterange"
          placeholder="选择日期"
          v-model="searchForm.dt_range"
          clearable
          value-format="YYYY-MM-DD"
          format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button type="info" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  `,
}
