import { queryEmployeeScore } from '../../api/index.js'

const { onMounted, ref, onBeforeUnmount } = Vue
const EmployeeRatingChart = {
  setup() {
    const chartData = ref([])
    const total = ref(0)
    const month = ref(moment().format('YYYY-MM'))
    let strMonth = month.value;
    const getChartData = async (month) => {
      const {
        data: { data },
      } = await queryEmployeeScore(month)
      strMonth = month;
      chartData.value = data.filter((item) => item.name !== '总数')
      total.value = data.find((item) => item.name === '总数').count
      employeeRatingChart.value = echarts.init(employeeRatingRef.value)
      employeeRatingOption.series[0].data = chartData.value.map((item) => ({
        name: item.name,
        value: item.count,
      }))
      employeeRatingChart.value.setOption(employeeRatingOption)
      employeeRatingChart.value.on('click', 'series', JumpScoreClick)
    }
    // 饼图
    const employeeRatingRef = ref(null)
    const employeeRatingChart = ref(null)

    const employeeRatingOption = {
      backgroundColor: 'rgba(129,204,190, 0.2)',
      title: {
        text: '员工打分',
        textStyle: {
          color: '#0e1821',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        bottom: '50%',
        right: '8%',
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 12,
        formatter: (name) => {
          const value = chartData.value.find((item) => item.name === name).count
          return `{name|${name}}: {value|${value}}`
        },
        textStyle: {
          rich: {
            name: {
              fontSize: 14,
              color: '#0e1821',
            },
            value: {
              fontSize: 14,
              color: '#0e1821',
            },
          },
        },
      },
      series: [
        {
          name: '评分',
          type: 'pie',
          radius: ['50%', '85%'],
          center: ['40%', '50%'],
          clockwise: false,
          avoidLabelOverlap: false,
          data: chartData.value,
          label: {
            show: true,
            position: 'center',
            formatter: () => `总数\n${total.value}`,
            fontWeight: 'bold',
            fontSize: 20,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }
    const handleResize = () => {
      employeeRatingChart.value.resize()
    }

    const JumpScoreClick =  async (params) => {
      let isState = 0;
     if(params.data.name==='未打分'){
       isState = -1;
     }
     const url = `/Page/?moduleid=1808019598047449088&param_value={str_calculate_month:\'${strMonth}\',is_state:${isState}}`;
       var winparam = { url: url, title:'Staff Score Board', grid: null, width: '800px', height: '600px', region: null };               
       com.openwin(this, '', winparam, false, false, false);
 
     }


    onMounted(async () => {
      await getChartData(month.value)
      window.addEventListener('resize', handleResize)
    })
    onBeforeUnmount(() => {
      if (employeeRatingChart.value) {
        employeeRatingChart.value.dispose()
      }
      window.removeEventListener('resize', handleResize)
    })
    return {
      employeeRatingRef,
      getChartData,
      JumpScoreClick
    }
  },
  template: /*html*/ `
    <div ref="employeeRatingRef" class="w-full h-full"></div>
  `,
}
export default EmployeeRatingChart
