import {post} from '../../config/axios/httpReuest.js';
import Line<PERSON>hart from './LineChart.js';

const {ref, onMounted, nextTick, defineAsyncComponent} = Vue;
const SiteTrends = {
  components: {
    LineChart,
  },
  setup() {
    // line chart data
    const lineChartData = ref([]);
    // define form data
    const formData = ref({
      date: [moment().subtract(7, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      engineType: '',
    });
    const shortcuts = [
      {
        text: '最近一周',
        value() {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          return [start, end];
        },
      },
      {
        text: '最近一个月',
        value() {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          return [start, end];
        },
      },
      {
        text: '最近三个月',
        value() {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          return [start, end];
        },
      },
    ];
    // search line chart data
    const searchLineChartData = async () => {
      const {date, engineType} = formData.value;
      let filter_fields = [
        {
          str_key: 'id_engine_type',
          str_value: engineType === '' ? 'all' : engineType,
        },
      ];

      if (date.length !== 0) {
        filter_fields.push({
          str_key: 'dt_date',
          str_value: date,
        });
      }

      const param = {
        ac: 'pda_PTrend',
        filter_fields,
      };
      const {data} = await post(param);
      lineChartData.value = data.data;
    };

    onMounted(() => {
      searchLineChartData();
    });


    return {
      lineChartData,
      formData,
      shortcuts,
      searchLineChartData,
    };
  },
  /*html*/
  template: `
    <div class="h-full w-full bg-sky-400">
      <div class="px-4 pt-4">
        <el-form :model="formData">
          <div class="grid grid-cols-4 gap-4">
            <el-form-item label="Date:" class="col-span-2">
              <el-date-picker
                v-model="formData.date"
                unlink-panels
                type="daterange"
                :shortcuts="shortcuts"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="Engine Type:">
              <el-select v-model="formData.engineType" placeholder="请选择机型" clearable>
                <el-option label="CFM56" value="CFM56"></el-option>
                <el-option label="LEAP" value="LEAP"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchLineChartData">查询</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div v-for="(item) in lineChartData" :key="item.TrendType">
        <!--      p title-->
        <div class="text-2xl font-semibold px-2 bg-gray-200">{{ item.TrendType }}负</div>
        <!--   min-width sm: 640  md: 768 lg:1024 xl: 1280  -->
        <div class="grid sm:grid-cols-1 md:grid-cols-3 gap-4 p-2">
          <div v-for="siteItem in item.site" :key="siteItem.int_site" class="bg-white rounded-lg">
            <div class="flex justify-between items-center bg-gray-200 p-2 rounded-t-lg">
              <div class="text-lg font-semibold">{{ siteItem.str_site }}</div>
              <div class="text-sm text-gray-500">{{ siteItem.str_dept }}</div>
            </div>
            <!-- 折线图 高度自适应-->
            <line-chart :key="Math.round()" :data="siteItem.trends"/>
          </div>
        </div>
      </div>
    </div>
  `,
};

export default SiteTrends;
