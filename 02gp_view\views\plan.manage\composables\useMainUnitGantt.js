/**
 * 主单元体甘特图
 */
import { useInitialize } from './useInitialize.js'
import { post } from '../../../../config/axios/httpReuest.js'
import { useDynamicColumn } from '../../../composables/useDynamicColumn.js'

const { ref } = Vue

export const useMainUnitGantt = (id, emit) => {
  const columns = [
    { name: 'text', label: '任务名称', tree: true, width: '*', resize: true },
    { name: 'start_date', label: '开始时间', align: 'center', width: 100 },
    { name: 'dt_end', label: '结束时间', align: 'center', width: 100 },
    { name: 'duration', label: '持续时间', align: 'center', width: 100 },
    { name: 'dt_f2_3_end', label: 'F2/3结束时间', width: 100 },
  ]
  /**
   * 初始化甘特图
   * @param {string} domId - 甘特图容器的DOM元素
   * @param {array} columns - 甘特图列配置
   */
  const { initGantt, gantt } = useInitialize('mainUnitGantt', columns)
  // 是否查看主单元体甘特图
  const isViewMainUnitGantt = ref(false)
  // 查看主单元体甘特图
  const handleMainUnitGantt = (str_node) => {
    isViewMainUnitGantt.value = true
    initGantt()
    getGanttData(str_node)
    handleMainUnitExpand()
    emit('hasSave', false)
  }
  // 转换数据
  const transformData = (data) => {
    return _.map(data, (item) => {
      // 查看当前id下是否有子节点
      const hasChild = data.some((it) => it.id_root === item.id)
      const unscheduled = !(item.dt_start && item.dt_end) && !hasChild
      return {
        id: item.id,
        text: item.str_node,
        start_date: item.dt_start,
        dt_end: item.dt_end,
        duration: item.duration,
        progress: item.dec_precent,
        parent: item.id_root,
        unscheduled,
        type: hasChild ? 'project' : 'task',
        dt_f2_3_end: item.dt_f2_3_end,
      }
    })
  }

  const dynamicColumn = useDynamicColumn(gantt, columns)

  /*
   * 获取甘特图数据
   */
  const getGanttData = async (str_node) => {
    const params = {
      ac: 'gp_planmain_sm_gantt',
      id,
      str_node,
    }
    const { data } = await post(params)
    const { code, text, data: resData } = data
    if (code === 'error') {
      ElementPlus.ElMessage.error(text)
      return
    }
    gantt.parse({
      tasks: transformData(resData.subPlans),
      links: resData.links,
    })
  }

  // 是否折叠甘特图
  const isMainUnitCollapse = ref(false)
  // 折叠甘特图
  const handleMainUnitCollapse = () => {
    isMainUnitCollapse.value = true
    // 使用批量操作优化性能
    gantt.batchUpdate(() => {
      // 只折叠第一层，保持数据结构
      const rootTasks = gantt.getChildren(0)
      rootTasks.forEach((taskId) => {
        if (gantt.isTaskExists(taskId)) {
          // 确保任务存在
          gantt.close(taskId)
        }
      })
    })
  }

  // 展开甘特图
  const handleMainUnitExpand = () => {
    isMainUnitCollapse.value = false
    // 使用批量操作优化性能
    gantt.batchUpdate(() => {
      const rootTasks = gantt.getChildren(0)
      rootTasks.forEach((taskId) => {
        if (gantt.isTaskExists(taskId)) {
          gantt.open(taskId)
          const children = gantt.getChildren(taskId)
          children.forEach((child) => {
            gantt.open(child)
          })
        }
      })
    })
  }

  // 是否折叠grid
  const isMainUnitCollapseGrid = ref(false)
  // 折叠grid
  const handleMainUnitCollapseGrid = () => {
    isMainUnitCollapseGrid.value = true
    gantt.config.show_grid = false
    gantt.render()
  }
  // 展开grid
  const handleMainUnitExpandGrid = () => {
    isMainUnitCollapseGrid.value = false
    gantt.config.show_grid = true
    gantt.render()
  }

  return {
    handleMainUnitGantt,
    isViewMainUnitGantt,
    isMainUnitCollapse,
    handleMainUnitCollapse,
    handleMainUnitExpand,
    isMainUnitCollapseGrid,
    handleMainUnitCollapseGrid,
    handleMainUnitExpandGrid,
    dynamicColumn,
  }
}
