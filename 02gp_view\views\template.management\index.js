import { deleteTemplate, enableTemplate, queryTemplateList } from '../../api/index.js'
import { useTemplateTable } from './useTemplateTable.js'
import TemplateAdd from './TemplateAdd.js'
import { useTableColumn } from '../../hooks/useTableColumn.js'
import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'

const { ref, nextTick, reactive, toRefs, defineAsyncComponent, onMounted } = Vue
export default {
  name: 'ProjectTemplate',
  components: {
    PagePager: defineAsyncComponent(() => import('../../components/PagePager.js')),
    HtDrawer: defineAsyncComponent(() => import('../../../components/ht.drawer.js')),
    TemplateAdd,
    HtVxeTable,
    TemplateDetial: defineAsyncComponent(() => import('./detial.js')),
  },
  setup() {
    // 引入表格相关的方法
    const { tableRef, isSelectOne, getSelectedId } = useTemplateTable()
    // project模板表格列
    const { getProjectTemplateColumns } = useTableColumn()
    const tableColumns = getProjectTemplateColumns()

    // define table state
    const tableState = reactive({
      tableData: [],
    })
    // define page state
    const pagePagerState = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 20,
    })
    /**
     * @description 获取表格数据通过前端分页
     * @param {number} currentPage - 当前页
     * @param {number} pageSize - 每页条数
     * @param {array} queryLists - 查询条件
     * @return {Promise<void>}
     */
    const getTableDataByFrontPage = async (currentPage, pageSize, queryLists = []) => {
      const tableData = await queryTemplateList(queryLists)
      const start = (currentPage - 1) * pageSize
      const end = currentPage * pageSize
      tableState.tableData = tableData.slice(start, end)
      pagePagerState.total = tableData.length
    }
    // 分页改变
    const handlePageChange = async ({ currentPage, pageSize }) => {
      pagePagerState.currentPage = currentPage
      pagePagerState.pageSize = pageSize
      await getTableDataByFrontPage(currentPage, pageSize)
    }
    // 筛选条件变化
    const handleFilterChange = async (params) => {
      const { filterList } = params
      const queryLists = filterList.map((item) => {
        return {
          str_key: item.field,
          str_value: item.values[0] ?? item.datas[0],
        }
      })
      await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize, queryLists)
    }

    onMounted(async () => {
      await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize)
    })

    // define add drawer state
    const drawerState = reactive({
      visible: false,
      title: '',
      isShowSaveBtn: true,
    })
    //#region  查看
    const templateViewRef = ref(null)

    /**查看 */
    const drawerStateView = reactive({
      visible: false,
      title: '查看',
      isShowSaveBtn: false,
      id: null,
    })
    /**查看 */
    const handleViewClick = () => {
      nextTick(() => {
        const selectedRow = isSelectOne()
        if (!selectedRow) {
          return
        }
        drawerStateView.id = selectedRow.id

        drawerStateView.visible = true
      })
    }
    //#endregion
    const templateAddRef = ref(null)
    // 新增按钮点击事件
    const handleAddClick = () => {
      drawerState.visible = true
      drawerState.title = '新增'
      drawerState.isShowSaveBtn = true
    }
    // 修改按钮点击事件
    const handleEditClick = () => {
      const selectedRow = isSelectOne()
      if (!selectedRow) {
        return
      }
      const id = selectedRow.id
      drawerState.visible = true
      drawerState.title = '修改'
      drawerState.isShowSaveBtn = true
      nextTick(() => {
        templateAddRef.value.getFormDataById(id)
      })
    }

    // 保存和修改模板
    const saveAndEditTemplate = async () => {
      if (!templateAddRef.value) {
        return
      }
      let boolean = false
      if (drawerState.title === '新增') {
        boolean = await templateAddRef.value.addAndEditTemplate('add')
      } else {
        boolean = await templateAddRef.value.addAndEditTemplate('edit')
      }
      if (!boolean) return
      drawerState.visible = false
      await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize)
    }
    /**
     * @description 修改状态
     * @param {object} row - 行数据
     */
    const changeState = async (row) => {
      const { id, is_state } = row
      const res = await ElementPlus.ElMessageBox.confirm(`是否${is_state == 1 ? '停用' : '启用'}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      if (res === 'confirm') {
        const dealType = is_state === 1 ? 0 : 1
        const lists = [id]
        const boolean = await enableTemplate(dealType, lists)
        if (!boolean) return
        await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize)
      }
    }

    /**
     * @description 删除模板
     */
    const delTemplate = () => {
      const ids = getSelectedId()
      ElementPlus.ElMessageBox.confirm('是否删除模板', '提示', {
        type: 'warning',
      }).then(async () => {
        const boolean = await deleteTemplate(ids)
        if (!boolean) return
        await getTableDataByFrontPage(pagePagerState.currentPage, pagePagerState.pageSize)
      })
    }

    return {
      tableRef,
      tableColumns,
      handleAddClick,
      handleEditClick,
      templateAddRef,
      ...toRefs(tableState),
      handleFilterChange,
      ...toRefs(pagePagerState),
      drawerState,
      handlePageChange,
      saveAndEditTemplate,
      changeState,
      delTemplate,
      templateViewRef,
      drawerStateView,
      handleViewClick,
    }
  },
  /*html*/
  template: `
    <!--    头部按钮-->
    <div class="flex items-center flex-wrap">
      <article class="mx-4 my-2">
        <el-button type="primary" @click="handleAddClick">新增</el-button>
        <el-button type="info" @click="handleViewClick" >查看</el-button>
        <el-button type="warning"  @click="handleEditClick">修改</el-button>
        <el-button type="danger" @click="delTemplate">删除</el-button>
      </article>
    </div>
    <div class="border-b-2 mb-2"></div>
    <!--    vxe表格-->
    <div class="mx-4" style="height: calc(100vh - 140px);">
      <HtVxeTable
        ref="tableRef"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :remote="true"
        @filter-change="handleFilterChange"
      >
        <template #checkbox>
          <vxe-column type="checkbox" width="80" fixed="left"></vxe-column>
        </template>
        <template #operation>
          <vxe-column title="操作" width="100">
            <template #default="{row}">
              <el-button size="small" @click="changeState(row)">
                <span v-if="row.is_state === 1" style="color: red">停用</span>
                <span v-else style="color: green">启用</span>
              </el-button>
            </template>
          </vxe-column>
        </template>
      </HtVxeTable>
      <div class="border-b-2 my-2"></div>
      <PagePager
        :currentPage
        :pageSize
        :total
        @pageChange="handlePageChange"
      ></PagePager>
    </div>

    <!--    新增和编辑抽屉-->
    <HtDrawer
      v-model:visible="drawerState.visible"
      :title="drawerState.title"
      :isShowSave="drawerState.isShowSaveBtn"
      @save="saveAndEditTemplate"
    >
      <TemplateAdd ref="templateAddRef"></TemplateAdd>
    </HtDrawer>

     <!--查看-->
     <HtDrawer
     v-model:visible="drawerStateView.visible"
     :title="drawerStateView.title"
     :isShowSave="drawerStateView.isShowSaveBtn"
   >
     <TemplateDetial  ref="templateViewRef" :id="drawerStateView.id"></TemplateDetial>
     <!--<TemplateAdd  ref="templateViewRef" :id="drawerStateView.id" :blAddFlow="false"></TemplateAdd>-->
     
   </HtDrawer>
  `,
}
