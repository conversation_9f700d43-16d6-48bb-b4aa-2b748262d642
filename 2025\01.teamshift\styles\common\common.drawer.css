.common-drawer {
  /* 抽屉整体样式 */
  .el-drawer {
    border-radius: 8px 0 0 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .el-drawer__header {
    margin: 0;
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 0 !important;
    flex-shrink: 0;

    .el-drawer__title {
      font-size: 15px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-drawer__body {
    padding: 16px;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
    flex: 1;
    overflow-y: auto;
    /* 添加底部内边距，防止内容被footer遮挡 */
    padding-bottom: 76px;

    /* 兼容Firefox的滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #dcdfe6 #f5f7fa;

    /* Webkit浏览器滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
      &:hover {
        background-color: #c0c4cc;
      }
    }
    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
  }

  .el-drawer__footer {
    margin: 0;
    padding: 12px 16px;
    border-top: 1px solid #e4e7ed;
    background-color: #f5f7fa;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    box-sizing: border-box;
    z-index: 1000;
    flex-shrink: 0;
  }

  /* 抽屉动画效果 */
  &.el-drawer-fade-enter-active {
    animation: drawerSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@keyframes drawerSlideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
