import HtVxeTable from '../../components/VxeTable/HtVxeTable.js'
import { SetOrderDialog } from './components/SetOrderDialog.js'
import { AddTaskDialog } from './components/AddTaskDialog.js'
import { SelectSmDialog } from './components/SelectSmDialog.js'
import { useTaskTemplate } from './composables/useTaskTemplate.js'

const { defineComponent } = Vue

export default defineComponent({
  name: 'ProjectTemplate',
  components: {
    HtVxeTable,
    AddTaskDialog,
    SetOrderDialog,
    SelectSmDialog,
  },
  props: {
    flow: { type: String },
    group: { type: String },
    engine_type: { type: String },
  },
  setup(props) {
    // 使用提取出的业务逻辑composable
    const taskTemplateState = useTaskTemplate(props)

    // 将所有状态和方法提供给UI层
    return {
      ...taskTemplateState,
    }
  },
  template: /*html*/ `
    <!--    头部按钮-->
    <div class="flex flex-wrap items-center">
      <article class="mx-4 my-2">
        <el-button type="warning" @click="handleEditClick">修改</el-button>
        <el-button type="primary" @click="handleSetOrder">设置顺序</el-button>
        <el-button type="primary" @click="handleSendTask">下发</el-button>
       <el-button type="warning" @click="handleCancelSendTask">取消下发</el-button>
       <el-button type="warning" @click="handleCompleteTask">完成</el-button>
       <el-button type="primary" @click="handleSendTask({is_issued:2})">返工下发</el-button>
      </article>
    </div>
    <div class="mb-2 border-b-2"></div>
    <!--    vxe表格-->
    <div class="mx-4" style="height: calc(100vh - 140px);">
      <HtVxeTable
        ref="tableRef"
        :tableData="tableData"
        :tableColumns="tableColumns"
        :remote="true"
        :is-show-header-checkbox="true"
        @filter-change="handleFilterChange"
      >
        <template #checkbox>
          <vxe-column type="checkbox" width="80" fixed="left"></vxe-column>
        </template>
      </HtVxeTable>
      <div class="my-2 border-b-2"></div>
      <vxe-pager
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        @page-change="handlePageChange"
      ></vxe-pager>
    </div>

    <el-drawer class="common-drawer" v-model="isShowEditDrawer" title="修改" size="80%">
      <div class="p-4">
        <el-table
          :data="editTableData"
          row-key="id"
          :tree-props="{ children: 'children' }"
          :expand-row-keys="levelids"
          @row-click="handleRowClick"
          @expand-change="handleExpandChange"
        >
          <el-table-column prop="str_name" label="名称" min-width="200" > 
          <template #default="{ row }"> {{row.str_name}}
         
          <span  v-if="row.str_level=='3' && row.int_type!=0 " style="color:red">-非生产</span>
          </template>
          </el-table-column>
          
          <el-table-column prop="str_predecessors" label="前置任务" />
          <el-table-column prop="str_pre_model" label="前置单元体" />
          <el-table-column prop="int_sort" label="排序" />
          <el-table-column prop="int_status" label="状态">
            <template #default="{ row } ">
              <span v-if="row.str_level === '2' && row.int_status=='1'">已完成</span>
              <span v-if="row.str_level === '3' && row.int_status=='1'">已取消</span>
              <span v-if="row.int_status=='0'"></span>
            </template>
          </el-table-column>
           <el-table-column prop="dt_plan" label="排班日期" />
           <el-table-column prop="int_task_status" label="反馈" > 
           <template #default="{ row }">
             <span v-if="row.int_task_status==0"  style="color:green"></span>
           <span v-if="row.int_task_status==1"  style="color:green">绿</span>
           <span v-if="row.int_task_status==-1" style="color:#FFD700">黄</span>
           <span v-if="row.int_task_status==-2" style="color:red">红</span>
           </template>
          </el-table-column>
          <el-table-column prop="int_task_status" label="完成" > 
           <template #default="{ row }">
              <span v-if="row.str_level==3" >{{row.is_task_complete?'是':'否'}}</span>
           </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button v-if="row.str_level === '2' || row.str_level === '1'" text type="primary" @click.stop="handleAddTask(row)">
                ADD
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>

    <el-dialog v-model="editDialogVisible" title="单元体/任务编辑" size="80%">
      <div class="p-4">
        <el-form :model="currentEditRow" label-width="120px">
          <el-form-item label="名称">
            <el-input v-model="currentEditRow.str_name" disabled />
          </el-form-item>
          <el-form-item label="层级">
            <el-input v-model="currentEditRow.str_level" disabled />
          </el-form-item>
          <el-form-item v-if="currentEditRow.str_level === '2' " :label="currentEditRow.str_level === '2' ? '单元体完成' : '是否取消'">
            <el-checkbox v-model="currentEditRow.int_sm_status" :true-value="1" :false-value="0" />
          </el-form-item>
          <div  v-if="currentEditRow.str_level === '3'">
           <el-form-item label="是否取消">
            <el-checkbox v-model="currentEditRow.int_type" :true-value="1" :false-value="0" />
          </el-form-item>
          <el-form-item v-if="currentEditRow.str_level === '3'" label="班次任务完成">
            <el-checkbox v-model="currentEditRow.is_task_complete" :true-value="1" :false-value="0" />
          </el-form-item>
           <el-form-item label="班次任务状态">
             <el-select v-model="currentEditRow.int_task_status"  clearable>
                <el-option :value="1" label="绿" ><span style="color:green;font-size:14:">绿</span></el-option>
                <el-option :value="-1" label="黄" ><span style="color:#FFD700;font-size:14:">黄</span></el-option>
                <el-option :value="-2" label="红" ><span style="color:red;font-size:14:">红</span></el-option>
            </el-select>
          </el-form-item>
           </div>
          <el-form-item label="排序">
            <el-input-number v-model="currentEditRow.int_sort" :min="1" :disabled="currentEditRow.str_level !== '2'" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleEditSave">保存</el-button>
        <el-button type="danger" @click="editDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <AddTaskDialog
      v-if="addDialogVisible"
      v-model:modelValue="addDialogVisible"
      :current-add-row="currentAddRow"
      :id-wo="currentRow?.id_wo"
      @task-added="getEditData"
    />
    <SetOrderDialog
      v-if="isSetOrderVisible"
      v-model:modelValue="isSetOrderVisible"
      :id="currentSetOrderRow?.id"
      :id-wo="currentSetOrderRow?.id_wo"
      :group="group"
      :flow="currentSetOrderRow?.str_flow"
      :current-sort="currentSetOrderRow?.sort"
      :engine-type="currentSetOrderRow?.engine_type"
      @refresh="getTableData"
    />
    <SelectSmDialog
      v-if="selectSmDialogVisible"
      v-model:modelValue="selectSmDialogVisible"
      :current-add-row="currentSmRow"
      :id-wo="currentRow?.id_wo"
      :str-flow="currentRow?.str_flow"
      @sm-selected="getEditData"
    />

  <el-dialog class="common-dialog" v-model="formSendTask.is_visible" :title="formSendTask.title" width="20%" >
   <el-date-picker
        v-model="formSendTask.selectedDate"
        type="date"
        value-format="YYYY-MM-DD"
        placeholder="启动日期"
        :size="size"
      />
    <template #footer>
      <div class="dialog-footer">
        <el-button  @click="formSendTask.is_visible = false">取消</el-button>
         <el-button type="primary" @click="handleIssued()">下发</el-button>
      </div>
    </template>
  </el-dialog>
  `,
})
