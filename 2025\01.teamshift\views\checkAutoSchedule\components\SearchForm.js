import { getTeamList, getStaffList } from '../api/index.js'
const { ref, defineComponent, onMounted } = Vue
const { useVModel } = VueUse

export default defineComponent({
  name: 'SearchForm',
  props: {
    searchForm: {
      type: Object,
      required: true,
    },
  },
  emits: ['update:searchForm', 'search'],
  setup(props, { emit }) {
    const searchFormModel = useVModel(props, 'searchForm', emit)
    const isExpanded = ref(false)

    const handleSearch = () => {
      emit('search')
    }

    const handleReset = () => {
      // 重置表单内容，但保留日期范围
      const date = searchFormModel.value.date || []
      searchFormModel.value = {
        date,
        str_esn: '',
        str_wo: '',
        str_type: [],
        str_team: [],
        str_sm: '',
        id_staff: [],
        int_check_type:'',
        is_finish:''
      
      }
      emit('search')
    }

    const toggleAdvanced = () => {
      isExpanded.value = !isExpanded.value
    }

    const teamOptions = ref([])
    /**
     * 获取Team下拉框数据
     */
    const getTeamOptions = async () => {
      teamOptions.value = await getTeamList('RUPI')
    }

    const staffOptions = ref([])
    /**
     * 获取Staff下拉框数据
     */
    const getStaffOptions = async () => {
      const params = {
        id_dept: '',
        id_team: searchFormModel.value.str_team,
        str_dept: 'RUPI',
      }
      staffOptions.value = await getStaffList(params)
    }

    onMounted(() => {
      getTeamOptions()
      getStaffOptions()
    })

    return {
      searchFormModel,
      isExpanded,
      teamOptions,
      staffOptions,
      handleSearch,
      handleReset,
      toggleAdvanced,
    }
  },
  template: /*html */ `
    <!-- 表单内容 -->
    <div>
      <el-form :inline="true" :model="searchFormModel" class="flex flex-wrap items-center">
        <!-- 基础搜索区域 - 一行展示 -->
        <el-form-item label="日期" class="mb-2 mr-4">
          <el-date-picker
            v-model="searchFormModel.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            class="w-auto"
          />
        </el-form-item>

        <el-form-item label="ESN" class="mb-2 mr-4">
          <el-input v-model="searchFormModel.str_esn" placeholder="请输入ESN" clearable class="w-44" />
        </el-form-item>

        <el-form-item label="WO" class="mb-2 mr-4">
          <el-input v-model="searchFormModel.str_wo" placeholder="请输入WO" clearable class="w-44" />
        </el-form-item>

        <el-form-item label="Type" class="mb-2 mr-4">
          <el-select
            v-model="searchFormModel.str_type"
            placeholder="请选择Type"
            multiple
            clearable
            filterable
            class="w-44"
          >
            <el-option label="Core" value="Core" />
            <el-option label="FAN" value="FAN" />
            <el-option label="LPT" value="LPT" />
            <el-option label="B1" value="B1" />
          </el-select>
        </el-form-item>

        <el-form-item label="Team" class="mb-2 mr-4">
          <el-select
            v-model="searchFormModel.str_team"
            placeholder="请选择Team"
            clearable
            filterable
            multiple
            class="w-44"
          >
            <el-option v-for="item in teamOptions" :key="item.str_key" :label="item.str_name" :value="item.str_key" />
          </el-select>
        </el-form-item>

        <el-form-item label="SM" class="mb-2 mr-4">
          <el-input v-model="searchFormModel.str_sm" placeholder="请输入SM" clearable class="w-44" />
        </el-form-item>

        <el-form-item label="Staff" class="mb-2 mr-4">
          <el-select
            v-model="searchFormModel.id_staff"
            placeholder="请选择Staff"
            clearable
            filterable
            multiple
            class="w-44"
          >
            <el-option v-for="item in staffOptions" :key="item.id" :label="item.str_name" :value="item.id" />
          </el-select>
        </el-form-item>

         <el-form-item label="CheckType" class="mb-2 mr-4">
          <el-select
            v-model="searchFormModel.int_check_type"
            placeholder="请选择检验类型" 
            clearable
            filterable
            class="w-44"
          >
            <el-option label="放行检验" value="1" />
            <el-option label="过程检验" value="0" /> 
          </el-select>
        </el-form-item>
        <el-form-item label="放行标签" class="mb-2 mr-4">
          <el-select
            v-model="searchFormModel.is_finish"
            placeholder="请选择是/否" 
            clearable
            filterable
            class="w-44"
          >
            <el-option label="已开具" value="1" />
            <el-option label="未开具" value="0" /> 
          </el-select>
        </el-form-item>
        <!-- 操作按钮 -->
        <div class="mb-2 flex items-center">
          <el-button @click="handleReset" icon="Refresh">重置</el-button>
          <el-button type="primary" @click="handleSearch" icon="Search">查询</el-button>
        </div>
      </el-form>
    </div>
  `,
})
