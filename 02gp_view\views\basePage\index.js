import { post } from '../../../config/axios/httpReuest.js'
import { useSelection } from '../../hooks/useSelection.js'
import { useTableColumn } from '../../hooks/useTableColumn.js'
import { useConversion } from './useConversion.js'
import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'

const { defineAsyncComponent, reactive, toRefs, onMounted, ref, nextTick, watch } = Vue
const BasePage = {
  components: {
    HtVxeTable,
    PagePager: defineAsyncComponent(() => import('../../components/PagePager.js')),
    HtDrawer: defineAsyncComponent(() => import('../../../components/ht.drawer.js')),
    BasePageAddAndEdit: defineAsyncComponent(() => import('./BasePageAddAndEdit.js')),
    BasePageView: defineAsyncComponent(() => import('./view.js')),
  },
  setup() {
    const { getTaskBaseDataColumns } = useTableColumn()
    // 表格组件的dom
    const basePageTableRef = ref(null)
    // 表格数据
    const tableState = reactive({
      tableData: [],
      tableColumns: getTaskBaseDataColumns(),
    })
    const queryListsComm=ref(null)
    const { apiToTableData, fieldConversion } = useConversion()
    // 获取表格数据
    const getTableData = async (queryLists) => {
      const tableData = []
      const param = {
        ac: 'gp_task_info_search',
        queryLists,
      }
      const { data } = await post(param)
      tableData.push(...apiToTableData(data.data))
      if (data.code === 'error') {
        ElementPlus.ElMessage.error(data.text)
        return []
      }
      return tableData
    }
    // 获取表格数据通过前端分页
    const getTableDataByFrontPage = async (currentPage, pageSize, queryLists = []) => {
      const tableData = await getTableData(queryLists)
      const start = (currentPage - 1) * pageSize
      const end = currentPage * pageSize
      tableState.tableData = tableData.slice(start, end)
      pagerState.total = tableData.length
    }

    // 分页数据
    const pagerState = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
    })
    // 分页改变
    const handlePageChange = async ({ currentPage, pageSize }) => {
      pagerState.currentPage = currentPage
      pagerState.pageSize = pageSize
      await getTableDataByFrontPage(pagerState.currentPage, pagerState.pageSize)
    }

    // 新增和编辑抽屉Dom
    const basePageAddAndEditRef = ref(null)
    // 新增和编辑抽屉数据
    const addAndEditDrawerState = reactive({
      isShowSave: true,
      isShowDrawer: false,
      title: '新增',
    })

    // 查看抽屉数据
    const viewDrawerState = reactive({
      isShowDrawer: false,
      title: '查看',
      isShowSave: false,
    })
    // 关闭抽屉，清除表格所有状态，恢复至初始状态
    const handleClear = () => {
      addAndEditDrawerState.isShowDrawer = false
    }

    const { hasSelectedData, hasSelectedOneData } = useSelection()

    // 新增按钮点击事件
    const handleAdd = () => {
      addAndEditDrawerState.title = '新增'
      addAndEditDrawerState.isShowDrawer = true
      addAndEditDrawerState.isShowSave = true
    }
    // 修改按钮点击事件
    const handleEdit = () => {
      const selectedData = hasSelectedData(basePageTableRef, 'getSelectedData')
      const oneSelectedData = hasSelectedOneData(selectedData)
      if (!oneSelectedData) {
        return
      }
      addAndEditDrawerState.title = '修改'
      addAndEditDrawerState.isShowSave = true
      addAndEditDrawerState.isShowDrawer = true
    }

    const deleteApi = async (lists) => {
      const param = {
        ac: 'gp_task_info_del',
        lists,
      }
      const { data } = await post(param)
      if (data.code === 'error') {
        ElementPlus.ElMessage.error(data.text)
        return false
      }
      ElementPlus.ElMessage.success(data.text)
      return true
    }

    /**
     * @description 修改状态
     * @param {object} row - 行数据
     */
    const changeState = async (row) => {
      const { id, int_state } = row
      const dealType = int_state === 1 ? 0 : 1
      const lists = [id]
      const res = await ElementPlus.ElMessageBox.confirm(`是否${int_state == 1 ? '停用' : '启用'}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      if (res === 'confirm') {
        const param = {
          ac: 'gp_task_info_active',
          id,
          int_state: dealType,
        }
        const { data } = await post(param)
        if (data.code === 'error') {
          return
        }
        getTableDataByFrontPage(pagerState.currentPage, pagerState.pageSize)
      }
    }

    // 删除按钮点击事件
    const handleDelete = () => {
      const selectedData = hasSelectedData(basePageTableRef, 'getSelectedData')
      if (!selectedData) return
      ElementPlus.ElMessageBox.confirm('此操作将永久删除选中数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const lists = selectedData.map((item) => item.id)
          deleteApi(lists).then((res) => {
            if (!res) return
            handleClear()
            getTableDataByFrontPage(pagerState.currentPage, pagerState.pageSize)
          })
        })
        .catch(() => {
          ElementPlus.ElMessage.info('已取消删除')
        })
    }
    const basePageViewRef = ref(null)
    // 查看按钮点击事件
    const handleDetail = () => {
      const selectedData = hasSelectedData(basePageTableRef, 'getSelectedData')
      const oneSelectedData = hasSelectedOneData(selectedData)
      if (!oneSelectedData) {
        return
      }
      viewDrawerState.title = '查看'
      viewDrawerState.isShowSave = false
      viewDrawerState.isShowDrawer = true
    }

    /**
     * 监听新增和编辑抽屉的显示状态
     * 1. 新增和编辑抽屉是同一个组件，所以需要在抽屉显示的时候，调用新增和编辑抽屉的方法
     * 2. 抽屉组件和添加编辑组件都是异步组件, 当你打开抽屉的时候，添加编辑组件还没有加载完成
     * 3. 监听dom变化，当dom变化的时候，并且不是添加,调用编辑组件的方法
     */
    watch(basePageAddAndEditRef, (newVal) => {
      if (newVal && addAndEditDrawerState.title !== '新增') {
        // 获取表格当前选中的数据
        const selectedData = basePageTableRef.value.getSelectedData()
        // 取第一个选中的数据
        const id = selectedData[0].id
        // 异步调用添加编辑组件的方法
        nextTick(() => {
          basePageAddAndEditRef.value.getEditForm(addAndEditDrawerState.title, id)
        })
      }
    })

    watch(basePageViewRef, (newVal) => {
      if (newVal) {
        // 获取表格当前选中的数据
        const selectedData = basePageTableRef.value.getSelectedData()
        // 取第一个选中的数据
        const id = selectedData[0].id
        // 异步调用添加编辑组件的方法
        nextTick(() => {
          basePageViewRef.value.getDetailData(id).then()
        })
      }
    })

    // 保存和更新
    const handleSaveAndUpdate = async () => {
      const response = await basePageAddAndEditRef.value.saveAndUpdate(addAndEditDrawerState.title)
      if (!response) return
      handleClear()
      await getTableDataByFrontPage(pagerState.currentPage, pagerState.pageSize,queryListsComm)
    }

    /**
     * @description 过滤条件改变
     * @param { object } panel
     */
    const handleFilterChange = async (panel) => {
      const { filterList } = panel
      const fieldMap = fieldConversion()
      const queryLists = filterList.map((item) => {
        return {
          str_key: fieldMap[item.field] ?? item.field,
          str_value: item.values[0] ?? item.datas[0],
        }
      })
      queryListsComm.value=queryLists;
      await getTableDataByFrontPage(pagerState.currentPage, pagerState.pageSize, queryLists)
    }

    onMounted(async () => {
      await getTableDataByFrontPage(pagerState.currentPage, pagerState.pageSize)
    })

    return {
      basePageTableRef,
      ...toRefs(tableState),
      ...toRefs(pagerState),
      handlePageChange,
      handleAdd,
      handleEdit,
      handleDelete,
      handleDetail,
      addAndEditDrawerState,
      handleClear,
      basePageAddAndEditRef,
      handleSaveAndUpdate,
      viewDrawerState,
      basePageViewRef,
      handleFilterChange,
      changeState,
      queryListsComm
    }
  },
  template: /*html*/ `
    <!--    头部按钮-->
    <div class="flex flex-wrap items-center">
      <article class="mx-4 my-2">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="info" @click="handleDetail">查看</el-button>
        <el-button type="warning" @click="handleEdit">修改</el-button>
        <!-- <el-button type="danger" @click="handleEdit">启用|占用</el-button>-->
        <!--    <el-button type="danger" @click="handleDelete">删除</el-button>-->
      </article>
    </div>
    <div class="mb-2 border-b-2"></div>
    <!--    任务基础数据表格-->
    <div class="mx-4" style="height: calc(100vh - 140px);">
      <HtVxeTable ref="basePageTableRef" :tableData :tableColumns :remote="true" @filterChange="handleFilterChange">
        <template #checkbox>
          <vxe-column type="checkbox" width="80" fixed="left"></vxe-column>
        </template>
        <template #operation>
          <vxe-column title="操作" width="100">
            <template #default="{row}">
              <el-button size="small" @click="changeState(row)">
                <span v-if="row.int_state === 1" style="color: red">停用</span>
                <span v-else style="color: green">启用</span>
              </el-button>
            </template>
          </vxe-column>
        </template>
      </HtVxeTable>
      <PagePager
        :currentPage="currentPage"
        :pageSize="pageSize"
        :total="total"
        @pageChange="handlePageChange"
      ></PagePager>
    </div>

    <!--    新增和编辑抽屉-->
    <HtDrawer
      v-model:visible="addAndEditDrawerState.isShowDrawer"
      :title="addAndEditDrawerState.title"
      width="50%"
      :is-show-save="addAndEditDrawerState.isShowSave"
      @clear="handleClear"
      @save="handleSaveAndUpdate"
    >
      <BasePageAddAndEdit ref="basePageAddAndEditRef"></BasePageAddAndEdit>
    </HtDrawer>

    <!--    查看抽屉-->
    <HtDrawer
      v-model:visible="viewDrawerState.isShowDrawer"
      :title="viewDrawerState.title"
      width="50%"
      :is-show-save="viewDrawerState.isShowSave"
      @clear="handleClear"
    >
      <BasePageView ref="basePageViewRef"></BasePageView>
    </HtDrawer>
  `,
}
export default BasePage
