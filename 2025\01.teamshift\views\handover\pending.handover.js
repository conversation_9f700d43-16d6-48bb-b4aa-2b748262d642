import HtVxeTable from '../../components/VxeTable/HtVxeTable.js'
import { queryHandOverData, queryHandoverStart, deleteHandover,exportHandover } from '../../api/handover/index.js'
import ReceiveHandover from './receive.handover.page.js'
import HandoverAdd from './components/HandoverAdd.js'
import { useTableColumns } from './composables/useTableColumns.js'
import StartWorkDialog from './components/StartWorkDialog.js'
const { onMounted, onUnmounted, nextTick } = Vue
const { ElMessage, ElMessageBox } = ElementPlus
export default {
  name: 'PendingHandover',
  components: {
    HtVxeTable,
    ReceiveHandover,
    HandoverAdd,
    StartWorkDialog,
  },
  props: {
    status: String,
    idWo: String,
    // 是否是开工
    strFlow: {
      type: String,
      default: 'flow',
    },
    flow: String,
    // 是否是开工
    isStart: {
      type: Boolean,
      default: false,
    },
    startWorkRow: {
      type: Object,
      default: () => {},
    },
    business: {
      type: [Number, String],
      default: '',
    },
    currentRow: {
      type: Object,
      default: () => {},
    },
    handoverType: {
      type: String,
      default: '',
    },
    isShowHandoverType: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const { ref, provide } = Vue
    const loading = ref(true)
    const tableData = ref([])

    const { tableColumns } = useTableColumns(props.business)

    provide('businessType', props.business)

    // 添加分页相关数据
    const pagination = ref({
      currentPage: 1,
      pageSize: 10,
      total: 0,
    })

    // 添加表格高度和容器引用
    const tableHeight = ref('400px')
    const tableContainerRef = ref(null)

    // 接收交接弹窗相关
    const receiveDialogVisible = ref(false)
    const currentRow = ref({})
    const currentPlanItem = ref({})

    const searchForm = ref({})

    // 获取表格数据
    const getTableData = async () => {
      loading.value = true
      const str_handover_type = props.business
      // if(props.business='my') {
      //   str_handover_type=''
      // }
      let param = {
        CurrentPage: pagination.value.currentPage,
        PageSize: pagination.value.pageSize,
        str_handover_type: str_handover_type,
        ...searchForm.value,
      }
      try {
        let res = null
        if (props.isStart) {
          // 获取当前日期
          const currentDate = moment().format('YYYY-MM-DD')
          if (props.strFlow === 'F2') {
            const startWorkRow = props.startWorkRow.plans.find((item) =>
              moment(item.dt_shift).isSame(currentDate, 'day'),
            )
            const idWos = startWorkRow.task.map((item) => item.id_wo).join(',')
            param.id_wo = idWos
            const id_by_receive = startWorkRow.id_staff
            param.id_by_receive = id_by_receive
          } else {
            param.id_wo = props.idWo
            // 从startWorkRow中获取当前日期的数据
            const startWorkRow = props.startWorkRow.plan.find((item) =>
              moment(item.plan_date).isSame(currentDate, 'day'),
            )
            // 从startWorkRow的task中获取id_sms
            if (!startWorkRow) {
              ElMessage.error('当前日期没有任务')
            }
            const idSms = startWorkRow.task.map((item) => item.modelId).join(',')
            const idtasks = startWorkRow.task.map((item) => item.taskId).join(',')
            param.id_sms = idSms
            param.id_tasks = idtasks
          }
          param.id_shift = props.startWorkRow.id_shift
          param.str_task_type = props.isShowHandoverType ? props.startWorkRow.str_task_type : ''
          param.str_handover_type = props.handoverType
          res = await queryHandoverStart(param)
        } else {
          if (props.strFlow === 'F2') {
            param.id_by_receive = props.startWorkRow.id_staff
          } else {
            param.id_wo = props.idWo
          }
          res = await queryHandOverData(param)
        }
        // const res = await queryHandOverData(param)
        tableData.value = res.items
        pagination.value.total = res.totalCount || 0
      } catch (error) {
        // ElMessage.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }

    // 处理页码变化
    const handleCurrentChange = () => {
      getTableData()
    }

    // 窗口大小变化监听器
    let resizeObserver = null

    // 计算表格高度
    const calculateTableHeight = () => {
      nextTick(() => {
        // 获取窗口高度
        let windowHeight = window.innerHeight

        // 检查是否在iframe中
        const isInIframe = window !== window.parent

        if (isInIframe) {
          try {
            // 尝试获取iframe的高度
            const iframe = window.frameElement
            if (iframe) {
              windowHeight = iframe.clientHeight
            }
          } catch (e) {
            console.error('获取iframe高度失败:', e)
          }
        }

        // 获取表格容器元素
        const tableContainer = tableContainerRef.value
        if (!tableContainer) return

        // 获取表格容器到视口顶部的距离
        const containerTop = tableContainer.getBoundingClientRect().top

        // 获取分页器高度
        const paginationHeight = 60

        // 计算表格可用高度 (窗口高度 - 表格顶部位置 - 分页器高度 - 底部边距)
        const availableHeight = windowHeight - containerTop - paginationHeight - 60

        // 设置最小高度
        const minHeight = 300

        // 更新表格高度
        tableHeight.value = `${Math.max(availableHeight, minHeight)}px`
      })
    }

    // 接收处理函数
    const handleHandover = (row) => {
      // 打开接收交接弹窗
      currentRow.value = row
      // 获取计划项信息，这里假设从row中获取
      currentPlanItem.value = {
        plan_date: row.handover_date, // 使用交接日期作为计划日期
      }
      receiveDialogVisible.value = true
    }

    onMounted(async () => {
      await getTableData()

      // 初始计算表格高度
      calculateTableHeight()

      // 监听窗口大小变化
      resizeObserver = new ResizeObserver(calculateTableHeight)
      window.addEventListener('resize', calculateTableHeight)

      // 如果在iframe中，尝试监听iframe大小变化
      if (window !== window.parent) {
        try {
          const iframe = window.frameElement
          if (iframe && resizeObserver) {
            resizeObserver.observe(iframe)
          }
        } catch (e) {
          console.error('监听iframe大小变化失败:', e)
        }
      }

      // 监听表格容器大小变化
      if (tableContainerRef.value && resizeObserver) {
        resizeObserver.observe(tableContainerRef.value)
      }
    })

    // 组件卸载时清理监听器
    onUnmounted(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', calculateTableHeight)

      // 断开ResizeObserver连接
      if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
      }
    })

    // 新增交接班弹窗
    const addHandoverVisible = ref(false)
    const addHandoverFlow = ref({})
    const handleAddHandover = (flow, type) => {
      addHandoverFlow.value = { flow: flow, type: type }
      addHandoverVisible.value = true
    }

    const isEdit = ref(false)
    const currentEditRow = ref({})
    const handleEdit = (row) => {
      isEdit.value = true
      addHandoverFlow.value.type = parseInt(row.str_handover_type)
      currentEditRow.value = row
      addHandoverVisible.value = true
    }
    const handClose = () => {
      isEdit.value = false
      addHandoverVisible.value = false
    }
    const handleDelate = (row) => {
      ElMessageBox.confirm('确定删除该交接班记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const res = await deleteHandover(row.id)
        ElMessage.success('删除成功')
        getTableData()
      })
    }
    const handleExport = async (row) => {
      const res = await exportHandover(row.id)
      const origin = window.location.origin
      const filePath = `${origin}/${res.replace("wwwroot/","")}`
      // 创建一个a标签用于下载
      const a = document.createElement('a')
      a.href = filePath
      a.download = row.str_esn+'交接.docx'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
    const handleFilterChange = (filter) => {
      searchForm.value = {}
      filter.filterList.forEach((element) => {
        searchForm.value[element.field] = element.datas[0] ?? element.values[0]
      })
      getTableData()
    }

    const isOpenStartDialog = ref(false)
    const handleStart = () => {
      isOpenStartDialog.value = true
    }

    // 刷新表格数据
    const handleRefresh = (isStart) => {
      getTableData()
    }

    return {
      isOpenStartDialog,
      tableData,
      tableColumns,
      pagination,
      isEdit,
      currentEditRow,
      tableHeight,
      tableContainerRef,
      receiveDialogVisible,
      currentRow,
      currentPlanItem,
      addHandoverVisible,
      addHandoverFlow,
      getTableData,
      handleHandover,
      handleAddHandover,
      handleCurrentChange,
      handleEdit,
      handClose,
      handleFilterChange,
      handleDelate,
      handleExport,
      handleStart,
      handleRefresh,
    }
  },
  template: /*html*/ `
    <div class="p-2" ref="tableContainerRef" :class="{ 'h-screen': !idWo }">
      <div v-if="!isStart" class="mb-2 flex flex-row justify-end gap-2">
        <el-button type="primary" v-if="business==101" @click="handleAddHandover('F1-2',101)">F1-2</el-button>
        <el-button type="primary" v-if="business==102" @click="handleAddHandover('F2',102)">F2</el-button>
        <el-button type="primary" v-if="business==103" @click="handleAddHandover('F1-1',103)">F1 B1交接</el-button>
        <el-button type="primary" v-if="business==104" @click="handleAddHandover('F1-1',104)">F1 B23交接</el-button>
        <el-button type="primary" v-if="business==105" @click="handleAddHandover('F4-1',105)">F4 B1交接</el-button>
        <el-button type="primary" v-if="business==106" @click="handleAddHandover('F4-1',106)">F4 B23交接</el-button>
        <el-button type="primary" v-if="business==107" @click="handleAddHandover('F1-1',107)">F1 B1检验交接</el-button>
        <el-button type="primary" v-if="business==108" @click="handleAddHandover('F1-1',108)">F1 B23检验交接</el-button>
        <el-button type="primary" v-if="business==109" @click="handleAddHandover('F4-1',109)">F4 B1检验交接</el-button>
        <el-button type="primary" v-if="business==110" @click="handleAddHandover('F4-1',110)">F4 B23检验交接</el-button>
        <el-button type="primary" @click="handleStart">开工</el-button>
      </div>
      <HtVxeTable
        :tableData="tableData"
        :tableColumns="tableColumns"
        :height="tableHeight"
        :remote="true"
        @filter-change="handleFilterChange"
      >
        <template #operation>
          <vxe-column title="操作" min-width="130" fixed="right" align="center">
            <template #default="{ row }">
              <div class="flex gap-2">
                <div
                  :disabled="row.int_status === 1 || row.is_completed"
                  v-if="business!='' && row.int_status !== 1 && row.int_handover_status !== 0 "
                  class="cursor-pointer text-blue-500"
                  @click="handleHandover(row)"
                >
                  接班
                </div>
                <div class="cursor-pointer text-blue-500" @click="handleHandover(row)">查看</div>
                <div
                  :disabled="row.int_status === 1"
                  v-if="business!='' && !isStart && row.int_status !== 1"
                  class="cursor-pointer text-blue-500"
                  @click="handleEdit(row)"
                >
                  交班
                </div>
                <div :disabled="row.int_status === 1 | row.is_completed" v-if="business!='' && !isStart && row.int_status !== 1" class="cursor-pointer text-blue-500" @click="handleDelate(row)">
                  删除
                </div>
                <div class="cursor-pointer text-blue-500" @click="handleExport(row)">
                  导出
                </div>
              </div>
            </template>
          </vxe-column>
        </template>
      </HtVxeTable>

      <!-- 添加分页组件 -->
      <div class="mt-4 flex justify-end">
        <vxe-pager
          background
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :layouts="['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']"
          @page-change="handleCurrentChange"
        ></vxe-pager>
      </div>

      <!-- 接收交接弹窗 -->
      <ReceiveHandover
        v-model:visible="receiveDialogVisible"
        :row="currentRow"
        :buinessType="business"
        @refresh="getTableData"
      />

      <!-- 新增交接班弹窗 -->
      <HandoverAdd
        v-if="addHandoverVisible"
        v-model:visible="addHandoverVisible"
        :flow="addHandoverFlow.flow"
        :type="addHandoverFlow.type"
        :isEdit="isEdit"
        :currentEditRow="currentEditRow"
        :businessType="business"
        @refresh="handleRefresh"
        @close="handClose"
      />

      <!-- 开工弹框 -->
       <StartWorkDialog
        v-if="isOpenStartDialog"
        v-model:visible="isOpenStartDialog"
        @refresh="handleRefresh"
      />
    </div>
  `,
}
