// export function useApi () {
  const requestDataApi = async (args) => {
    const initParams = {
      au: 'ssamc',
      ap: 'api2018',
      ak: '',
    }
    const params = Object.assign(initParams, args)
    const { data } = await axios.post(globalApiUrl, params)
    if (data.code === 'success') {
      return data.data ?? true
    } else {
      ElementPlus.ElMessage({
      message: data.text,
      type: 'error'
    })
    return false
  }
}

  // return {
  //   requestDataApi
  // }
// }
