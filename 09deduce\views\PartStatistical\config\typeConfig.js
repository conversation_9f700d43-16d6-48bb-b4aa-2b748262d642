export const TYPE_CONFIG = {
  int_total: { type: 0, title: '总量(不包含Keep Missing)' },
  hasekd: { type: 888, title: 'EKD数量' },
  int107: { type: 107, title: 'Keep Missing' },
  int116: { type: 116, title: '待CSM确认(锁库)' },
  int102: { type: 102, title: '待转包' },
  int112: { type: 112, title: '试车借件' },
  int104: { type: 104, title: '待构型确认' },
  int109: { type: 109, title: '转包无EDD' },
  int113: { type: 113, title: '未离开 Gate 1' },
  int103: { type: 103, title: 'CSM确认(进厂缺件)' },
  int115: { type: 115, title: '修理领料' },
  int114: { type: 114, title: '进入供应链后消失' },
  int105: { type: 105, title: '待锁库' },
  int117: { type: 117, title: '未发料' },
  int106: { type: 106, title: '消耗件' },
  int101: { type: 101, title: 'EKD计算中' },
  int111: { type: 111, title: '待采购' },
  int110: { type: 110, title: '备板' },
  int118: { type: 118, title: '锁库/需求关闭' },
  int119: { type: 119, title: '无PN号' },
  int120: { type: 120, title: '串件中' },
  int122: { type: 122, title: '未离开 Gate 1' },
  int121: { type: 121, title: '待客户提供无LTDate' },
  int124: { type: 124, title: '待发料以前' },
  int123: { type: 123, title: 'F2 EDD过期' },
  int125: { type: 125, title: '未离开 Gate 1' },
  int126: { type: 126, title: '采购无EDD' },
  int200: { type: 200, title: '待转PNR' },
  int300: { type: 300, title: '已转PNR' },
  int400: { type: 400, title: '待点灯扫描' },
  int0110: { type: 110, title: 'core,fan,lpt红框后零件' },
  int0111: { type: 111, title: 'b1红框后零件' },
};
