const { ref, reactive, onMounted } = Vue;

import { useSelectList } from "../../hooks/useSelectList.js";
export function useWorkWear() {
  const { getWorkclothesList } = useSelectList();
  // 工装信息表单
  const workWearForm = reactive({
    list: [

      { name: '', int_worktype: '', quantity: '' },
    ],
  });
  // 初始化工装信息表单
  const initWorkWearForm = () => {
    workWearForm.list = [
      { name: '', int_worktype: '', quantity: '' },
    ];
  };
  // 工装名称下拉列表
  const workWearNameList = ref([]);
  // 获取工装名称下拉列表
  const getWorkWearNameList = async () => {

    const data = await getWorkclothesList();
    console.log(data)
    workWearNameList.value = data.map((item) => {
      return {
        label: `${item.str_name}_${item.str_code}`,
        value: item.id,
      };
    });
    for (let index = 0; index < workWearNameList.value.length; index++) {

      workWearNameForm.nameList[index] = workWearNameList.value;

    }
  };
  onMounted(async () => {
    getWorkWearNameList();
    
  });
  const workWearNameForm = reactive({
    nameList: [],
  });
  // 下拉列表显示隐藏
  const visibleChangeWorkWearName = (visible, index) => {
    if (visible) {
      // 当前选择的下拉列表不能包含已经选择的
      workWearNameForm.nameList[index] = workWearNameList.value.filter((item) => {
        return !workWearForm.list.map((item) => item.name).includes(item.value);
      });
      // 如果点击同一个下拉列表，则需要包含当前选择的
      if (workWearForm.list[index].name) {
        // 从下拉列表中拿去当前选择的label
        const currentLabel = workWearNameList.value.find((item) => item.value === workWearForm.list[index].name).label;
        workWearNameForm.nameList[index].push({
          label: currentLabel,
          value: workWearForm.list[index].name,
        });

      }
    }
  };
  // 工装下拉列表改变
  const changeWorkWearName = (value, index) => {
    workWearForm.list[index].number = value;
  };
  return {
    workWearForm,
    initWorkWearForm,
    workWearNameForm,
    visibleChangeWorkWearName,
    changeWorkWearName,
  };
}
