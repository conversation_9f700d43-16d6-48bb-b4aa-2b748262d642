import { post } from '../../../../config/axios/httpReuest.js'
import { CSM_APPROVAL_STATUS_TO_STRING } from '../../../enum/index.js'
import { useApi } from '../../../hooks/useApi.js'
import { getCertificateInfo } from '../../../api/index.js'
const { ref, reactive } = Vue
/**
 * @description 模拟后清单组件函数
 * @date 2024年8月7日16:55:32
 */
export function useSimulateAfter() {
  const pointColorMapForHtml = {
    1: '<div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>',
    2: '<div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>',
    3: '<div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>',
    4: '<div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>',
  }

  const tableRef = ref(null)
  // 表格数据
  const tableState = reactive({
    data: null,
    columns: [
      {
        title: '点位类型',
        field: 'int_point_type',
        minWidth: 80,
        type: 'html',
        fixed: 'left',
        formatter: ({ cellValue }) => {
          return pointColorMapForHtml[cellValue]
        },
      },
      {
        title: '零件来源',
        field: 'str_source_type',
        fixed: 'left',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '仓库',
        field: 'str_wh_name',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台客户',
        field: 'str_client_sp',
        minWidth: 160,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标客户',
        field: 'str_client',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '是否锁定',
        field: 'is_lock',
        filters: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        minWidth: 100,
        formatter: ({ cellValue }) => {
          return cellValue === 1 ? '是' : '否'
        },
      },
      {
        title: '需要沟通',
        field: 'is_csm',
        minWidth: 100,
        filters: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        formatter: ({ cellValue }) => {
          return cellValue === 1 ? '是' : '否'
        },
        // filterRender: { name: 'FilterInput' },
      },

      {
        title: 'CSM审批状态',
        field: 'int_csm_result',
        minWidth: 150,
        filters: Object.keys(CSM_APPROVAL_STATUS_TO_STRING).map((key) => {
          return { label: CSM_APPROVAL_STATUS_TO_STRING[key], value: +key }
        }),
        formatter: ({ cellValue }) => {
          return CSM_APPROVAL_STATUS_TO_STRING[cellValue]
        },
      },
      {
        title: '目标站点',
        field: 'str_nodename',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台站点',
        field: 'str_nodename_sp',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标WO',
        field: 'str_wo',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标ESN',
        field: 'str_esn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原ESN',
        field: 'str_esn_sp',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原WO',
        field: 'str_wo_sp',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PN',
        field: 'str_pn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件名称',
        field: 'str_part_name',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'SM',
        field: 'str_sm',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件标签',
        field: 'str_label',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件条码',
        field: 'str_bcode',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '货位名称',
        field: 'str_product_name',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '批次',
        field: 'str_batch',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PKP',
        field: 'id_pkp',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '要求证书',
        field: 'str_certificate_copy',
        minWidth: 130,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '供体零件证书',
        field: 'str_certificate',
        minWidth: 130,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标数量',
        minWidth: 100,
        field: 'int_num',
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台数量',
        minWidth: 100,
        field: 'int_num_inuse',
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '模拟前EKD',
        minWidth: 120,
        field: 'dt_ekd_before',
        filters: [{ data: '' }],
        fiterRender: { name: 'FilterInput' },
      },
      {
        title: '模拟后EKD',
        minWidth: 120,
        field: 'dt_ekd',
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
    ],
    total: 0,
  })
  // * 获取表格数据
  const getTableData = async (idWo) => {
    const params = {
      ac: 'de_getsimulationresult',
      id_wo: idWo,
    }
    const { data } = await post(params)
    if (data.code === 'success') {
      tableState.data = data.data
      tableState.total = data.data.length
    } else {
      ElementPlus.ElMessage.error(data.text)
    }
  }
  // * 表格筛选
  const handleFilterChange = (data) => {
    tableState.total = tableRef.value.getCurrentLength()
  }
  // * 导出表格数据
  const exportTableData = () => {
    tableRef.value.exportData()
  }
  /**标记是否需要CSN */
  const iSNeddCsm = (is_csm) => {
    const selectedData = tableRef.value.getSelectedData()
    // 提醒
    if (selectedData.length === 0) {
      ElementPlus.ElMessage.warning('请选择数据')
      return
    }
    // 是否确定提交
    let msg = is_csm ? '是否确定需要CSM确认？' : '是否确定不需要CSM确认？'
    ElementPlus.ElMessageBox.confirm(msg, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      // 提交
      selectedData.forEach((item) => {
        item.is_csm = +is_csm
      })
    })
  }
  // * 判断是否需要沟通
  const isNeedCsm = (selectedData, int_csm_result) => {
    // 所选数据是否都需要沟通
    const isNeedCsm = selectedData.every((item) => item.is_csm === 1)
    return !isNeedCsm && int_csm_result === 301
  }
  /**标记是否同意串件 */
  const iSCsmResult = async (params, idWo) => {
    const { is_csm, int_csm_result, is_lock } = params
    const selectedData = tableRef.value.getSelectedData()
    // 判断是否需要沟通
    // if (isNeedCsm(selectedData, int_csm_result)) {
    //   ElementPlus.ElMessage.warning('勾选的零件中包含不需要沟通的数据,请重新选择!')
    //   return
    // }
    // 提醒
    if (selectedData.length === 0) {
      ElementPlus.ElMessage.warning('请选择数据')
      return
    }
    const msg =
      int_csm_result === 301
        ? '是否确定，需要CSM确认？'
        : is_lock === 1
        ? '是否锁定？'
        : is_lock === 0
        ? '是否解锁？'
        : int_csm_result === 1
        ? 'CSM同意？'
        : 'CSM不同意？'
    ElementPlus.ElMessageBox.confirm(msg, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      // 提交
      const ids = selectedData.map((item) => item.id)
      const params = {
        ac: 'de_set_sim_details_status',
        is_csm: is_csm,
        is_lock: is_lock,
        int_csm_result: int_csm_result,
        ids: ids.join(','),
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        ElementPlus.ElMessage.success(data.text)
        await getTableData(idWo)
        // 找出tableSatate.data中与selectedData相同的数据并获取
        const newData = tableState.data.filter((item) => {
          return selectedData.some((selectedItem) => selectedItem.id === item.id)
        })
        // 选中
        tableRef.value.xTable.setCheckboxRow(newData, true)
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    })
  }
  const { runSimulationApi } = useApi()
  // * 执行模拟结果
  const handleRunSimulation = async (idWo) => {
    const data = await runSimulationApi(idWo)
    // todo 待补充
  }

  // 重选资源
  const reSelectResource = reactive({
    visible: false,
    id: '',
    idWo: '',
    pn: '',
    customer: [],
  })
  // * 重选资源方案
  const handleReSelect = () => {
    // 获取当前选中的数据
    const selectedData = tableRef.value.getSelectedData()
    // 提醒只能选择一条数据
    if (selectedData.length !== 1) {
      ElementPlus.ElMessage.warning('请选择一条数据')
      return
    }
    reSelectResource.visible = true
    reSelectResource.id = selectedData[0].id
    reSelectResource.idWo = selectedData[0].id_wo
    reSelectResource.pn = selectedData[0].str_pn
    reSelectResource.customer = selectedData[0].id_client?.split(',') ?? []
  }

  return {
    tableRef,
    tableState,
    getTableData,
    handleFilterChange,
    exportTableData,
    iSNeddCsm,
    iSCsmResult,
    handleRunSimulation,
    reSelectResource,
    handleReSelect,
  }
}

/**
 * 获取证书信息
 */
export const useCertificateInfo = (tableRef) => {

  const certificateInfoVisible = ref(false)
  const openCertificateInfo = () => {
    getCertificateTableData()
    certificateInfoVisible.value = true
  }
  const closeCertificateInfo = () => {
    certificateInfoVisible.value = false
  }


  const certificateLoading = ref(false)
  const certificateTableData = ref([])
  const getCertificateTableData = async () => {
    const selectedData = tableRef.value.getSelectedData()
    if (selectedData.length === 0) {
      ElementPlus.ElMessage.warning('请先选择数据')
      return
    }
    if (selectedData.length > 1) {
      ElementPlus.ElMessage.warning('只能选择一条数据')
      return
    }
    const { id_pkp } = selectedData[0]
    const queryParams = {
      id_pkp,
      int_page: 2,
    }
    certificateLoading.value = true
    const { data } = await getCertificateInfo(queryParams)
    certificateTableData.value = data.data
    certificateLoading.value = false
  }
  return {
    certificateInfoVisible,
    openCertificateInfo,
    closeCertificateInfo,
    certificateLoading,
    certificateTableData,
    getCertificateTableData,
  }
}
