import { post } from '../../../config/axios/httpReuest.js'
import HtVxeTable from '../../../components/VxeTable/HtVxeTable.js'
import ReasonRemarkDialog from '../components/ReasonRemarkDialog.js'
import { useReasonRemark } from '../hooks/useReasonRemark.js'

const { reactive, toRefs, ref, onMounted } = Vue
const { useVModel } = VueUse

const TotalDrawer = {
  components: { HtVxeTable, ReasonRemarkDialog },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    idWo: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    // 双向绑定抽屉显示状态
    const visible = useVModel(props, 'visible', emit)
    // 表格引用
    const tableRef = ref(null)

    // 定义表格列配置
    const defaultColumns = [
      {
        title: 'Kitting完成/站点',
        field: 'str_nodename',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标WO',
        field: 'str_code',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '目标ESN',
        field: 'str_esn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台',
        field: 'str_wo_code_ori',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原台 ESN',
        field: 'str_wo_esn_ori',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PN',
        field: 'str_pn',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件标签',
        field: 'str_label',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件名称',
        field: 'str_part_name',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'SM',
        field: 'str_sm',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '客户',
        field: 'str_client',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '转包供应商/采购供应商',
        field: 'str_subcontract',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件来源',
        field: 'str_class',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '零件类别',
        field: 'str_item_type',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'Marshalling集件关闭',
        field: 'is_close',
        minWidth: 150,
        filters: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        formatter: ({ cellValue }) => {
          return cellValue === 1 ? '是' : '否'
        },
      },
      {
        title: 'Marshalling完成日期',
        field: 'dt_close',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'PKP',
        field: 'id_pkp',
        minWidth: 150,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '机型',
        field: 'str_engine_type',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '数量',
        field: 'int_qty',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: 'EKD',
        field: 'dt_ekd',
        minWidth: 100,
        filters: [{ data: '' }],
        filterRender: { name: 'FilterInput' },
      },
      {
        title: '原因备注',
        field: 'str_reason',
        minWidth: 200,
      },
    ]

    // 表格状态管理
    const tableState = reactive({
      data: null, // 表格数据
      columns: [], // 表格列
      total: 0, // 数据总数
    })

    /**
     * 获取表格数据
     * @description 通过POST请求获取零件清单数据
     */
    const getTableData = async () => {
      try {
        const { idWo, type } = props
        const params = {
          ac: 'de_getpnlist_bygroup',
          id_wo: idWo,
          int_point_type: type,
        }
        const { data } = await post(params)

        if (data.code === 'success') {
          tableState.columns = [...defaultColumns]
          tableState.data = data.data
          tableState.total = data.data.length
        } else {
          ElementPlus.ElMessage.error(data.text)
        }
      } catch (error) {
        console.error('获取表格数据失败:', error)
        ElementPlus.ElMessage.error('获取数据失败，请稍后重试')
      }
    }

    /**
     * 处理表格筛选变化
     * @description 更新筛选后的数据总数
     */
    const handleFilterChange = () => {
      tableState.total = tableRef.value.getCurrentLength()
    }

    const { reasonRemark, openReasonRemark, saveReasonRemark, closeReasonRemark } = useReasonRemark(tableRef)

    // 跳转配置映射表
    const JUMP_CONFIG = {
      a: {
        name: '集件零件管理',
        id: '1435766996880461825',
        key: 'id_pkp',
      },
      b: {
        name: '用料申请',
        id: '1453234319950618624',
        key: 'id_apply',
      },
      c: {
        name: '零件报表',
        id: '1435766981386702849',
        key: 'id_offlog',
      },
      d: {
        name: '转包零件跟踪',
        id: '1435766992040235009',
        key: 'id_po_sub_sc',
      },
      e: {
        name: '锁库处理',
        id: '1453282700483895296',
        key: 'id_apply',
      },
      g: {
        name: '零件历程',
        id: '1826524987646808064',
        key: 'id_pkp',
      },
    }

    /**
     * 处理跳转逻辑
     * @param {string} command - 跳转命令
     * @description 根据选中数据跳转到对应模块
     */
    const handleJump = (command) => {
      const selectedData = tableRef.value.getSelectedData()

      if (!selectedData.length) {
        ElementPlus.ElMessage.warning('请先选择数据')
        return
      }

      const config = JUMP_CONFIG[command]
      const idList = selectedData
        .map((item) => item[config.key])
        .filter(Boolean)
        .join(',')

      if (!idList) {
        ElementPlus.ElMessage.warning(`无${config.name}信息`)
        return
      }

      const getUrl = (command, idList) => {
        const baseUrl = `/Page/?moduleid=${config.id}`
        return command === 'e' ? `${baseUrl}&qrc_id_main=${idList}` : `${baseUrl}&qrc_id=${idList}`
      }
      com.refreshTab('*' + config.name, getUrl(command, idList))
    }

    // 组件挂载时获取数据
    onMounted(getTableData)

    return {
      visible,
      tableRef,
      ...toRefs(tableState),
      handleFilterChange,
      handleJump,
      reasonRemark,
      openReasonRemark,
      saveReasonRemark,
      closeReasonRemark,
    }
  },
  template: /*html*/ `
    <el-drawer class="my_drawer" v-model="visible" size="85%" :show-close="false" destroy-on-close>
      <template #title>
        <div class="flex items-center justify-between">
          <div class="text-white">{{ title }}零件清单</div>
          <el-button type="danger" @click="visible = false">关闭</el-button>
        </div>
      </template>
      <div style="height: calc(100% - 50px)">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-dropdown @command="handleJump">
              <el-button type="primary">
                跳转
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="a">集件零件管理（On-log）</el-dropdown-item>
                  <el-dropdown-item command="b">用料申请</el-dropdown-item>
                  <el-dropdown-item command="c">零件报表（Off-log）</el-dropdown-item>
                  <el-dropdown-item command="d">转包跟踪</el-dropdown-item>
                  <el-dropdown-item command="e">锁库处理</el-dropdown-item>
                  <el-dropdown-item command="g">零件历程</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button class="ml-4" type="primary" @click="openReasonRemark(0)">原因备注</el-button>
          </div>
          <div class="text-black">共计：{{ total || 0 }} 条</div>
        </div>
        <HtVxeTable
          ref="tableRef"
          :tableData="data"
          :tableColumns="columns"
          :isShowHeaderCheckbox="true"
          @filterChange="handleFilterChange"
        >
          <template #checkbox>
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          </template>
        </HtVxeTable>
      </div>
    </el-drawer>
    <!-- 原因备注 -->
    <ReasonRemarkDialog v-model="reasonRemark.visible" :form="reasonRemark.form" @saveReasonRemark="saveReasonRemark" @closeReasonRemark="closeReasonRemark" />
  `,
}

export default TotalDrawer
