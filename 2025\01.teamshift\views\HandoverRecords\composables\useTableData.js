import { getHandoverTableList } from '../api/index.js'

const { ref } = Vue

// 表格数据管理
export function useTableData() {
  const tableData = ref([])
  const loading = ref(false)

  // 获取表格数据
  const getTableData = async (params) => {
    try {
      loading.value = true
      const res = await getHandoverTableList(params)

      // 转换数据格式
      tableData.value = res
    } catch (error) {
      console.error('获取表格数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  return {
    tableData,
    loading,
    getTableData,
  }
}
