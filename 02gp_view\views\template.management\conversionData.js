// 去除数据中的_X_ROW_CHILD 字段和 _X_ROW_KEY 字段
function removeXRowChild(data) {
  data.forEach((item) => {
    delete item._X_ROW_CHILD;
    delete item._X_ROW_KEY;
    if (item.children.length > 0) {
      removeXRowChild(item.children);
    }
  });
}

// 将多级数据转换为一级数据
function convertToOneLevel(data, list) {
  data.forEach((item) => {
    list.push(item);
    if (item.children.length > 0) {
      convertToOneLevel(item.children, list);
    }
  });
}

// 将数据中重复的对象去掉
function removeDuplicate(data, list) {
  data.forEach((item) => {
    if (!list.some((i) => i.id === item.id)) {
      list.push(item);
    }
  });
}

// 去除数据中的 children 字段
function removeChildren(data) {
  const list = JSON.parse(JSON.stringify(data));
  list.forEach((item) => {
    delete item.children;
  });
  return list;
}

// 组装数据为添加或编辑数据
export function convertData(data) {
  const list = JSON.parse(JSON.stringify(data));
  removeXRowChild(list);
  const oneLevelList = [];
  convertToOneLevel(list, oneLevelList);
  const noDuplicateList = [];
  removeDuplicate(oneLevelList, noDuplicateList);
  return removeChildren(noDuplicateList);
}
