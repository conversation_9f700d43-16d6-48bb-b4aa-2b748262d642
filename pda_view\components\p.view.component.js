import { post } from '../../config/axios/httpReuest.js';
import { currentDateKey, currentNodeKey, currentTypeViewKey, searchForm<PERSON>ey } from '../../config/keys.js';
import { PDA_VIEW_AD, PDA_VIEW_P } from '../../config/tabPaneKey.js';
import { usePView } from '../hooks/usePView.js';
import ErrorComponent from '../../components/error.component.js';
import LoadingComponent from '../../components/loading.component.js';
import { useFilter } from '../hooks/useFilter.js';
import { columnStrategy } from '../hooks/nodeTableColumn.js';
import { useBusinessJump } from '../hooks/useBusinessJump.js';

const { inject, ref, reactive, unref, onMounted, watch, nextTick, computed, defineAsyncComponent, toRefs } = Vue;
export default {
  name: 'PViewComponent',
  components: {
    HtDrawer: defineAsyncComponent({
      loader: () => import('../../components/ht.drawer.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    TotalTableComponent: defineAsyncComponent({
      loader: () => import('./total.table.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HtVxeTable: defineAsyncComponent({
      loader: () => import('../../components/VxeTable/HtVxeTable.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  props: {
    type: String
  },
  setup(props, { emit }) {
    const inputDay = ref(7);
    const inputLowerDay = ref(-7);
    const is_gp = ref(1);
    const { currentDate } = inject(currentDateKey);
    const currentNode = inject(currentNodeKey);
    // 带有默认值得inject
    const { currentTypeView } = inject(currentTypeViewKey, { currentTypeView: ref(PDA_VIEW_P) });
    const { getPViewData, getDatasetSource, chartRef, initChart, myChart, drawerState, pViewTotal, exceedTotal,neverTotal,getPViewEddTypeNum} = usePView();

    watch(currentTypeView, (newVal) => {
      nextTick(async () => {
        await getPViewData(newVal, currentNode, inputDay.value,inputLowerDay.value, currentDate.value, searchForm,is_gp.value);
        await getPViewEddTypeNum(currentNode);
        getDatasetSource(newVal);
        initChart();
      });
    });
    watch(currentDate, (newVal) => {
      nextTick(async () => {
        await getPViewData(unref(currentTypeView), currentNode, inputDay.value, inputLowerDay.value,newVal, searchForm,is_gp.value);
        await getPViewEddTypeNum(currentNode);
        getDatasetSource(unref(currentTypeView));
        initChart();
      });
    });

    const { searchForm } = inject(searchFormKey, { searchForm: reactive({}) });
    watch(searchForm, (newVal) => {
      nextTick(async () => {
        await getPViewData(unref(currentTypeView), currentNode, inputDay.value,inputLowerDay.value, currentDate.value, newVal,is_gp.value);
        await getPViewEddTypeNum(currentNode);
        getDatasetSource(unref(currentTypeView));
        initChart();
      });
    });
    const { searchEddForm } = inject('searchEddForm', { searchEddForm: reactive({})});
    // const { searchEddForm } = inject('searchEddForm');

    onMounted(async () => {
      await handleRefresh();
    });
    watch(searchEddForm, async (newVal) => {
      await handleRefresh();
    });

    // 刷新接口
    const handleRefresh = async () => {
      let filterSearch = null;
      if (props.type === 'edd' && currentNode === '10') {
        filterSearch = searchEddForm;
      } else {
        filterSearch = searchForm;
      }
      await getPViewData(unref(currentTypeView), currentNode, inputDay.value,inputLowerDay.value, currentDate.value, filterSearch,is_gp.value);
      await getPViewEddTypeNum(currentNode);
      getDatasetSource(unref(currentTypeView));
      initChart();
    };
    // 刷新按钮
    const refreshClick = () => {
      const params = {
        ac: 'pda_update_plan_auto',
      };
      post(params).then(res => {
        if (res.data.code === 'success') {
          ElementPlus.ElMessage({
            message: 'refresh success',
            type: 'success',
          });
        } else {
          ElementPlus.ElMessage({
            message: 'refresh fail',
            type: 'error',
          });
        }
      });
    };
    const parseExcel = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target.result;
          const workbook = XLSX.read(data, { type: 'binary' });
          const sheetNames = workbook.SheetNames; // 工作表名称集合
          const worksheet = workbook.Sheets[sheetNames[0]]; // 这里我们只读取第一张sheet
          const json = XLSX.utils.sheet_to_json(worksheet, { raw: true });
          resolve(json);
        };
        reader.onerror = (e) => {
          reject(e);
        };
        reader.readAsBinaryString(file);
      });
    };
    // 格式化Excel数据
    const formatExcelData = (data) => {
      const keyMap = {
        '合同号': 'str_po',
        '件号': 'str_pn',
        '数量': 'int_qty',
        'L/T Date': 'dt_lt',
        'ADD': 'dt_add',
        '订单负责人': 'str_po_by',
      };

      return data.map(item => {
        const obj = {};
        for (const k in item) {
          if (keyMap[k]) {
            if (k === 'L/T Date') {
              // 获取的时间为1900-01-01开始的天数
              obj[keyMap[k]] = moment('1900-01-01', 'YYYY-MM-DD').add(item[k] - 2, 'days').format('YYYY-MM-DD');
            } else {
              obj[keyMap[k]] = item[k];
            }
          }
        }
        return obj;
      });
    };
    // 上传
    const handleUpload = (e) => {
      const file = e.target.files[0];
      parseExcel(file).then((res) => {
        const data = formatExcelData(res);
        const params = {
          ac: 'pda_excelupload_factory',
          data,
        };
        post(params).then((res) => {
          handleRefresh();
        });
      });
    };
    // 下载
    const handleDownload = () => {
      // 生成excel
      const ws = XLSX.utils.aoa_to_sheet([
        ['合同号', '件号', '数量', 'L/T Date', 'ADD', '订单负责人'],
      ]);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
      XLSX.writeFile(wb, '厂家交付模板.xlsx');
    };

    const headerTitle = computed(() => {
      let title = '';
      switch (unref(currentTypeView)) {
        case PDA_VIEW_AD:
          title = 'AD View视图';
          break;
        case 'EDD_TASK_PDA':
          title = 'EDD Ask Tasks P-view视图';
          break;
        default:
          title = 'P View视图';
          break;
      }
      return title;
    });

    /**
     * @description: 天数改变
     * @param {number} val
     * @return {*}
     */
    const handleChangeDay = async (val) => {
      await handleRefresh()
    };

    const handleChangeLowerDay = async (val) => {
      inputLowerDay.value = val
      await handleRefresh()
    };

    const { getFilterData } = useFilter();
    const eddAllTableRef = ref(null)
    const eddAllTableState = reactive({
      isShowDrawer: false,
      tableData: null,
      tableColumns: [],
      total: 0
    });
    // 获取点击edd total的数据
    const getEddTotalData = async () => {
      eddAllTableState.tableColumns = columnStrategy[currentNode];
      const filterSearch = Object.assign({ is_all: '1' }, searchEddForm);
      const param = {
        ac: 'pda_get_plan_plist',
        str_type: currentNode,
        dt_date: moment(currentDate.value).add(inputDay.value, 'days').format('YYYY-MM-DD'),
        dt_date_lower: moment(currentDate.value).add(inputLowerDay.value, 'days').format('YYYY-MM-DD'),
        str_ywtype: 'dm_task_p',
        filter_fields: getFilterData(filterSearch),
        sort_fields: [],
        dt_p0: currentDate.value,
      };
      const { data } = await post(param);
      eddAllTableState.tableData = data.data;
      eddAllTableState.total = data.data.length
    };
    const handleTotalClick = async () => {
      eddAllTableState.isShowDrawer = true;
      eddAllTableState.tableColumns = []
      eddAllTableState.tableData = null
      eddAllTableState.total = 0
      await getEddTotalData();
    };

    const getEddTypeData = async (strType=0) => {
      eddAllTableState.tableColumns = columnStrategy[currentNode];
      const param = {
        ac: 'pda_edd_nevel_deal_list',
        str_type: strType
      };
      const { data } = await post(param);
      eddAllTableState.tableData = data.data;
      eddAllTableState.total = data.data.length   
    };

    const handleEddTypeClick = async (val) => {
      eddAllTableState.isShowDrawer = true;
      eddAllTableState.tableColumns = []
      eddAllTableState.tableData = null
      eddAllTableState.total = 0
      await getEddTypeData(val);
    };

    const exportDataEvent = () => {
      eddAllTableRef.value.exportData();
    }
    const { skipSelectEvent } = useBusinessJump();
    const skipSelectEventClick = () => {
      skipSelectEvent(eddAllTableRef.value.xTable, currentNode);
    };
    // 
    const handleFilterChange = (val) => {
      eddAllTableState.total = eddAllTableRef.value.getCurrentLength()
    }
    return {
      currentDate,
      inputDay,
      chartRef,
      currentTypeView,
      handleRefresh,
      handleUpload,
      handleDownload,
      headerTitle,
      refreshClick,
      ...toRefs(drawerState),
      handleChangeDay,
      pViewTotal,
      handleTotalClick,
      eddAllTableState,
      eddAllTableRef,
      exportDataEvent,
      inputLowerDay,
      handleChangeLowerDay,
      exceedTotal,
      neverTotal,
      handleEddTypeClick,
      skipSelectEventClick,
      handleFilterChange,
      is_gp,
      currentNode,
    };
  },
  template: `
     <el-card :body-style="{ padding: '0px' }">
    <template #header>
      <div class="flex items-center flex-wrap">
        <div class="flex-1 text-sm">{{ headerTitle }}<span class="ml-2">{{ currentDate }}</span>
        </div>
        <div class="flex-1">
          <span class="text-sm mr-2">Day天:</span>
          <el-input-number size="small" v-model="inputDay" :step="7" @change="handleChangeDay"></el-input-number>
        </div>
        <div v-if="currentNode==5" class="flex-1">
          <span class="text-sm mr-2">GP接手:</span>
          <el-select size="small" v-model="is_gp" style="width: 60%;float: right;" @change="handleChangeDay">
            <el-option label="GP" :value="1" />
            <el-option label="全部" :value="0" />
          </el-select>
        </div>
        <div class="flex-1">
          <slot name="lower" v-bind:inputLowerDay="inputLowerDay" v-bind:max="inputDay"
            v-bind:lower="handleChangeLowerDay"></slot>
        </div>
        <!--:min="7" 20240426 去掉最小值限制-->
        <div class="flex-1">
          <slot name="refresh" v-bind:refresh="refreshClick"></slot>
        </div>
        <div class="flex-1">
          <slot name="upload" v-bind:upload="handleUpload"></slot>
        </div>
        <div class="flex-1">
          <slot name="download" v-bind:download="handleDownload"></slot>
        </div>
        <div class="flex-1">
          <slot name="exceed" :exceed="exceedTotal" v-bind:exceedClick="handleEddTypeClick"></slot>
        </div>
        <div class="flex-1">
          <slot name="never" :never="neverTotal" v-bind:neverClick="handleEddTypeClick"></slot>
        </div>
        <div class="flex-auto">
          <slot name="total" :total="pViewTotal" v-bind:totalClick="handleTotalClick"></slot>
        </div>
      </div>
    </template>
    <div ref="chartRef" class="h-[70vh] w-full"></div>
  </el-card>
  <!--    点击series的抽屉-->
  <HtDrawer v-model:visible="isShowDrawer" :title="title" :is-show-save="false">
    <TotalTableComponent :type="flag" :date="date"></TotalTableComponent>
  </HtDrawer>
  <!--    点击总数-->
  <HtDrawer v-model:visible="eddAllTableState.isShowDrawer" title="零件详情" :is-show-save="false">
    <vxe-toolbar>
      <template #buttons>
        <el-button title="下载" type="primary" circle @click="exportDataEvent">
          <el-icon>
            <Download></Download>
          </el-icon>
        </el-button>
        <el-button title="跳转" type="primary" circle @click="skipSelectEventClick">
          <el-icon>
            <Position></Position>
          </el-icon>
        </el-button>
      </template>
      <template #tools>
        <span class="text-xl">Total: {{ eddAllTableState.total ?? 0 }} </span>
      </template>
    </vxe-toolbar>
    <div class="border-b-2 mb-2"></div>
    <div style="height: calc(100vh - 220px);">
      <HtVxeTable ref="eddAllTableRef" :tableData="eddAllTableState.tableData"
        :tableColumns="eddAllTableState.tableColumns" :isShowHeaderCheckbox="true" @filterChange="handleFilterChange">
        <template #checkbox>
          <vxe-column type="checkbox" width="80" fixed="left"></vxe-column>
        </template>
      </HtVxeTable>
    </div>
  </HtDrawer>
  `,
};
