import { queryStationList, queryAllEngine, insertStation } from '../../views/stationManage/api/index.js'

const { ref, reactive, onMounted } = Vue
/**
 * 站位管理
 */
const StationManagement = {
  setup() {
    const loading = ref(false)
    const tableData = ref([])
    // 获取站位管理列表
    const getStationList = async () => {
      try {
        loading.value = true
        const { data } = await queryStationList()
        const { code, text, data: list } = data
        if (code === 'success') {
          tableData.value = list
        } else {
          throw new Error(text)
        }
      } catch (error) {
        ElementPlus.ElMessage.error(error.message || '获取站位列表失败')
      } finally {
        loading.value = false
      }
    }


    const stationState = reactive({
      visible: false,
      id: '',
      idSite: '',
      siteName: '',
      currentDate: '',
      idWo: '',
    })

    // 获取ESN下拉列表
    const allEsn = ref([])
    const esnOptions = ref([])
    const getEsnOptions = async () => {
      try {
        const { data } = await queryAllEngine()
        const { code, text, data: list } = data
        if (code === 'success') {
          allEsn.value = list
        } else {
          throw new Error(text)
        }
      } catch (error) {
        ElementPlus.ElMessage.error(error.message || '获取ESN列表失败')
      }
    }

    const noRepeatIdWoBySameDay = (currentDate) => {
      // 获取当前日期的idWo
      const idWoList = tableData.value.filter((item) => item[currentDate]).map((item) => item.idWo)
      const idWoListArr = idWoList.map((item) => item.split(',')).flat()
      // 过滤掉已经存在的idWo
      esnOptions.value = allEsn.value
        .filter((item) => !idWoListArr.includes(item.id_wo))
        .map((item) => ({
          label: item.str_esn,
          value: item.id_wo,
        }))
    }

    const addStation = (row, column) => {
      stationState.visible = true
      stationState.title = '添加站位'
      stationState.id = ''
      stationState.idSite = row.id_site
      stationState.siteName = row.stationName
      stationState.currentDate = column.title
      // 同一天的idWo不能重复
      noRepeatIdWoBySameDay(column.title)
    }
    const editStation = (row, column) => {
      stationState.visible = true
      stationState.title = '编辑站位'
      stationState.id = row.id
      stationState.currentDate = column.title
      stationState.idSite = row.id_site
      stationState.siteName = row.stationName
      stationState.idWo = row.idWo.split(',')
      esnOptions.value = allEsn.value.map((item) => ({
        label: item.str_esn,
        value: item.id_wo,
      }))
    }
    const handleAddStation = async () => {
      try {
        const params = {
          id: stationState.id,
          idSite: stationState.idSite,
          idWo: stationState.idWo?.length ? stationState.idWo?.join(',') : '',
          plan: stationState.currentDate,
          siteName: stationState.siteName,
        }
        loading.value = true
        const { data } = await insertStation(params)
        const { code, text } = data
        
        if (code === 'success') {
          ElementPlus.ElMessage.success('操作成功')
          stationState.visible = false
          handleStationCancel()
          await getStationList()
        } else {
          throw new Error(text)
        }
      } catch (error) {
        ElementPlus.ElMessage.error(error.message || '操作失败')
      } finally {
        loading.value = false
      }
    }
    // 取消添加站位
    const handleStationCancel = () => {
      // 初始化表单数据
      stationState.idSite = ''
      stationState.siteName = ''
      stationState.currentDate = ''
      stationState.idWo = ''
      stationState.visible = false
    }

    // 表格单元格样式
    const cellClassName = ({ row, column }) => {
      return 'my-cell'
    }

    const isBeforeToday = (date) => {
      return moment(date).isBefore(moment().format('YYYY-MM-DD'))
    }

    // 添加重置功能
    const handleStationReset = () => {
      if (stationState.title === '添加站位') {
        // 添加场景 - 清空选择
        stationState.idWo = []
      } else {
        // 编辑场景 - 恢复原始数据
        const currentRow = tableData.value.find(item => item.id === stationState.id)
        if (currentRow) {
          stationState.idWo = currentRow.idWo.split(',')
        }
      }
    }

    onMounted(() => {
      getStationList()
      getEsnOptions()
    })
    return {
      loading,
      tableData,
      addStation,
      editStation,
      stationState,
      handleAddStation,
      esnOptions,
      cellClassName,
      handleStationCancel,
      isBeforeToday,
      handleStationReset,
    }
  },
  template: /*html*/ `
    <div class="flex h-screen flex-col py-4">
      <div class="flex-auto px-4">
        <vxe-table
          :loading="loading"
          class="my-table"
          ref="xTable"
          border
          :data="tableData"
          height="100%"
          :cell-class-name="cellClassName"
          column-config="{ resizable: true }"
          align="center"
        >
          <vxe-column type="seq" width="70" fixed="left"></vxe-column>
          <vxe-column field="stationName" width="200" title="站位"></vxe-column>
           <vxe-column field="str_url" width="300" title="Url"></vxe-column>
          <vxe-column field="strEsn" min-width="100" title="ESN">
            <template #default="{ row, column }">
              <div v-if="row.strEsn" class="flex flex-wrap gap-2 pl-2">
                <el-tag v-for="item in row.strEsn.split(',')" :key="item" type="primary">{{ item }}</el-tag>
                <!-- 分割线 -->
                <div class="w-1 h-1 bg-gray-300 rounded-full"></div>
                <!-- 按钮 -->
                <el-button type="primary" size="small" @click="editStation(row, column)">编辑</el-button>
              </div>
              <div v-else>
                <el-button type="success" size="small" @click="addStation(row, column)">添加</el-button>
              </div>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <el-dialog v-model="stationState.visible" class="station-dialog" :show-close="false">
        <template #title>
          <div class="flex justify-between items-center">
            <span class="text-white">{{ stationState.title }}</span>
            <el-button type="danger" size="small" @click="handleStationCancel">关闭</el-button>
          </div>
        </template>
        <el-form :model="stationState" width="500" class="p-2">
          <el-form-item label="站位" prop="siteName">
            <el-input v-model="stationState.siteName" readonly />
          </el-form-item>
          <el-form-item label="ESN" prop="idWo">
            <!-- 虚拟下拉列表 多选，可搜索查询 -->
            <el-select-v2
              v-model="stationState.idWo"
              multiple
              filterable
              :options="esnOptions"
              placeholder="请输入ESN"
              clearable
            ></el-select-v2>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button type="warning" @click="handleStationReset">重置</el-button>
          <el-button type="primary" @click="handleAddStation">保存</el-button>
          <el-button @click="handleStationCancel">取消</el-button>
        </template>
      </el-dialog>
    </div>
  `,
}

export default StationManagement
