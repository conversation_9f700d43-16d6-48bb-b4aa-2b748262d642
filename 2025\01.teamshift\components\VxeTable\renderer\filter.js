import FilterComplex from './components/FilterComplex.js'
import FilterInput from './components/FilterInput.js'
import FilterCalendar from './components/FilterCalendar.js'
import FilterSelect from './components/FilterSelect.js'

const { h } = Vue

// 创建一个字段与过滤条件的映射，用于保存各列的过滤条件
const fieldFilterMap = new Map()

VXETable.renderer.add('FilterInput', {
  // 筛选模板
  renderFilter(renderOpts, params) {
    const { column } = params

    // 确保每个列都有独立的过滤器选项
    if (!column.filters || !column.filters.length) {
      column.filters = [{ data: '' }]
    }

    // 为每个列创建独立的组件实例
    return h(
      'div',
      {
        key: column.field,
      },
      [
        h(FilterInput, {
          params,
        }),
      ],
    )
  },
  // 重置数据方法
  filterResetMethod(params) {
    const { options, column } = params
    options.forEach((option) => {
      option.data = ''
    })

    // 清除该列存储的过滤条件
    if (column) {
      fieldFilterMap.delete(column.field)
    }
  },
  // 重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
  filterRecoverMethod({ option, column }) {
    option.data = ''

    // 恢复该列存储的过滤条件
    if (column) {
      fieldFilterMap.delete(column.field)
    }
  },
  // 筛选方法
  filterMethod(params) {
    const { option, row, column } = params
    const cellValue = row[column.field]
    if (!cellValue || !option?.data) {
      return false
    }
    const { data } = option

    // 保存当前列的过滤条件
    if (column && data) {
      fieldFilterMap.set(column.field, data)
    }

    if (cellValue) {
      // 不区分大小写
      return String(cellValue).toLowerCase().indexOf(data.toLowerCase()) > -1
    }
    return false
  },
})

// 创建一个条件的渲染器
VXETable.renderer.add('FilterComplex', {
  // 不显示底部按钮，使用自定义按钮
  showFilterFooter: false,
  // 筛选模板
  renderFilter(renderOpts, params) {
    return h(FilterComplex, {
      params,
    })
  },
  // 重置数据方法
  filterResetMethod(params) {
    const { options } = params
    options.forEach((option) => {
      option.data = { type: 'less', name: '' }
    })
  },
  // 筛选数据方法
  filterMethod(params) {
    const { option, row, column } = params
    const { data } = option
    const cellValue = row[column.field]
    const { type, name } = data
    // 判断输入的是否是数字
    const isNumber = /^\d+$/.test(name)
    if (cellValue && isNumber) {
      if (type === 'less') {
        return cellValue < name
      } else if (type === 'eq') {
        return Number(cellValue) === Number(name)
      } else if (type === 'greater') {
        return cellValue > name
      }
    }
    return false
  },
})

// 创建日期筛选渲染器
VXETable.renderer.add('FilterCalendar', {
  // 显示底部按钮
  showFilterFooter: true,
  // 筛选模板
  renderFilter(renderOpts, params) {
    const { column } = params
    // 确保列有初始的过滤器选项
    if (!column.filters || !column.filters.length) {
      column.filters = [{ data: null }]
    }

    // 为每个列创建独立的过滤器实例
    return h(
      'div',
      {
        class: 'filter-wrapper',
      },
      [
        h(FilterCalendar, {
          key: column.field,
          params,
        }),
      ],
    )
  },
  // 重置数据方法
  filterResetMethod(params) {
    const { options } = params
    if (options) {
      options.forEach((option) => {
        option.data = null
      })
    }
  },
  // 重置筛选复原方法
  filterRecoverMethod({ option }) {
    if (option) {
      option.data = null
    }
  },
  // 筛选方法
  filterMethod({ option, row, column }) {
    const cellValue = row[column.field]
    if (!cellValue || !option?.data) {
      return false
    }

    try {
      // 使用 moment 进行日期比较
      const filterDate = moment(option.data)
      const cellDate = moment(cellValue)

      // 使用 isSame 方法比较日期是否相同（忽略时分秒）
      return filterDate.isSame(cellDate, 'day')
    } catch (e) {
      console.error('日期比较出错：', e)
      return false
    }
  },
})

// 创建选择型过滤器渲染器
VXETable.renderer.add('FilterSelect', {
  // 显示底部按钮
  showFilterFooter: true,
  // 筛选模板
  renderFilter(renderOpts, params) {
    const { column } = params
    // 确保列有初始的过滤器选项
    if (!column.filters || !column.filters.length) {
      column.filters = [{ data: null }]
    }

    // 为每个列创建独立的过滤器实例
    return h(
      'div',
      {
        class: 'filter-wrapper',
      },
      [
        h(FilterSelect, {
          key: column.field,
          params,
        }),
      ],
    )
  },
  // 重置数据方法
  filterResetMethod(params) {
    console.log('FilterSelect reset params:', params)
    const { options, column } = params
    options.forEach((option, index) => {
      console.log(`Resetting option ${index} for ${column.field}:`, option, 'from', option.data, 'to null')
      option.data = null
    })

    // 清除该列存储的过滤条件
    if (column) {
      fieldFilterMap.delete(column.field)
    }
  },
  // 重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
  filterRecoverMethod({ option, column }) {
    console.log('option', option)
    option.data = null

    // 恢复该列存储的过滤条件
    if (column) {
      fieldFilterMap.delete(column.field)
    }
  },
  // 筛选方法 - 这里是本地筛选，但我们使用远程筛选，所以这个方法不会被调用
  filterMethod({ option, row, column }) {
    const cellValue = row[column.field]
    if (!option?.data && option?.data !== 0) {
      return true // 没有选择过滤条件时显示所有数据
    }

    // 精确匹配
    return cellValue === option.data
  },
})
