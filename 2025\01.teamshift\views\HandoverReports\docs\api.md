# 交接统计报表 API 文档

## 接口概述

本文档描述了交接统计报表组件与后端系统交互的接口规范。这些接口主要用于获取交接提交和接收的统计数据，以及详细的交接事项列表。

## 接口列表

### 1. 获取交接提交统计数据

#### 接口描述
获取交接提交的统计数据，包括已提交和未提交的数量统计及详情列表。

#### 请求方法
```
GET /api/handover/submission/stats
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| startDate | String | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | String | 否 | 结束日期，格式：YYYY-MM-DD |
| businessTypes | Array | 否 | 业务类型列表，例如：["F1-2", "F2"] |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "submitted": 85,
    "unsubmitted": 35,
    "total": 120,
    "details": {
      "已提交": [
        {
          "id": "S1001",
          "businessType": "F1-2",
          "title": "已提交交接事项 1",
          "status": "已提交",
          "date": "2023-06-15"
        },
        // ... 更多数据
      ],
      "未提交": [
        {
          "id": "U1001",
          "businessType": "F2",
          "title": "未提交交接事项 1",
          "status": "未提交",
          "date": "2023-06-16"
        },
        // ... 更多数据
      ]
    }
  }
}
```

### 2. 获取交接接收统计数据

#### 接口描述
获取交接接收的统计数据，包括已接收和未接收的数量统计及详情列表。

#### 请求方法
```
GET /api/handover/receiving/stats
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| startDate | String | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | String | 否 | 结束日期，格式：YYYY-MM-DD |
| businessTypes | Array | 否 | 业务类型列表，例如：["F1-2", "F2"] |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "received": 72,
    "unreceived": 28,
    "total": 100,
    "details": {
      "已接收": [
        {
          "id": "R1001",
          "businessType": "F1分解",
          "title": "已接收交接事项 1",
          "status": "已接收",
          "date": "2023-06-17"
        },
        // ... 更多数据
      ],
      "未接收": [
        {
          "id": "P1001",
          "businessType": "F4装配",
          "title": "未接收交接事项 1",
          "status": "未接收",
          "date": "2023-06-18"
        },
        // ... 更多数据
      ]
    }
  }
}
```

### 3. 获取交接详情

#### 接口描述
根据ID获取单个交接事项的详细信息。

#### 请求方法
```
GET /api/handover/detail/{id}
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| id | String | 是 | 交接事项ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "S1001",
    "businessType": "F1-2",
    "title": "已提交交接事项 1",
    "status": "已提交",
    "date": "2023-06-15",
    "submitter": "张三",
    "receiver": "李四",
    "description": "这是一个详细的交接事项描述",
    "attachments": [
      {
        "name": "附件1.pdf",
        "url": "/uploads/attachments/123456.pdf"
      }
    ],
    "history": [
      {
        "action": "创建",
        "operator": "张三",
        "time": "2023-06-14 10:30:00",
        "comment": "初始创建"
      },
      {
        "action": "提交",
        "operator": "张三",
        "time": "2023-06-15 14:20:00",
        "comment": "完成并提交"
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 描述 |
|-------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 接口调用示例

### JavaScript 示例

```javascript
/**
 * 获取交接提交统计数据
 * @param {Object} filters 筛选条件
 * @returns {Promise<Object>} 统计数据
 */
async function fetchSubmissionData(filters = {}) {
    const params = new URLSearchParams();
    
    if (filters.dateRange && filters.dateRange.length === 2) {
        params.append('startDate', filters.dateRange[0].toISOString().split('T')[0]);
        params.append('endDate', filters.dateRange[1].toISOString().split('T')[0]);
    }
    
    if (filters.businessTypes && filters.businessTypes.length > 0) {
        filters.businessTypes.forEach(type => {
            params.append('businessTypes', type);
        });
    }
    
    try {
        const response = await fetch(`/api/handover/submission/stats?${params.toString()}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        return result.data;
    } catch (error) {
        console.error('Error fetching submission data:', error);
        throw error;
    }
}
```

## 更新历史

| 版本号 | 更新日期 | 更新内容 |
|-------|---------|---------|
| 1.0.0 | 2023-06-20 | 初始版本 | 