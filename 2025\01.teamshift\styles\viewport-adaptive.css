/* 视口适配样式 - 确保内容占满可视区域且无滚动条 */

/* 基础视口适配 */
.viewport-adaptive {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

/* 内容容器适配 */
.viewport-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 表格容器适配 */
.table-viewport-adaptive {
  flex: 1;
  min-height: 0; /* 重要：允许 flex 子元素缩小 */
  overflow: hidden;
  position: relative;
}

.table-viewport-adaptive .vxe-table {
  height: 100% !important;
  width: 100% !important;
}

.table-viewport-adaptive .vxe-table .vxe-table--body-wrapper {
  /* 确保表格主体可滚动但容器不溢出 */
  overflow: auto !important;
  max-height: calc(100% - var(--table-header-height, 40px));
}

/* 分页器适配 */
.pagination-viewport-adaptive {
  flex-shrink: 0;
  height: auto;
  min-height: 40px;
  padding: 8px 16px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 头部区域适配 */
.header-viewport-adaptive {
  flex-shrink: 0;
  height: auto;
  min-height: 60px;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

/* 根据缩放级别动态调整 */
.zoom-scale-50 {
  --base-font-size: 18px;
  --base-padding: 12px;
  --base-margin: 8px;
  --table-row-height: 48px;
  --table-header-height: 50px;
}

.zoom-scale-75 {
  --base-font-size: 16px;
  --base-padding: 10px;
  --base-margin: 6px;
  --table-row-height: 44px;
  --table-header-height: 45px;
}

.zoom-scale-100 {
  --base-font-size: 14px;
  --base-padding: 8px;
  --base-margin: 4px;
  --table-row-height: 40px;
  --table-header-height: 40px;
}

.zoom-scale-125 {
  --base-font-size: 13px;
  --base-padding: 7px;
  --base-margin: 3px;
  --table-row-height: 36px;
  --table-header-height: 38px;
}

.zoom-scale-150 {
  --base-font-size: 12px;
  --base-padding: 6px;
  --base-margin: 2px;
  --table-row-height: 32px;
  --table-header-height: 35px;
}

.zoom-scale-200 {
  --base-font-size: 11px;
  --base-padding: 4px;
  --base-margin: 2px;
  --table-row-height: 28px;
  --table-header-height: 32px;
}

/* 应用缩放变量 */
.viewport-adaptive {
  font-size: var(--base-font-size, 14px);
}

.viewport-adaptive * {
  box-sizing: border-box;
}

.viewport-adaptive .el-button {
  padding: var(--base-padding, 8px) calc(var(--base-padding, 8px) * 1.5);
  font-size: var(--base-font-size, 14px);
  margin: var(--base-margin, 4px);
}

.viewport-adaptive .vxe-table .vxe-cell {
  padding: var(--base-padding, 8px);
  font-size: var(--base-font-size, 14px);
  height: var(--table-row-height, 40px);
  line-height: calc(var(--table-row-height, 40px) - var(--base-padding, 8px) * 2);
}

.viewport-adaptive .vxe-table .vxe-header--column {
  height: var(--table-header-height, 40px);
  font-size: var(--base-font-size, 14px);
  padding: var(--base-padding, 8px);
}

/* 确保内容不超出视口 */
.viewport-adaptive .el-form {
  margin: 0;
  padding: var(--base-padding, 8px);
}

.viewport-adaptive .el-form-item {
  margin-bottom: var(--base-margin, 4px);
}

/* 响应式列宽调整 */
.viewport-adaptive .vxe-table {
  --col-width-xs: 60px;
  --col-width-sm: 80px;
  --col-width-md: 120px;
  --col-width-lg: 150px;
  --col-width-xl: 200px;
}

.zoom-scale-50 .vxe-table {
  --col-width-xs: 80px;
  --col-width-sm: 100px;
  --col-width-md: 150px;
  --col-width-lg: 180px;
  --col-width-xl: 240px;
}

.zoom-scale-200 .vxe-table {
  --col-width-xs: 50px;
  --col-width-sm: 60px;
  --col-width-md: 90px;
  --col-width-lg: 120px;
  --col-width-xl: 160px;
}

/* 滚动条优化 */
.viewport-adaptive ::-webkit-scrollbar {
  width: calc(var(--base-padding, 8px) * 0.75);
  height: calc(var(--base-padding, 8px) * 0.75);
}

.viewport-adaptive ::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.5);
  border-radius: calc(var(--base-padding, 8px) * 0.375);
}

.viewport-adaptive ::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 防止内容溢出的安全措施 */
.viewport-adaptive {
  max-width: 100vw;
  max-height: 100vh;
}

.viewport-adaptive * {
  max-width: 100%;
}

/* 表格自适应列宽 */
.auto-fit-columns .vxe-table {
  table-layout: auto;
}

.auto-fit-columns .vxe-table .vxe-header--column,
.auto-fit-columns .vxe-table .vxe-body--column {
  width: auto !important;
  min-width: var(--col-width-xs, 60px);
  max-width: none;
}

/* 紧凑模式 */
.compact-mode {
  --base-font-size: 12px;
  --base-padding: 4px;
  --base-margin: 2px;
  --table-row-height: 28px;
  --table-header-height: 32px;
}

/* 宽松模式 */
.spacious-mode {
  --base-font-size: 16px;
  --base-padding: 12px;
  --base-margin: 8px;
  --table-row-height: 48px;
  --table-header-height: 50px;
}

/* 动画过渡 */
.viewport-adaptive * {
  transition: font-size 0.2s ease,
              padding 0.2s ease,
              margin 0.2s ease,
              height 0.2s ease;
}

/* 禁用过渡的情况 */
@media (prefers-reduced-motion: reduce) {
  .viewport-adaptive * {
    transition: none !important;
  }
}

/* 打印适配 */
@media print {
  .viewport-adaptive {
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
    font-size: 12pt !important;
  }
  
  .table-viewport-adaptive .vxe-table .vxe-table--body-wrapper {
    overflow: visible !important;
    max-height: none !important;
  }
  
  .pagination-viewport-adaptive {
    display: none !important;
  }
} 