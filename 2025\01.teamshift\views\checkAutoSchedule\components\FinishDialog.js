const { defineComponent } = Vue
const { useVModel } = VueUse

export default defineComponent({
  name: 'FinishDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
      default: () => ({
        str_code: '',
        str_esn: '',
        str_sm: '',
      }),
    },
    smOptionList: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    const isVisible = useVModel(props, 'visible', emit)

    const handleSubmit = () => {
      emit('submit')
    }

    const handleCancel = () => {
      isVisible.value = false
    }

    return {
      isVisible,
      handleSubmit,
      handleCancel,
    }
  },
  template: /*html */ `
    <el-dialog class="common-dialog" v-model="isVisible" title="完工单元体" width="30%">
      <!-- 基本信息 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="WO">{{ form.str_code }}</el-descriptions-item>
        <el-descriptions-item label="ESN">{{ form.str_esn }}</el-descriptions-item>
      </el-descriptions>
      <el-form :model="form" class="mt-4">
        <el-form-item label="SM">
          <el-select v-model="form.str_sm" placeholder="请选择SM" filterable clearable multiple>
            <el-option v-for="item in smOptionList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </template>
    </el-dialog>
  `,
}) 