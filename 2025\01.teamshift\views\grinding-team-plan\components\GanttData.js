import { useGantt } from '../composables/useGantt.js'
export default {
  name: 'GanttData',
  props: {
    ganttData: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const { configSettings, templateSettings, eventSettings } = useGantt()
    gantt.i18n.setLocale('cn')
    gantt.setSkin('material')
    Object.keys(configSettings).forEach((key) => {
      gantt.config[key] = configSettings[key]
    })
    Object.keys(templateSettings).forEach((key) => {
      gantt.templates[key] = templateSettings[key]
    })
    Object.keys(eventSettings).forEach((key) => {
      gantt.attachEvent(key, eventSettings[key])
    })

    const ganttInit = (tasks) => {
      gantt.init('gantt_container')
      gantt.parse({
        tasks,
      })
      gantt.render()
    }

    return {
      ganttInit,
    }
  },
  template: /*html*/ `
    <div id="gantt_container" class="h-full w-full"></div>
  `,
}
