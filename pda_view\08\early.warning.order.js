import ErrorComponent from '../../components/error.component.js';
import LoadingComponent from '../../components/loading.component.js';
import { post } from '../../config/axios/httpReuest.js';
import { useTableColumn } from './useTableColumn.js';

const { onMounted, ref, defineAsyncComponent } = Vue
export default {
  name: 'EarlyWarningOrder',
  components: {
    HtVxeTable: defineAsyncComponent({
      loader: () => import('../components/ht.vxe.table.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup() {
    const { getTableColumn } = useTableColumn()
    const tableColumn = getTableColumn()
    const tableData = ref([])
    const totalNum = ref(0)
    const isShowTableData = ref(false)
    // get table data and total number
    const getTableData = async () => {
      const params = {
        ac: 'pda_GetNoDeliveryList',
        int_num: 10,
        int_page: 1,
        filter_fields: [],
        sort_fields: []
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        tableData.value = data.data
        totalNum.value = data.data.length
        isShowTableData.value = true
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    onMounted(() => {
      getTableData()
    })
    return {
      tableData,
      totalNum,
      tableColumn,
      isShowTableData
    }
  },
  // language=HTML
  template: `
    <div v-if="isShowTableData">
      <HtVxeTable :tableData :totalNum :tableColumn></HtVxeTable>
    </div>
    <el-skeleton v-else :count="5"></el-skeleton>
  `
}
