import ErrorComponent from '../../components/error.component.js'
import LoadingComponent from '../../components/loading.component.js'
import { currentDateKey, currentNodeKey, currentTypeViewKey, searchDateKey, searchFormKey } from '../../config/keys.js'
import { F2_NODE } from '../../config/nodeKey.js'
import { DM_VIEW, PDA_VIEW_AD, PDA_VIEW_P } from '../../config/tabPaneKey.js'
import { useCommApi } from '../hooks/useCommApi.js'

const { ref, reactive, provide, defineAsyncComponent, onMounted } = Vue
export default {
  name: 'F2Component',
  components: {
    CardComponent: defineAsyncComponent({
      loader: () => import('../components/card.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    TotalViewComponent: defineAsyncComponent({
      loader: () => import('../components/total.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    PViewComponent: defineAsyncComponent({
      loader: () => import('../components/p.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    DMViewComponent: defineAsyncComponent({
      loader: () => import('../components/dm.view.component.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
    HeaderSearchComponent: defineAsyncComponent({
      loader: () => import('../components/HeaderSearch/index.js'),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    }),
  },
  setup() {
    const currentTypeView = ref(PDA_VIEW_P)
    const updateCurrentTypeView = type => {
      currentTypeView.value = type
    }
    provide(currentTypeViewKey, {
      currentTypeView,
      updateCurrentTypeView,
    })

    const startDate = ref(moment().format('YYYY-MM-DD'))
    const endDate = ref(moment().add(6, 'days').format('YYYY-MM-DD'))
    provide(searchDateKey, {
      startDate,
      endDate,
      updateSearchDate: (start, end) => {
        startDate.value = start
        endDate.value = end
      },
    })

    const currentDate = ref(moment().format('YYYY-MM-DD'))
    provide(currentDateKey, {
      currentDate,
      updateCurrentDate: date => {
        currentDate.value = date
      },
    })

    provide(currentNodeKey, F2_NODE)

    const activeName = ref('0')
    const handleTabChangeByView = tab => {
      switch (tab) {
        case '0':
          updateCurrentTypeView(PDA_VIEW_P)
          break
        case '1':
          updateCurrentTypeView(PDA_VIEW_AD)
          break
        default:
          break
      }
    }
    const searchForm = reactive({
      date: [moment().format('YYYY-MM-DD'), moment().add(6, 'days').format('YYYY-MM-DD')],
      //id_engine_type: 'cfm56',
    })
    const refreshKey = ref(0)
    const updateSearchForm = form => {
      Object.assign(searchForm, form)
      refreshKey.value += 1
    }
    provide(searchFormKey, {
      searchForm,
      updateSearchForm,
    })
    // 查询
    const handleSearchClick = form => {
      updateSearchForm(form)
    }

    const headerActiveName = ref('0')
    const handleTabChange = tab => {
      switch (tab) {
        case '0':
          updateCurrentTypeView(PDA_VIEW_P)
          break
        case '1':
          updateCurrentTypeView(DM_VIEW)
          break
        default:
          break
      }
    }

    const { burialPoint } = useCommApi()

    onMounted(async () => {
      burialPoint(F2_NODE)
    })

    return {
      headerActiveName,
      handleTabChangeByView,
      activeName,
      handleSearchClick,
      handleTabChange,
      refreshKey,
    }
  },
  // language=HTML
  template: `
    <el-tabs v-model="headerActiveName" type="card" @tab-change="handleTabChange">
      <el-tab-pane label="PDA View 视图" name="0" lazy>
        <div class="mx-4">
          <HeaderSearchComponent @search="handleSearchClick"></HeaderSearchComponent>
        </div>
        <div class="w-full border-b border-gray-200 mb-4"></div>
        <!--    卡片-->
        <div class="mx-4">
          <CardComponent :key="refreshKey"></CardComponent>
        </div>
        <div class="w-full border-b border-gray-200 my-4"></div>
        <div class="mx-4">
          <TotalViewComponent :key="refreshKey"></TotalViewComponent>
        </div>

        <div class="w-full border-b border-gray-200 my-4"></div>
        <div class="mx-4">
          <el-tabs v-model="activeName" type="card" @tab-change="handleTabChangeByView">
            <el-tab-pane label="P View视图" name="0" lazy>
              <PViewComponent :key="refreshKey"></PViewComponent>
            </el-tab-pane>
            <el-tab-pane label="AD View视图" name="1" lazy>
              <PViewComponent :key="refreshKey"></PViewComponent>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="mt-4"></div>
      </el-tab-pane>
      <el-tab-pane label="DM View 视图" name="1" lazy>
        <div class="mx-4">
          <DMViewComponent></DMViewComponent>
        </div>
      </el-tab-pane>
    </el-tabs>
  `,
}
