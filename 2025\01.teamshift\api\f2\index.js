import { post } from '../../utils/request.js'
/**
 * 获取组长和组员
 */
export const queryTeamLeaderAndStaff = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_teamleaderandstaff',
  })
}


export const queryF2Site = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_f2_site_list',
  })
}

export const delF2Site = (id) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_f2_site_del',
    id:id
  })
}

export const saveF2Site = (param) => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_f2_site_save',
    data:param
  })
}

export const queryTeam = () => {
  return post('/api/Do/DoAPI', {
    au: 'ssamc',
    ap: 'api2018',
    ak: '',
    ac: 'pt_get_teams'
  })
}


