import {
  queryTaskTemplateList,
  queryTaskTemplateDetail,
  sendTask,
  completeTask,
  queryPromptMessage,
} from '../../../api/task/index.js'
import { updateEngineTaskStatus, cancelTask } from '../../../api/calendar/index.js'

export const useTaskTemplate = (props) => {
  const { ref, onMounted } = Vue
  const { ElMessage, ElLoading, ElMessageBox } = ElementPlus

  // 状态管理
  const tableData = ref([])
  const tableColumns = ref([
    {
      field: 'int_group_sort',
      title: '顺序',
      minWidth: 100,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
    },
    {
      field: 'str_wo',
      title: 'WO',
      minWidth: 100,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
    },
    {
      field: 'str_esn',
      title: 'ESN',
      minWidth: 100,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
    },
    {
      field: 'engine_type',
      title: '机型',
      minWidth: 100,
      // filterRender: { name: 'FilterInput' },
      // filters: [{ data: '' }],
    },
    {
      field: 'str_flow',
      title: 'Flow',
      minWidth: 100,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
    },
    {
      field: 'wip_flow',
      title: 'wip_flow',
      minWidth: 100,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
    },
    {
      field: 'dt_issued',
      title: '下发时间',
      minWidth: 100,
      filterRender: { name: 'FilterCalendar' },
      filters: [{ data: '' }],
    },
    {
      field: 'is_issued',
      title: '状态',
      minWidth: 100,
      filterRender: { name: 'FilterInput' },
      filters: [{ data: '' }],
      formatter: ({ cellValue }) => {
        return cellValue === 1 ? '已下发' : '未下发'
      },
    },
    {
      field: 'int_status',
      title: '完成',
      minWidth: 100,
      filters: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
        { label: '已返工', value: 999 }
      ],
      formatter: ({ cellValue }) => {
        return cellValue == 999 ? '已返工' : (cellValue == 1 ? '是' : '否')
      },
    },
  ])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(100)
  const tableRef = ref(null)
  const currentRow = ref(null)
  const isShowEditDrawer = ref(false)
  const filterParams = ref({})
  const levelids = ref([])
  const editTableData = ref([])
  const editDialogVisible = ref(false)
  const currentEditRow = ref(null)
  const addDialogVisible = ref(false)
  const currentAddRow = ref(null)
  const isSetOrderVisible = ref(false)
  const currentSetOrderRow = ref(null)
  const selectSmDialogVisible = ref(false)
  const currentSmRow = ref(null)

  // 业务方法
  const getTableData = async () => {
    const postData = {
      PageSize: pageSize.value,
      currentPage: currentPage.value,
      str_flow: props.flow,
      str_group: props.group,
      engine_type: props.engine_type,
      ...filterParams.value,
    }
    const res = await queryTaskTemplateList(postData)
    tableData.value = res.items
    total.value = res.totalCount
  }

  const getEditData = async () => {
    const postData = {
      str_flow: currentRow.value.str_flow,
      str_group: props.group,
      id_wo: currentRow.value.id_wo,
    }
    const res = await queryTaskTemplateDetail(postData)
    if (levelids.value.length === 0) {
      levelids.value = res.filter((item) => item.str_level === '1').map((item) => item.id)
    }

    // 转换平面数据为树形结构
    const convertToTree = (data) => {
      const buildTree = (items) => {
        const result = []
        items.forEach((item) => {
          const children = data.filter((child) => child.id_root === item.id)
          if (children.length) {
            item.children = children.sort((a, b) => a.int_sort - b.int_sort)
          }
          if (item.id_root === '0') {
            result.push(item)
          }
        })
        return result.sort((a, b) => a.int_sort - b.int_sort)
      }
      return buildTree(data)
    }

    editTableData.value = convertToTree(res)
  }

  // 事件处理方法
  const handleEditClick = () => {
    const selectRow = tableRef.value.getSelectedData()
    if (selectRow.length === 0) {
      ElMessage.warning('请选择一条数据')
      return
    }
    if (selectRow.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    currentRow.value = selectRow[0]
    getEditData()
    isShowEditDrawer.value = true
  }

  const handleFilterChange = (params) => {
    currentPage.value = 1
    filterParams.value[params.column.field] = params.datas?.[0] || params.values?.join(',') || ''
    getTableData()
  }

  const handlePageChange = (params) => {
    currentPage.value = params.currentPage
    pageSize.value = params.pageSize
    getTableData()
  }

  const handleRowClick = (row) => {
    if (row.str_level === '1' || row.str_level === '4') {
      return
    }
    currentEditRow.value = JSON.parse(JSON.stringify(row))
    if (row.str_level === '3') {
      currentEditRow.value.int_type = row.int_status == 1 ? 1 : 0;
    }

    editDialogVisible.value = true
  }

  const handleExpandChange = (row, expanded) => {
    if (expanded) {
      levelids.value.push(row.id)
    } else {
      levelids.value = levelids.value.filter((item) => item !== row.id)
    }
  }

  const handleComplete = async () => {
    const content = currentEditRow.value.int_sm_status === 1 ? '确定完成吗？' : '确定保存吗？'
    await updateEngineTaskStatus(
      currentRow.value.id_wo,
      (props.flow || currentRow.value.str_flow), // props.flow ||
      [currentEditRow.value.str_name],
      currentEditRow.value.int_sm_status,// 单元体状态
      currentEditRow.value.int_sort,
    )
    await getEditData()
    editDialogVisible.value = false
  }

  const handleCancel = async () => {
    const int_type = currentEditRow.value.int_type === 1 ? -1 : 0
    const params = {
      id_wo: currentRow.value.id_wo,
      id_task: currentEditRow.value.id,
      id_parent_task: currentEditRow.value.id_root,
      int_type: int_type,
      int_sort: currentEditRow.value.int_sort,
      is_task_complete: currentEditRow.value.is_task_complete,
      int_task_status: currentEditRow.value.int_task_status,
    }
    await cancelTask(params)
    setTimeout(async () => {
      await getEditData()
    }, 500)
    editDialogVisible.value = false
  }

  const handleEditSave = () => {
    if (currentEditRow.value.str_level === '2') {
      handleComplete()// 单元体完成
    } else {
      handleCancel()// 任务task 保存
    }
  }

  const handleAddTask = (row) => {
    // 根据层级判断显示哪个弹框
    if (row.str_level === '1') {
      // 层级为1时显示SM选择弹框
      selectSmDialogVisible.value = true
      currentSmRow.value = row
    } else {
      // 其他层级显示原有的任务添加弹框
      addDialogVisible.value = true
      currentAddRow.value = row
    }
  }

  const handleSetOrder = () => {
    const selectRow = tableRef.value.getSelectedData()
    if (selectRow.length === 0 || selectRow.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    currentSetOrderRow.value = selectRow[0]
    isSetOrderVisible.value = true
  }
  const handleCompleteTask = async () => {
    const selectRow = tableRef.value.getSelectedData()
    const ids = selectRow.map((item) => item.id)?.join(',')
    const id_wos = selectRow.map((item) => item.id_wo)?.join(',')
    const str_flow = props.flow
    const str_group = props.group
    const str_engine_type = props.engine_type
    const params = { ids, id_wos, str_flow, str_group, str_engine_type }
    await completeTask(params)
    ElMessage.success('操作成功')
    getTableData()
  }
  const formSendTask = ref({
    title: "下发",
    is_visible: false,
    selectedDate: moment().add(1, 'day').format('YYYY-MM-DD'),
    is_issued: 1, // 默认下发
  });
  const handleSendTask = async ({ is_issued = 1 }) => {
    const selectRow = tableRef.value.getSelectedData()
    if (selectRow.length === 0) {
      ElMessage.warning('请选择一条数据')
      return
    }
    formSendTask.value.title = is_issued === 2 ? '返工下发' : '下发';
    formSendTask.value.is_visible = true;
    formSendTask.value.is_issued = is_issued; // 设置下发状态

    // 弹出自定义对话框，内容为 el-date-picker
    // const dialog = Vue.ref(null)
    // const selectedDate = Vue.ref(moment().add(1, 'day').format('YYYY-MM-DD'))

  }
  // 下发
  const handleIssued = async () => {
    const selectRow = tableRef.value.getSelectedData()
    if (selectRow.length === 0) {
      ElMessage.warning('请选择一条数据')
      return
    }
    if (!formSendTask.value.selectedDate || moment(formSendTask.value.selectedDate).format('YYYY-MM-DD') <= moment().format('YYYY-MM-DD')) {
      ElMessage.error('请选择大于今天的日期')
      return
    }
    // 先关闭弹窗
    // dialog.value.close()
    const loading = ElLoading.service({
      lock: true,
      text: '下发中,请稍后...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const id_mains = selectRow.map((item) => item.id)?.join(',')
    const id_wos = selectRow.map((item) => item.id_wo)?.join(',')
    const str_flow = selectRow.map((item) => item.str_flow)?.join(',')
    const str_group = props.group
    const str_engine_type = props.engine_type

    const params = { id_wos, str_flow, str_group, str_engine_type, id_mains, dt_issued: formSendTask.value.selectedDate, is_issued: formSendTask.value.is_issued }
    try {
      await sendTask(params)
      ElMessage.success('下发成功')
      getTableData()
    } catch (error) {
      getTableData()
      ElMessage.error('下发失败')
    } finally {
      formSendTask.value.is_visible = false;
      formSendTask.value.selectedDate = moment().add(1, 'day').format('YYYY-MM-DD'), // 重置日期
        loading.close()
    }
  }
  const handleReWorkTask = async () => {
    const selectRow = tableRef.value.getSelectedData()
    if (selectRow.length === 0) {
      ElMessage.warning('请选择一条数据')
      return
    }
    const loading = ElLoading.service({
      lock: true,
      text: '下发中,请稍后...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const id_mains = selectRow.map((item) => item.id)?.join(',')
    const id_wos = selectRow.map((item) => item.id_wo)?.join(',')
    const str_flow = selectRow.map((item) => item.str_flow)?.join(',') //props.flow
    const str_group = props.group
    const str_engine_type = props.engine_type
    const is_issued = 2
    const params = { id_wos, str_flow, str_group, is_issued, str_engine_type, id_mains }
    try {
      await sendTask(params)
      ElMessage.success('下发成功')
      getTableData()
    } catch (error) {
      getTableData()
      ElMessage.error('下发失败')
    } finally {
      loading.close()
    }
  }

  // 获取提示信息
  const getPromptMessage = async (params) => {
    const res = await queryPromptMessage(params)
    return res
  }

  // 取消下发
  const cancelSendTask = async (params) => {
    const loading = ElLoading.service({
      lock: true,
      text: '取消下发中,请稍后...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
      await sendTask(params)
      loading.close()
      ElMessage.success('取消下发成功')
      getTableData()
    } catch (error) {
      loading.close()
      ElMessage.error('取消下发失败')
    }
  }

  const handleCancelSendTask = async () => {
    const selectRow = tableRef.value.getSelectedData()
    const id_mains = selectRow.map((item) => item.id)?.join(',')
    const id_wos = selectRow.map((item) => item.id_wo)?.join(',')
    const str_flow = selectRow.map((item) => item.str_flow)?.join(',') //props.flow
    const str_group = props.group
    const str_engine_type = props.engine_type
    const is_issued = 0
    const params = { id_wos, str_flow, str_group, is_issued, str_engine_type, id_mains }
    const message = await getPromptMessage(params)
    if (message) {
      ElMessageBox.confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await cancelSendTask(params)
      })
    } else {
      await cancelSendTask(params)
      getTableData()
    }
  }

  onMounted(() => {
    getTableData()
  })

  return {
    // 状态
    tableData,
    tableColumns,
    currentPage,
    pageSize,
    total,
    tableRef,
    editTableData,
    levelids,
    currentRow,
    isShowEditDrawer,
    editDialogVisible,
    currentEditRow,
    addDialogVisible,
    currentAddRow,
    isSetOrderVisible,
    currentSetOrderRow,
    selectSmDialogVisible,
    currentSmRow,

    // 方法
    getEditData,
    getTableData,
    handleEditClick,
    handleFilterChange,
    handlePageChange,
    handleRowClick,
    handleEditSave,
    handleAddTask,
    handleExpandChange,
    handleSetOrder,
    handleSendTask,
    handleCancelSendTask,
    handleCompleteTask,
    handleReWorkTask,
    formSendTask,
    handleIssued,
  }
}
