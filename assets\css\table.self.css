.selt_table {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
    table-layout: fixed;
}

.selt_table tr td {
    border: solid 1px #666;
    padding: 5px;
    min-height: 26px;
    line-height: 22px !important;
    word-wrap: break-word;
}

.selt_table tr td.last {
    border-bottom: none !important;
}

.selt_table tr td.field {
    background: #eee;
    color: #666;
}


.demo-table-expand {
    font-size: 0;
  }
  .demo-table-expand label {
    width: 90px;
    color: #99a9bf;
  }
  .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
  }

  .my-table .el-table__body tr:hover td {
    background-color: transparent !important;
  }
