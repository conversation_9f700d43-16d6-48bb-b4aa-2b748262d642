/* 日历卡片样式 */
.calendar-card .el-calendar {
  --el-calendar-header-height: auto;
  --el-calendar-selected-bg-color: var(--el-color-primary-light-9);
  --el-calendar-selected-color: var(--el-color-primary);
}

.calendar-card .el-calendar__header {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--el-border-color-light);
}

.calendar-card .el-calendar__body {
  padding: 0.5rem;
}

.calendar-card .el-calendar-table {
  border-collapse: separate;
  border-spacing: 2px;
}

.calendar-card .el-calendar-table td {
  border: none;
  padding: 2px;
}

.calendar-card .el-calendar-table tr td:first-child {
  border-left: none;
}

/* 日历单元格样式 */
.calendar-cell {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar-day {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.calendar-day.current-month {
  color: var(--el-text-color-primary);
}

.calendar-day.other-month {
  color: var(--el-text-color-secondary);
}

.calendar-day.today {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: 600;
}

.calendar-day.selected {
  background-color: var(--el-color-primary);
  color: white;
  font-weight: 600;
}

/* 紧凑型日历样式 */
.compact-calendar .el-calendar-table .el-calendar-day {
  height: 32px;
  padding: 0;
}

/* 日历标题样式 */
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid var(--el-border-color-light);
}

.calendar-header .current-date {
  font-size: 0.875rem;
  color: var(--el-text-color-secondary);
}

/* 悬停效果 */
.calendar-day:hover:not(.selected):not(.today) {
  background-color: var(--el-fill-color-light);
  cursor: pointer;
}

/* 响应式布局相关样式 */
@media screen and (max-width: 1280px) {
  .calendar-card {
    min-width: 240px;
  }
}

/* 确保卡片内容不溢出 */
.el-card {
  overflow: hidden;
}

/* 优化日历在小屏幕上的显示 */
.compact-calendar .el-calendar__body {
  min-width: 240px;
}

/* 优化待排班人员列表在固定宽度下的显示 */
.el-tag {
  max-width: 100%;
  text-overflow: ellipsis;
  font-size: 20px;
}