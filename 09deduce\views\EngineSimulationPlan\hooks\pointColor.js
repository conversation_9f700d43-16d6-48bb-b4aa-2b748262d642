import { getCertificateInfo } from '../../../api/index.js'
const { ref } = Vue
/**
 * 证书信息
 */
export function useCertificateInfo(tableRef) {
    const certificateInfoVisible = ref(false)

    const openCertificateInfo = () => {
        getCertificateTableData()
        certificateInfoVisible.value = true
    }

    const closeCertificateInfo = () => {
        certificateInfoVisible.value = false
    }

    const certificateTableData = ref([])
    const certificateLoading = ref(false)

    const getCertificateTableData = async () => {
        certificateLoading.value = true
        const selectedData = tableRef.value.getSelectedData()
        if (selectedData.length === 0) {
            ElementPlus.ElMessage.warning('请先选择数据')
            return
        }
        if (selectedData.length > 1) {
            ElementPlus.ElMessage.warning('只能选择一条数据')
            return
        }
        const { id_pkp } = selectedData[0] || {}
        const queryParams = {
            id_pkp,
            int_page: 0,
        }
        const { data } = await getCertificateInfo(queryParams)
        certificateTableData.value = data.data
        certificateLoading.value = false
    }

    return {
        certificateInfoVisible,
        openCertificateInfo,
        closeCertificateInfo,
        certificateTableData,
        certificateLoading,
        getCertificateTableData,
    }
}