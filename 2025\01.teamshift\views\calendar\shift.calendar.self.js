import { selfTaskShift } from '../../api/calendar/shift.calendar.self.js'
const { onMounted, ref,watch  } = Vue

/**
 * 日历组件
 */
const ShiftCalendar = {
  props: {
    page_type: '',
  },
  setup(props, { emit }) {
    const calendarDate = ref(new Date())
    const dayShift = ref([])
    /**获取登录人的版次 */
    const getData = async () => {
      try {
        const param = {
          str_year: calendarDate.value.getFullYear(),
          str_month: calendarDate.value.getMonth() + 1,
        }
        const data = await selfTaskShift(param)
        dayShift.value = data
      } catch (error) {
        ElementPlus.ElMessage.error('获取数据失败')
        console.error(error)
      }
    }
    const data_sm_test_pip = (day) => {
      const shift = dayShift.value.find((item) => moment(item.dt_plan).isSame(day, 'day'))
      return {
        shift: (shift && shift.shifts) || [],
        task: (shift && shift.str_task) || [],
      }
    }
    
    // 获取班次类型对应的颜色
    const getShiftColor = (shiftName) => {
      const name = shiftName?.toLowerCase() || ''
      if (name.includes('夜') || name.includes('night')) {
        return 'night-shift'
      } else if (name.includes('白') || name.includes('day')) {
        return 'day-shift'
      } else if (name.includes('中') || name.includes('middle')) {
        return 'middle-shift'
      }
      return 'day-shift'
    }
    
    watch(
      () => calendarDate.value, (newVal, oldVal) => {
        if (newVal && moment(newVal).format('YYYY-MM-DD') === moment().format('YYYY-MM-DD')) {
          // console.log('点击了'今天'按钮');
          getData()
        } else if (newVal && moment(newVal).toDate() < moment(oldVal).startOf('month').toDate()) {
          // console.log('点击了'上个月'按钮');
          getData()
        } else if (newVal && moment(newVal).toDate() > moment(oldVal).endOf('month').toDate()) {
          // console.log('点击了'下个月'按钮');
          getData()
        } else {
          // console.log('点击了' + moment(newVal).format('YYYY-MM') + '的按钮');
        }
      },
      { deep: true },
    )
    // 组件挂载时获取表格数据
    onMounted(async () => {
      getData()
    })

    // 返回模板需要使用的属性和方法
    return {
      calendarDate,
      getData,
      data_sm_test_pip,
      getShiftColor,
    }
  },

  // 组件模板
  template: /*html*/ `
    <el-calendar class="calendar-container" v-model="calendarDate">
      <template #date-cell="{ data }">
        <div class="calendar-date-content">
          <div class="calendar-date-number" :class="data.isSelected ? 'is-selected' : ''">
            {{ data.day.split('-').slice(1).join('-') }}
            <span v-if="data.isSelected" class="ml-1 text-blue-500">✓</span>
          </div>
          
          <!-- 班次信息 -->
          <div class="shift-container" v-if="data_sm_test_pip(data.day).shift.length > 0">
            <div 
              v-for="(item, index) in data_sm_test_pip(data.day).shift" 
              :key="'shift-' + index"
              class="flex items-center justify-between mb-1"
            >
              <span 
                class="shift-badge"
                :class="getShiftColor(item.str_shift)"
              >
                {{ item.str_shift }}
              </span>
              <span class="time-display">
                {{ item.str_start_time }} - {{ item.str_end_time }}
              </span>
            </div>
          </div>
          
          <!-- 任务信息 -->
          <div class="task-container" v-if="data_sm_test_pip(data.day).task.length > 0">
            <span 
              v-for="(task, index) in data_sm_test_pip(data.day).task" 
              :key="'task-' + index"
              class="task-badge"
            >
              {{ task }}
            </span>
          </div>
        </div>
      </template>
    </el-calendar>
  `,
}

export default ShiftCalendar
