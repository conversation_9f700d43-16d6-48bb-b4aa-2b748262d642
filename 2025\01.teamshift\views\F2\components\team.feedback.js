const { useVModel } = VueUse
const { defineComponent, ref } = Vue
export const TeamFeedback = defineComponent({
  name: 'TeamFeedback',
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
  },
  emits: ['refresh', 'saveFeedback'],
  setup(props, { emit }) {
    const openFeedback = useVModel(props, 'visible')
    const feedback = ref('')
    const handleClose = () => {
      openFeedback.value = false
    }

    const handleSaveFeedback = () => {
      emit('saveFeedback', feedback.value)
    }

    return {
      openFeedback,
      feedback,
      handleClose,
      handleSaveFeedback,
    }
  },
  template: /*html*/ `
    <el-dialog class="common-dialog" v-model="openFeedback" title="班组反馈" width="50%">
      <label class="mb-2 el-form-item__label">Remark</label>
      <el-input v-model="feedback" type="textarea" :rows="4" placeholder="Please input" />
      <template #footer>
        <el-button type="primary" @click="handleClose">Cancel</el-button>
        <el-button type="primary" @click="handleSaveFeedback">Submit</el-button>
      </template>
    </el-dialog>
  `,
})

export default TeamFeedback
