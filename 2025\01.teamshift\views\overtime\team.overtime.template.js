export default /*html*/ `
  <div class="h-screen p-2" ref="tableContainerRef">
    <!-- 操作按钮 -->
    <div class="mb-4 flex gap-2">
      <el-button type="primary" class="bg-green-500" @click="handleAdd">新增</el-button>
    </div>

    <!-- 表格 -->
    <HtVxeTable
      ref="xTableRef"
      :tableData="tableData"
      :tableColumns="tableColumns"
      :height="tableHeight"
      @filterChange="handleFilterChange"
    >
      <template #checkbox>
        <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
      </template>
      <template #operation>
        <vxe-column title="操作" min-width="280" fixed="right">
          <template #default="{ row }">
            <div class="flex gap-2">
              <span class="cursor-pointer text-blue-500 hover:text-blue-600" @click="handleView(row)">查看</span>
              <span
                class="cursor-pointer text-blue-500 hover:text-blue-600"
                v-if="(row.int_substatus==0 || row.int_substatus==-1 || row.int_substatus==-99) && is_hr != 1"
                @click="handleCommit(row)"
              >
                提交审批
              </span>
              <span
                class="cursor-pointer text-blue-500 hover:text-blue-600"
                v-if="row.int_substatus==0 || row.int_substatus==-1 || row.int_substatus==-99"
                @click="handleEdit(row)"
              >
                修改
              </span>
              <span
                class="cursor-pointer text-blue-500 hover:text-blue-600"
                @click="handleWithdraw(row)"
              >
                撤回
              </span>
              <span
                class="cursor-pointer text-red-500 hover:text-red-600"
                v-if="row.int_substatus === 0 || row_int_substatus === -1 || row_int_substatus === -99"
                @click="handleDelete(row)"
              >
                删除
              </span>
            </div>
          </template>
        </vxe-column>
      </template>
    </HtVxeTable>

    <!-- 分页组件 -->
    <div class="mt-4 flex justify-end">
      <vxe-pager
        background
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :layouts="['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
      ></vxe-pager>
    </div>

    <!-- 新增/编辑弹窗表单 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px" destroy-on-close class="common-dialog">
      <el-form label-width="100px">
        <el-form-item label="日期" required>
          <el-date-picker
            v-model="formData.dt_delay"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            
          />
        </el-form-item>
        <el-form-item label="班次" required>
          <el-select
            v-model="formData.shift"
            placeholder="请选择班次"
            @change="handleShiftChange"
            style="width: 100%"
            clearable
          >
            <el-option v-for="option in shiftOptionList" :key="option.id" :label="option.str_name" :value="option.id" />
          </el-select>
          <span style="color:red">(周末浮动排班，请选择灵活班) </span>
        </el-form-item>
        <el-form-item label="班次时间">
          <el-input disabled v-model="formData.shift_time" placeholder="请输入班次时间" />
           
        </el-form-item>

        <el-form-item label="延时范围" required>  
          <div v-for="(timeRange, index) in formData.timeRanges" :key="index" class="mb-2">
            <div class="flex items-center gap-2">
              <el-time-select
                placeholder="起始时间"
                v-model="timeRange.dt_delay_start"            
                start="00:00"
                step="00:30"
                end="23:59"
                class="flex-1"
              >
              </el-time-select>
              <el-time-select
                placeholder="结束时间"
                v-model="timeRange.dt_delay_end"
                start="00:00"
                step="00:30"
                end="23:59"
                class="flex-1"          
                @change="calculateDelay">            
              </el-time-select>
              <el-button 
                type="danger" 
                circle 
                size="small"
                icon="Delete"
                @click="removeTimeRange(index)"
                :disabled="formData.timeRanges.length <= 1"
              ></el-button>
            </div>
          </div>
          <div class="mt-2">
            <el-button type="primary" icon="Plus" size="small" @click="addTimeRange" >
              添加延时段 
            </el-button>
            <span style="color:red">(延时范围需空出吃饭的时间，分段提交) </span>
          </div>
        </el-form-item>

        <el-form-item label="延时时长">         
          <el-input disabled v-model="formData.int_delay" placeholder="请输入延时时长" />
        </el-form-item>
        
        
        <el-form-item label="Team">
          <el-select
            v-model="formData.team"
            placeholder="请选择Team"
            style="width: 100%"
            filterable
            @change="handleTeamChange"
          >
            <el-option v-for="item in teamOptionList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="人员" required>
          <el-select v-model="formData.staffs" placeholder="请选择人员" multiple style="width: 100%" filterable>
            <el-option v-for="item in staffOptionList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button type="primary" v-if="is_hr != 1" @click="handleSaveAndCommit">保存并提交</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="延时班申请详情" width="500px" destroy-on-close>
      <el-descriptions v-if="detailData" :column="1" border>
        <el-descriptions-item label="日期">{{ detailData.dt_delay }}</el-descriptions-item>
        <el-descriptions-item label="班次">{{ detailData.shift_name }}</el-descriptions-item>
        <el-descriptions-item label="班次时间">{{ detailData.shift_time }}</el-descriptions-item>
        <el-descriptions-item label="延时时长">{{ detailData.int_delay }}</el-descriptions-item>
        <el-descriptions-item label="延时范围">{{ detailData.delay_time }}</el-descriptions-item>
        <el-descriptions-item label="Team">{{ detailData.team_name }}</el-descriptions-item>
        <el-descriptions-item label="人员">{{ detailData.staff_name }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <span :class="statusColor(detailData.int_substatus)">{{ statusText(detailData.int_substatus) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="审批人">{{ detailData.str_sec_manage }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
`
