export const refreshMap = new Map();

refreshMap.set('1', 'pda_node_receive')
refreshMap.set('2', 'pda_node_check')
refreshMap.set('3', 'pda_node_instorage')
refreshMap.set('4', 'pda_node_outstorage')
refreshMap.set('5', 'pda_node_sendmaterial')
refreshMap.set('6', 'pda_node_light')
refreshMap.set('8', 'pda_node_import')
refreshMap.set('9', 'pda_node_f12_checkout')
refreshMap.set('10', 'pda_node_subcontract')
refreshMap.set('11', 'pda_node_carting')
refreshMap.set('12', 'pda_node_arrive')
refreshMap.set('13', 'pda_node_factoryPurphase')
refreshMap.set('15', 'pda_node_f2')
refreshMap.set('41', 'pda_node_outstorage')
refreshMap.set('42', 'pda_node_outstorage')
refreshMap.set('43', 'pda_node_purphase')
refreshMap.set('111', 'pda_node_subcontractpo')
refreshMap.set('141', 'pda_node_purphase')
refreshMap.set('142', 'pda_node_purphase')
refreshMap.set('411', 'pda_node_csmconfirm')
refreshMap.set('412', 'pda_node_configuration')
