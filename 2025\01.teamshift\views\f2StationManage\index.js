import { queryF2Site, queryTeam, saveF2Site } from '../../api/f2StationManage/index.js'
const { ref, reactive, onMounted } = Vue
/**
 * 站位管理
 */
const StationManagement = {
  setup() {
    const loading = ref(false)
    const tableData = ref([])
    // 获取站位管理列表
    const getStationList = async () => {
      try {
        loading.value = true
        const res = await queryF2Site()
        tableData.value = res
      } catch (error) {
        ElementPlus.ElMessage.error(error.message || '获取站位列表失败')
      } finally {
        loading.value = false
      }
    }

    const stationState = reactive({
      visible: false,
      title: '',
      str_site_code: '',
      id_team: [],
    })

    // 获取ESN下拉列表
    const allTeams = ref([])
    const teamOptions = ref([])
    const getTeamOptions = async () => {
      try {
        const data = await queryTeam()
        allTeams.value = data
      } catch (error) {
        ElementPlus.ElMessage.error(error.message || '获取ESN列表失败')
      }
    }

    const noRepeatIdWoBySameDay = (currentDate) => {
      // 获取当前日期的idWo
      const idWoList = tableData.value.filter((item) => item[currentDate]).map((item) => item.id)
      const idWoListArr = idWoList.map((item) => item.split(',')).flat()
      // 过滤掉已经存在的idWo
      teamOptions.value = allTeams.value
        .filter((item) => !idWoListArr.includes(item.id))
        .map((item) => ({
          label: item.str_code,
          value: item.id,
        }))
    }

    const addStation = (row, column) => {
      stationState.visible = true
      stationState.title = '添加站位'
      stationState.str_site_code = row.str_site_code
      stationState.currentDate = column.title
      // 同一天的idWo不能重复
      noRepeatIdWoBySameDay(column.title)
    }
    const editStation = (row, column) => {
      stationState.visible = true
      stationState.title = '编辑站位'
      stationState.currentDate = column.title
      stationState.str_site_code = row.str_site_code
      stationState.id_team = row.teams.map((item) => item.id_team)
      teamOptions.value = allTeams.value.map((item) => ({
        label: item.str_code,
        value: item.id,
      }))
    }
    const handleAddStation = async () => {
      try {
        if (stationState.id_team?.length == 0) {
          ElementPlus.ElMessage.error('Team不可为空' || '操作失败')
        } else {
          const params = {
            id: stationState.id,
            str_site_code: stationState.str_site_code,
            id_team: stationState.id_team?.length ? stationState.id_team?.join(',') : '',
          }
          loading.value = true
          const res = await saveF2Site(params)
          stationState.visible = false
          handleStationCancel()
          await getStationList()
        }
      } catch (error) {
        ElementPlus.ElMessage.error(error.message || '操作失败')
      } finally {
        loading.value = false
      }
    }
    // 取消添加站位
    const handleStationCancel = () => {
      // 初始化表单数据
      stationState.id = ''
      stationState.str_site_code = ''
      stationState.currentDate = ''
      stationState.id_team = []
      stationState.visible = false
    }

    // 表格单元格样式
    const cellClassName = ({ row, column }) => {
      return 'my-cell'
    }

    const isBeforeToday = (date) => {
      return moment(date).isBefore(moment().format('YYYY-MM-DD'))
    }

    // 添加重置功能
    const handleStationReset = () => {
      if (stationState.title === '添加站位') {
        // 添加场景 - 清空选择
        stationState.id_team = []
      } else {
        // 编辑场景 - 恢复原始数据
        const currentRow = tableData.value.find((item) => item.id === stationState.id)
        if (currentRow) {
          stationState.id_team = currentRow.id_team.split(',')
        }
      }
    }

    onMounted(() => {
      getStationList()
      getTeamOptions()
    })
    return {
      loading,
      tableData,
      addStation,
      editStation,
      stationState,
      handleAddStation,
      teamOptions,
      cellClassName,
      handleStationCancel,
      isBeforeToday,
      handleStationReset,
    }
  },
  template: /*html*/ `
    <div class="flex h-screen flex-col p-4">  
        <vxe-table
          :loading="loading"
          class="my-table"
          ref="xTable"
          border
          :data="tableData"
          height="100%"
          :cell-class-name="cellClassName"
          column-config="{ resizable: true }"
          align="center"
        >
          <vxe-column type="seq" width="70" fixed="left"></vxe-column>
          <vxe-column field="str_site_code" width="200" title="站位"></vxe-column>
          <vxe-column field="str_url" width="300" title="Url"></vxe-column>
         
          <vxe-column field="teams" min-width="100" title="Team">
            <template #default="{ row, column }">
              <div v-if="row.teams.length>0" class="flex flex-wrap gap-2 pl-2">
                <el-tag v-for="item in row.teams" :key="item.id_team" type="primary">{{ item.str_team_code }}</el-tag>
                <!-- 分割线 -->
                <div class="w-1 h-1 bg-gray-300 rounded-full"></div>
                <!-- 按钮 -->
                <el-button type="primary" size="small" @click="editStation(row, column)">编辑</el-button>
              </div>
              <div v-else>
                <el-button type="success" size="small" @click="addStation(row, column)">添加</el-button>
              </div>
            </template>
          </vxe-column>
        </vxe-table> 


      <el-dialog v-model="stationState.visible" class="station-dialog" :show-close="false">
        <template #title>
          <div class="flex justify-between items-center">
            <span class="text-white">{{ stationState.title }}</span>
            <el-button type="danger" size="small" @click="handleStationCancel">关闭</el-button>
          </div>
        </template>
        <el-form :model="stationState" width="500" class="p-2">
          <el-form-item label="站位" prop="str_site_code">
            <el-input v-model="stationState.str_site_code" readonly />
          </el-form-item>
          <el-form-item label="Team" prop="id_team">
            <!-- 虚拟下拉列表 多选，可搜索查询 -->
            <el-select-v2
              v-model="stationState.id_team"
              multiple
              filterable
              :options="teamOptions"
              placeholder="请输入Team"
              clearable
            ></el-select-v2>
          </el-form-item>
        </el-form>
        <template #footer> 
          <el-button type="primary" @click="handleAddStation">保存</el-button>
          <el-button @click="handleStationCancel">取消</el-button>
        </template>
      </el-dialog>
    </div>
  `,
}

export default StationManagement
