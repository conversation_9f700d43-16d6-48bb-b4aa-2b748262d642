// 表格数据操作
export function useTableOperations(tableData) {
  const addRow = (newRow) => {
    tableData.value.push(newRow)
  }

  const removeRow = (index) => {
    tableData.value.splice(index, 1)
  }

  const updateRow = (index, newData) => {
    Object.assign(tableData.value[index], newData)
  }

  const clearData = () => {
    tableData.value = []
  }

  const exportData = () => {
    return tableData.value.map(row => ({ ...row }))
  }

  return {
    addRow,
    removeRow,
    updateRow,
    clearData,
    exportData
  }
}

// 表格搜索和过滤
export function useTableFilter(tableData) {
  const { ref, computed } = Vue
  
  const searchKeyword = ref('')
  const filterConfig = ref({})

  const filteredData = computed(() => {
    let data = tableData.value || []
    
    // 关键词搜索
    if (searchKeyword.value) {
      data = data.filter(row => 
        Object.values(row).some(value => 
          String(value).toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
      )
    }

    // 其他过滤条件
    Object.keys(filterConfig.value).forEach(key => {
      const filterValue = filterConfig.value[key]
      if (filterValue !== undefined && filterValue !== null && filterValue !== '') {
        data = data.filter(row => row[key] === filterValue)
      }
    })

    return data
  })

  const setSearchKeyword = (keyword) => {
    searchKeyword.value = keyword
  }

  const setFilter = (field, value) => {
    filterConfig.value[field] = value
  }

  const clearFilters = () => {
    searchKeyword.value = ''
    filterConfig.value = {}
  }

  return {
    searchKeyword,
    filterConfig,
    filteredData,
    setSearchKeyword,
    setFilter,
    clearFilters
  }
} 