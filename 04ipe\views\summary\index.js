import PerformanceSummaryButton from './PerformanceSummaryButton.js'
import MesButton from './MesButton.js'
import TatButton from './TatButton.js'
import FlowTatRepeatButton from './FlowTatRepeatButton.js'
import FlowTatMissingButton from './FlowTatMissingButton.js'
import TeamButton from './TeamButton.js'
import CalculationButton from './CalculationButton.js'
import ExportButton from './ExportButton.js'
import EmployeeRatingChart from './EmployeeRatingChart.js'
import DistributionChart from './DistributionChart.js'
import NqcButton from './NqcButton.js'
import StaffScoreBoardButton from './StaffButton.js'
import {
  queryExceptionStatus,
  exportExcel,
  computeStaff,
  computeTeam,
  computeFlow,
  queryUpdateState,
  queryLockState,
  updateLockState,
} from '../../api/index.js'
import { apiEnvironment } from '../../../config/axios/httpReuest.js'
const { Search } = ElementPlusIconsVue
const { reactive, ref, onMounted, onUnmounted } = Vue
/**
 * GlobalView
 */
const GlobalView = {
  components: {
    PerformanceSummaryButton,
    MesButton,
    TatButton,
    FlowTatRepeatButton,
    FlowTatMissingButton,
    TeamButton,
    CalculationButton,
    ExportButton,
    EmployeeRatingChart,
    DistributionChart,
    NqcButton,
    StaffScoreBoardButton,
    Search,
  },
  setup() {
    /**
     * 表单响应式对象，默认为当前月份
     */
    const searchForm = reactive({
      month: moment().format('YYYY-MM'),
    })

    /**
     * 搜索按钮点击处理函数
     * 验证月份输入并更新图表数据、异常状态和锁定状态
     */
    const searchClick = () => {
      if (!searchForm.month) {
        ElementPlus.ElMessage.warning('Please select a month')
        isShowEmployeeRatingChart.value = false
        isShowDistributionChart.value = false
        return
      }
      isShowEmployeeRatingChart.value = true
      isShowDistributionChart.value = true
      employeeRef.value.getChartData(searchForm.month)
      distributionRef.value.getChartData(searchForm.month)
      getExceptionStatus()
      getUpdateState(searchForm.month)
      getLockState(searchForm.month)
    }

    /**
     * 状态标志位
     */
    const isSummaryException = ref(false)
    const isNqc = ref(false)
    const isMes = ref(false)
    const isTeamScoreBoardException = ref(false)
    const isStaffScoreBoardException = ref(false)
    const isUpdateExport = ref(false)
    const isUpdateFlow = ref(false)
    const isUpdateTeam = ref(false)
    const isUpdateStaff = ref(false)
    const isLock = ref(false)

    /**
     * 异常状态映射表
     */
    const EXCEPTION_STATUS_MAP = {
      绩效汇总: 1,
      'Team Score Board': 2,
      'Staff Score Board': 3,
      'MES': 4,
      'NQC': 5
      
    }

    /**
     * 模块配置信息
     */
    const MODULE_CONFIG = {
      FLOW_SCORE_BOARD: { id: '1806201869451661312', name: 'Flow Score Board' },
      FLOW_SCORE_SUMMARY: { id: '1826543425210695681', name: 'Flow Score Summary' },
      TEAM_SCORE_BOARD: { id: '1807658199089680385', name: 'Team Score Board' },
      STAFF_SCORE_BOARD: { id: '1808019598047449088', name: 'Staff Score Board' },
      MES_TEAM_RATIO: { id: '1805797771418013696', name: 'Mes团队占比' },
      NQC_DEDUCTION: { id: '1829075009893711872', name: 'NQC扣分' },
      Flow_TAT_REPEAT:{id: '1853979132209139713', name: '发动机重复绩效管理'},
      Flow_TAT_MISSING:{id: '1859771644135870465', name: '发动机补漏绩效管理'}
    }

    /**
     * 查询异常状态
     * @param {string} month - 查询月份
     * @param {number} key - 异常状态键值
     * @returns {Promise<boolean>} 是否存在异常
     */
    const queryException = async (month, key) => {
      try {
        const { data } = await queryExceptionStatus(month, key)
        const { code, text, data: res } = data
        if (code === 'success') {
          return res > 0
        }
        ElementPlus.ElMessage.error(text)
        return false
      } catch (error) {
        console.error('Query exception failed:', error)
        return false
      }
    }

    /**
     * 获取所有异常状态
     * 包括绩效汇总、Team Score Board和Staff Score Board的异常状态
     */
    const getExceptionStatus = async () => {
      isSummaryException.value = await queryException(searchForm.month, EXCEPTION_STATUS_MAP['绩效汇总'])
      isTeamScoreBoardException.value = await queryException(searchForm.month, EXCEPTION_STATUS_MAP['Team Score Board'])
      isStaffScoreBoardException.value = await queryException(
        searchForm.month,
        EXCEPTION_STATUS_MAP['Staff Score Board'],
      )
      isNqc.value = await queryException(
        searchForm.month,
        EXCEPTION_STATUS_MAP['NQC'],
      )
      isMes.value = await queryException(
        searchForm.month,
        EXCEPTION_STATUS_MAP['MES'],
      )
    }

    /**
     * 获取更新状态
     * @param {string} month - 查询月份，默认为当前月份
     */
    const getUpdateState = async (month = moment().format('YYYY-MM')) => {
      const { data } = await queryUpdateState(month)
      const { code, text, data: res } = data
      if (code === 'success') {
        isUpdateExport.value = res.is_update_export
        isUpdateFlow.value = res.is_update_flow
        isUpdateTeam.value = res.is_update_team
        isUpdateStaff.value = res.is_update_staff
      } else {
        ElementPlus.ElMessage.error(text)
      }
    }

    /**
     * 更新锁定状态
     * @param {string} month - 查询月份
     * @param {string} is_lock - 锁定状态
     */
    const setLockState = async (month, is_lock) => {
      try {
        const { data } = await updateLockState(month, is_lock)
        const { code, text } = data
        if (code === 'success') {
          isLock.value = is_lock === '1'
          ElementPlus.ElMessage.success(is_lock === '1' ? 'Locked' : 'Unlocked')
        } else {
          ElementPlus.ElMessage.error(text)
        }
      } catch (error) {
        console.error('Update lock state failed:', error)
        ElementPlus.ElMessage.error('更新锁定状态失败')
      }
    }

    /**
     * 获取锁定状态
     * @param {string} month - 查询月份，默认为当前月份
     */
    const getLockState = async (month = moment().format('YYYY-MM')) => {
      try {
        const { data } = await queryLockState(month)
        const { code, text, data: res } = data
        if (code === 'success') {
          isLock.value = res.is_lock === '1'
        } else {
          ElementPlus.ElMessage.error(text)
        }
      } catch (error) {
        console.error('Get lock state failed:', error)
        ElementPlus.ElMessage.error('获取锁定状态失败')
      }
    }

    /**
     * 导出Excel文件
     */
    const exportClick = async () => {
      const { data } = await exportExcel(searchForm.month)
      const { code, text, data: res } = data
      if (code === 'success') {
        if (data && data.data) {
          window.location.href = apiEnvironment + data.data
          searchClick()
        } else {
          alert('无法下载文件')
        }
      } else {
        ElementPlus.ElMessage.error(text)
      }
    }

    /**
     * 通用计算处理函数
     * @param {Function} computeFunction - 计算函数
     * @param {string} loadingText - 加载提示文本
     */
    const handleCompute = async (computeFunction, loadingText = '计算中...') => {
      console.log('handleCompute', computeFunction)
      const loading = ElementPlus.ElLoading.service({
        lock: true,
        text: loadingText,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
        try {
          const { data } = await computeFunction(searchForm.month)
          if (data.code === 'success') {
            searchClick()
            ElementPlus.ElMessage.success('计算成功')
          } else {
            ElementPlus.ElMessage.error(data.text)
          }
        } catch (error) {
          console.error('Compute failed:', error)
          ElementPlus.ElMessage.error('计算失败')
        } finally {
          loading.close()
        }
    }

    /**
     * 各种计算按钮的点击处理函数
     */
    const computeStaffClick = () => handleCompute(computeStaff)
    const computeFlowClick = () => handleCompute(computeFlow)
    const computeTeamClick = () => handleCompute(computeTeam)

    /**
     * 跳转到指定模块
     * @param {string} moduleKey - 模块键
     */
    const JumpClick = (moduleKey) => {
      const module = MODULE_CONFIG[moduleKey]
      let month = searchForm.month;
      if(moduleKey =='Flow_TAT_MISSING')
      {
        month = moment(searchForm.month).format("YYYY");
      }
      const url = `/Page/?moduleid=${module.id}&param_value={str_calculate_month:'${month}'}`
      const winparam = {
        url,
        title: module.name,
        grid: null,
        width: '1000px',
        height: '800px',
        region: null,
      }
      com.openwin(this, '', winparam, false, false, false)
    }

    /**
     * 图表相关的响应式引用
     */
    const employeeRef = ref(null)
    const isShowEmployeeRatingChart = ref(true)
    const distributionRef = ref(null)
    const isShowDistributionChart = ref(true)

    /**
     * 处理锁定状态切换
     */
    const handleLockState = async () => {
      const isLockValue = isLock.value ? '0' : '1'
      const lockName = isLock.value ? '解锁' : '锁定'

      const res = await ElementPlus.ElMessageBox.confirm('是否'+lockName+searchForm.month+'财务月的计算', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      if (res === 'confirm') {
        try {
         
          setLockState(searchForm.month, isLockValue)
        } catch (error) {
          console.error('Lock/Unlock failed:', error)
          ElementPlus.ElMessage.error('操作失败')
        }
      }
    }

    /**
     * 重新渲染图表
     */
    const reRenderCharts = () => {
      if (employeeRef.value && distributionRef.value) {
        employeeRef.value.resizeChart()
        distributionRef.value.resizeChart()
      }
    }

    /**
     * 处理浏览器缩放
     */
    const handleZoom = () => {
      // 使用 requestAnimationFrame 来优化性能
      window.requestAnimationFrame(() => {
        reRenderCharts()
      })
    }

    /**
     * 添加缩放监听器
     */
    const addZoomListener = () => {
      // 监听窗口大小变化
      window.addEventListener('resize', handleZoom)

      // 监听浏览器缩放
      window.visualViewport?.addEventListener('resize', handleZoom)
    }

    /**
     * 组件挂载时初始化数据
     */
    onMounted(() => {
      getExceptionStatus()
      getUpdateState()
      getLockState()
      addZoomListener() // 添加缩放监听
    })

    onUnmounted(() => {
      // 清理监听器
      window.removeEventListener('resize', handleZoom)
      window.visualViewport?.removeEventListener('resize', handleZoom)
    })

    return {
      searchForm,
      searchClick,
      exportClick,
      computeStaffClick,
      computeFlowClick,
      computeTeamClick,
      JumpClick,
      employeeRef,
      getExceptionStatus,
      isShowEmployeeRatingChart,
      distributionRef,
      isShowDistributionChart,
      isSummaryException,
      isNqc,
      isMes,
      isTeamScoreBoardException,
      isStaffScoreBoardException,
      isUpdateExport,
      isUpdateFlow,
      isUpdateTeam,
      isUpdateStaff,
      getUpdateState,
      getLockState,
      isLock,
      handleLockState,
      reRenderCharts,
    }
  },
  template: /*html*/ `
    <div class="flex h-screen flex-col overflow-x-hidden bg-gray-100 p-1.5">
      <!-- 头部区域 -->
      <div class="mx-auto mb-1.5 w-full h-full/2 flex rounded-lg bg-white p-2  justify-between">
        <div class="flex items-center ">
          <!-- 搜索和锁定区域 -->
          <div class="flex items-center gap-3">
            <div class="flex items-center gap-2">
              <span class="whitespace-nowrap">Month:</span>
              <el-date-picker
                v-model="searchForm.month"
                type="month"
                placeholder="Select month"
                value-format="YYYY-MM"
                class="w-36"
              />
            </div>
            <div class="ml-4 flex items-center gap-2">
              <el-button type="primary" @click="searchClick">
                <el-icon class="mr-1"><search /></el-icon>
                Search
              </el-button>
            </div>
          </div>
        </div>
        <div class="flex items-center"> 
        
          <div class="ml-4 flex items-center gap-2 ml-2 mr-1 ">
              <el-button :type="!isLock ? 'success' : 'danger'" @click="handleLockState" class="min-w-[90px]">
              <el-icon class="mr-1">
                <component :is="!isLock ? 'Unlock' : 'Lock'" />
              </el-icon>
              {{ !isLock ? 'Unlock' : 'Lock' }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 图表区域 - 设置固定高度 -->
      <div class="mx-auto w-full flex-1 rounded-lg bg-white shadow-sm">
        <div class="flex h-full gap-2">
          <div class="w-1/2">
            <div class="h-full rounded-lg bg-gray-50">
              <EmployeeRatingChart v-show="isShowEmployeeRatingChart" ref="employeeRef" />
              <el-empty v-show="!isShowEmployeeRatingChart" />
            </div>
          </div>
          <div class="w-1/2">
            <div class="h-full rounded-lg bg-gray-50">
              <DistributionChart v-show="isShowDistributionChart" ref="distributionRef" />
              <el-empty v-show="!isShowDistributionChart" />
            </div>
          </div>
        </div>
        <div class="border-t border-gray-300"></div>
      </div>

      <!-- Summary List -->
      <h3 class="mt-2 mb-2">Summary List</h3>
      <div class="border-t border-gray-300"></div>
      <div class="mx-auto mb-1.5 w-full flex-none rounded-lg bg-white shadow-sm">
        <div class="grid grid-cols-4 gap-2">
        
          <div class="transform rounded-lg bg-gray-50 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md">
            <MesButton :is-abnormal="isMes" @click="JumpClick('MES_TEAM_RATIO')" class="h-full w-full" />
          </div>
          <div class="transform rounded-lg bg-gray-50 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md">
            <NqcButton :is-abnormal="isNqc" @click="JumpClick('NQC_DEDUCTION')" class="h-full w-full" />
          </div>
          <div class="relative rounded-lg bg-gray-50"
          :class="[isLock ? 'cursor-not-allowed' : 'transform rounded-lg bg-gray-50 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md']">
          <div v-if="isLock" class="absolute inset-0 z-10 bg-gray-200 bg-opacity-50"></div>
              <FlowTatRepeatButton @click="JumpClick('Flow_TAT_REPEAT')" class="h-full w-full" />
          </div>

          <div class="relative rounded-lg bg-gray-50"
          :class="[isLock ? 'cursor-not-allowed' : 'transform rounded-lg bg-gray-50 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md']">
              <div v-if="isLock" class="absolute inset-0 z-10 bg-gray-200 bg-opacity-50"></div>
              <FlowTatMissingButton @click="JumpClick('Flow_TAT_MISSING')" class="h-full w-full" />
          </div>

          <div class="transform rounded-lg bg-gray-50 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md">
            <PerformanceSummaryButton
              :is-abnormal="isSummaryException"
              @click="JumpClick('FLOW_SCORE_BOARD')"
              class="h-full w-full"
            />
          </div>
          <div class="transform rounded-lg bg-gray-50 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md">
            <TeamButton
              :is-abnormal="isTeamScoreBoardException"
              @click="JumpClick('TEAM_SCORE_BOARD')"
              class="h-full w-full"
            />
          </div>
          <div class="transform rounded-lg bg-gray-50 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md">
            <StaffScoreBoardButton
              :is-abnormal="isStaffScoreBoardException"
              @click="JumpClick('STAFF_SCORE_BOARD')"
              class="h-full w-full"
            />
          </div>
          <div class="transform rounded-lg bg-gray-50 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md">
          <TatButton @click="JumpClick('FLOW_SCORE_SUMMARY')" class="h-full w-full" />
        </div>
         
        </div>
      </div>

      <!-- Compute & Export -->

      <h3 class="mt-2 mb-2">Compute & Export</h3>
      <div class="border-t border-gray-300"></div>
      <div class="mx-auto   w-full flex-none rounded-lg bg-white  shadow-sm">
        <div class="grid grid-cols-4 gap-2">
          <div
            class="relative rounded-lg bg-gray-50"
            :class="[isLock ? 'cursor-not-allowed' : 'hover:shadow-md transition-all duration-300 transform hover:-translate-y-0.5']">
            <div v-if="isLock" class="absolute inset-0 z-10 bg-gray-200 bg-opacity-50"></div>
            <CalculationButton
              :isUpdateFlow="isUpdateFlow"
              title="Flow TAT Compute"
              @click="computeFlowClick"
              class="h-full w-full"
              :disabled="isLock"
            />
          </div>
          <div
            class="relative rounded-lg bg-gray-50"
            :class="[isLock ? 'cursor-not-allowed' : 'hover:shadow-md transition-all duration-300 transform hover:-translate-y-0.5']">
            <div v-if="isLock" class="absolute inset-0 z-10 bg-gray-200 bg-opacity-50"></div>
            <CalculationButton
              :isUpdateTeam="isUpdateTeam"
              title="Team Score Compute"
              @click="computeTeamClick"
              class="h-full w-full"
              :disabled="isLock"
            />
          </div>
          <div
            class="relative rounded-lg bg-gray-50"
            :class="[isLock ? 'cursor-not-allowed' : 'hover:shadow-md transition-all duration-300 transform hover:-translate-y-0.5']">
            <div v-if="isLock" class="absolute inset-0 z-10 bg-gray-200 bg-opacity-50"></div>
              <CalculationButton
                :isUpdateStaff="isUpdateStaff"
                title="Staff Score Compute"
                @click="computeStaffClick"
                class="h-full w-full"
                :disabled="isLock"
              />
            </div>
          <div class="transform rounded-lg bg-gray-50 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md">
            <ExportButton :isUpdateExport="isUpdateExport" @click="exportClick" class="h-full w-full" />
          </div>
        </div>
      </div>
      <div class="border-t border-gray-300"></div>
    </div>
  `,
}

export default GlobalView
