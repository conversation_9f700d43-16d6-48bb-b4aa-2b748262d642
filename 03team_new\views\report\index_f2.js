let idWoS = [];
let idTeams = [];// 当前站位的所有Team
let currentIndex = 0;
let currentIdWo = "";
let currentFlow = "F0" // 当前Flow

// 自动滚动相关变量
let autoScrollIntervals = {};
const AUTO_SCROLL_SPEED = 1; // 滚动速度（像素/次）
const AUTO_SCROLL_DELAY = 30; // 滚动间隔（毫秒）
const AUTO_SCROLL_PAUSE = 2000; // 到达底部后的暂停时间（毫秒）
const AUTO_SCROLL_START_DELAY = 2000; // 开始滚动前的延迟时间（毫秒）

// API URL和基础参数
const API_URL = 'http://192.168.209.22/api/Do/DoAPI';
const API_BASE_PARAMS = {
  "au": "ssamc",
  "ap": "api2018",
  "ak": ""
};
// 兼容性日期显示
window.onload = function () {

  $('body').append(template);
  document.getElementById('stationInfo').innerHTML = stationName;
  let date = new Date();
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  document.getElementById('currentDate').innerHTML = year + '-' + month + '-' + day;

  getData();// 当前占位 team

  fetchChartData();// 获取图表数据
  fetchTasksData();// 获取任务数据

  //getEngineInfo();// 当前占位 idwo

  //setInterval(getEngineInfo, 1000 * 60 * 5); // 5分钟获取一次10 播放下一台，多台时使用

}

// 初始化自动滚动
function initAutoScroll() {
  // 为所有需要自动滚动的容器启动自动滚动
  const scrollContainers = [
    { id: 'monthChart', type: 'chart' },
    { id: 'weekChart', type: 'chart' },
    { id: 'yesterdayTasks', type: 'table' },
    { id: 'todayTasks', type: 'table' }
  ];

  scrollContainers.forEach(container => {
    startAutoScroll(container.id, container.type);
  });
}

// 开始自动滚动
function startAutoScroll(containerId, containerType) {
  // 延迟启动，让用户先看到内容
  setTimeout(() => {
    // 清除现有的滚动间隔
    if (autoScrollIntervals[containerId]) {
      clearInterval(autoScrollIntervals[containerId]);
    }

    // 获取滚动容器
    let scrollContainer;
    if (containerType === 'chart') {
      // 对于图表，需要找到图表的滚动内容容器
      const chartContainer = document.getElementById(containerId);
      if (chartContainer) {
        scrollContainer = chartContainer.querySelector('.chart-scroll-content');
      }
    } else if (containerType === 'table') {
      // 对于表格，需要找到表格的父容器
      const tableElement = document.getElementById(containerId);
      if (tableElement) {
        scrollContainer = tableElement.closest('.table-container');
      }
    }

    if (!scrollContainer) return;

    let scrollDirection = 1; // 1 向下，-1 向上
    let isPaused = false;

    autoScrollIntervals[containerId] = setInterval(() => {
      if (isPaused) return;

      const currentScrollTop = scrollContainer.scrollTop;
      const maxScrollTop = scrollContainer.scrollHeight - scrollContainer.clientHeight;

      // 如果内容高度小于容器高度，不需要滚动
      if (maxScrollTop <= 0) return;

      // 计算下一个滚动位置
      const nextScrollTop = currentScrollTop + (AUTO_SCROLL_SPEED * scrollDirection);

      // 检查是否到达边界
      if (nextScrollTop >= maxScrollTop) {
        // 到达底部，暂停后回到顶部
        isPaused = true;
        setTimeout(() => {
          scrollContainer.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
          scrollDirection = 1;
          isPaused = false;
        }, AUTO_SCROLL_PAUSE);
      } else if (nextScrollTop <= 0) {
        // 到达顶部，暂停后向下滚动
        isPaused = true;
        setTimeout(() => {
          scrollDirection = 1;
          isPaused = false;
        }, AUTO_SCROLL_PAUSE);
      } else {
        // 正常滚动
        scrollContainer.scrollTop = nextScrollTop;
      }
    }, AUTO_SCROLL_DELAY);
  }, AUTO_SCROLL_START_DELAY);
}

// 停止自动滚动
function stopAutoScroll(containerId) {
  if (autoScrollIntervals[containerId]) {
    clearInterval(autoScrollIntervals[containerId]);
    delete autoScrollIntervals[containerId];
  }
}

// 暂停所有自动滚动
function pauseAllAutoScroll() {
  Object.keys(autoScrollIntervals).forEach(containerId => {
    stopAutoScroll(containerId);
  });
}

// 恢复所有自动滚动
function resumeAllAutoScroll() {
  initAutoScroll();
}

// 鼠标悬浮时暂停滚动，离开时恢复
function addScrollPauseListeners() {
  const containers = ['monthChart', 'weekChart'];
  const tableContainers = document.querySelectorAll('.table-container');

  // 为图表容器添加监听器
  containers.forEach(containerId => {
    const chartContainer = document.getElementById(containerId);
    if (chartContainer) {
      const scrollContent = chartContainer.querySelector('.chart-scroll-content');
      if (scrollContent) {
        scrollContent.addEventListener('mouseenter', () => {
          stopAutoScroll(containerId);
        });

        scrollContent.addEventListener('mouseleave', () => {
          startAutoScroll(containerId, 'chart');
        });
      }
    }
  });

  // 为表格容器添加监听器
  tableContainers.forEach((container, index) => {
    const tableId = index === 0 ? 'yesterdayTasks' : 'todayTasks';

    container.addEventListener('mouseenter', () => {
      stopAutoScroll(tableId);
    });

    container.addEventListener('mouseleave', () => {
      startAutoScroll(tableId, 'table');
    });
  });
}

// 格式化日期时间
function formatDateTime() {
  var now = new Date();
  var year = now.getFullYear();
  var month = String(now.getMonth() + 1).padStart(2, '0');
  var day = String(now.getDate()).padStart(2, '0');
  var hours = String(now.getHours()).padStart(2, '0');
  var minutes = String(now.getMinutes()).padStart(2, '0');

  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
}


/*** 获取占位下的Team接口数据*/
function getData() {
  $.ajax({
    async: false,
    type: "POST",
    url: API_URL,
    data: {
      ac: "pt_query_team_by_site",
      ak: "",
      ap: "",
      au: "ssamc",
      siteCode: stationName
    },
    dataType: "json",
    success: function (rsp) {
      if (rsp.code == "success" && rsp.data) {
        document.getElementById('teamNumber').textContent = 'Team: ' + rsp.data.map(x => x.str_team).join('&');
        document.getElementById('teamLeader').textContent = 'Team Leader: ' + rsp.data.map(x => x.str_team_leader).join('&');
        idTeams = rsp.data.map(x => x.id_team);
      }
    }
  });
}

function fetchChartData() {
  // 显示加载状态
  document.querySelectorAll('.chart-container').forEach(function (container) {
    container.innerHTML = '<div class="loading">加载数据中...</div>';
  });
  $.ajax({
    async: false,
    type: "POST",
    url: API_URL,
    data: {
      ac: "pt_f2_bar_chart",
      ak: "",
      ap: "",
      au: "ssamc",
      siteCode: stationName
    },
    dataType: "json",
    success: function (rsp) {
      if (rsp.code == "success" && rsp.data) {
        renderChart('monthChart', groupChartData(rsp.data.monthChart));
        renderChart('weekChart', groupChartData(rsp.data.weekChart));

        // 数据加载完成后，延迟启动自动滚动和添加鼠标监听器
        setTimeout(() => {
          startAutoScroll('monthChart', 'chart');
          startAutoScroll('weekChart', 'chart');
          addScrollPauseListeners();
        }, 1000);
      }
    }
  });
}

// 将API返回的图表数据按工单号(str_wo)分组
function groupChartData(apiData) {
  if (!apiData || !apiData.length) return [];

  // 按工单号分组
  var groupedByWo = {};

  apiData.forEach(function (item) {
    // 安全地访问属性，确保不会因为缺少属性而报错
    if (!item) return; // 跳过无效数据项

    // 确保工单号(str_wo)存在
    var woKey = item.str_wo || '未知工单';
    var esnId = item.str_esn || '未知ESN';

    // 初始化工单组
    if (!groupedByWo[woKey]) {
      groupedByWo[woKey] = {
        id: esnId + '(' + woKey + ')',
        data: {
          percent: item.is_finish === 1 ? "100%" : "In progress"
        }
      };
    }

    try {
      // 确保str_type存在且为字符串
      var typeStr = (item.str_type || 'unknown').toString();
      // 获取类型小写形式（如 b1, b2, b3, b4 等）
      var typeKey = typeStr.toLowerCase();

      // 确保TAT值存在且为数字
      var tatValue = parseFloat(item.dec_tat || 0);
      var overdueTatValue = parseFloat(item.dec_overdue_tat || 0);

      // 处理所有类型
      if (typeKey && typeKey !== 'unknown') {
        // 初始化类型数据
        if (!groupedByWo[woKey].data[typeKey]) {
          groupedByWo[woKey].data[typeKey] = {
            segments: []
          };
        }

        // 如果有TAT值，添加为常规TAT段
        if (tatValue > 0) {
          groupedByWo[woKey].data[typeKey].segments.push({
            value: Math.round(tatValue),
            isOverdue: false // 标记为非超期
          });
        }

        // 如果有超期时间，添加为超期段
        if (overdueTatValue > 0) {
          groupedByWo[woKey].data[typeKey].segments.push({
            value: Math.round(overdueTatValue),
            isOverdue: true // 标记为超期
          });
        }
      }

      // 更新完成状态（只要有一个完成，整个工单就显示完成）
      if (item.is_finish === 1) {
        groupedByWo[woKey].data.percent = "100%";
      }
    } catch (err) {
      console.error('处理图表数据项时出错:', err, item);
      // 错误处理继续下一项，不中断整个循环
    }
  });

  // 将对象转换回数组
  // return Object.values(groupedByWo);
  // 将对象转换回数组
  let result = [];
  for (var key in groupedByWo) {
    if (groupedByWo.hasOwnProperty(key)) {
      result.push(groupedByWo[key]);
    }
  }
  return result;
}

// 渲染图表
function renderChart(containerId, dataArray) {
  try {
    var container = document.getElementById(containerId);
    container.innerHTML = '';

    // 如果数据为空，显示无数据
    if (!dataArray || dataArray.length === 0) {
      container.innerHTML = '<div style="text-align:center;">Empty</div>';
      return;
    }

    // 创建滚动内容容器
    var scrollContent = document.createElement('div');
    scrollContent.className = 'chart-scroll-content';

    // 找出所有数据中的最大值
    var maxValue = 0;
    dataArray.forEach(function (group) {
      if (!group || !group.data) return;

      // 遍历所有类型数据（b1, b2, b3, b4等）
      Object.keys(group.data).forEach(function (key) {
        if (key === 'percent') return; // 跳过百分比字段

        var data = group.data[key];
        if (data && data.segments && Array.isArray(data.segments)) {
          // 遍历所有数据段
          data.segments.forEach(function (segment) {
            if (!isNaN(segment.value) && segment.value > maxValue) {
              maxValue = segment.value;
            }
          });
        }
      });
    });

    // 为了美观，将最大值向上取整到100的倍数
    maxValue = Math.ceil(maxValue / 100) * 100;
    if (maxValue < 100) maxValue = 100;

    // 计算调整后的最大值（用于刻度显示）
    const tickStep = calculateOptimalTickStep(maxValue);
    const adjustedMaxValue = Math.ceil(maxValue / tickStep) * tickStep;

    // 创建X轴网格线（添加到滚动内容中）
    createXGridLines(scrollContent, maxValue);

    // 直接使用传入的dataArray（已经是分组好的数据）
    const chartData = dataArray;

    // 定义颜色
    const TAT_COLOR = '#66BB6A';    // 绿色：常规TAT
    const OVERDUE_COLOR = '#EF5350';  // 红色：超期TAT

    chartData.forEach(function (group) {
      if (!group || !group.data) return; // 跳过无效数据

      var chartGroup = document.createElement('div');
      chartGroup.className = 'chart-group';

      // 创建组标题（ESN和百分比）
      var groupHeader = document.createElement('div');
      groupHeader.className = 'group-header';

      var esnLabel = document.createElement('div');
      esnLabel.className = 'esn-code';
      esnLabel.textContent = group.id || '未知ESN';
      groupHeader.appendChild(esnLabel);

      if (group.data.percent) {
        var percentLabel = document.createElement('div');
        percentLabel.className = 'percentage';
        percentLabel.textContent = group.data.percent;
        groupHeader.appendChild(percentLabel);
      }

      chartGroup.appendChild(groupHeader);

      // 收集所有有数据的类型
      var validTypes = [];
      Object.keys(group.data).forEach(function (typeKey) {
        if (typeKey === 'percent') return; // 跳过percent属性

        const typeData = group.data[typeKey];
        const hasData = typeData !== undefined &&
          typeData.segments &&
          typeData.segments.length > 0 &&
          typeData.segments.some(segment => !isNaN(segment.value) && segment.value > 0);

        if (hasData) {
          validTypes.push({
            key: typeKey,
            data: typeData
          });
        }
      });

      // 如果有有效数据，创建合并显示的单行
      if (validTypes.length > 0) {
        var chartItem = document.createElement('div');
        chartItem.className = 'chart-item';

        var barLabel = document.createElement('div');
        barLabel.className = 'bar-label';
        // 创建复合标签，显示所有类型
        barLabel.textContent = validTypes.map(t => t.key.toUpperCase()).join('+');
        chartItem.appendChild(barLabel);

        var barWrapper = document.createElement('div');
        barWrapper.className = 'bar-wrapper';

        // 遍历所有有效类型，在同一行显示
        validTypes.forEach(function (typeInfo, typeIndex) {
          if (typeInfo.data.segments && typeInfo.data.segments.length > 0) {
            // 遍历该类型的所有数据段
            typeInfo.data.segments.forEach(segment => {
              if (!isNaN(segment.value) && segment.value > 0) {
                var segmentWidth = (segment.value / adjustedMaxValue * 100) + '%';

                var barSegment = document.createElement('div');
                barSegment.className = 'bar-segment';
                barSegment.style.width = segmentWidth;

                // 计算实际百分比
                var percentValue = (segment.value / adjustedMaxValue) * 100;

                // 为不同类型设置不同的颜色系列
                var colors = [
                  { normal: '#66BB6A', overdue: '#EF5350', dark: '#2e7d32', darkOverdue: '#d32f2f' }, // B1: 绿色/红色
                  { normal: '#42A5F5', overdue: '#FF7043', dark: '#1565c0', darkOverdue: '#d84315' }, // B2: 蓝色/橙色
                  { normal: '#AB47BC', overdue: '#EC407A', dark: '#6a1b9a', darkOverdue: '#ad1457' }, // B3: 紫色/粉色
                  { normal: '#FFA726', overdue: '#8D6E63', dark: '#e65100', darkOverdue: '#3e2723' }  // B4: 橙色/棕色
                ];

                var colorSet = colors[typeIndex % colors.length];

                // 根据是否为超期设置颜色
                if (segment.isOverdue) {
                  barSegment.style.backgroundColor = percentValue < 5 ? colorSet.darkOverdue : colorSet.overdue;
                } else {
                  barSegment.style.backgroundColor = percentValue < 5 ? colorSet.dark : colorSet.normal;
                }

                // 所有段都在内部显示数值，并添加类型标识
                var barValue = document.createElement('div');
                barValue.className = 'bar-value';
                barValue.textContent = typeInfo.key.toUpperCase() + ':' + segment.value;

                // 对于非常短的段，调整字体大小以确保数值可见
                if (percentValue < 8) {
                  barValue.style.fontSize = '14px';
                  // 为超短段添加最小宽度，确保数字至少部分可见
                  if (percentValue < 4) {
                    barSegment.style.minWidth = '30px';
                  }
                }

                barSegment.appendChild(barValue);
                barWrapper.appendChild(barSegment);
              }
            });
          }
        });

        chartItem.appendChild(barWrapper);
        chartGroup.appendChild(chartItem);
      }

      scrollContent.appendChild(chartGroup);
    });

    // 将滚动内容添加到容器
    container.appendChild(scrollContent);

    // 创建固定的X轴刻度（添加到主容器底部）
    createXAxis(container, maxValue);
  } catch (error) {
    var container = document.getElementById(containerId);
    container.innerHTML = '<div class="error-message">图表渲染失败: ' + error.message + '</div>';
    return;
  }
}

// 计算最优刻度步长（分级刻度系统）
function calculateOptimalTickStep(maxValue) {
  // 分级刻度系统：根据数据范围选择合适的步长
  if (maxValue <= 100) {
    return 10;  // 0-100范围使用10为步长
  } else if (maxValue <= 500) {
    return 50;  // 100-500范围使用50为步长
  } else if (maxValue <= 1000) {
    return 100; // 500-1000范围使用100为步长
  } else if (maxValue <= 2000) {
    return 200; // 1000-2000范围使用200为步长
  } else {
    return 500; // 2000+范围使用500为步长
  }
}

// 创建X轴刻度（固定在底部）
function createXAxis(container, maxValue) {
  // 清除现有的X轴
  const existingAxis = container.querySelector('.x-axis');
  if (existingAxis) {
    existingAxis.remove();
  }

  // 创建X轴容器
  const xAxis = document.createElement('div');
  xAxis.className = 'x-axis';

  // 使用分级刻度系统计算最优步长
  const tickStep = calculateOptimalTickStep(maxValue);

  // 将maxValue向上取整到步长的倍数
  const adjustedMaxValue = Math.ceil(maxValue / tickStep) * tickStep;

  // 根据步长计算刻度数量
  const tickCount = adjustedMaxValue / tickStep;

  // 创建刻度和标签
  for (let i = 0; i <= tickCount; i++) {
    // 使用固定步长计算刻度值
    const value = i * tickStep;

    // 计算位置百分比（基于调整后的最大值）
    const position = (value / adjustedMaxValue * 100) + '%';

    // 创建刻度标记
    const tick = document.createElement('div');
    tick.className = 'x-axis-mark';

    // 刻度位置根据值设置
    tick.style.position = 'absolute';
    tick.style.left = position;

    // 创建刻度标签
    const label = document.createElement('div');
    label.className = 'x-axis-label';
    label.textContent = value;

    tick.appendChild(label);
    xAxis.appendChild(tick);
  }

  container.appendChild(xAxis);
}

// 创建X轴网格线（在滚动内容中）
function createXGridLines(scrollContent, maxValue) {
  // 清除现有的网格线
  const existingGridLines = scrollContent.querySelector('.x-grid-lines');
  if (existingGridLines) {
    existingGridLines.remove();
  }

  // 创建网格线容器
  const gridLines = document.createElement('div');
  gridLines.className = 'x-grid-lines';

  // 使用分级刻度系统计算最优步长
  const tickStep = calculateOptimalTickStep(maxValue);

  // 将maxValue向上取整到步长的倍数
  const adjustedMaxValue = Math.ceil(maxValue / tickStep) * tickStep;

  // 根据步长计算刻度数量
  const tickCount = adjustedMaxValue / tickStep;

  // 创建网格线
  for (let i = 1; i < tickCount; i++) {
    // 使用固定步长计算刻度值
    const value = i * tickStep;

    // 计算位置百分比（基于调整后的最大值）
    const position = (value / adjustedMaxValue * 100) + '%';

    // 创建网格线
    const gridLine = document.createElement('div');
    gridLine.className = 'x-grid-line';
    gridLine.style.left = position;
    gridLines.appendChild(gridLine);
  }

  scrollContent.appendChild(gridLines);
}
/** 任务 */
function fetchTasksData() {
  // 显示加载状态
  document.getElementById('todayTasks').innerHTML = '<tr><td colspan="8" class="loading">加载数据中...</td></tr>';
  document.getElementById('yesterdayTasks').innerHTML = '<tr><td colspan="8" class="loading">加载数据中...</td></tr>';
  $.ajax({
    async: false,
    type: "POST",
    url: API_URL,
    data: {
      ac: "pt_f2_task_list",
      ak: "",
      ap: "",
      au: "ssamc",
      siteCode: stationName
    },
    dataType: "json",
    success: function (rsp) {
      if (rsp.code == "success" && rsp.data) {
        renderTasks('todayTasks', rsp.data.nowDatas);
        renderTasks('yesterdayTasks', rsp.data.yesterdayDatas);

        // 数据加载完成后，延迟启动表格的自动滚动
        setTimeout(() => {
          startAutoScroll('todayTasks', 'table');
          startAutoScroll('yesterdayTasks', 'table');
        }, 1000);
      }
    }
  });
}

// 格式化任务数据
function formatTasks(apiData) {
  return apiData.map(function (item, index) {
    return {
      id: index + 1,
      esn: item.str_esn || '',
      type: item.str_type || '',
      taskType: item.str_task_type || '',
      task: item.str_task || '',
      date: item.dt_range || '',
      people: item.str_staff || '',
      complete: item.str_complete || ''
    };
  });
}

// 渲染任务表格
function renderTasks(containerId, tasks) {
  let container = document.getElementById(containerId);
  let html = '';
  if (tasks.length === 0) {
    html = '<tr><td colspan="8" style="text-align:center;">Empty</td></tr>';
  } else {
    tasks.forEach(function (task, index) {
      // 根据完成状态设置样式类名
      // let statusClass = task.complete === '完成' ? 'status-complete' :
      //   task.complete === '进行中' ? 'status-pending' : '';

      html += '<tr class="task-row">' +
        '<td>' + (index + 1 || '') + '</td>' +
        '<td class="esn-code-small">' + (task.str_esn || '') + '</td>' +
        '<td>' + (task.str_type || '') + '</td>' +
        '<td>' + (task.str_task_type || '') + '</td>' +
        '<td>' + (task.str_task || '') + '</td>' +
        '<td>' + (task.dt_range || '') + '</td>' +
        '<td>' + (task.str_staff || '') + '</td>' +
        // '<td class="' + statusClass + '">' + (task.complete || '') + '</td>' +
        '</tr>';
    });
  }

  container.innerHTML = html;
}

let template =
  `
      <div class="info-section">
      <span id="stationInfo" class="station-info">Work Station: </span>
      <span id="teamNumber" class="station-info">Team: </span>
      <span id="teamLeader" class="station-info">Team Leader: </span>
      <!--<span id="teamRotation" class="station-info" style="font-size: 12px; opacity: 0.8;">(自动切换: 30:00)</span>-->
      <span class="date">
        DATE:
        <span id="currentDate"></span>
      </span>
    </div>

    <div class="dashboard">
      <div class="section">
        <div class="card">
          <div class="header">
            <span>Month</span>
            <span class="refresh-time"></span>
          </div>
          <div class="card-content">
            <div id="monthChart" class="chart-container">
              <div class="loading">加载数据中...</div>
            </div>
          </div>
        </div>
        <div class="card">
          <div class="header">
            <span>Yesterday</span>
            <span class="refresh-time"></span>
          </div>
          <div class="card-content">
            <div class="table-container">
              <table class="task-table">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>ESN</th>
                    <th>Type</th>
                    <th>Task type</th>
                    <th>Task</th>
                    <th>Date</th>
                    <th>People</th>
                    <!--<th>Complete</th>-->
                  </tr>
                </thead>
                <tbody id="yesterdayTasks">
                  <tr><td colspan="8" class="loading">加载数据中...</td></tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="section">
        <div class="card">
          <div class="header">
            <span>Week</span>
            <span class="refresh-time"></span>
          </div>
          <div class="card-content">
            <div id="weekChart" class="chart-container">
              <div class="loading">加载数据中...</div>
            </div>
          </div>
        </div>
        <div class="card">
          <div class="header">
            <span>Today</span>
            <span class="refresh-time"></span>
          </div>
          <div class="card-content">
            <div class="table-container">
              <table class="task-table">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>ESN</th>
                    <th>Type</th>
                    <th>Task type</th>
                    <th>Task</th>
                    <th>Date</th>
                    <th>People</th>
                   <!-- <th>Complete</th>-->
                  </tr>
                </thead>
                <tbody id="todayTasks">
                  <tr><td colspan="8" class="loading">加载数据中...</td></tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
 
 `
