import {
  queryTableData,
  adjustTask,
  getTeamByType,
  getShiftList,
  getSmList,
  cancelTask,
  updateEngineTaskStatus,
  lockTaskView,
  getWarning,
  getTaskListByTaskId,
  getAddSmList,
  getTeamStaff,
  getTeamSecStaff,
  addTask,
  saveStaff,
} from '../../api/calendar/index.js'
import TaskDialog from './components/TaskDialog.js'
import { isWeekend, getWeekday } from '../../utils/tools.js'
import { sendTask } from '../../api/task/index.js'
import { TaskPlanGantt } from '../task.edit.gannt/index.js'

const { ref, onMounted, nextTick, computed, onBeforeUnmount, defineAsyncComponent, reactive } = Vue

const { ElMessage, ElMessageBox, ElLoading } = ElementPlus
const { Check, Lock, Memo, User, WarningFilled } = ElementPlusIconsVue

export default {
  name: 'Calendar',
  components: {
    TaskDialog,
    Check,
    Lock,
    TaskPlanGantt,
    Memo,
    User,
    WarningFilled,
    HtDrawer: defineAsyncComponent(() => import('../../components/ht.drawer.js')),
  },
  props: {
    flow: String,
    type: String,
    enginetype: String,
  },
  setup(props) {
    // 常量定义
    const DEFAULT_VISIBLE_TASKS = 10
    const MAX_RETRY = 5

    // 状态管理
    const loading = ref(true)
    const tableData = ref([])
    const dateRange = ref([])
    const tableHeight = ref('100%')
    const cellExpandStatus = ref({})

    // 选项列表
    const taskOptionList = ref([])
    const teamOptionList = ref([])
    const subTeamOptionList = ref([])
    const shiftOptionList = ref([])
    const personOptions = ref([])
    /**人员信息 */
    // const allPersonOptionList = ref([])

    // 搜索表单
    const searchForm = ref({
      date: getCurrentMonthRange(),
      esn: '',
    })

    // 任务编辑状态
    const taskEditDialogVisible = ref(false)
    const currentEditTask = ref(null)
    const taskEditForm = ref(getEmptyTaskForm())

    // 任务查看编辑状态
    const taskEditViewDialogVisible = ref(false)
    const taskEditViewForm = ref(getEmptyTaskForm())

    // 获取空的任务表单
    function getEmptyTaskForm() {
      return {
        taskName: '',
        date: '',
        team: '',
        subTeam: [],
        shift: '',
        resourceCount: 1,
        remark: '',
        person: [],
        planId: '',
        mainId: '',
        is_by_person_or_team: '1', // 默认按人员或小组
        is_delay: '0', // 默认延时
      }
    }

    // 获取当前月份的起始日期和结束日期
    function getCurrentMonthRange() {
      const firstDay = moment().format('YYYY-MM-DD')
      const lastDay = moment().add(14, 'days').format('YYYY-MM-DD')
      return [firstDay, lastDay]
    }

    // 计算日期范围内的所有日期
    const calculateDateRange = () => {
      if (!searchForm.value.date || searchForm.value.date.length !== 2) {
        return []
      }

      const [start, end] = searchForm.value.date
      const dates = []
      let currentDate = new Date(start)
      const endDate = new Date(end)

      while (currentDate <= endDate) {
        dates.push(currentDate.toISOString().split('T')[0])
        currentDate.setDate(currentDate.getDate() + 1)
      }

      return dates
    }

    // 初始化计算日期范围
    dateRange.value = calculateDateRange()

    // 获取年月标题
    const monthGroups = computed(() => {
      const groups = {}
      dateRange.value.forEach((date) => {
        const [year, month] = date.split('-')
        const key = `${year}-${month}`
        if (!groups[key]) {
          groups[key] = []
        }
        groups[key].push(date)
      })
      return groups
    })

    // 锁定单元格
    const toggleCellLock = async (row, date) => {
      if (!isCellLocked('', date, row)) {
        let param = {
          id_wo: row.id_wo,
          dt_pt: date,
          str_flow: props.flow,
          str_type: row.str_type,
          str_engine_type: props.enginetype,
          is_lock: '1',
        }
        await lockTaskView(param)
        ElMessage.success('已锁定')
      } else {
        let param = {
          id_wo: row.id_wo,
          dt_pt: date,
          str_flow: props.flow,
          str_type: row.str_type,
          str_engine_type: props.enginetype,
          is_lock: '0',
        }
        await lockTaskView(param)
        ElMessage.success('已解锁')
      }
      await getTableData()
    }

    // 检查单元格是否被锁定
    const isCellLocked = (esn, date, row, mm) => {
      const currentDateTaskList = row.tasks?.filter((item) => moment(item.dt_pt).isSame(date)) || []
      return (currentDateTaskList.length > 0 && currentDateTaskList.every((item) => item.is_lock == '1')) || false
    }
    // 检查单元格是否已展开
    const isCellExpanded = (esn, date) => {
      const key = `${esn}-${date}`
      return cellExpandStatus.value[key] || false
    }

    // 获取某天的任务
    const getDayTasks = (row, date) => {
      if (!row?.tasks) return []

      const tasks = row.tasks.filter((task) => task.dt_pt === date)
      if (!tasks?.length) return []
      // 如果任务数量小于等于默认显示数量，显示所有任务
      if (tasks.length <= DEFAULT_VISIBLE_TASKS) {
        return tasks
      }
      // 如果单元格已展开，显示所有任务
      if (isCellExpanded(row.key, date)) {
        return tasks
      }
      // 否则只显示默认数量的任务
      return tasks.slice(0, DEFAULT_VISIBLE_TASKS)
    }

    // 检查是否需要显示"查看更多"按钮
    const shouldShowViewMore = (row, date) => {
      if (!row?.tasks) return false

      const tasks = row.tasks.filter((task) => task.dt_pt === date)
      return tasks?.length > DEFAULT_VISIBLE_TASKS
    }

    const getEmptyAddTaskForm = () => {
      return {
        id_wo: '',
        str_group: '',
        str_flow: '',
        taskId: '',
        taskName: '',
        date: '',
        team: '',
        shift: '',
        resourceCount: 1,
        remark: '',
        planId: '',
        mainId: '',
        type: '',
        sub_team: [],
        person: [],
        subTeamName: [],
      }
    }

    const addTaskDialogVisible = ref(false)
    const addTaskForm = ref(getEmptyAddTaskForm())
    const smAddOptionList = ref([])

    // 添加任务
    const handleAddTask = async (row, date) => {
      const param = {
        type: props.type,
        dt_pt: date,
        id_task: row.id_task,
      }
      const teamRes = await getTeamByType(param)
      allTeamOptionList.value = teamRes
      teamOptionList.value = teamRes.filter((item) => item.id_parent === null)
      subTeamOptionList.value = teamRes
        .filter((item) => item.id_parent !== null)
        .filter((item) => item.id_parent === row.id_team)
      addTaskForm.value.taskId = row.id_task
      addTaskForm.value.id_wo = row.id_wo
      addTaskForm.value.str_group = props.type
      addTaskForm.value.str_flow = props.flow
      addTaskForm.value.taskName = row.str_task_name
      addTaskForm.value.date = date
      addTaskForm.value.team = row.id_team
      addTaskForm.value.shift = row.id_shift
      addTaskForm.value.resourceCount = row.int_sub || 1
      addTaskForm.value.remark = row.task_remark || ''
      addTaskForm.value.planId = row.plan_id
      addTaskForm.value.mainId = row.main_id
      addTaskForm.value.type = row.str_type || ''
      addTaskForm.value.is_by_person_or_team = row.is_by_person_or_team || '1' // 默认按人员或小组
      // 获取SM下拉选项id_wo, str_group, props.flow
      const smRes = await getAddSmList(row.id_wo, row.str_type, props.flow)
      smAddOptionList.value = smRes.map((item) => ({
        value: item.id,
        label: item.str_sm,
        id_task: item.id_task,
        id_sm: item.id_sm,
      }))

      // 获取班次选项
      const shiftRes = await getShiftList()
      shiftOptionList.value = shiftRes.data.filter((item) => item.str_name.includes('早') || item.str_name.includes('晚') || item.str_name.includes('行'));
      personOptions.value = []
      const params = {
        id_team: row.id_team,
        dt_date: date,
        str_dept: props.type,
      }
      const teamstaff = await getTeamStaff(params) // 获取部门员工列表
      const teamsecstaff = await getTeamSecStaff(params) // 获取部门员工列表
      teamstaff.forEach((item) => {
        personOptions.value.push(item)
      })
      teamsecstaff.forEach((item) => {
        personOptions.value.push(item)
      })
      addTaskDialogVisible.value = true
    }

    // 查看更多
    const handleViewMore = (esn, date, row) => {
      if (isCellLocked(esn, date, row)) {
        return
      }
      const key = `${esn}-${date}`
      cellExpandStatus.value[key] = !cellExpandStatus.value[key]
      // 重新初始化拖拽
      nextTick(() => {
        initAllSortable()
      })
    }
    // 重排
    const handleSortTask = async (row, date) => {
      if (isCellLocked(row.key, date, row)) {
        return
      }
      // 获取当前日期任务的id_wo
      const id_wos = row.id_wo
      const str_group = props.type
      const str_flow = props.flow
      const str_engine_type = row.engine_type
      const dt_date = date
      const is_issued = 1
      const params = {
        id_wos,
        str_group,
        str_flow,
        str_engine_type,
        dt_date,
        is_issued,
        id_mains: row.id_main_engine,
      }
      ElMessageBox.confirm('确定重排任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const loading = ElLoading.service({
            lock: true,
            text: '重排中...',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          await sendTask(params)
          ElMessage.success('重排成功')
          await getTableData()
          loading.close()
        })
        .catch(() => {
          ElMessage.info('已取消')
        })
        .finally(() => {
          loading.close()
        })
    }

    // 获取表格数据
    const getTableData = async () => {
      loading.value = true
      try {
        const params = {
          Flow: props.flow,
          Type: props.type,
          DtStart: searchForm.value.date[0],
          DtEnd: searchForm.value.date[1],
          Esn: searchForm.value.str_esn,
          Strwo: searchForm.value.str_wo,
          StrEngineType: props.enginetype,
          StrType: searchForm.value.str_type,
          StrTeam: searchForm.value.str_team,
          str_sm: searchForm.value.str_sm,
          StrStaff: searchForm.value.str_staff,
        }
        const res = await queryTableData(params)
        tableData.value = res
      } catch (error) {
        ElMessage.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }

    // 判断任务是否可拖动
    const isTaskDraggable = (task, module, date) => {
      if (isCellLocked(module, date, task)) {
        return false
      }
      // 使用key作为唯一标识
      const currentRow = tableData.value.find((row) => row.key === module)
      return currentRow !== undefined
    }

    // 获取任务的样式类
    const getTaskClass = (task, module, date, row) => {
      const classes = ['task-item']
      const isDay = task.str_shift_name === '早班'
      classes.push(isDay ? 'status-day' : 'status-night')

      if (!isTaskDraggable(task, module, date)) {
        classes.push('non-draggable')
      }
      if (isCellLocked(module, date, row)) {
        classes.push('is-locked')
      }
      return classes.join(' ')
    }

    // 处理任务移动
    const handleTaskMove = async (taskId, fromDate, toDate, fromModule, toModule, task) => {
      try {
        // 只允许在相同key内移动
        if (fromModule !== toModule) {
          ElMessage.error('只能在相同工单内移动任务')
          return
        }

        const fromRow = tableData.value.find((row) => row.key === fromModule)
        const toRow = tableData.value.find((row) => row.key === toModule)

        if (!fromRow || !toRow) return

        const taskIndex = fromRow.tasks.findIndex((t) => t.id_task === taskId)

        if (taskIndex > -1) {
          const [movedTask] = fromRow.tasks.splice(taskIndex, 1)
          movedTask.dt_pt = toDate
          toRow.tasks.push(movedTask)

          // 检查并重置展开状态
          const fromKey = `${fromModule}-${fromDate}`
          const toKey = `${toModule}-${toDate}`

          updateExpandStatus(fromKey, getDayTasks(fromRow, fromDate))
          updateExpandStatus(toKey, getDayTasks(toRow, toDate))

          const debouncedInit = _.debounce(initAllSortable, 300)
          debouncedInit()
        }
      } catch (error) {
        ElMessage.error('任务移动失败')
      }
    }

    // 更新展开状态
    const updateExpandStatus = (key, tasks) => {
      const shouldExpand = tasks.length > DEFAULT_VISIBLE_TASKS
      if (cellExpandStatus.value[key] !== shouldExpand) {
        cellExpandStatus.value[key] = shouldExpand
      }
    }

    const allTeamOptionList = ref([])
    // 打开任务编辑对话框
    const openTaskEditDialog = async (task, date, fromDate, fromModule, toModule) => {
      // 添加任务选项
      const taskoption = { value: task.id_task, label: task.str_task_name }
      taskOptionList.value = [taskoption]

      try {
        personOptions.value = []
        const params = {
          // id_team: task.id_team,
          str_dept: props.type,
          dt_date: task.dt_pt,
        }
        const teamstaff = await getTeamStaff(params) // 获取部门员工列表
        const teamsecstaff = await getTeamSecStaff(params) // 获取部门员工列表
        teamstaff.forEach((item) => {
          personOptions.value.push(item)
        })
        teamsecstaff.forEach((item) => {
          personOptions.value.push(item)
        })

        // 获取小组选项
        const param = {
          type: props.type,
          dt_pt: date,
          id_task: task.id_task,
          type: props.type,
        }
        const teamRes = await getTeamByType(param)
        allTeamOptionList.value = teamRes
        teamOptionList.value = teamRes.filter((item) => item.id_parent === null)
        subTeamOptionList.value = teamRes
          .filter((item) => item.id_parent !== null)
          .filter((item) => item.id_parent === task.id_team)
        // 获取班次选项
        const shiftRes = await getShiftList()
        shiftOptionList.value = shiftRes.data.filter((item) => item.str_name.includes('早') || item.str_name.includes('晚') || item.str_name.includes('行'));

        // 保存任务的原始信息，用于关闭时恢复
        currentEditTask.value = {
          task: task,
          fromDate: fromDate,
          toDate: date,
          fromModule: fromModule,
          toModule: toModule,
        }

        // 设置编辑表单值
        taskEditForm.value = {
          taskName: task.str_task_name || '',
          date: date,
          team: task.id_team,
          shift: task.id_shift,
          resourceCount: task.int_sub || 1,
          remark: task.task_remark || '',
          planId: task.plan_id,
          mainId: task.main_id,
          subTeam: task.id_sub_teams?.split(',') || [],
          person: task.id_staffs?.split(',') || [],
          subTeamName: task.str_sub_teams?.split(',') || [],
          is_delay: '0', // 是否延时
          is_by_person_or_team: task.is_by_person_or_team, // 默认按人员或小组

        }

        taskEditDialogVisible.value = true
      } catch (error) {
        ElMessage.error('加载任务编辑数据失败')
      }
    }

    // 处理Team选择
    const handleTeamChange = async (value, form) => {
      // subTeamOptionList.value = allTeamOptionList.value
      //   .filter((item) => item.id_parent !== null)
      //   .filter((item) => item.id_parent === value)
      // if (!value) {
      //   personOptions.value = []
      //   return
      // }
      // personOptions.value = []
      //  const params = {
      //       id_team: value,
      //       dt_date: form.date,
      //     }
      //  const teamstaff = await getTeamStaff(params) // 获取部门员工列表
      //  const teamsecstaff= await getTeamSecStaff(params) // 获取部门员工列表
      //  teamstaff.forEach((item) => {
      //    personOptions.value.push(item)
      //  })
      //  teamsecstaff.forEach((item) => {
      //    personOptions.value.push(item)
      //  })
    }

    // 保存任务编辑
    const saveTaskEdit = async (form) => {
      if (!currentEditTask.value) return

      try {
        // 更新任务数据
        const task = currentEditTask.value.task
        const pesonnames = personOptions.value
          .filter((item) => form.person.includes(item.id))
          .map((item) => item.str_name.split('(')[0])

        const params = {
          id_main: task.id_main,
          id_plan: task.plan_id,
          id_shift: form.shift,
          id_team: form.team,
          pt_dt: form.date,
          id_sub_teams: form.subTeam.join(','),
          id_staffs: form.person.join(','),
          str_sub_teams: pesonnames.join(','),
          id_parent_task: task.id_parent_task,
          str_task_type: task.str_type,
          is_delay: form.is_delay || '0', // 是否延时
          id_wo: task.id_wo,
          id_shift_task: task.id_task,
          str_flow: props.flow,
          str_sm: task.str_model,
        }

        // 调用接口
        await addTask(params)

        // 刷新表格
        await getTableData()

        // 关闭对话框
        taskEditDialogVisible.value = false
        taskEditViewDialogVisible.value = false
        currentEditTask.value = null

        ElMessage.success('任务更新成功')
      } catch (error) {
        ElMessage.error('任务更新失败')
      }
    }

    // 保存任务 取消
    const cancelTaskEdit = async () => {
      if (!currentEditTask.value) return

      try {
        // 更新任务数据
        const task = currentEditTask.value.task
        const params = {
          id_wo: task.id_wo,
          id_task: task.id_task,
          id_parent_task: task.id_parent_task,
          int_type: -1,
          int_sort: task.int_sort,
        }

        // 调用接口
        await cancelTask(params)

        // 刷新表格
        await getTableData()

        // 关闭对话框
        taskEditDialogVisible.value = false
        taskEditViewDialogVisible.value = false
        currentEditTask.value = null

        ElMessage.success('任务更新成功')
      } catch (error) {
        ElMessage.error('任务更新失败')
      }
    }
    // 删除任务 
    const delTaskEdit = async () => {
      if (!currentEditTask.value) return

      try {
        // 更新任务数据
        const task = currentEditTask.value.task
        const params = {
          id_wo: task.id_wo,
          id_task: task.id_task,
          id_parent_task: task.id_parent_task,
          int_type: -1,
          int_sort: task.int_sort,
        }

        // // 调用接口
        // await cancelTask(params)

        // // 刷新表格
        // await getTableData()

        // // 关闭对话框
        // taskEditDialogVisible.value = false
        // taskEditViewDialogVisible.value = false
        // currentEditTask.value = null

        ElMessage.success('任务更新成功')
      } catch (error) {
        ElMessage.error('任务更新失败')
      }
    }
    // 关闭任务编辑对话框并恢复任务位置
    const closeTaskEdit = () => {
      if (!currentEditTask.value) {
        taskEditDialogVisible.value = false
        return
      }

      const { task, fromDate, fromModule, toModule } = currentEditTask.value

      // 将任务移回原位置
      const fromRow = tableData.value.find((row) => row.key === fromModule)
      const toRow = tableData.value.find((row) => row.key === toModule)

      if (fromRow && toRow) {
        // 从当前位置移除任务
        const taskIndex = toRow.tasks.findIndex((t) => t.id_task === task.id_task)
        if (taskIndex > -1) {
          const [movedTask] = toRow.tasks.splice(taskIndex, 1)
          // 恢复原始日期
          movedTask.dt_pt = fromDate
          // 添加回原始位置
          fromRow.tasks.push(movedTask)

          // 重新初始化拖拽
          const debouncedInit = _.debounce(initAllSortable, 300)
          debouncedInit()

          ElMessage.info('已取消移动，任务已恢复原位')
        }
      }

      // 关闭对话框
      taskEditDialogVisible.value = false
      currentEditTask.value = null
    }

    // 初始化拖拽
    const initSortable = (element, date, module, task) => {
      if (!element) return

      // 获取任务容器元素
      const container = element.querySelector('.tasks-container')
      if (!container) return

      // 如果已经初始化过，先销毁
      if (container._sortable) {
        container._sortable.destroy()
      }

      // 如果单元格被锁定，不初始化拖拽
      // if (isCellLocked(module, date, task)) {
      //   return
      // }

      container._sortable = new Sortable(container, {
        group: `shared-tasks-${module}`, // 使用key作为group
        animation: 200,
        ghostClass: 'sortable-ghost',
        dragClass: 'sortable-drag',
        handle: '.task-item',
        draggable: '.task-item',
        scroll: true,
        scrollSensitivity: 70,
        scrollSpeed: 15,
        bubbleScroll: true,
        fallbackOnBody: true,
        fallbackTolerance: 10,
        dragoverBubble: true,
        removeCloneOnHide: true,
        forceFallback: false,
        setData: function (dataTransfer, dragEl) {
          dataTransfer.setData('text/plain', '')
        },
        onChoose: function (evt) {
          const item = evt.item
          item.classList.add('is-dragging')
        },
        onUnchoose: function (evt) {
          const item = evt.item
          item.classList.remove('is-dragging')
        },
        onEnd: (evt) => {
          const item = evt.item
          const fromTaskList = evt.from.closest('.task-list')
          const toTaskList = evt.to.closest('.task-list')

          const taskId = item.getAttribute('data-task-id')
          const fromDate = fromTaskList.getAttribute('data-date')
          const toDate = toTaskList.getAttribute('data-date')
          const fromModule = fromTaskList.getAttribute('data-module')
          const toModule = toTaskList.getAttribute('data-module')
          if (fromDate !== toDate) {
            const findRow = tableData.value.find((row) => row.key === fromModule)
            const task = findRow?.tasks.find((t) => t.id_task === taskId)
            if (task && isTaskDraggable(task, fromModule, fromDate)) {
              handleTaskMove(taskId, fromDate, toDate, fromModule, toModule, task)
              // 拖动结束后打开任务编辑对话框，并传递原始位置信息
              openTaskEditDialog(task, toDate, fromDate, fromModule, toModule)
            }
          }
        },
      })
    }

    // 初始化所有拖拽区域
    const initAllSortable = (() => {
      let isInitializing = false
      let retryCount = 0

      return function _init() {
        if (isInitializing) return
        isInitializing = true
        retryCount = 0

        const tryInit = () => {
          const taskLists = document.querySelectorAll('.task-list')
          if (taskLists.length > 0 || retryCount >= MAX_RETRY) {
            taskLists.forEach((element) => {
              const task = element.getAttribute('data-data')
              const taskData = JSON.parse(task)
              const date = element.getAttribute('data-date')
              const module = element.getAttribute('data-module')
              initSortable(element, date, module, taskData)
            })
            isInitializing = false
            return
          }

          retryCount++
          // 重试间隔时间
          setTimeout(tryInit, 100 * Math.min(retryCount, 5))
        }

        nextTick(tryInit)
      }
    })()

    // 计算表格高度
    const calculateTableHeight = _.debounce(() => {
      nextTick(() => {
        const container = document.querySelector('.schedule-container')
        if (!container) return

        // 获取视窗高度（考虑iframe情况）
        const viewportHeight =
          window.self === window.top ? window.innerHeight : window.frameElement?.clientHeight || window.innerHeight

        // 获取搜索表单高度
        const searchForm = container.querySelector('.search-form')
        const searchFormHeight = searchForm?.getBoundingClientRect().height || 0

        // 计算表格可用高度（减去padding和表单高度）
        const formMargin = 50 // 表单的下边距
        const availableHeight = viewportHeight - searchFormHeight - formMargin

        // 更新表格高度
        tableHeight.value = `${Math.floor(availableHeight)}px`
      })
    }, 300)

    // 监听窗口大小变化
    const resizeHandler = () => {
      calculateTableHeight()
    }

    // 点击任务查看和编辑
    const openTaskEditViewDialog = async (task, date) => {
      try {
        personOptions.value = []
        const params = {
          // id_team: task.id_team,
          str_dept: props.type,
          dt_date: task.dt_pt,
        }
        const teamstaff = await getTeamStaff(params) // 获取部门员工列表
        const teamsecstaff = await getTeamSecStaff(params) // 获取部门员工列表
        teamstaff.forEach((item) => {
          personOptions.value.push(item)
        })
        teamsecstaff.forEach((item) => {
          personOptions.value.push(item)
        })
        // 获取小组选项 不选择team
        const teamRes = await getTeamByType({
          type: props.type,
          dt_pt: date,
          type: props.type,
        })
        allTeamOptionList.value = teamRes
        teamOptionList.value = teamRes.filter((item) => item.id_parent === null)

        // subTeamOptionList.value = teamRes
        //   .filter((item) => item.id_parent !== null)
        //   .filter((item) => item.id_parent === task.id_team)
        // // 获取班次选项
        const shiftRes = await getShiftList()
        shiftOptionList.value = shiftRes.data.filter((item) => item.str_name.includes('早') || item.str_name.includes('晚') || item.str_name.includes('行'));

        // 添加任务选项
        const taskoption = { value: task.id_task, label: task.str_task_name }
        taskOptionList.value = [taskoption]
        // 设置查看表单值
        taskEditViewForm.value = {
          taskName: task.str_task_name || '',
          date: date,
          team: task.id_team,
          shift: task.id_shift,
          resourceCount: task.int_sub || 1,
          remark: task.task_remark || '',
          planId: task.plan_id,
          mainId: task.main_id,
          subTeam: task.id_sub_teams?.split(',') || [],
          person: task.id_staffs?.split(',') || [],
          subTeamName: task.str_sub_teams?.split(',') || [],
          is_by_person_or_team: task.is_by_person_or_team,
          is_delay: '1', // 是否延时
        }
        // 保存当前编辑任务
        currentEditTask.value = {
          task: task,
          fromDate: date,
          toDate: date,
          fromModule: task.key,
          toModule: task.key,
        }

        taskEditViewDialogVisible.value = true
      } catch (error) {
        ElMessage.error('加载任务详情失败')
      }
    }

    // 关闭任务查看对话框
    const closeTaskEditView = () => {
      taskEditViewDialogVisible.value = false
      currentEditTask.value = null
    }

    // 处理查询按钮点击
    const handleSearch = async () => {
      await getTableData()
      initAllSortable()
      dateRange.value = calculateDateRange()
    }

    // 删除
    const handleDelete = (row) => {
      ElMessageBox.confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          getTableData()
        })
        .catch(() => {
          ElMessage.info('已取消删除')
        })
    }

    // 设置
    const handleSetting = (row) => {
      // console.log(row)
    }

    // 生命周期钩子 - 组件挂载
    onMounted(async () => {
      await getTableData()
      initAllSortable()
      calculateTableHeight()

      // 添加窗口大小变化监听
      window.addEventListener('resize', resizeHandler)

      // 如果在iframe中，尝试监听父窗口的resize
      if (window.self !== window.top) {
        try {
          window.top.addEventListener('resize', resizeHandler)
        } catch (e) {
          console.warn('Unable to add resize listener to parent window')
        }
      }
    })

    // 生命周期钩子 - 组件销毁前
    onBeforeUnmount(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', resizeHandler)

      if (window.self !== window.top) {
        try {
          window.top.removeEventListener('resize', resizeHandler)
        } catch (e) {
          console.warn('Unable to remove resize listener from parent window')
        }
      }
    })

    // 获取表头单元格类名
    const getHeaderCellClassName = ({ column }) => {
      if (column.label === '星期六' || column.label === '星期日') {
        return '!bg-red-500 !text-white'
      }
      return '!h-10'
    }

    // 完工
    const handleFinish = (row) => {
      finishDialogVisible.value = true

      finishForm.value.str_code = row.str_code
      finishForm.value.str_esn = row.str_esn
      finishForm.value.id_wo = row.id_wo
      getSmOptionList(row.id_wo, row.str_type)
    }

    const smOptionList = ref([])

    const getSmOptionList = async (id_wo, str_group) => {
      const res = await getSmList(id_wo, str_group, props.flow)
      smOptionList.value = res.map((item) => ({
        value: item.str_sm,
        label: item.str_sm,
      }))
    }

    // 完工弹窗
    const finishDialogVisible = ref(false)
    const finishForm = ref({
      str_code: '',
      str_esn: '',
      str_sm: '',
    })

    // 提交
    const handleFinishSubmit = () => {
      ElMessageBox.confirm('确定提交吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const res = await updateEngineTaskStatus(finishForm.value.id_wo, props.flow, finishForm.value.str_sm,1)
          finishDialogVisible.value = false
          finishForm.value.str_sm = null
          await getTableData()
        })
        .catch(() => {
          finishForm.value.str_sm = null
        })
    }

    const handleAddTaskReset = () => {
      addTaskForm.value = getEmptyAddTaskForm()
      addTaskDialogVisible.value = false
    }

    const handleRefresh = () => {
      handleAddTaskReset()
      getTableData()
    }

    // 处理子团队变更
    const handleSubTeamChange = (() => {
      const pendingWarnings = new Set()
      const pendingErrors = new Set()
      let currentForm = null
      let debounceTimer = null

      // 显示收集的消息
      const showCollectedMessages = (form) => {
        if (pendingWarnings.size > 0) {
          const warningTeams = Array.from(pendingWarnings).join(', ')
          ElMessage.warning(`选择 SubTeam ${warningTeams} 将超出资源数量 (${form.resourceCount}) 限制，无法添加。`)
          pendingWarnings.clear()
        }

        if (pendingErrors.size > 0) {
          const errorTeams = Array.from(pendingErrors).join(', ')
          ElMessage.error(`获取 SubTeam ${errorTeams} 信息失败。`)
          pendingErrors.clear()
        }
      }

      // 返回实际的处理函数
      return async function (value, form) {
        // 清除之前的定时器
        if (debounceTimer) {
          clearTimeout(debounceTimer)
        }

        // 如果表单对象发生变化，清除之前收集的信息
        if (currentForm !== form) {
          pendingWarnings.clear()
          pendingErrors.clear()
          currentForm = form
        }

        if (!value || value.length === 0) {
          form.person = []
          personOptions.value = []

          if (form.hasOwnProperty('subTeam')) {
            form.subTeam = []
          } else if (form.hasOwnProperty('sub_team')) {
            form.sub_team = []
          }

          return
        }

        // 存储成功添加的子团队ID和人员
        const successfullyAddedSubTeamIds = []
        const collectedPersonIds = new Set()
        const personOptionsList = []

        for (const subTeamId of value) {
          const params = {
            id_sub_team: subTeamId,
            dt_pt: form.date,
            id_team: form.team,
          }

          try {
            const res = await getWarning(params)
            const personsFromThisSubTeam = res.data || []

            // 检查是否超出资源数量限制
            const uniquePersons = personsFromThisSubTeam.filter((p) => !collectedPersonIds.has(p.id))

            if (collectedPersonIds.size + uniquePersons.length <= form.resourceCount) {
              successfullyAddedSubTeamIds.push(subTeamId)

              uniquePersons.forEach((p) => {
                if (!collectedPersonIds.has(p.id)) {
                  collectedPersonIds.add(p.id)
                  personOptionsList.push(p)
                }
              })
            } else {
              const subTeamInfo = subTeamOptionList.value.find((opt) => opt.id === subTeamId)
              const subTeamName = subTeamInfo ? subTeamInfo.str_code : `ID ${subTeamId}`
              pendingWarnings.add(`"${subTeamName}"`)
            }
          } catch (error) {
            const subTeamInfo = subTeamOptionList.value.find((opt) => opt.id === subTeamId)
            const subTeamName = subTeamInfo ? subTeamInfo.str_code : `ID ${subTeamId}`
            pendingErrors.add(`"${subTeamName}"`)
          }
        }

        // 更新表单数据
        form.person = Array.from(collectedPersonIds)
        personOptions.value = personOptionsList

        // 获取子团队名称
        const subTeamNames = successfullyAddedSubTeamIds.map((id) => {
          const subTeamInfo = subTeamOptionList.value.find((opt) => opt.id === id)
          return subTeamInfo?.str_code || ''
        })

        if (form.hasOwnProperty('subTeamName')) {
          form.subTeamName = subTeamNames
        }

        // 更新表单字段
        if (form.hasOwnProperty('subTeam')) {
          form.subTeam = successfullyAddedSubTeamIds
        } else if (form.hasOwnProperty('sub_team')) {
          form.sub_team = successfullyAddedSubTeamIds
        }

        // 使用延迟函数确保消息只显示一次
        debounceTimer = setTimeout(() => {
          showCollectedMessages(form)
        }, 100)
      }
    })()

    // 处理SM变更
    const handleSmChange = async (value) => {
      if (!value) return

      try {
        const taskId = smAddOptionList.value.find((item) => item.value === value)?.id_task
        if (taskId) {
          const res = await getTaskListByTaskId(taskId)
          taskOptionList.value = res.map((item) => ({
            value: item.id,
            label: item.str_task,
            is_by_person_or_team: item.is_by_person_or_team, // 默认按人员或小组
          }))
        }
      } catch (error) {
        ElMessage.error('加载任务选项失败')
      }
    }

    // 获取stafff显示
    const showStaffTitle = (task, date) => {
      if (task.is_by_person_or_team == '0') {
        const teams = task.str_team?.split(',') || []
        return teams.map((item) => {
          return {
            team: item,
          }
        })
      } else {
        const teams = task.str_sub_teams?.split(',') || []
        return teams.map((item) => {
          return {
            team: item,
          }
        })
      }
    }

    // 获取人员显示文本
    const getStaffDisplayText = (task, date) => {
      const staffList = showStaffTitle(task, date)
      if (staffList.length === 0) return ''

      return staffList.map((item) => item.team).join(', ')
    }

    // 获取人员完整文本（用于tooltip）
    const getStaffFullText = (task, date) => {
      const staffList = showStaffTitle(task, date)
      if (staffList.length === 0) return ''

      return staffList.map((item) => `(${item.team})`).join('\n')
    }
    const lockDialogVisible = ref(false)
    const lockAllForm = ref({
      str_code: '',
      str_esn: '',
      str_sm: '',
      dt_lock: '',
      dt_start: '',
      dt_end: '',
    })
    //  批量锁定
    const handleLockAll = (row) => {
      lockDialogVisible.value = true
      lockAllForm.value.str_code = row.str_code
      lockAllForm.value.str_esn = row.str_esn
      lockAllForm.value.id_wo = row.id_wo
      lockAllForm.value.is_all = 1
      lockAllForm.value.str_type = row.str_type
      lockAllForm.value.id_main_engine = row.id_main_engine
      lockAllForm.value.str_flow = props.flow
    }
    // 锁定全部
    const handleLockAllSubmit = (is_lock) => {
      if (!lockAllForm.value.dt_lock) ElMessage.error('请选择锁定时间')
      else {
        let msg = is_lock ? `确定[锁定]吗？` : '确定[解锁]吗？'
        ElMessageBox.confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            let param = Object.assign(lockAllForm.value, {
              dt_start: lockAllForm.value.dt_lock[0],
              dt_end: lockAllForm.value.dt_lock[1],
              is_lock: is_lock,
            })
            await lockTaskView(param)
            ElMessage.success('已保存')
            lockDialogVisible.value = false
            lockAllForm.value.dt_lock = null
            await getTableData()
          })
          .catch(() => { })
      }
    }
    // 打开甘特图
    const ganttVisible = ref(false)
    const ganttForm = ref({
      str_flow_input: props.flow,
      str_engine_type: props.enginetype,
      str_group_input: props.type,
      id_wo_input: '',

    })
    const openTaskGantt = ({ id_wo, str_type }) => {
      if (id_wo) {
        ganttForm.value.id_wo_input = id_wo;
        ganttForm.value.str_group_input = str_type;

      }

      ganttVisible.value = true
    }
    const closeTaskGantt = () => {
      ganttForm.value.id_wo_input = '';
      ganttForm.value.str_group_input = props.type;
      ganttForm.value.str_flow_input = props.flow;
      ganttVisible.value = false
    }
    // 打开人员重排弹框
    const sourseVisible = ref(false)
    const sourseForm = ref({
      id_wos: [],
      str_group: [],
      str_flow: props.flow,
      str_engine_type: props.enginetype,
      dt_start: moment().format('YYYY-MM-DD'),
      dt_end: '',
    });
    const woList = ref([])
    const rules = reactive({
      id_wos: [
        { required: true, message: 'Please ESN', trigger: 'blur' },
      ],
      dt_start: [
        { required: true, message: 'Please Start', trigger: 'blur' },
      ],
      str_group: [
        { required: true, message: 'Please Group', trigger: 'blur' },
      ],
    })
    const openSourseDialog = ({ str_type, id_wo }) => {
      // ElMessage.success('开发中')
      tableData.value.forEach((item) => {
        if (!woList.value.some(m => m.id_wo === item.id_wo)) {
          woList.value.push({ id_wo: item.id_wo, str_esn: item.str_esn });
        }
      });
      if (id_wo)
        sourseForm.value.id_wos = [id_wo]
      sourseForm.value.str_group = props.type == "B1" ? ["B1"] : ["Core", "FAN", "LPT"]
      if (str_type) {
        sourseForm.value.str_group = [str_type];
      }

      sourseForm.value.dt_start = moment().format('YYYY-MM-DD')
      sourseVisible.value = true
    }
    const handSaveStaff = async () => {
      // 创建全局loading遮罩
      let loading = ElementPlus.ElLoading.service({
        fullscreen: true,
        lock: true,
        text: '重排中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const params = {
        id_wos: sourseForm.value.id_wos.join(','),
        str_group: sourseForm.value.str_group.join(','),
        str_flow: sourseForm.value.str_flow,
        str_engine_type: sourseForm.value.str_engine_type,
        dt_start: sourseForm.value.dt_start,
        dt_end: sourseForm.value.dt_end,
      }
      await saveStaff(params)
      loading.close();
      ElMessage.success('保存成功')
      sourseVisible.value = false
      await getTableData()
    }
    const closeSourseDialog = () => {
      sourseVisible.value = false
    }
    return {
      searchForm,
      tableData,
      taskOptionList,
      teamOptionList,
      subTeamOptionList,
      shiftOptionList,
      personOptions,
      tableHeight,
      dateRange,
      monthGroups,
      loading,
      taskEditDialogVisible,
      taskEditForm,
      taskEditViewDialogVisible,
      taskEditViewForm,
      finishDialogVisible,
      finishForm,
      smOptionList,
      Check,
      Lock,
      Memo,
      User,
      WarningFilled,
      addTaskDialogVisible,
      addTaskForm,
      smAddOptionList,
      getDayTasks,
      getTaskClass,
      isTaskDraggable,
      toggleCellLock,
      isCellLocked,
      handleViewMore,
      shouldShowViewMore,
      isCellExpanded,
      handleSearch,
      handleDelete,
      handleSetting,
      saveTaskEdit,
      closeTaskEdit,
      openTaskEditViewDialog,
      closeTaskEditView,
      isWeekend,
      getWeekday,
      getHeaderCellClassName,
      handleFinish,
      handleFinishSubmit,
      cancelTaskEdit,
      handleAddTask,
      handleAddTaskReset,
      handleRefresh,
      handleSortTask,
      handleTeamChange,
      //  handleSubTeamChange,
      handleSmChange,
      showStaffTitle,
      getStaffDisplayText,
      getStaffFullText,
      // allPersonOptionList,
      handleLockAll,
      lockDialogVisible,
      lockAllForm,
      handleLockAllSubmit,
      delTaskEdit,
      ganttVisible,
      openTaskGantt,
      closeTaskGantt,
      sourseVisible,
      sourseForm,
      openSourseDialog,
      closeSourseDialog,
      ganttForm,
      rules,
      woList,
      handSaveStaff,
    }
  },
  template: /*html */ `
    <div class="schedule-container">
    <!-- 加载遮罩层 -->
    <div v-if="loading" class="loading-mask">
      <div class="loading-spinner">
        <el-icon class="is-loading" color="#409EFF" :size="30">
          <Loading />
        </el-icon>
        <div class="loading-text">数据加载中...</div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="日期">
        <el-date-picker v-model="searchForm.date" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
      </el-form-item>
      <el-form-item label="ESN">
        <el-input v-model="searchForm.str_esn" placeholder="请输入ESN" clearable class="!w-40" />
      </el-form-item>
      <el-form-item label="WO">
        <el-input v-model="searchForm.str_wo" placeholder="请输入WO" clearable class="!w-40" />
      </el-form-item>

      <el-form-item label="Type">
        <el-input v-model="searchForm.str_type" placeholder="请输入Type" clearable class="!w-40" />
      </el-form-item>
      <el-form-item label="Team">
        <el-input v-model="searchForm.str_team" placeholder="请输入Team" clearable class="!w-40" />
      </el-form-item>
      <el-form-item label="SM">
        <el-input v-model="searchForm.str_sm" placeholder="请输入SM" clearable class="!w-40" />
      </el-form-item>
      <el-form-item label="Staff Name">
        <el-input v-model="searchForm.str_staff" placeholder="请输入员工姓名" clearable class="!w-40" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="openTaskGantt">任务甘特图</el-button>
      </el-form-item>
     
      <el-form-item>
        <el-button type="primary" @click="openSourseDialog">人员分配</el-button>
      </el-form-item>
    </el-form>

    <!-- 主表格 -->
    <el-table :data="tableData" border class="schedule-table" :height="tableHeight"
      :header-cell-class-name="getHeaderCellClassName">
      <el-table-column type="index" width="40" label="#" fixed="left" />
      <el-table-column label="operate" width="100" fixed="left">
        <template #default="scope">
          <el-button title="完工" type="success" :icon="Check" circle @click="handleFinish(scope.row)" />
          <el-button title="锁定" type="success" :icon="Lock" circle @click="handleLockAll(scope.row)" />
          <el-row style="margin-top: 5px;">
            <el-button title="任务调整" type="success" :icon="Memo" circle @click="openTaskGantt(scope.row)" />
            <el-button title="人员重排" type="success" :icon="User" circle @click="openSourseDialog(scope.row)" />
          </el-row>
        </template>
      </el-table-column>
      <!--<el-table-column prop="str_code" label="WO" width="100" />-->
      <el-table-column prop="str_esn" label="ESN" width="80" fixed="left">
        <template
          #default="scope">{{scope.row.str_esn}}
          <el-row>{{scope.row.str_code}}</el-row>
          <el-row>{{scope.row.engine_type}}</el-row>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="engine_type" label="Engine type" width="100" />-->
      <el-table-column prop="str_type" label="Type" width="50" fixed="left">
        <template #default="scope">{{scope.row.str_type}}
        <el-row><el-icon :title="scope.row.dt_pt_max"v-if="scope.row.dt_pt_max>scope.row.dt_gp_end" style="font-size: 18px; color: red"><WarningFilled />
            </el-icon></el-row>
            </template>
      </el-table-column>
      <el-table-column prop="dt_gp_start" label="GP start" width="100">
        <template #default="scope">{{scope.row.dt_gp_start}}<el-row>{{scope.row.dt_gp_end}} </el-row></template>
      </el-table-column>
      <!--  <el-table-column prop="dt_gp_end" label="GP end" width="100" />-->

      <!-- 动态生成日期列分组 -->
      <template v-for="date in dateRange" :key="date">
        <el-table-column :label="getWeekday(date)" min-width="280">
          <el-table-column :label="date" min-width="280">
            <template #default="scope">
              <div class="task-list" :data-data="JSON.stringify(scope.row)" :data-date="date"
                :data-module="scope.row.key" :data-locked="isCellLocked(scope.row.key, date,scope.row)">
                <div class="tasks-container">
                  <template v-for="task in getDayTasks(scope.row, date)" :key="task.id_task">
                    <div :data-task-id="task.id_task" :class="getTaskClass(task, scope.row.key, date, scope.row)"
                      @click="openTaskEditViewDialog(task,date)">
                      <div class="flex items-center gap-2 min-w-0">
                        <el-tag :color="task.str_shift_name === '早班' ? '#409EFF' : '#E6A23C'" size="small"
                          class="flex-shrink-0">
                          <div class="text-white">{{ task.str_shift_name }}</div>
                        </el-tag>
                        <span :title="task.str_esn" class="flex-shrink-0">{{ task.str_esn }}</span>
                        <span :title="task.str_task_name" class="flex-shrink-0">|{{ task.int_sort }}#</span>
                        <span :title="task.str_model" class="flex-shrink-0">{{ task.str_model }}</span>
                        <!-- <div class="truncate" :title="task.str_team">{{ task.str_team }}</div> -->
                        <template v-if="getStaffDisplayText(task, date)">
                          <el-tooltip effect="dark" placement="top-start" :content="getStaffFullText(task, date)"
                            :disabled="!getStaffDisplayText(task, date)">
                            <div class="min-w-0 flex-1 overflow-hidden">
                              <el-tag type="info" size="small" class="truncate max-w-full">
                                {{ getStaffDisplayText(task, date) }}
                              </el-tag>
                            </div>
                          </el-tooltip>
                        </template>
                        <template v-else>
                          <div class="truncate italic text-gray-400 min-w-0">无人员</div>
                        </template>
                      </div>
                    </div>
                  </template>
                </div>
                <!-- 底部按钮区域 -->
                <div class="cell-actions">
                  <el-button :class="{'is-locked':isCellLocked(scope.row.key,date,scope.row)}" type="text" size="small"
                    :disabled="isCellLocked(scope.row.key, date, scope.row)" @click="handleAddTask(scope.row, date)">
                    添加任务
                  </el-button>
                  <el-button v-if="shouldShowViewMore(scope.row, date)" type="text" size="small"
                    :disabled="isCellLocked(scope.row.key, date, scope.row)"
                    @click="handleViewMore(scope.row.key, date, scope.row)">
                    {{ isCellExpanded(scope.row.key, date) ? '收起' : '查看更多' }}
                  </el-button>
                  <el-button v-if="getDayTasks(scope.row, date).length > 0"
                    :class="{'is-locked':isCellLocked(scope.row.key,date,scope.row)}" type="text" size="small"
                    :disabled="isCellLocked(scope.row.key, date, scope.row)" @click="handleSortTask(scope.row, date)">
                    重排
                  </el-button>
                  <el-button v-if="getDayTasks(scope.row, date).length > 0" type="text" size="small"
                    :class="{ 'is-locked': isCellLocked(scope.row.key, date, scope.row) }"
                    @click="toggleCellLock(scope.row, date)">
                    {{ isCellLocked(scope.row.key, date, scope.row,1) ? '取消锁定' : '锁定' }}
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
    </el-table>

    <!-- 任务移动编辑对话框 -->
    <task-dialog :visible="taskEditDialogVisible" title="任务移动" :form="taskEditForm" mode="edit"
      :task-options="taskOptionList" :team-options="teamOptionList" :sub-team-options="subTeamOptionList"
      :shift-options="shiftOptionList" :person-options="personOptions" team-placeholder="select Team" :type="type"
      @update:visible="(val) => taskEditDialogVisible = val" @cancel="closeTaskEdit" @save="saveTaskEdit"
      @cancel-task="cancelTaskEdit" @team-change="handleTeamChange" @del-task="delTaskEdit" />

    <!-- 任务查看编辑对话框 -->
    <task-dialog :visible="taskEditViewDialogVisible" title="任务编辑" :form="taskEditViewForm" mode="edit"
      :task-options="taskOptionList" :team-options="teamOptionList" :sub-team-options="subTeamOptionList"
      :shift-options="shiftOptionList" :person-options="personOptions" :type="type" team-placeholder="select Team"
      @update:visible="(val) => taskEditViewDialogVisible = val" @cancel="closeTaskEditView" @save="saveTaskEdit"
      @cancel-task="cancelTaskEdit" @team-change="handleTeamChange" @del-task="delTaskEdit" />

    <!-- 添加任务对话框 -->
    <task-dialog v-if="addTaskDialogVisible" :visible="addTaskDialogVisible" title="添加任务" :form="addTaskForm" mode="add"
      :task-options="taskOptionList" :team-options="teamOptionList" :sub-team-options="subTeamOptionList"
      :shift-options="shiftOptionList" :sm-options="smAddOptionList" :person-options="personOptions"
      team-placeholder="select Team" :type="type" @update:visible="(val) => addTaskDialogVisible = val"
      @reset="handleAddTaskReset" @refresh="handleRefresh" @team-change="handleTeamChange"
      @sm-change="handleSmChange" />

    <!-- 完工弹窗 -->
    <el-dialog class="common-dialog" v-model="finishDialogVisible" title="完工单元体" width="30%">
      <!-- 基本信息 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="WO">{{ finishForm.str_code }}</el-descriptions-item>
        <el-descriptions-item label="ESN">{{ finishForm.str_esn }}</el-descriptions-item>
      </el-descriptions>
      <el-form :model="finishForm" class="mt-4">
        <el-form-item label="SM">
          <el-select v-model="finishForm.str_sm" placeholder="请选择SM" filterable clearable multiple>
            <el-option v-for="item in smOptionList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="handleFinishSubmit">保存</el-button>
        <el-button @click="finishDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 锁定弹窗 -->
    <el-dialog class="common-dialog" v-model="lockDialogVisible" title="锁定单元体任务" width="30%">
      <!-- 基本信息 -->
      <el-descriptions :column="3" border>
        <el-descriptions-item label="WO">{{ lockAllForm.str_code }}</el-descriptions-item>
        <el-descriptions-item label="ESN">{{ lockAllForm.str_esn }}</el-descriptions-item>
        <el-descriptions-item label="ESN">{{ lockAllForm.str_type }}</el-descriptions-item>
      </el-descriptions>
      <el-form :model="lockAllForm" class="mt-4">
        <el-form-item label="Date Range">
          <el-date-picker v-model="lockAllForm.dt_lock" type="daterange" value-format="YYYY-MM-DD" range-separator="To"
            start-placeholder="Start date" end-placeholder="End date" :size="size" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="handleLockAllSubmit(1)">锁定</el-button>
        <el-button type="" @click="handleLockAllSubmit(0)">取消锁定</el-button>
        <el-button @click="lockDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 甘特图 -->
    <HtDrawer title="任务甘特图" width="85%" :is-show-save="false" v-model="ganttVisible" @clear="closeTaskGantt">
      <TaskPlanGantt :str_flow_input="ganttForm.str_flow_input" :str_engine_type_input="ganttForm.str_engine_type"
        :str_group_input="ganttForm.str_group_input" :id_wo_input="ganttForm.id_wo_input" @save_task="handleRefresh">
        </TaskPlanGantt>

    </HtDrawer>


    <!-- 人员重排 -->
    <el-dialog class="common-dialog" v-model="sourseVisible" title="人员重排" width="30%">
      <!-- 基本信息 -->

      <el-form label-width="120" :model="sourseForm" class="mt-4" :rules="rules">
        <el-form-item label="ESN" prop="id_wos">
          <el-select clearable multiple filterable v-model="sourseForm.id_wos" placeholder="please esn">
            <el-option v-for="item in woList" :key="item.id_wo" :label="item.str_esn" :value="item.id_wo" />

          </el-select>
        </el-form-item>
        <el-form-item label="Group" prop="str_group">
          <el-select clearable multiple v-model="sourseForm.str_group" placeholder="Group">
            <el-option key="Core" label="Core" value="Core" />
            <el-option key="FAN" label="FAN" value="FAN" />
            <el-option key="LPT" label="LPT" value="LPT" />
            <el-option key="B1" label="B1" value="B1" />
          </el-select>
        </el-form-item>
        <el-form-item label="Start" prop="dt_start">
          <el-date-picker format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="sourseForm.dt_start" type="date"
            placeholder="Pick a day" />
        </el-form-item>
        <el-form-item label="End" prop="dt_end">
          <el-date-picker format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="sourseForm.dt_end" type="date"
            placeholder="Pick a day" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="handSaveStaff">重排</el-button>
        <el-button @click="sourseVisible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
  `,
}
