export function useHeaderButton() {

  // 导入
  const handleImport = () => {
    // 创建一个input标签
    const input = document.createElement('input');
    // 设置input的type为file
    input.type = 'file';
    // 设置input的accept为csv文件
    input.accept = '.csv';
    // 设置input的multiple为false
    input.multiple = false;
    // 设置input的onchange事件
    input.onchange = (e) => {
      // 获取文件
      const file = e.target.files[0];
      // 创建一个FormData对象
      const formData = new FormData();
      // 将文件添加到formData对象中
      formData.append('file', file);
      // 发送请求
      axios.post('/api/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }).then((res) => {
        console.log(res);
      });
    };
    // 触发input的点击事件
    input.click();
    // 移除input
    input.remove();
  };

  // 导出
  const handleExport = (data) => {
    // 将数据转换为sheet
    const ws = XLSX.utils.json_to_sheet(data);
    // 创建一个workbook
    const wb = XLSX.utils.book_new();
    // 将sheet添加到workbook中
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    // 将workbook转换为二进制字符串
    const wbout = XLSX.write(wb, {
      bookType: 'xlsx',
      bookSST: false,
      type: 'binary',
    });
    // 创建一个Blob对象
    const blob = new Blob([s2ab(wbout)], {
      type: 'application/octet-stream',
    });

    function s2ab(s) {
      const buf = new ArrayBuffer(s.length);
      const view = new Uint8Array(buf);
      for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
      return buf;
    }

    // 创建一个a标签
    const a = document.createElement('a');
    // 设置a标签的href为Blob对象的URL
    a.href = URL.createObjectURL(blob);
    // 设置a标签的download属性为文件名
    a.download = 'data.xlsx';
    // 触发a标签的点击事件
    a.click();
    // 移除a标签
    a.remove();
  };


  return {
    handleImport,
    handleExport,
  };
}
