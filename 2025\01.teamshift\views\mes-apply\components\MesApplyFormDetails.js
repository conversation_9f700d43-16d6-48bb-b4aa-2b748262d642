const { ref, reactive, computed, watch, onMounted } = Vue
const { ElMessage, ElMessageBox } = ElementPlus

// 引入moment.js进行时间处理
// 注意：需要在页面中引入moment.js库
// <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>

// 统一的工时详情组件 - 支持申请和确认两种模式
export const MesApplyFormDetails = {
  name: 'MesApplyFormDetails',
  props: {
    details: { type: Array, required: true },
    readonly: { type: Boolean, default: false },
    mode: { type: String, default: 'apply', validator: (value) => ['apply', 'confirm'].includes(value) }, // 'apply' 或 'confirm'
  },
  emits: ['update:details'],
  setup(props, { emit }) {
    const batchMode = ref(false)
    const batchSettings = reactive({
      startTime: '09:00',
      endTime: '18:00',
      appliedHours: 8,
    })

    // 计算工时 - 使用moment.js
    const calculateHours = (startTime, endTime) => {
      if (!startTime || !endTime) return 0

      // 使用moment.js解析时间
      const start = moment(startTime, 'HH:mm')
      const end = moment(endTime, 'HH:mm')

      // 计算时间差（小时）
      const diff = end.diff(start, 'hours', true)
      return diff > 0 ? Math.round(diff * 100) / 100 : 0 // 保留两位小数
    }

    // 格式化日期显示 - 使用moment.js
    const formatDate = (dateString) => {
      if (!dateString) return ''

      // 使用moment.js格式化日期
      const date = moment(dateString, 'YYYY-MM-DD')
      if (!date.isValid()) return dateString

      // 中文星期映射
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekday = weekdays[date.day()]

      return `${date.format('YYYY-MM-DD')} (${weekday})`
    }

    // 计算总申请工时
    const totalHours = computed(() => {
      return props.details.reduce((total, detail) => total + (detail.appliedHours || 0), 0).toFixed(2)
    })

    // 计算总确认工时
    const totalConfirmHours = computed(() => {
      return props.details.reduce((total, detail) => total + (detail.confirmHours || 0), 0).toFixed(2)
    })

    // 验证时间范围 - 使用moment.js
    const validateTimeRange = (startTime, endTime) => {
      if (!startTime || !endTime) return true

      // 使用moment.js验证时间范围
      const start = moment(startTime, 'HH:mm')
      const end = moment(endTime, 'HH:mm')

      return end.isAfter(start)
    }

    // 处理申请时间变化
    const handleTimeChange = (detail, field) => {
      if (field === 'startTime' || field === 'endTime') {
        // 验证时间范围
        if (!validateTimeRange(detail.startTime, detail.endTime)) {
          ElMessage.warning('结束时间必须晚于开始时间')
          return
        }

        // 自动计算工时
        const calculatedHours = calculateHours(detail.startTime, detail.endTime)

        // 如果当前申请工时超过计算的工时，则调整为计算的工时
        if (detail.appliedHours > calculatedHours) {
          detail.appliedHours = calculatedHours
        } else if (!detail.appliedHours || detail.appliedHours === 0) {
          // 如果没有设置申请工时，则使用计算的工时
          detail.appliedHours = calculatedHours
        }
      }

      emit('update:details', [...props.details])
    }

    // 处理确认时间变化
    const handleConfirmTimeChange = (detail, field) => {
      if (field === 'confirmStartTime' || field === 'confirmEndTime') {
        // 验证时间范围
        if (!validateTimeRange(detail.confirmStartTime, detail.confirmEndTime)) {
          ElMessage.warning('确认结束时间必须晚于确认开始时间')
          return
        }

        // 自动计算确认工时
        const calculatedHours = calculateHours(detail.confirmStartTime, detail.confirmEndTime)

        // 如果当前确认工时超过计算的工时，则调整为计算的工时
        if (detail.confirmHours > calculatedHours) {
          detail.confirmHours = calculatedHours
        } else if (!detail.confirmHours || detail.confirmHours === 0) {
          // 如果没有设置确认工时，则使用计算的工时
          detail.confirmHours = calculatedHours
        }
      }

      emit('update:details', [...props.details])
    }

    // 处理申请工时直接修改
    const handleHoursChange = (detail) => {
      // 计算时间范围对应的最大工时
      const maxHours = calculateHours(detail.startTime, detail.endTime)

      // 验证工时范围
      if (detail.appliedHours < 0) {
        detail.appliedHours = 0
        ElMessage.warning('工时不能为负数')
      } else if (detail.appliedHours > 24) {
        detail.appliedHours = 24
        ElMessage.warning('单日工时不能超过24小时')
      } else if (detail.appliedHours > maxHours && maxHours > 0) {
        detail.appliedHours = maxHours
        ElMessage.warning(`申请工时不能超过时间范围计算的工时：${maxHours}小时`)
      }

      emit('update:details', [...props.details])
    }

    // 处理确认工时直接修改
    const handleConfirmHoursChange = (detail) => {
      // 计算时间范围对应的最大工时
      const maxHours = calculateHours(detail.confirmStartTime, detail.confirmEndTime)

      // 验证工时范围
      if (detail.confirmHours < 0) {
        detail.confirmHours = 0
        ElMessage.warning('确认工时不能为负数')
      } else if (detail.confirmHours > 24) {
        detail.confirmHours = 24
        ElMessage.warning('单日确认工时不能超过24小时')
      } else if (detail.confirmHours > maxHours && maxHours > 0) {
        detail.confirmHours = maxHours
        ElMessage.warning(`确认工时不能超过时间范围计算的工时：${maxHours}小时`)
      }

      emit('update:details', [...props.details])
    }

    // 批量设置时间
    const applyBatchSettings = () => {
      if (!validateTimeRange(batchSettings.startTime, batchSettings.endTime)) {
        ElMessage.error('批量设置的结束时间必须晚于开始时间')
        return
      }

      const updatedDetails = props.details.map((detail) => ({
        ...detail,
        startTime: batchSettings.startTime,
        endTime: batchSettings.endTime,
        appliedHours: batchSettings.appliedHours,
      }))

      emit('update:details', updatedDetails)
      ElMessage.success('批量设置成功')
      batchMode.value = false
    }

    // 检查是否为工作日 - 使用moment.js
    const isWorkday = (dateString) => {
      const date = moment(dateString, 'YYYY-MM-DD')
      if (!date.isValid()) return true

      const day = date.day()
      return day !== 0 && day !== 6 // 0是周日，6是周六
    }

    // 获取最大可申请工时（返回数字类型）
    const getMaxAppliedHours = (detail) => {
      return calculateHours(detail.startTime, detail.endTime)
    }

    // 获取最大可申请工时的显示文本（返回字符串类型）
    const getMaxAppliedHoursText = (detail) => {
      return calculateHours(detail.startTime, detail.endTime).toFixed(2)
    }

    // 获取最大可确认工时（返回数字类型，用于ElInputNumber的max属性）
    const getMaxConfirmHours = (detail) => {
      return calculateHours(detail.confirmStartTime, detail.confirmEndTime)
    }

    // 获取最大可确认工时的显示文本（返回字符串类型，用于显示）
    const getMaxConfirmHoursText = (detail) => {
      return calculateHours(detail.confirmStartTime, detail.confirmEndTime).toFixed(2)
    }

    // 计算属性：是否为确认模式
    const isConfirmMode = computed(() => props.mode === 'confirm')

    // 计算属性：标题文本
    const titleText = computed(() => {
      if (props.readonly) {
        return isConfirmMode.value ? '申请与确认工时详情' : '申请与确认工时详情'
      }
      return isConfirmMode.value ? '确认工时详情' : '工时详情'
    })

    // 计算属性：主题色
    const themeColor = computed(() => {
      return isConfirmMode.value ? 'green' : 'blue'
    })

    return {
      batchMode,
      batchSettings,
      totalHours,
      totalConfirmHours,
      formatDate,
      isWorkday,
      getMaxAppliedHours,
      getMaxAppliedHoursText,
      getMaxConfirmHours,
      getMaxConfirmHoursText,
      handleTimeChange,
      handleConfirmTimeChange,
      handleHoursChange,
      handleConfirmHoursChange,
      applyBatchSettings,
      isConfirmMode,
      titleText,
      themeColor,
    }
  },
  template: /*html */ `
    <div
      class="flex h-full min-h-0 w-full flex-col overflow-hidden rounded-xl border border-gray-100 bg-white shadow-sm"
    >
      <!-- 头部工具栏 -->
      <div
        class="flex-shrink-0 border-b border-gray-100 px-6 py-4"
        :class="isConfirmMode ? 'bg-gradient-to-r from-green-50 to-emerald-50' : 'bg-gradient-to-r from-blue-50 to-indigo-50'"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <div class="h-2 w-2 rounded-full" :class="isConfirmMode ? 'bg-green-500' : 'bg-blue-500'"></div>
              <h3 class="text-lg font-semibold text-gray-800">{{ titleText }}</h3>
            </div>
            <div v-if="details.length > 0" class="flex items-center space-x-4">
              <div class="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-700">
                申请工时: {{ totalHours }} 小时
              </div>
              <div
                v-if="isConfirmMode || (readonly && totalConfirmHours > 0)"
                class="rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-700"
              >
                确认工时: {{ totalConfirmHours }} 小时
              </div>
            </div>
          </div>

          <!-- 批量设置工具（仅申请模式且非只读时显示） -->
          <div v-if="!isConfirmMode && !readonly && details.length > 0" class="flex items-center space-x-2">
            <el-button
              v-if="!batchMode"
              size="small"
              type="primary"
              @click="batchMode = true"
              icon="Setting"
              class="shadow-sm"
            >
              批量设置
            </el-button>

            <div v-else class="flex items-center space-x-2 rounded-lg bg-white p-2 shadow-sm">
              <el-time-picker
                v-model="batchSettings.startTime"
                placeholder="开始时间"
                format="HH:mm"
                value-format="HH:mm"
              />
              <span class="text-gray-400">-</span>
              <el-time-picker
                v-model="batchSettings.endTime"
                placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
              <el-input-number
                v-model="batchSettings.appliedHours"
                :min="0"
                :max="getMaxAppliedHours(batchSettings)"
                :precision="2"
                :step="0.01"
              />
              <el-button type="success" @click="applyBatchSettings">应用</el-button>
              <el-button @click="batchMode = false">取消</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="details.length === 0" class="flex flex-1 items-center justify-center p-12">
        <div class="text-center">
          <div class="mb-4">
            <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
              <span class="text-2xl">📅</span>
            </div>
          </div>
          <h4 class="mb-2 text-lg font-medium text-gray-600">暂无工时详情</h4>
          <p class="text-gray-500">
            {{ isConfirmMode ? '暂无可确认的工时详情' : '请先选择开始日期和结束日期，系统将自动生成工时详情' }}
          </p>
        </div>
      </div>

      <!-- 工时详情列表 - 可滚动区域 -->
      <div v-else class="flex-1 overflow-y-auto">
        <div class="divide-y divide-gray-50">
          <div
            v-for="(detail, index) in details"
            :key="detail.id || index"
            class="px-6 py-2 transition-all duration-200 hover:bg-gray-50/50"
          >
            <!-- 日期标题行 -->
            <div class="mb-1 flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div
                  class="flex h-8 w-8 items-center justify-center rounded-lg"
                  :class="isConfirmMode ? 'bg-green-100' : 'bg-blue-100'"
                >
                  <span class="text-sm font-semibold" :class="isConfirmMode ? 'text-green-600' : 'text-blue-600'">
                    {{ index + 1 }}
                  </span>
                </div>
                <div class="flex flex-row">
                  <div class="font-medium text-gray-900">{{ formatDate(detail.date) }}</div>
                  <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <span v-if="!isWorkday(detail.date)" class="rounded bg-red-100 px-2 py-0.5 text-xs text-red-600">
                      周末
                    </span>
                    <span v-else class="rounded bg-green-100 px-2 py-0.5 text-xs text-green-600">工作日</span>
                    <span
                      v-if="detail.confirmStatus === 0"
                      class="rounded bg-yellow-100 px-2 py-0.5 text-xs text-yellow-600"
                    >
                      待确认
                    </span>
                    <span
                      v-else-if="detail.confirmStatus === 1"
                      class="rounded bg-blue-100 px-2 py-0.5 text-xs text-blue-600"
                    >
                      已确认
                    </span>
                    <span
                      v-else-if="detail.confirmStatus === -1"
                      class="rounded bg-red-100 px-2 py-0.5 text-xs text-red-600"
                    >
                      已作废
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 申请信息 -->
            <div
              class="mb-1 overflow-hidden rounded-xl border border-blue-100 bg-gradient-to-br from-blue-50/50 to-indigo-50/30 shadow-sm"
            >
              <div class="border-b border-blue-100 bg-white/60 px-3 py-2">
                <div class="flex items-center space-x-2">
                  <h4 class="text-sm font-semibold text-blue-800">申请信息</h4>
                </div>
              </div>

              <div class="p-3">
                <div
                  class="grid grid-cols-1 gap-3"
                  :class="isConfirmMode ? 'md:grid-cols-3' : 'md:grid-cols-2 lg:grid-cols-4'"
                >
                  <!-- 申请开始时间卡片 -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center space-x-2">
                      <div class="h-2 w-2 rounded-full bg-green-500"></div>
                      <label class="text-xs font-medium text-gray-700">
                        {{ isConfirmMode ? '申请开始时间' : '开始时间' }}
                      </label>
                    </div>
                    <el-time-picker
                      v-if="!isConfirmMode && !readonly"
                      v-model="detail.startTime"
                      placeholder="选择开始时间"
                      format="HH:mm"
                      value-format="HH:mm"
                      class="w-full"
                      @change="handleTimeChange(detail, 'startTime')"
                    />
                    <div v-else class="rounded border bg-white px-3 py-2 text-sm text-gray-700">
                      {{ detail.startTime || '-' }}
                    </div>
                  </div>

                  <!-- 申请结束时间卡片 -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center space-x-2">
                      <div class="h-2 w-2 rounded-full bg-red-500"></div>
                      <label class="text-xs font-medium text-gray-700">
                        {{ isConfirmMode ? '申请结束时间' : '结束时间' }}
                      </label>
                    </div>
                    <el-time-picker
                      v-if="!isConfirmMode && !readonly"
                      v-model="detail.endTime"
                      placeholder="选择结束时间"
                      format="HH:mm"
                      value-format="HH:mm"
                      class="w-full"
                      @change="handleTimeChange(detail, 'endTime')"
                    />
                    <div v-else class="rounded border bg-white px-3 py-2 text-sm text-gray-700">
                      {{ detail.endTime || '-' }}
                    </div>
                  </div>

                  <!-- 计算工时卡片（仅申请模式显示） -->
                  <div
                    v-if="!isConfirmMode"
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center space-x-2">
                      <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                      <label class="text-xs font-medium text-gray-700">计算工时</label>
                    </div>
                    <div
                      class="flex h-8 items-center justify-center rounded-lg border border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 px-3 text-sm font-medium text-gray-700"
                    >
                      <span class="text-blue-600">{{ getMaxAppliedHoursText(detail) || 0 }}</span>
                      <span class="ml-1 text-gray-500">小时</span>
                    </div>
                  </div>

                  <!-- 申请工时卡片 -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                        <label class="text-xs font-medium text-gray-700">申请工时</label>
                      </div>
                      <span v-if="!isConfirmMode && !readonly" class="text-xs text-gray-500">
                        ≤{{ getMaxAppliedHoursText(detail) || 0 }}h
                      </span>
                    </div>
                    <el-input-number
                      v-if="!isConfirmMode && !readonly"
                      v-model="detail.appliedHours"
                      :min="0"
                      :max="getMaxAppliedHours(detail) || 24"
                      :precision="2"
                      :step="0.01"
                      class="w-full"
                      @change="handleHoursChange(detail)"
                      placeholder="输入申请工时"
                    />
                    <div v-else class="rounded border bg-white px-3 py-2 text-sm text-gray-700">
                      {{ detail.appliedHours || 0 }} 小时
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 确认信息 -->
            <div
              v-if="isConfirmMode || readonly"
              class="mb-1 overflow-hidden rounded-xl border border-green-100 bg-gradient-to-br from-green-50/50 to-emerald-50/30 shadow-sm"
            >
              <div class="border-b border-green-100 bg-white/60 px-3 py-2">
                <div class="flex items-center space-x-2">
                  <h4 class="text-sm font-semibold text-green-800">确认信息</h4>
                </div>
              </div>

              <div class="p-3">
                <div v-if="readonly" class="grid grid-cols-1 gap-3 md:grid-cols-3">
                  <!-- 确认开始时间卡片（只读） -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center space-x-2">
                      <div class="h-2 w-2 rounded-full bg-green-500"></div>
                      <label class="text-xs font-medium text-gray-700">确认开始时间</label>
                    </div>
                    <div class="rounded border bg-white px-3 py-2 text-sm text-gray-700">
                      {{ detail.confirmStartTime || '-' }}
                    </div>
                  </div>

                  <!-- 确认结束时间卡片（只读） -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center space-x-2">
                      <div class="h-2 w-2 rounded-full bg-red-500"></div>
                      <label class="text-xs font-medium text-gray-700">确认结束时间</label>
                    </div>
                    <div class="rounded border bg-white px-3 py-2 text-sm text-gray-700">
                      {{ detail.confirmEndTime || '-' }}
                    </div>
                  </div>

                  <!-- 确认工时卡片（只读） -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center space-x-2">
                      <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                      <label class="text-xs font-medium text-gray-700">确认工时</label>
                    </div>
                    <div class="rounded border bg-white px-3 py-2 text-sm text-gray-700">
                      {{ detail.confirmHours || 0 }} 小时
                    </div>
                  </div>
                </div>

                <!-- 确认信息编辑模式 -->
                <div v-else class="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
                  <!-- 确认开始时间卡片 -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center space-x-2">
                      <div class="h-2 w-2 rounded-full bg-green-500"></div>
                      <label class="text-xs font-medium text-gray-700">确认开始时间</label>
                    </div>
                    <el-time-picker
                      v-model="detail.confirmStartTime"
                      placeholder="选择确认开始时间"
                      format="HH:mm"
                      value-format="HH:mm"
                      class="w-full"
                      @change="handleConfirmTimeChange(detail, 'confirmStartTime')"
                    />
                  </div>

                  <!-- 确认结束时间卡片 -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center space-x-2">
                      <div class="h-2 w-2 rounded-full bg-red-500"></div>
                      <label class="text-xs font-medium text-gray-700">确认结束时间</label>
                    </div>
                    <el-time-picker
                      v-model="detail.confirmEndTime"
                      placeholder="选择确认结束时间"
                      format="HH:mm"
                      value-format="HH:mm"
                      class="w-full"
                      @change="handleConfirmTimeChange(detail, 'confirmEndTime')"
                    />
                  </div>

                  <!-- 计算确认工时卡片 -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center space-x-2">
                      <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                      <label class="text-xs font-medium text-gray-700">计算确认工时</label>
                    </div>
                    <div
                      class="flex h-8 items-center justify-center rounded-lg border border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 px-3 text-sm font-medium text-gray-700"
                    >
                      <span class="text-blue-600">{{ getMaxConfirmHoursText(detail) || 0 }}</span>
                      <span class="ml-1 text-gray-500">小时</span>
                    </div>
                  </div>

                  <!-- 确认工时卡片 -->
                  <div
                    class="group rounded-lg border border-white/50 bg-white/80 p-2 shadow-sm transition-all duration-200 hover:bg-white hover:shadow-md"
                  >
                    <div class="mb-1 flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                        <label class="text-xs font-medium text-gray-700">确认工时</label>
                      </div>
                      <span class="text-xs text-gray-500">≤{{ getMaxConfirmHoursText(detail) || 0 }}h</span>
                    </div>
                    <el-input-number
                      v-model="detail.confirmHours"
                      :min="0"
                      :max="getMaxConfirmHours(detail) || 24"
                      :precision="2"
                      :step="0.01"
                      class="w-full"
                      @change="handleConfirmHoursChange(detail)"
                      placeholder="输入确认工时"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 状态和警告信息 -->
            <div class="mt-2 space-y-1">
              <!-- 申请时间冲突警告 -->
              <div
                v-if="!isConfirmMode && detail.startTime && detail.endTime && detail.startTime >= detail.endTime"
                class="flex items-center space-x-2 rounded-lg border border-red-200 bg-red-50 p-2"
              >
                <span class="text-red-500">⚠️</span>
                <span class="text-sm text-red-700">结束时间必须晚于开始时间</span>
              </div>

              <!-- 确认时间冲突警告 -->
              <div
                v-if="isConfirmMode && detail.confirmStartTime && detail.confirmEndTime && detail.confirmStartTime >= detail.confirmEndTime"
                class="flex items-center space-x-2 rounded-lg border border-red-200 bg-red-50 p-2"
              >
                <span class="text-red-500">⚠️</span>
                <span class="text-sm text-red-700">确认结束时间必须晚于确认开始时间</span>
              </div>

              <!-- 申请工时超限警告 -->
              <div
                v-if="!isConfirmMode && detail.appliedHours > getMaxAppliedHours(detail) && getMaxAppliedHours(detail) > 0"
                class="flex items-center space-x-2 rounded-lg border border-orange-200 bg-orange-50 p-2"
              >
                <span class="text-orange-500">⚠️</span>
                <span class="text-sm text-orange-700">申请工时超过时间范围计算的工时</span>
              </div>

              <!-- 确认工时超限警告 -->
              <div
                v-if="isConfirmMode && detail.confirmHours > getMaxConfirmHours(detail) && getMaxConfirmHours(detail) > 0"
                class="flex items-center space-x-2 rounded-lg border border-orange-200 bg-orange-50 p-2"
              >
                <span class="text-orange-500">⚠️</span>
                <span class="text-sm text-orange-700">确认工时超过时间范围计算的工时</span>
              </div>

              <!-- 申请工时异常提示 -->
              <div
                v-if="!isConfirmMode && detail.appliedHours > 12"
                class="flex items-center space-x-2 rounded-lg border border-yellow-200 bg-yellow-50 p-2"
              >
                <span class="text-yellow-500">💡</span>
                <span class="text-sm text-yellow-700">工时超过12小时，请确认是否正确</span>
              </div>

              <!-- 工时差异提示（确认模式） -->
              <div
                v-if="isConfirmMode && Math.abs((detail.confirmHours || 0) - (detail.appliedHours || 0)) > 0.5"
                class="flex items-center space-x-2 rounded-lg border border-blue-200 bg-blue-50 p-2"
              >
                <span class="text-blue-500">💡</span>
                <span class="text-sm text-blue-700">
                  确认工时与申请工时存在差异： {{ Math.abs((detail.confirmHours || 0) - (detail.appliedHours || 0)) }}
                  小时
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
}
