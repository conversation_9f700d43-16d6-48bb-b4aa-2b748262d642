# DynamicColumnConfigurator 组件文档

## 组件概述

`DynamicColumnConfigurator` 是一个基于 Vue 3 的通用列配置组件，用于动态控制表格、甘特图等组件中显示的列。通过弹出式菜单，用户可以灵活选择需要显示的列，提升数据展示的灵活性和用户体验。

## 功能特点

- **弹出式配置界面**：点击按钮触发弹出菜单显示列配置选项
- **列搜索过滤**：支持对列名进行实时搜索过滤
- **固定列支持**：可设置必须显示的固定列，不可被用户取消选择
- **默认值支持**：提供合理的默认配置，即使父组件未传递配置也能正常工作
- **响应式更新**：列选择变化实时反馈到视图

## 实现思路

### 1. 数据管理设计

组件采用了明确的数据分离模式：

- **固定列与可选列分离**：
  - 固定列（`fixedColumnKeys`）：从所有列中提取设置了 `fixed: true` 的列
  - 可选列：用户可以自由选择显示或隐藏的列

- **内部状态与外部接口分离**：
  - 内部状态（`selectedNonFixedKeys`）：仅存储用户选择的非固定列
  - 外部接口（`v-model/modelValue`）：与父组件交互的数据接口
  - 显示状态（`displaySelectedKeys`）：合并固定列和选中的非固定列用于UI展示

### 2. 双向数据流

![数据流示意图](https://via.placeholder.com/500x200?text=数据流示意图)

- **输入数据流**：
  - 通过 `allColumns` 接收所有可能的列配置
  - 通过 `modelValue`（`v-model`）接收当前选中的列

- **输出数据流**：
  - 通过 `update:modelValue` 事件将用户选择的非固定列同步回父组件

### 3. 关键计算属性

```js
// 固定列（不可更改）
const fixedColumnKeys = computed(() => { 
  return props.allColumns
    .filter(col => col.fixed === true)
    .map(col => col.key)
})

// 搜索过滤后的列
const filteredColumns = computed(() => {
  if (!searchTerm.value) return props.allColumns
  return props.allColumns.filter(col => 
    col.label.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
})

// 最终UI显示为选中的列（固定列 + 用户选择的非固定列）
const displaySelectedKeys = computed(() => {
  return [...selectedNonFixedKeys.value, ...fixedColumnKeys.value]
})
```

## 使用示例

```vue
<!-- 基本用法 -->
<DynamicColumnConfigurator
  v-model="visibleColumns"
  :all-columns="columnConfig"
  button-text="列配置"
  button-icon="Grid"
/>

<!-- 脚本部分 -->
<script>
export default {
  setup() {
    // 当前选中的列（非固定列）
    const visibleColumns = ref(['col1', 'col2', 'col3'])
    
    // 所有可配置的列
    const columnConfig = computed(() => [
      { key: 'col0', label: '序号', fixed: true },  // 固定列
      { key: 'col1', label: '名称' },
      { key: 'col2', label: '日期' },
      { key: 'col3', label: '状态' },
      { key: 'col4', label: '描述' }
    ])
    
    // 监听列变化
    watch(visibleColumns, (newColumns) => {
      // 更新表格或甘特图的显示列
      updateTableColumns(newColumns)
    })
    
    return { visibleColumns, columnConfig }
  }
}
</script>
```

## 组件API

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| allColumns | Array | 一组默认列 | 所有可配置的列 |
| modelValue (v-model) | Array | ['name', 'date'] | 当前选中的非固定列 |
| defaultSearchTerm | String | '' | 默认搜索词 |
| searchPlaceholder | String | '搜索列名...' | 搜索框占位文本 |
| buttonText | String | '列设置' | 触发按钮文本 |
| buttonIcon | String | 'Setting' | 触发按钮图标 |

### 列配置对象格式

```js
{
  key: 'column_id',     // 列的唯一标识符
  label: '列名称',      // 显示名称 
  fixed: true/false     // 是否为固定列（可选，默认为false）
}
```

### 事件

| 事件名 | 参数 | 说明 |
|-------|------|------|
| update:modelValue | Array | 当列选择变化时触发，返回选中的非固定列 |

## 技术要点

### 1. 固定列处理

组件巧妙地处理了固定列与可选列的关系：
- 固定列对用户显示为禁用状态
- 固定列总是显示为选中状态
- 固定列不包含在组件向外发送的值中

### 2. 搜索筛选

基于Vue的响应式系统，实现了实时搜索过滤：
- 输入即搜索，无需额外操作
- 搜索结果为空时显示提示信息

### 3. UI交互

采用El-Popover实现点击触发的弹出菜单：
- 解决了El-Tooltip不适合点击触发场景的问题
- 通过v-model:visible实现精确控制

## 优化与扩展方向

1. **列拖拽排序**：添加对列顺序调整的支持
2. **分组显示**：支持按类别分组显示列选项
3. **列设置持久化**：结合localStorage实现用户配置的持久保存
4. **响应式设计优化**：针对不同设备屏幕大小优化展示

## 总结

`DynamicColumnConfigurator`组件提供了一种高度可复用的列配置解决方案，适用于各类表格、甘特图等需要动态配置列的场景。通过清晰的设计和良好的封装，实现了既简单又强大的用户体验，是前端开发中表格列配置的理想选择。
