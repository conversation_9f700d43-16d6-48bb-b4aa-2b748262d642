const { ref, reactive } = Vue;
const { useVModel } = VueUse;
const SearchForm = {
  props: {
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  emits: ['search'],
  setup(props, { emit }) {
    // 展开收起状态
    const collapseStatus = ref(true);
    // 表单数据
    const formData = useVModel(props, 'form', emit);

    // 禁用Assessment Period Start Date
    const disableAPStartMethod = (params) => {
      const { date } = params;
      // 开始时间不可晚于结束时间并且不可晚于当前时间
      return moment(date).isAfter(moment(formData.endDateAP ?? '')) || moment(date).isAfter(moment());
    };
    // Assessment Period Start Date改变
    const startDateAPChange = ({ value }) => {
      formData.startDateAP = value;
    };

    // 禁用Assessment Period End Date
    const disableAPEndDateMethod = (params) => {
      const { date } = params;
      // 结束时间不可早于开始时间并且不可早于当前时间
      return moment(date).isBefore(moment(formData.startDateAP ?? '')) || moment(date).isAfter(moment());
    };
    // Assessment Period End Date改变
    const endDateAPChange = ({ value }) => {
      formData.endDateAP = value;
    };

    // 搜索方法
    const searchMethod = () => {
      emit('search');
    };
    // 清除搜索条件，并恢复默认值, 重新搜索
    const cancelSearch = () => {
      formData.value.assessmentDate = [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')];
      formData.value.esn = '';
      formData.value.wo = '';
      formData.value.sm = '';
      formData.value.pn = '';
      formData.value.wip = '';
      formData.value.engineType = '';
      formData.value.releaseDate = '';
      emit('search');
    };

    return {
      collapseStatus,
      formData,
      disableAPStartMethod,
      startDateAPChange,
      disableAPEndDateMethod,
      endDateAPChange,
      searchMethod,
      cancelSearch
    };
  },
  template: `
    <vxe-form v-model:collapseStaus="collapseStatus" :data="formData" title-align="right" title-width="60"
              prevent-submit custom-layout size="mini" align="center">
      <vxe-form-item prop="startDateAP" title-width="200">
        <template #title>
          <span class="text-base">Assessment Period Date:</span>
        </template>
        <el-date-picker v-model="formData.assessmentDate" type="daterange" range-separator="To" size="small"
                        value-format="YYYY-MM-DD" start-placeholder="Start Date" end-placeholder="End Date"
                        clearable></el-date-picker>
      </vxe-form-item>
      <vxe-form-item prop="esn">
        <template #title>
          <span class="text-base">ESN:</span>
        </template>
        <vxe-input v-model.trim="formData.esn" placement="please input esn" clearable></vxe-input>
      </vxe-form-item>
      <vxe-form-item prop="wo" folding>
        <template #title>
          <span class="text-base">WO:</span>
        </template>
        <vxe-input v-model.trim="formData.wo" placement="please input wo" clearable></vxe-input>
      </vxe-form-item>
      <vxe-form-item prop="sm" folding>
        <template #title>
          <span class="text-base">SM:</span>
        </template>
        <vxe-input v-model.trim="formData.sm" placement="please input sm" clearable></vxe-input>
      </vxe-form-item>
      <vxe-form-item prop="pn" folding>
        <template #title>
          <span class="text-base">PN:</span>
        </template>
        <vxe-input v-model.trim="formData.pn" placement="please input pn" clearable></vxe-input>
      </vxe-form-item>
      <vxe-form-item prop="wip" folding>
        <template #title>
          <span class="text-base">WIP:</span>
        </template>
        <vxe-select v-model="formData.wip" placement="please select wip" clearable>
          <vxe-option label="Yes" value="1"></vxe-option>
          <vxe-option label="No" value="0"></vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item prop="engineType" folding title-width="100">
        <template #title>
          <span class="text-base">Engine Type:</span>
        </template>
        <vxe-select v-model="formData.engineType" placement="please select engine type" clearable>
          <vxe-option label="CFM56" value="CFM56"></vxe-option>
          <vxe-option label="LEAP" value="LEAP"></vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item prop="releaseDateStart" folding title-width="150">
        <template #title>
          <span class="text-base">Release Date:</span>
        </template>
        <el-date-picker v-model="formData.releaseDate" type="daterange" range-separator="To" size="small"
                        clearable value-format="YYYY-MM-DD"></el-date-picker>
      </vxe-form-item>

      <vxe-form-item align="center" collapse-node>
        <template #default>
          <vxe-button status="primary" @click="searchMethod">Search</vxe-button>
          <vxe-button @click="cancelSearch">Cancel</vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  `,
};
export default SearchForm;
