import { isWeekend, getWeekday } from '../../../utils/tools.js'

const { defineComponent } = Vue
const { Check } = ElementPlusIconsVue

export default defineComponent({
  name: 'TaskTable',
  components: {
    Check,
  },
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    tableHeight: {
      type: String,
      default: '100%',
    },
    dateRange: {
      type: Array,
      default: () => [],
    },
    getHeaderCellClassName: {
      type: Function,
      required: true,
    },
    getDayTasks: {
      type: Function,
      required: true,
    },
    shouldShowViewMore: {
      type: Function,
      required: true,
    },
    isCellExpanded: {
      type: Function,
      required: true,
    },
  },
  emits: ['addTask', 'viewMore', 'sortTask', 'editTask', 'finish'],
  setup(props, { emit }) {
    // 获取任务的样式类
    const getTaskClass = (task, module, date, row) => {
      const classes = ['task-item']
      const type = task.int_check_type === 0
      // 0-过程检验 1-放行检验
      // classes.push(type ? 'status-day' : 'status-night')
      classes.push(type ? 'status-process-check' : 'status-release-check')

      return classes.join(' ')
    }

    // 查看更多
    const handleViewMore = (esn, date, row) => {
      emit('viewMore', esn, date, row)
    }

    // 完工
    const handleFinish = (row) => {
      emit('finish', row)
    }

    // 获取stafff显示
    const showStaffTitle = (task, date) => {
      return task.staffs.map((item) => {
        return {
          staff: item.str_staff,
          team: item.str_team,
          dec_hours: item.dec_hours,
        }
      })
    }

    // 编辑任务
    const handleEditTask = (task, date, row) => {
      emit('editTask', task, date, row)
    }

    return {
      getTaskClass,
      handleViewMore,
      handleFinish,
      getWeekday,
      showStaffTitle,
      handleEditTask,
    }
  },
  template: /*html */ `
    <el-table
      :data="tableData"
      border
      class="schedule-table"
      :height="tableHeight"
      :header-cell-class-name="getHeaderCellClassName"
    >
      <el-table-column type="index" width="50" label="#" />
      <el-table-column label="operate" width="80">
        <template #default="scope">
          <el-button title="完工" type="success" icon="Check" circle @click="handleFinish(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column prop="str_wo" label="WO" width="100" />
      <el-table-column prop="str_esn" label="ESN" width="80" />
      <el-table-column prop="str_group" label="TYPE" width="80" />
      <el-table-column prop="dt_project_start" label="GP start" min-width="120" />
      <el-table-column prop="dt_project_end" label="GP end" min-width="120" />

      <!-- 动态生成日期列 -->
      <template v-for="date in dateRange" :key="date">
        <el-table-column :label="getWeekday(date)" min-width="350">
          <el-table-column :label="date" min-width="350">
            <template #default="scope">
              <div class="task-list" :data-date="date" :data-module="scope.row.key">
                <div class="tasks-container">
                  <template v-for="task in getDayTasks(scope.row, date)" :key="task.id_task">
                    <div
                      :data-task-id="task.id_task"
                      :class="getTaskClass(task, scope.row.key, date, scope.row)"
                      @click="handleEditTask(task, date, scope.row)"
                    >
                      <div class="task-content flex items-center space-x-2">
                        <el-icon color="rgba(98, 223, 40, 1)" :size="20" v-if="task.is_finish==1" ><SuccessFilled /></el-icon>
                        <el-icon color="red" :size="20" v-else-if="task.is_abnormal"><WarningFilled /></el-icon>
                        <el-tag :color="task.str_shift === '早班' ? '#409EFF' : '#E6A23C'" size="small" effect="dark">
                          {{ task.str_shift }}
                        </el-tag>
                        <span class="truncate font-semibold" :title="task.str_esn">{{ task.str_esn }}</span>
                        <span class="truncate" :title="task.str_task_name">#{{ task.int_task_sort }}({{ task.dec_tat }}h)</span>
                        <span class="truncate" :title="task.str_sm">{{ task.str_sm }}</span>

                        <!-- Staff display: Show first staff + count, full list in tooltip -->
                        <template v-if="showStaffTitle(task, date).length > 0">
                          <div class="flex items-center space-x-1 truncate">
                            <el-tag v-for="(item, index) in showStaffTitle(task, date)" type="info" size="small">
                              <div :key="index" style="padding: 2px 0;">{{ item.staff }}({{ item.dec_hours }}h)</div>
                            </el-tag>
                          </div>
                        </template>
                        <template v-else>
                          <div class="truncate text-gray-400 italic">无人员</div>
                        </template>
                        <span class="truncate" :title="task.dt_limited">
                          {{ task.dt_limited }} {{ task.str_limited_shift }}
                        </span>
                      </div>
                    </div>
                  </template>
                </div>
                <div class="cell-actions">
                  <el-button
                    v-if="shouldShowViewMore(scope.row, date)"
                    type="text"
                    size="small"
                    :data-expanded="isCellExpanded(scope.row.key, date)"
                    @click="handleViewMore(scope.row.key, date, scope.row)"
                  >
                    {{ isCellExpanded(scope.row.key, date) ? '收起' : '查看更多' }}
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
    </el-table>
  `,
})
