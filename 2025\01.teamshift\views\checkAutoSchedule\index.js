import { getSmList, updateEngineTaskStatus } from '../../api/calendar/index.js'
import { getCheckAutoScheduleData, getGanttData, reSchedule } from './api/index.js'

import SearchForm from './components/SearchForm.js'
import TaskTable from './components/TaskTable.js'
import FinishDialog from './components/FinishDialog.js'
import ButtonGroup from './components/ButtonGroup.js'
import { useButtonGroup } from './composables/useButtonGroup.js'
import AssignTaskDrawer from './components/AssignTaskDrawer.js'
import GanttDrawer from './components/GanttDrawer.js'
import CheckAutoScheduleTaskEditDialog from './components/TaskEditDialog.js'
import { useTaskEditDialog } from './composables/useTaskEditDialog.js'

const { ref, onMounted, nextTick, computed, onBeforeUnmount, defineComponent, reactive } = Vue
const { ElMessage } = ElementPlus

export default defineComponent({
  name: 'CheckAutoSchedule',
  components: {
    SearchForm,
    TaskTable,
    FinishDialog,
    ButtonGroup,
    AssignTaskDrawer,
    GanttDrawer,
    CheckAutoScheduleTaskEditDialog,
  },
  props: {
    flow: String,
    type: String,
    enginetype: String,
  },
  setup(props) {
    // 常量定义
    const DEFAULT_VISIBLE_TASKS = 2

    // 状态管理
    const loading = ref(true)
    const tableData = ref([])
    const dateRange = ref([])
    const tableHeight = ref('100%')
    const cellExpandStatus = ref({})

    // 选项列表
    const taskOptionList = ref([])
    const teamOptionList = ref([])
    const shiftOptionList = ref([])
    const smOptionList = ref([])

    // 搜索表单
    const searchForm = ref({
      date: getCurrentMonthRange(),
      str_esn: '',
      str_wo: '',
      str_type: '',
      str_team: '',
      str_sm: '',
    })

    // 完工弹窗
    const finishDialogVisible = ref(false)
    const finishForm = ref({
      str_code: '',
      str_esn: '',
      id_wo: '',
      str_sm: '',
    })

    // 获取当前月份的起始日期和结束日期
    function getCurrentMonthRange() {
      const firstDay = moment().format('YYYY-MM-DD')
      const lastDay = moment().add(14, 'days').format('YYYY-MM-DD')
      return [firstDay, lastDay]
    }

    // 计算日期范围内的所有日期
    const calculateDateRange = () => {
      if (!searchForm.value.date || searchForm.value.date.length !== 2) {
        return []
      }

      const [start, end] = searchForm.value.date
      const dates = []
      let currentDate = new Date(start)
      const endDate = new Date(end)

      while (currentDate <= endDate) {
        dates.push(currentDate.toISOString().split('T')[0])
        currentDate.setDate(currentDate.getDate() + 1)
      }

      return dates
    }

    // 初始化计算日期范围
    dateRange.value = calculateDateRange()
    // 获取年月标题
    const monthGroups = computed(() => {
      const groups = {}
      dateRange.value.forEach((date) => {
        const [year, month] = date.split('-')
        const key = `${year}-${month}`
        if (!groups[key]) {
          groups[key] = []
        }
        groups[key].push(date)
      })
      return groups
    })

    // 获取表格数据
    const getTableData = async () => {
      loading.value = true
      try {
        const params = {
          dt_range: searchForm.value.date,
          str_engine_type: props.enginetype,
          str_type: props.type,
          str_flow: props.flow,
          teams: searchForm.value.str_team,
          str_sm: searchForm.value.str_sm,
          str_esn: searchForm.value.str_esn,
          str_wo: searchForm.value.str_wo,
          str_group: searchForm.value.str_type,
          staffs: searchForm.value.id_staff,
          int_check_type:searchForm.value.int_check_type,
          is_finish:searchForm.value.is_finish,
        }
        const res = await getCheckAutoScheduleData(params)
        tableData.value = res
      } catch (error) {
        ElMessage.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }

    // 检查单元格是否已展开
    const isCellExpanded = (esn, date) => {
      const key = `${esn}-${date}`
      return cellExpandStatus.value[key] !== undefined ? cellExpandStatus.value[key] : true
    }

    // 获取某天的任务
    const getDayTasks = (row, date) => {
      if (!row?.tasks) return []

      const tasks = row.tasks.filter((task) => task.dt_shift === date)
      if (!tasks?.length) return []

      // 如果任务数量小于等于默认显示数量，显示所有任务
      if (tasks.length <= DEFAULT_VISIBLE_TASKS) {
        return tasks
      }
      // 如果单元格已展开，显示所有任务
      if (isCellExpanded(row.key, date)) {
        return tasks
      }
      // 否则只显示默认数量的任务
      return tasks.slice(0, DEFAULT_VISIBLE_TASKS)
    }

    // 检查是否需要显示"查看更多"按钮
    const shouldShowViewMore = (row, date) => {
      if (!row?.tasks) return false

      const tasks = row.tasks.filter((task) => task.dt_shift === date)
      return tasks?.length > DEFAULT_VISIBLE_TASKS
    }

    // 查看更多
    const handleViewMore = (esn, date, row) => {
      const key = `${esn}-${date}`
      // 如果是undefined，说明是初始状态（默认展开），点击后应该收起
      if (cellExpandStatus.value[key] === undefined) {
        cellExpandStatus.value[key] = false
      } else {
        cellExpandStatus.value[key] = !cellExpandStatus.value[key]
      }
    }

    // 处理查询按钮点击
    const handleSearch = async () => {
      await getTableData()
      dateRange.value = calculateDateRange()
    }

    // 完工
    const handleFinish = (row) => {
      finishForm.value.str_code = row.str_wo
      finishForm.value.str_esn = row.str_esn
      finishForm.value.id_wo = row.id_wo
      finishForm.value.str_sm = ''
      getSmOptionList(row.id_wo, row.str_group)
      finishDialogVisible.value = true
    }

    // 获取SM选项列表
    const getSmOptionList = async (id_wo, str_group) => {
      const res = await getSmList(id_wo, str_group, props.flow)
      smOptionList.value = res.map((item) => ({
        value: item.str_sm,
        label: item.str_sm,
      }))
    }

    // 提交完工
    const handleFinishSubmit = async () => {
      try {
        await updateEngineTaskStatus(finishForm.value.id_wo, props.flow, finishForm.value.str_sm,1)
        finishDialogVisible.value = false
        finishForm.value.str_sm = null
        await getTableData()
        ElMessage.success('操作成功')
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }

    // 刷新数据
    const handleRefresh = () => {
      getTableData()
    }

    // 计算表格高度
    const calculateTableHeight = _.debounce(() => {
      nextTick(() => {
        const container = document.querySelector('.schedule-container')
        if (!container) return

        const viewportHeight =
          window.self === window.top ? window.innerHeight : window.frameElement?.clientHeight || window.innerHeight

        const searchFormElement = container.querySelector('.search-form') // Renamed to avoid conflict
        const searchFormHeight = searchFormElement?.getBoundingClientRect().height || 0

        const formMargin = 50
        const availableHeight = viewportHeight - searchFormHeight - formMargin

        tableHeight.value = `${Math.floor(availableHeight)}px`
      })
    }, 300)

    // 监听窗口大小变化
    const resizeHandler = () => {
      calculateTableHeight()
    }

    // 生命周期钩子 - 组件挂载
    onMounted(async () => {
      await getTableData()
      calculateTableHeight()
      window.addEventListener('resize', resizeHandler)
    })

    // 生命周期钩子 - 组件销毁前
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
    })

    // 获取表头单元格类名
    const getHeaderCellClassName = ({ column }) => {
      if (column.label === '星期六' || column.label === '星期日') {
        return '!bg-red-500 !text-white'
      }
      return '!h-10'
    }

    // 按钮组
    const { assignTaskDialogVisible, handleAssignTask } = useButtonGroup()

    // 使用 TaskEditDialog composable
    const {
      editTaskDialogVisible,
      editTaskForm,
      handleEditTask,
      handleEditTaskSave,
      handleEditTaskCancel,
      handleEditTaskDelete,
    } = useTaskEditDialog(getTableData)

    // 甘特图抽屉状态和数据
    const ganttDrawerVisible = ref(false)
    const ganttTasks = reactive({
      data: [],
      links: [],
    })

    const handleGanttChart = async () => {
      try {
        const params = {
          dt_range: searchForm.value.date,
          str_engine_type: props.enginetype,
          str_type: props.type,
          str_flow: props.flow,
        }

        const response = await getGanttData(params)

        if (response && response.nodes && Array.isArray(response.nodes)) {
          if (response.nodes.length > 0) {
            ganttTasks.data = response.nodes.map((node) => ({
              id: node.id,
              text: node.str_node,
              start_date: node.dt_begin,
              dt_end: node.dt_end,
              parent: node.id_root !== '0' && node.id_root !== null ? node.id_root : undefined,
              progress: 0.0,
              duration: node.dec_tat,
              open: true,
            }))
            ganttTasks.links = response.links || []
          } else {
            ganttTasks.data = []
            ganttTasks.links = []
            ElMessage.info('未查询到甘特图数据。')
          }
        } else {
          ganttTasks.data = []
          ganttTasks.links = []
          ElMessage.error('获取甘特图数据格式不正确。')
        }
      } catch (error) {
        console.error('获取甘特图数据失败:', error)
        ganttTasks.data = []
        ganttTasks.links = []
        ElMessage.error(`获取甘特图数据失败: ${error instanceof Error ? error.message : String(error)}`)
      } finally {
        ganttDrawerVisible.value = true
      }
    }

    // 防止重复点击
    const isReSchedule = ref(false)
    // 重排
    const handleReSchedule = _.debounce(async () => {
      if (isReSchedule.value) return
      isReSchedule.value = true
      const params = {
        id_wo: searchForm.value.str_wo,
        str_engine_type: props.enginetype,
        str_group: props.type,
        str_flow: props.flow,
      }
      try {
        await reSchedule(params)
        ElMessage.success('重排成功')
        await getTableData()
      } catch (error) {
        ElMessage.error('重排失败')
      } finally {
        isReSchedule.value = false
      }
    }, 500)

    return {
      searchForm,
      tableData,
      taskOptionList,
      teamOptionList,
      shiftOptionList,
      smOptionList,
      tableHeight,
      dateRange,
      monthGroups,
      loading,
      finishDialogVisible,
      finishForm,
      getDayTasks,
      handleViewMore,
      shouldShowViewMore,
      isCellExpanded,
      handleSearch,
      getHeaderCellClassName,
      // 任务
      handleFinish,
      handleFinishSubmit,
      handleRefresh,
      getTableData,
      // 编辑任务弹窗相关 (从 composable 返回)
      editTaskDialogVisible,
      editTaskForm,
      handleEditTask,
      handleEditTaskSave,
      handleEditTaskCancel,
      handleEditTaskDelete,
      // 按钮组
      isReSchedule,
      assignTaskDialogVisible,
      handleAssignTask,
      handleReSchedule,
      // 甘特图抽屉
      ganttDrawerVisible,
      ganttTasks,
      handleGanttChart,
    }
  },
  template: /*html */ `
    <div class="schedule-container">
      <!-- 加载遮罩层 -->
      <div v-if="loading" class="loading-mask">
        <div class="loading-spinner">
          <el-icon class="is-loading" color="#409EFF" :size="30">
            <Loading />
          </el-icon>
          <div class="loading-text">数据加载中...</div>
        </div>
      </div>
      
      <!-- 搜索表单组件 -->
      <search-form 
        v-model:search-form="searchForm"
        @search="handleSearch"
      />
      <!-- 按钮组件 -->
      <button-group 
        v-if="enginetype && flow && type "
        :is-re-schedule="isReSchedule"
        @assign-task="handleAssignTask"
        @gantt-chart="handleGanttChart"
        @re-schedule="handleReSchedule"
      />

      <!-- 任务表格组件 -->
      <task-table
        :table-data="tableData"
        :date-range="dateRange"
        :table-height="tableHeight"
        :get-header-cell-class-name="getHeaderCellClassName"
        :get-day-tasks="getDayTasks"
        :should-show-view-more="shouldShowViewMore"
        :is-cell-expanded="isCellExpanded"
        @view-more="handleViewMore"
        @finish="handleFinish"
        @edit-task="handleEditTask" 
      />

      <!-- 完工弹窗 -->
      <finish-dialog
        v-model:visible="finishDialogVisible"
        :form="finishForm"
        :sm-option-list="smOptionList"
        @submit="handleFinishSubmit"
      />

      <!-- 分配任务抽屉 -->
      <assign-task-drawer
        v-if="assignTaskDialogVisible"
        v-model:visible="assignTaskDialogVisible"
        v-model:search-form="searchForm"
        :flow="flow"
        :type="type"
        :enginetype="enginetype"
        @refresh="handleRefresh"
      />

      <!-- 甘特图抽屉 -->
      <gantt-drawer
        v-if="ganttDrawerVisible" 
        v-model:visible="ganttDrawerVisible"
        :tasks="ganttTasks"
        title="生产计划甘特图"
      />

      <!-- 编辑任务弹窗 -->
      <check-auto-schedule-task-edit-dialog
        v-if="editTaskDialogVisible" 
        v-model:visible="editTaskDialogVisible"
        :form="editTaskForm"
        title="编辑检验任务"
        :type="type"
        @save="handleEditTaskSave"
        @cancel="handleEditTaskCancel"
      />
    </div>
  `,
})
