<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://unpkg.com/@dataview/datav-vue3@latest/umd/datav.umd.js"></script>
    <!--    引入element-plus-->
    <script src="../assets/element-plus@2.5.5/index.js"></script>
    <link href="../assets/element-plus@2.5.5/index.css" rel="stylesheet">
    <title>DataV</title>
    <style>
        html,
        body,
        #app {
            width: 100%;
            height: 100%;
            overflow: hidden;
            padding: 0px;
            margin: 0px;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-row style="margin-bottom: 30px;margin-top: 10px;">
            <el-col :span="3" :offset="9">
               生产
                <decoration-8 style="width:300px; height:50px;"></decoration-8>
            </el-col>
            <el-col :span="3">
                大屏
                <decoration-8  :reverse="true" style="width:300px; height:50px;">2222 </decoration-8>
            </el-col>
            <el-col :span="9">
               
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="6" :style="{height: scrollerHeight}">
                <border-box-8>111111 </border-box-8>
            </el-col>
            <el-col :span="3" style="height:5vh">
                <border-box-7>2222 </border-box-8>
            </el-col>
            <el-col :span="3" style="height:5vh">
                <border-box-7>111333111 </border-box-8>
            </el-col>
            <el-col :span="3" style="height:5vh">
                <border-box-7>2222 </border-box-8>
            </el-col>
            <el-col :span="3" style="height:5vh">
                <border-box-7>111333111 </border-box-8>
            </el-col>
            <el-col :span="6" :style="{height: scrollerHeight}">
                <border-box-8>4444 </border-box-8>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="6" :style="{height: scrollerHeight}">
                <border-box-8>111111 </border-box-8>
            </el-col>
            <el-col :span="12" style="height:5vh">
              
            </el-col>
          
            <el-col :span="6" :style="{height: scrollerHeight}">
                <border-box-8>4444 </border-box-8>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="6" :style="{height: scrollerHeight}">
                <border-box-8>111111 </border-box-8>
            </el-col>
            <el-col :span="12" style="height:5vh">
              
            </el-col>
          
            <el-col :span="6" :style="{height: scrollerHeight}">
                <border-box-8>4444 </border-box-8>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="6" style="height: 10vh;">
                
            </el-col>
         
        </el-row>
      

    </div>
</body>



<script>
    const { createApp } = Vue;
    const { BorderBox11, BorderBox8, BorderBox7,Decoration8,Decoration9} = DataV;
    const App = createApp({
        computed: {
            // 滚动区高度 
            scrollerHeight: function () {
                return "25vh";
            }
        }
    });
    App.component('BorderBox11', BorderBox11);
    App.component('BorderBox8', BorderBox8);
    App.component('BorderBox7', BorderBox7);
    App.component('Decoration8', Decoration8);
   
    App.use(ElementPlus);
    App.mount('#app');
</script>

</html>