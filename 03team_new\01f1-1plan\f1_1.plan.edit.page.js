Vue.component('allot-edit-page', {
    props: ['is_allot_show', 'dialogData', 'input_page_type', 'allot_table'],
    template: `
        <el-dialog
            ref="allot_dialog"
            title="Allot" 
            :width="dialogWidth" 
            :visible.sync="isAllotShow" 
            :close-on-press-escape='false'
            :show-close='false'
            class="self_dialog" 
        >
            <el-row  style="margin-bottom: 10px;">
                <el-col :span="6">
                    <span class='edit_heade_color'>Esn</span>:&nbsp;&nbsp;&nbsp;<span>{{dialog_data.main.str_code}}-{{dialog_data.main.str_esn}}</span>
                </el-col>
                <el-col :span='5' :offset='1'>
                    <span class='edit_heade_color'>Wip Flow</span>:&nbsp;&nbsp;&nbsp;<span>{{dialog_data.main.wip_flow}}</span>
                </el-col>
                <el-col :span='6' :offset='1'>
                <!-- fan core lpt  endTime < dt_f41_update -->
                    <span class='edit_heade_color'>F1-1</span>:&nbsp;&nbsp;&nbsp;<span>{{dialog_data.main.dt_f11_begin}}</span>
                </el-col>               
            </el-row>
            <el-table 
                :data='allot_table' 
                tooltip-effect='dark' 
                border
                :cell-class-name='changeStatus'
                style="width: 100%"
            >
                <el-table-column type='index' label='Item' fixed width='60' align="center"></el-table-column>
                <el-table-column label='Type' >
                    <template slot-scope="{row}">
                        <span class='newheadtitle'>{{ row.str_type }}</span>
                    </template>
                </el-table-column>
               
                <el-table-column label='Start' >
                    <template slot-scope="scope">
                        <el-date-picker  
                            v-model="scope.row.dt_start_project" 
                            type="date"  
                            value-format='yyyy-MM-dd' 
                            placeholder="选择日期"
                        ></el-date-picker>
                    </template>
                </el-table-column>

                <el-table-column label='End' >
                    <template slot-scope="scope">
                        <el-date-picker 
                            v-model="scope.row.dt_end_project" 
                            type="date" 
                             value-format='yyyy-MM-dd' 
                             placeholder="选择日期" 
                        ></el-date-picker>
                    </template>
                </el-table-column>              
             
               
            </el-table>

            <div slot="footer">
                <el-button 
                    class="topButton_right" 
                    style="margin-left:20px;" 
                    size="small" 
                    type='danger'
                    @click="closeDialog()">Cancle
                </el-button>
                <el-button 
                    class="topButton_right" 
                    size="small" 
                    type='primary'
                    @click="allot_save()">Save
                </el-button>
            </div>
        </el-dialog>
    `,
    data() {
        return {
            id_wo_select: '',
            save_flag: false,
            date_max: '',
           // date_b1_max: '',
            dialog_data: this.dialogData,
            dialogWidth: '90%',
        }
    },
    filters: {



    },
    created() {
        this.date_max = this.dialog_data.main.dt_f11_update
        //this.date_b1_max = this.dialog_data.main.dt_release_update
        if (Number(window.screen.height) >= 1080) {
            this.dialogWidth = '60%'
        }
    },
    computed: {
        isAllotShow() {
            return this.is_allot_show
        }
    },
    mounted() {
    },
    methods: {
        changeStatus({ row, column, rowIndex, columnIndex }) {
            let _this = this
            if (column.label === 'Start Project' || column.label === 'End Project') {
                if (row.is_change_date) {
                    _this.save_flag = true
                    return 'bgPurple'
                }
            }
            if (column.label === 'Type') {
                if (row.is_change_type) {
                    _this.save_flag = true
                    return 'bgPurple'
                }
            }
        },
        // 关闭弹框
        closeDialog() {
            this.$emit('close-dialog')
        },
        /**保存 分配 */
        allot_save() {
            let _this = this;
            if (_this.save_flag) {
                _this.$confirm('Project的类型或时间发生变化,是否调整完毕?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    _this.save()
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消保存'
                    });
                })
            } else {
                _this.save()
            }



        },
        save() {
            let _this = this;
            let _obj = JSON.stringify(_this.allot_table)
            let save_data_t = JSON.parse(_obj);
            let error_msg = [];
            // 组织判断 action
            save_data_t && save_data_t.length > 0 &&
                save_data_t.forEach((task, index) => {
                    if (!task.dt_start_project || !task.dt_end_project)
                        error_msg.push(`No.${index + 1} please select start-end date`)
                    if (task.dt_end_project) {
                        // if (_this.date_max && _this.date_max && task.dt_end > _this.date_max)
                        //     error_msg.push(`No.${index + 1}   please select end date < ${_this.date_max}`)
                    }
                });
            if (error_msg.length > 0) {
                _this.$message({
                    dangerouslyUseHTMLString: true,
                    message: error_msg.join("<br/>"),
                    type: "warning",
                });
            } else {
                axios.post(globalApiUrl, {
                    au: "ssamc",
                    ap: "api2018",
                    ak: "",
                    ac: "pt_save_project_allot",
                    id_wo: _this.dialog_data.main.id_wo,
                    save_data: save_data_t,
                    str_flow: _this.input_page_type,
                    id: _this.dialog_data.main.id
                })
                    .then(function (response) {
                        if(response.data.code=="success"){                           
                             _this.$emit('save')
                         }

                    })
                    .catch(function (error) {
                        console.log(error);
                    });
            }
        },
    
        
    }
})