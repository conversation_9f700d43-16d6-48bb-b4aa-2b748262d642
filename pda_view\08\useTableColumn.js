export function useTableColumn() {
  const getTableColumn = () => {
    return [
      {
        showColumn: true,
        prop: 'str_po',
        label: '订单号',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_pn',
        label: 'PN',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'part_name',
        label: 'PN Name',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'int_qty',
        label: 'QTY',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_shipper',
        label: '发货人',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'dt_add',
        label: 'ADD',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'str_clientname',
        label: '转包商/采购商',
        field: [{ value: '' }],
        is_select: 'input',
      },
      {
        showColumn: true,
        prop: 'int_delaydays',
        label: '延迟天数',
        field: [{ value: '' }],
        is_select: 'input',
      },
    ];
  };

  return {
    getTableColumn,
  };
}
