// 表格渲染器
import { useTableData } from '../composables/useTableData.js'
import { useTableOperations, useTableFilter } from '../composables/useTableOperations.js'
import { useTableColumns, useColumnRenderer } from '../composables/useTableColumns.js'
import { useTableEvents } from '../composables/useTableEvents.js'
import { useDrawer } from '../composables/useDrawer.js'
import { useDrawerRenderer } from './useDrawerRenderer.js'
import { useFromRenderer } from './useFromRenderer.js'

const { h } = Vue
const { VxeTable } = VXETable

export function useTableRenderer() {
  // 组合各种 composable
  const { tableData, loading, getTableData } = useTableData()
  const { addRow, removeRow, updateRow, clearData, exportData } = useTableOperations(tableData)
  const { searchKeyword, filteredData, setSearchKeyword, clearFilters } = useTableFilter(tableData)
  const { baseColumns, colgroups } = useTableColumns()
  const { renderFrom, searchForm } = useFromRenderer(getTableData)

  const { drawerVisible, drawerTableData, drawerTableColumns, closeDrawer, saveDrawer, openDrawer } =
    useDrawer(searchForm)
  const { createColumn, createColgroup, createSeqColumn } = useColumnRenderer(openDrawer)
  const { selectedRows } = useTableEvents()

  const { renderDrawer } = useDrawerRenderer(
    drawerVisible,
    drawerTableData,
    drawerTableColumns,
    closeDrawer,
    saveDrawer,
  )
  // 渲染基础列的自定义单元格
  const renderBaseColumn = (col) => {
    const baseConfig = {
      title: col.title,
      field: col.field,
      sortable: col.sortable,
    }
    return createColumn(baseConfig)
  }
  // 主表格渲染
  const renderTable = () =>
    h(
      VxeTable,
      {
        data: filteredData.value,
        border: true,
        loading: loading.value,
        stripe: true,
        showOverflow: false,
        height: 'auto',
        rowHeight: 180,
      },
      {
        // 使用函数形式的默认插槽
        default: () => [
          // 选择列
          h('vxe-column', { type: 'checkbox', width: '50' }),

          // 序号列
          createSeqColumn(),

          // 基础列 - 使用动态配置
          ...baseColumns.value.map((col) => renderBaseColumn(col)),

          // 分组列 - 使用新的多层嵌套结构
          ...colgroups.value.map((group) => createColgroup(group.title, group.field, group.items)),
        ],
      },
    )

  // 完整的组件渲染
  const renderComponent = () =>
    h('div', { class: 'px-2 h-[calc(100vh-100px)]' }, [renderFrom(), renderTable(), renderDrawer()])

  return {
    // 数据
    tableData,
    loading,
    selectedRows,
    searchKeyword,
    filteredData,
    baseColumns,
    colgroups,
    drawerVisible,

    // 方法
    getTableData,
    addRow,
    removeRow,
    updateRow,
    clearData,
    exportData,
    setSearchKeyword,
    clearFilters,
    closeDrawer,
    saveDrawer,

    // 渲染函数
    renderComponent,
  }
}
