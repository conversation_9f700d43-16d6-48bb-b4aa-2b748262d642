// ==================== MES申请系统配置文件 ====================
export const MES_CONFIG = {
  // 表单配置
  form: {
    rules: {
      person: [{ required: true, message: '请选择人员', trigger: 'change' }],
      startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
      endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
      reason: [{ required: true, message: '请填写申请事由', trigger: 'blur' }],
    },
    defaultWorkTime: {
      startTime: '09:00',
      endTime: '18:00',
      appliedHours: 8,
    },
  },

  // 状态配置
  status: {
    options: [
      { label: '全部', value: '' },
      { label: '草稿', value: 'Draft' },
      { label: '待审批', value: 'Pending' },
      { label: '已批准', value: 'Approved' },
      { label: '已驳回', value: 'Rejected' },
      { label: '已确认', value: 'Confirmed' },
    ],
    styles: {
      Draft: { type: 'info', text: '草稿' },
      Pending: { type: 'warning', text: '待审批' },
      Approved: { type: 'success', text: '已批准' },
      Rejected: { type: 'danger', text: '已驳回' },
      Confirmed: { type: 'primary', text: '已确认' },
    },
  },

  // 表格配置
  table: {
    columns: [
       {
        field: 'staff_name',
        title: 'MES人员',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
     
     
      {
        field: 'dt_date',
        title: 'MES日期',
        minWidth: 140,
        filterRender: { name: 'FilterCalendar' },
        filters: [{ data: '' }],
      },
      {
        field: 'dt_start_time',
        title: '开始时间',
        minWidth: 120,
        filterRender: { name: 'FilterCalendar' },
        filters: [{ data: '' }],
      },
      {
        field: 'dt_end_time',
        title: '结束时间',
        minWidth: 120,
        filterRender: { name: 'FilterCalendar' },
        filters: [{ data: '' }],
      },
      {
        field: 'dt_confirm_start',
        title: '确认开始时间',
        minWidth: 180,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'dt_confirm_end',
        title: '确认结束时间',
        minWidth: 160,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
       {
        field: 'apply_user',
        title: '申请人',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        type: 'html',
        field: 'int_confirm_status',
        title: '确认状态',
        minWidth: 120,
        filters: [],
        filterMultiple: false,
        formatter: ({ row }) => {
          return row.int_confirm_status === 0
            ? '<span class="text-yellow-500">待确认</span>'
            : row.int_confirm_status === 1
              ? '<span class="text-blue-500">已确认</span>'
              : '<span class="text-red-500">已作废</span>'
        },
      },
      {
        field: 'str_apply_reason',
        title: '申请原因',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_remark',
        title: '备注',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_audit_manage',
        title: '审批人',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
      {
        field: 'str_mes_manage',
        title: 'MES审批人',
        minWidth: 120,
        filterRender: { name: 'FilterInput' },
        filters: [{ data: '' }],
      },
    ],
    pagination: {
      pageSizes: [10, 20, 50, 100],
      layout: 'total, sizes, prev, pager, next, jumper',
    },
  },

  // 操作权限配置
  permissions: {
    canEdit: ['Draft', 'Rejected'],
    canSubmit: ['Draft'],
    canConfirm: ['Approved'],
  },
}
