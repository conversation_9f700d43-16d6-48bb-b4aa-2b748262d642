/**
 * @description 单元体散点图组件
 * @date 2024-07-20
 * <AUTHOR>
 */
import { post } from '../../../../config/axios/httpReuest.js'
import { useDotClick } from '../hooks/useDotClick.js'
const { onMounted, onBeforeUnmount, ref } = Vue
const ScatterChart = {
  props: {
    id: {
      type: String,
      required: true,
    },
    idWo: {
      type: String,
      required: true,
    },
    idEngineType: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
    // 红框时间范围
    redRange: {
      type: Array,
      required: false,
    },
    // 蓝框时间范围
    blueRange: {
      type: Array,
      required: false,
    },
    // 绿框时间范围
    greenRange: {
      type: Array,
      required: false,
    },
  },
  emits: ['dotClick'],
  setup(props, { emit }) {
    const { dotClick, dotDrawerState } = useDotClick()
    // 定义图表数据
    const chartData = ref([])
    // 获取图表数据
    const getChartList = async (filterFields = []) => {
      const myChart = echarts.init(document.getElementById(props.id))
      // 添加loading动画
      myChart.showLoading()
      const { idWo, idEngineType, type } = props
      const params = {
        ac: 'de_sm_site_by_idwo',
        id_wo: idWo,
        id_engine_type: idEngineType,
        str_type: type,
        filter_fields: filterFields,
      }
      const { data } = await post(params)
      if (data.code === 'success') {
        chartData.value = data.data.map((item) => {
          return {
            red: null,
            blue: null,
            green: null,
            ...item,
          }
        })
        initChart(myChart)
      } else {
        ElementPlus.ElMessage.error(data.text)
      }
    }

    /*
     * int_point_type为1时为红色
     * int_point_type为2时为蓝色
     * int_point_type为3时为绿色
     * int_point_type为4时为紫色
     */
    const colorMap = {
      1: '#ef4444',
      2: '#3b82f6',
      3: '#22c55e',
      4: '#a855f7',
    }
    // 初始化图表
    const initChart = (myChart) => {
      myChart.clear()
      // 散点图
      let option = {
        backgroundColor: '#f3f4f6',
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'cross',
          },
          formatter: function (params) {
            // ! 通过formatter方法来自定义提示框内容
            return `日期: ${params.value.dt_ekd}<br>名称: ${params.value.str_part_name}`
          },
        },
        grid: {
          left: '3%',
          right: '3%',
          // containLabel: true,
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            start: 0,
            end: 100,
            // 让datazoom变细
            height: 10,
          },
          {
            type: 'slider',
            show: true,
            yAxisIndex: [0],
            filterMode: 'empty',
            width: 10,
            // datazoom放在左边
            left: '20',
          },
        ],
        dataset: {
          source: chartData.value,
        },
        // x轴为日期,分割以周为单位
        xAxis: [
          {
            type: 'time',
            // axisLabel: {
            //   formatter: function (value) {
            //     // ? 如果当前日期是周一，则显示该周的标签
            //     if (moment(value).day() === 1) {
            //       return moment(value).week().toString()
            //     }
            //     return ''
            //   },
            // },
            // min: 'dataMin',
            // 刻度线
            axisTick: {
              interval: 0,
              alignWithLabel: true,
            },
          },
          {
            type: 'time',
            axisLabel: {
              // 旋转45度
              rotate: 90,
              formatter: function (value) {
                // ? 如果当前日期是本月的第一天，则显示该月的标签
                if (moment(value).date() === 1) {
                  // 显示xxxx年xx月
                  return moment(value).month() + 1 + '月'
                }
                return ''
              },
            },
            // 刻度线
            axisTick: {
              interval: 0,
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            show: false,
            type: 'value',
            scale: true,
            axisLabel: {
              formatter: '{value}',
            },
            axisTick: {
              alignWithLabel: true,
              interval: 0,
            },
            splitLine: {
              show: true,
            },
          },
        ],
        series: [
          {
            name: '可串件',
            type: 'scatter',
            large: true,
            symbolSize: 6,
            progressive: 10000,
            xAxisIndex: 0,
            // 高亮下的样式
            emphasis: {
              disabled: false,
              scale: 1.5,
            },
            // 系列颜色为绿色
            itemStyle: {
              color: function (params) {
                return colorMap[params.data.int_point_type]
              },
            },
            encode: {
              x: 'dt_ekd',
              y: 'y',
            },
          },
          // 绿色框
          {
            type: 'scatter',
            xAxisIndex: 0,
            encode: {
              x: 'dt_ekd',
              y: 'green',
            },
            symbolSize: 0,
            markArea: {
              silent: true,
              label: {
                show: props.greenRange[0] !== null && props.greenRange[1] !== null,
                position: 'insideTopLeft',
                fontSize: 12,
                color: 'black',
              },
              itemStyle: {
                color: 'rgba(0, 128, 0, 0.1)',
                borderColor: 'green',
                borderWidth: 1,
              },
              data: [
                [
                  {
                    name: `建议时间: ${props.greenRange[0]}`,
                    xAxis: props.greenRange[0] ?? '',
                  },
                  {
                    xAxis: props.greenRange[1] ?? '',
                  },
                ],
              ],
            },
          },
          // 蓝色框
          {
            type: 'scatter',
            xAxisIndex: 0,
            encode: {
              x: 'dt_ekd',
              y: 'blue',
            },
            symbolSize: 0,
            markArea: {
              silent: true,
              label: {
                show: props.blueRange[0] !== null && props.blueRange[1] !== null,
                position: 'insideTopLeft',
                fontSize: 12,
                color: 'black',
              },
              itemStyle: {
                color: 'rgba(0, 0, 255, 0.1)',
                borderColor: 'blue',
                borderWidth: 1,
              },
              data: [
                [
                  {
                    name: `班组计划日期: ${props.blueRange[0]}`,
                    xAxis: props.blueRange[0] ?? '',
                  },
                  {
                    xAxis: props.blueRange[1] ?? '',
                  },
                ],
              ],
            },
          },
          // 红色框
          {
            type: 'scatter',
            xAxisIndex: 0,
            encode: {
              x: 'dt_ekd',
              y: 'red',
            },
            symbolSize: 0,
            markArea: {
              silent: true,
              label: {
                show: props.redRange[0] !== null && props.redRange[1] !== null,
                position: 'insideTopLeft',
                fontSize: 12,
                color: 'black',
              },
              itemStyle: {
                color: 'rgba(255, 0, 0, 0.1)',
                borderColor: 'red',
                borderWidth: 1,
              },
              data: [
                [
                  {
                    name: `装配日期: ${props.redRange[0]}`,
                    xAxis: props.redRange[0] ?? '',
                  },
                  {
                    xAxis: props.redRange[1] ?? '',
                  },
                ],
              ],
            },
          },
        ],
      }
      myChart.setOption(option)
      // 隐藏loading动画
      myChart.hideLoading()
      // 点击散点图事件
      myChart.on('click', (params) => {
        const {
          value,
        } = params
        emit('dotClick',value)
      })
    }

    onMounted(() => {
      // 监听resize事件
      window.addEventListener('resize', () => {
        const myChart = echarts.init(document.getElementById(props.id))
        myChart.resize()
      })
    })
    onBeforeUnmount(() => {
      // 移除resize事件
      window.removeEventListener('resize', () => {
        const myChart = echarts.init(document.getElementById(props.id))
        myChart.resize()
      })
    })

    return {
      getChartList,
      dotDrawerState,
    }
  },
  template: /*html*/ `
    <div :id="id" style="width: 100%; height: 100%;"></div>
  `,
}

export default ScatterChart
