﻿import { post } from '../../config/axios/httpReuest.js'
import { useDownloadAndUpload } from '../hooks/useDownloadAndUpload.js'

/**
 * @description 绩效导入
 * <AUTHOR>
 */
const { ref } = Vue
const { genFileId } = ElementPlus
const PerformanceView = {
  setup() {
    const upload = ref(null)
    const fileList = ref([])
    const handleExceed = (files) => {
      upload.value?.clearFiles()
      const file = files[0]
      file.uid = genFileId()
      upload.value?.handleStart(file)
    }
    const { downloadTemplate, uploadXlsx } = useDownloadAndUpload()
    const map = {
      'Sister Team': 'str_sys',
      'BONUS SORT': 'str_bonus_sort',
      '员工编号': 'str_code',
      '员工名称':'str_name',
      'FINANCE SORT':'str_finance_sort',
      'POST SORT':'str_post_sort',
      '身份证号':'str_card_num',
      '银行卡号':'str_bank_num'
    }
    const submitUpload = () => {
      uploadXlsx(upload, fileList, map, 'pe_sister_staff_import')
    }

    // * 下载模板
    const handleDownloadTemplate = () => {
      // 使用XLXS.js生成模板
      const data = [
        ['Sister Team', 'BONUS SORT', '员工编号','员工名称', 'FINANCE SORT','POST SORT','身份证号','银行卡号']
        
      ]
      downloadTemplate(data, '绩效员工导入模板.xlsx')
    }

    return {
      upload,
      fileList,
      handleExceed,
      submitUpload,
      handleDownloadTemplate,
    }
  },
  template: /*html*/ `
    <el-upload
      ref="upload"
      class="m-3"
      v-model:file-list="fileList"
      :limit="1"
      :on-exceed="handleExceed"
      :auto-upload="false"
      accpet=".xls,.xlsx"
    >
      <template #trigger>
        <el-button type="primary">select file</el-button>
      </template>  
      <el-button class="ml-3" type="success" @click="submitUpload">upload to server</el-button>
      <el-button class="ml-3" type="warning" @click="handleDownloadTemplate">download template</el-button>
      <template #tip>
        <div class="text-sm italic">only support xls/xlsx file</div>
      </template>
    </el-upload>
  `,
}

export default PerformanceView
