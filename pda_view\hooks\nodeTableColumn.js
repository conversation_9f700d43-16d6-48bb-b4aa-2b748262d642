// 创建列
const createColumn = (
  title,
  field,
  minWidth,
  fixed,
  filters = [{ data: '' }],
  filterRender = { name: 'FilterInput' },
  filterMultiple = true,
) => {
  return {
    title,
    field,
    minWidth,
    fixed,
    filters,
    filterRender,
    filterMultiple,
    formatter: ({ cellValue, column }) => {
      if (column.field === 'is_out' || column.field === 'is_sp' || column.field === 'is_aog') {
        return cellValue === 1 ? '是' : '否';
      }
      return cellValue;
    },
  };
};
const columnNames = [
  ['P/#', 'int_day', 90, 'left'],
  ['ESN', 'str_esn', 100],
  ['WO', 'str_wo', 100],
  ['Engine Type', 'str_engine_type', 150],
  ['Flow', 'str_flow', 100],
  ['SM', 'str_sm', 90],
  ['PN', 'str_pn', 90],
  ['Pn Name', 'str_part_name', 220],
  ['QTY', 'int_num', 90],
  ['站点要求开工时间', 'dt_plan', 200],
  ['站点要求完工时间', 'dt_ttd', 200],
  ['输出', 'is_out', 100, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['AOG', 'is_aog', 100, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['进入站点时间', 'dt_input', 150],
  ['实际TAT', 'int_actat', 120],
  ['标准TAT', 'int_tatdays', 120],
  ['数据状态', 'is_abnormal', 150],
  ['业务id', 'id_main', 140, 'right'],
];
const columnNames1 = [
  ['P/#', 'int_day', 90, 'left'],
  ['ESN', 'str_esn', 100],
  ['WO', 'str_wo', 100],
  ['Engine Type', 'str_engine_type', 150],
  ['Flow', 'str_flow', 100],
  ['SM', 'str_sm', 90],
  ['PN', 'str_pn', 90],
  ['Pn Name', 'str_part_name', 220],
  ['串件', 'is_sp', 90, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['AOG', 'is_aog', 100, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['受体Wo', 'str_wo_sp', 150],
  ['受体ESN', 'str_esn_sp', 150],
  ['QTY', 'int_num', 90],
  ['站点要求开工时间', 'dt_plan', 200],
  ['站点要求完工时间', 'dt_ttd', 200],
  ['输出', 'is_out', 100, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['AOG', 'is_aog', 100, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['进入站点时间', 'dt_input', 150],
  ['实际TAT', 'int_actat', 120],
  ['标准TAT', 'int_tatdays', 120],
  ['数据状态', 'is_abnormal', 150],
  ['业务id', 'id_main', 140, 'right'],
];
const defaultColumnNames = [
  ['P/#', 'int_day', 90, 'left'],
  ['ESN', 'str_esn', 100],
  ['WO', 'str_wo', 100],
  ['Engine Type', 'str_engine_type', 150],
  ['Flow', 'str_flow', 100],
  ['SM', 'str_sm', 90],
  ['PN', 'str_pn', 90],
  ['Pn Name', 'str_part_name', 220],
  ['串件', 'is_sp', 90, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['AOG', 'is_aog', 100, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['受体Wo', 'str_wo_sp', 150],
  ['受体ESN', 'str_esn_sp', 150],
  ['QTY', 'int_num', 90],
  ['输出', 'is_out', 100, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['AOG', 'is_aog', 100, null, [{ label: '是', value: 1 }, { label: '否', value: 0 }], null, false],
  ['进入站点时间', 'dt_input', 150],
  ['标准TAT', 'int_tatdays', 120],
  ['业务id', 'id_main', 140, 'right'],
];
// Create columns from names
const createColumns = (names) => {
  return names.map((item) => createColumn(...item));
};
// 创建转包列
export const createSubcontractColumn = (names) => {
  const list = [];
  list.push(...names);
  list.push(['EDD', 'dt_edd', 170]);
  list.push(['送修件号', 'str_sn_out', 170]);
  list.push(['运单号', 'str_ycode', 170]);
  list.push(['定单号', 'str_billcode', 170]);
  list.push(['转包商', 'str_shipper', 190]);
  list.push(['修理厂', 'str_repairship', 170]);
  list.push(['责任人', 'str_staff', 170]);
  return createColumns(list);
};
// 创建F2列
export const createF2Column = (names) => {
  const list = [];
  list.push(...names);
  list.push(['零件条码', 'str_bcode', 150]);
  list.push(['已处理位置', 'str_tcolocation', 150]);
  list.push(['待处理位置', 'str_pendinglocation', 150]);
  list.push(['零件状态', 'is_state_dispname', 150]);
  return createColumns(list);
};
// 起运准备列
export const createStartShipmentColumn = (names) => {
  const list = []
  list.push(...names);
  list.push(['转包商', 'str_shipper', 190]);
  list.push(['定单号', 'str_billcode', 150]);
  list.push(['定单日期', 'dt_po', 150]);
  list.push(['订单明细备注', 'str_notes', 150]);
  list.push(['责任人', 'str_staff', 150]);
  return createColumns(list);
};
// 出口运输列
export const createExportTransportColumn = (names) => {
  const list = [];
  list.push(...names);
  list.push(['定单号', 'str_billcode', 150]);
  list.push(['状态备注', 'str_abnormal', 150]);
  list.push(['运输代理', 'str_forwarder', 150]);
  list.push(['转包商', 'str_shipper', 190]);
  list.push(['修理厂', 'str_repairship', 170]);
  list.push(['提货日期', 'dt_begin', 120]);
  list.push(['机场出发', 'dt_begin_air', 120]);
  list.push(['到目的机场', 'dt_end_air', 150]);
  list.push(['订单明细备注', 'str_notes', 150]);
  list.push(['责任人', 'str_staff', 170]);
  return createColumns(list);
};
// 进口运输列
export const createImportTransportColumn = (names) => {
  const list = []
  list.push(...names);
  list.push(['运单号', 'str_ycode', 170]);
  list.push(['运输代理', 'str_forwarder', 120]);
  list.push(['定单号', 'str_billcode', 150]);
  list.push(['发货人', 'str_shipper', 120]);
  list.push(['预到机场', 'dt_end_air', 120]);
  list.push(['提单时间', 'dt_end_true', 120]);
  list.push(['运代收货时间', 'dt_ship_receive', 150]);
  list.push(['订单明细备注', 'str_notes', 150]);
  list.push(['责任人', 'str_staff', 170]);
  return createColumns(list);
};
// 采购-厂家交付列
export const createPurchaseFactoryDeliveryColumn = (names) => {
  const list = []
  list.push(...names);
  list.push(['定单号', 'str_billcode', 150]);
  list.push(['L/T Date', 'dt_edd', 120]);
  list.push(['责任人', 'str_staff', 170]);
  return createColumns(list);
};
// 行政接收列
export const createAdminReceiveColumn = (names) => {
  const list = []
  list.push(...names);
  list.push(['F3绩效时间', 'dt_f3_end', 140]);
  list.push(['实际TAT', 'int_actat', 120]);
  list.push(['站点要求开工时间', 'dt_plan', 200]);
  list.push(['排产时间', 'dt_schedule', 130]);
  list.push(['站点要求完工时间', 'dt_planend', 200]);
  list.push(['运单号', 'str_ycode', 140]);
  list.push(['定单号', 'str_billcode', 160]);
  list.push(['到SSAMC时间', 'dt_ssamc', 170]);
  list.push(['回厂状态', 'is_scrap_dispname', 170]);
  list.push(['行政接收', 'is_state_dispname', 120]);
  list.push(['责任人', 'str_staff', 150]);
  return createColumns(list);
};
// 进厂检验列
export const createIncomingInspectionColumn = (names) => {
  const list = []
  list.push(...names);
  list.push(['数据状态', 'is_abnormal', 150]);
  list.push(['状态备注', 'str_abnormal', 150]);
  list.push(['F3绩效时间', 'dt_f3_end', 140]);
  list.push(['实际TAT', 'int_actat', 120]);
  list.push(['站点要求开工时间', 'dt_plan', 200]);
  list.push(['排产时间', 'dt_schedule', 130]);
  list.push(['站点要求完工时间', 'dt_planend', 200]);
  list.push(['运单号', 'str_ycode', 140]);
  list.push(['定单号', 'str_billcode', 160]);
  list.push(['到SSAMC时间', 'dt_ssamc', 170]);
  list.push(['检验状态', 'is_check_dispname', 120]);
  list.push(['回厂序号', 'str_sn_in', 120]);
  list.push(['转包商/采购商', 'str_shipper', 200]);
  list.push(['来源', 'str_from', 130]);
  list.push(['责任人', 'str_staff', 170]);
  return createColumns(list);
};
// 入库列
export const createWarehousingColumn = (names) => {
  const list = []
  list.push(...names);
  list.push(['数据状态', 'is_abnormal', 150]);
  list.push(['状态备注', 'str_abnormal', 150]);
  list.push(['F3绩效时间', 'dt_f3_end', 140]);
  list.push(['实际TAT', 'int_actat', 120]);
  list.push(['站点要求开工时间', 'dt_plan', 200]);
  list.push(['排产时间', 'dt_schedule', 130]);
  list.push(['站点要求完工时间', 'dt_planend', 200]);
  list.push(['运单号', 'str_ycode', 140]);
  list.push(['定单号', 'str_billcode', 160]);
  list.push(['到SSAMC时间', 'dt_ssamc', 170]);
  list.push(['责任人', 'str_staff', 150]);
  return createColumns(list);
};
// 发料节点列
export const createSendMaterialColumn = (names) => {
  const list = []
  list.push(...names);
  list.push(['数据状态', 'is_abnormal', 150]);
  list.push(['状态备注', 'str_abnormal', 150]);
  list.push(['F3绩效时间', 'dt_f3_end', 140]);
  list.push(['站点要求开工时间', 'dt_plan', 200]);
  list.push(['排产时间', 'dt_schedule', 130]);
  list.push(['站点要求完工时间', 'dt_ttd', 200]);
  list.push(['责任人', 'str_staff', 150]);
  return createColumns(list);
};
// 点灯列
export const createLightUpColumn = (names) => {
  const list = []
  list.push(['数据状态', 'is_abnormal', 150]);
  list.push(['状态备注', 'str_abnormal', 150]);
  list.push(['排产时间', 'dt_schedule', 130]);
  list.push(['站点要求完工时间', 'dt_planend', 200]);
  list.push(['集件状态', 'is_kit_dispname', 120]);
  list.push(['责任人', 'str_staff', 150]);
  return createColumns(list);
};
// F1-2列
export const createF1_2Column = (names) => {
  const list = []
  list.push(['状态备注', 'str_abnormal', 150]);
  return createColumns(list);
};
// 列策略
export const columnStrategy = {
  '42': createColumns(columnNames),
  '9': createF1_2Column(columnNames),
  '15': createF2Column(columnNames1),
  '111': createColumns(columnNames),
  '11': createStartShipmentColumn(columnNames),
  '141': createColumns(columnNames),
  '12': createExportTransportColumn(columnNames),
  '10': createSubcontractColumn(columnNames1),
  '8': createImportTransportColumn(columnNames1),
  '1': createAdminReceiveColumn(defaultColumnNames),
  '2': createIncomingInspectionColumn(defaultColumnNames),
  '3': createWarehousingColumn(defaultColumnNames),
  '5': createSendMaterialColumn(defaultColumnNames),
  '6': createLightUpColumn(defaultColumnNames),
  '411': createColumns(columnNames),
  '41': createColumns(columnNames),
  '142': createColumns(columnNames),
  '13': createPurchaseFactoryDeliveryColumn(columnNames1),
  '43': createColumns(defaultColumnNames),
  '412': createColumns(columnNames),
};
